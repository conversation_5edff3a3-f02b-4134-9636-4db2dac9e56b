{"version": 3, "sources": ["../src/hu-HU.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const huHU: LocalizationResource = {\n  locale: 'hu-HU',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Vissza',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Alapértelmezett',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Másik megszemélyesítő eszköz',\n  badge__primary: 'Elsődleges',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Beavatkozás szükséges',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Ez az eszköz',\n  badge__unverified: 'Nem ellenőrzött',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Felhasználói eszköz',\n  badge__you: 'Te',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Szervezet létrehozása',\n    invitePage: {\n      formButtonReset: 'Kihagyás',\n    },\n    title: 'Szervezet létrehozása',\n  },\n  dates: {\n    lastDay: \"Tegnap {{ date | timeString('hu-HU') }}-kor\",\n    next6Days: \"{{ date | weekday('hu-HU','long') }} {{ date | timeString('hu-HU') }}-kor\",\n    nextDay: \"Holnap {{ date | timeString('hu-HU') }}-kor\",\n    numeric: \"{{ date | timeString('hu-HU') }}\",\n    previous6Days: \"Elmúlt {{ date | weekday('hu-HU','long') }} {{ date | timeString('hu-HU') }}-kor\",\n    sameDay: \"Ma {{ date | timeString('hu-HU') }}-kor\",\n  },\n  dividerText: 'vagy',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Másik módszer használata',\n  footerPageLink__help: 'Súgó',\n  footerPageLink__privacy: 'Adatvédelem',\n  footerPageLink__terms: 'Felhasználási feltételek',\n  formButtonPrimary: 'Tovább',\n  formButtonPrimary__verify: 'Ellenőrzés',\n  formFieldAction__forgotPassword: 'Elfelejtetted a jelszavad?',\n  formFieldError__matchingPasswords: 'A jelszavak megegyeznek',\n  formFieldError__notMatchingPasswords: 'A jelszavak nem egyeznek',\n  formFieldError__verificationLinkExpired: 'A megerősítő link lejárt. Kérlek kérj egy újat.',\n  formFieldHintText__optional: 'Nem kötelező',\n  formFieldHintText__slug: 'A slug egy egyedi azonosító, amelyet általában URL-ben használunk.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Fiók törlése',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: 'my-org',\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Automatikus meghívások engedélyezése ezen a domainen',\n  formFieldLabel__backupCode: 'Tartalék kód',\n  formFieldLabel__confirmDeletion: 'Megerősítés',\n  formFieldLabel__confirmPassword: 'Jelszó megerősítése',\n  formFieldLabel__currentPassword: 'Jelenlegi jelszó',\n  formFieldLabel__emailAddress: 'Email cím',\n  formFieldLabel__emailAddress_username: 'Email cím vagy felhasználónév',\n  formFieldLabel__emailAddresses: 'Email címek',\n  formFieldLabel__firstName: 'Keresztnév',\n  formFieldLabel__lastName: 'Vezetéknév',\n  formFieldLabel__newPassword: 'Új jelszó',\n  formFieldLabel__organizationDomain: 'Domain',\n  formFieldLabel__organizationDomainDeletePending: 'Függőben lévő meghívások és javaslatok törlése',\n  formFieldLabel__organizationDomainEmailAddress: 'Visszaigazoló email cím',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Írj be egy email címet ez alatt a domain alatt, hogy visszaigazold a domaint.',\n  formFieldLabel__organizationName: 'Szervezet neve',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Passkey neve',\n  formFieldLabel__password: 'Jelszó',\n  formFieldLabel__phoneNumber: 'Telefonszám',\n  formFieldLabel__role: 'Beosztás',\n  formFieldLabel__signOutOfOtherSessions: 'Kijelentkeztetés minden más eszközökről',\n  formFieldLabel__username: 'Felhasználónév',\n  impersonationFab: {\n    action__signOut: 'Kijelentkezés',\n    title: 'Bejelntkezve mint {{identifier}}',\n  },\n  maintenanceMode: 'Jelenleg karbantartás alatt állunk, de ne aggódj, ez nem tart tovább pár percnél!',\n  membershipRole__admin: 'Adminisztrátor',\n  membershipRole__basicMember: 'Tag',\n  membershipRole__guestMember: 'Vendég',\n  organizationList: {\n    action__createOrganization: 'Szervezet létrehozása',\n    action__invitationAccept: 'Csatlakozás',\n    action__suggestionsAccept: 'Csatlakozás kérése',\n    createOrganization: 'Szervezet létrehozása',\n    invitationAcceptedLabel: 'Meghívás elfogadva',\n    subtitle: 'Amivel folytathatod a(z) {{applicationName}}',\n    suggestionsAcceptedLabel: 'Elfogadásra vár',\n    title: 'Válassz egy fiókot',\n    titleWithoutPersonal: 'Válassz egy szervezetet',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Automatikus meghívások',\n    badge__automaticSuggestion: 'Automatikus javaslatok',\n    badge__manualInvitation: 'Nincs automatikus felvétel',\n    badge__unverified: 'Nincs visszaigazolva',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Add meg a visszaigazolandó domain nevét. Minden email cím erről a tartományjól automatikusan tud csatlakozni a szervezethez.',\n      title: 'Domain hozzáadása',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'A meghívok küldése sikertelen. Már vannak függőben lévő meghívók a következő címekre: {{email_addresses}}',\n      formButtonPrimary__continue: 'Meghívók küldése',\n      selectDropdown__role: 'Válassz beosztást',\n      subtitle: 'Írj be vagy illessz be egy vagy több email címet, vesszővel, vagy szóközzel elválasztva.',\n      successMessage: 'A meghívók sikeresen elküldve',\n      title: 'Új tagok meghívása',\n    },\n    membersPage: {\n      action__invite: 'Meghívás',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Tag eltávolítása',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'Csatlakozott',\n        tableHeader__role: 'Beosztás',\n        tableHeader__user: 'Felhasználó',\n      },\n      detailsTitle__emptyRow: 'Nincsenek listázható tagok',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Adj meg egy email domaint, hogy meghívhass tagokat. Bárki aki erről a tartományról regisztrál, bármikor, csatlakozhat a szervezethez bármikor',\n          headerTitle: 'Automatikus meghívások',\n          primaryButton: 'Visszaigazolt domainek kezelése',\n        },\n        table__emptyRow: 'Nincsenek listázható meghívások',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Meghívó visszavonása',\n        tableHeader__invited: 'Meghívva',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Azok a felhasználók, akik, egyező email domainnel regisztrálnak, látni fognak egy javaslatot, hogy csatlakozzanak a szervezetedhez.',\n          headerTitle: 'Automatikus javaslat',\n          primaryButton: 'Visszaigazolt domainek kezelése',\n        },\n        menuAction__approve: 'Elfogadás',\n        menuAction__reject: 'Elutasítás',\n        tableHeader__requested: 'Hozzáférés kérése',\n        table__emptyRow: 'Nincsenek listázható kérések',\n      },\n      start: {\n        headerTitle__invitations: 'Meghívók',\n        headerTitle__members: 'Tagok',\n        headerTitle__requests: 'Kérések',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'A szervezeted kezelése',\n      general: 'Általános',\n      members: 'Tagok',\n      title: 'Szervezet',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Írd be, hogy: \"{{organizationName}}\" a folytatáshoz',\n          messageLine1: 'Biztosan törölni szeretnéd ez a szervezetet?',\n          messageLine2: 'Ez a művelet végleges és visszafordíthatatlan.',\n          successMessage: 'Kitörölted a szervezetet.',\n          title: 'Szervezet törlése',\n        },\n        leaveOrganization: {\n          actionDescription: 'Írd be, hogy: \"{{organizationName}}\" a folytatáshoz',\n          messageLine1:\n            'Biztos vagy benne, hogy el szeretnéd hagyni a szervezetet? Elveszíted a hozzáférést a szervezethez, és alkamazásaihoz.',\n          messageLine2: 'Ez a művelet végleges és visszafordíthatatlan.',\n          successMessage: 'Elhagytad a szervezetet.',\n          title: 'Szervezet elhagyása',\n        },\n        title: 'Veszély',\n      },\n      domainSection: {\n        menuAction__manage: 'Kezelés',\n        menuAction__remove: 'Törlés',\n        menuAction__verify: 'Visszaigazolás',\n        primaryButton: 'Domain hozzáadása',\n        subtitle:\n          'Endegélyezd, hogy a felhasználók automatikusan csatlakozhassanak a szervezetedhez, vagy hozzáférést kérjenek, az email domainjük alapján.',\n        title: 'Visszaigazolt domainek',\n      },\n      successMessage: 'A szervezet frissítve',\n      title: 'Profil frissítése',\n    },\n    removeDomainPage: {\n      messageLine1: 'Az email domain {{domain}} el lesz távolítva',\n      messageLine2: 'Ez után a felhasználók nem tudnak automatikusan csatlakozni',\n      successMessage: '{{domain}} törölve lett',\n      title: 'Domain törlése',\n    },\n    start: {\n      headerTitle__general: 'Általános',\n      headerTitle__members: 'Tagok',\n      profileSection: {\n        primaryButton: 'Profil frissítése',\n        title: 'Szervezet Profil',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'A domain törlése befolyásolja a meghívott felhasználókat',\n        removeDomainActionLabel__remove: 'Domain törlése',\n        removeDomainSubtitle: 'Domain törlése a visszaigazolt domainek listájáról',\n        removeDomainTitle: 'Domain törlése',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'A felhasználók automatikusan meg lesznek hívva a szervezetbe, amikor regisztrálnak, és bármikor csatlakozhatnak.',\n        automaticInvitationOption__label: 'Automatikus meghívások',\n        automaticSuggestionOption__description:\n          'A felasználók kapnak egy javaslatot, hogy kérjenek hozzáférést, de előbb egy adminisztrátornak jóvá kell hagynia, mielőtt csatlakozhatnak.',\n        automaticSuggestionOption__label: 'Automatikus javaslatok',\n        calloutInfoLabel: 'A felvételi mód megváltoztatása csak az új felhasználókra lesz hatással',\n        calloutInvitationCountLabel: 'Függőben lévő meghívók: {{count}}',\n        calloutSuggestionCountLabel: 'Függőben lévő javaslatok: {{count}}',\n        manualInvitationOption__description: 'Felhasználókat csak manuálisan lehet meghívni a szervezetbe.',\n        manualInvitationOption__label: 'Nincs automatikus felvétel',\n        subtitle: 'Válaszd ki, hogy a felhasználók, hogyan csatlakozhatnak szervezethez.',\n      },\n      start: {\n        headerTitle__danger: 'Veszély',\n        headerTitle__enrollment: 'Felvételi opciók',\n      },\n      subtitle: 'A domain {{domain}} visszaigazolva. A folytatáshoz válassz felvételi módot',\n      title: '{{domain}} frissítése',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Írd be a megerősítő kódot, amit elküldtünk az email címedre',\n      formTitle: 'Megerősítő kód',\n      resendButton: 'Nem kaptál kódot? Újraküldés',\n      subtitle: 'A domain {{domainName}}-t emaillel jóvá kell hagyni.',\n      subtitleVerificationCodeScreen:\n        'Elküldtük a megerősítő kódot a(z) {{emailAddress}} címre. A folytatáshoz írd be a kódot',\n      title: 'Domain visszaigazolása',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Szervezet létrehozása',\n    action__invitationAccept: 'Csatlakozás',\n    action__manageOrganization: 'Kezelés',\n    action__suggestionsAccept: 'Csatlakozás kérése',\n    notSelected: 'Nincs szervezet kiválasztva',\n    personalWorkspace: 'Személyes fiók',\n    suggestionsAcceptedLabel: 'Elfogadásra vár',\n  },\n  paginationButton__next: 'Következő',\n  paginationButton__previous: 'Előző',\n  paginationRowText__displaying: 'Mutat',\n  paginationRowText__of: '-ból/-ből',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Fiók hozzáadása',\n      action__signOutAll: 'Kijelentkezés minden fiókból',\n      subtitle: 'Válaszd ki a fiókot amivel folytatni szeretnéd',\n      title: 'Válassz egy fiókot',\n    },\n    alternativeMethods: {\n      actionLink: 'Segítség kérése',\n      actionText: 'Nincs ezekből egyik sem ?',\n      blockButton__backupCode: 'Tartalék kód használata',\n      blockButton__emailCode: 'Email kód a {{identifier}}-nak/nek',\n      blockButton__emailLink: 'Email link a {{identifier}}-nak/nek',\n      blockButton__passkey: 'Bejelentkezés passkey-vel',\n      blockButton__password: 'Bejelentkezés jelszóval',\n      blockButton__phoneCode: 'SMS kód {{identifier}}-nak/nek',\n      blockButton__totp: 'Hitelesítő app használata',\n      getHelp: {\n        blockButton__emailSupport: 'Segítség kérése emailben',\n        content:\n          'Ha bármilyen problémád van a bejelentkezéssel a fiókodba, küldj nekünk egy emailt, és visszaállítjuk a fiókodat, amint lehetséges.',\n        title: 'Segítség kérés',\n      },\n      subtitle: 'Problémád akadt? Ezek közül bármelyik bejelentkezési módot választhatod.',\n      title: 'Bejelentkezés más módon',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'A tartalék kód az, amit akkor kaptál, amikor beállítottad a kétlépcsős azonosítást',\n      title: 'Írd be a tartalék kódot',\n    },\n    emailCode: {\n      formTitle: 'Visszaigazoló kód',\n      resendButton: 'Nem kaptad meg a kódot? Újraküldés',\n      subtitle: 'hogy folytathasd a(z) {{applicationName}}',\n      title: 'Ellenőrizd az emailed',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: 'Menj vissza az eredeti lapra a folytatáshoz.',\n        title: 'Ez a megerősítő link lejárt',\n      },\n      failed: {\n        subtitle: 'Menj vissza az eredeti lapra a folytatáshoz.',\n        title: 'Ez a megerősítő link érvénytelen',\n      },\n      formSubtitle: 'Használd a megerősítő linket, amit a emailben kaptál',\n      formTitle: 'Megerősítő link',\n      loading: {\n        subtitle: 'Hamarosan átirányítunk',\n        title: 'Bejelentkezés folyamatban...',\n      },\n      resendButton: 'Nem kaptál linket? Újraküldés',\n      subtitle: 'hogy folytathasd a(z) {{applicationName}}',\n      title: 'Ellenőrizd az emailed',\n      unusedTab: {\n        title: 'Ezt a lapot bezárhatod',\n      },\n      verified: {\n        subtitle: 'Hamarosan átirányítunk',\n        title: 'Sikeres bejelentkezés',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Menj vissza az eredeti lapra a folyatáshoz',\n        subtitleNewTab: 'Menj át az újonan megnyitott lapra a folytatáshoz',\n        titleNewTab: 'Egy másik lapon bejelezkeztél be',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Jelszó visszaállító kód',\n      resendButton: 'Nem kaptad meg a kódot? Újraküldés',\n      subtitle: 'hogy visszaállíthasd a jelszavad',\n      subtitle_email: 'Először, írd be a kódot amit emailben kaptál',\n      subtitle_phone: 'Először, írd be a kódot amit a telefonodra kaptál',\n      title: 'Jelszó visszaállítás',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Állítsd vissza a jelszavad',\n      label__alternativeMethods: 'Vagy jelentkezz be más módon',\n      title: 'Efelejtetted a jelszavad?',\n    },\n    noAvailableMethods: {\n      message: 'Nem lehet bejelentkezni. Nincs elérhető hitelesítő tényező.',\n      subtitle: 'Hiba történt',\n      title: 'Nem lehetett bejelentkezni',\n    },\n    passkey: {\n      subtitle:\n        'A Passkey-d használata megerősíti, hogy te vagy az. Az eszközöd kérheti az ujjlenyomatod, arcod vagy a képernyőzárad.',\n      title: 'Használd a passkeydet',\n    },\n    password: {\n      actionLink: 'Másik mód használata',\n      subtitle: 'Írd be a fiókhoz tartozó jelszavad',\n      title: 'Írd be a jelszavad',\n    },\n    passwordPwned: {\n      title: 'Jelszó kompromitálódott',\n    },\n    phoneCode: {\n      formTitle: 'Visszaigazoló kód',\n      resendButton: 'Nem kaptad meg a kódot? Újraküldés',\n      subtitle: 'hogy folytathasd a(z) {{applicationName}}',\n      title: 'Ellenőrizd a telefonod',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Visszaigazoló kód',\n      resendButton: 'Nem kaptad meg a kódot? Újraküldés',\n      subtitle: 'A folytatáshoz, kérlek írd be a visszaigazoló kódot, amit a telefonodra küldtünk.',\n      title: 'Ellenőrizd a telefonod',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Jelszó visszaállítása',\n      requiredMessage: 'Biztonsági okokból, muszáj megváltoztatnod a jelszavadat.',\n      successMessage: 'A jelszavad sikeresen megváltozott. A bejelentkezés folyamatban, kérlek várj.',\n      title: 'Új jelszó beállítása',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Vissza kell igazolnod az identitásod, mielőtt visszaállítod a jelszavad',\n    },\n    start: {\n      actionLink: 'Regisztráció',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Email használata',\n      actionLink__use_email_username: 'Használd az emailded vagy a felhasználóneved',\n      actionLink__use_passkey: 'Passkey használata',\n      actionLink__use_phone: 'Telefon használata',\n      actionLink__use_username: 'Felhasználónév használata',\n      actionText: 'Nincs fiókod?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Üdv újra! A folytatáshoz kérlek jelentkezz be.',\n      subtitleCombined: undefined,\n      title: 'Bejelentkezés a(z) {{applicationName}} fiókba',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Visszaigazoló kód',\n      subtitle: 'A folytatáshoz, kérlek írd be a visszaigazoló kódot, amit a hitelesítő app készített.',\n      title: 'Két lépécsős azonosítás',\n    },\n  },\n  signInEnterPasswordTitle: 'Írd be a jelszavad',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Bejelentkezés',\n      actionText: 'Van már fiókod?',\n      subtitle: 'Kérlek töltsd ki a hátralévő mezőket a folytatáshoz',\n      title: 'Töltsd ki a hiányzó mezőket',\n    },\n    emailCode: {\n      formSubtitle: 'Írd be a visszaigazoló kódot, amit emailben kaptál',\n      formTitle: 'Viszaigazoló kód',\n      resendButton: 'Nem kaptad meg a kódot? Újraküldés',\n      subtitle: 'Írd be a visszaigazoló kódot, amit emailben kaptál',\n      title: 'Email megerősítése',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: 'Használd a  visszaigazoló linket, amit a emailben kaptál',\n      formTitle: 'Visszaigazoló link',\n      loading: {\n        title: 'Regisztrálás...',\n      },\n      resendButton: 'Nem kaptad meg a kódot? Újraküldés',\n      subtitle: 'hogy folytasd a(z) {{applicationName}}',\n      title: 'Erősítsd meg az email címed',\n      verified: {\n        title: 'Sikeres regisztráció',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Menj az újonan nyitott lapra, a folytatáshoz',\n        subtitleNewTab: 'Menj vissza az előző lapra a folytatáshoz',\n        title: 'Sikeresen megerősítetted az email címed',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Írd be a visszaigazoló kódot, amit a telefondra kaptál',\n      formTitle: 'Visszaigazoló kód',\n      resendButton: 'Nem kaptad meg a kódot? Újraküldés',\n      subtitle: 'Írd be a visszaigazoló kódot, amit a telefonodra kaptál',\n      title: 'Erősítsd meg a telefonszámod',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Bejelentkezés',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: 'Van már fiókod?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Üdv! Kérlek add meg az adatokat, hogy elkezdhesd.',\n      subtitleCombined: 'Üdv! Kérlek add meg az adatokat, hogy elkezdhesd.',\n      title: 'Fiók létrehozása',\n      titleCombined: 'Fiók létrehozása',\n    },\n  },\n  socialButtonsBlockButton: 'Folytatás {{provider|titleize}} segítségével',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'Biztonsági okokból a regisztráció sikertelen volt. Kérlek frissítsd az oldalt, hogy újra próbálhasd, vagy kérj támogatást.',\n    captcha_unavailable:\n      'Bot érvényesítése miatt, a regisztráció sikertelen volt. Kérlek frissítsd az oldalt, hogy újra próbálhasd, vagy kérj támogatást.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: 'Ez az email cím már foglalt. Kérlek próbálj egy másikat.',\n    form_identifier_exists__phone_number: 'Ez a telefonszám már foglalt. Kérlek próbálj egy másikat.',\n    form_identifier_exists__username: 'Ez a felhasználónév már foglalt. Kérlek próbálj egy másikat.',\n    form_identifier_not_found: 'Nem találtunk fiókot ezekkel a részletekkel.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'Az email címnek érvényes email címnek kell lennie.',\n    form_param_format_invalid__phone_number: 'A telefonszámnak érvényes telefonszámnak kell lennie.',\n    form_param_max_length_exceeded__first_name: 'A keresztnév nem lehet hosszabb, mint 256 karakter.',\n    form_param_max_length_exceeded__last_name: 'A vezetéknév nem lehet hosszabb, mint 256 karakter.',\n    form_param_max_length_exceeded__name: 'A név nem lehet hosszabb mint 256 karakter.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: 'A jelszó nem elég erős',\n    form_password_pwned:\n      'Úgy látjuk, hogy ez a jelszó kiszivárgott, ezért ezt nem használhatod, kérlek próbálj egy másik jelszót.',\n    form_password_pwned__sign_in:\n      'Úgy látjuk, hogy ez a jelszó kiszivárgott, ezért ezt nem használhatod, kérlek állítsd át a jelszavad.',\n    form_password_size_in_bytes_exceeded:\n      'A jelszavad több bájtot tartalmaz mint a megadott maximum, kérlek rövidítsd vagy törölj ki néhány speciális karaktert.',\n    form_password_validation_failed: 'Helytelen jelszó',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: 'Nem törölheted ki az utolsó azonosítód.',\n    not_allowed_access:\n      \"Az email címed vagy a telefonszámod nem használható regisztrációhoz. Ez lehet, mert az email címedben vagy a telefonszámodban szerepel a '+', '=', '#' vagy '.' karakter, vagy az email címedben vagy a telefonszámodban szerepel egy időzített email szolgáltató vagy kizárt tartomány. Ha úgy gondolja, hogy ez hiba, vegye fel velünk a kapcsolatot.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: 'Egy passkey már regisztrálva van ehhez az eszközhöz.',\n    passkey_not_supported: 'Passkeyk nem támogatottak ezen az eszközön.',\n    passkey_pa_not_supported: 'A regisztrációhoz egy platform hitelesítő kell, de ez az eszköz ezt nem támogatja.',\n    passkey_registration_cancelled: 'Passkey regisztráció megszakadt vagy lejárt.',\n    passkey_retrieval_cancelled: 'Passkey visszaigazolás megszakadt vagy lejárt.',\n    passwordComplexity: {\n      maximumLength: 'kevesebb mint {{length}} karaktert',\n      minimumLength: '{{length}} vagy több karaktert',\n      requireLowercase: 'egy kisbetűt',\n      requireNumbers: 'egy számot',\n      requireSpecialCharacter: 'egy speciális karaktert',\n      requireUppercase: 'egy nagybetű',\n      sentencePrefix: 'A jelszavadnak tartalmaznia kell',\n    },\n    phone_number_exists: 'Ez a telefonszám már foglalt. Kérlek próbálj meg egy másikat.',\n    session_exists: 'Már be vagy jelentkezve.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'A jelszavad, jó, de lehetne erősebb. Adj hozzá több karaktert.',\n      goodPassword: 'A jelszavad megfelel az elvárásoknak.',\n      notEnough: 'Nem elég erős a jelszavad.',\n      suggestions: {\n        allUppercase: 'Változtass meg néhány betűt nagybetűre, de ne mindet.',\n        anotherWord: 'Adj hozzá kevésbé gyakori szavakat.',\n        associatedYears: 'Kerüld el a hozzád kapcsolható évek használatát',\n        capitalization: 'Ne csak az első betű legyen nagy betű.',\n        dates: 'Kerüld el a hozzád köthető dátumok és évek használatát.',\n        l33t: \"Kerüld el a kiszámítható betű behelyettesítéseket, mint a '@' az 'a' helyett.\",\n        longerKeyboardPattern: 'Használj hosszabb billentyűzet mintákat, és változtasd meg a gépelés irányát többször.',\n        noNeed:\n          'Úgy is létrehozhatsz erős jelszót, hogy nem használsz speciális karaktereket, számokat, vagy nagybetűket.',\n        pwned: 'Ha máshol is használod ezt a jelszót, akkor változtasd meg.',\n        recentYears: 'Kerüld el a közelmúlt évek használatát.',\n        repeated: 'Kerüld el a szó- vagy karakterismétlést',\n        reverseWords: 'Kerüld el a szavak visszafelé írását.',\n        sequences: 'Kerüld el a gyakori karakter sorozatokat.',\n        useWords: 'Használj több szót, de kerüld el a gyakori kifejezéseket.',\n      },\n      warnings: {\n        common: 'Ez egy gyakran használt jelszó',\n        commonNames: 'A gyakori nevek könnyen kitalálhatóak.',\n        dates: 'A dátumokat könnyű kitalálni.',\n        extendedRepeat: 'Ismétlődő karakter sorozatok, mint \"abcabcabc\" könnyen kitalálhatóak.',\n        keyPattern: 'A rövid billentyűzetminták könnyen kitalálhatóak.',\n        namesByThemselves: 'A nevek könnyen kitalálhatóak.',\n        pwned: 'A jelszavad kiszivárgott egy adatszivárgás során az Interneten. Válassz egy másikat.',\n        recentYears: 'Az elmúlt évek könnyen kitalálhatóak.',\n        sequences: 'Gyakori karakter sorozatok, mint \"abc\" könnyen kitalálhatóak.',\n        similarToCommon: 'Ez hasonlít egy gyakran használt jelszóhoz.',\n        simpleRepeat: 'Ismétlődő karakterek, mint az \"aaa\" könnyen kitalálhatóak.',\n        straightRow: 'Egyenes sor a billentyűzeten, mint az \"asdf\" könnyen kitalálhatóak.',\n        topHundred: 'Ez egy gyakran használt jelszó',\n        topTen: 'Ez egy nagyon gyakori jelszó',\n        userInputs: 'Ne tartalmazzon, személyes, vagy az oldalhoz köthető információt.',\n        wordByItself: 'Egyszavas jelszavak könnyen kitalálhatóak.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Fiók hozzáadása',\n    action__manageAccount: 'Fiók kezelése',\n    action__signOut: 'Kijelentkezés',\n    action__signOutAll: 'Kijelentkezés minden fiókból',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Kimásolva!',\n      actionLabel__copy: 'Az összes kimásolása',\n      actionLabel__download: '.txt letöltése',\n      actionLabel__print: 'Nyomtatás',\n      infoText1: 'A tartalék kódok be lesznek kapcsolva ehhez a fiókhoz.',\n      infoText2:\n        'A tartalék kódokat tartsd titokban, és tárold biztonságos helyen. Újragenerálhatod a tartalék kódokat, ha azt gondolod, hogy kiszivárogtak.',\n      subtitle__codelist: 'Tárold őket biztonságos helyen, és tartsd titokban.',\n      successMessage:\n        'A tartalék kódok bekapcsolva. Használhatod ezeket, hogy belépj a fiókodba, ha nem férsz hozzá a hitelesítő eszközhöz. Mindegyik kódot egyszer tudod használni.',\n      successSubtitle: 'Használhatod ezeket is, hogy belépj a fiókodba, ha nem férsz hozzá a hitelesítő eszközödhöz.',\n      title: 'Tartalék kód megerősítés hozzáadása',\n      title__codelist: 'Tartalék kódok',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Válassz egy szolgáltatót, amit összekötsz a fiókoddal.',\n      formHint__noAccounts: 'Nincs elérhető külső fiók szolgáltató.',\n      removeResource: {\n        messageLine1: '{{identifier}} el lesz távolítva ebből a fiókból.',\n        messageLine2:\n          'Nem fogod tudni használni ezt a kapcsolt fiókot. Bármilyen ettől függő szolgáltatás nem fog működni.',\n        successMessage: '{{connectedAccount}} eltávolítva a fiókdból.',\n        title: 'Kapcsolt fiók eltávolítása',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}',\n      successMessage: 'A szolgátató hozzá lett adva a fiókodhoz.',\n      title: 'Kapcsolt fiók hozzáadása',\n    },\n    deletePage: {\n      actionDescription: 'Írd be, hogy \"Delete account\" a folytatáshoz.',\n      confirm: 'Fiók törlése',\n      messageLine1: 'Biztos vagy benne, hogy törölni szeretnéd a fiókod?',\n      messageLine2: 'Ez a művelet végleges és visszafordíthatatlan.',\n      title: 'Fiók törlése',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Egy visszaigazoló kódot tartalmazó emailt fogunk küldeni erre az email címre.',\n        formSubtitle: 'Írd be a visszaigazoló kódot, amit a(z) {{identifier}} címre küldtünk.',\n        formTitle: 'Visszaigazoló kód',\n        resendButton: 'Nem kaptad meg a kódot? Újraküldés',\n        successMessage: 'Az email: {{identifier}} hozzá lett adva a fiókodhoz.',\n      },\n      emailLink: {\n        formHint: 'Egy visszaigazoló linket tartalmazó emailt fogunk küldeni erre az email címre.',\n        formSubtitle: 'Kattints a visszaigazoló linkre az emailben, amit ide küldtünk: {{identifier}}',\n        formTitle: 'Visszaigazoló link',\n        resendButton: 'Nem kaptad meg a linket? Újraküldés',\n        successMessage: 'Az email: {{identifier}} hozzá lett adva a fiókodhoz.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} el lesz távolítva ebből a fiókból.',\n        messageLine2: 'Nem fogsz tudni többet bejelentkezni ezzel az email címmel.',\n        successMessage: '{{emailAddress}} el lett távolítva a fiókodból.',\n        title: 'Email cím törlése',\n      },\n      title: 'Email cím hozzáadása',\n      verifyTitle: 'Email cím visszaigazolása',\n    },\n    formButtonPrimary__add: 'Hozzáadás',\n    formButtonPrimary__continue: 'Folytatás',\n    formButtonPrimary__finish: 'Befejezés',\n    formButtonPrimary__remove: 'Eltávolítás',\n    formButtonPrimary__save: 'Mentés',\n    formButtonReset: 'Mégsem',\n    mfaPage: {\n      formHint: 'Válassz egy hitelesítő módszert, amit hozzá szeretnél adni.',\n      title: 'Kétlépcsős azonosítás bekapcsolása',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Létező szám használata',\n      primaryButton__addPhoneNumber: 'Telefonszám hozzáadása',\n      removeResource: {\n        messageLine1: '{{identifier}} nem fog többet visszaigazoló kódot kapni, amikor belépsz.',\n        messageLine2: 'A fiókód nem biztos, hogy olyan biztonságos lesz. Biztosan folytatod?',\n        successMessage: 'Kétlépcsős SMS kód eltávolítva a {{mfaPhoneCode}} számhoz',\n        title: 'Kétlépcsős azonosítás eltávolítása',\n      },\n      subtitle__availablePhoneNumbers:\n        'Válassz egy telefonszámot, hogy regisztráld az SMS kód kétlépcsős azonosítást, vagy adj hozzá egy újat.',\n      subtitle__unavailablePhoneNumbers:\n        'Nincs elérhető telefonszám, az SMS kód kétlépcsős azonosításhoz. Kérlek adj hozzá egyet.',\n      successMessage1:\n        'Amikor belépsz, extra lépésként meg kell adnod a visszaigazoló kódot, amit elküldünk erre a telefonszámra.',\n      successMessage2:\n        'Tárold ezeket a tartalék kódokat, egy biztonságos helyen. Ha nem férsz hozzá a hitelesítő eszközödhöz, ezekkel tudsz belépni.',\n      successTitle: 'SMS visszaigazoló kód hozzáadva',\n      title: 'SMS visszaigazoló kód hozzáadása',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Inkább olvasd be a QR kódot',\n        buttonUnableToScan__nonPrimary: 'Nem tudod beolvasni a QR kódot?',\n        infoText__ableToScan:\n          'Állíts be egy új belépési módot, a hitelesítő alkalmazásodban és olvasd a QR kódot, hogy összekösd a fiókoddal.',\n        infoText__unableToScan:\n          'Állíts be egy új bejelentkezés módot a hitelesítő alkalmazásodban, írd be a kulcsot, amit lejjebb találsz.',\n        inputLabel__unableToScan1:\n          'Bizonyosodj meg, hogy a Time-based vagy a One-time passwords be van kapcsolva, majd fejezd be a fiók összekötését a következő kulccsal:',\n        inputLabel__unableToScan2:\n          'Alternatívaként, ha az hitelesítő alkalmazásod támogatja a TOTP URI-kat, a teljes URI-t is be lehet másolni.',\n      },\n      removeResource: {\n        messageLine1:\n          'Visszaigazoló kódok ebből a hitelesítő alkalmazásból, már nem fognak kelleni a bejelenetkezéshez.',\n        messageLine2: 'Előfordulhat, hogy a fiókod nem lesz olyan biztonságos. Biztonsan folytatni szeretnéd?',\n        successMessage: 'Kétlépcsős azonosítás hitelesítő alkalmazással eltávolítva.',\n        title: 'Kétlépcsős azonosítás eltávolítása',\n      },\n      successMessage:\n        'Kétlépcsős azonosítást bekapcsolva. Extra lépésként, amikor belépsz, meg kell adnod a visszaigazoló kódot a hitelesítő alkalmazásodból.',\n      title: 'Hitelesítő alkalmazás hozzáadása',\n      verifySubtitle: 'Írd be a visszaigazoló kódot, amit a hitelesítő alkalmazásodból.',\n      verifyTitle: 'Visszaigazoló kód',\n    },\n    mobileButton__menu: 'Menü',\n    navbar: {\n      account: 'Profil',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Fiók információk kezelése',\n      security: 'Biztonság',\n      title: 'Fiók',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} el lesz távolítva ebből a fiókból.',\n        title: 'Passkey törlése',\n      },\n      subtitle__rename: 'Megváltoztathatod a passkey nevét, hogy könnyebb legyen megtalálni.',\n      title__rename: 'Passkey átnevezése',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Ajánlott kijelentkeztetni az összes olyan eszközt, ami a régi jelszavadat használja.',\n      readonly: 'A jelszavad jelenleg nem módosítható, mert csak vállalati kapcsolattal tudsz belépni.',\n      successMessage__set: 'A jelszavad beállítottuk.',\n      successMessage__signOutOfOtherSessions: 'Minden más eszköz kijelentkeztetve.',\n      successMessage__update: 'A jelszavad frissítésre került.',\n      title__set: 'Jelszó beállítása',\n      title__update: 'Jelszó frissítése',\n    },\n    phoneNumberPage: {\n      infoText:\n        'Egy szöveges üzenetet küldünk a visszaigazoló kóddal erre a számra. Az üzenet és az adatforgalom díjai érvényesek lehetnek.',\n      removeResource: {\n        messageLine1: '{{identifier}} el lesz távolítva ebből a fiókból.',\n        messageLine2: 'Nem fogsz tudni többet bejelentkezni ezzel a telefonszámmal.',\n        successMessage: '{{phoneNumber}} el lett távolítva a fiókodból.',\n        title: 'Telefonszám eltávolítása',\n      },\n      successMessage: '{{identifier}} hozzá lett adva a fiókodhoz.',\n      title: 'Telefonszám hozzáadása',\n      verifySubtitle: 'Írd be a visszaigazóló kódot, amit a(z) {{identifier}} számra küldtünk.',\n      verifyTitle: 'Telefonszám visszaigazolása',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Ajánlott méret 1:1, 10MB-ig.',\n      imageFormDestructiveActionSubtitle: 'Eltávolítás',\n      imageFormSubtitle: 'Feltöltés',\n      imageFormTitle: 'Profil kép',\n      readonly: 'A profilod adatai, vállalati kapcsolatból származnak, így nem módosíthatóak.',\n      successMessage: 'A profilod frissítve.',\n      title: 'Profil frissítése',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Kijelentkezés az eszközről',\n        title: 'Aktív eszközök',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Próbáld újra',\n        actionLabel__reauthorize: 'Engedélyezd most',\n        destructiveActionTitle: 'Eltávolítás',\n        primaryButton: 'Fiók összekötése',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'A szükséges hatáskörök megváltozták, előfordulhat, hogy limitált funkcionalitást tapasztalhatsz. Kérlek, újra engedélyezd az alkalmazást, hogy elkerüld a hibákat.',\n        title: 'Kapcsolt fiókok',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Fiók törlése',\n        title: 'Fiók törlése',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Email eltávolítása',\n        detailsAction__nonPrimary: 'Beállítás elsődlegesként',\n        detailsAction__primary: 'Visszaigazolás befejezése',\n        detailsAction__unverified: 'Visszaigazolás',\n        primaryButton: 'Email cím hozzáadása',\n        title: 'Email címek',\n      },\n      enterpriseAccountsSection: {\n        title: 'Vállalati fiókok',\n      },\n      headerTitle__account: 'Profil adatok',\n      headerTitle__security: 'Biztonság',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Újra generálás',\n          headerTitle: 'Tartalék kódok',\n          subtitle__regenerate:\n            'Kérj egy új biztonságos tartalék kódokat. Az előző kódok törlésre kerülnek, és nem lesznek használhatók.',\n          title__regenerate: 'Tartalék kódok újragenerálása',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Beállítás alapértelmezettként',\n          destructiveActionLabel: 'Eltávolítás',\n        },\n        primaryButton: 'Kétlépcsős azonosítás hozzáadása',\n        title: 'Két lépcsős azonosítás',\n        totp: {\n          destructiveActionTitle: 'Eltávolítás',\n          headerTitle: 'Hitelesítő alkalmazás',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Eltávolítás',\n        menuAction__rename: 'Átnevezés',\n        primaryButton: undefined,\n        title: 'Passkey-k',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Jelszó beállítása',\n        primaryButton__updatePassword: 'Jelszó frissítése',\n        title: 'Jelszó',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Teleofnszám eltávolítása',\n        detailsAction__nonPrimary: 'Beállítás elsődlegesként',\n        detailsAction__primary: 'Visszaigazolás befejezése',\n        detailsAction__unverified: 'Teleofnszám visszaigazolása',\n        primaryButton: 'Telefonszám hozzáadása',\n        title: 'Telefonszámok',\n      },\n      profileSection: {\n        primaryButton: 'Profil frissítése',\n        title: 'Profil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Felhasználónév beállítása',\n        primaryButton__updateUsername: 'Felhasználónév frissítése',\n        title: 'Felhasználónév',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Tárca eltávolítása',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 tárcák',\n        title: 'Web3 tárcák',\n      },\n    },\n    usernamePage: {\n      successMessage: 'A felhasználóneved frissítve.',\n      title__set: 'Felhasználónév beállítása',\n      title__update: 'Felasználónév frissítése',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} el lesz távolítva ebből a fiókból.',\n        messageLine2: 'Nem fogod tudni használni a bejelentkezéshez, ezt a web3 tárcát.',\n        successMessage: '{{web3Wallet}} eltávolítva a fiókodból.',\n        title: 'Web3 tárca eltávolítása',\n      },\n      subtitle__availableWallets: 'Válaszd ki a web3 tárcát, amit hozzá szeretnél adni a fiókodhoz.',\n      subtitle__unavailableWallets: 'Nincs elérhető web3 tárca.',\n      successMessage: 'A tárca sikeresen hozzáadva a fiókodhoz.',\n      title: 'Web3 tárca hozzáadása',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BACE;AAAA,IACF,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QACE;AAAA,QACF,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cACE;AAAA,QACF,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UACE;AAAA,MACF,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}