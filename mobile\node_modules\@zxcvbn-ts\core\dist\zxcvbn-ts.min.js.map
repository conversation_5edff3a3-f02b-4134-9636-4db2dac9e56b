{"version": 3, "file": "zxcvbn-ts.min.js", "sources": ["../src/helper.ts", "../src/data/const.ts", "../src/data/dateSplits.ts", "../src/matcher/date/matching.ts", "../../../../node_modules/fastest-levenshtein/esm/mod.js", "../src/levenshtein.ts", "../src/data/l33tTable.ts", "../src/data/translationKeys.ts", "../src/matcher/dictionary/variants/matching/unmunger/TrieNode.ts", "../src/matcher/dictionary/variants/matching/unmunger/l33tTableToTrieNode.ts", "../src/Options.ts", "../src/matcher/dictionary/variants/matching/reverse.ts", "../src/matcher/dictionary/variants/matching/unmunger/getCleanPasswords.ts", "../src/matcher/dictionary/variants/matching/l33t.ts", "../src/matcher/dictionary/matching.ts", "../src/matcher/regex/matching.ts", "../src/scoring/utils.ts", "../src/matcher/dictionary/variants/scoring/uppercase.ts", "../src/matcher/dictionary/variants/scoring/l33t.ts", "../src/matcher/spatial/scoring.ts", "../src/scoring/estimate.ts", "../src/matcher/bruteforce/scoring.ts", "../src/matcher/date/scoring.ts", "../src/matcher/dictionary/scoring.ts", "../src/matcher/regex/scoring.ts", "../src/matcher/repeat/scoring.ts", "../src/matcher/sequence/scoring.ts", "../src/matcher/separator/scoring.ts", "../src/scoring/index.ts", "../src/matcher/repeat/matching.ts", "../src/matcher/sequence/matching.ts", "../src/matcher/spatial/matching.ts", "../src/matcher/separator/matching.ts", "../src/Matching.ts", "../src/TimeEstimates.ts", "../src/matcher/bruteforce/feedback.ts", "../src/matcher/date/feedback.ts", "../src/matcher/dictionary/feedback.ts", "../src/matcher/regex/feedback.ts", "../src/matcher/repeat/feedback.ts", "../src/matcher/sequence/feedback.ts", "../src/matcher/spatial/feedback.ts", "../src/matcher/separator/feedback.ts", "../src/Feedback.ts", "../src/index.ts", "../src/debounce.ts"], "sourcesContent": ["export const empty = (obj) => Object.keys(obj).length === 0;\nexport const extend = (listToExtend, list) => \n// eslint-disable-next-line prefer-spread\nlistToExtend.push.apply(listToExtend, list);\nexport const translate = (string, chrMap) => {\n    let newString = string;\n    Object.entries(chrMap).forEach(([key, value]) => {\n        const escapedKey = key.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n        const regex = new RegExp(escapedKey, 'g');\n        newString = newString.replace(regex, value);\n    });\n    return newString;\n};\n// mod implementation that works for negative numbers\nexport const mod = (n, m) => ((n % m) + m) % m;\n// sort on i primary, j secondary\nexport const sorted = (matches) => matches.sort((m1, m2) => m1.i - m2.i || m1.j - m2.j);\nexport const buildRankedDictionary = (orderedList) => {\n    const result = {};\n    let counter = 1; // rank starts at 1, not 0\n    orderedList.forEach((word) => {\n        result[word] = counter;\n        counter += 1;\n    });\n    return result;\n};\n//# sourceMappingURL=helper.js.map", "import dateSplits from './dateSplits';\nexport const DATE_MAX_YEAR = 2050;\nexport const DATE_MIN_YEAR = 1000;\nexport const DATE_SPLITS = dateSplits;\nexport const BRUTEFORCE_CARDINALITY = 10;\nexport const MIN_GUESSES_BEFORE_GROWING_SEQUENCE = 10000;\nexport const MIN_SUBMATCH_GUESSES_SINGLE_CHAR = 10;\nexport const MIN_SUBMATCH_GUESSES_MULTI_CHAR = 50;\nexport const MIN_YEAR_SPACE = 20;\n// \\xbf-\\xdf is a range for almost all special uppercase letter like Ä and so on\nexport const START_UPPER = /^[A-Z\\xbf-\\xdf][^A-Z\\xbf-\\xdf]+$/;\nexport const END_UPPER = /^[^A-Z\\xbf-\\xdf]+[A-Z\\xbf-\\xdf]$/;\n// \\xdf-\\xff is a range for almost all special lowercase letter like ä and so on\nexport const ALL_UPPER = /^[A-Z\\xbf-\\xdf]+$/;\nexport const ALL_UPPER_INVERTED = /^[^a-z\\xdf-\\xff]+$/;\nexport const ALL_LOWER = /^[a-z\\xdf-\\xff]+$/;\nexport const ALL_LOWER_INVERTED = /^[^A-Z\\xbf-\\xdf]+$/;\nexport const ONE_LOWER = /[a-z\\xdf-\\xff]/;\nexport const ONE_UPPER = /[A-Z\\xbf-\\xdf]/;\nexport const ALPHA_INVERTED = /[^A-Za-z\\xbf-\\xdf]/gi;\nexport const ALL_DIGIT = /^\\d+$/;\nexport const REFERENCE_YEAR = new Date().getFullYear();\nexport const REGEXEN = { recentYear: /19\\d\\d|200\\d|201\\d|202\\d/g };\n/* Separators */\nexport const SEPERATOR_CHARS = [\n    ' ',\n    ',',\n    ';',\n    ':',\n    '|',\n    '/',\n    '\\\\',\n    '_',\n    '.',\n    '-',\n];\nexport const SEPERATOR_CHAR_COUNT = SEPERATOR_CHARS.length;\n//# sourceMappingURL=const.js.map", "export default {\n    4: [\n        // for length-4 strings, eg 1191 or 9111, two ways to split:\n        [1, 2],\n        [2, 3], // 91 1 1\n    ],\n    5: [\n        [1, 3],\n        [2, 3],\n        //  [2, 3], // 91 1 11    <- duplicate previous one\n        [2, 4], // 91 11 1    <- New and must be added as bug fix\n    ],\n    6: [\n        [1, 2],\n        [2, 4],\n        [4, 5], // 1991 1 1\n    ],\n    //  1111991\n    7: [\n        [1, 3],\n        [2, 3],\n        [4, 5],\n        [4, 6], // 1991 11 1\n    ],\n    8: [\n        [2, 4],\n        [4, 6], // 1991 11 11\n    ],\n};\n//# sourceMappingURL=dateSplits.js.map", "import { DATE_MAX_YEAR, DATE_MIN_YEAR, DATE_SPLITS, REFERENCE_YEAR, } from '../../data/const';\nimport { sorted } from '../../helper';\n/*\n * -------------------------------------------------------------------------------\n *  date matching ----------------------------------------------------------------\n * -------------------------------------------------------------------------------\n */\nclass MatchDate {\n    /*\n     * a \"date\" is recognized as:\n     *   any 3-tuple that starts or ends with a 2- or 4-digit year,\n     *   with 2 or 0 separator chars (1.1.91 or 1191),\n     *   maybe zero-padded (01-01-91 vs 1-1-91),\n     *   a month between 1 and 12,\n     *   a day between 1 and 31.\n     *\n     * note: this isn't true date parsing in that \"feb 31st\" is allowed,\n     * this doesn't check for leap years, etc.\n     *\n     * recipe:\n     * start with regex to find maybe-dates, then attempt to map the integers\n     * onto month-day-year to filter the maybe-dates into dates.\n     * finally, remove matches that are substrings of other matches to reduce noise.\n     *\n     * note: instead of using a lazy or greedy regex to find many dates over the full string,\n     * this uses a ^...$ regex against every substring of the password -- less performant but leads\n     * to every possible date match.\n     */\n    match({ password }) {\n        const matches = [\n            ...this.getMatchesWithoutSeparator(password),\n            ...this.getMatchesWithSeparator(password),\n        ];\n        const filteredMatches = this.filterNoise(matches);\n        return sorted(filteredMatches);\n    }\n    getMatchesWithSeparator(password) {\n        const matches = [];\n        const maybeDateWithSeparator = /^(\\d{1,4})([\\s/\\\\_.-])(\\d{1,2})\\2(\\d{1,4})$/;\n        // # dates with separators are between length 6 '1/1/91' and 10 '11/11/1991'\n        for (let i = 0; i <= Math.abs(password.length - 6); i += 1) {\n            for (let j = i + 5; j <= i + 9; j += 1) {\n                if (j >= password.length) {\n                    break;\n                }\n                const token = password.slice(i, +j + 1 || 9e9);\n                const regexMatch = maybeDateWithSeparator.exec(token);\n                if (regexMatch != null) {\n                    const dmy = this.mapIntegersToDayMonthYear([\n                        parseInt(regexMatch[1], 10),\n                        parseInt(regexMatch[3], 10),\n                        parseInt(regexMatch[4], 10),\n                    ]);\n                    if (dmy != null) {\n                        matches.push({\n                            pattern: 'date',\n                            token,\n                            i,\n                            j,\n                            separator: regexMatch[2],\n                            year: dmy.year,\n                            month: dmy.month,\n                            day: dmy.day,\n                        });\n                    }\n                }\n            }\n        }\n        return matches;\n    }\n    // eslint-disable-next-line max-statements\n    getMatchesWithoutSeparator(password) {\n        const matches = [];\n        const maybeDateNoSeparator = /^\\d{4,8}$/;\n        const metric = (candidate) => Math.abs(candidate.year - REFERENCE_YEAR);\n        // # dates without separators are between length 4 '1191' and 8 '11111991'\n        for (let i = 0; i <= Math.abs(password.length - 4); i += 1) {\n            for (let j = i + 3; j <= i + 7; j += 1) {\n                if (j >= password.length) {\n                    break;\n                }\n                const token = password.slice(i, +j + 1 || 9e9);\n                if (maybeDateNoSeparator.exec(token)) {\n                    const candidates = [];\n                    const index = token.length;\n                    const splittedDates = DATE_SPLITS[index];\n                    splittedDates.forEach(([k, l]) => {\n                        const dmy = this.mapIntegersToDayMonthYear([\n                            parseInt(token.slice(0, k), 10),\n                            parseInt(token.slice(k, l), 10),\n                            parseInt(token.slice(l), 10),\n                        ]);\n                        if (dmy != null) {\n                            candidates.push(dmy);\n                        }\n                    });\n                    if (candidates.length > 0) {\n                        /*\n                         * at this point: different possible dmy mappings for the same i,j substring.\n                         * match the candidate date that likely takes the fewest guesses: a year closest\n                         * to 2000.\n                         * (scoring.REFERENCE_YEAR).\n                         *\n                         * ie, considering '111504', prefer 11-15-04 to 1-1-1504\n                         * (interpreting '04' as 2004)\n                         */\n                        let bestCandidate = candidates[0];\n                        let minDistance = metric(candidates[0]);\n                        candidates.slice(1).forEach((candidate) => {\n                            const distance = metric(candidate);\n                            if (distance < minDistance) {\n                                bestCandidate = candidate;\n                                minDistance = distance;\n                            }\n                        });\n                        matches.push({\n                            pattern: 'date',\n                            token,\n                            i,\n                            j,\n                            separator: '',\n                            year: bestCandidate.year,\n                            month: bestCandidate.month,\n                            day: bestCandidate.day,\n                        });\n                    }\n                }\n            }\n        }\n        return matches;\n    }\n    /*\n     * matches now contains all valid date strings in a way that is tricky to capture\n     * with regexes only. while thorough, it will contain some unintuitive noise:\n     *\n     * '2015_06_04', in addition to matching 2015_06_04, will also contain\n     * 5(!) other date matches: 15_06_04, 5_06_04, ..., even 2015 (matched as 5/1/2020)\n     *\n     * to reduce noise, remove date matches that are strict substrings of others\n     */\n    filterNoise(matches) {\n        return matches.filter((match) => {\n            let isSubmatch = false;\n            const matchesLength = matches.length;\n            for (let o = 0; o < matchesLength; o += 1) {\n                const otherMatch = matches[o];\n                if (match !== otherMatch) {\n                    if (otherMatch.i <= match.i && otherMatch.j >= match.j) {\n                        isSubmatch = true;\n                        break;\n                    }\n                }\n            }\n            return !isSubmatch;\n        });\n    }\n    /*\n     * given a 3-tuple, discard if:\n     *   middle int is over 31 (for all dmy formats, years are never allowed in the middle)\n     *   middle int is zero\n     *   any int is over the max allowable year\n     *   any int is over two digits but under the min allowable year\n     *   2 integers are over 31, the max allowable day\n     *   2 integers are zero\n     *   all integers are over 12, the max allowable month\n     */\n    // eslint-disable-next-line complexity, max-statements\n    mapIntegersToDayMonthYear(integers) {\n        if (integers[1] > 31 || integers[1] <= 0) {\n            return null;\n        }\n        let over12 = 0;\n        let over31 = 0;\n        let under1 = 0;\n        for (let o = 0, len1 = integers.length; o < len1; o += 1) {\n            const int = integers[o];\n            if ((int > 99 && int < DATE_MIN_YEAR) || int > DATE_MAX_YEAR) {\n                return null;\n            }\n            if (int > 31) {\n                over31 += 1;\n            }\n            if (int > 12) {\n                over12 += 1;\n            }\n            if (int <= 0) {\n                under1 += 1;\n            }\n        }\n        if (over31 >= 2 || over12 === 3 || under1 >= 2) {\n            return null;\n        }\n        return this.getDayMonth(integers);\n    }\n    // eslint-disable-next-line max-statements\n    getDayMonth(integers) {\n        // first look for a four digit year: yyyy + daymonth or daymonth + yyyy\n        const possibleYearSplits = [\n            [integers[2], integers.slice(0, 2)],\n            [integers[0], integers.slice(1, 3)], // year first\n        ];\n        const possibleYearSplitsLength = possibleYearSplits.length;\n        for (let j = 0; j < possibleYearSplitsLength; j += 1) {\n            const [y, rest] = possibleYearSplits[j];\n            if (DATE_MIN_YEAR <= y && y <= DATE_MAX_YEAR) {\n                const dm = this.mapIntegersToDayMonth(rest);\n                if (dm != null) {\n                    return {\n                        year: y,\n                        month: dm.month,\n                        day: dm.day,\n                    };\n                }\n                /*\n                 * for a candidate that includes a four-digit year,\n                 * when the remaining integers don't match to a day and month,\n                 * it is not a date.\n                 */\n                return null;\n            }\n        }\n        // given no four-digit year, two digit years are the most flexible int to match, so\n        // try to parse a day-month out of integers[0..1] or integers[1..0]\n        for (let k = 0; k < possibleYearSplitsLength; k += 1) {\n            const [y, rest] = possibleYearSplits[k];\n            const dm = this.mapIntegersToDayMonth(rest);\n            if (dm != null) {\n                return {\n                    year: this.twoToFourDigitYear(y),\n                    month: dm.month,\n                    day: dm.day,\n                };\n            }\n        }\n        return null;\n    }\n    mapIntegersToDayMonth(integers) {\n        const temp = [integers, integers.slice().reverse()];\n        for (let i = 0; i < temp.length; i += 1) {\n            const data = temp[i];\n            const day = data[0];\n            const month = data[1];\n            if (day >= 1 && day <= 31 && month >= 1 && month <= 12) {\n                return {\n                    day,\n                    month,\n                };\n            }\n        }\n        return null;\n    }\n    twoToFourDigitYear(year) {\n        if (year > 99) {\n            return year;\n        }\n        if (year > 50) {\n            // 87 -> 1987\n            return year + 1900;\n        }\n        // 15 -> 2015\n        return year + 2000;\n    }\n}\nexport default MatchDate;\n//# sourceMappingURL=matching.js.map", "const peq = new Uint32Array(0x10000);\nconst myers_32 = (a, b) => {\n    const n = a.length;\n    const m = b.length;\n    const lst = 1 << (n - 1);\n    let pv = -1;\n    let mv = 0;\n    let sc = n;\n    let i = n;\n    while (i--) {\n        peq[a.charCodeAt(i)] |= 1 << i;\n    }\n    for (i = 0; i < m; i++) {\n        let eq = peq[b.charCodeAt(i)];\n        const xv = eq | mv;\n        eq |= ((eq & pv) + pv) ^ pv;\n        mv |= ~(eq | pv);\n        pv &= eq;\n        if (mv & lst) {\n            sc++;\n        }\n        if (pv & lst) {\n            sc--;\n        }\n        mv = (mv << 1) | 1;\n        pv = (pv << 1) | ~(xv | mv);\n        mv &= xv;\n    }\n    i = n;\n    while (i--) {\n        peq[a.charCodeAt(i)] = 0;\n    }\n    return sc;\n};\nconst myers_x = (b, a) => {\n    const n = a.length;\n    const m = b.length;\n    const mhc = [];\n    const phc = [];\n    const hsize = Math.ceil(n / 32);\n    const vsize = Math.ceil(m / 32);\n    for (let i = 0; i < hsize; i++) {\n        phc[i] = -1;\n        mhc[i] = 0;\n    }\n    let j = 0;\n    for (; j < vsize - 1; j++) {\n        let mv = 0;\n        let pv = -1;\n        const start = j * 32;\n        const vlen = Math.min(32, m) + start;\n        for (let k = start; k < vlen; k++) {\n            peq[b.charCodeAt(k)] |= 1 << k;\n        }\n        for (let i = 0; i < n; i++) {\n            const eq = peq[a.charCodeAt(i)];\n            const pb = (phc[(i / 32) | 0] >>> i) & 1;\n            const mb = (mhc[(i / 32) | 0] >>> i) & 1;\n            const xv = eq | mv;\n            const xh = ((((eq | mb) & pv) + pv) ^ pv) | eq | mb;\n            let ph = mv | ~(xh | pv);\n            let mh = pv & xh;\n            if ((ph >>> 31) ^ pb) {\n                phc[(i / 32) | 0] ^= 1 << i;\n            }\n            if ((mh >>> 31) ^ mb) {\n                mhc[(i / 32) | 0] ^= 1 << i;\n            }\n            ph = (ph << 1) | pb;\n            mh = (mh << 1) | mb;\n            pv = mh | ~(xv | ph);\n            mv = ph & xv;\n        }\n        for (let k = start; k < vlen; k++) {\n            peq[b.charCodeAt(k)] = 0;\n        }\n    }\n    let mv = 0;\n    let pv = -1;\n    const start = j * 32;\n    const vlen = Math.min(32, m - start) + start;\n    for (let k = start; k < vlen; k++) {\n        peq[b.charCodeAt(k)] |= 1 << k;\n    }\n    let score = m;\n    for (let i = 0; i < n; i++) {\n        const eq = peq[a.charCodeAt(i)];\n        const pb = (phc[(i / 32) | 0] >>> i) & 1;\n        const mb = (mhc[(i / 32) | 0] >>> i) & 1;\n        const xv = eq | mv;\n        const xh = ((((eq | mb) & pv) + pv) ^ pv) | eq | mb;\n        let ph = mv | ~(xh | pv);\n        let mh = pv & xh;\n        score += (ph >>> (m - 1)) & 1;\n        score -= (mh >>> (m - 1)) & 1;\n        if ((ph >>> 31) ^ pb) {\n            phc[(i / 32) | 0] ^= 1 << i;\n        }\n        if ((mh >>> 31) ^ mb) {\n            mhc[(i / 32) | 0] ^= 1 << i;\n        }\n        ph = (ph << 1) | pb;\n        mh = (mh << 1) | mb;\n        pv = mh | ~(xv | ph);\n        mv = ph & xv;\n    }\n    for (let k = start; k < vlen; k++) {\n        peq[b.charCodeAt(k)] = 0;\n    }\n    return score;\n};\nconst distance = (a, b) => {\n    if (a.length < b.length) {\n        const tmp = b;\n        b = a;\n        a = tmp;\n    }\n    if (b.length === 0) {\n        return a.length;\n    }\n    if (a.length <= 32) {\n        return myers_32(a, b);\n    }\n    return myers_x(a, b);\n};\nconst closest = (str, arr) => {\n    let min_distance = Infinity;\n    let min_index = 0;\n    for (let i = 0; i < arr.length; i++) {\n        const dist = distance(str, arr[i]);\n        if (dist < min_distance) {\n            min_distance = dist;\n            min_index = i;\n        }\n    }\n    return arr[min_index];\n};\nexport { closest, distance };\n", "import { distance } from 'fastest-levenshtein';\nconst getUsedThreshold = (password, entry, threshold) => {\n    const isPasswordToShort = password.length <= entry.length;\n    const isThresholdLongerThanPassword = password.length <= threshold;\n    const shouldUsePasswordLength = isPasswordToShort || isThresholdLongerThanPassword;\n    // if password is too small use the password length divided by 4 while the threshold needs to be at least 1\n    return shouldUsePasswordLength ? Math.ceil(password.length / 4) : threshold;\n};\nconst findLevenshteinDistance = (password, rankedDictionary, threshold) => {\n    let foundDistance = 0;\n    const found = Object.keys(rankedDictionary).find((entry) => {\n        const usedThreshold = getUsedThreshold(password, entry, threshold);\n        if (Math.abs(password.length - entry.length) > usedThreshold) {\n            return false;\n        }\n        const foundEntryDistance = distance(password, entry);\n        const isInThreshold = foundEntryDistance <= usedThreshold;\n        if (isInThreshold) {\n            foundDistance = foundEntryDistance;\n        }\n        return isInThreshold;\n    });\n    if (found) {\n        return {\n            levenshteinDistance: foundDistance,\n            levenshteinDistanceEntry: found,\n        };\n    }\n    return {};\n};\nexport default findLevenshteinDistance;\n//# sourceMappingURL=levenshtein.js.map", "export default {\n    a: ['4', '@'],\n    b: ['8'],\n    c: ['(', '{', '[', '<'],\n    d: ['6', '|)'],\n    e: ['3'],\n    f: ['#'],\n    g: ['6', '9', '&'],\n    h: ['#', '|-|'],\n    i: ['1', '!', '|'],\n    k: ['<', '|<'],\n    l: ['!', '1', '|', '7'],\n    m: ['^^', 'nn', '2n', '/\\\\\\\\/\\\\\\\\'],\n    n: ['//'],\n    o: ['0', '()'],\n    q: ['9'],\n    u: ['|_|'],\n    s: ['$', '5'],\n    t: ['+', '7'],\n    v: ['<', '>', '/'],\n    w: ['^/', 'uu', 'vv', '2u', '2v', '\\\\\\\\/\\\\\\\\/'],\n    x: ['%', '><'],\n    z: ['2'],\n};\n//# sourceMappingURL=l33tTable.js.map", "export default {\n    warnings: {\n        straightRow: 'straightRow',\n        keyPattern: 'keyPattern',\n        simpleRepeat: 'simpleRepeat',\n        extendedRepeat: 'extendedRepeat',\n        sequences: 'sequences',\n        recentYears: 'recentYears',\n        dates: 'dates',\n        topTen: 'topTen',\n        topHundred: 'topHundred',\n        common: 'common',\n        similarToCommon: 'similarToCommon',\n        wordByItself: 'wordByItself',\n        namesByThemselves: 'namesByThemselves',\n        commonNames: 'commonNames',\n        userInputs: 'userInputs',\n        pwned: 'pwned',\n    },\n    suggestions: {\n        l33t: 'l33t',\n        reverseWords: 'reverseWords',\n        allUppercase: 'allUppercase',\n        capitalization: 'capitalization',\n        dates: 'dates',\n        recentYears: 'recentYears',\n        associatedYears: 'associatedYears',\n        sequences: 'sequences',\n        repeated: 'repeated',\n        longerKeyboardPattern: 'longerKeyboardPattern',\n        anotherWord: 'anotherWord',\n        useWords: 'useWords',\n        noNeed: 'noNeed',\n        pwned: 'pwned',\n    },\n    timeEstimation: {\n        ltSecond: 'ltSecond',\n        second: 'second',\n        seconds: 'seconds',\n        minute: 'minute',\n        minutes: 'minutes',\n        hour: 'hour',\n        hours: 'hours',\n        day: 'day',\n        days: 'days',\n        month: 'month',\n        months: 'months',\n        year: 'year',\n        years: 'years',\n        centuries: 'centuries',\n    },\n};\n//# sourceMappingURL=translationKeys.js.map", "export default class TrieNode {\n    constructor(parents = []) {\n        this.parents = parents;\n        // eslint-disable-next-line no-use-before-define\n        this.children = new Map();\n    }\n    addSub(key, ...subs) {\n        const firstChar = key.charAt(0);\n        if (!this.children.has(firstChar)) {\n            this.children.set(firstChar, new TrieNode([...this.parents, firstChar]));\n        }\n        let cur = this.children.get(firstChar);\n        for (let i = 1; i < key.length; i += 1) {\n            const c = key.charAt(i);\n            if (!cur.hasChild(c)) {\n                cur.addChild(c);\n            }\n            cur = cur.getChild(c);\n        }\n        cur.subs = (cur.subs || []).concat(subs);\n        return this;\n    }\n    getChild(child) {\n        return this.children.get(child);\n    }\n    isTerminal() {\n        return !!this.subs;\n    }\n    addChild(child) {\n        if (!this.hasChild(child)) {\n            this.children.set(child, new TrieNode([...this.parents, child]));\n        }\n    }\n    hasChild(child) {\n        return this.children.has(child);\n    }\n}\n//# sourceMappingURL=TrieNode.js.map", "export default (l33tTable, triNode) => {\n    Object.entries(l33tTable).forEach(([letter, substitutions]) => {\n        substitutions.forEach((substitution) => {\n            triNode.addSub(substitution, letter);\n        });\n    });\n    return triNode;\n};\n//# sourceMappingURL=l33tTableToTrieNode.js.map", "import { buildRankedDictionary } from './helper';\nimport l33tTable from './data/l33tTable';\nimport translationKeys from './data/translationKeys';\nimport TrieNode from './matcher/dictionary/variants/matching/unmunger/TrieNode';\nimport l33tTableToTrieNode from './matcher/dictionary/variants/matching/unmunger/l33tTableToTrieNode';\nexport class Options {\n    constructor() {\n        this.matchers = {};\n        this.l33tTable = l33tTable;\n        this.trieNodeRoot = l33tTableToTrieNode(l33tTable, new TrieNode());\n        this.dictionary = {\n            userInputs: [],\n        };\n        this.rankedDictionaries = {};\n        this.rankedDictionariesMaxWordSize = {};\n        this.translations = translationKeys;\n        this.graphs = {};\n        this.useLevenshteinDistance = false;\n        this.levenshteinThreshold = 2;\n        this.l33tMaxSubstitutions = 100;\n        this.maxLength = 256;\n        this.setRankedDictionaries();\n    }\n    // eslint-disable-next-line max-statements,complexity\n    setOptions(options = {}) {\n        if (options.l33tTable) {\n            this.l33tTable = options.l33tTable;\n            this.trieNodeRoot = l33tTableToTrieNode(options.l33tTable, new TrieNode());\n        }\n        if (options.dictionary) {\n            this.dictionary = options.dictionary;\n            this.setRankedDictionaries();\n        }\n        if (options.translations) {\n            this.setTranslations(options.translations);\n        }\n        if (options.graphs) {\n            this.graphs = options.graphs;\n        }\n        if (options.useLevenshteinDistance !== undefined) {\n            this.useLevenshteinDistance = options.useLevenshteinDistance;\n        }\n        if (options.levenshteinThreshold !== undefined) {\n            this.levenshteinThreshold = options.levenshteinThreshold;\n        }\n        if (options.l33tMaxSubstitutions !== undefined) {\n            this.l33tMaxSubstitutions = options.l33tMaxSubstitutions;\n        }\n        if (options.maxLength !== undefined) {\n            this.maxLength = options.maxLength;\n        }\n    }\n    setTranslations(translations) {\n        if (this.checkCustomTranslations(translations)) {\n            this.translations = translations;\n        }\n        else {\n            throw new Error('Invalid translations object fallback to keys');\n        }\n    }\n    checkCustomTranslations(translations) {\n        let valid = true;\n        Object.keys(translationKeys).forEach((type) => {\n            if (type in translations) {\n                const translationType = type;\n                Object.keys(translationKeys[translationType]).forEach((key) => {\n                    if (!(key in translations[translationType])) {\n                        valid = false;\n                    }\n                });\n            }\n            else {\n                valid = false;\n            }\n        });\n        return valid;\n    }\n    setRankedDictionaries() {\n        const rankedDictionaries = {};\n        const rankedDictionariesMaxWorkSize = {};\n        Object.keys(this.dictionary).forEach((name) => {\n            rankedDictionaries[name] = buildRankedDictionary(this.dictionary[name]);\n            rankedDictionariesMaxWorkSize[name] =\n                this.getRankedDictionariesMaxWordSize(this.dictionary[name]);\n        });\n        this.rankedDictionaries = rankedDictionaries;\n        this.rankedDictionariesMaxWordSize = rankedDictionariesMaxWorkSize;\n    }\n    getRankedDictionariesMaxWordSize(list) {\n        const data = list.map((el) => {\n            if (typeof el !== 'string') {\n                return el.toString().length;\n            }\n            return el.length;\n        });\n        // do not use Math.max(...data) because it can result in max stack size error because every entry will be used as an argument\n        if (data.length === 0) {\n            return 0;\n        }\n        return data.reduce((a, b) => Math.max(a, b), -Infinity);\n    }\n    buildSanitizedRankedDictionary(list) {\n        const sanitizedInputs = [];\n        list.forEach((input) => {\n            const inputType = typeof input;\n            if (inputType === 'string' ||\n                inputType === 'number' ||\n                inputType === 'boolean') {\n                sanitizedInputs.push(input.toString().toLowerCase());\n            }\n        });\n        return buildRankedDictionary(sanitizedInputs);\n    }\n    extendUserInputsDictionary(dictionary) {\n        if (!this.dictionary.userInputs) {\n            this.dictionary.userInputs = [];\n        }\n        const newList = [...this.dictionary.userInputs, ...dictionary];\n        this.rankedDictionaries.userInputs =\n            this.buildSanitizedRankedDictionary(newList);\n        this.rankedDictionariesMaxWordSize.userInputs =\n            this.getRankedDictionariesMaxWordSize(newList);\n    }\n    addMatcher(name, matcher) {\n        if (this.matchers[name]) {\n            console.info(`Matcher ${name} already exists`);\n        }\n        else {\n            this.matchers[name] = matcher;\n        }\n    }\n}\nexport const zxcvbnOptions = new Options();\n//# sourceMappingURL=Options.js.map", "/*\n * -------------------------------------------------------------------------------\n *  Dictionary reverse matching --------------------------------------------------\n * -------------------------------------------------------------------------------\n */\nclass MatchReverse {\n    constructor(defaultMatch) {\n        this.defaultMatch = defaultMatch;\n    }\n    match({ password }) {\n        const passwordReversed = password.split('').reverse().join('');\n        return this.defaultMatch({\n            password: passwordReversed,\n        }).map((match) => ({\n            ...match,\n            token: match.token.split('').reverse().join(''),\n            reversed: true,\n            // map coordinates back to original string\n            i: password.length - 1 - match.j,\n            j: password.length - 1 - match.i,\n        }));\n    }\n}\nexport default MatchReverse;\n//# sourceMappingURL=reverse.js.map", "class CleanPasswords {\n    constructor({ substr, limit, trieRoot }) {\n        this.buffer = [];\n        this.finalPasswords = [];\n        this.substr = substr;\n        this.limit = limit;\n        this.trieRoot = trieRoot;\n    }\n    getAllPossibleSubsAtIndex(index) {\n        const nodes = [];\n        let cur = this.trieRoot;\n        for (let i = index; i < this.substr.length; i += 1) {\n            const character = this.substr.charAt(i);\n            cur = cur.getChild(character);\n            if (!cur) {\n                break;\n            }\n            nodes.push(cur);\n        }\n        return nodes;\n    }\n    // eslint-disable-next-line complexity,max-statements\n    helper({ onlyFullSub, isFullSub, index, subIndex, changes, lastSubLetter, consecutiveSubCount, }) {\n        if (this.finalPasswords.length >= this.limit) {\n            return;\n        }\n        if (index === this.substr.length) {\n            if (onlyFullSub === isFullSub) {\n                this.finalPasswords.push({ password: this.buffer.join(''), changes });\n            }\n            return;\n        }\n        // first, exhaust all possible substitutions at this index\n        const nodes = [...this.getAllPossibleSubsAtIndex(index)];\n        let hasSubs = false;\n        // iterate backward to get wider substitutions first\n        for (let i = index + nodes.length - 1; i >= index; i -= 1) {\n            const cur = nodes[i - index];\n            if (cur.isTerminal()) {\n                // Skip if this would be a 4th or more consecutive substitution of the same letter\n                // this should work in all language as there shouldn't be the same letter more than four times in a row\n                // So we can ignore the rest to save calculation time\n                if (lastSubLetter === cur.parents.join('') &&\n                    consecutiveSubCount >= 3) {\n                    // eslint-disable-next-line no-continue\n                    continue;\n                }\n                hasSubs = true;\n                const subs = cur.subs;\n                // eslint-disable-next-line no-restricted-syntax\n                for (const sub of subs) {\n                    this.buffer.push(sub);\n                    const newSubs = changes.concat({\n                        i: subIndex,\n                        letter: sub,\n                        substitution: cur.parents.join(''),\n                    });\n                    // recursively build the rest of the string\n                    this.helper({\n                        onlyFullSub,\n                        isFullSub,\n                        index: i + 1,\n                        subIndex: subIndex + sub.length,\n                        changes: newSubs,\n                        lastSubLetter: cur.parents.join(''),\n                        consecutiveSubCount: lastSubLetter === cur.parents.join('')\n                            ? consecutiveSubCount + 1\n                            : 1,\n                    });\n                    // backtrack by ignoring the added postfix\n                    this.buffer.pop();\n                    if (this.finalPasswords.length >= this.limit) {\n                        return;\n                    }\n                }\n            }\n        }\n        // next, generate all combos without doing a substitution at this index\n        // if a partial substitution is requested or there are no substitutions at this index\n        if (!onlyFullSub || !hasSubs) {\n            const firstChar = this.substr.charAt(index);\n            this.buffer.push(firstChar);\n            this.helper({\n                onlyFullSub,\n                isFullSub: isFullSub && !hasSubs,\n                index: index + 1,\n                subIndex: subIndex + 1,\n                changes,\n                lastSubLetter,\n                consecutiveSubCount,\n            });\n            this.buffer.pop();\n        }\n    }\n    getAll() {\n        // only full substitution\n        this.helper({\n            onlyFullSub: true,\n            isFullSub: true,\n            index: 0,\n            subIndex: 0,\n            changes: [],\n            lastSubLetter: undefined,\n            consecutiveSubCount: 0,\n        });\n        // only partial substitution\n        this.helper({\n            onlyFullSub: false,\n            isFullSub: true,\n            index: 0,\n            subIndex: 0,\n            changes: [],\n            lastSubLetter: undefined,\n            consecutiveSubCount: 0,\n        });\n        return this.finalPasswords;\n    }\n}\nconst getCleanPasswords = (password, limit, trieRoot) => {\n    const helper = new CleanPasswords({\n        substr: password,\n        limit,\n        trieRoot,\n    });\n    return helper.getAll();\n};\nexport default getCleanPasswords;\n//# sourceMappingURL=getCleanPasswords.js.map", "import { zxcvbnOptions } from '../../../../Options';\nimport getCleanPasswords from './unmunger/getCleanPasswords';\nconst getExtras = (passwordWithSubs, i, j) => {\n    const previousChanges = passwordWithSubs.changes.filter((changes) => {\n        return changes.i < i;\n    });\n    const iUnsubbed = previousChanges.reduce((value, change) => {\n        return value - change.letter.length + change.substitution.length;\n    }, i);\n    const usedChanges = passwordWithSubs.changes.filter((changes) => {\n        return changes.i >= i && changes.i <= j;\n    });\n    const jUnsubbed = usedChanges.reduce((value, change) => {\n        return value - change.letter.length + change.substitution.length;\n    }, j - i + iUnsubbed);\n    const filtered = [];\n    const subDisplay = [];\n    usedChanges.forEach((value) => {\n        const existingIndex = filtered.findIndex((t) => {\n            return t.letter === value.letter && t.substitution === value.substitution;\n        });\n        if (existingIndex < 0) {\n            filtered.push({\n                letter: value.letter,\n                substitution: value.substitution,\n            });\n            subDisplay.push(`${value.substitution} -> ${value.letter}`);\n        }\n    });\n    return {\n        i: iUnsubbed,\n        j: jUnsubbed,\n        subs: filtered,\n        subDisplay: subDisplay.join(', '),\n    };\n};\n/*\n * -------------------------------------------------------------------------------\n *  Dictionary l33t matching -----------------------------------------------------\n * -------------------------------------------------------------------------------\n */\nclass MatchL33t {\n    constructor(defaultMatch) {\n        this.defaultMatch = defaultMatch;\n    }\n    isAlreadyIncluded(matches, newMatch) {\n        return matches.some((l33tMatch) => {\n            return Object.entries(l33tMatch).every(([key, value]) => {\n                return key === 'subs' || value === newMatch[key];\n            });\n        });\n    }\n    match({ password }) {\n        const matches = [];\n        const subbedPasswords = getCleanPasswords(password, zxcvbnOptions.l33tMaxSubstitutions, zxcvbnOptions.trieNodeRoot);\n        let hasFullMatch = false;\n        let isFullSubstitution = true;\n        subbedPasswords.forEach((subbedPassword) => {\n            if (hasFullMatch) {\n                return;\n            }\n            const matchedDictionary = this.defaultMatch({\n                password: subbedPassword.password,\n                useLevenshtein: isFullSubstitution,\n            });\n            // only the first entry has a full substitution\n            isFullSubstitution = false;\n            matchedDictionary.forEach((match) => {\n                if (!hasFullMatch) {\n                    hasFullMatch = match.i === 0 && match.j === password.length - 1;\n                }\n                const extras = getExtras(subbedPassword, match.i, match.j);\n                const token = password.slice(extras.i, +extras.j + 1 || 9e9);\n                const newMatch = {\n                    ...match,\n                    l33t: true,\n                    token,\n                    ...extras,\n                };\n                const alreadyIncluded = this.isAlreadyIncluded(matches, newMatch);\n                // only return the matches that contain an actual substitution\n                if (token.toLowerCase() !== match.matchedWord && !alreadyIncluded) {\n                    matches.push(newMatch);\n                }\n            });\n        });\n        // filter single-character l33t matches to reduce noise.\n        // otherwise '1' matches 'i', '4' matches 'a', both very common English words\n        // with low dictionary rank.\n        return matches.filter((match) => match.token.length > 1);\n    }\n}\nexport default MatchL33t;\n//# sourceMappingURL=l33t.js.map", "import findLevenshteinDistance from '../../levenshtein';\nimport { sorted } from '../../helper';\nimport { zxcvbnOptions } from '../../Options';\nimport Reverse from './variants/matching/reverse';\nimport L33t from './variants/matching/l33t';\nclass MatchDictionary {\n    constructor() {\n        this.l33t = new L33t(this.defaultMatch);\n        this.reverse = new Reverse(this.defaultMatch);\n    }\n    match({ password }) {\n        const matches = [\n            ...this.defaultMatch({\n                password,\n            }),\n            ...this.reverse.match({ password }),\n            ...this.l33t.match({ password }),\n        ];\n        return sorted(matches);\n    }\n    defaultMatch({ password, useLevenshtein = true }) {\n        const matches = [];\n        const passwordLength = password.length;\n        const passwordLower = password.toLowerCase();\n        // eslint-disable-next-line complexity,max-statements\n        Object.keys(zxcvbnOptions.rankedDictionaries).forEach((dictionaryName) => {\n            const rankedDict = zxcvbnOptions.rankedDictionaries[dictionaryName];\n            const longestDictionaryWordSize = zxcvbnOptions.rankedDictionariesMaxWordSize[dictionaryName];\n            const searchWidth = Math.min(longestDictionaryWordSize, passwordLength);\n            for (let i = 0; i < passwordLength; i += 1) {\n                const searchEnd = Math.min(i + searchWidth, passwordLength);\n                for (let j = i; j < searchEnd; j += 1) {\n                    const usedPassword = passwordLower.slice(i, +j + 1 || 9e9);\n                    const isInDictionary = usedPassword in rankedDict;\n                    let foundLevenshteinDistance = {};\n                    // only use levenshtein distance on full password to minimize the performance drop\n                    // and because otherwise there would be to many false positives\n                    const isFullPassword = i === 0 && j === passwordLength - 1;\n                    if (zxcvbnOptions.useLevenshteinDistance &&\n                        isFullPassword &&\n                        !isInDictionary &&\n                        useLevenshtein) {\n                        foundLevenshteinDistance = findLevenshteinDistance(usedPassword, rankedDict, zxcvbnOptions.levenshteinThreshold);\n                    }\n                    const isLevenshteinMatch = Object.keys(foundLevenshteinDistance).length !== 0;\n                    if (isInDictionary || isLevenshteinMatch) {\n                        const usedRankPassword = isLevenshteinMatch\n                            ? foundLevenshteinDistance.levenshteinDistanceEntry\n                            : usedPassword;\n                        const rank = rankedDict[usedRankPassword];\n                        matches.push({\n                            pattern: 'dictionary',\n                            i,\n                            j,\n                            token: password.slice(i, +j + 1 || 9e9),\n                            matchedWord: usedPassword,\n                            rank,\n                            dictionaryName: dictionaryName,\n                            reversed: false,\n                            l33t: false,\n                            ...foundLevenshteinDistance,\n                        });\n                    }\n                }\n            }\n        });\n        return matches;\n    }\n}\nexport default MatchDictionary;\n//# sourceMappingURL=matching.js.map", "import { REGEXEN } from '../../data/const';\nimport { sorted } from '../../helper';\n/*\n * -------------------------------------------------------------------------------\n *  regex matching ---------------------------------------------------------------\n * -------------------------------------------------------------------------------\n */\nclass MatchRegex {\n    match({ password, regexes = REGEXEN }) {\n        const matches = [];\n        Object.keys(regexes).forEach((name) => {\n            const regex = regexes[name];\n            regex.lastIndex = 0; // keeps regexMatch stateless\n            let regexMatch;\n            // eslint-disable-next-line no-cond-assign\n            while ((regexMatch = regex.exec(password))) {\n                if (regexMatch) {\n                    const token = regexMatch[0];\n                    matches.push({\n                        pattern: 'regex',\n                        token,\n                        i: regexMatch.index,\n                        j: regexMatch.index + regexMatch[0].length - 1,\n                        regexName: name,\n                        regexMatch,\n                    });\n                }\n            }\n        });\n        return sorted(matches);\n    }\n}\nexport default MatchRegex;\n//# sourceMappingURL=matching.js.map", "export default {\n    // binomial coefficients\n    // src: http://blog.plover.com/math/choose.html\n    nCk(n, k) {\n        let count = n;\n        if (k > count) {\n            return 0;\n        }\n        if (k === 0) {\n            return 1;\n        }\n        let coEff = 1;\n        for (let i = 1; i <= k; i += 1) {\n            coEff *= count;\n            coEff /= i;\n            count -= 1;\n        }\n        return coEff;\n    },\n    log10(n) {\n        if (n === 0)\n            return 0;\n        return Math.log(n) / Math.log(10); // IE doesn't support Math.log10 :(\n    },\n    log2(n) {\n        return Math.log(n) / Math.log(2);\n    },\n    factorial(num) {\n        let rval = 1;\n        for (let i = 2; i <= num; i += 1)\n            rval *= i;\n        return rval;\n    },\n};\n//# sourceMappingURL=utils.js.map", "import utils from '../../../../scoring/utils';\nimport { START_UPPER, END_UPPER, ALL_UPPER_INVERTED, ALL_LOWER_INVERTED, ONE_LOWER, ONE_UPPER, ALPHA_INVERTED, } from '../../../../data/const';\nconst getVariations = (cleanedWord) => {\n    const wordArray = cleanedWord.split('');\n    const upperCaseCount = wordArray.filter((char) => char.match(ONE_UPPER)).length;\n    const lowerCaseCount = wordArray.filter((char) => char.match(ONE_LOWER)).length;\n    let variations = 0;\n    const variationLength = Math.min(upperCaseCount, lowerCaseCount);\n    for (let i = 1; i <= variationLength; i += 1) {\n        variations += utils.nCk(upperCaseCount + lowerCaseCount, i);\n    }\n    return variations;\n};\nexport default (word) => {\n    // clean words of non alpha characters to remove the reward effekt to capitalize the first letter https://github.com/dropbox/zxcvbn/issues/232\n    const cleanedWord = word.replace(ALPHA_INVERTED, '');\n    if (cleanedWord.match(ALL_LOWER_INVERTED) ||\n        cleanedWord.toLowerCase() === cleanedWord) {\n        return 1;\n    }\n    // a capitalized word is the most common capitalization scheme,\n    // so it only doubles the search space (uncapitalized + capitalized).\n    // all caps and end-capitalized are common enough too, underestimate as 2x factor to be safe.\n    const commonCases = [START_UPPER, END_UPPER, ALL_UPPER_INVERTED];\n    const commonCasesLength = commonCases.length;\n    for (let i = 0; i < commonCasesLength; i += 1) {\n        const regex = commonCases[i];\n        if (cleanedWord.match(regex)) {\n            return 2;\n        }\n    }\n    // otherwise calculate the number of ways to capitalize U+L uppercase+lowercase letters\n    // with U uppercase letters or less. or, if there's more uppercase than lower (for eg. PASSwORD),\n    // the number of ways to lowercase U+L letters with L lowercase letters or less.\n    return getVariations(cleanedWord);\n};\n//# sourceMappingURL=uppercase.js.map", "import utils from '../../../../scoring/utils';\nconst countSubstring = (string, substring) => {\n    let count = 0;\n    let pos = string.indexOf(substring);\n    while (pos >= 0) {\n        count += 1;\n        pos = string.indexOf(substring, pos + substring.length);\n    }\n    return count;\n};\nconst getCounts = ({ sub, token }) => {\n    // lower-case match.token before calculating: capitalization shouldn't affect l33t calc.\n    const tokenLower = token.toLowerCase();\n    // num of subbed chars\n    const subbedCount = countSubstring(tokenLower, sub.substitution);\n    // num of unsubbed chars\n    const unsubbedCount = countSubstring(tokenLower, sub.letter);\n    return {\n        subbedCount,\n        unsubbedCount,\n    };\n};\nexport default ({ l33t, subs, token }) => {\n    if (!l33t) {\n        return 1;\n    }\n    let variations = 1;\n    subs.forEach((sub) => {\n        const { subbedCount, unsubbedCount } = getCounts({ sub, token });\n        if (subbedCount === 0 || unsubbedCount === 0) {\n            // for this sub, password is either fully subbed (444) or fully unsubbed (aaa)\n            // treat that as doubling the space (attacker needs to try fully subbed chars in addition to\n            // unsubbed.)\n            variations *= 2;\n        }\n        else {\n            // this case is similar to capitalization:\n            // with aa44a, U = 3, S = 2, attacker needs to try unsubbed + one sub + two subs\n            const p = Math.min(unsubbedCount, subbedCount);\n            let possibilities = 0;\n            for (let i = 1; i <= p; i += 1) {\n                possibilities += utils.nCk(unsubbedCount + subbedCount, i);\n            }\n            variations *= possibilities;\n        }\n    });\n    return variations;\n};\n//# sourceMappingURL=l33t.js.map", "import utils from '../../scoring/utils';\nimport { zxcvbnOptions } from '../../Options';\nconst calcAverageDegree = (graph) => {\n    let average = 0;\n    Object.keys(graph).forEach((key) => {\n        const neighbors = graph[key];\n        average += neighbors.filter((entry) => !!entry).length;\n    });\n    average /= Object.entries(graph).length;\n    return average;\n};\nconst estimatePossiblePatterns = ({ token, graph, turns, }) => {\n    const startingPosition = Object.keys(zxcvbnOptions.graphs[graph]).length;\n    const averageDegree = calcAverageDegree(zxcvbnOptions.graphs[graph]);\n    let guesses = 0;\n    const tokenLength = token.length;\n    // # estimate the number of possible patterns w/ tokenLength or less with turns or less.\n    for (let i = 2; i <= tokenLength; i += 1) {\n        const possibleTurns = Math.min(turns, i - 1);\n        for (let j = 1; j <= possibleTurns; j += 1) {\n            guesses += utils.nCk(i - 1, j - 1) * startingPosition * averageDegree ** j;\n        }\n    }\n    return guesses;\n};\nexport default ({ graph, token, shiftedCount, turns, }) => {\n    let guesses = estimatePossiblePatterns({ token, graph, turns });\n    // add extra guesses for shifted keys. (% instead of 5, A instead of a.)\n    // math is similar to extra guesses of l33t substitutions in dictionary matches.\n    if (shiftedCount) {\n        const unShiftedCount = token.length - shiftedCount;\n        if (shiftedCount === 0 || unShiftedCount === 0) {\n            guesses *= 2;\n        }\n        else {\n            let shiftedVariations = 0;\n            for (let i = 1; i <= Math.min(shiftedCount, unShiftedCount); i += 1) {\n                shiftedVariations += utils.nCk(shiftedCount + unShiftedCount, i);\n            }\n            guesses *= shiftedVariations;\n        }\n    }\n    return Math.round(guesses);\n};\n//# sourceMappingURL=scoring.js.map", "import { MIN_SUBMATCH_GUESSES_SINGLE_CHAR, MIN_SUBMATCH_GUESSES_MULTI_CHAR, } from '../data/const';\nimport utils from './utils';\nimport { zxcvbnOptions } from '../Options';\nimport bruteforceMatcher from '../matcher/bruteforce/scoring';\nimport dateMatcher from '../matcher/date/scoring';\nimport dictionaryMatcher from '../matcher/dictionary/scoring';\nimport regexMatcher from '../matcher/regex/scoring';\nimport repeatMatcher from '../matcher/repeat/scoring';\nimport sequenceMatcher from '../matcher/sequence/scoring';\nimport spatialMatcher from '../matcher/spatial/scoring';\nimport separatorMatcher from '../matcher/separator/scoring';\nconst getMinGuesses = (match, password) => {\n    let minGuesses = 1;\n    if (match.token.length < password.length) {\n        if (match.token.length === 1) {\n            minGuesses = MIN_SUBMATCH_GUESSES_SINGLE_CHAR;\n        }\n        else {\n            minGuesses = MIN_SUBMATCH_GUESSES_MULTI_CHAR;\n        }\n    }\n    return minGuesses;\n};\nconst matchers = {\n    bruteforce: bruteforceMatcher,\n    date: dateMatcher,\n    dictionary: dictionaryMatcher,\n    regex: regexMatcher,\n    repeat: repeatMatcher,\n    sequence: sequenceMatcher,\n    spatial: spatialMatcher,\n    separator: separatorMatcher,\n};\nconst getScoring = (name, match) => {\n    if (matchers[name]) {\n        return matchers[name](match);\n    }\n    if (zxcvbnOptions.matchers[name] &&\n        'scoring' in zxcvbnOptions.matchers[name]) {\n        return zxcvbnOptions.matchers[name].scoring(match);\n    }\n    return 0;\n};\n// ------------------------------------------------------------------------------\n// guess estimation -- one function per match pattern ---------------------------\n// ------------------------------------------------------------------------------\n// eslint-disable-next-line complexity, max-statements\nexport default (match, password) => {\n    const extraData = {};\n    // a match's guess estimate doesn't change. cache it.\n    if ('guesses' in match && match.guesses != null) {\n        return match;\n    }\n    const minGuesses = getMinGuesses(match, password);\n    const estimationResult = getScoring(match.pattern, match);\n    let guesses = 0;\n    if (typeof estimationResult === 'number') {\n        guesses = estimationResult;\n    }\n    else if (match.pattern === 'dictionary') {\n        guesses = estimationResult.calculation;\n        extraData.baseGuesses = estimationResult.baseGuesses;\n        extraData.uppercaseVariations = estimationResult.uppercaseVariations;\n        extraData.l33tVariations = estimationResult.l33tVariations;\n    }\n    const matchGuesses = Math.max(guesses, minGuesses);\n    return {\n        ...match,\n        ...extraData,\n        guesses: matchGuesses,\n        guessesLog10: utils.log10(matchGuesses),\n    };\n};\n//# sourceMappingURL=estimate.js.map", "import { BRUTEFORCE_CARDINALITY, MIN_SUBMATCH_GUESSES_SINGLE_CHAR, MIN_SUBMATCH_GUESSES_MULTI_CHAR, } from '../../data/const';\nexport default ({ token }) => {\n    let guesses = BRUTEFORCE_CARDINALITY ** token.length;\n    if (guesses === Number.POSITIVE_INFINITY) {\n        guesses = Number.MAX_VALUE;\n    }\n    let minGuesses;\n    // small detail: make bruteforce matches at minimum one guess bigger than smallest allowed\n    // submatch guesses, such that non-bruteforce submatches over the same [i..j] take precedence.\n    if (token.length === 1) {\n        minGuesses = MIN_SUBMATCH_GUESSES_SINGLE_CHAR + 1;\n    }\n    else {\n        minGuesses = MIN_SUBMATCH_GUESSES_MULTI_CHAR + 1;\n    }\n    return Math.max(guesses, minGuesses);\n};\n//# sourceMappingURL=scoring.js.map", "import { MIN_YEAR_SPACE, REFERENCE_YEAR } from '../../data/const';\nexport default ({ year, separator }) => {\n    // base guesses: (year distance from REFERENCE_YEAR) * num_days * num_years\n    const yearSpace = Math.max(Math.abs(year - REFERENCE_YEAR), MIN_YEAR_SPACE);\n    let guesses = yearSpace * 365;\n    // add factor of 4 for separator selection (one of ~4 choices)\n    if (separator) {\n        guesses *= 4;\n    }\n    return guesses;\n};\n//# sourceMappingURL=scoring.js.map", "import uppercaseVariant from './variants/scoring/uppercase';\nimport l33tVariant from './variants/scoring/l33t';\nexport default ({ rank, reversed, l33t, subs, token, dictionaryName, }) => {\n    const baseGuesses = rank; // keep these as properties for display purposes\n    const uppercaseVariations = uppercaseVariant(token);\n    const l33tVariations = l33tVariant({ l33t, subs, token });\n    const reversedVariations = (reversed && 2) || 1;\n    let calculation;\n    if (dictionaryName === 'diceware') {\n        // diceware dictionaries are special, so we get a simple scoring of 1/2 of 6^5 (6 digits on 5 dice)\n        // to get fix entropy of ~12.9 bits for every entry https://en.wikipedia.org/wiki/Diceware#:~:text=The%20level%20of,bits\n        calculation = 6 ** 5 / 2;\n    }\n    else {\n        calculation =\n            baseGuesses * uppercaseVariations * l33tVariations * reversedVariations;\n    }\n    return {\n        baseGuesses,\n        uppercaseVariations,\n        l33tVariations,\n        calculation,\n    };\n};\n//# sourceMappingURL=scoring.js.map", "import { MIN_YEAR_SPACE, REFERENCE_YEAR } from '../../data/const';\nexport default ({ regexName, regexMatch, token, }) => {\n    const charClassBases = {\n        alphaLower: 26,\n        alphaUpper: 26,\n        alpha: 52,\n        alphanumeric: 62,\n        digits: 10,\n        symbols: 33,\n    };\n    if (regexName in charClassBases) {\n        return (charClassBases[regexName] ** token.length);\n    }\n    // TODO add more regex types for example special dates like 09.11\n    // eslint-disable-next-line default-case\n    switch (regexName) {\n        case 'recentYear':\n            // conservative estimate of year space: num years from REFERENCE_YEAR.\n            // if year is close to REFERENCE_YEAR, estimate a year space of MIN_YEAR_SPACE.\n            return Math.max(Math.abs(parseInt(regexMatch[0], 10) - REFERENCE_YEAR), MIN_YEAR_SPACE);\n    }\n    return 0;\n};\n//# sourceMappingURL=scoring.js.map", "export default ({ baseGuesses, repeatCount }) => baseGuesses * repeatCount;\n//# sourceMappingURL=scoring.js.map", "export default ({ token, ascending }) => {\n    const firstChr = token.charAt(0);\n    let baseGuesses = 0;\n    const startingPoints = ['a', 'A', 'z', 'Z', '0', '1', '9'];\n    // lower guesses for obvious starting points\n    if (startingPoints.includes(firstChr)) {\n        baseGuesses = 4;\n    }\n    else if (firstChr.match(/\\d/)) {\n        baseGuesses = 10; // digits\n    }\n    else {\n        // could give a higher base for uppercase,\n        // assigning 26 to both upper and lower sequences is more conservative.\n        baseGuesses = 26;\n    }\n    // need to try a descending sequence in addition to every ascending sequence ->\n    // 2x guesses\n    if (!ascending) {\n        baseGuesses *= 2;\n    }\n    return baseGuesses * token.length;\n};\n//# sourceMappingURL=scoring.js.map", "import { SEPERATOR_CHAR_COUNT } from '../../data/const';\nexport default () => {\n    return SEPERATOR_CHAR_COUNT;\n};\n//# sourceMappingURL=scoring.js.map", "import utils from './utils';\nimport estimateGuesses from './estimate';\nimport { MIN_GUESSES_BEFORE_GROWING_SEQUENCE } from '../data/const';\nconst scoringHelper = {\n    password: '',\n    optimal: {},\n    excludeAdditive: false,\n    separatorRegex: undefined,\n    fillArray(size, valueType) {\n        const result = [];\n        for (let i = 0; i < size; i += 1) {\n            let value = [];\n            if (valueType === 'object') {\n                value = {};\n            }\n            result.push(value);\n        }\n        return result;\n    },\n    // helper: make bruteforce match objects spanning i to j, inclusive.\n    makeBruteforceMatch(i, j) {\n        return {\n            pattern: 'bruteforce',\n            token: this.password.slice(i, +j + 1 || 9e9),\n            i,\n            j,\n        };\n    },\n    // helper: considers whether a length-sequenceLength\n    // sequence ending at match m is better (fewer guesses)\n    // than previously encountered sequences, updating state if so.\n    update(match, sequenceLength) {\n        const k = match.j;\n        const estimatedMatch = estimateGuesses(match, this.password);\n        let pi = estimatedMatch.guesses;\n        if (sequenceLength > 1) {\n            // we're considering a length-sequenceLength sequence ending with match m:\n            // obtain the product term in the minimization function by multiplying m's guesses\n            // by the product of the length-(sequenceLength-1)\n            // sequence ending just before m, at m.i - 1.\n            pi *= this.optimal.pi[estimatedMatch.i - 1][sequenceLength - 1];\n        }\n        // calculate the minimization func\n        let g = utils.factorial(sequenceLength) * pi;\n        if (!this.excludeAdditive) {\n            g += MIN_GUESSES_BEFORE_GROWING_SEQUENCE ** (sequenceLength - 1);\n        }\n        // update state if new best.\n        // first see if any competing sequences covering this prefix,\n        // with sequenceLength or fewer matches,\n        // fare better than this sequence. if so, skip it and return.\n        let shouldSkip = false;\n        Object.keys(this.optimal.g[k]).forEach((competingPatternLength) => {\n            const competingMetricMatch = this.optimal.g[k][competingPatternLength];\n            if (parseInt(competingPatternLength, 10) <= sequenceLength) {\n                if (competingMetricMatch <= g) {\n                    shouldSkip = true;\n                }\n            }\n        });\n        if (!shouldSkip) {\n            // this sequence might be part of the final optimal sequence.\n            this.optimal.g[k][sequenceLength] = g;\n            this.optimal.m[k][sequenceLength] = estimatedMatch;\n            this.optimal.pi[k][sequenceLength] = pi;\n        }\n    },\n    // helper: evaluate bruteforce matches ending at passwordCharIndex.\n    bruteforceUpdate(passwordCharIndex) {\n        // see if a single bruteforce match spanning the passwordCharIndex-prefix is optimal.\n        let match = this.makeBruteforceMatch(0, passwordCharIndex);\n        this.update(match, 1);\n        for (let i = 1; i <= passwordCharIndex; i += 1) {\n            // generate passwordCharIndex bruteforce matches, spanning from (i=1, j=passwordCharIndex) up to (i=passwordCharIndex, j=passwordCharIndex).\n            // see if adding these new matches to any of the sequences in optimal[i-1]\n            // leads to new bests.\n            match = this.makeBruteforceMatch(i, passwordCharIndex);\n            const tmp = this.optimal.m[i - 1];\n            // eslint-disable-next-line no-loop-func\n            Object.keys(tmp).forEach((sequenceLength) => {\n                const lastMatch = tmp[sequenceLength];\n                // corner: an optimal sequence will never have two adjacent bruteforce matches.\n                // it is strictly better to have a single bruteforce match spanning the same region:\n                // same contribution to the guess product with a lower length.\n                // --> safe to skip those cases.\n                if (lastMatch.pattern !== 'bruteforce') {\n                    // try adding m to this length-sequenceLength sequence.\n                    this.update(match, parseInt(sequenceLength, 10) + 1);\n                }\n            });\n        }\n    },\n    // helper: step backwards through optimal.m starting at the end,\n    // constructing the final optimal match sequence.\n    unwind(passwordLength) {\n        const optimalMatchSequence = [];\n        let k = passwordLength - 1;\n        // find the final best sequence length and score\n        let sequenceLength = 0;\n        // eslint-disable-next-line no-loss-of-precision\n        let g = 2e308;\n        const temp = this.optimal.g[k];\n        // safety check for empty passwords\n        if (temp) {\n            Object.keys(temp).forEach((candidateSequenceLength) => {\n                const candidateMetricMatch = temp[candidateSequenceLength];\n                if (candidateMetricMatch < g) {\n                    sequenceLength = parseInt(candidateSequenceLength, 10);\n                    g = candidateMetricMatch;\n                }\n            });\n        }\n        while (k >= 0) {\n            const match = this.optimal.m[k][sequenceLength];\n            optimalMatchSequence.unshift(match);\n            k = match.i - 1;\n            sequenceLength -= 1;\n        }\n        return optimalMatchSequence;\n    },\n};\nexport default {\n    // ------------------------------------------------------------------------------\n    // search --- most guessable match sequence -------------------------------------\n    // ------------------------------------------------------------------------------\n    //\n    // takes a sequence of overlapping matches, returns the non-overlapping sequence with\n    // minimum guesses. the following is a O(l_max * (n + m)) dynamic programming algorithm\n    // for a length-n password with m candidate matches. l_max is the maximum optimal\n    // sequence length spanning each prefix of the password. In practice it rarely exceeds 5 and the\n    // search terminates rapidly.\n    //\n    // the optimal \"minimum guesses\" sequence is here defined to be the sequence that\n    // minimizes the following function:\n    //\n    //    g = sequenceLength! * Product(m.guesses for m in sequence) + D^(sequenceLength - 1)\n    //\n    // where sequenceLength is the length of the sequence.\n    //\n    // the factorial term is the number of ways to order sequenceLength patterns.\n    //\n    // the D^(sequenceLength-1) term is another length penalty, roughly capturing the idea that an\n    // attacker will try lower-length sequences first before trying length-sequenceLength sequences.\n    //\n    // for example, consider a sequence that is date-repeat-dictionary.\n    //  - an attacker would need to try other date-repeat-dictionary combinations,\n    //    hence the product term.\n    //  - an attacker would need to try repeat-date-dictionary, dictionary-repeat-date,\n    //    ..., hence the factorial term.\n    //  - an attacker would also likely try length-1 (dictionary) and length-2 (dictionary-date)\n    //    sequences before length-3. assuming at minimum D guesses per pattern type,\n    //    D^(sequenceLength-1) approximates Sum(D^i for i in [1..sequenceLength-1]\n    //\n    // ------------------------------------------------------------------------------\n    mostGuessableMatchSequence(password, matches, excludeAdditive = false) {\n        scoringHelper.password = password;\n        scoringHelper.excludeAdditive = excludeAdditive;\n        const passwordLength = password.length;\n        // partition matches into sublists according to ending index j\n        let matchesByCoordinateJ = scoringHelper.fillArray(passwordLength, 'array');\n        matches.forEach((match) => {\n            matchesByCoordinateJ[match.j].push(match);\n        });\n        // small detail: for deterministic output, sort each sublist by i.\n        matchesByCoordinateJ = matchesByCoordinateJ.map((match) => match.sort((m1, m2) => m1.i - m2.i));\n        scoringHelper.optimal = {\n            // optimal.m[k][sequenceLength] holds final match in the best length-sequenceLength\n            // match sequence covering the\n            // password prefix up to k, inclusive.\n            // if there is no length-sequenceLength sequence that scores better (fewer guesses) than\n            // a shorter match sequence spanning the same prefix,\n            // optimal.m[k][sequenceLength] is undefined.\n            m: scoringHelper.fillArray(passwordLength, 'object'),\n            // same structure as optimal.m -- holds the product term Prod(m.guesses for m in sequence).\n            // optimal.pi allows for fast (non-looping) updates to the minimization function.\n            pi: scoringHelper.fillArray(passwordLength, 'object'),\n            // same structure as optimal.m -- holds the overall metric.\n            g: scoringHelper.fillArray(passwordLength, 'object'),\n        };\n        for (let k = 0; k < passwordLength; k += 1) {\n            matchesByCoordinateJ[k].forEach((match) => {\n                if (match.i > 0) {\n                    Object.keys(scoringHelper.optimal.m[match.i - 1]).forEach((sequenceLength) => {\n                        scoringHelper.update(match, parseInt(sequenceLength, 10) + 1);\n                    });\n                }\n                else {\n                    scoringHelper.update(match, 1);\n                }\n            });\n            scoringHelper.bruteforceUpdate(k);\n        }\n        const optimalMatchSequence = scoringHelper.unwind(passwordLength);\n        const optimalSequenceLength = optimalMatchSequence.length;\n        const guesses = this.getGuesses(password, optimalSequenceLength);\n        return {\n            password,\n            guesses,\n            guessesLog10: utils.log10(guesses),\n            sequence: optimalMatchSequence,\n        };\n    },\n    getGuesses(password, optimalSequenceLength) {\n        const passwordLength = password.length;\n        let guesses = 0;\n        if (password.length === 0) {\n            guesses = 1;\n        }\n        else {\n            guesses =\n                scoringHelper.optimal.g[passwordLength - 1][optimalSequenceLength];\n        }\n        return guesses;\n    },\n};\n//# sourceMappingURL=index.js.map", "import scoring from '../../scoring';\n/*\n *-------------------------------------------------------------------------------\n * repeats (aaa, abcabcabc) ------------------------------\n *-------------------------------------------------------------------------------\n */\nclass MatchRepeat {\n    // eslint-disable-next-line max-statements\n    match({ password, omniMatch }) {\n        const matches = [];\n        let lastIndex = 0;\n        while (lastIndex < password.length) {\n            const greedyMatch = this.getGreedyMatch(password, lastIndex);\n            const lazyMatch = this.getLazyMatch(password, lastIndex);\n            if (greedyMatch == null) {\n                break;\n            }\n            const { match, baseToken } = this.setMatchToken(greedyMatch, lazyMatch);\n            if (match) {\n                const j = match.index + match[0].length - 1;\n                const baseGuesses = this.getBaseGuesses(baseToken, omniMatch);\n                matches.push(this.normalizeMatch(baseToken, j, match, baseGuesses));\n                lastIndex = j + 1;\n            }\n        }\n        const hasPromises = matches.some((match) => {\n            return match instanceof Promise;\n        });\n        if (hasPromises) {\n            return Promise.all(matches);\n        }\n        return matches;\n    }\n    // eslint-disable-next-line max-params\n    normalizeMatch(baseToken, j, match, baseGuesses) {\n        const baseMatch = {\n            pattern: 'repeat',\n            i: match.index,\n            j,\n            token: match[0],\n            baseToken,\n            baseGuesses: 0,\n            repeatCount: match[0].length / baseToken.length,\n        };\n        if (baseGuesses instanceof Promise) {\n            return baseGuesses.then((resolvedBaseGuesses) => {\n                return {\n                    ...baseMatch,\n                    baseGuesses: resolvedBaseGuesses,\n                };\n            });\n        }\n        return {\n            ...baseMatch,\n            baseGuesses,\n        };\n    }\n    getGreedyMatch(password, lastIndex) {\n        const greedy = /(.+)\\1+/g;\n        greedy.lastIndex = lastIndex;\n        return greedy.exec(password);\n    }\n    getLazyMatch(password, lastIndex) {\n        const lazy = /(.+?)\\1+/g;\n        lazy.lastIndex = lastIndex;\n        return lazy.exec(password);\n    }\n    setMatchToken(greedyMatch, lazyMatch) {\n        const lazyAnchored = /^(.+?)\\1+$/;\n        let match;\n        let baseToken = '';\n        if (lazyMatch && greedyMatch[0].length > lazyMatch[0].length) {\n            // greedy beats lazy for 'aabaab'\n            // greedy: [aabaab, aab]\n            // lazy:   [aa,     a]\n            match = greedyMatch;\n            // greedy's repeated string might itself be repeated, eg.\n            // aabaab in aabaabaabaab.\n            // run an anchored lazy match on greedy's repeated string\n            // to find the shortest repeated string\n            const temp = lazyAnchored.exec(match[0]);\n            if (temp) {\n                baseToken = temp[1];\n            }\n        }\n        else {\n            // lazy beats greedy for 'aaaaa'\n            // greedy: [aaaa,  aa]\n            // lazy:   [aaaaa, a]\n            match = lazyMatch;\n            if (match) {\n                baseToken = match[1];\n            }\n        }\n        return {\n            match,\n            baseToken,\n        };\n    }\n    getBaseGuesses(baseToken, omniMatch) {\n        const matches = omniMatch.match(baseToken);\n        if (matches instanceof Promise) {\n            return matches.then((resolvedMatches) => {\n                const baseAnalysis = scoring.mostGuessableMatchSequence(baseToken, resolvedMatches);\n                return baseAnalysis.guesses;\n            });\n        }\n        const baseAnalysis = scoring.mostGuessableMatchSequence(baseToken, matches);\n        return baseAnalysis.guesses;\n    }\n}\nexport default MatchRepeat;\n//# sourceMappingURL=matching.js.map", "import { ALL_UPPER, ALL_LOWER, ALL_DIGIT } from '../../data/const';\n/*\n *-------------------------------------------------------------------------------\n * sequences (abcdef) ------------------------------\n *-------------------------------------------------------------------------------\n */\nclass MatchSequence {\n    constructor() {\n        this.MAX_DELTA = 5;\n    }\n    // eslint-disable-next-line max-statements\n    match({ password }) {\n        /*\n         * Identifies sequences by looking for repeated differences in unicode codepoint.\n         * this allows skipping, such as 9753, and also matches some extended unicode sequences\n         * such as Greek and Cyrillic alphabets.\n         *\n         * for example, consider the input 'abcdb975zy'\n         *\n         * password: a   b   c   d   b    9   7   5   z   y\n         * index:    0   1   2   3   4    5   6   7   8   9\n         * delta:      1   1   1  -2  -41  -2  -2  69   1\n         *\n         * expected result:\n         * [(i, j, delta), ...] = [(0, 3, 1), (5, 7, -2), (8, 9, 1)]\n         */\n        const result = [];\n        if (password.length === 1) {\n            return [];\n        }\n        let i = 0;\n        let lastDelta = null;\n        const passwordLength = password.length;\n        for (let k = 1; k < passwordLength; k += 1) {\n            const delta = password.charCodeAt(k) - password.charCodeAt(k - 1);\n            if (lastDelta == null) {\n                lastDelta = delta;\n            }\n            if (delta !== lastDelta) {\n                const j = k - 1;\n                this.update({\n                    i,\n                    j,\n                    delta: lastDelta,\n                    password,\n                    result,\n                });\n                i = j;\n                lastDelta = delta;\n            }\n        }\n        this.update({\n            i,\n            j: passwordLength - 1,\n            delta: lastDelta,\n            password,\n            result,\n        });\n        return result;\n    }\n    update({ i, j, delta, password, result }) {\n        if (j - i > 1 || Math.abs(delta) === 1) {\n            const absoluteDelta = Math.abs(delta);\n            if (absoluteDelta > 0 && absoluteDelta <= this.MAX_DELTA) {\n                const token = password.slice(i, +j + 1 || 9e9);\n                const { sequenceName, sequenceSpace } = this.getSequence(token);\n                return result.push({\n                    pattern: 'sequence',\n                    i,\n                    j,\n                    token: password.slice(i, +j + 1 || 9e9),\n                    sequenceName,\n                    sequenceSpace,\n                    ascending: delta > 0,\n                });\n            }\n        }\n        return null;\n    }\n    getSequence(token) {\n        // TODO conservatively stick with roman alphabet size.\n        //  (this could be improved)\n        let sequenceName = 'unicode';\n        let sequenceSpace = 26;\n        if (ALL_LOWER.test(token)) {\n            sequenceName = 'lower';\n            sequenceSpace = 26;\n        }\n        else if (ALL_UPPER.test(token)) {\n            sequenceName = 'upper';\n            sequenceSpace = 26;\n        }\n        else if (ALL_DIGIT.test(token)) {\n            sequenceName = 'digits';\n            sequenceSpace = 10;\n        }\n        return {\n            sequenceName,\n            sequenceSpace,\n        };\n    }\n}\nexport default MatchSequence;\n//# sourceMappingURL=matching.js.map", "import { sorted, extend } from '../../helper';\nimport { zxcvbnOptions } from '../../Options';\n/*\n * ------------------------------------------------------------------------------\n * spatial match (qwerty/dvorak/keypad and so on) -----------------------------------------\n * ------------------------------------------------------------------------------\n */\nclass MatchSpatial {\n    constructor() {\n        this.SHIFTED_RX = /[~!@#$%^&*()_+QWERTYUIOP{}|ASDFGHJKL:\"ZXCVBNM<>?]/;\n    }\n    match({ password }) {\n        const matches = [];\n        Object.keys(zxcvbnOptions.graphs).forEach((graphName) => {\n            const graph = zxcvbnOptions.graphs[graphName];\n            extend(matches, this.helper(password, graph, graphName));\n        });\n        return sorted(matches);\n    }\n    checkIfShifted(graphName, password, index) {\n        if (!graphName.includes('keypad') &&\n            // initial character is shifted\n            this.SHIFTED_RX.test(password.charAt(index))) {\n            return 1;\n        }\n        return 0;\n    }\n    // eslint-disable-next-line complexity, max-statements\n    helper(password, graph, graphName) {\n        let shiftedCount;\n        const matches = [];\n        let i = 0;\n        const passwordLength = password.length;\n        while (i < passwordLength - 1) {\n            let j = i + 1;\n            let lastDirection = null;\n            let turns = 0;\n            shiftedCount = this.checkIfShifted(graphName, password, i);\n            // eslint-disable-next-line no-constant-condition\n            while (true) {\n                const prevChar = password.charAt(j - 1);\n                const adjacents = graph[prevChar] || [];\n                let found = false;\n                let foundDirection = -1;\n                let curDirection = -1;\n                // consider growing pattern by one character if j hasn't gone over the edge.\n                if (j < passwordLength) {\n                    const curChar = password.charAt(j);\n                    const adjacentsLength = adjacents.length;\n                    for (let k = 0; k < adjacentsLength; k += 1) {\n                        const adjacent = adjacents[k];\n                        curDirection += 1;\n                        // eslint-disable-next-line max-depth\n                        if (adjacent) {\n                            const adjacentIndex = adjacent.indexOf(curChar);\n                            // eslint-disable-next-line max-depth\n                            if (adjacentIndex !== -1) {\n                                found = true;\n                                foundDirection = curDirection;\n                                // eslint-disable-next-line max-depth\n                                if (adjacentIndex === 1) {\n                                    // # index 1 in the adjacency means the key is shifted,\n                                    // # 0 means unshifted: A vs a, % vs 5, etc.\n                                    // # for example, 'q' is adjacent to the entry '2@'.\n                                    // # @ is shifted w/ index 1, 2 is unshifted.\n                                    shiftedCount += 1;\n                                }\n                                // eslint-disable-next-line max-depth\n                                if (lastDirection !== foundDirection) {\n                                    // # adding a turn is correct even in the initial\n                                    // case when last_direction is null:\n                                    // # every spatial pattern starts with a turn.\n                                    turns += 1;\n                                    lastDirection = foundDirection;\n                                }\n                                break;\n                            }\n                        }\n                    }\n                }\n                // if the current pattern continued, extend j and try to grow again\n                if (found) {\n                    j += 1;\n                    // otherwise push the pattern discovered so far, if any...\n                }\n                else {\n                    // don't consider length 1 or 2 chains.\n                    if (j - i > 2) {\n                        matches.push({\n                            pattern: 'spatial',\n                            i,\n                            j: j - 1,\n                            token: password.slice(i, j),\n                            graph: graphName,\n                            turns,\n                            shiftedCount,\n                        });\n                    }\n                    // ...and then start a new search for the rest of the password.\n                    i = j;\n                    break;\n                }\n            }\n        }\n        return matches;\n    }\n}\nexport default MatchSpatial;\n//# sourceMappingURL=matching.js.map", "import { SEPERATOR_CHARS } from '../../data/const';\nconst separatorRegex = new RegExp(`[${SEPERATOR_CHARS.join('')}]`);\n/*\n *-------------------------------------------------------------------------------\n * separators (any semi-repeated special character) -----------------------------\n *-------------------------------------------------------------------------------\n */\nclass MatchSeparator {\n    static getMostUsedSeparatorChar(password) {\n        const mostUsedSeperators = [\n            ...password\n                .split('')\n                .filter((c) => separatorRegex.test(c))\n                .reduce((memo, c) => {\n                const m = memo.get(c);\n                if (m) {\n                    memo.set(c, m + 1);\n                }\n                else {\n                    memo.set(c, 1);\n                }\n                return memo;\n            }, new Map())\n                .entries(),\n        ].sort(([_a, a], [_b, b]) => b - a);\n        if (!mostUsedSeperators.length)\n            return undefined;\n        const match = mostUsedSeperators[0];\n        // If the special character is only used once, don't treat it like a separator\n        if (match[1] < 2)\n            return undefined;\n        return match[0];\n    }\n    static getSeparatorRegex(separator) {\n        return new RegExp(`([^${separator}\\n])(${separator})(?!${separator})`, 'g');\n        // negative lookbehind can be added again in a few years when it is more supported by the browsers (currently 2023)\n        // https://github.com/zxcvbn-ts/zxcvbn/issues/202\n        // return new RegExp(`(?<!${separator})(${separator})(?!${separator})`, 'g')\n    }\n    // eslint-disable-next-line max-statements\n    match({ password }) {\n        const result = [];\n        if (password.length === 0)\n            return result;\n        const mostUsedSpecial = MatchSeparator.getMostUsedSeparatorChar(password);\n        if (mostUsedSpecial === undefined)\n            return result;\n        const isSeparator = MatchSeparator.getSeparatorRegex(mostUsedSpecial);\n        // eslint-disable-next-line no-restricted-syntax\n        for (const match of password.matchAll(isSeparator)) {\n            // eslint-disable-next-line no-continue\n            if (match.index === undefined)\n                continue;\n            // add one to the index because we changed the regex from negative lookbehind to something simple.\n            // this simple approach uses the first character before the separater too but we only need the index of the separater\n            // https://github.com/zxcvbn-ts/zxcvbn/issues/202\n            const i = match.index + 1;\n            result.push({\n                pattern: 'separator',\n                token: mostUsedSpecial,\n                i,\n                j: i,\n            });\n        }\n        return result;\n    }\n}\nexport default MatchSeparator;\n//# sourceMappingURL=matching.js.map", "import { extend, sorted } from './helper';\nimport dateMatcher from './matcher/date/matching';\nimport dictionaryMatcher from './matcher/dictionary/matching';\nimport regexMatcher from './matcher/regex/matching';\nimport repeatMatcher from './matcher/repeat/matching';\nimport sequenceMatcher from './matcher/sequence/matching';\nimport spatialMatcher from './matcher/spatial/matching';\nimport separatorMatcher from './matcher/separator/matching';\nimport { zxcvbnOptions } from './Options';\nclass Matching {\n    constructor() {\n        this.matchers = {\n            date: dateMatcher,\n            dictionary: dictionaryMatcher,\n            regex: regexMatcher,\n            // @ts-ignore => TODO resolve this type issue. This is because it is possible to be async\n            repeat: repeatMatcher,\n            sequence: sequenceMatcher,\n            spatial: spatialMatcher,\n            separator: separatorMatcher,\n        };\n    }\n    match(password) {\n        const matches = [];\n        const promises = [];\n        const matchers = [\n            ...Object.keys(this.matchers),\n            ...Object.keys(zxcvbnOptions.matchers),\n        ];\n        matchers.forEach((key) => {\n            if (!this.matchers[key] && !zxcvbnOptions.matchers[key]) {\n                return;\n            }\n            const Matcher = this.matchers[key]\n                ? this.matchers[key]\n                : zxcvbnOptions.matchers[key].Matching;\n            const usedMatcher = new Matcher();\n            const result = usedMatcher.match({\n                password,\n                omniMatch: this,\n            });\n            if (result instanceof Promise) {\n                result.then((response) => {\n                    extend(matches, response);\n                });\n                promises.push(result);\n            }\n            else {\n                extend(matches, result);\n            }\n        });\n        if (promises.length > 0) {\n            return new Promise((resolve, reject) => {\n                Promise.all(promises)\n                    .then(() => {\n                    resolve(sorted(matches));\n                })\n                    .catch((error) => {\n                    reject(error);\n                });\n            });\n        }\n        return sorted(matches);\n    }\n}\nexport default Matching;\n//# sourceMappingURL=Matching.js.map", "import { zxcvbnOptions } from './Options';\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst MONTH = DAY * 31;\nconst YEAR = MONTH * 12;\nconst CENTURY = YEAR * 100;\nconst times = {\n    second: SECOND,\n    minute: MINUTE,\n    hour: HOUR,\n    day: DAY,\n    month: MONTH,\n    year: YEAR,\n    century: CENTURY,\n};\n/*\n * -------------------------------------------------------------------------------\n *  Estimates time for an attacker ---------------------------------------------------------------\n * -------------------------------------------------------------------------------\n */\nclass TimeEstimates {\n    translate(displayStr, value) {\n        let key = displayStr;\n        if (value !== undefined && value !== 1) {\n            key += 's';\n        }\n        const { timeEstimation } = zxcvbnOptions.translations;\n        return timeEstimation[key].replace('{base}', `${value}`);\n    }\n    estimateAttackTimes(guesses) {\n        const crackTimesSeconds = {\n            onlineThrottling100PerHour: guesses / (100 / 3600),\n            onlineNoThrottling10PerSecond: guesses / 10,\n            offlineSlowHashing1e4PerSecond: guesses / 1e4,\n            offlineFastHashing1e10PerSecond: guesses / 1e10,\n        };\n        const crackTimesDisplay = {\n            onlineThrottling100PerHour: '',\n            onlineNoThrottling10PerSecond: '',\n            offlineSlowHashing1e4PerSecond: '',\n            offlineFastHashing1e10PerSecond: '',\n        };\n        Object.keys(crackTimesSeconds).forEach((scenario) => {\n            const seconds = crackTimesSeconds[scenario];\n            crackTimesDisplay[scenario] =\n                this.displayTime(seconds);\n        });\n        return {\n            crackTimesSeconds,\n            crackTimesDisplay,\n            score: this.guessesToScore(guesses),\n        };\n    }\n    guessesToScore(guesses) {\n        const DELTA = 5;\n        if (guesses < 1e3 + DELTA) {\n            // risky password: \"too guessable\"\n            return 0;\n        }\n        if (guesses < 1e6 + DELTA) {\n            // modest protection from throttled online attacks: \"very guessable\"\n            return 1;\n        }\n        if (guesses < 1e8 + DELTA) {\n            // modest protection from unthrottled online attacks: \"somewhat guessable\"\n            return 2;\n        }\n        if (guesses < 1e10 + DELTA) {\n            // modest protection from offline attacks: \"safely unguessable\"\n            // assuming a salted, slow hash function like bcrypt, scrypt, PBKDF2, argon, etc\n            return 3;\n        }\n        // strong protection from offline attacks under same scenario: \"very unguessable\"\n        return 4;\n    }\n    displayTime(seconds) {\n        let displayStr = 'centuries';\n        let base;\n        const timeKeys = Object.keys(times);\n        const foundIndex = timeKeys.findIndex((time) => seconds < times[time]);\n        if (foundIndex > -1) {\n            displayStr = timeKeys[foundIndex - 1];\n            if (foundIndex !== 0) {\n                base = Math.round(seconds / times[displayStr]);\n            }\n            else {\n                displayStr = 'ltSecond';\n            }\n        }\n        return this.translate(displayStr, base);\n    }\n}\nexport default TimeEstimates;\n//# sourceMappingURL=TimeEstimates.js.map", "export default () => {\n    return null;\n};\n//# sourceMappingURL=feedback.js.map", "import { zxcvbnOptions } from '../../Options';\nexport default () => {\n    return {\n        warning: zxcvbnOptions.translations.warnings.dates,\n        suggestions: [zxcvbnOptions.translations.suggestions.dates],\n    };\n};\n//# sourceMappingURL=feedback.js.map", "import { zxcvbnOptions } from '../../Options';\nimport { ALL_UPPER_INVERTED, START_UPPER } from '../../data/const';\nconst getDictionaryWarningPassword = (match, isSoleMatch) => {\n    let warning = null;\n    if (isSoleMatch && !match.l33t && !match.reversed) {\n        if (match.rank <= 10) {\n            warning = zxcvbnOptions.translations.warnings.topTen;\n        }\n        else if (match.rank <= 100) {\n            warning = zxcvbnOptions.translations.warnings.topHundred;\n        }\n        else {\n            warning = zxcvbnOptions.translations.warnings.common;\n        }\n    }\n    else if (match.guessesLog10 <= 4) {\n        warning = zxcvbnOptions.translations.warnings.similarToCommon;\n    }\n    return warning;\n};\nconst getDictionaryWarningWikipedia = (match, isSoleMatch) => {\n    let warning = null;\n    if (isSoleMatch) {\n        warning = zxcvbnOptions.translations.warnings.wordByItself;\n    }\n    return warning;\n};\nconst getDictionaryWarningNames = (match, isSoleMatch) => {\n    if (isSoleMatch) {\n        return zxcvbnOptions.translations.warnings.namesByThemselves;\n    }\n    return zxcvbnOptions.translations.warnings.commonNames;\n};\nconst getDictionaryWarning = (match, isSoleMatch) => {\n    let warning = null;\n    const dictName = match.dictionaryName;\n    const isAName = dictName === 'lastnames' || dictName.toLowerCase().includes('firstnames');\n    if (dictName === 'passwords') {\n        warning = getDictionaryWarningPassword(match, isSoleMatch);\n    }\n    else if (dictName.includes('wikipedia')) {\n        warning = getDictionaryWarningWikipedia(match, isSoleMatch);\n    }\n    else if (isAName) {\n        warning = getDictionaryWarningNames(match, isSoleMatch);\n    }\n    else if (dictName === 'userInputs') {\n        warning = zxcvbnOptions.translations.warnings.userInputs;\n    }\n    return warning;\n};\nexport default (match, isSoleMatch) => {\n    const warning = getDictionaryWarning(match, isSoleMatch);\n    const suggestions = [];\n    const word = match.token;\n    if (word.match(START_UPPER)) {\n        suggestions.push(zxcvbnOptions.translations.suggestions.capitalization);\n    }\n    else if (word.match(ALL_UPPER_INVERTED) && word.toLowerCase() !== word) {\n        suggestions.push(zxcvbnOptions.translations.suggestions.allUppercase);\n    }\n    if (match.reversed && match.token.length >= 4) {\n        suggestions.push(zxcvbnOptions.translations.suggestions.reverseWords);\n    }\n    if (match.l33t) {\n        suggestions.push(zxcvbnOptions.translations.suggestions.l33t);\n    }\n    return {\n        warning,\n        suggestions,\n    };\n};\n//# sourceMappingURL=feedback.js.map", "import { zxcvbnOptions } from '../../Options';\nexport default (match) => {\n    if (match.regexName === 'recentYear') {\n        return {\n            warning: zxcvbnOptions.translations.warnings.recentYears,\n            suggestions: [\n                zxcvbnOptions.translations.suggestions.recentYears,\n                zxcvbnOptions.translations.suggestions.associatedYears,\n            ],\n        };\n    }\n    return {\n        warning: null,\n        suggestions: [],\n    };\n};\n//# sourceMappingURL=feedback.js.map", "import { zxcvbnOptions } from '../../Options';\nexport default (match) => {\n    let warning = zxcvbnOptions.translations.warnings.extendedRepeat;\n    if (match.baseToken.length === 1) {\n        warning = zxcvbnOptions.translations.warnings.simpleRepeat;\n    }\n    return {\n        warning,\n        suggestions: [zxcvbnOptions.translations.suggestions.repeated],\n    };\n};\n//# sourceMappingURL=feedback.js.map", "import { zxcvbnOptions } from '../../Options';\nexport default () => {\n    return {\n        warning: zxcvbnOptions.translations.warnings.sequences,\n        suggestions: [zxcvbnOptions.translations.suggestions.sequences],\n    };\n};\n//# sourceMappingURL=feedback.js.map", "import { zxcvbnOptions } from '../../Options';\nexport default (match) => {\n    let warning = zxcvbnOptions.translations.warnings.keyPattern;\n    if (match.turns === 1) {\n        warning = zxcvbnOptions.translations.warnings.straightRow;\n    }\n    return {\n        warning,\n        suggestions: [zxcvbnOptions.translations.suggestions.longerKeyboardPattern],\n    };\n};\n//# sourceMappingURL=feedback.js.map", "export default () => {\n    // no suggestions\n    return null;\n};\n//# sourceMappingURL=feedback.js.map", "import { zxcvbnOptions } from './Options';\nimport bruteforceMatcher from './matcher/bruteforce/feedback';\nimport dateMatcher from './matcher/date/feedback';\nimport dictionaryMatcher from './matcher/dictionary/feedback';\nimport regexMatcher from './matcher/regex/feedback';\nimport repeatMatcher from './matcher/repeat/feedback';\nimport sequenceMatcher from './matcher/sequence/feedback';\nimport spatialMatcher from './matcher/spatial/feedback';\nimport separatorMatcher from './matcher/separator/feedback';\nconst defaultFeedback = {\n    warning: null,\n    suggestions: [],\n};\n/*\n * -------------------------------------------------------------------------------\n *  Generate feedback ---------------------------------------------------------------\n * -------------------------------------------------------------------------------\n */\nclass Feedback {\n    constructor() {\n        this.matchers = {\n            bruteforce: bruteforceMatcher,\n            date: dateMatcher,\n            dictionary: dictionaryMatcher,\n            regex: regexMatcher,\n            repeat: repeatMatcher,\n            sequence: sequenceMatcher,\n            spatial: spatialMatcher,\n            separator: separatorMatcher,\n        };\n        this.defaultFeedback = {\n            warning: null,\n            suggestions: [],\n        };\n        this.setDefaultSuggestions();\n    }\n    setDefaultSuggestions() {\n        this.defaultFeedback.suggestions.push(zxcvbnOptions.translations.suggestions.useWords, zxcvbnOptions.translations.suggestions.noNeed);\n    }\n    getFeedback(score, sequence) {\n        if (sequence.length === 0) {\n            return this.defaultFeedback;\n        }\n        if (score > 2) {\n            return defaultFeedback;\n        }\n        const extraFeedback = zxcvbnOptions.translations.suggestions.anotherWord;\n        const longestMatch = this.getLongestMatch(sequence);\n        let feedback = this.getMatchFeedback(longestMatch, sequence.length === 1);\n        if (feedback !== null && feedback !== undefined) {\n            feedback.suggestions.unshift(extraFeedback);\n        }\n        else {\n            feedback = {\n                warning: null,\n                suggestions: [extraFeedback],\n            };\n        }\n        return feedback;\n    }\n    getLongestMatch(sequence) {\n        let longestMatch = sequence[0];\n        const slicedSequence = sequence.slice(1);\n        slicedSequence.forEach((match) => {\n            if (match.token.length > longestMatch.token.length) {\n                longestMatch = match;\n            }\n        });\n        return longestMatch;\n    }\n    getMatchFeedback(match, isSoleMatch) {\n        if (this.matchers[match.pattern]) {\n            return this.matchers[match.pattern](match, isSoleMatch);\n        }\n        if (zxcvbnOptions.matchers[match.pattern] &&\n            'feedback' in zxcvbnOptions.matchers[match.pattern]) {\n            return zxcvbnOptions.matchers[match.pattern].feedback(match, isSoleMatch);\n        }\n        return defaultFeedback;\n    }\n}\nexport default Feedback;\n//# sourceMappingURL=Feedback.js.map", "import Matching from './Matching';\nimport scoring from './scoring';\nimport TimeEstimates from './TimeEstimates';\nimport Feedback from './Feedback';\nimport { zxcvbnOptions, Options } from './Options';\nimport debounce from './debounce';\nconst time = () => new Date().getTime();\nconst createReturnValue = (resolvedMatches, password, start) => {\n    const feedback = new Feedback();\n    const timeEstimates = new TimeEstimates();\n    const matchSequence = scoring.mostGuessableMatchSequence(password, resolvedMatches);\n    const calcTime = time() - start;\n    const attackTimes = timeEstimates.estimateAttackTimes(matchSequence.guesses);\n    return {\n        calcTime,\n        ...matchSequence,\n        ...attackTimes,\n        feedback: feedback.getFeedback(attackTimes.score, matchSequence.sequence),\n    };\n};\nconst main = (password, userInputs) => {\n    if (userInputs) {\n        zxcvbnOptions.extendUserInputsDictionary(userInputs);\n    }\n    const matching = new Matching();\n    return matching.match(password);\n};\nexport const zxcvbn = (password, userInputs) => {\n    const start = time();\n    const matches = main(password, userInputs);\n    if (matches instanceof Promise) {\n        throw new Error('You are using a Promised matcher, please use `zxcvbnAsync` for it.');\n    }\n    return createReturnValue(matches, password, start);\n};\nexport const zxcvbnAsync = async (password, userInputs) => {\n    const usedPassword = password.substring(0, zxcvbnOptions.maxLength);\n    const start = time();\n    const matches = await main(usedPassword, userInputs);\n    return createReturnValue(matches, usedPassword, start);\n};\nexport * from './types';\nexport { zxcvbnOptions, Options, debounce };\n//# sourceMappingURL=index.js.map", "/**\n * @link https://davidwalsh.name/javascript-debounce-function\n * @param func needs to implement a function which is debounced\n * @param wait how long do you want to wait till the previous declared function is executed\n * @param isImmediate defines if you want to execute the function on the first execution or the last execution inside the time window. `true` for first and `false` for last.\n */\nexport default (func, wait, isImmediate) => {\n    let timeout;\n    return function debounce(...args) {\n        const context = this;\n        const later = () => {\n            timeout = undefined;\n            if (!isImmediate) {\n                func.apply(context, args);\n            }\n        };\n        const shouldCallNow = isImmediate && !timeout;\n        if (timeout !== undefined) {\n            clearTimeout(timeout);\n        }\n        timeout = setTimeout(later, wait);\n        if (shouldCallNow) {\n            return func.apply(context, args);\n        }\n        return undefined;\n    };\n};\n//# sourceMappingURL=debounce.js.map"], "names": ["extend", "listToExtend", "list", "push", "apply", "sorted", "matches", "sort", "m1", "m2", "i", "j", "buildRankedDictionary", "orderedList", "result", "counter", "for<PERSON>ach", "word", "DATE_SPLITS", "START_UPPER", "END_UPPER", "ALL_UPPER", "ALL_UPPER_INVERTED", "ALL_LOWER", "ALL_LOWER_INVERTED", "ONE_LOWER", "ONE_UPPER", "ALPHA_INVERTED", "ALL_DIGIT", "REFERENCE_YEAR", "Date", "getFullYear", "REGEXEN", "recentYear", "SEPERATOR_CHARS", "SEPERATOR_CHAR_COUNT", "length", "MatchDate", "match", "password", "this", "getMatchesWithoutSeparator", "getMatchesWithSeparator", "filteredMatches", "filterNoise", "maybeDateWithSeparator", "Math", "abs", "token", "slice", "regexMatch", "exec", "dmy", "mapIntegersToDayMonthYear", "parseInt", "pattern", "separator", "year", "month", "day", "maybeDateNoSeparator", "metric", "candidate", "candidates", "index", "k", "l", "bestCandidate", "minDistance", "distance", "filter", "isSubmatch", "matchesLength", "o", "otherMatch", "integers", "over12", "over31", "under1", "len1", "int", "getDayMonth", "possibleYearSplits", "possibleYearSplitsLength", "y", "rest", "dm", "mapIntegersToDayMonth", "twoToFourDigitYear", "temp", "reverse", "data", "peq", "Uint32Array", "a", "b", "tmp", "n", "m", "lst", "pv", "mv", "sc", "charCodeAt", "eq", "xv", "myers_32", "mhc", "phc", "hsize", "ceil", "vsize", "start", "vlen", "min", "pb", "mb", "xh", "ph", "mh", "score", "myers_x", "findLevenshteinDistance", "rankedDictionary", "threshold", "foundDistance", "found", "Object", "keys", "find", "entry", "usedThreshold", "getUsedT<PERSON><PERSON>old", "isPasswordToShort", "isThresholdLongerThanPassword", "foundEntryDistance", "isInThreshold", "levenshteinDistance", "levenshteinDistanceEntry", "l33tTable", "c", "d", "e", "f", "g", "h", "q", "u", "s", "t", "v", "w", "x", "z", "translationKeys", "warnings", "straightRow", "keyPattern", "simpleRepeat", "extendedRepeat", "sequences", "recentYears", "dates", "topTen", "topHundred", "common", "similarToCommon", "wordByItself", "namesByThemselves", "commonNames", "userInputs", "pwned", "suggestions", "l33t", "reverseWords", "allUppercase", "capitalization", "associatedYears", "repeated", "longerKeyboardPattern", "anotherWord", "useWords", "no<PERSON><PERSON>", "timeEstimation", "ltSecond", "second", "seconds", "minute", "minutes", "hour", "hours", "days", "months", "years", "centuries", "TrieNode", "constructor", "parents", "children", "Map", "addSub", "key", "subs", "firstChar", "char<PERSON>t", "has", "set", "cur", "get", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "concat", "child", "isTerminal", "l33tTableToTrieNode", "triNode", "entries", "letter", "substitutions", "substitution", "Options", "matchers", "trieNodeRoot", "dictionary", "rankedDictionaries", "rankedDictionariesMaxWordSize", "translations", "graphs", "useLevenshteinDistance", "levenshteinThreshold", "l33tMaxSubstitutions", "max<PERSON><PERSON><PERSON>", "setRankedDictionaries", "setOptions", "options", "setTranslations", "undefined", "checkCustomTranslations", "Error", "valid", "type", "translationType", "rankedDictionariesMaxWorkSize", "name", "getRankedDictionariesMaxWordSize", "map", "el", "toString", "reduce", "max", "Infinity", "buildSanitizedRankedDictionary", "sanitizedInputs", "input", "inputType", "toLowerCase", "extendUserInputsDictionary", "newList", "addMatcher", "matcher", "console", "info", "zxcvbnOptions", "MatchReverse", "defaultMatch", "passwordReversed", "split", "join", "reversed", "CleanPasswords", "substr", "limit", "trieRoot", "buffer", "finalPasswords", "getAllPossibleSubsAtIndex", "nodes", "character", "helper", "only<PERSON>ull<PERSON><PERSON>", "isFullSub", "subIndex", "changes", "lastSubLetter", "consecutiveSubCount", "hasSubs", "sub", "newSubs", "pop", "getAll", "MatchL33t", "isAlreadyIncluded", "newMatch", "some", "l33tMatch", "every", "value", "subbedPasswords", "getCleanPasswords", "hasFullMatch", "isFullSubstitution", "subbedPassword", "matchedDictionary", "useLevenshtein", "extras", "getExtras", "passwordWithSubs", "iUnsubbed", "change", "usedChanges", "jUnsubbed", "filtered", "subDisplay", "findIndex", "alreadyIncluded", "matchedWord", "MatchDictionary", "L33t", "Reverse", "<PERSON><PERSON><PERSON><PERSON>", "passwordLower", "dictionaryName", "rankedDict", "longestDictionaryWordSize", "searchWidth", "searchEnd", "usedPassword", "isInDictionary", "foundLevenshteinDistance", "isFullPassword", "isLevenshteinMatch", "rank", "MatchRegex", "regexes", "regex", "lastIndex", "regexName", "utils", "nCk", "count", "<PERSON><PERSON><PERSON>", "log10", "log", "log2", "factorial", "num", "rval", "uppercaseVariant", "cleaned<PERSON><PERSON>", "replace", "commonCases", "commonCasesLength", "wordArray", "upperCaseCount", "char", "lowerCaseCount", "variations", "<PERSON><PERSON><PERSON><PERSON>", "getVariations", "countSubstring", "string", "substring", "pos", "indexOf", "l33tVariant", "subbedCount", "unsubbedCount", "getCounts", "tokenLower", "p", "possibilities", "estimatePossiblePatterns", "graph", "turns", "startingPosition", "averageDegree", "average", "neighbors", "calcAverageDegree", "guesses", "token<PERSON><PERSON>th", "possibleTurns", "bruteforce", "minGuesses", "Number", "POSITIVE_INFINITY", "MAX_VALUE", "MIN_SUBMATCH_GUESSES_SINGLE_CHAR", "MIN_SUBMATCH_GUESSES_MULTI_CHAR", "date", "baseGuesses", "uppercaseVariations", "l33tVariations", "calculation", "charClassBases", "alphaLower", "alphaUpper", "alpha", "alphanumeric", "digits", "symbols", "repeat", "repeatCount", "sequence", "ascending", "firstChr", "includes", "spatial", "shiftedCount", "unShiftedCount", "shiftedVariations", "round", "estimateGuesses", "extraData", "getMinGuesses", "estimationResult", "getScoring", "scoring", "matchGuesses", "guessesLog10", "<PERSON><PERSON><PERSON><PERSON>", "optimal", "excludeAdditive", "separatorRegex", "fillA<PERSON>y", "size", "valueType", "makeBruteforceMatch", "update", "sequenceLength", "estimatedMatch", "pi", "shouldSkip", "competingPatternLength", "competingMetricMatch", "bruteforceUpdate", "passwordCharIndex", "unwind", "optimalMatchSequence", "candidate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "candidate<PERSON>etric<PERSON><PERSON>", "unshift", "mostGuessableMatchSequence", "matchesByCoordinateJ", "optimalSequenceLength", "getGuesses", "MatchRepeat", "omniMatch", "greedyMatch", "getGreedyMatch", "lazyMatch", "getLazyMatch", "baseToken", "setMatchToken", "getBaseGuesses", "normalizeMatch", "Promise", "all", "baseMatch", "then", "resolvedBaseGuesses", "greedy", "lazy", "lazyAnchored", "resolvedMatches", "MatchSequence", "MAX_DELTA", "<PERSON><PERSON><PERSON><PERSON>", "delta", "absoluteDelta", "sequenceName", "sequenceSpace", "getSequence", "test", "MatchSpatial", "SHIFTED_RX", "graphName", "checkIfShifted", "lastDirection", "adjacents", "foundDirection", "curDirection", "cur<PERSON><PERSON>", "adjacentsLength", "adjacent", "adjacentIndex", "RegExp", "MatchSeparator", "getMostUsedSeparatorChar", "mostUsedSeperators", "memo", "_a", "_b", "getSeparatorRegex", "mostUsedSpecial", "isSeparator", "matchAll", "Matching", "date<PERSON><PERSON><PERSON>", "dictionaryMatcher", "regexMatcher", "repeatM<PERSON>er", "sequenceMatcher", "spatialMatcher", "separatorM<PERSON>er", "promises", "response", "resolve", "reject", "catch", "error", "MONTH", "DAY", "YEAR", "times", "SECOND", "MINUTE", "HOUR", "century", "TimeEstimates", "translate", "displayStr", "estimateAttackTimes", "crackTimesSeconds", "onlineThrottling100PerHour", "onlineNoThrottling10PerSecond", "offlineSlowHashing1e4PerSecond", "offlineFastHashing1e10PerSecond", "crackTimesDisplay", "scenario", "displayTime", "guessesToScore", "base", "timeKeys", "foundIndex", "time", "bruteforceMatcher", "warning", "getDictionaryWarning", "isSoleMatch", "dictName", "isAName", "getDictionaryWarningPassword", "getDictionaryWarningWikipedia", "getDictionaryWarningNames", "defaultFeedback", "<PERSON><PERSON><PERSON>", "setDefaultSuggestions", "getFeedback", "extraFeedback", "longestMatch", "getLongestMatch", "feedback", "getMatchFeedback", "getTime", "createReturnValue", "timeEstimates", "matchSequence", "calcTime", "attackTimes", "main", "func", "wait", "isImmediate", "timeout", "args", "context", "shouldCallNow", "clearTimeout", "setTimeout", "later", "zxcvbn", "async"], "mappings": "4EACO,MAAMA,EAASA,CAACC,EAAcC,IAErCD,EAAaE,KAAKC,MAAMH,EAAcC,GAazBG,EAAUC,GAAYA,EAAQC,MAAK,CAACC,EAAIC,IAAOD,EAAGE,EAAID,EAAGC,GAAKF,EAAGG,EAAIF,EAAGE,IACxEC,EAAyBC,IAClC,MAAMC,EAAS,CAAA,EACf,IAAIC,EAAU,EAKd,OAJAF,EAAYG,SAASC,IACjBH,EAAOG,GAAQF,EACfA,GAAW,CAAC,IAETD,CAAM,ECvBV,MAEMI,ECHE,CACX,EAAG,CAEC,CAAC,EAAG,GACJ,CAAC,EAAG,IAER,EAAG,CACC,CAAC,EAAG,GACJ,CAAC,EAAG,GAEJ,CAAC,EAAG,IAER,EAAG,CACC,CAAC,EAAG,GACJ,CAAC,EAAG,GACJ,CAAC,EAAG,IAGR,EAAG,CACC,CAAC,EAAG,GACJ,CAAC,EAAG,GACJ,CAAC,EAAG,GACJ,CAAC,EAAG,IAER,EAAG,CACC,CAAC,EAAG,GACJ,CAAC,EAAG,KDhBCC,EAAc,mCACdC,EAAY,mCAEZC,EAAY,oBACZC,EAAqB,qBACrBC,EAAY,oBACZC,EAAqB,qBACrBC,EAAY,iBACZC,EAAY,iBACZC,EAAiB,uBACjBC,EAAY,QACZC,GAAiB,IAAIC,MAAOC,cAC5BC,EAAU,CAAEC,WAAY,6BAExBC,EAAkB,CAC3B,IACA,IACA,IACA,IACA,IACA,IACA,KACA,IACA,IACA,KAESC,EAAuBD,EAAgBE,OE7BpD,MAAMC,EAqBFC,KAAAA,EAAMC,SAAEA,IACJ,MAAMjC,EAAU,IACTkC,KAAKC,2BAA2BF,MAChCC,KAAKE,wBAAwBH,IAE9BI,EAAkBH,KAAKI,YAAYtC,GACzC,OAAOD,EAAOsC,EAClB,CACAD,uBAAAA,CAAwBH,GACpB,MAAMjC,EAAU,GACVuC,EAAyB,8CAE/B,IAAK,IAAInC,EAAI,EAAGA,GAAKoC,KAAKC,IAAIR,EAASH,OAAS,GAAI1B,GAAK,EACrD,IAAK,IAAIC,EAAID,EAAI,EAAGC,GAAKD,EAAI,KACrBC,GAAK4B,EAASH,QADUzB,GAAK,EAAG,CAIpC,MAAMqC,EAAQT,EAASU,MAAMvC,GAAIC,EAAI,GAAK,KACpCuC,EAAaL,EAAuBM,KAAKH,GAC/C,GAAkB,MAAdE,EAAoB,CACpB,MAAME,EAAMZ,KAAKa,0BAA0B,CACvCC,SAASJ,EAAW,GAAI,IACxBI,SAASJ,EAAW,GAAI,IACxBI,SAASJ,EAAW,GAAI,MAEjB,MAAPE,GACA9C,EAAQH,KAAK,CACToD,QAAS,OACTP,QACAtC,IACAC,IACA6C,UAAWN,EAAW,GACtBO,KAAML,EAAIK,KACVC,MAAON,EAAIM,MACXC,IAAKP,EAAIO,KAGrB,CACJ,CAEJ,OAAOrD,CACX,CAEAmC,0BAAAA,CAA2BF,GACvB,MAAMjC,EAAU,GACVsD,EAAuB,YACvBC,EAAUC,GAAchB,KAAKC,IAAIe,EAAUL,KAAO5B,GAExD,IAAK,IAAInB,EAAI,EAAGA,GAAKoC,KAAKC,IAAIR,EAASH,OAAS,GAAI1B,GAAK,EACrD,IAAK,IAAIC,EAAID,EAAI,EAAGC,GAAKD,EAAI,KACrBC,GAAK4B,EAASH,QADUzB,GAAK,EAAG,CAIpC,MAAMqC,EAAQT,EAASU,MAAMvC,GAAIC,EAAI,GAAK,KAC1C,GAAIiD,EAAqBT,KAAKH,GAAQ,CAClC,MAAMe,EAAa,GACbC,EAAQhB,EAAMZ,OAYpB,GAXsBlB,EAAY8C,GACpBhD,SAAQ,EAAEiD,EAAGC,MACvB,MAAMd,EAAMZ,KAAKa,0BAA0B,CACvCC,SAASN,EAAMC,MAAM,EAAGgB,GAAI,IAC5BX,SAASN,EAAMC,MAAMgB,EAAGC,GAAI,IAC5BZ,SAASN,EAAMC,MAAMiB,GAAI,MAElB,MAAPd,GACAW,EAAW5D,KAAKiD,EACpB,IAEAW,EAAW3B,OAAS,EAAG,CAUvB,IAAI+B,EAAgBJ,EAAW,GAC3BK,EAAcP,EAAOE,EAAW,IACpCA,EAAWd,MAAM,GAAGjC,SAAS8C,IACzB,MAAMO,EAAWR,EAAOC,GACpBO,EAAWD,IACXD,EAAgBL,EAChBM,EAAcC,EAClB,IAEJ/D,EAAQH,KAAK,CACToD,QAAS,OACTP,QACAtC,IACAC,IACA6C,UAAW,GACXC,KAAMU,EAAcV,KACpBC,MAAOS,EAAcT,MACrBC,IAAKQ,EAAcR,KAE3B,CACJ,CACJ,CAEJ,OAAOrD,CACX,CAUAsC,WAAAA,CAAYtC,GACR,OAAOA,EAAQgE,QAAQhC,IACnB,IAAIiC,GAAa,EACjB,MAAMC,EAAgBlE,EAAQ8B,OAC9B,IAAK,IAAIqC,EAAI,EAAGA,EAAID,EAAeC,GAAK,EAAG,CACvC,MAAMC,EAAapE,EAAQmE,GAC3B,GAAInC,IAAUoC,GACNA,EAAWhE,GAAK4B,EAAM5B,GAAKgE,EAAW/D,GAAK2B,EAAM3B,EAAG,CACpD4D,GAAa,EACb,KACJ,CAER,CACA,OAAQA,CAAU,GAE1B,CAYAlB,yBAAAA,CAA0BsB,GACtB,GAAIA,EAAS,GAAK,IAAMA,EAAS,IAAM,EACnC,OAAO,KAEX,IAAIC,EAAS,EACTC,EAAS,EACTC,EAAS,EACb,IAAK,IAAIL,EAAI,EAAGM,EAAOJ,EAASvC,OAAQqC,EAAIM,EAAMN,GAAK,EAAG,CACtD,MAAMO,EAAML,EAASF,GACrB,GAAKO,EAAM,IAAMA,EF9KA,KE8KwBA,EF/KxB,KEgLb,OAAO,KAEPA,EAAM,KACNH,GAAU,GAEVG,EAAM,KACNJ,GAAU,GAEVI,GAAO,IACPF,GAAU,EAElB,CACA,OAAID,GAAU,GAAgB,IAAXD,GAAgBE,GAAU,EAClC,KAEJtC,KAAKyC,YAAYN,EAC5B,CAEAM,WAAAA,CAAYN,GAER,MAAMO,EAAqB,CACvB,CAACP,EAAS,GAAIA,EAAS1B,MAAM,EAAG,IAChC,CAAC0B,EAAS,GAAIA,EAAS1B,MAAM,EAAG,KAE9BkC,EAA2BD,EAAmB9C,OACpD,IAAK,IAAIzB,EAAI,EAAGA,EAAIwE,EAA0BxE,GAAK,EAAG,CAClD,MAAOyE,EAAGC,GAAQH,EAAmBvE,GACrC,GF1MiB,KE0MIyE,GAAKA,GF3MT,KE2M6B,CAC1C,MAAME,EAAK9C,KAAK+C,sBAAsBF,GACtC,OAAU,MAANC,EACO,CACH7B,KAAM2B,EACN1B,MAAO4B,EAAG5B,MACVC,IAAK2B,EAAG3B,KAQT,IACX,CACJ,CAGA,IAAK,IAAIM,EAAI,EAAGA,EAAIkB,EAA0BlB,GAAK,EAAG,CAClD,MAAOmB,EAAGC,GAAQH,EAAmBjB,GAC/BqB,EAAK9C,KAAK+C,sBAAsBF,GACtC,GAAU,MAANC,EACA,MAAO,CACH7B,KAAMjB,KAAKgD,mBAAmBJ,GAC9B1B,MAAO4B,EAAG5B,MACVC,IAAK2B,EAAG3B,IAGpB,CACA,OAAO,IACX,CACA4B,qBAAAA,CAAsBZ,GAClB,MAAMc,EAAO,CAACd,EAAUA,EAAS1B,QAAQyC,WACzC,IAAK,IAAIhF,EAAI,EAAGA,EAAI+E,EAAKrD,OAAQ1B,GAAK,EAAG,CACrC,MAAMiF,EAAOF,EAAK/E,GACZiD,EAAMgC,EAAK,GACXjC,EAAQiC,EAAK,GACnB,GAAIhC,GAAO,GAAKA,GAAO,IAAMD,GAAS,GAAKA,GAAS,GAChD,MAAO,CACHC,MACAD,QAGZ,CACA,OAAO,IACX,CACA8B,kBAAAA,CAAmB/B,GACf,OAAIA,EAAO,GACAA,EAEPA,EAAO,GAEAA,EAAO,KAGXA,EAAO,GAClB,ECrQJ,MAAMmC,EAAM,IAAIC,YAAY,OA+GtBxB,EAAW,CAACyB,EAAGC,KACjB,GAAID,EAAE1D,OAAS2D,EAAE3D,OAAQ,CACrB,MAAM4D,EAAMD,EACZA,EAAID,EACJA,EAAIE,CACP,CACD,OAAiB,IAAbD,EAAE3D,OACK0D,EAAE1D,OAET0D,EAAE1D,QAAU,GAvHH,EAAC0D,EAAGC,KACjB,MAAME,EAAIH,EAAE1D,OACN8D,EAAIH,EAAE3D,OACN+D,EAAM,GAAMF,EAAI,EACtB,IAAIG,GAAM,EACNC,EAAK,EACLC,EAAKL,EACLvF,EAAIuF,EACR,KAAOvF,KACHkF,EAAIE,EAAES,WAAW7F,KAAO,GAAKA,EAEjC,IAAKA,EAAI,EAAGA,EAAIwF,EAAGxF,IAAK,CACpB,IAAI8F,EAAKZ,EAAIG,EAAEQ,WAAW7F,IAC1B,MAAM+F,EAAKD,EAAKH,EAChBG,IAAQA,EAAKJ,GAAMA,EAAMA,EACzBC,KAAQG,EAAKJ,GACbA,GAAMI,EACFH,EAAKF,GACLG,IAEAF,EAAKD,GACLG,IAEJD,EAAMA,GAAM,EAAK,EACjBD,EAAMA,GAAM,IAAOK,EAAKJ,GACxBA,GAAMI,CACT,CAED,IADA/F,EAAIuF,EACGvF,KACHkF,EAAIE,EAAES,WAAW7F,IAAM,EAE3B,OAAO4F,CAAE,EAyFEI,CAASZ,EAAGC,GAvFX,EAACA,EAAGD,KAChB,MAAMG,EAAIH,EAAE1D,OACN8D,EAAIH,EAAE3D,OACNuE,EAAM,GACNC,EAAM,GACNC,EAAQ/D,KAAKgE,KAAKb,EAAI,IACtBc,EAAQjE,KAAKgE,KAAKZ,EAAI,IAC5B,IAAK,IAAIxF,EAAI,EAAGA,EAAImG,EAAOnG,IACvBkG,EAAIlG,IAAM,EACViG,EAAIjG,GAAK,EAEb,IAAIC,EAAI,EACR,KAAOA,EAAIoG,EAAQ,EAAGpG,IAAK,CACvB,IAAI0F,EAAK,EACLD,GAAM,EACV,MAAMY,EAAY,GAAJrG,EACRsG,EAAOnE,KAAKoE,IAAI,GAAIhB,GAAKc,EAC/B,IAAK,IAAI/C,EAAI+C,EAAO/C,EAAIgD,EAAMhD,IAC1B2B,EAAIG,EAAEQ,WAAWtC,KAAO,GAAKA,EAEjC,IAAK,IAAIvD,EAAI,EAAGA,EAAIuF,EAAGvF,IAAK,CACxB,MAAM8F,EAAKZ,EAAIE,EAAES,WAAW7F,IACtByG,EAAMP,EAAKlG,EAAI,GAAM,KAAOA,EAAK,EACjC0G,EAAMT,EAAKjG,EAAI,GAAM,KAAOA,EAAK,EACjC+F,EAAKD,EAAKH,EACVgB,IAASb,EAAKY,GAAMhB,GAAMA,EAAMA,EAAMI,EAAKY,EACjD,IAAIE,EAAKjB,IAAOgB,EAAKjB,GACjBmB,EAAKnB,EAAKiB,EACTC,IAAO,GAAMH,IACdP,EAAKlG,EAAI,GAAM,IAAM,GAAKA,GAEzB6G,IAAO,GAAMH,IACdT,EAAKjG,EAAI,GAAM,IAAM,GAAKA,GAE9B4G,EAAMA,GAAM,EAAKH,EACjBI,EAAMA,GAAM,EAAKH,EACjBhB,EAAKmB,IAAOd,EAAKa,GACjBjB,EAAKiB,EAAKb,CACb,CACD,IAAK,IAAIxC,EAAI+C,EAAO/C,EAAIgD,EAAMhD,IAC1B2B,EAAIG,EAAEQ,WAAWtC,IAAM,CAE9B,CACD,IAAIoC,EAAK,EACLD,GAAM,EACV,MAAMY,EAAY,GAAJrG,EACRsG,EAAOnE,KAAKoE,IAAI,GAAIhB,EAAIc,GAASA,EACvC,IAAK,IAAI/C,EAAI+C,EAAO/C,EAAIgD,EAAMhD,IAC1B2B,EAAIG,EAAEQ,WAAWtC,KAAO,GAAKA,EAEjC,IAAIuD,EAAQtB,EACZ,IAAK,IAAIxF,EAAI,EAAGA,EAAIuF,EAAGvF,IAAK,CACxB,MAAM8F,EAAKZ,EAAIE,EAAES,WAAW7F,IACtByG,EAAMP,EAAKlG,EAAI,GAAM,KAAOA,EAAK,EACjC0G,EAAMT,EAAKjG,EAAI,GAAM,KAAOA,EAAK,EACjC+F,EAAKD,EAAKH,EACVgB,IAASb,EAAKY,GAAMhB,GAAMA,EAAMA,EAAMI,EAAKY,EACjD,IAAIE,EAAKjB,IAAOgB,EAAKjB,GACjBmB,EAAKnB,EAAKiB,EACdG,GAAUF,IAAQpB,EAAI,EAAM,EAC5BsB,GAAUD,IAAQrB,EAAI,EAAM,EACvBoB,IAAO,GAAMH,IACdP,EAAKlG,EAAI,GAAM,IAAM,GAAKA,GAEzB6G,IAAO,GAAMH,IACdT,EAAKjG,EAAI,GAAM,IAAM,GAAKA,GAE9B4G,EAAMA,GAAM,EAAKH,EACjBI,EAAMA,GAAM,EAAKH,EACjBhB,EAAKmB,IAAOd,EAAKa,GACjBjB,EAAKiB,EAAKb,CACb,CACD,IAAK,IAAIxC,EAAI+C,EAAO/C,EAAIgD,EAAMhD,IAC1B2B,EAAIG,EAAEQ,WAAWtC,IAAM,EAE3B,OAAOuD,CAAK,EAcLC,CAAQ3B,EAAGC,EAAE,ECnHlB2B,EAA0BA,CAACnF,EAAUoF,EAAkBC,KACzD,IAAIC,EAAgB,EACpB,MAAMC,EAAQC,OAAOC,KAAKL,GAAkBM,MAAMC,IAC9C,MAAMC,EAVWC,EAAC7F,EAAU2F,EAAON,KACvC,MAAMS,EAAoB9F,EAASH,QAAU8F,EAAM9F,OAC7CkG,EAAgC/F,EAASH,QAAUwF,EAGzD,OAFgCS,GAAqBC,EAEpBxF,KAAKgE,KAAKvE,EAASH,OAAS,GAAKwF,CAAS,EAKjDQ,CAAiB7F,EAAU2F,EAAON,GACxD,GAAI9E,KAAKC,IAAIR,EAASH,OAAS8F,EAAM9F,QAAU+F,EAC3C,OAAO,EAEX,MAAMI,EAAqBlE,EAAS9B,EAAU2F,GACxCM,EAAgBD,GAAsBJ,EAI5C,OAHIK,IACAX,EAAgBU,GAEbC,CAAa,IAExB,OAAIV,EACO,CACHW,oBAAqBZ,EACrBa,yBAA0BZ,GAG3B,EAAE,EC5BE,IAAAa,EAAA,CACX7C,EAAG,CAAC,IAAK,KACTC,EAAG,CAAC,KACJ6C,EAAG,CAAC,IAAK,IAAK,IAAK,KACnBC,EAAG,CAAC,IAAK,MACTC,EAAG,CAAC,KACJC,EAAG,CAAC,KACJC,EAAG,CAAC,IAAK,IAAK,KACdC,EAAG,CAAC,IAAK,OACTvI,EAAG,CAAC,IAAK,IAAK,KACduD,EAAG,CAAC,IAAK,MACTC,EAAG,CAAC,IAAK,IAAK,IAAK,KACnBgC,EAAG,CAAC,KAAM,KAAM,KAAM,cACtBD,EAAG,CAAC,MACJxB,EAAG,CAAC,IAAK,MACTyE,EAAG,CAAC,KACJC,EAAG,CAAC,OACJC,EAAG,CAAC,IAAK,KACTC,EAAG,CAAC,IAAK,KACTC,EAAG,CAAC,IAAK,IAAK,KACdC,EAAG,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,cAClCC,EAAG,CAAC,IAAK,MACTC,EAAG,CAAC,MCtBOC,EAAA,CACXC,SAAU,CACNC,YAAa,cACbC,WAAY,aACZC,aAAc,eACdC,eAAgB,iBAChBC,UAAW,YACXC,YAAa,cACbC,MAAO,QACPC,OAAQ,SACRC,WAAY,aACZC,OAAQ,SACRC,gBAAiB,kBACjBC,aAAc,eACdC,kBAAmB,oBACnBC,YAAa,cACbC,WAAY,aACZC,MAAO,SAEXC,YAAa,CACTC,KAAM,OACNC,aAAc,eACdC,aAAc,eACdC,eAAgB,iBAChBd,MAAO,QACPD,YAAa,cACbgB,gBAAiB,kBACjBjB,UAAW,YACXkB,SAAU,WACVC,sBAAuB,wBACvBC,YAAa,cACbC,SAAU,WACVC,OAAQ,SACRX,MAAO,SAEXY,eAAgB,CACZC,SAAU,WACVC,OAAQ,SACRC,QAAS,UACTC,OAAQ,SACRC,QAAS,UACTC,KAAM,OACNC,MAAO,QACPnI,IAAK,MACLoI,KAAM,OACNrI,MAAO,QACPsI,OAAQ,SACRvI,KAAM,OACNwI,MAAO,QACPC,UAAW,cCjDJ,MAAMC,EACjBC,WAAAA,CAAYC,EAAU,IAClB7J,KAAK6J,QAAUA,EAEf7J,KAAK8J,SAAW,IAAIC,GACxB,CACAC,MAAAA,CAAOC,KAAQC,GACX,MAAMC,EAAYF,EAAIG,OAAO,GACxBpK,KAAK8J,SAASO,IAAIF,IACnBnK,KAAK8J,SAASQ,IAAIH,EAAW,IAAIR,EAAS,IAAI3J,KAAK6J,QAASM,KAEhE,IAAII,EAAMvK,KAAK8J,SAASU,IAAIL,GAC5B,IAAK,IAAIjM,EAAI,EAAGA,EAAI+L,EAAIrK,OAAQ1B,GAAK,EAAG,CACpC,MAAMkI,EAAI6D,EAAIG,OAAOlM,GAChBqM,EAAIE,SAASrE,IACdmE,EAAIG,SAAStE,GAEjBmE,EAAMA,EAAII,SAASvE,EACvB,CAEA,OADAmE,EAAIL,MAAQK,EAAIL,MAAQ,IAAIU,OAAOV,GAC5BlK,IACX,CACA2K,QAAAA,CAASE,GACL,OAAO7K,KAAK8J,SAASU,IAAIK,EAC7B,CACAC,UAAAA,GACI,QAAS9K,KAAKkK,IAClB,CACAQ,QAAAA,CAASG,GACA7K,KAAKyK,SAASI,IACf7K,KAAK8J,SAASQ,IAAIO,EAAO,IAAIlB,EAAS,IAAI3J,KAAK6J,QAASgB,IAEhE,CACAJ,QAAAA,CAASI,GACL,OAAO7K,KAAK8J,SAASO,IAAIQ,EAC7B,ECnCJ,IAAAE,EAAA,CAAgB5E,EAAW6E,KACvBzF,OAAO0F,QAAQ9E,GAAW3H,SAAQ,EAAE0M,EAAQC,MACxCA,EAAc3M,SAAS4M,IACnBJ,EAAQhB,OAAOoB,EAAcF,EAAO,GACtC,IAECF,GCDJ,MAAMK,EACTzB,WAAAA,GACI5J,KAAKsL,SAAW,GAChBtL,KAAKmG,UAAYA,EACjBnG,KAAKuL,aAAeR,EAAoB5E,EAAW,IAAIwD,GACvD3J,KAAKwL,WAAa,CACdtD,WAAY,IAEhBlI,KAAKyL,mBAAqB,GAC1BzL,KAAK0L,8BAAgC,GACrC1L,KAAK2L,aAAezE,EACpBlH,KAAK4L,OAAS,GACd5L,KAAK6L,wBAAyB,EAC9B7L,KAAK8L,qBAAuB,EAC5B9L,KAAK+L,qBAAuB,IAC5B/L,KAAKgM,UAAY,IACjBhM,KAAKiM,uBACT,CAEAC,UAAAA,CAAWC,EAAU,IACbA,EAAQhG,YACRnG,KAAKmG,UAAYgG,EAAQhG,UACzBnG,KAAKuL,aAAeR,EAAoBoB,EAAQhG,UAAW,IAAIwD,IAE/DwC,EAAQX,aACRxL,KAAKwL,WAAaW,EAAQX,WAC1BxL,KAAKiM,yBAELE,EAAQR,cACR3L,KAAKoM,gBAAgBD,EAAQR,cAE7BQ,EAAQP,SACR5L,KAAK4L,OAASO,EAAQP,aAEaS,IAAnCF,EAAQN,yBACR7L,KAAK6L,uBAAyBM,EAAQN,6BAELQ,IAAjCF,EAAQL,uBACR9L,KAAK8L,qBAAuBK,EAAQL,2BAEHO,IAAjCF,EAAQJ,uBACR/L,KAAK+L,qBAAuBI,EAAQJ,2BAEdM,IAAtBF,EAAQH,YACRhM,KAAKgM,UAAYG,EAAQH,UAEjC,CACAI,eAAAA,CAAgBT,GACZ,IAAI3L,KAAKsM,wBAAwBX,GAI7B,MAAM,IAAIY,MAAM,gDAHhBvM,KAAK2L,aAAeA,CAK5B,CACAW,uBAAAA,CAAwBX,GACpB,IAAIa,GAAQ,EAcZ,OAbAjH,OAAOC,KAAK0B,GAAiB1I,SAASiO,IAClC,GAAIA,KAAQd,EAAc,CACtB,MAAMe,EAAkBD,EACxBlH,OAAOC,KAAK0B,EAAgBwF,IAAkBlO,SAASyL,IAC7CA,KAAO0B,EAAae,KACtBF,GAAQ,EACZ,GAER,MAEIA,GAAQ,CACZ,IAEGA,CACX,CACAP,qBAAAA,GACI,MAAMR,EAAqB,CAAA,EACrBkB,EAAgC,CAAA,EACtCpH,OAAOC,KAAKxF,KAAKwL,YAAYhN,SAASoO,IAClCnB,EAAmBmB,GAAQxO,EAAsB4B,KAAKwL,WAAWoB,IACjED,EAA8BC,GAC1B5M,KAAK6M,iCAAiC7M,KAAKwL,WAAWoB,GAAM,IAEpE5M,KAAKyL,mBAAqBA,EAC1BzL,KAAK0L,8BAAgCiB,CACzC,CACAE,gCAAAA,CAAiCnP,GAC7B,MAAMyF,EAAOzF,EAAKoP,KAAKC,GACD,iBAAPA,EACAA,EAAGC,WAAWpN,OAElBmN,EAAGnN,SAGd,OAAoB,IAAhBuD,EAAKvD,OACE,EAEJuD,EAAK8J,QAAO,CAAC3J,EAAGC,IAAMjD,KAAK4M,IAAI5J,EAAGC,KAAK4J,IAClD,CACAC,8BAAAA,CAA+B1P,GAC3B,MAAM2P,EAAkB,GASxB,OARA3P,EAAKc,SAAS8O,IACV,MAAMC,SAAmBD,EACP,WAAdC,GACc,WAAdA,GACc,YAAdA,GACAF,EAAgB1P,KAAK2P,EAAMN,WAAWQ,cAC1C,IAEGpP,EAAsBiP,EACjC,CACAI,0BAAAA,CAA2BjC,GAClBxL,KAAKwL,WAAWtD,aACjBlI,KAAKwL,WAAWtD,WAAa,IAEjC,MAAMwF,EAAU,IAAI1N,KAAKwL,WAAWtD,cAAesD,GACnDxL,KAAKyL,mBAAmBvD,WACpBlI,KAAKoN,+BAA+BM,GACxC1N,KAAK0L,8BAA8BxD,WAC/BlI,KAAK6M,iCAAiCa,EAC9C,CACAC,UAAAA,CAAWf,EAAMgB,GACT5N,KAAKsL,SAASsB,GACdiB,QAAQC,KAAM,WAAUlB,oBAGxB5M,KAAKsL,SAASsB,GAAQgB,CAE9B,EAESG,MAAAA,EAAgB,IAAI1C,EC/HjC,MAAM2C,EACFpE,WAAAA,CAAYqE,GACRjO,KAAKiO,aAAeA,CACxB,CACAnO,KAAAA,EAAMC,SAAEA,IACJ,MAAMmO,EAAmBnO,EAASoO,MAAM,IAAIjL,UAAUkL,KAAK,IAC3D,OAAOpO,KAAKiO,aAAa,CACrBlO,SAAUmO,IACXpB,KAAKhN,IAAW,IACZA,EACHU,MAAOV,EAAMU,MAAM2N,MAAM,IAAIjL,UAAUkL,KAAK,IAC5CC,UAAU,EAEVnQ,EAAG6B,EAASH,OAAS,EAAIE,EAAM3B,EAC/BA,EAAG4B,EAASH,OAAS,EAAIE,EAAM5B,KAEvC,ECrBJ,MAAMoQ,EACF1E,WAAAA,EAAY2E,OAAEA,EAAMC,MAAEA,EAAKC,SAAEA,IACzBzO,KAAK0O,OAAS,GACd1O,KAAK2O,eAAiB,GACtB3O,KAAKuO,OAASA,EACdvO,KAAKwO,MAAQA,EACbxO,KAAKyO,SAAWA,CACpB,CACAG,yBAAAA,CAA0BpN,GACtB,MAAMqN,EAAQ,GACd,IAAItE,EAAMvK,KAAKyO,SACf,IAAK,IAAIvQ,EAAIsD,EAAOtD,EAAI8B,KAAKuO,OAAO3O,OAAQ1B,GAAK,EAAG,CAChD,MAAM4Q,EAAY9O,KAAKuO,OAAOnE,OAAOlM,GAErC,GADAqM,EAAMA,EAAII,SAASmE,IACdvE,EACD,MAEJsE,EAAMlR,KAAK4M,EACf,CACA,OAAOsE,CACX,CAEAE,MAAAA,EAAOC,YAAEA,EAAWC,UAAEA,EAASzN,MAAEA,EAAK0N,SAAEA,EAAQC,QAAEA,EAAOC,cAAEA,EAAaC,oBAAEA,IACtE,GAAIrP,KAAK2O,eAAe/O,QAAUI,KAAKwO,MACnC,OAEJ,GAAIhN,IAAUxB,KAAKuO,OAAO3O,OAItB,YAHIoP,IAAgBC,GAChBjP,KAAK2O,eAAehR,KAAK,CAAEoC,SAAUC,KAAK0O,OAAON,KAAK,IAAKe,aAKnE,MAAMN,EAAQ,IAAI7O,KAAK4O,0BAA0BpN,IACjD,IAAI8N,GAAU,EAEd,IAAK,IAAIpR,EAAIsD,EAAQqN,EAAMjP,OAAS,EAAG1B,GAAKsD,EAAOtD,GAAK,EAAG,CACvD,MAAMqM,EAAMsE,EAAM3Q,EAAIsD,GACtB,GAAI+I,EAAIO,aAAc,CAIlB,GAAIsE,IAAkB7E,EAAIV,QAAQuE,KAAK,KACnCiB,GAAuB,EAEvB,SAEJC,GAAU,EACV,MAAMpF,EAAOK,EAAIL,KAEjB,IAAK,MAAMqF,KAAOrF,EAAM,CACpBlK,KAAK0O,OAAO/Q,KAAK4R,GACjB,MAAMC,EAAUL,EAAQvE,OAAO,CAC3B1M,EAAGgR,EACHhE,OAAQqE,EACRnE,aAAcb,EAAIV,QAAQuE,KAAK,MAgBnC,GAbApO,KAAK+O,OAAO,CACRC,cACAC,YACAzN,MAAOtD,EAAI,EACXgR,SAAUA,EAAWK,EAAI3P,OACzBuP,QAASK,EACTJ,cAAe7E,EAAIV,QAAQuE,KAAK,IAChCiB,oBAAqBD,IAAkB7E,EAAIV,QAAQuE,KAAK,IAClDiB,EAAsB,EACtB,IAGVrP,KAAK0O,OAAOe,MACRzP,KAAK2O,eAAe/O,QAAUI,KAAKwO,MACnC,MAER,CACJ,CACJ,CAGA,IAAKQ,IAAgBM,EAAS,CAC1B,MAAMnF,EAAYnK,KAAKuO,OAAOnE,OAAO5I,GACrCxB,KAAK0O,OAAO/Q,KAAKwM,GACjBnK,KAAK+O,OAAO,CACRC,cACAC,UAAWA,IAAcK,EACzB9N,MAAOA,EAAQ,EACf0N,SAAUA,EAAW,EACrBC,UACAC,gBACAC,wBAEJrP,KAAK0O,OAAOe,KAChB,CACJ,CACAC,MAAAA,GAqBI,OAnBA1P,KAAK+O,OAAO,CACRC,aAAa,EACbC,WAAW,EACXzN,MAAO,EACP0N,SAAU,EACVC,QAAS,GACTC,mBAAe/C,EACfgD,oBAAqB,IAGzBrP,KAAK+O,OAAO,CACRC,aAAa,EACbC,WAAW,EACXzN,MAAO,EACP0N,SAAU,EACVC,QAAS,GACTC,mBAAe/C,EACfgD,oBAAqB,IAElBrP,KAAK2O,cAChB,EC3EJ,MAAMgB,EACF/F,WAAAA,CAAYqE,GACRjO,KAAKiO,aAAeA,CACxB,CACA2B,iBAAAA,CAAkB9R,EAAS+R,GACvB,OAAO/R,EAAQgS,MAAMC,GACVxK,OAAO0F,QAAQ8E,GAAWC,OAAM,EAAE/F,EAAKgG,KAC3B,SAARhG,GAAkBgG,IAAUJ,EAAS5F,MAGxD,CACAnK,KAAAA,EAAMC,SAAEA,IACJ,MAAMjC,EAAU,GACVoS,EDgEYC,EAACpQ,EAAUyO,EAAOC,IACzB,IAAIH,EAAe,CAC9BC,OAAQxO,EACRyO,QACAC,aAEUiB,SCtEcS,CAAkBpQ,EAAUgO,EAAchC,qBAAsBgC,EAAcxC,cACtG,IAAI6E,GAAe,EACfC,GAAqB,EAiCzB,OAhCAH,EAAgB1R,SAAS8R,IACrB,GAAIF,EACA,OAEJ,MAAMG,EAAoBvQ,KAAKiO,aAAa,CACxClO,SAAUuQ,EAAevQ,SACzByQ,eAAgBH,IAGpBA,GAAqB,EACrBE,EAAkB/R,SAASsB,IAClBsQ,IACDA,EAA2B,IAAZtQ,EAAM5B,GAAW4B,EAAM3B,IAAM4B,EAASH,OAAS,GAElE,MAAM6Q,EArEJC,EAACC,EAAkBzS,EAAGC,KACpC,MAGMyS,EAHkBD,EAAiBxB,QAAQrN,QAAQqN,GAC9CA,EAAQjR,EAAIA,IAEW+O,QAAO,CAACgD,EAAOY,IACtCZ,EAAQY,EAAO3F,OAAOtL,OAASiR,EAAOzF,aAAaxL,QAC3D1B,GACG4S,EAAcH,EAAiBxB,QAAQrN,QAAQqN,GAC1CA,EAAQjR,GAAKA,GAAKiR,EAAQjR,GAAKC,IAEpC4S,EAAYD,EAAY7D,QAAO,CAACgD,EAAOY,IAClCZ,EAAQY,EAAO3F,OAAOtL,OAASiR,EAAOzF,aAAaxL,QAC3DzB,EAAID,EAAI0S,GACLI,EAAW,GACXC,EAAa,GAanB,OAZAH,EAAYtS,SAASyR,IACKe,EAASE,WAAWrK,GAC/BA,EAAEqE,SAAW+E,EAAM/E,QAAUrE,EAAEuE,eAAiB6E,EAAM7E,eAE7C,IAChB4F,EAASrT,KAAK,CACVuN,OAAQ+E,EAAM/E,OACdE,aAAc6E,EAAM7E,eAExB6F,EAAWtT,KAAM,GAAEsS,EAAM7E,mBAAmB6E,EAAM/E,UACtD,IAEG,CACHhN,EAAG0S,EACHzS,EAAG4S,EACH7G,KAAM8G,EACNC,WAAYA,EAAW7C,KAAK,MAC/B,EAqC0BsC,CAAUJ,EAAgBxQ,EAAM5B,EAAG4B,EAAM3B,GAClDqC,EAAQT,EAASU,MAAMgQ,EAAOvS,GAAIuS,EAAOtS,EAAI,GAAK,KAClD0R,EAAW,IACV/P,EACHuI,MAAM,EACN7H,WACGiQ,GAEDU,EAAkBnR,KAAK4P,kBAAkB9R,EAAS+R,GAEpDrP,EAAMgN,gBAAkB1N,EAAMsR,aAAgBD,GAC9CrT,EAAQH,KAAKkS,EACjB,GACF,IAKC/R,EAAQgE,QAAQhC,GAAUA,EAAMU,MAAMZ,OAAS,GAC1D,ECrFJ,MAAMyR,EACFzH,WAAAA,GACI5J,KAAKqI,KAAO,IAAIiJ,EAAKtR,KAAKiO,cAC1BjO,KAAKkD,QAAU,IAAIqO,EAAQvR,KAAKiO,aACpC,CACAnO,KAAAA,EAAMC,SAAEA,IACJ,MAAMjC,EAAU,IACTkC,KAAKiO,aAAa,CACjBlO,gBAEDC,KAAKkD,QAAQpD,MAAM,CAAEC,gBACrBC,KAAKqI,KAAKvI,MAAM,CAAEC,cAEzB,OAAOlC,EAAOC,EAClB,CACAmQ,YAAAA,EAAalO,SAAEA,EAAQyQ,eAAEA,GAAiB,IACtC,MAAM1S,EAAU,GACV0T,EAAiBzR,EAASH,OAC1B6R,EAAgB1R,EAASyN,cA2C/B,OAzCAjI,OAAOC,KAAKuI,EAActC,oBAAoBjN,SAASkT,IACnD,MAAMC,EAAa5D,EAActC,mBAAmBiG,GAC9CE,EAA4B7D,EAAcrC,8BAA8BgG,GACxEG,EAAcvR,KAAKoE,IAAIkN,EAA2BJ,GACxD,IAAK,IAAItT,EAAI,EAAGA,EAAIsT,EAAgBtT,GAAK,EAAG,CACxC,MAAM4T,EAAYxR,KAAKoE,IAAIxG,EAAI2T,EAAaL,GAC5C,IAAK,IAAIrT,EAAID,EAAGC,EAAI2T,EAAW3T,GAAK,EAAG,CACnC,MAAM4T,EAAeN,EAAchR,MAAMvC,GAAIC,EAAI,GAAK,KAChD6T,EAAiBD,KAAgBJ,EACvC,IAAIM,EAA2B,CAAA,EAG/B,MAAMC,EAAuB,IAANhU,GAAWC,IAAMqT,EAAiB,EACrDzD,EAAclC,wBACdqG,IACCF,GACDxB,IACAyB,EAA2B/M,EAAwB6M,EAAcJ,EAAY5D,EAAcjC,uBAE/F,MAAMqG,EAAsE,IAAjD5M,OAAOC,KAAKyM,GAA0BrS,OACjE,GAAIoS,GAAkBG,EAAoB,CACtC,MAGMC,EAAOT,EAHYQ,EACnBF,EAAyB/L,yBACzB6L,GAENjU,EAAQH,KAAK,CACToD,QAAS,aACT7C,IACAC,IACAqC,MAAOT,EAASU,MAAMvC,GAAIC,EAAI,GAAK,KACnCiT,YAAaW,EACbK,OACAV,eAAgBA,EAChBrD,UAAU,EACVhG,MAAM,KACH4J,GAEX,CACJ,CACJ,KAEGnU,CACX,EC5DJ,MAAMuU,EACFvS,KAAAA,EAAMC,SAAEA,EAAQuS,QAAEA,EAAU9S,IACxB,MAAM1B,EAAU,GAoBhB,OAnBAyH,OAAOC,KAAK8M,GAAS9T,SAASoO,IAC1B,MAAM2F,EAAQD,EAAQ1F,GAEtB,IAAIlM,EAEJ,IAHA6R,EAAMC,UAAY,EAGV9R,EAAa6R,EAAM5R,KAAKZ,IAC5B,GAAIW,EAAY,CACZ,MAAMF,EAAQE,EAAW,GACzB5C,EAAQH,KAAK,CACToD,QAAS,QACTP,QACAtC,EAAGwC,EAAWc,MACdrD,EAAGuC,EAAWc,MAAQd,EAAW,GAAGd,OAAS,EAC7C6S,UAAW7F,EACXlM,cAER,CACJ,IAEG7C,EAAOC,EAClB,EC9BW,IAAA4U,EAAA,CAGXC,GAAAA,CAAIlP,EAAGhC,GACH,IAAImR,EAAQnP,EACZ,GAAIhC,EAAImR,EACJ,OAAO,EAEX,GAAU,IAANnR,EACA,OAAO,EAEX,IAAIoR,EAAQ,EACZ,IAAK,IAAI3U,EAAI,EAAGA,GAAKuD,EAAGvD,GAAK,EACzB2U,GAASD,EACTC,GAAS3U,EACT0U,GAAS,EAEb,OAAOC,CACV,EACDC,MAAMrP,GACQ,IAANA,EACO,EACJnD,KAAKyS,IAAItP,GAAKnD,KAAKyS,IAAI,IAElCC,KAAKvP,GACMnD,KAAKyS,IAAItP,GAAKnD,KAAKyS,IAAI,GAElCE,SAAAA,CAAUC,GACN,IAAIC,EAAO,EACX,IAAK,IAAIjV,EAAI,EAAGA,GAAKgV,EAAKhV,GAAK,EAC3BiV,GAAQjV,EACZ,OAAOiV,CACX,GCnBJ,IAAAC,EAAgB3U,IAEZ,MAAM4U,EAAc5U,EAAK6U,QAAQnU,EAAgB,IACjD,GAAIkU,EAAYvT,MAAMd,IAClBqU,EAAY7F,gBAAkB6F,EAC9B,OAAO,EAKX,MAAME,EAAc,CAAC5U,EAAaC,EAAWE,GACvC0U,EAAoBD,EAAY3T,OACtC,IAAK,IAAI1B,EAAI,EAAGA,EAAIsV,EAAmBtV,GAAK,EAAG,CAC3C,MAAMqU,EAAQgB,EAAYrV,GAC1B,GAAImV,EAAYvT,MAAMyS,GAClB,OAAO,CAEf,CAIA,MAhCmBc,KACnB,MAAMI,EAAYJ,EAAYlF,MAAM,IAC9BuF,EAAiBD,EAAU3R,QAAQ6R,GAASA,EAAK7T,MAAMZ,KAAYU,OACnEgU,EAAiBH,EAAU3R,QAAQ6R,GAASA,EAAK7T,MAAMb,KAAYW,OACzE,IAAIiU,EAAa,EACjB,MAAMC,EAAkBxT,KAAKoE,IAAIgP,EAAgBE,GACjD,IAAK,IAAI1V,EAAI,EAAGA,GAAK4V,EAAiB5V,GAAK,EACvC2V,GAAcnB,EAAMC,IAAIe,EAAiBE,EAAgB1V,GAE7D,OAAO2V,CAAU,EAuBVE,CAAcV,EACxB,EClCD,MAAMW,EAAiBA,CAACC,EAAQC,KAC5B,IAAItB,EAAQ,EACRuB,EAAMF,EAAOG,QAAQF,GACzB,KAAOC,GAAO,GACVvB,GAAS,EACTuB,EAAMF,EAAOG,QAAQF,EAAWC,EAAMD,EAAUtU,QAEpD,OAAOgT,CAAK,EAchB,IAAAyB,EAAA,EAAkBhM,OAAM6B,OAAM1J,YAC1B,IAAK6H,EACD,OAAO,EAEX,IAAIwL,EAAa,EAoBjB,OAnBA3J,EAAK1L,SAAS+Q,IACV,MAAM+E,YAAEA,EAAWC,cAAEA,GAlBXC,GAAGjF,MAAK/O,YAEtB,MAAMiU,EAAajU,EAAMgN,cAKzB,MAAO,CACH8G,YAJgBN,EAAeS,EAAYlF,EAAInE,cAK/CmJ,cAHkBP,EAAeS,EAAYlF,EAAIrE,QAIpD,EAQ0CsJ,CAAU,CAAEjF,MAAK/O,UACxD,GAAoB,IAAhB8T,GAAuC,IAAlBC,EAIrBV,GAAc,MAEb,CAGD,MAAMa,EAAIpU,KAAKoE,IAAI6P,EAAeD,GAClC,IAAIK,EAAgB,EACpB,IAAK,IAAIzW,EAAI,EAAGA,GAAKwW,EAAGxW,GAAK,EACzByW,GAAiBjC,EAAMC,IAAI4B,EAAgBD,EAAapW,GAE5D2V,GAAcc,CAClB,KAEGd,CACV,EC7CD,MASMe,EAA2BA,EAAGpU,QAAOqU,QAAOC,YAC9C,MAAMC,EAAmBxP,OAAOC,KAAKuI,EAAcnC,OAAOiJ,IAAQjV,OAC5DoV,EAXiBH,KACvB,IAAII,EAAU,EAMd,OALA1P,OAAOC,KAAKqP,GAAOrW,SAASyL,IACxB,MAAMiL,EAAYL,EAAM5K,GACxBgL,GAAWC,EAAUpT,QAAQ4D,KAAYA,IAAO9F,MAAM,IAE1DqV,GAAW1P,OAAO0F,QAAQ4J,GAAOjV,OAC1BqV,CAAO,EAIQE,CAAkBpH,EAAcnC,OAAOiJ,IAC7D,IAAIO,EAAU,EACd,MAAMC,EAAc7U,EAAMZ,OAE1B,IAAK,IAAI1B,EAAI,EAAGA,GAAKmX,EAAanX,GAAK,EAAG,CACtC,MAAMoX,EAAgBhV,KAAKoE,IAAIoQ,EAAO5W,EAAI,GAC1C,IAAK,IAAIC,EAAI,EAAGA,GAAKmX,EAAenX,GAAK,EACrCiX,GAAW1C,EAAMC,IAAIzU,EAAI,EAAGC,EAAI,GAAK4W,EAAmBC,GAAiB7W,CAEjF,CACA,OAAOiX,CAAO,ECZlB,MAYM9J,EAAW,CACbiK,WCvBJ,EAAkB/U,YACd,IAIIgV,EAJAJ,EpBE8B,IoBFM5U,EAAMZ,OAa9C,OAZIwV,IAAYK,OAAOC,oBACnBN,EAAUK,OAAOE,WAMjBH,EADiB,IAAjBhV,EAAMZ,OACOgW,GAGAC,GAEVvV,KAAK4M,IAAIkI,EAASI,EAC5B,EDSGM,KExBJ,EAAkB7U,OAAMD,gBAGpB,IAAIoU,EAAsB,IADR9U,KAAK4M,IAAI5M,KAAKC,IAAIU,EAAO5B,GrBKjB,IqBC1B,OAHI2B,IACAoU,GAAW,GAERA,CACV,EFgBG5J,WGxBJ,EAAkB4G,OAAM/D,WAAUhG,OAAM6B,OAAM1J,QAAOkR,qBACjD,MAAMqE,EAAc3D,EACd4D,EAAsB5C,EAAiB5S,GACvCyV,EAAiB5B,EAAY,CAAEhM,OAAM6B,OAAM1J,UAEjD,IAAI0V,EAUJ,OANIA,EAHmB,aAAnBxE,EAGc,KAIVqE,EAAcC,EAAsBC,GAThB5H,EAAY,EAAM,GAWvC,CACH0H,cACAC,sBACAC,iBACAC,cAEP,EHIG3D,MI1BJ,EAAkBE,YAAW/R,aAAYF,YACrC,MAAM2V,EAAiB,CACnBC,WAAY,GACZC,WAAY,GACZC,MAAO,GACPC,aAAc,GACdC,OAAQ,GACRC,QAAS,IAEb,OAAIhE,KAAa0D,EACLA,EAAe1D,IAAcjS,EAAMZ,OAKtC,eADD6S,EAIOnS,KAAK4M,IAAI5M,KAAKC,IAAIO,SAASJ,EAAW,GAAI,IAAMrB,GvBXrC,IuBanB,CACV,EJMGqX,OK5BJ,EAAkBX,cAAaY,iBAAkBZ,EAAcY,EL6B3DC,SM7BJ,EAAkBpW,QAAOqW,gBACrB,MAAMC,EAAWtW,EAAM4J,OAAO,GAC9B,IAAI2L,EAAc,EAmBlB,OAfIA,EAHmB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAEnCgB,SAASD,GACV,EAETA,EAAShX,MAAM,MACN,GAKA,GAIb+W,IACDd,GAAe,GAEZA,EAAcvV,EAAMZ,MAC9B,ENQGoX,QDLJ,EAAkBnC,QAAOrU,QAAOyW,eAAcnC,YAC1C,IAAIM,EAAUR,EAAyB,CAAEpU,QAAOqU,QAAOC,UAGvD,GAAImC,EAAc,CACd,MAAMC,EAAiB1W,EAAMZ,OAASqX,EACtC,GAAqB,IAAjBA,GAAyC,IAAnBC,EACtB9B,GAAW,MAEV,CACD,IAAI+B,EAAoB,EACxB,IAAK,IAAIjZ,EAAI,EAAGA,GAAKoC,KAAKoE,IAAIuS,EAAcC,GAAiBhZ,GAAK,EAC9DiZ,GAAqBzE,EAAMC,IAAIsE,EAAeC,EAAgBhZ,GAElEkX,GAAW+B,CACf,CACJ,CACA,OAAO7W,KAAK8W,MAAMhC,EACrB,ECZGpU,UO9BJ,IACWrB,GP6CX,IAAA0X,EAAA,CAAgBvX,EAAOC,KACnB,MAAMuX,EAAY,CAAA,EAElB,GAAI,YAAaxX,GAA0B,MAAjBA,EAAMsV,QAC5B,OAAOtV,EAEX,MAAM0V,EA1CY+B,EAACzX,EAAOC,KAC1B,IAAIyV,EAAa,EASjB,OARI1V,EAAMU,MAAMZ,OAASG,EAASH,SAE1B4V,EADuB,IAAvB1V,EAAMU,MAAMZ,OnBRwB,GACD,ImBcpC4V,CAAU,EAgCE+B,CAAczX,EAAOC,GAClCyX,EArBSC,EAAC7K,EAAM9M,IAClBwL,EAASsB,GACFtB,EAASsB,GAAM9M,GAEtBiO,EAAczC,SAASsB,IACvB,YAAamB,EAAczC,SAASsB,GAC7BmB,EAAczC,SAASsB,GAAM8K,QAAQ5X,GAEzC,EAakB2X,CAAW3X,EAAMiB,QAASjB,GACnD,IAAIsV,EAAU,EACkB,iBAArBoC,EACPpC,EAAUoC,EAEa,eAAlB1X,EAAMiB,UACXqU,EAAUoC,EAAiBtB,YAC3BoB,EAAUvB,YAAcyB,EAAiBzB,YACzCuB,EAAUtB,oBAAsBwB,EAAiBxB,oBACjDsB,EAAUrB,eAAiBuB,EAAiBvB,gBAEhD,MAAM0B,EAAerX,KAAK4M,IAAIkI,EAASI,GACvC,MAAO,IACA1V,KACAwX,EACHlC,QAASuC,EACTC,aAAclF,EAAMI,MAAM6E,GAEjC,EQrED,MAAME,EAAgB,CAClB9X,SAAU,GACV+X,QAAS,CAAE,EACXC,iBAAiB,EACjBC,oBAAgB3L,EAChB4L,SAAAA,CAAUC,EAAMC,GACZ,MAAM7Z,EAAS,GACf,IAAK,IAAIJ,EAAI,EAAGA,EAAIga,EAAMha,GAAK,EAAG,CAC9B,IAAI+R,EAAQ,GACM,WAAdkI,IACAlI,EAAQ,CAAA,GAEZ3R,EAAOX,KAAKsS,EAChB,CACA,OAAO3R,CACV,EAED8Z,mBAAAA,CAAoBla,EAAGC,GACnB,MAAO,CACH4C,QAAS,aACTP,MAAOR,KAAKD,SAASU,MAAMvC,GAAIC,EAAI,GAAK,KACxCD,IACAC,IAEP,EAIDka,MAAAA,CAAOvY,EAAOwY,GACV,MAAM7W,EAAI3B,EAAM3B,EACVoa,EAAiBlB,EAAgBvX,EAAOE,KAAKD,UACnD,IAAIyY,EAAKD,EAAenD,QACpBkD,EAAiB,IAKjBE,GAAMxY,KAAK8X,QAAQU,GAAGD,EAAera,EAAI,GAAGoa,EAAiB,IAGjE,IAAI9R,EAAIkM,EAAMO,UAAUqF,GAAkBE,EACrCxY,KAAK+X,kBACNvR,G3BxCuC,M2BwCM8R,EAAiB,IAMlE,IAAIG,GAAa,EACjBlT,OAAOC,KAAKxF,KAAK8X,QAAQtR,EAAE/E,IAAIjD,SAASka,IACpC,MAAMC,EAAuB3Y,KAAK8X,QAAQtR,EAAE/E,GAAGiX,GAC3C5X,SAAS4X,EAAwB,KAAOJ,GACpCK,GAAwBnS,IACxBiS,GAAa,EAErB,IAECA,IAEDzY,KAAK8X,QAAQtR,EAAE/E,GAAG6W,GAAkB9R,EACpCxG,KAAK8X,QAAQpU,EAAEjC,GAAG6W,GAAkBC,EACpCvY,KAAK8X,QAAQU,GAAG/W,GAAG6W,GAAkBE,EAE5C,EAEDI,gBAAAA,CAAiBC,GAEb,IAAI/Y,EAAQE,KAAKoY,oBAAoB,EAAGS,GACxC7Y,KAAKqY,OAAOvY,EAAO,GACnB,IAAK,IAAI5B,EAAI,EAAGA,GAAK2a,EAAmB3a,GAAK,EAAG,CAI5C4B,EAAQE,KAAKoY,oBAAoBla,EAAG2a,GACpC,MAAMrV,EAAMxD,KAAK8X,QAAQpU,EAAExF,EAAI,GAE/BqH,OAAOC,KAAKhC,GAAKhF,SAAS8Z,IAMI,eALR9U,EAAI8U,GAKRvX,SAEVf,KAAKqY,OAAOvY,EAAOgB,SAASwX,EAAgB,IAAM,EACtD,GAER,CACH,EAGDQ,MAAAA,CAAOtH,GACH,MAAMuH,EAAuB,GAC7B,IAAItX,EAAI+P,EAAiB,EAErB8G,EAAiB,EAEjB9R,EAAI,SACR,MAAMvD,EAAOjD,KAAK8X,QAAQtR,EAAE/E,GAW5B,IATIwB,GACAsC,OAAOC,KAAKvC,GAAMzE,SAASwa,IACvB,MAAMC,EAAuBhW,EAAK+V,GAC9BC,EAAuBzS,IACvB8R,EAAiBxX,SAASkY,EAAyB,IACnDxS,EAAIyS,EACR,IAGDxX,GAAK,GAAG,CACX,MAAM3B,EAAQE,KAAK8X,QAAQpU,EAAEjC,GAAG6W,GAChCS,EAAqBG,QAAQpZ,GAC7B2B,EAAI3B,EAAM5B,EAAI,EACdoa,GAAkB,CACtB,CACA,OAAOS,CACX,GAEW,IAAArB,EAAA,CAiCXyB,0BAAAA,CAA2BpZ,EAAUjC,EAASia,GAAkB,GAC5DF,EAAc9X,SAAWA,EACzB8X,EAAcE,gBAAkBA,EAChC,MAAMvG,EAAiBzR,EAASH,OAEhC,IAAIwZ,EAAuBvB,EAAcI,UAAUzG,EAAgB,SACnE1T,EAAQU,SAASsB,IACbsZ,EAAqBtZ,EAAM3B,GAAGR,KAAKmC,EAAM,IAG7CsZ,EAAuBA,EAAqBtM,KAAKhN,GAAUA,EAAM/B,MAAK,CAACC,EAAIC,IAAOD,EAAGE,EAAID,EAAGC,MAC5F2Z,EAAcC,QAAU,CAOpBpU,EAAGmU,EAAcI,UAAUzG,EAAgB,UAG3CgH,GAAIX,EAAcI,UAAUzG,EAAgB,UAE5ChL,EAAGqR,EAAcI,UAAUzG,EAAgB,WAE/C,IAAK,IAAI/P,EAAI,EAAGA,EAAI+P,EAAgB/P,GAAK,EACrC2X,EAAqB3X,GAAGjD,SAASsB,IACzBA,EAAM5B,EAAI,EACVqH,OAAOC,KAAKqS,EAAcC,QAAQpU,EAAE5D,EAAM5B,EAAI,IAAIM,SAAS8Z,IACvDT,EAAcQ,OAAOvY,EAAOgB,SAASwX,EAAgB,IAAM,EAAE,IAIjET,EAAcQ,OAAOvY,EAAO,EAChC,IAEJ+X,EAAce,iBAAiBnX,GAEnC,MAAMsX,EAAuBlB,EAAciB,OAAOtH,GAC5C6H,EAAwBN,EAAqBnZ,OAC7CwV,EAAUpV,KAAKsZ,WAAWvZ,EAAUsZ,GAC1C,MAAO,CACHtZ,WACAqV,UACAwC,aAAclF,EAAMI,MAAMsC,GAC1BwB,SAAUmC,EAEjB,EACDO,UAAAA,CAAWvZ,EAAUsZ,GACjB,MAAM7H,EAAiBzR,EAASH,OAChC,IAAIwV,EAAU,EAQd,OANIA,EADoB,IAApBrV,EAASH,OACC,EAINiY,EAAcC,QAAQtR,EAAEgL,EAAiB,GAAG6H,GAE7CjE,CACX,GC/MJ,MAAMmE,EAEFzZ,KAAAA,EAAMC,SAAEA,EAAQyZ,UAAEA,IACd,MAAM1b,EAAU,GAChB,IAAI0U,EAAY,EAChB,KAAOA,EAAYzS,EAASH,QAAQ,CAChC,MAAM6Z,EAAczZ,KAAK0Z,eAAe3Z,EAAUyS,GAC5CmH,EAAY3Z,KAAK4Z,aAAa7Z,EAAUyS,GAC9C,GAAmB,MAAfiH,EACA,MAEJ,MAAM3Z,MAAEA,EAAK+Z,UAAEA,GAAc7Z,KAAK8Z,cAAcL,EAAaE,GAC7D,GAAI7Z,EAAO,CACP,MAAM3B,EAAI2B,EAAM0B,MAAQ1B,EAAM,GAAGF,OAAS,EACpCmW,EAAc/V,KAAK+Z,eAAeF,EAAWL,GACnD1b,EAAQH,KAAKqC,KAAKga,eAAeH,EAAW1b,EAAG2B,EAAOiW,IACtDvD,EAAYrU,EAAI,CACpB,CACJ,CAIA,OAHoBL,EAAQgS,MAAMhQ,GACvBA,aAAiBma,UAGjBA,QAAQC,IAAIpc,GAEhBA,CACX,CAEAkc,cAAAA,CAAeH,EAAW1b,EAAG2B,EAAOiW,GAChC,MAAMoE,EAAY,CACdpZ,QAAS,SACT7C,EAAG4B,EAAM0B,MACTrD,IACAqC,MAAOV,EAAM,GACb+Z,YACA9D,YAAa,EACbY,YAAa7W,EAAM,GAAGF,OAASia,EAAUja,QAE7C,OAAImW,aAAuBkE,QAChBlE,EAAYqE,MAAMC,IACd,IACAF,EACHpE,YAAasE,MAIlB,IACAF,EACHpE,cAER,CACA2D,cAAAA,CAAe3Z,EAAUyS,GACrB,MAAM8H,EAAS,WAEf,OADAA,EAAO9H,UAAYA,EACZ8H,EAAO3Z,KAAKZ,EACvB,CACA6Z,YAAAA,CAAa7Z,EAAUyS,GACnB,MAAM+H,EAAO,YAEb,OADAA,EAAK/H,UAAYA,EACV+H,EAAK5Z,KAAKZ,EACrB,CACA+Z,aAAAA,CAAcL,EAAaE,GACvB,MAAMa,EAAe,aACrB,IAAI1a,EACA+Z,EAAY,GAChB,GAAIF,GAAaF,EAAY,GAAG7Z,OAAS+Z,EAAU,GAAG/Z,OAAQ,CAI1DE,EAAQ2Z,EAKR,MAAMxW,EAAOuX,EAAa7Z,KAAKb,EAAM,IACjCmD,IACA4W,EAAY5W,EAAK,GAEzB,MAKInD,EAAQ6Z,EACJ7Z,IACA+Z,EAAY/Z,EAAM,IAG1B,MAAO,CACHA,QACA+Z,YAER,CACAE,cAAAA,CAAeF,EAAWL,GACtB,MAAM1b,EAAU0b,EAAU1Z,MAAM+Z,GAChC,GAAI/b,aAAmBmc,QACnB,OAAOnc,EAAQsc,MAAMK,GACI/C,EAAQyB,2BAA2BU,EAAWY,GAC/CrF,UAI5B,OADqBsC,EAAQyB,2BAA2BU,EAAW/b,GAC/CsX,OACxB,ECvGJ,MAAMsF,EACF9Q,WAAAA,GACI5J,KAAK2a,UAAY,CACrB,CAEA7a,KAAAA,EAAMC,SAAEA,IAeJ,MAAMzB,EAAS,GACf,GAAwB,IAApByB,EAASH,OACT,MAAO,GAEX,IAAI1B,EAAI,EACJ0c,EAAY,KAChB,MAAMpJ,EAAiBzR,EAASH,OAChC,IAAK,IAAI6B,EAAI,EAAGA,EAAI+P,EAAgB/P,GAAK,EAAG,CACxC,MAAMoZ,EAAQ9a,EAASgE,WAAWtC,GAAK1B,EAASgE,WAAWtC,EAAI,GAI/D,GAHiB,MAAbmZ,IACAA,EAAYC,GAEZA,IAAUD,EAAW,CACrB,MAAMzc,EAAIsD,EAAI,EACdzB,KAAKqY,OAAO,CACRna,IACAC,IACA0c,MAAOD,EACP7a,WACAzB,WAEJJ,EAAIC,EACJyc,EAAYC,CAChB,CACJ,CAQA,OAPA7a,KAAKqY,OAAO,CACRna,IACAC,EAAGqT,EAAiB,EACpBqJ,MAAOD,EACP7a,WACAzB,WAEGA,CACX,CACA+Z,MAAAA,EAAOna,EAAEA,EAACC,EAAEA,EAAC0c,MAAEA,EAAK9a,SAAEA,EAAQzB,OAAEA,IAC5B,GAAIH,EAAID,EAAI,GAAyB,IAApBoC,KAAKC,IAAIsa,GAAc,CACpC,MAAMC,EAAgBxa,KAAKC,IAAIsa,GAC/B,GAAIC,EAAgB,GAAKA,GAAiB9a,KAAK2a,UAAW,CACtD,MAAMna,EAAQT,EAASU,MAAMvC,GAAIC,EAAI,GAAK,MACpC4c,aAAEA,EAAYC,cAAEA,GAAkBhb,KAAKib,YAAYza,GACzD,OAAOlC,EAAOX,KAAK,CACfoD,QAAS,WACT7C,IACAC,IACAqC,MAAOT,EAASU,MAAMvC,GAAIC,EAAI,GAAK,KACnC4c,eACAC,gBACAnE,UAAWgE,EAAQ,GAE3B,CACJ,CACA,OAAO,IACX,CACAI,WAAAA,CAAYza,GAGR,IAAIua,EAAe,UACfC,EAAgB,GAapB,OAZIjc,EAAUmc,KAAK1a,IACfua,EAAe,QACfC,EAAgB,IAEXnc,EAAUqc,KAAK1a,IACpBua,EAAe,QACfC,EAAgB,IAEX5b,EAAU8b,KAAK1a,KACpBua,EAAe,SACfC,EAAgB,IAEb,CACHD,eACAC,gBAER,EC7FJ,MAAMG,EACFvR,WAAAA,GACI5J,KAAKob,WAAa,mDACtB,CACAtb,KAAAA,EAAMC,SAAEA,IACJ,MAAMjC,EAAU,GAKhB,OAJAyH,OAAOC,KAAKuI,EAAcnC,QAAQpN,SAAS6c,IACvC,MAAMxG,EAAQ9G,EAAcnC,OAAOyP,GACnC7d,EAAOM,EAASkC,KAAK+O,OAAOhP,EAAU8U,EAAOwG,GAAW,IAErDxd,EAAOC,EAClB,CACAwd,cAAAA,CAAeD,EAAWtb,EAAUyB,GAChC,OAAK6Z,EAAUtE,SAAS,WAEpB/W,KAAKob,WAAWF,KAAKnb,EAASqK,OAAO5I,IAC9B,EAEJ,CACX,CAEAuN,MAAAA,CAAOhP,EAAU8U,EAAOwG,GACpB,IAAIpE,EACJ,MAAMnZ,EAAU,GAChB,IAAII,EAAI,EACR,MAAMsT,EAAiBzR,EAASH,OAChC,KAAO1B,EAAIsT,EAAiB,GAAG,CAC3B,IAAIrT,EAAID,EAAI,EACRqd,EAAgB,KAChBzG,EAAQ,EAGZ,IAFAmC,EAAejX,KAAKsb,eAAeD,EAAWtb,EAAU7B,KAE3C,CACT,MACMsd,EAAY3G,EADD9U,EAASqK,OAAOjM,EAAI,KACA,GACrC,IAAImH,GAAQ,EACRmW,GAAkB,EAClBC,GAAgB,EAEpB,GAAIvd,EAAIqT,EAAgB,CACpB,MAAMmK,EAAU5b,EAASqK,OAAOjM,GAC1Byd,EAAkBJ,EAAU5b,OAClC,IAAK,IAAI6B,EAAI,EAAGA,EAAIma,EAAiBna,GAAK,EAAG,CACzC,MAAMoa,EAAWL,EAAU/Z,GAG3B,GAFAia,GAAgB,EAEZG,EAAU,CACV,MAAMC,EAAgBD,EAASzH,QAAQuH,GAEvC,IAAuB,IAAnBG,EAAsB,CACtBxW,GAAQ,EACRmW,EAAiBC,EAEK,IAAlBI,IAKA7E,GAAgB,GAGhBsE,IAAkBE,IAIlB3G,GAAS,EACTyG,EAAgBE,GAEpB,KACJ,CACJ,CACJ,CACJ,CAEA,IAAInW,EAIC,CAEGnH,EAAID,EAAI,GACRJ,EAAQH,KAAK,CACToD,QAAS,UACT7C,IACAC,EAAGA,EAAI,EACPqC,MAAOT,EAASU,MAAMvC,EAAGC,GACzB0W,MAAOwG,EACPvG,QACAmC,iBAIR/Y,EAAIC,EACJ,KACJ,CAnBIA,GAAK,CAoBb,CACJ,CACA,OAAOL,CACX,ECxGJ,MAAMka,EAAiB,IAAI+D,OAAQ,IAAGrc,EAAgB0O,KAAK,QAM3D,MAAM4N,EACF,+BAAOC,CAAyBlc,GAC5B,MAAMmc,EAAqB,IACpBnc,EACEoO,MAAM,IACNrM,QAAQsE,GAAM4R,EAAekD,KAAK9U,KAClC6G,QAAO,CAACkP,EAAM/V,KACf,MAAM1C,EAAIyY,EAAK3R,IAAIpE,GAOnB,OANI1C,EACAyY,EAAK7R,IAAIlE,EAAG1C,EAAI,GAGhByY,EAAK7R,IAAIlE,EAAG,GAET+V,CAAI,GACZ,IAAIpS,KACFkB,WACPlN,MAAK,EAAEqe,EAAI9Y,IAAK+Y,EAAI9Y,KAAOA,EAAID,IACjC,IAAK4Y,EAAmBtc,OACpB,OACJ,MAAME,EAAQoc,EAAmB,GAEjC,OAAIpc,EAAM,GAAK,OAAf,EAEOA,EAAM,EACjB,CACA,wBAAOwc,CAAkBtb,GACrB,OAAO,IAAI+a,OAAQ,MAAK/a,SAAiBA,QAAgBA,KAAc,IAI3E,CAEAlB,KAAAA,EAAMC,SAAEA,IACJ,MAAMzB,EAAS,GACf,GAAwB,IAApByB,EAASH,OACT,OAAOtB,EACX,MAAMie,EAAkBP,EAAeC,yBAAyBlc,GAChE,QAAwBsM,IAApBkQ,EACA,OAAOje,EACX,MAAMke,EAAcR,EAAeM,kBAAkBC,GAErD,IAAK,MAAMzc,KAASC,EAAS0c,SAASD,GAAc,CAEhD,QAAoBnQ,IAAhBvM,EAAM0B,MACN,SAIJ,MAAMtD,EAAI4B,EAAM0B,MAAQ,EACxBlD,EAAOX,KAAK,CACRoD,QAAS,YACTP,MAAO+b,EACPre,IACAC,EAAGD,GAEX,CACA,OAAOI,CACX,ECxDJ,MAAMoe,EACF9S,WAAAA,GACI5J,KAAKsL,SAAW,CACZwK,KAAM6G,EACNnR,WAAYoR,EACZrK,MAAOsK,EAEPnG,OAAQoG,EACRlG,SAAUmG,EACV/F,QAASgG,EACThc,UAAWic,EAEnB,CACAnd,KAAAA,CAAMC,GACF,MAAMjC,EAAU,GACVof,EAAW,GA2BjB,MA1BiB,IACV3X,OAAOC,KAAKxF,KAAKsL,aACjB/F,OAAOC,KAAKuI,EAAczC,WAExB9M,SAASyL,IACd,IAAKjK,KAAKsL,SAASrB,KAAS8D,EAAczC,SAASrB,GAC/C,OAEJ,MAIM3L,GADc,IAHJ0B,KAAKsL,SAASrB,GACxBjK,KAAKsL,SAASrB,GACd8D,EAAczC,SAASrB,GAAKyS,WAEP5c,MAAM,CAC7BC,WACAyZ,UAAWxZ,OAEX1B,aAAkB2b,SAClB3b,EAAO8b,MAAM+C,IACT3f,EAAOM,EAASqf,EAAS,IAE7BD,EAASvf,KAAKW,IAGdd,EAAOM,EAASQ,EACpB,IAEA4e,EAAStd,OAAS,EACX,IAAIqa,SAAQ,CAACmD,EAASC,KACzBpD,QAAQC,IAAIgD,GACP9C,MAAK,KACNgD,EAAQvf,EAAOC,GAAS,IAEvBwf,OAAOC,IACRF,EAAOE,EAAM,GACf,IAGH1f,EAAOC,EAClB,EC9DJ,MAIM0f,EAAQC,QACRC,EAAOF,SAEPG,EAAQ,CACV1U,OARW,EASXE,OARWyU,GASXvU,KARSwU,KAST1c,IARQ2c,MASR5c,MAAOsc,EACPvc,KAAMyc,EACNK,QARYL,UAehB,MAAMM,EACFC,SAAAA,CAAUC,EAAYjO,GAClB,IAAIhG,EAAMiU,OACI7R,IAAV4D,GAAiC,IAAVA,IACvBhG,GAAO,KAEX,MAAMlB,eAAEA,GAAmBgF,EAAcpC,aACzC,OAAO5C,EAAekB,GAAKqJ,QAAQ,SAAW,GAAErD,IACpD,CACAkO,mBAAAA,CAAoB/I,GAChB,MAAMgJ,EAAoB,CACtBC,2BAA4BjJ,GAAW,IAAM,MAC7CkJ,8BAA+BlJ,EAAU,GACzCmJ,+BAAgCnJ,EAAU,IAC1CoJ,gCAAiCpJ,EAAU,MAEzCqJ,EAAoB,CACtBJ,2BAA4B,GAC5BC,8BAA+B,GAC/BC,+BAAgC,GAChCC,gCAAiC,IAOrC,OALAjZ,OAAOC,KAAK4Y,GAAmB5f,SAASkgB,IACpC,MAAMxV,EAAUkV,EAAkBM,GAClCD,EAAkBC,GACd1e,KAAK2e,YAAYzV,EAAQ,IAE1B,CACHkV,oBACAK,oBACAzZ,MAAOhF,KAAK4e,eAAexJ,GAEnC,CACAwJ,cAAAA,CAAexJ,GAEX,OAAIA,EAAU,KAEH,EAEPA,EAAU,QAEH,EAEPA,EAAU,UAEH,EAEPA,EAAU,YAGH,EAGJ,CACX,CACAuJ,WAAAA,CAAYzV,GACR,IACI2V,EADAX,EAAa,YAEjB,MAAMY,EAAWvZ,OAAOC,KAAKmY,GACvBoB,EAAaD,EAAS5N,WAAW8N,GAAS9V,EAAUyU,EAAMqB,KAUhE,OATID,GAAc,IACdb,EAAaY,EAASC,EAAa,GAChB,IAAfA,EACAF,EAAOve,KAAK8W,MAAMlO,EAAUyU,EAAMO,IAGlCA,EAAa,YAGdle,KAAKie,UAAUC,EAAYW,EACtC,EC5FJ,IAAAI,EAAA,IACW,KCAXtC,GAAA,KACW,CACHuC,QAASnR,EAAcpC,aAAaxE,SAASO,MAC7CU,YAAa,CAAC2F,EAAcpC,aAAavD,YAAYV,SCF7D,MA+BMyX,GAAuBA,CAACrf,EAAOsf,KACjC,IAAIF,EAAU,KACd,MAAMG,EAAWvf,EAAM4R,eACjB4N,EAAuB,cAAbD,GAA4BA,EAAS7R,cAAcuJ,SAAS,cAa5E,MAZiB,cAAbsI,EACAH,EApC6BK,EAACzf,EAAOsf,KACzC,IAAIF,EAAU,KAed,OAdIE,GAAgBtf,EAAMuI,MAASvI,EAAMuO,SAWhCvO,EAAM8X,cAAgB,IAC3BsH,EAAUnR,EAAcpC,aAAaxE,SAASW,iBAV1CoX,EADApf,EAAMsS,MAAQ,GACJrE,EAAcpC,aAAaxE,SAASQ,OAEzC7H,EAAMsS,MAAQ,IACTrE,EAAcpC,aAAaxE,SAASS,WAGpCmG,EAAcpC,aAAaxE,SAASU,OAM/CqX,CAAO,EAoBAK,CAA6Bzf,EAAOsf,GAEzCC,EAAStI,SAAS,aACvBmI,EArB8BM,EAAC1f,EAAOsf,KAC1C,IAAIF,EAAU,KAId,OAHIE,IACAF,EAAUnR,EAAcpC,aAAaxE,SAASY,cAE3CmX,CAAO,EAgBAM,CAA8B1f,EAAOsf,GAE1CE,EACLJ,EAjB0BO,EAAC3f,EAAOsf,IAClCA,EACOrR,EAAcpC,aAAaxE,SAASa,kBAExC+F,EAAcpC,aAAaxE,SAASc,YAa7BwX,CAA0B3f,EAAOsf,GAEzB,eAAbC,IACLH,EAAUnR,EAAcpC,aAAaxE,SAASe,YAE3CgX,CAAO,EAElB,IAAAtC,GAAA,CAAgB9c,EAAOsf,KACnB,MAAMF,EAAUC,GAAqBrf,EAAOsf,GACtChX,EAAc,GACd3J,EAAOqB,EAAMU,MAanB,OAZI/B,EAAKqB,MAAMnB,GACXyJ,EAAYzK,KAAKoQ,EAAcpC,aAAavD,YAAYI,gBAEnD/J,EAAKqB,MAAMhB,IAAuBL,EAAK+O,gBAAkB/O,GAC9D2J,EAAYzK,KAAKoQ,EAAcpC,aAAavD,YAAYG,cAExDzI,EAAMuO,UAAYvO,EAAMU,MAAMZ,QAAU,GACxCwI,EAAYzK,KAAKoQ,EAAcpC,aAAavD,YAAYE,cAExDxI,EAAMuI,MACND,EAAYzK,KAAKoQ,EAAcpC,aAAavD,YAAYC,MAErD,CACH6W,UACA9W,cAEP,ECtEDyU,GAAgB/c,GACY,eAApBA,EAAM2S,UACC,CACHyM,QAASnR,EAAcpC,aAAaxE,SAASM,YAC7CW,YAAa,CACT2F,EAAcpC,aAAavD,YAAYX,YACvCsG,EAAcpC,aAAavD,YAAYK,kBAI5C,CACHyW,QAAS,KACT9W,YAAa,ICZrB0U,GAAgBhd,IACZ,IAAIof,EAAUnR,EAAcpC,aAAaxE,SAASI,eAIlD,OAH+B,IAA3BzH,EAAM+Z,UAAUja,SAChBsf,EAAUnR,EAAcpC,aAAaxE,SAASG,cAE3C,CACH4X,UACA9W,YAAa,CAAC2F,EAAcpC,aAAavD,YAAYM,UAE5D,ECTDqU,GAAA,KACW,CACHmC,QAASnR,EAAcpC,aAAaxE,SAASK,UAC7CY,YAAa,CAAC2F,EAAcpC,aAAavD,YAAYZ,aCH7DwV,GAAgBld,IACZ,IAAIof,EAAUnR,EAAcpC,aAAaxE,SAASE,WAIlD,OAHoB,IAAhBvH,EAAMgV,QACNoK,EAAUnR,EAAcpC,aAAaxE,SAASC,aAE3C,CACH8X,UACA9W,YAAa,CAAC2F,EAAcpC,aAAavD,YAAYO,uBAE5D,ECVDsU,GAAA,IAEW,KCOX,MAAMyC,GAAkB,CACpBR,QAAS,KACT9W,YAAa,IAOjB,MAAMuX,GACF/V,WAAAA,GACI5J,KAAKsL,SAAW,CACZiK,WAAY0J,EACZnJ,KAAM6G,GACNnR,WAAYoR,GACZrK,MAAOsK,GACPnG,OAAQoG,GACRlG,SAAUmG,GACV/F,QAASgG,GACThc,UAAWic,IAEfjd,KAAK0f,gBAAkB,CACnBR,QAAS,KACT9W,YAAa,IAEjBpI,KAAK4f,uBACT,CACAA,qBAAAA,GACI5f,KAAK0f,gBAAgBtX,YAAYzK,KAAKoQ,EAAcpC,aAAavD,YAAYS,SAAUkF,EAAcpC,aAAavD,YAAYU,OAClI,CACA+W,WAAAA,CAAY7a,EAAO4R,GACf,GAAwB,IAApBA,EAAShX,OACT,OAAOI,KAAK0f,gBAEhB,GAAI1a,EAAQ,EACR,OAAO0a,GAEX,MAAMI,EAAgB/R,EAAcpC,aAAavD,YAAYQ,YACvDmX,EAAe/f,KAAKggB,gBAAgBpJ,GAC1C,IAAIqJ,EAAWjgB,KAAKkgB,iBAAiBH,EAAkC,IAApBnJ,EAAShX,QAU5D,OATIqgB,QACAA,EAAS7X,YAAY8Q,QAAQ4G,GAG7BG,EAAW,CACPf,QAAS,KACT9W,YAAa,CAAC0X,IAGfG,CACX,CACAD,eAAAA,CAAgBpJ,GACZ,IAAImJ,EAAenJ,EAAS,GAO5B,OANuBA,EAASnW,MAAM,GACvBjC,SAASsB,IAChBA,EAAMU,MAAMZ,OAASmgB,EAAavf,MAAMZ,SACxCmgB,EAAejgB,EACnB,IAEGigB,CACX,CACAG,gBAAAA,CAAiBpgB,EAAOsf,GACpB,OAAIpf,KAAKsL,SAASxL,EAAMiB,SACbf,KAAKsL,SAASxL,EAAMiB,SAASjB,EAAOsf,GAE3CrR,EAAczC,SAASxL,EAAMiB,UAC7B,aAAcgN,EAAczC,SAASxL,EAAMiB,SACpCgN,EAAczC,SAASxL,EAAMiB,SAASkf,SAASngB,EAAOsf,GAE1DM,EACX,ECzEJ,MAAMV,GAAOA,KAAM,IAAI1f,MAAO6gB,UACxBC,GAAoBA,CAAC3F,EAAiB1a,EAAUyE,KAClD,MAAMyb,EAAW,IAAIN,GACfU,EAAgB,IAAIrC,EACpBsC,EAAgB5I,EAAQyB,2BAA2BpZ,EAAU0a,GAC7D8F,EAAWvB,KAASxa,EACpBgc,EAAcH,EAAclC,oBAAoBmC,EAAclL,SACpE,MAAO,CACHmL,cACGD,KACAE,EACHP,SAAUA,EAASJ,YAAYW,EAAYxb,MAAOsb,EAAc1J,UACnE,EAEC6J,GAAOA,CAAC1gB,EAAUmI,KAChBA,GACA6F,EAAcN,2BAA2BvF,GAG7C,OADiB,IAAIwU,GACL5c,MAAMC,EAAS,gCCnBnC,CAAgB2gB,EAAMC,EAAMC,KACxB,IAAIC,EACJ,OAAO,YAAqBC,GACxB,MAAMC,EAAU/gB,KAOVghB,EAAgBJ,IAAgBC,EAKtC,QAJgBxU,IAAZwU,GACAI,aAAaJ,GAEjBA,EAAUK,YAVIC,KACVN,OAAUxU,EACLuU,GACDF,EAAK9iB,MAAMmjB,EAASD,EACxB,GAMwBH,GACxBK,EACA,OAAON,EAAK9iB,MAAMmjB,EAASD,GAItC,WDCqBM,CAACrhB,EAAUmI,KAC7B,MAAM1D,EAAQwa,KACRlhB,EAAU2iB,GAAK1gB,EAAUmI,GAC/B,GAAIpK,aAAmBmc,QACnB,MAAM,IAAI1N,MAAM,sEAEpB,OAAO6T,GAAkBtiB,EAASiC,EAAUyE,EAAM,gBAE3B6c,MAAOthB,EAAUmI,KACxC,MAAM6J,EAAehS,EAASmU,UAAU,EAAGnG,EAAc/B,WACnDxH,EAAQwa,KACRlhB,QAAgB2iB,GAAK1O,EAAc7J,GACzC,OAAOkY,GAAkBtiB,EAASiU,EAAcvN,EAAM", "x_google_ignoreList": [4]}