// src/pt-PT.ts
var ptPT = {
  locale: "pt-PT",
  apiKeys: {
    action__add: void 0,
    action__search: void 0,
    createdAndExpirationStatus__expiresOn: void 0,
    createdAndExpirationStatus__never: void 0,
    detailsTitle__emptyRow: void 0,
    formButtonPrimary__add: void 0,
    formFieldCaption__expiration__expiresOn: void 0,
    formFieldCaption__expiration__never: void 0,
    formFieldOption__expiration__180d: void 0,
    formFieldOption__expiration__1d: void 0,
    formFieldOption__expiration__1y: void 0,
    formFieldOption__expiration__30d: void 0,
    formFieldOption__expiration__60d: void 0,
    formFieldOption__expiration__7d: void 0,
    formFieldOption__expiration__90d: void 0,
    formFieldOption__expiration__never: void 0,
    formHint: void 0,
    formTitle: void 0,
    lastUsed__days: void 0,
    lastUsed__hours: void 0,
    lastUsed__minutes: void 0,
    lastUsed__months: void 0,
    lastUsed__seconds: void 0,
    lastUsed__years: void 0,
    menuAction__revoke: void 0,
    revokeConfirmation: {
      confirmationText: void 0,
      formButtonPrimary__revoke: void 0,
      formHint: void 0,
      formTitle: void 0
    }
  },
  backButton: "Voltar",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Padr\xE3o",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Personificar outro dispositivo",
  badge__primary: "Principal",
  badge__renewsAt: void 0,
  badge__requiresAction: "Requer a\xE7\xE3o",
  badge__startsAt: void 0,
  badge__thisDevice: "Este dispositivo",
  badge__unverified: "N\xE3o verificado",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Dispositivo do utilizador",
  badge__you: "O utilizador",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      pastDueNotice: void 0,
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    credit: void 0,
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDue: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Criar organiza\xE7\xE3o",
    invitePage: {
      formButtonReset: "Ignorar"
    },
    title: "Criar organiza\xE7\xE3o"
  },
  dates: {
    lastDay: "Ontem \xE0s {{ date | timeString('pt-PT') }}",
    next6Days: "{{ date | weekday('pt-PT','long') }} \xE0s {{ date | timeString('pt-PT') }}",
    nextDay: "Amanh\xE3 \xE0s {{ date | timeString('pt-PT') }}",
    numeric: "{{ date | numeric('pt-PT') }}",
    previous6Days: "\xDAltimo {{ date | weekday('pt-PT','long') }} \xE0s {{ date | timeString('pt-PT') }}",
    sameDay: "Hoje \xE0s {{ date | timeString('pt-PT') }}"
  },
  dividerText: "ou",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Utilize outro m\xE9todo",
  footerPageLink__help: "Ajuda",
  footerPageLink__privacy: "Privacidade",
  footerPageLink__terms: "Termos de uso",
  formButtonPrimary: "Continuar",
  formButtonPrimary__verify: "Verify",
  formFieldAction__forgotPassword: "Esqueceu a palavra-passe?",
  formFieldError__matchingPasswords: "Passwords match.",
  formFieldError__notMatchingPasswords: "Passwords don't match.",
  formFieldError__verificationLinkExpired: "The verification link expired. Please request a new link.",
  formFieldHintText__optional: "Opcional",
  formFieldHintText__slug: "A slug is a human-readable ID that must be unique. It\u2019s often used in URLs.",
  formFieldInputPlaceholder__apiKeyDescription: void 0,
  formFieldInputPlaceholder__apiKeyExpirationDate: void 0,
  formFieldInputPlaceholder__apiKeyName: void 0,
  formFieldInputPlaceholder__backupCode: "Insira o c\xF3digo de backup",
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Eliminar conta",
  formFieldInputPlaceholder__emailAddress: "Insira o seu endere\xE7o de e-mail",
  formFieldInputPlaceholder__emailAddress_username: "Insira o seu e-mail ou nome de utilizador",
  formFieldInputPlaceholder__emailAddresses: "Insira um ou mais endere\xE7os de e-mail separados por espa\xE7os ou v\xEDrgulas",
  formFieldInputPlaceholder__firstName: "Insira o seu primeiro nome",
  formFieldInputPlaceholder__lastName: "Insira o seu apelido",
  formFieldInputPlaceholder__organizationDomain: "Insira o dom\xEDnio da organiza\xE7\xE3o",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "Insira o endere\xE7o de e-mail do dom\xEDnio da organiza\xE7\xE3o",
  formFieldInputPlaceholder__organizationName: "Insira o nome da organiza\xE7\xE3o",
  formFieldInputPlaceholder__organizationSlug: "Insira o identificador da organiza\xE7\xE3o (slug)",
  formFieldInputPlaceholder__password: "Insira a sua palavra-passe",
  formFieldInputPlaceholder__phoneNumber: "Insira o seu n\xFAmero de telefone",
  formFieldInputPlaceholder__username: "Insira o seu nome de utilizador",
  formFieldLabel__apiKeyDescription: void 0,
  formFieldLabel__apiKeyExpiration: void 0,
  formFieldLabel__apiKeyName: void 0,
  formFieldLabel__automaticInvitations: "Ativar convites autom\xE1ticos para este dom\xEDnio",
  formFieldLabel__backupCode: "C\xF3digo de backup",
  formFieldLabel__confirmDeletion: "Confirmar exclus\xE3o",
  formFieldLabel__confirmPassword: "Confirmar palavra-passe",
  formFieldLabel__currentPassword: "Palavra-passe atual",
  formFieldLabel__emailAddress: "Insira o seu e-mail",
  formFieldLabel__emailAddress_username: "E-mail ou nome de utilizador",
  formFieldLabel__emailAddresses: "Endere\xE7os de e-mail",
  formFieldLabel__firstName: "Nome",
  formFieldLabel__lastName: "Apelido",
  formFieldLabel__newPassword: "Nova palavra-passe",
  formFieldLabel__organizationDomain: "Dom\xEDnio",
  formFieldLabel__organizationDomainDeletePending: "Excluir convites e sugest\xF5es pendentes",
  formFieldLabel__organizationDomainEmailAddress: "Endere\xE7o de e-mail de verifica\xE7\xE3o",
  formFieldLabel__organizationDomainEmailAddressDescription: "Endere\xE7o de e-mail para receber um c\xF3digo e verificar este dom\xEDnio",
  formFieldLabel__organizationName: "Nome da organiza\xE7\xE3o",
  formFieldLabel__organizationSlug: "URL Slug",
  formFieldLabel__passkeyName: "Nome da Chave de Acesso",
  formFieldLabel__password: "Palavra-passe",
  formFieldLabel__phoneNumber: "Telem\xF3vel",
  formFieldLabel__role: "Fun\xE7\xE3o",
  formFieldLabel__signOutOfOtherSessions: "Desconectar de todos os outros dispositivos",
  formFieldLabel__username: "Nome de utilizador",
  impersonationFab: {
    action__signOut: "Terminar sess\xE3o",
    title: "Sess\xE3o iniciada como {{identifier}}"
  },
  maintenanceMode: "Modo de Manuten\xE7\xE3o",
  membershipRole__admin: "Administrador",
  membershipRole__basicMember: "Membro",
  membershipRole__guestMember: "Convidado",
  organizationList: {
    action__createOrganization: "Criar organiza\xE7\xE3o",
    action__invitationAccept: "Participar",
    action__suggestionsAccept: "Solicitar participa\xE7\xE3o",
    createOrganization: "Criar organiza\xE7\xE3o",
    invitationAcceptedLabel: "Participando",
    subtitle: "para continuar no {{applicationName}}",
    suggestionsAcceptedLabel: "Aprova\xE7\xE3o pendente",
    title: "Selecione uma conta",
    titleWithoutPersonal: "Selecione uma organiza\xE7\xE3o"
  },
  organizationProfile: {
    apiKeysPage: {
      title: void 0
    },
    badge__automaticInvitation: "Convites autom\xE1ticos",
    badge__automaticSuggestion: "Sugest\xF5es autom\xE1ticas",
    badge__manualInvitation: "Sem inscri\xE7\xE3o autom\xE1tica",
    badge__unverified: "N\xE3o verificado",
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Adicione o dom\xEDnio para verificar. Utilizadores com endere\xE7os de e-mail neste dom\xEDnio podem entrar na organiza\xE7\xE3o automaticamente ou solicitar entrada.",
      title: "Adicionar dom\xEDnio"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Os convites n\xE3o puderam ser enviados. Corrija o seguinte e tente novamente:",
      formButtonPrimary__continue: "Enviar convites",
      selectDropdown__role: "Select role",
      subtitle: "Convidar novos membros para esta organiza\xE7\xE3o",
      successMessage: "Convites enviados com sucesso",
      title: "Convidar membros"
    },
    membersPage: {
      action__invite: "Convidar",
      action__search: "Pesquisar",
      activeMembersTab: {
        menuAction__remove: "Remover membro",
        tableHeader__actions: "A\xE7\xF5es",
        tableHeader__joined: "Entrou",
        tableHeader__role: "Fun\xE7\xE3o",
        tableHeader__user: "Utilizador"
      },
      detailsTitle__emptyRow: "Nenhum membro para mostrar",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Convide utilizadores conectando um dom\xEDnio de e-mail com a sua organiza\xE7\xE3o. Qualquer pessoa que se inscrever com um dom\xEDnio de e-mail correspondente poder\xE1 se entrar na organiza\xE7\xE3o a qualquer momento.",
          headerTitle: "Convites autom\xE1ticos",
          primaryButton: "Configurar dom\xEDnios verificados"
        },
        table__emptyRow: "Nenhum convite a mostrar"
      },
      invitedMembersTab: {
        menuAction__revoke: "Cancelar convite",
        tableHeader__invited: "Convidado"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Utilizadores que se inscrevem com um dom\xEDnio de e-mail correspondente podem ver uma sugest\xE3o para solicitar participa\xE7\xE3o na sua organiza\xE7\xE3o.",
          headerTitle: "Sugest\xF5es autom\xE1ticas",
          primaryButton: "Configurar dom\xEDnios verificados"
        },
        menuAction__approve: "Aprovar",
        menuAction__reject: "Rejeitar",
        tableHeader__requested: "Acesso solicitado",
        table__emptyRow: "Nenhuma solicita\xE7\xE3o a mostrar"
      },
      start: {
        headerTitle__invitations: "Convites",
        headerTitle__members: "Membros",
        headerTitle__requests: "Pedidos"
      }
    },
    navbar: {
      apiKeys: void 0,
      billing: void 0,
      description: "Manage your organization.",
      general: "General",
      members: "Members",
      title: "Organization"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: "Escreva {{organizationName}} abaixo para continuar.",
          messageLine1: "Tem certeza de que deseja excluir esta organiza\xE7\xE3o?",
          messageLine2: "Esta a\xE7\xE3o \xE9 permanente e irrevers\xEDvel.",
          successMessage: "Voc\xEA excluiu a organiza\xE7\xE3o.",
          title: "Excluir organiza\xE7\xE3o"
        },
        leaveOrganization: {
          actionDescription: "Escreva {{organizationName}} abaixo para continuar.",
          messageLine1: "Tem certeza de que deseja sair desta organiza\xE7\xE3o? Voc\xEA perder\xE1 o acesso a esta organiza\xE7\xE3o e \xE0s suas aplica\xE7\xF5es.",
          messageLine2: "Esta a\xE7\xE3o \xE9 permanente e n\xE3o pode ser desfeita.",
          successMessage: "Voc\xEA saiu da organiza\xE7\xE3o.",
          title: "Sair da organiza\xE7\xE3o"
        },
        title: "Perigo"
      },
      domainSection: {
        menuAction__manage: "Manage",
        menuAction__remove: "Delete",
        menuAction__verify: "Verify",
        primaryButton: "Adicionar dom\xEDnio",
        subtitle: "Permita que os utilizadores juntem-se \xE0 organiza\xE7\xE3o automaticamente ou solicitem participa\xE7\xE3o com base num dom\xEDnio de e-mail verificado.",
        title: "Dom\xEDnios verificados"
      },
      successMessage: "A organiza\xE7\xE3o foi atualizada.",
      title: "Perfil da organiza\xE7\xE3o"
    },
    removeDomainPage: {
      messageLine1: "O dom\xEDnio de e-mail {{domain}} ser\xE1 removido.",
      messageLine2: "Os utilizadores n\xE3o conseguir\xE3o entrar na organiza\xE7\xE3o ap\xF3s isso.",
      successMessage: "{{domain}} foi removido.",
      title: "Excluir dom\xEDnio"
    },
    start: {
      headerTitle__general: "General",
      headerTitle__members: "Membros",
      profileSection: {
        primaryButton: "Guardar",
        title: "Organization Profile",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "A exclus\xE3o deste dom\xEDnio afetar\xE1 os utilizadores convidados.",
        removeDomainActionLabel__remove: "Excluir dom\xEDnio",
        removeDomainSubtitle: "Remova este dom\xEDnio dos seus dom\xEDnios verificados",
        removeDomainTitle: "Excluir dom\xEDnio"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Os utilizadores s\xE3o automaticamente convidados a entrar na organiza\xE7\xE3o quando se inscrevem.",
        automaticInvitationOption__label: "Convites autom\xE1ticos",
        automaticSuggestionOption__description: "Os utilizadores recebem uma sugest\xE3o para solicitar participa\xE7\xE3o, mas devem ser aprovados por um administrador antes de poderem entrar na organiza\xE7\xE3o.",
        automaticSuggestionOption__label: "Sugest\xF5es autom\xE1ticas",
        calloutInfoLabel: "Alterar o modo de inscri\xE7\xE3o afetar\xE1 apenas os novos utilizadores.",
        calloutInvitationCountLabel: "Convites pendentes enviados aos utilizadores: {{count}}",
        calloutSuggestionCountLabel: "Sugest\xF5es pendentes enviadas aos utilizadores: {{count}}",
        manualInvitationOption__description: "Os utilizadores s\xF3 podem ser convidados manualmente para a organiza\xE7\xE3o.",
        manualInvitationOption__label: "Sem inscri\xE7\xE3o autom\xE1tica",
        subtitle: "Escolha como os utilizadores deste dom\xEDnio se podem entrar na organiza\xE7\xE3o."
      },
      start: {
        headerTitle__danger: "Perigo",
        headerTitle__enrollment: "Op\xE7\xF5es de inscri\xE7\xE3o"
      },
      subtitle: "O dom\xEDnio {{domain}} agora est\xE1 verificado. Continue por selecionar o modo de inscri\xE7\xE3o.",
      title: "Update {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Insira o c\xF3digo de verifica\xE7\xE3o enviado para o seu endere\xE7o de e-mail",
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "N\xE3o recebeu um c\xF3digo? Reenviar",
      subtitle: "O dom\xEDnio {{domainName}} precisa ser verificado por e-mail.",
      subtitleVerificationCodeScreen: "Um c\xF3digo de verifica\xE7\xE3o foi enviado para {{emailAddress}}. Insira-o para continuar.",
      title: "Verificar dom\xEDnio"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Criar organiza\xE7\xE3o",
    action__invitationAccept: "Participar",
    action__manageOrganization: "Configurar organiza\xE7\xE3o",
    action__suggestionsAccept: "Solicitar participa\xE7\xE3o",
    notSelected: "Nenhuma organiza\xE7\xE3o selecionada",
    personalWorkspace: "Conta pessoal",
    suggestionsAcceptedLabel: "Aprova\xE7\xE3o pendente"
  },
  paginationButton__next: "Pr\xF3ximo",
  paginationButton__previous: "Anterior",
  paginationRowText__displaying: "Apresentando",
  paginationRowText__of: "de",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Add account",
      action__signOutAll: "Sign out of all accounts",
      subtitle: "Select the account with which you wish to continue.",
      title: "Choose an account"
    },
    alternativeMethods: {
      actionLink: "Ajuda",
      actionText: "Don\u2019t have any of these?",
      blockButton__backupCode: "Utilize um c\xF3digo de backup",
      blockButton__emailCode: "Enviar c\xF3digo para {{identifier}}",
      blockButton__emailLink: "Enviar link para {{identifier}}",
      blockButton__passkey: "Utilizar chave de acesso",
      blockButton__password: "Fazer login com palavra-passe",
      blockButton__phoneCode: "Enviar c\xF3digo para {{identifier}}",
      blockButton__totp: "Utilize o seu autenticador",
      getHelp: {
        blockButton__emailSupport: "E-mail de suporte",
        content: "Se estiver com dificuldades para entrar na sua conta, envie-nos um e-mail e iremos ajudar-te a restaurar o acesso o mais r\xE1pido poss\xEDvel.",
        title: "Ajuda"
      },
      subtitle: "Facing issues? You can use any of these methods to sign in.",
      title: "Utilize outro m\xE9todo"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "para continuar em {{applicationName}}",
      title: "Insira um c\xF3digo de backup"
    },
    emailCode: {
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "Reenviar c\xF3digo",
      subtitle: "para continuar em {{applicationName}}",
      title: "Verifique o seu e-mail"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "O cliente n\xE3o corresponde ao esperado. Tente novamente.",
        title: "Erro de cliente"
      },
      expired: {
        subtitle: "Retorne para a aba original para continuar",
        title: "Este link de verifica\xE7\xE3o expirou"
      },
      failed: {
        subtitle: "Retorne para a aba original para continuar",
        title: "Este link de verifica\xE7\xE3o \xE9 inv\xE1lido"
      },
      formSubtitle: "Utilize o link enviado no seu e-mail",
      formTitle: "Link de verifica\xE7\xE3o",
      loading: {
        subtitle: "Ser\xE1 redirecionado em breve",
        title: "Entrando..."
      },
      resendButton: "N\xE3o recebeu um link? Reenviar",
      subtitle: "para continuar em {{applicationName}}",
      title: "Verifique o seu e-mail",
      unusedTab: {
        title: "J\xE1 pode fechar esta aba"
      },
      verified: {
        subtitle: "Ser\xE1 redirecionado em breve",
        title: "Login realizado com sucesso"
      },
      verifiedSwitchTab: {
        subtitle: "Retorne para a aba original para continuar",
        subtitleNewTab: "Retorne para a nova aba que foi aberta para continuar",
        titleNewTab: "Conectado em outra aba"
      }
    },
    forgotPassword: {
      formTitle: "C\xF3digo de redefini\xE7\xE3o de palavra-passe",
      resendButton: "N\xE3o recebeu um c\xF3digo? Reenviar",
      subtitle: "to reset your password",
      subtitle_email: "First, enter the code sent to your email ID",
      subtitle_phone: "First, enter the code sent to your phone",
      title: "Reset password"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Repor a palavra-passe",
      label__alternativeMethods: "Ou, fa\xE7a login com outro m\xE9todo.",
      title: "Esqueceu-se da palavra-passe?"
    },
    noAvailableMethods: {
      message: "N\xE3o foi poss\xEDvel fazer login. N\xE3o h\xE1 nenhum m\xE9todo de autentica\xE7\xE3o dispon\xEDvel.",
      subtitle: "Ocorreu um erro",
      title: "N\xE3o foi poss\xEDvel fazer login"
    },
    passkey: {
      subtitle: "Utilize a sua chave de acesso para autentica\xE7\xE3o.",
      title: "Autentica\xE7\xE3o com Chave de Acesso"
    },
    password: {
      actionLink: "Utilize outro m\xE9todo",
      subtitle: "para continuar em {{applicationName}}",
      title: "Insira a sua palavra-passe"
    },
    passwordPwned: {
      title: "Este password foi comprometido em uma viola\xE7\xE3o de dados. Escolha outro por motivos de seguran\xE7a."
    },
    phoneCode: {
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "Reenviar c\xF3digo",
      subtitle: "para continuar em {{applicationName}}",
      title: "Verifique o seu telem\xF3vel"
    },
    phoneCodeMfa: {
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "Reenviar c\xF3digo",
      subtitle: "Insira o c\xF3digo enviado para o seu n\xFAmero de telefone",
      title: "Verifique o seu telem\xF3vel"
    },
    resetPassword: {
      formButtonPrimary: "Repor Palavra-passe",
      requiredMessage: "For security reasons, it is required to reset your password.",
      successMessage: "A sua palavra-passe foi alterada com sucesso. Entrando, por favor aguarde um momento.",
      title: "Repor Palavra-passe"
    },
    resetPasswordMfa: {
      detailsLabel: "Precisamos verificar a sua identidade antes de redefinir a palavra-passe."
    },
    start: {
      actionLink: "Registre-se",
      actionLink__join_waitlist: "Juntar-se \xE0 lista de espera",
      actionLink__use_email: "Usar e-mail",
      actionLink__use_email_username: "Usar e-mail ou nome de utilizador",
      actionLink__use_passkey: "Usar chave de acesso",
      actionLink__use_phone: "Usar telem\xF3vel",
      actionLink__use_username: "Usar nome de utilizador",
      actionText: "N\xE3o possui uma conta?",
      actionText__join_waitlist: "Ainda n\xE3o tem uma conta? Junte-se \xE0 lista de espera.",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "para continuar em {{applicationName}}",
      subtitleCombined: void 0,
      title: "Entrar",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      subtitle: "Insira o c\xF3digo de verifica\xE7\xE3o enviado para o seu dispositivo.",
      title: "Verifica\xE7\xE3o de duas etapas"
    }
  },
  signInEnterPasswordTitle: "Insira a sua palavra-passe",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Entrar",
      actionText: "J\xE1 possui uma conta?",
      subtitle: "para continuar em {{applicationName}}",
      title: "Preencha os campos ausentes"
    },
    emailCode: {
      formSubtitle: "Insira o c\xF3digo enviado para o seu e-mail",
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "N\xE3o recebeu o c\xF3digo? Reenviar",
      subtitle: "para continuar em {{applicationName}}",
      title: "Verifique o seu e-mail"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Parece que houve um erro com a sua sess\xE3o. Tente novamente.",
        title: "Erro de sess\xE3o"
      },
      formSubtitle: "Utilize o link enviado no seu e-mail",
      formTitle: "Link de verifica\xE7\xE3o",
      loading: {
        title: "Entrando..."
      },
      resendButton: "Reenviar link",
      subtitle: "para continuar em {{applicationName}}",
      title: "Verifique seu e-mail",
      verified: {
        title: "Registo realizado com sucesso"
      },
      verifiedSwitchTab: {
        subtitle: "Volte para a nova aba que foi aberta para continuar",
        subtitleNewTab: "Volte para a aba anterior para continuar",
        title: "E-mail verificado com sucesso"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: "Aceito a Pol\xEDtica de Privacidade",
        label__onlyTermsOfService: "Aceito os Termos de Servi\xE7o",
        label__termsOfServiceAndPrivacyPolicy: 'Aceito os {{ termsOfServiceLink || link("Termos de Servi\xE7o") }} e a {{ privacyPolicyLink || link("Pol\xEDtica de Privacidade") }}'
      },
      continue: {
        subtitle: "Ao continuar, voc\xEA concorda com os termos acima.",
        title: "Continuar"
      }
    },
    phoneCode: {
      formSubtitle: "Insira o c\xF3digo enviado para o seu telem\xF3vel",
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "N\xE3o recebeu o c\xF3digo? Reenviar",
      subtitle: "para continuar em {{applicationName}}",
      title: "Verifique o seu telem\xF3vel"
    },
    restrictedAccess: {
      actionLink: "Contactar suporte",
      actionText: "Precisa de ajuda?",
      blockButton__emailSupport: "Enviar e-mail para suporte",
      blockButton__joinWaitlist: "Junte-se \xE0 lista de espera",
      subtitle: "O seu acesso est\xE1 restrito. Para mais informa\xE7\xF5es, entre em contacto connosco.",
      subtitleWaitlist: "Estamos a aguardar a sua entrada na lista de espera. Agradecemos pela paci\xEAncia.",
      title: "Acesso Restrito"
    },
    start: {
      actionLink: "Entrar",
      actionLink__use_email: "Usar e-mail",
      actionLink__use_phone: "Usar telem\xF3vel",
      actionText: "J\xE1 tem uma conta?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "para continuar em {{applicationName}}",
      subtitleCombined: "para continuar em {{applicationName}}",
      title: "Criar a sua conta",
      titleCombined: "Criar a sua conta"
    }
  },
  socialButtonsBlockButton: "Continuar com {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: "J\xE1 \xE9 membro nesta organiza\xE7\xE3o.",
    captcha_invalid: "N\xE3o foi poss\xEDvel inscrever-se devido a falhas nas valida\xE7\xF5es de seguran\xE7a. Por favor, atualize a p\xE1gina para tentar novamente ou entre em contato com o suporte para obter mais ajuda.",
    captcha_unavailable: "Inscri\xE7\xE3o mal-sucedida devido a falha na valida\xE7\xE3o de bot. Por favor, atualize a p\xE1gina para tentar novamente ou entre em contato com o suporte para obter mais ajuda.",
    form_code_incorrect: "C\xF3digo incorreto.",
    form_identifier_exists__email_address: "O endere\xE7o de e-mail j\xE1 est\xE1 em uso.",
    form_identifier_exists__phone_number: "O n\xFAmero de telem\xF3vel j\xE1 est\xE1 em uso.",
    form_identifier_exists__username: "O nome de utilizador j\xE1 est\xE1 em uso.",
    form_identifier_not_found: "N\xE3o foi poss\xEDvel encontrar uma conta com esses detalhes.",
    form_param_format_invalid: "Formato de par\xE2metro inv\xE1lido.",
    form_param_format_invalid__email_address: "O endere\xE7o de e-mail deve ser v\xE1lido.",
    form_param_format_invalid__phone_number: "O n\xFAmero de telem\xF3vel deve ser v\xE1lido.",
    form_param_max_length_exceeded__first_name: "O primeiro nome n\xE3o deve exceder 256 caracteres.",
    form_param_max_length_exceeded__last_name: "O apelido n\xE3o deve exceder 256 caracteres.",
    form_param_max_length_exceeded__name: "O nome n\xE3o deve exceder 256 caracteres.",
    form_param_nil: "Par\xE2metro n\xE3o pode ser nulo.",
    form_param_value_invalid: "Valor de par\xE2metro inv\xE1lido.",
    form_password_incorrect: "Palavra-passe incorreta.",
    form_password_length_too_short: "A palavra-passe \xE9 muito curta.",
    form_password_not_strong_enough: "A sua palavra-passe n\xE3o \xE9 forte o suficiente.",
    form_password_pwned: "Esta palavra-passe foi encontrada como parte de uma viola\xE7\xE3o e n\xE3o pode ser usada, por favor, tente outra palavra-passe.",
    form_password_pwned__sign_in: "Esta palavra-passe foi encontrada como parte de uma viola\xE7\xE3o e n\xE3o pode ser utilizada para login. Por favor, escolha outra.",
    form_password_size_in_bytes_exceeded: "A sua palavra-passe excedeu o n\xFAmero m\xE1ximo de bytes permitidos, por favor, encurte-a ou remova alguns caracteres especiais.",
    form_password_validation_failed: "Falha na valida\xE7\xE3o da palavra-passe.",
    form_username_invalid_character: "O nome de utilizador cont\xE9m caracteres inv\xE1lidos.",
    form_username_invalid_length: "O nome de utilizador deve ter entre 3 e 50 caracteres.",
    identification_deletion_failed: "Voc\xEA n\xE3o pode excluir a sua \xFAltima identifica\xE7\xE3o.",
    not_allowed_access: "O endere\xE7o de e-mail ou n\xFAmero de telefone n\xE3o \xE9 permitido para registro. Isso pode ser devido ao uso de '+', '=', '#' ou '.' no endere\xE7o de e-mail, o uso de um dom\xEDnio associado a um servi\xE7o de e-mail tempor\xE1rio ou uma exclus\xE3o expl\xEDcita.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: void 0,
    passkey_not_supported: void 0,
    passkey_pa_not_supported: void 0,
    passkey_registration_cancelled: void 0,
    passkey_retrieval_cancelled: void 0,
    passwordComplexity: {
      maximumLength: "menos de {{length}} caracteres",
      minimumLength: "{{length}} ou mais caracteres",
      requireLowercase: "uma letra min\xFAscula",
      requireNumbers: "um n\xFAmero",
      requireSpecialCharacter: "um caractere especial",
      requireUppercase: "uma letra mai\xFAscula",
      sentencePrefix: "A sua palavra-passe deve conter"
    },
    phone_number_exists: "Este n\xFAmero de telem\xF3vel j\xE1 est\xE1 em uso. Por favor, tente outro.",
    session_exists: "J\xE1 est\xE1 conectado.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "A sua palavra-passe funciona, mas poderia ser mais forte. Tente adicionar mais caracteres.",
      goodPassword: "A sua palavra-passe atende a todos os requisitos necess\xE1rios.",
      notEnough: "A sua palavra-passe n\xE3o \xE9 forte o suficiente.",
      suggestions: {
        allUppercase: "Utilize apenas algumas letras mai\xFAsculas, n\xE3o todas.",
        anotherWord: "Adicione palavras menos comuns.",
        associatedYears: "Evite anos associados a voc\xEA.",
        capitalization: "Utilize outras letras mai\xFAsculas, al\xE9m do que primeira.",
        dates: "Evite datas e anos associados a voc\xEA.",
        l33t: "Evite substitui\xE7\xF5es previs\xEDveis de letras, como '@' por 'a'.",
        longerKeyboardPattern: "Use padr\xF5es de teclado mais longos e mude a dire\xE7\xE3o da digita\xE7\xE3o v\xE1rias vezes.",
        noNeed: "Voc\xEA pode criar palavras-passes fortes sem usar s\xEDmbolos, n\xFAmeros ou letras mai\xFAsculas.",
        pwned: "Se usar esta palavra-passe noutro lugar, deve mud\xE1-la.",
        recentYears: "Evite anos recentes.",
        repeated: "Evite palavras e caracteres repetidos.",
        reverseWords: 'Evite utilizar palavras comuns escritas de "tr\xE1s para frente".',
        sequences: "Evite sequ\xEAncias comuns de caracteres.",
        useWords: "Use v\xE1rias palavras, mas evite frases comuns."
      },
      warnings: {
        common: "Esta \xE9 uma palavra-passe comumente usada.",
        commonNames: "Nomes e apelidos comuns s\xE3o f\xE1ceis de adivinhar.",
        dates: "Datas s\xE3o f\xE1ceis de adivinhar.",
        extendedRepeat: 'Padr\xF5es de caracteres repetidos, como "abcabcabc" s\xE3o f\xE1ceis de adivinhar.',
        keyPattern: "Padr\xF5es curtos de teclado s\xE3o f\xE1ceis de adivinhar.",
        namesByThemselves: "Nomes ou apelidos s\xE3o f\xE1ceis de adivinhar.",
        pwned: "A sua palavra-passe foi exposta numa viola\xE7\xE3o de dados na Internet.",
        recentYears: "Anos recentes s\xE3o f\xE1ceis de adivinhar.",
        sequences: 'Sequ\xEAncias comuns de caracteres, como "abc" s\xE3o f\xE1ceis de adivinhar.',
        similarToCommon: "Esta \xE9 semelhante a uma palavra-passe comumente usada.",
        simpleRepeat: 'Caracteres repetidos, como "aaa" s\xE3o f\xE1ceis de adivinhar.',
        straightRow: "Letras que v\xEAm em sequ\xEAncia no teclado s\xE3o f\xE1ceis de adivinhar.",
        topHundred: "Esta \xE9 uma palavra-passe usada frequentemente.",
        topTen: "Esta \xE9 uma palavra-passe muito usada.",
        userInputs: "N\xE3o deve haver nenhum dado pessoal ou relacionado \xE0 p\xE1gina.",
        wordByItself: "Palavras simples s\xE3o f\xE1ceis de adivinhar."
      }
    }
  },
  userButton: {
    action__addAccount: "Adicionar conta",
    action__manageAccount: "Configurar conta",
    action__signOut: "Terminar sess\xE3o",
    action__signOutAll: "Terminar sess\xE3o de todas as contas"
  },
  userProfile: {
    apiKeysPage: {
      title: void 0
    },
    backupCodePage: {
      actionLabel__copied: "Copiado!",
      actionLabel__copy: "Copiar tudo",
      actionLabel__download: "Download .txt",
      actionLabel__print: "Imprimir",
      infoText1: "C\xF3digos de backup ser\xE3o ativados para esta conta.",
      infoText2: "Guarde-os em seguran\xE7a e n\xE3o os partilhe. Voc\xEA pode gerar novos c\xF3digos de backup se suspeitar que eles tenham sido comprometidos.",
      subtitle__codelist: "Guarde-os em seguran\xE7a e n\xE3o os partilhe.",
      successMessage: "C\xF3digos de backup foram ativados para esta conta. Pode usar um deles para fazer login na sua conta caso perca o acesso ao seu dispositivo de autentica\xE7\xE3o. Cada c\xF3digo poder\xE1 ser utilizado apenas uma vez.",
      successSubtitle: "Pode usar um deles para fazer login na sua conta caso perca o acesso ao seu dispositivo de autentica\xE7\xE3o.",
      title: "Adicionar c\xF3digo de backup para verifica\xE7\xE3o",
      title__codelist: "C\xF3digos de backup"
    },
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Selecione um provedor para conectar \xE0 sua conta.",
      formHint__noAccounts: "N\xE3o h\xE1 provedores de conta externos dispon\xEDveis.",
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 removido desta conta.",
        messageLine2: "N\xE3o vai conseguir usar esta conta e, quaisquer recursos dependentes dela deixar\xE3o de funcionar.",
        successMessage: "{{connectedAccount}} foi removido da sua conta.",
        title: "Remover conta conectada"
      },
      socialButtonsBlockButton: "Conectar conta {{provider|titleize}}",
      successMessage: "O provedor foi adicionado \xE0 sua conta",
      title: "Conecte uma conta"
    },
    deletePage: {
      actionDescription: "Escreva Excluir conta abaixo para continuar.",
      confirm: "Excluir conta",
      messageLine1: "Tem certeza de que deseja excluir a sua conta?",
      messageLine2: "Esta a\xE7\xE3o \xE9 permanente e irrevers\xEDvel.",
      title: "Excluir conta"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "Um e-mail contendo um c\xF3digo de verifica\xE7\xE3o ser\xE1 enviado para este endere\xE7o de e-mail.",
        formSubtitle: "Insira o c\xF3digo de verifica\xE7\xE3o enviado para {{identifier}}",
        formTitle: "C\xF3digo de verifica\xE7\xE3o",
        resendButton: "N\xE3o recebeu um c\xF3digo? Reenviar",
        successMessage: "O e-mail {{identifier}} foi adicionado \xE0 sua conta."
      },
      emailLink: {
        formHint: "Um e-mail contendo um link de verifica\xE7\xE3o ser\xE1 enviado para este endere\xE7o de e-mail.",
        formSubtitle: "Clique no link de verifica\xE7\xE3o enviado para {{identifier}}",
        formTitle: "Link de verifica\xE7\xE3o",
        resendButton: "N\xE3o recebeu um c\xF3digo? Reenviar",
        successMessage: "O e-mail {{identifier}} foi adicionado \xE0 sua conta."
      },
      enterpriseSSOLink: {
        formButton: "Clique para autenticar",
        formSubtitle: "Complete a autentica\xE7\xE3o com {{identifier}}"
      },
      formHint: "Voc\xEA precisar\xE1 verificar este endere\xE7o de email antes de poder adicion\xE1-lo \xE0 sua conta.",
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 removido desta conta.",
        messageLine2: "N\xE3o vai conseguir fazer login novamente com este endere\xE7o de e-mail.",
        successMessage: "{{emailAddress}} foi removido da sua conta.",
        title: "Remover e-mail"
      },
      title: "Adicionar e-mail",
      verifyTitle: "Verify email address"
    },
    formButtonPrimary__add: "Add",
    formButtonPrimary__continue: "Continuar",
    formButtonPrimary__finish: "Finalizar",
    formButtonPrimary__remove: "Remove",
    formButtonPrimary__save: "Save",
    formButtonReset: "Cancelar",
    mfaPage: {
      formHint: "Selecione um m\xE9todo para adicionar.",
      title: "Adicione verifica\xE7\xE3o de duas etapas"
    },
    mfaPhoneCodePage: {
      backButton: "Use existing number",
      primaryButton__addPhoneNumber: "Adicione um n\xFAmero de telem\xF3vel",
      removeResource: {
        messageLine1: "{{identifier}} n\xE3o receber\xE1 mais c\xF3digos de verifica\xE7\xE3o ao realizar o login.",
        messageLine2: "A sua conta pode ficar menos segura. Tem certeza que deseja continuar?",
        successMessage: "C\xF3digo SMS de verifica\xE7\xE3o de duas etapas foi removido para {{mfaPhoneCode}}",
        title: "Remover verifica\xE7\xE3o de duas etapas"
      },
      subtitle__availablePhoneNumbers: "Selecione um n\xFAmero de telem\xF3vel para registrar a verifica\xE7\xE3o de duas etapas por c\xF3digo SMS.",
      subtitle__unavailablePhoneNumbers: "N\xE3o h\xE1 n\xFAmeros de telem\xF3vel dispon\xEDveis para registrar a verifica\xE7\xE3o de duas etapas por c\xF3digo SMS.",
      successMessage1: "When signing in, you will need to enter a verification code sent to this phone number as an additional step.",
      successMessage2: "Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.",
      successTitle: "SMS code verification enabled",
      title: "Adicionar verifica\xE7\xE3o por SMS"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Ler c\xF3digo QR em vez disso",
        buttonUnableToScan__nonPrimary: "N\xE3o pode ler o c\xF3digo QR?",
        infoText__ableToScan: "Configure um novo m\xE9todo de login no seu autenticador e leia o seguinte c\xF3digo QR para vincul\xE1-lo \xE0 sua conta.",
        infoText__unableToScan: "Configure um novo m\xE9todo de login no seu autenticador e insira a chave informada abaixo.",
        inputLabel__unableToScan1: "Certifique-se de que o 'One-time passwords' est\xE1 ativo, de seguida, conclua a conex\xE3o da sua conta.",
        inputLabel__unableToScan2: "Alternativamente, se o seu autenticador suportar URIs TOTP, tamb\xE9m pode copiar a URI completa."
      },
      removeResource: {
        messageLine1: "Os c\xF3digos de verifica\xE7\xE3o deste autenticador n\xE3o ser\xE3o mais necess\xE1rios ao fazer login.",
        messageLine2: "A sua conta pode ficar menos segura. Tem certeza que deseja continuar?",
        successMessage: "A verifica\xE7\xE3o de duas etapas via autenticador foi removida.",
        title: "Remover verifica\xE7\xE3o de duas etapas"
      },
      successMessage: "A verifica\xE7\xE3o de duas etapas est\xE1 agora ativa. Ao fazer login, precisar\xE1 de inserir um c\xF3digo de verifica\xE7\xE3o deste autenticador como uma etapa adicional.",
      title: "Adicionar um autenticador",
      verifySubtitle: "Insira o c\xF3digo de verifica\xE7\xE3o gerado pelo seu autenticador",
      verifyTitle: "C\xF3digo de verifica\xE7\xE3o"
    },
    mobileButton__menu: "Menu",
    navbar: {
      account: "Profile",
      apiKeys: void 0,
      billing: void 0,
      description: "Manage your account info.",
      security: "Security",
      title: "Account"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "Tem a certeza de que deseja remover este recurso?",
        title: "Remover Recurso"
      },
      subtitle__rename: "Altere o nome do recurso, se necess\xE1rio.",
      title__rename: "Renomear Recurso"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "It is recommended to sign out of all other devices which may have used your old password.",
      readonly: "A sua palavra-passe n\xE3o pode ser editada porque s\xF3 pode fazer login por meio da conex\xE3o da empresa.",
      successMessage__set: "A sua palavra-passe foi guardada.",
      successMessage__signOutOfOtherSessions: "Todos os outros dispositivos foram desconectados.",
      successMessage__update: "A sua palavra-passe foi atualizada.",
      title__set: "Defina a palavra-passe",
      title__update: "Trocar palavra-passe"
    },
    phoneNumberPage: {
      infoText: "Um SMS contendo um link de verifica\xE7\xE3o ser\xE1 enviado para este telem\xF3vel.",
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 removido desta conta.",
        messageLine2: "N\xE3o vai conseguir fazer login novamente com este n\xFAmero de telem\xF3vel.",
        successMessage: "{{phoneNumber}} foi removido da sua conta.",
        title: "Remover telem\xF3vel"
      },
      successMessage: "{{identifier}} foi adicionado \xE0 sua conta.",
      title: "Adicionar telem\xF3vel",
      verifySubtitle: "Enter the verification code sent to {{identifier}}",
      verifyTitle: "Verify phone number"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "Carregue uma imagem JPG, PNG, GIF ou WEBP menor que 10MB",
      imageFormDestructiveActionSubtitle: "Remover imagem",
      imageFormSubtitle: "Carregar imagem",
      imageFormTitle: "Imagem de perfil",
      readonly: "As informa\xE7\xF5es do perfil foram fornecidas pela conex\xE3o corporativa e n\xE3o podem ser editadas.",
      successMessage: "O perfil foi atualizado.",
      title: "Atualizar perfil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Terminar sess\xE3o",
        title: "Dispositivos ativos"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Tentar novamente",
        actionLabel__reauthorize: "Reautorizar agora",
        destructiveActionTitle: "Remover",
        primaryButton: "Conectar conta",
        subtitle__disconnected: "A conta foi desconectada. Clique abaixo para conectar novamente.",
        subtitle__reauthorize: "The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues",
        title: "Contas conectadas"
      },
      dangerSection: {
        deleteAccountButton: "Excluir Conta",
        title: "Perigo"
      },
      emailAddressesSection: {
        destructiveAction: "Remover e-mail",
        detailsAction__nonPrimary: "Definir como principal",
        detailsAction__primary: "Completar verifica\xE7\xE3o",
        detailsAction__unverified: "Completar verifica\xE7\xE3o",
        primaryButton: "Adicionar um e-mail",
        title: "Endere\xE7os de e-mail"
      },
      enterpriseAccountsSection: {
        title: "Contas corporativas"
      },
      headerTitle__account: "Conta",
      headerTitle__security: "Seguran\xE7a",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Gerar novos c\xF3digos",
          headerTitle: "C\xF3digos de backup",
          subtitle__regenerate: "Obter um novo conjunto de c\xF3digos de backup seguros. Os c\xF3digos de backup anteriores ser\xE3o exclu\xEDdos e n\xE3o poder\xE3o ser utilizados novamente.",
          title__regenerate: "Gerar novos c\xF3digos de backup"
        },
        phoneCode: {
          actionLabel__setDefault: "Definir como principal",
          destructiveActionLabel: "Remover telem\xF3vel"
        },
        primaryButton: "Adicione verifica\xE7\xE3o",
        title: "Verifica\xE7\xE3o de duas etapas",
        totp: {
          destructiveActionTitle: "Remover",
          headerTitle: "Autenticador"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Remover chave de acesso",
        menuAction__rename: "Renomear chave de acesso",
        primaryButton: void 0,
        title: "Chaves de Acesso"
      },
      passwordSection: {
        primaryButton__setPassword: void 0,
        primaryButton__updatePassword: void 0,
        title: void 0
      },
      phoneNumbersSection: {
        destructiveAction: "Remover telem\xF3vel",
        detailsAction__nonPrimary: "Definir como principal",
        detailsAction__primary: "Completar verifica\xE7\xE3o",
        detailsAction__unverified: "Completar verifica\xE7\xE3o",
        primaryButton: "Adicione um telem\xF3vel",
        title: "N\xFAmeros de telem\xF3vel"
      },
      profileSection: {
        primaryButton: "Salvar altera\xE7\xF5es",
        title: "Perfil"
      },
      usernameSection: {
        primaryButton__setUsername: "Definir nome de utilizador",
        primaryButton__updateUsername: "Trocar nome de utilizador",
        title: "Nome de utilizador"
      },
      web3WalletsSection: {
        destructiveAction: "Remover carteira",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Carteiras Web3",
        title: "Carteiras Web3"
      }
    },
    usernamePage: {
      successMessage: "O nome de utilizador foi atualizado.",
      title__set: "Atualizar nome de utilizador",
      title__update: "Atualizar nome de utilizador"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 removido desta conta.",
        messageLine2: "N\xE3o vai conseguir usar esta carteira Web3 para entrar na sua conta.",
        successMessage: "{{Web3Wallet}} foi removido da sua conta.",
        title: "Remover carteira Web3"
      },
      subtitle__availableWallets: "Selecione uma carteira Web3 para conectar \xE0 sua conta.",
      subtitle__unavailableWallets: "N\xE3o h\xE1 carteiras Web3 dispon\xEDveis.",
      successMessage: "A carteira foi adicionada \xE0 sua conta.",
      title: "Adicionar carteira Web3",
      web3WalletButtonsBlockButton: "Conectar carteira Web3"
    }
  },
  waitlist: {
    start: {
      actionLink: "Junte-se \xE0 lista de espera",
      actionText: "Ainda n\xE3o tem uma conta? Junte-se \xE0 lista de espera.",
      formButton: "Submeter",
      subtitle: "Deixe os seus dados para se juntar \xE0 lista de espera.",
      title: "Juntar-se \xE0 lista de espera"
    },
    success: {
      message: "Obrigado por se inscrever! Voc\xEA ser\xE1 notificado assim que tiver acesso.",
      subtitle: "Aguarde enquanto processamos o seu pedido.",
      title: "Inscri\xE7\xE3o bem-sucedida na lista de espera"
    }
  }
};
export {
  ptPT
};
//# sourceMappingURL=pt-PT.mjs.map