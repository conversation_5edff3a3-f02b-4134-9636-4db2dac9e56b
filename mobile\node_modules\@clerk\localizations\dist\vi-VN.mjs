// src/vi-VN.ts
var viVN = {
  locale: "vi-VN",
  apiKeys: {
    action__add: "Th\xEAm kho\xE1 m\u1EDBi",
    action__search: "T\xECm ki\u1EBFm kho\xE1",
    createdAndExpirationStatus__expiresOn: "T\u1EA1o {{ createdDate | shortDate('vi-VN') }} \u2022 H\u1EBFt h\u1EA1n {{ expiresDate | longDate('vi-VN') }}",
    createdAndExpirationStatus__never: "T\u1EA1o {{ createdDate | shortDate('vi-VN') }} \u2022 Kh\xF4ng h\u1EBFt h\u1EA1n",
    detailsTitle__emptyRow: "Kh\xF4ng t\xECm th\u1EA5y kho\xE1 API",
    formButtonPrimary__add: "T\u1EA1o kho\xE1",
    formFieldCaption__expiration__expiresOn: "H\u1EBFt h\u1EA1n {{ date }}",
    formFieldCaption__expiration__never: "Kho\xE1 n\xE0y s\u1EBD kh\xF4ng h\u1EBFt h\u1EA1n",
    formFieldOption__expiration__180d: "180 Ng\xE0y",
    formFieldOption__expiration__1d: "1 Ng\xE0y",
    formFieldOption__expiration__1y: "1 N\u0103m",
    formFieldOption__expiration__30d: "30 Ng\xE0y",
    formFieldOption__expiration__60d: "60 Ng\xE0y",
    formFieldOption__expiration__7d: "7 Ng\xE0y",
    formFieldOption__expiration__90d: "90 Ng\xE0y",
    formFieldOption__expiration__never: "Kh\xF4ng h\u1EBFt h\u1EA1n",
    formHint: "Nh\u1EADp t\xEAn \u0111\u1EC3 t\u1EA1o kho\xE1 m\u1EDBi. B\u1EA1n s\u1EBD c\xF3 th\u1EC3 h\u1EE7y b\u1ECF b\u1EA5t k\u1EF3 l\xFAc n\xE0o.",
    formTitle: "Th\xEAm kho\xE1 API m\u1EDBi",
    lastUsed__days: "{{days}} ng\xE0y tr\u01B0\u1EDBc",
    lastUsed__hours: "{{hours}} gi\u1EDD tr\u01B0\u1EDBc",
    lastUsed__minutes: "{{minutes}} ph\xFAt tr\u01B0\u1EDBc",
    lastUsed__months: "{{months}} th\xE1ng tr\u01B0\u1EDBc",
    lastUsed__seconds: "{{seconds}} gi\xE2y tr\u01B0\u1EDBc",
    lastUsed__years: "{{years}} n\u0103m tr\u01B0\u1EDBc",
    menuAction__revoke: "H\u1EE7y kho\xE1",
    revokeConfirmation: {
      confirmationText: "H\u1EE7y",
      formButtonPrimary__revoke: "H\u1EE7y kho\xE1",
      formHint: "B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n x\xF3a kho\xE1 n\xE0y kh\xF4ng?",
      formTitle: 'H\u1EE7y kho\xE1 "{{apiKeyName}}" kh\xF4ng?'
    }
  },
  backButton: "Quay l\u1EA1i",
  badge__activePlan: "\u0110ang ho\u1EA1t \u0111\u1ED9ng",
  badge__canceledEndsAt: "\u0110\xE3 h\u1EE7y \u2022 K\u1EBFt th\xFAc {{ date | shortDate('vi-VN') }}",
  badge__currentPlan: "G\xF3i hi\u1EC7n t\u1EA1i",
  badge__default: "M\u1EB7c \u0111\u1ECBnh",
  badge__endsAt: "K\u1EBFt th\xFAc {{ date | shortDate('vi-VN') }}",
  badge__expired: "\u0110\xE3 h\u1EBFt h\u1EA1n",
  badge__otherImpersonatorDevice: "Thi\u1EBFt b\u1ECB gi\u1EA3 m\u1EA1o kh\xE1c",
  badge__primary: "Ch\xEDnh",
  badge__renewsAt: "Gia h\u1EA1n {{ date | shortDate('vi-VN') }}",
  badge__requiresAction: "Y\xEAu c\u1EA7u h\xE0nh \u0111\u1ED9ng",
  badge__startsAt: "B\u1EAFt \u0111\u1EA7u {{ date | shortDate('vi-VN') }}",
  badge__thisDevice: "Thi\u1EBFt b\u1ECB n\xE0y",
  badge__unverified: "Ch\u01B0a x\xE1c minh",
  badge__upcomingPlan: "S\u1EAFp t\u1EDBi",
  badge__userDevice: "Thi\u1EBFt b\u1ECB ng\u01B0\u1EDDi d\xF9ng",
  badge__you: "B\u1EA1n",
  commerce: {
    addPaymentMethod: "Th\xEAm ph\u01B0\u01A1ng th\u1EE9c thanh to\xE1n",
    alwaysFree: "Mi\u1EC5n ph\xED m\xE3i m\xE3i",
    annually: "H\xE0ng n\u0103m",
    availableFeatures: "T\xEDnh n\u0103ng c\xF3 s\u1EB5n",
    billedAnnually: "T\xEDnh ph\xED h\xE0ng n\u0103m",
    billedMonthlyOnly: "Ch\u1EC9 t\xEDnh ph\xED h\xE0ng th\xE1ng",
    cancelSubscription: "H\u1EE7y \u0111\u0103ng k\xFD",
    cancelSubscriptionAccessUntil: "B\u1EA1n c\xF3 th\u1EC3 ti\u1EBFp t\u1EE5c s\u1EED d\u1EE5ng t\xEDnh n\u0103ng '{{plan}}' cho \u0111\u1EBFn {{ date | longDate('vi-VN') }}, sau \u0111\xF3 b\u1EA1n s\u1EBD kh\xF4ng c\xF2n quy\u1EC1n truy c\u1EADp.",
    cancelSubscriptionNoCharge: "B\u1EA1n s\u1EBD kh\xF4ng b\u1ECB t\xEDnh ph\xED cho \u0111\u0103ng k\xFD n\xE0y.",
    cancelSubscriptionTitle: "H\u1EE7y \u0111\u0103ng k\xFD {{plan}}?",
    cannotSubscribeMonthly: "B\u1EA1n kh\xF4ng th\u1EC3 \u0111\u0103ng k\xFD g\xF3i n\xE0y b\u1EB1ng c\xE1ch thanh to\xE1n h\xE0ng th\xE1ng. \u0110\u1EC3 \u0111\u0103ng k\xFD g\xF3i n\xE0y, b\u1EA1n c\u1EA7n ch\u1ECDn thanh to\xE1n h\xE0ng n\u0103m.",
    checkout: {
      description__paymentSuccessful: "Thanh to\xE1n c\u1EE7a b\u1EA1n \u0111\xE3 th\xE0nh c\xF4ng.",
      description__subscriptionSuccessful: "\u0110\u0103ng k\xFD m\u1EDBi c\u1EE7a b\u1EA1n \u0111\xE3 \u0111\u01B0\u1EE3c thi\u1EBFt l\u1EADp.",
      downgradeNotice: "B\u1EA1n s\u1EBD gi\u1EEF \u0111\u0103ng k\xFD hi\u1EC7n t\u1EA1i v\xE0 c\xE1c t\xEDnh n\u0103ng c\u1EE7a n\xF3 cho \u0111\u1EBFn cu\u1ED1i chu k\u1EF3 thanh to\xE1n, sau \u0111\xF3 b\u1EA1n s\u1EBD \u0111\u01B0\u1EE3c chuy\u1EC3n sang \u0111\u0103ng k\xFD n\xE0y.",
      emailForm: {
        subtitle: "Tr\u01B0\u1EDBc khi b\u1EA1n c\xF3 th\u1EC3 ho\xE0n th\xE0nh vi\u1EC7c mua h\xE0ng, b\u1EA1n ph\u1EA3i th\xEAm \u0111\u1ECBa ch\u1EC9 email n\u01A1i g\u1EEDi h\xF3a \u0111\u01A1n.",
        title: "Th\xEAm \u0111\u1ECBa ch\u1EC9 email"
      },
      lineItems: {
        title__paymentMethod: "Ph\u01B0\u01A1ng th\u1EE9c thanh to\xE1n",
        title__statementId: "ID h\xF3a \u0111\u01A1n",
        title__subscriptionBegins: "\u0110\u0103ng k\xFD b\u1EAFt \u0111\u1EA7u",
        title__totalPaid: "T\u1ED5ng thanh to\xE1n"
      },
      pastDueNotice: "\u0110\u0103ng k\xFD tr\u01B0\u1EDBc c\u1EE7a b\u1EA1n \u0111\xE3 qu\xE1 h\u1EA1n v\xE0 ch\u01B0a thanh to\xE1n.",
      perMonth: "h\xE0ng th\xE1ng",
      title: "Thanh to\xE1n",
      title__paymentSuccessful: "Thanh to\xE1n th\xE0nh c\xF4ng!",
      title__subscriptionSuccessful: "Th\xE0nh c\xF4ng!"
    },
    credit: "T\xEDn d\u1EE5ng",
    creditRemainder: "T\xEDn d\u1EE5ng cho ph\u1EA7n c\xF2n l\u1EA1i c\u1EE7a \u0111\u0103ng k\xFD hi\u1EC7n t\u1EA1i.",
    defaultFreePlanActive: "B\u1EA1n hi\u1EC7n \u0111ang tr\xEAn g\xF3i Mi\u1EC5n ph\xED",
    free: "Mi\u1EC5n ph\xED",
    getStarted: "B\u1EAFt \u0111\u1EA7u",
    keepSubscription: "Gi\u1EEF \u0111\u0103ng k\xFD",
    manage: "Qu\u1EA3n l\xFD",
    manageSubscription: "Qu\u1EA3n l\xFD \u0111\u0103ng k\xFD",
    month: "Th\xE1ng",
    monthly: "H\xE0ng th\xE1ng",
    pastDue: "Qu\xE1 h\u1EA1n",
    pay: "Thanh to\xE1n {{amount}}",
    paymentMethods: "Ph\u01B0\u01A1ng th\u1EE9c thanh to\xE1n",
    paymentSource: {
      applePayDescription: {
        annual: "Thanh to\xE1n h\xE0ng n\u0103m",
        monthly: "Thanh to\xE1n h\xE0ng th\xE1ng"
      },
      dev: {
        anyNumbers: "B\u1EA5t k\u1EF3 s\u1ED1 n\xE0o",
        cardNumber: "S\u1ED1 th\u1EBB",
        cvcZip: "CVC, ZIP",
        developmentMode: "Ch\u1EBF \u0111\u1ED9 ph\xE1t tri\u1EC3n",
        expirationDate: "Ng\xE0y h\u1EBFt h\u1EA1n",
        testCardInfo: "Th\xF4ng tin th\u1EED nghi\u1EC7m"
      }
    },
    popular: "Ph\u1ED5 bi\u1EBFn",
    pricingTable: {
      billingCycle: "Chu k\u1EF3 thanh to\xE1n",
      included: "Bao g\u1ED3m"
    },
    reSubscribe: "\u0110\u0103ng k\xFD l\u1EA1i",
    seeAllFeatures: "Xem t\u1EA5t c\u1EA3 t\xEDnh n\u0103ng",
    subscribe: "\u0110\u0103ng k\xFD",
    subtotal: "T\u1ED5ng c\u1ED9ng",
    switchPlan: "Chuy\u1EC3n sang g\xF3i n\xE0y",
    switchToAnnual: "Chuy\u1EC3n sang h\xE0ng n\u0103m",
    switchToMonthly: "Chuy\u1EC3n sang h\xE0ng th\xE1ng",
    totalDue: "T\u1ED5ng c\u1EA7n thanh to\xE1n",
    totalDueToday: "T\u1ED5ng c\u1EA7n thanh to\xE1n h\xF4m nay",
    viewFeatures: "Xem t\xEDnh n\u0103ng",
    year: "N\u0103m"
  },
  createOrganization: {
    formButtonSubmit: "T\u1EA1o t\u1ED5 ch\u1EE9c",
    invitePage: {
      formButtonReset: "B\u1ECF qua"
    },
    title: "T\u1EA1o t\u1ED5 ch\u1EE9c"
  },
  dates: {
    lastDay: "H\xF4m qua l\xFAc {{ date | timeString('vi-VN') }}",
    next6Days: "{{ date | weekday('vi-VN','long') }} l\xFAc {{ date | timeString('vi-VN') }}",
    nextDay: "Ng\xE0y mai l\xFAc {{ date | timeString('vi-VN') }}",
    numeric: "{{ date | numeric('vi-VN') }}",
    previous6Days: "{{ date | weekday('vi-VN','long') }} l\xFAc {{ date | timeString('vi-VN') }}",
    sameDay: "H\xF4m nay l\xFAc {{ date | timeString('vi-VN') }}"
  },
  dividerText: "ho\u1EB7c",
  footerActionLink__alternativePhoneCodeProvider: "G\u1EEDi m\xE3 qua SMS thay v\xEC email",
  footerActionLink__useAnotherMethod: "S\u1EED d\u1EE5ng ph\u01B0\u01A1ng th\u1EE9c kh\xE1c",
  footerPageLink__help: "Tr\u1EE3 gi\xFAp",
  footerPageLink__privacy: "Quy\u1EC1n ri\xEAng t\u01B0",
  footerPageLink__terms: "\u0110i\u1EC1u kho\u1EA3n",
  formButtonPrimary: "Ti\u1EBFp t\u1EE5c",
  formButtonPrimary__verify: "X\xE1c minh",
  formFieldAction__forgotPassword: "Qu\xEAn m\u1EADt kh\u1EA9u?",
  formFieldError__matchingPasswords: "M\u1EADt kh\u1EA9u tr\xF9ng kh\u1EDBp.",
  formFieldError__notMatchingPasswords: "M\u1EADt kh\u1EA9u kh\xF4ng tr\xF9ng kh\u1EDBp.",
  formFieldError__verificationLinkExpired: "Li\xEAn k\u1EBFt x\xE1c minh \u0111\xE3 h\u1EBFt h\u1EA1n. Vui l\xF2ng y\xEAu c\u1EA7u li\xEAn k\u1EBFt m\u1EDBi.",
  formFieldHintText__optional: "T\xF9y ch\u1ECDn",
  formFieldHintText__slug: "M\u1ED9t slug l\xE0 m\u1ED9t ID d\u1EC5 \u0111\u1ECDc b\u1EDFi con ng\u01B0\u1EDDi m\xE0 ph\u1EA3i \u0111\u1ED9c nh\u1EA5t. N\xF3 th\u01B0\u1EDDng \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng trong URL.",
  formFieldInputPlaceholder__apiKeyDescription: "Gi\u1EA3i th\xEDch t\u1EA1i sao b\u1EA1n \u0111ang t\u1EA1o kho\xE1 n\xE0y",
  formFieldInputPlaceholder__apiKeyExpirationDate: "Ch\u1ECDn ng\xE0y",
  formFieldInputPlaceholder__apiKeyName: "Nh\u1EADp t\xEAn kho\xE1 b\xED m\u1EADt",
  formFieldInputPlaceholder__backupCode: "Nh\u1EADp m\xE3 d\u1EF1 ph\xF2ng",
  formFieldInputPlaceholder__confirmDeletionUserAccount: "X\xF3a t\xE0i kho\u1EA3n",
  formFieldInputPlaceholder__emailAddress: "Nh\u1EADp \u0111\u1ECBa ch\u1EC9 email c\u1EE7a b\u1EA1n",
  formFieldInputPlaceholder__emailAddress_username: "Nh\u1EADp email ho\u1EB7c t\xEAn ng\u01B0\u1EDDi d\xF9ng",
  formFieldInputPlaceholder__emailAddresses: "<EMAIL>, <EMAIL>",
  formFieldInputPlaceholder__firstName: "T\xEAn",
  formFieldInputPlaceholder__lastName: "H\u1ECD",
  formFieldInputPlaceholder__organizationDomain: "example.com",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "<EMAIL>",
  formFieldInputPlaceholder__organizationName: "T\xEAn t\u1ED5 ch\u1EE9c",
  formFieldInputPlaceholder__organizationSlug: "my-org",
  formFieldInputPlaceholder__password: "Nh\u1EADp m\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n",
  formFieldInputPlaceholder__phoneNumber: "Nh\u1EADp s\u1ED1 \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n",
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__apiKeyDescription: "M\xF4 t\u1EA3",
  formFieldLabel__apiKeyExpiration: "H\u1EBFt h\u1EA1n",
  formFieldLabel__apiKeyName: "T\xEAn kho\xE1 b\xED m\u1EADt",
  formFieldLabel__automaticInvitations: "Cho ph\xE9p t\u1EF1 \u0111\u1ED9ng m\u1EDDi cho t\xEAn mi\u1EC1n n\xE0y",
  formFieldLabel__backupCode: "M\xE3 d\u1EF1 ph\xF2ng",
  formFieldLabel__confirmDeletion: "X\xE1c nh\u1EADn",
  formFieldLabel__confirmPassword: "X\xE1c nh\u1EADn m\u1EADt kh\u1EA9u",
  formFieldLabel__currentPassword: "M\u1EADt kh\u1EA9u hi\u1EC7n t\u1EA1i",
  formFieldLabel__emailAddress: "\u0110\u1ECBa ch\u1EC9 email",
  formFieldLabel__emailAddress_username: "Email ho\u1EB7c t\xEAn ng\u01B0\u1EDDi d\xF9ng",
  formFieldLabel__emailAddresses: "\u0110\u1ECBa ch\u1EC9 email",
  formFieldLabel__firstName: "T\xEAn",
  formFieldLabel__lastName: "H\u1ECD",
  formFieldLabel__newPassword: "M\u1EADt kh\u1EA9u m\u1EDBi",
  formFieldLabel__organizationDomain: "T\xEAn mi\u1EC1n",
  formFieldLabel__organizationDomainDeletePending: "X\xF3a m\u1EDDi v\xE0 g\u1EE3i \xFD \u0111ang ch\u1EDD",
  formFieldLabel__organizationDomainEmailAddress: "\u0110\u1ECBa ch\u1EC9 email x\xE1c minh",
  formFieldLabel__organizationDomainEmailAddressDescription: "Nh\u1EADp \u0111\u1ECBa ch\u1EC9 email d\u01B0\u1EDBi t\xEAn mi\u1EC1n n\xE0y \u0111\u1EC3 nh\u1EADn m\xE3 v\xE0 x\xE1c minh t\xEAn mi\u1EC1n n\xE0y.",
  formFieldLabel__organizationName: "T\xEAn",
  formFieldLabel__organizationSlug: "Slug",
  formFieldLabel__passkeyName: "T\xEAn c\u1EE7a passkey",
  formFieldLabel__password: "M\u1EADt kh\u1EA9u",
  formFieldLabel__phoneNumber: "S\u1ED1 \u0111i\u1EC7n tho\u1EA1i",
  formFieldLabel__role: "Vai tr\xF2",
  formFieldLabel__signOutOfOtherSessions: "\u0110\u0103ng xu\u1EA5t kh\u1ECFi t\u1EA5t c\u1EA3 thi\u1EBFt b\u1ECB kh\xE1c",
  formFieldLabel__username: "T\xEAn ng\u01B0\u1EDDi d\xF9ng",
  impersonationFab: {
    action__signOut: "\u0110\u0103ng xu\u1EA5t",
    title: "\u0110\u0103ng nh\u1EADp v\u1EDBi {{identifier}}"
  },
  maintenanceMode: "Ch\xFAng t\xF4i \u0111ang trong qu\xE1 tr\xECnh b\u1EA3o tr\xEC, nh\u01B0ng \u0111\u1EEBng lo l\u1EAFng, n\xF3 kh\xF4ng n\xEAn m\u1EA5t qu\xE1 nhi\u1EC1u th\u1EDDi gian.",
  membershipRole__admin: "Qu\u1EA3n tr\u1ECB vi\xEAn",
  membershipRole__basicMember: "Th\xE0nh vi\xEAn",
  membershipRole__guestMember: "Kh\xE1ch",
  organizationList: {
    action__createOrganization: "T\u1EA1o t\u1ED5 ch\u1EE9c",
    action__invitationAccept: "Tham gia",
    action__suggestionsAccept: "Y\xEAu c\u1EA7u tham gia",
    createOrganization: "T\u1EA1o t\u1ED5 ch\u1EE9c",
    invitationAcceptedLabel: "Tham gia",
    subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c \u0111\u1EBFn {{applicationName}}",
    suggestionsAcceptedLabel: "\u0110ang ch\u1EDD ph\xEA duy\u1EC7t",
    title: "Ch\u1ECDn t\xE0i kho\u1EA3n",
    titleWithoutPersonal: "Ch\u1ECDn t\u1ED5 ch\u1EE9c"
  },
  organizationProfile: {
    apiKeysPage: {
      title: "Kho\xE1 API"
    },
    badge__automaticInvitation: "T\u1EF1 \u0111\u1ED9ng m\u1EDDi",
    badge__automaticSuggestion: "T\u1EF1 \u0111\u1ED9ng g\u1EE3i \xFD",
    badge__manualInvitation: "Kh\xF4ng t\u1EF1 \u0111\u1ED9ng \u0111\u0103ng k\xFD",
    badge__unverified: "Ch\u01B0a x\xE1c minh",
    billingPage: {
      paymentHistorySection: {
        empty: "Kh\xF4ng c\xF3 l\u1ECBch s\u1EED thanh to\xE1n",
        notFound: "Kh\xF4ng t\xECm th\u1EA5y l\u1ECBch s\u1EED thanh to\xE1n",
        tableHeader__amount: "S\u1ED1 ti\u1EC1n",
        tableHeader__date: "Ng\xE0y",
        tableHeader__status: "Tr\u1EA1ng th\xE1i"
      },
      paymentSourcesSection: {
        actionLabel__default: "L\xE0m m\u1EB7c \u0111\u1ECBnh",
        actionLabel__remove: "X\xF3a",
        add: "Th\xEAm ph\u01B0\u01A1ng th\u1EE9c thanh to\xE1n m\u1EDBi",
        addSubtitle: "Th\xEAm ph\u01B0\u01A1ng th\u1EE9c thanh to\xE1n m\u1EDBi v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
        cancelButton: "H\u1EE7y",
        formButtonPrimary__add: "Th\xEAm ph\u01B0\u01A1ng th\u1EE9c thanh to\xE1n",
        formButtonPrimary__pay: "Thanh to\xE1n {{amount}}",
        payWithTestCardButton: "Thanh to\xE1n v\u1EDBi th\u1EBB th\u1EED nghi\u1EC7m",
        removeResource: {
          messageLine1: "{{identifier}} s\u1EBD b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n n\xE0y.",
          messageLine2: "B\u1EA1n s\u1EBD kh\xF4ng c\xF2n th\u1EC3 s\u1EED d\u1EE5ng ngu\u1ED3n thanh to\xE1n n\xE0y v\xE0 b\u1EA5t k\u1EF3 \u0111\u0103ng k\xFD l\u1EB7p l\u1EA1i n\xE0o ph\u1EE5 thu\u1ED9c v\xE0o n\xF3 s\u1EBD kh\xF4ng c\xF2n ho\u1EA1t \u0111\u1ED9ng.",
          successMessage: "{{paymentSource}} \u0111\xE3 b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
          title: "X\xF3a ph\u01B0\u01A1ng th\u1EE9c thanh to\xE1n"
        },
        title: "Ph\u01B0\u01A1ng th\u1EE9c thanh to\xE1n"
      },
      start: {
        headerTitle__payments: "Thanh to\xE1n",
        headerTitle__plans: "G\xF3i",
        headerTitle__statements: "H\xF3a \u0111\u01A1n",
        headerTitle__subscriptions: "\u0110\u0103ng k\xFD"
      },
      statementsSection: {
        empty: "Kh\xF4ng c\xF3 h\xF3a \u0111\u01A1n \u0111\u1EC3 hi\u1EC3n th\u1ECB",
        itemCaption__paidForPlan: "Thanh to\xE1n cho g\xF3i {{plan}} {{period}}",
        itemCaption__proratedCredit: "T\xEDn d\u1EE5ng ph\xE2n chia cho s\u1EED d\u1EE5ng m\u1ED9t ph\u1EA7n c\u1EE7a \u0111\u0103ng k\xFD tr\u01B0\u1EDBc",
        itemCaption__subscribedAndPaidForPlan: "\u0110\u0103ng k\xFD v\xE0 thanh to\xE1n cho g\xF3i {{plan}} {{period}}",
        notFound: "Kh\xF4ng t\xECm th\u1EA5y h\xF3a \u0111\u01A1n",
        tableHeader__amount: "S\u1ED1 ti\u1EC1n",
        tableHeader__date: "Ng\xE0y",
        title: "H\xF3a \u0111\u01A1n",
        totalPaid: "T\u1ED5ng thanh to\xE1n"
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: "\u0110\u0103ng k\xFD g\xF3i",
        actionLabel__switchPlan: "Chuy\u1EC3n g\xF3i",
        tableHeader__edit: "S\u1EEDa",
        tableHeader__plan: "G\xF3i",
        tableHeader__startDate: "Ng\xE0y b\u1EAFt \u0111\u1EA7u",
        title: "\u0110\u0103ng k\xFD"
      },
      subscriptionsSection: {
        actionLabel__default: "Qu\u1EA3n l\xFD"
      },
      switchPlansSection: {
        title: "Chuy\u1EC3n g\xF3i"
      },
      title: "Thanh to\xE1n"
    },
    createDomainPage: {
      subtitle: "Th\xEAm t\xEAn mi\u1EC1n \u0111\u1EC3 x\xE1c minh. Ng\u01B0\u1EDDi d\xF9ng v\u1EDBi \u0111\u1ECBa ch\u1EC9 email \u1EDF t\xEAn mi\u1EC1n n\xE0y c\xF3 th\u1EC3 tham gia t\u1ED5 ch\u1EE9c t\u1EF1 \u0111\u1ED9ng ho\u1EB7c y\xEAu c\u1EA7u tham gia.",
      title: "Th\xEAm t\xEAn mi\u1EC1n"
    },
    invitePage: {
      detailsTitle__inviteFailed: "M\u1EDDi kh\xF4ng th\u1EC3 \u0111\u01B0\u1EE3c g\u1EEDi. \u0110\xE3 c\xF3 m\u1EDDi \u0111ang ch\u1EDD cho c\xE1c \u0111\u1ECBa ch\u1EC9 email sau: {{email_addresses}}.",
      formButtonPrimary__continue: "G\u1EEDi m\u1EDDi",
      selectDropdown__role: "Ch\u1ECDn vai tr\xF2",
      subtitle: "Nh\u1EADp ho\u1EB7c d\xE1n m\u1ED9t ho\u1EB7c nhi\u1EC1u \u0111\u1ECBa ch\u1EC9 email, c\xE1ch nhau b\u1EB1ng d\u1EA5u c\xE1ch ho\u1EB7c d\u1EA5u ph\u1EA9y.",
      successMessage: "M\u1EDDi \u0111\xE3 \u0111\u01B0\u1EE3c g\u1EEDi th\xE0nh c\xF4ng",
      title: "M\u1EDDi th\xE0nh vi\xEAn m\u1EDBi"
    },
    membersPage: {
      action__invite: "M\u1EDDi",
      action__search: "T\xECm ki\u1EBFm",
      activeMembersTab: {
        menuAction__remove: "X\xF3a th\xE0nh vi\xEAn",
        tableHeader__actions: "H\xE0nh \u0111\u1ED9ng",
        tableHeader__joined: "Tham gia",
        tableHeader__role: "Vai tr\xF2",
        tableHeader__user: "Ng\u01B0\u1EDDi d\xF9ng"
      },
      detailsTitle__emptyRow: "Kh\xF4ng c\xF3 th\xE0nh vi\xEAn \u0111\u1EC3 hi\u1EC3n th\u1ECB",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "M\u1EDDi ng\u01B0\u1EDDi d\xF9ng b\u1EB1ng c\xE1ch k\u1EBFt n\u1ED1i t\xEAn mi\u1EC1n email v\u1EDBi t\u1ED5 ch\u1EE9c c\u1EE7a b\u1EA1n. B\u1EA5t k\u1EF3 ai \u0111\u0103ng k\xFD v\u1EDBi t\xEAn mi\u1EC1n email kh\u1EDBp s\u1EBD c\xF3 th\u1EC3 tham gia t\u1ED5 ch\u1EE9c b\u1EA5t c\u1EE9 l\xFAc n\xE0o.",
          headerTitle: "T\u1EF1 \u0111\u1ED9ng m\u1EDDi",
          primaryButton: "Qu\u1EA3n l\xFD t\xEAn mi\u1EC1n \u0111\xE3 x\xE1c minh"
        },
        table__emptyRow: "Kh\xF4ng c\xF3 m\u1EDDi \u0111\u1EC3 hi\u1EC3n th\u1ECB"
      },
      invitedMembersTab: {
        menuAction__revoke: "H\u1EE7y m\u1EDDi",
        tableHeader__invited: "\u0110\xE3 m\u1EDDi"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Ng\u01B0\u1EDDi d\xF9ng \u0111\u0103ng k\xFD v\u1EDBi t\xEAn mi\u1EC1n email kh\u1EDBp s\u1EBD c\xF3 th\u1EC3 th\u1EA5y g\u1EE3i \xFD \u0111\u1EC3 y\xEAu c\u1EA7u tham gia t\u1ED5 ch\u1EE9c c\u1EE7a b\u1EA1n.",
          headerTitle: "T\u1EF1 \u0111\u1ED9ng g\u1EE3i \xFD",
          primaryButton: "Qu\u1EA3n l\xFD t\xEAn mi\u1EC1n \u0111\xE3 x\xE1c minh"
        },
        menuAction__approve: "Ph\xEA duy\u1EC7t",
        menuAction__reject: "T\u1EEB ch\u1ED1i",
        tableHeader__requested: "Y\xEAu c\u1EA7u truy c\u1EADp",
        table__emptyRow: "Kh\xF4ng c\xF3 y\xEAu c\u1EA7u \u0111\u1EC3 hi\u1EC3n th\u1ECB"
      },
      start: {
        headerTitle__invitations: "M\u1EDDi",
        headerTitle__members: "Th\xE0nh vi\xEAn",
        headerTitle__requests: "Y\xEAu c\u1EA7u"
      }
    },
    navbar: {
      apiKeys: "Kho\xE1 API",
      billing: "Thanh to\xE1n",
      description: "Qu\u1EA3n l\xFD t\u1ED5 ch\u1EE9c c\u1EE7a b\u1EA1n.",
      general: "T\u1ED5ng quan",
      members: "Th\xE0nh vi\xEAn",
      title: "T\u1ED5 ch\u1EE9c"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: "B\u1EA1n kh\xF4ng c\xF3 quy\u1EC1n qu\u1EA3n l\xFD thanh to\xE1n cho t\u1ED5 ch\u1EE9c n\xE0y."
      },
      title: "G\xF3i"
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Nh\u1EADp "{{organizationName}}" d\u01B0\u1EDBi \u0111\u1EC3 ti\u1EBFp t\u1EE5c.',
          messageLine1: "B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n x\xF3a t\u1ED5 ch\u1EE9c n\xE0y?",
          messageLine2: "H\xE0nh \u0111\u1ED9ng n\xE0y l\xE0 v\u0129nh vi\u1EC5n v\xE0 kh\xF4ng th\u1EC3 ho\xE0n t\xE1c.",
          successMessage: "B\u1EA1n \u0111\xE3 x\xF3a t\u1ED5 ch\u1EE9c.",
          title: "X\xF3a t\u1ED5 ch\u1EE9c"
        },
        leaveOrganization: {
          actionDescription: 'Nh\u1EADp "{{organizationName}}" d\u01B0\u1EDBi \u0111\u1EC3 ti\u1EBFp t\u1EE5c.',
          messageLine1: "B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n r\u1EDDi kh\u1ECFi t\u1ED5 ch\u1EE9c n\xE0y? B\u1EA1n s\u1EBD m\u1EA5t quy\u1EC1n truy c\u1EADp v\xE0o t\u1ED5 ch\u1EE9c n\xE0y v\xE0 \u1EE9ng d\u1EE5ng c\u1EE7a n\xF3.",
          messageLine2: "H\xE0nh \u0111\u1ED9ng n\xE0y l\xE0 v\u0129nh vi\u1EC5n v\xE0 kh\xF4ng th\u1EC3 ho\xE0n t\xE1c.",
          successMessage: "B\u1EA1n \u0111\xE3 r\u1EDDi kh\u1ECFi t\u1ED5 ch\u1EE9c.",
          title: "R\u1EDDi kh\u1ECFi t\u1ED5 ch\u1EE9c"
        },
        title: "Nguy hi\u1EC3m"
      },
      domainSection: {
        menuAction__manage: "Qu\u1EA3n l\xFD",
        menuAction__remove: "X\xF3a",
        menuAction__verify: "X\xE1c minh",
        primaryButton: "Th\xEAm t\xEAn mi\u1EC1n",
        subtitle: "Cho ph\xE9p ng\u01B0\u1EDDi d\xF9ng tham gia t\u1ED5 ch\u1EE9c t\u1EF1 \u0111\u1ED9ng ho\u1EB7c y\xEAu c\u1EA7u tham gia d\u1EF1a tr\xEAn t\xEAn mi\u1EC1n email \u0111\xE3 x\xE1c minh.",
        title: "T\xEAn mi\u1EC1n \u0111\xE3 x\xE1c minh"
      },
      successMessage: "T\u1ED5 ch\u1EE9c \u0111\xE3 \u0111\u01B0\u1EE3c c\u1EADp nh\u1EADt.",
      title: "C\u1EADp nh\u1EADt h\u1ED3 s\u01A1"
    },
    removeDomainPage: {
      messageLine1: "T\xEAn mi\u1EC1n email {{domain}} s\u1EBD b\u1ECB x\xF3a.",
      messageLine2: "Ng\u01B0\u1EDDi d\xF9ng s\u1EBD kh\xF4ng th\u1EC3 tham gia t\u1ED5 ch\u1EE9c t\u1EF1 \u0111\u1ED9ng sau \u0111\xE2y.",
      successMessage: "{{domain}} \u0111\xE3 b\u1ECB x\xF3a.",
      title: "X\xF3a t\xEAn mi\u1EC1n"
    },
    start: {
      headerTitle__general: "T\u1ED5ng quan",
      headerTitle__members: "Th\xE0nh vi\xEAn",
      profileSection: {
        primaryButton: "C\u1EADp nh\u1EADt h\u1ED3 s\u01A1",
        title: "H\u1ED3 s\u01A1 t\u1ED5 ch\u1EE9c",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "X\xF3a t\xEAn mi\u1EC1n n\xE0y s\u1EBD \u1EA3nh h\u01B0\u1EDFng \u0111\u1EBFn ng\u01B0\u1EDDi d\xF9ng \u0111\xE3 m\u1EDDi.",
        removeDomainActionLabel__remove: "X\xF3a t\xEAn mi\u1EC1n",
        removeDomainSubtitle: "X\xF3a t\xEAn mi\u1EC1n n\xE0y kh\u1ECFi t\xEAn mi\u1EC1n \u0111\xE3 x\xE1c minh",
        removeDomainTitle: "X\xF3a t\xEAn mi\u1EC1n"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Ng\u01B0\u1EDDi d\xF9ng s\u1EBD t\u1EF1 \u0111\u1ED9ng \u0111\u01B0\u1EE3c m\u1EDDi tham gia t\u1ED5 ch\u1EE9c khi \u0111\u0103ng k\xFD v\xE0 c\xF3 th\u1EC3 tham gia b\u1EA5t c\u1EE9 l\xFAc n\xE0o.",
        automaticInvitationOption__label: "T\u1EF1 \u0111\u1ED9ng m\u1EDDi",
        automaticSuggestionOption__description: "Ng\u01B0\u1EDDi d\xF9ng s\u1EBD nh\u1EADn \u0111\u01B0\u1EE3c g\u1EE3i \xFD \u0111\u1EC3 y\xEAu c\u1EA7u tham gia, nh\u01B0ng ph\u1EA3i \u0111\u01B0\u1EE3c ph\xEA duy\u1EC7t b\u1EDFi qu\u1EA3n tr\u1ECB vi\xEAn tr\u01B0\u1EDBc khi h\u1ECD c\xF3 th\u1EC3 tham gia t\u1ED5 ch\u1EE9c.",
        automaticSuggestionOption__label: "T\u1EF1 \u0111\u1ED9ng g\u1EE3i \xFD",
        calloutInfoLabel: "Thay \u0111\u1ED5i ch\u1EBF \u0111\u1ED9 \u0111\u0103ng k\xFD s\u1EBD ch\u1EC9 \u1EA3nh h\u01B0\u1EDFng \u0111\u1EBFn ng\u01B0\u1EDDi d\xF9ng m\u1EDBi.",
        calloutInvitationCountLabel: "M\u1EDDi \u0111ang ch\u1EDD g\u1EEDi \u0111\u1EBFn ng\u01B0\u1EDDi d\xF9ng: {{count}}",
        calloutSuggestionCountLabel: "G\u1EE3i \xFD \u0111ang ch\u1EDD g\u1EEDi \u0111\u1EBFn ng\u01B0\u1EDDi d\xF9ng: {{count}}",
        manualInvitationOption__description: "Ng\u01B0\u1EDDi d\xF9ng ch\u1EC9 c\xF3 th\u1EC3 \u0111\u01B0\u1EE3c m\u1EDDi th\u1EE7 c\xF4ng \u0111\u1EBFn t\u1ED5 ch\u1EE9c.",
        manualInvitationOption__label: "Kh\xF4ng t\u1EF1 \u0111\u1ED9ng \u0111\u0103ng k\xFD",
        subtitle: "Ch\u1ECDn c\xE1ch ng\u01B0\u1EDDi d\xF9ng t\u1EEB t\xEAn mi\u1EC1n n\xE0y c\xF3 th\u1EC3 tham gia t\u1ED5 ch\u1EE9c."
      },
      start: {
        headerTitle__danger: "Nguy hi\u1EC3m",
        headerTitle__enrollment: "Ch\u1EBF \u0111\u1ED9 \u0111\u0103ng k\xFD"
      },
      subtitle: "T\xEAn mi\u1EC1n {{domain}} \u0111\xE3 \u0111\u01B0\u1EE3c x\xE1c minh. Ti\u1EBFp t\u1EE5c b\u1EB1ng c\xE1ch ch\u1ECDn ch\u1EBF \u0111\u1ED9 \u0111\u0103ng k\xFD.",
      title: "C\u1EADp nh\u1EADt {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Nh\u1EADp m\xE3 x\xE1c minh \u0111\xE3 g\u1EEDi \u0111\u1EBFn \u0111\u1ECBa ch\u1EC9 email c\u1EE7a b\u1EA1n",
      formTitle: "M\xE3 x\xE1c minh",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "T\xEAn mi\u1EC1n {{domainName}} c\u1EA7n \u0111\u01B0\u1EE3c x\xE1c minh qua email.",
      subtitleVerificationCodeScreen: "M\u1ED9t m\xE3 x\xE1c minh \u0111\xE3 \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn {{emailAddress}}. Nh\u1EADp m\xE3 \u0111\u1EC3 ti\u1EBFp t\u1EE5c.",
      title: "X\xE1c minh t\xEAn mi\u1EC1n"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "T\u1EA1o t\u1ED5 ch\u1EE9c",
    action__invitationAccept: "Tham gia",
    action__manageOrganization: "Qu\u1EA3n l\xFD",
    action__suggestionsAccept: "Y\xEAu c\u1EA7u tham gia",
    notSelected: "Kh\xF4ng c\xF3 t\u1ED5 ch\u1EE9c \u0111\u01B0\u1EE3c ch\u1ECDn",
    personalWorkspace: "T\xE0i kho\u1EA3n c\xE1 nh\xE2n",
    suggestionsAcceptedLabel: "\u0110ang ch\u1EDD ph\xEA duy\u1EC7t"
  },
  paginationButton__next: "Ti\u1EBFp",
  paginationButton__previous: "Tr\u01B0\u1EDBc",
  paginationRowText__displaying: "Hi\u1EC3n th\u1ECB",
  paginationRowText__of: "c\u1EE7a",
  reverification: {
    alternativeMethods: {
      actionLink: "Li\xEAn h\u1EC7 h\u1ED7 tr\u1EE3",
      actionText: "Kh\xF4ng c\xF3 b\u1EA5t k\u1EF3 ph\u01B0\u01A1ng th\u1EE9c n\xE0o?",
      blockButton__backupCode: "S\u1EED d\u1EE5ng m\xE3 d\u1EF1 ph\xF2ng",
      blockButton__emailCode: "Email m\xE3 \u0111\u1EBFn {{identifier}}",
      blockButton__passkey: "\u0110\u0103ng nh\u1EADp v\u1EDBi m\xE3 passkey",
      blockButton__password: "Ti\u1EBFp t\u1EE5c v\u1EDBi m\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n",
      blockButton__phoneCode: "G\u1EEDi m\xE3 SMS \u0111\u1EBFn {{identifier}}",
      blockButton__totp: "S\u1EED d\u1EE5ng \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c",
      getHelp: {
        blockButton__emailSupport: "Email h\u1ED7 tr\u1EE3",
        content: "N\u1EBFu b\u1EA1n g\u1EB7p v\u1EA5n \u0111\u1EC1 khi x\xE1c minh t\xE0i kho\u1EA3n c\u1EE7a m\xECnh, email ch\xFAng t\xF4i v\xE0 ch\xFAng t\xF4i s\u1EBD h\u1ED7 tr\u1EE3 b\u1EA1n kh\xF4i ph\u1EE5c quy\u1EC1n truy c\u1EADp s\u1EDBm nh\u1EA5t c\xF3 th\u1EC3.",
        title: "Li\xEAn h\u1EC7 h\u1ED7 tr\u1EE3"
      },
      subtitle: "G\u1EB7p v\u1EA5n \u0111\u1EC1? B\u1EA1n c\xF3 th\u1EC3 s\u1EED d\u1EE5ng b\u1EA5t k\u1EF3 ph\u01B0\u01A1ng th\u1EE9c n\xE0o \u0111\u1EC3 x\xE1c minh.",
      title: "S\u1EED d\u1EE5ng ph\u01B0\u01A1ng th\u1EE9c kh\xE1c"
    },
    backupCodeMfa: {
      subtitle: "Nh\u1EADp m\xE3 d\u1EF1 ph\xF2ng b\u1EA1n nh\u1EADn \u0111\u01B0\u1EE3c khi thi\u1EBFt l\u1EADp x\xE1c th\u1EF1c hai b\u01B0\u1EDBc",
      title: "Nh\u1EADp m\xE3 d\u1EF1 ph\xF2ng"
    },
    emailCode: {
      formTitle: "M\xE3 x\xE1c minh",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "Nh\u1EADp m\xE3 \u0111\xE3 g\u1EEDi \u0111\u1EBFn email c\u1EE7a b\u1EA1n \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
      title: "X\xE1c minh y\xEAu c\u1EA7u"
    },
    noAvailableMethods: {
      message: "Kh\xF4ng th\u1EC3 ti\u1EBFp t\u1EE5c v\u1EDBi x\xE1c minh. Kh\xF4ng c\xF3 y\u1EBFu t\u1ED1 x\xE1c th\u1EF1c ph\xF9 h\u1EE3p \u0111\u01B0\u1EE3c c\u1EA5u h\xECnh",
      subtitle: "\u0110\xE3 x\u1EA3y ra l\u1ED7i",
      title: "Kh\xF4ng th\u1EC3 x\xE1c minh t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n"
    },
    passkey: {
      blockButton__passkey: "S\u1EED d\u1EE5ng m\xE3 passkey",
      subtitle: "S\u1EED d\u1EE5ng m\xE3 passkey x\xE1c minh danh t\xEDnh c\u1EE7a b\u1EA1n. Thi\u1EBFt b\u1ECB c\u1EE7a b\u1EA1n c\xF3 th\u1EC3 y\xEAu c\u1EA7u v\xE2n tay, khu\xF4n m\u1EB7t ho\u1EB7c kh\xF3a m\xE0n h\xECnh.",
      title: "S\u1EED d\u1EE5ng m\xE3 passkey"
    },
    password: {
      actionLink: "S\u1EED d\u1EE5ng ph\u01B0\u01A1ng th\u1EE9c kh\xE1c",
      subtitle: "Nh\u1EADp m\u1EADt kh\u1EA9u hi\u1EC7n t\u1EA1i \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
      title: "X\xE1c minh y\xEAu c\u1EA7u"
    },
    phoneCode: {
      formTitle: "M\xE3 x\xE1c minh",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "Nh\u1EADp m\xE3 \u0111\xE3 g\u1EEDi \u0111\u1EBFn \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
      title: "X\xE1c minh y\xEAu c\u1EA7u"
    },
    phoneCodeMfa: {
      formTitle: "M\xE3 x\xE1c minh",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "Nh\u1EADp m\xE3 \u0111\xE3 g\u1EEDi \u0111\u1EBFn \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
      title: "X\xE1c minh y\xEAu c\u1EA7u"
    },
    totpMfa: {
      formTitle: "M\xE3 x\xE1c minh",
      subtitle: "Nh\u1EADp m\xE3 \u0111\xE3 \u0111\u01B0\u1EE3c t\u1EA1o b\u1EDFi \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c c\u1EE7a b\u1EA1n \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
      title: "X\xE1c minh y\xEAu c\u1EA7u"
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Th\xEAm t\xE0i kho\u1EA3n",
      action__signOutAll: "\u0110\u0103ng xu\u1EA5t kh\u1ECFi t\u1EA5t c\u1EA3 t\xE0i kho\u1EA3n",
      subtitle: "Ch\u1ECDn t\xE0i kho\u1EA3n v\u1EDBi \u0111\xF3 b\u1EA1n mu\u1ED1n ti\u1EBFp t\u1EE5c.",
      title: "Ch\u1ECDn t\xE0i kho\u1EA3n"
    },
    alternativeMethods: {
      actionLink: "Li\xEAn h\u1EC7 h\u1ED7 tr\u1EE3",
      actionText: "Kh\xF4ng c\xF3 b\u1EA5t k\u1EF3 ph\u01B0\u01A1ng th\u1EE9c n\xE0o?",
      blockButton__backupCode: "S\u1EED d\u1EE5ng m\xE3 d\u1EF1 ph\xF2ng",
      blockButton__emailCode: "Email m\xE3 \u0111\u1EBFn {{identifier}}",
      blockButton__emailLink: "Email li\xEAn k\u1EBFt \u0111\u1EBFn {{identifier}}",
      blockButton__passkey: "\u0110\u0103ng nh\u1EADp v\u1EDBi m\xE3 passkey",
      blockButton__password: "\u0110\u0103ng nh\u1EADp v\u1EDBi m\u1EADt kh\u1EA9u",
      blockButton__phoneCode: "G\u1EEDi m\xE3 SMS \u0111\u1EBFn {{identifier}}",
      blockButton__totp: "S\u1EED d\u1EE5ng \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c",
      getHelp: {
        blockButton__emailSupport: "Email h\u1ED7 tr\u1EE3",
        content: "N\u1EBFu b\u1EA1n g\u1EB7p v\u1EA5n \u0111\u1EC1 khi \u0111\u0103ng nh\u1EADp v\xE0o t\xE0i kho\u1EA3n c\u1EE7a m\xECnh, email ch\xFAng t\xF4i v\xE0 ch\xFAng t\xF4i s\u1EBD h\u1ED7 tr\u1EE3 b\u1EA1n kh\xF4i ph\u1EE5c quy\u1EC1n truy c\u1EADp s\u1EDBm nh\u1EA5t c\xF3 th\u1EC3.",
        title: "Li\xEAn h\u1EC7 h\u1ED7 tr\u1EE3"
      },
      subtitle: "G\u1EB7p v\u1EA5n \u0111\u1EC1? B\u1EA1n c\xF3 th\u1EC3 s\u1EED d\u1EE5ng b\u1EA5t k\u1EF3 ph\u01B0\u01A1ng th\u1EE9c n\xE0o \u0111\u1EC3 \u0111\u0103ng nh\u1EADp.",
      title: "S\u1EED d\u1EE5ng ph\u01B0\u01A1ng th\u1EE9c kh\xE1c"
    },
    alternativePhoneCodeProvider: {
      formTitle: "M\xE3 x\xE1c minh",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c \u0111\u1EBFn {{applicationName}}",
      title: "Ki\u1EC3m tra {{provider}}"
    },
    backupCodeMfa: {
      subtitle: "M\xE3 d\u1EF1 ph\xF2ng c\u1EE7a b\u1EA1n l\xE0 m\xE3 b\u1EA1n nh\u1EADn \u0111\u01B0\u1EE3c khi thi\u1EBFt l\u1EADp x\xE1c th\u1EF1c hai b\u01B0\u1EDBc.",
      title: "Nh\u1EADp m\xE3 d\u1EF1 ph\xF2ng"
    },
    emailCode: {
      formTitle: "M\xE3 x\xE1c minh",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c \u0111\u1EBFn {{applicationName}}",
      title: "Ki\u1EC3m tra email"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "\u0110\u1EC3 ti\u1EBFp t\u1EE5c, m\u1EDF li\xEAn k\u1EBFt x\xE1c minh tr\xEAn thi\u1EBFt b\u1ECB v\xE0 tr\xECnh duy\u1EC7t t\u1EEB \u0111\xF3 b\u1EA1n \u0111\xE3 kh\u1EDFi \u0111\u1ED9ng \u0111\u0103ng nh\u1EADp",
        title: "Li\xEAn k\u1EBFt x\xE1c minh kh\xF4ng h\u1EE3p l\u1EC7 cho thi\u1EBFt b\u1ECB n\xE0y"
      },
      expired: {
        subtitle: "Quay l\u1EA1i tab g\u1ED1c \u0111\u1EC3 ti\u1EBFp t\u1EE5c.",
        title: "Li\xEAn k\u1EBFt x\xE1c minh \u0111\xE3 h\u1EBFt h\u1EA1n"
      },
      failed: {
        subtitle: "Quay l\u1EA1i tab g\u1ED1c \u0111\u1EC3 ti\u1EBFp t\u1EE5c.",
        title: "Li\xEAn k\u1EBFt x\xE1c minh kh\xF4ng h\u1EE3p l\u1EC7"
      },
      formSubtitle: "S\u1EED d\u1EE5ng li\xEAn k\u1EBFt x\xE1c minh \u0111\xE3 g\u1EEDi \u0111\u1EBFn email c\u1EE7a b\u1EA1n",
      formTitle: "Li\xEAn k\u1EBFt x\xE1c minh",
      loading: {
        subtitle: "B\u1EA1n s\u1EBD \u0111\u01B0\u1EE3c chuy\u1EC3n h\u01B0\u1EDBng s\u1EDBm",
        title: "\u0110ang \u0111\u0103ng nh\u1EADp..."
      },
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c li\xEAn k\u1EBFt? G\u1EEDi l\u1EA1i",
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c \u0111\u1EBFn {{applicationName}}",
      title: "Ki\u1EC3m tra email",
      unusedTab: {
        title: "B\u1EA1n c\xF3 th\u1EC3 \u0111\xF3ng tab n\xE0y"
      },
      verified: {
        subtitle: "B\u1EA1n s\u1EBD \u0111\u01B0\u1EE3c chuy\u1EC3n h\u01B0\u1EDBng s\u1EDBm",
        title: "\u0110\u0103ng nh\u1EADp th\xE0nh c\xF4ng"
      },
      verifiedSwitchTab: {
        subtitle: "Quay l\u1EA1i tab g\u1ED1c \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
        subtitleNewTab: "Quay l\u1EA1i tab m\u1EDBi \u0111\u01B0\u1EE3c m\u1EDF \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
        titleNewTab: "\u0110\u0103ng nh\u1EADp tr\xEAn tab kh\xE1c"
      }
    },
    forgotPassword: {
      formTitle: "M\xE3 x\xE1c minh m\u1EADt kh\u1EA9u",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "\u0111\u1EC3 \u0111\u1EB7t l\u1EA1i m\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n",
      subtitle_email: "\u0110\u1EA7u ti\xEAn, nh\u1EADp m\xE3 \u0111\xE3 g\u1EEDi \u0111\u1EBFn email c\u1EE7a b\u1EA1n",
      subtitle_phone: "\u0110\u1EA7u ti\xEAn, nh\u1EADp m\xE3 \u0111\xE3 g\u1EEDi \u0111\u1EBFn \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n",
      title: "\u0110\u1EB7t l\u1EA1i m\u1EADt kh\u1EA9u"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "\u0110\u1EB7t l\u1EA1i m\u1EADt kh\u1EA9u",
      label__alternativeMethods: "Ho\u1EB7c, \u0111\u0103ng nh\u1EADp v\u1EDBi ph\u01B0\u01A1ng th\u1EE9c kh\xE1c",
      title: "Qu\xEAn m\u1EADt kh\u1EA9u?"
    },
    noAvailableMethods: {
      message: "Kh\xF4ng th\u1EC3 ti\u1EBFp t\u1EE5c \u0111\u0103ng nh\u1EADp. Kh\xF4ng c\xF3 y\u1EBFu t\u1ED1 x\xE1c th\u1EF1c ph\xF9 h\u1EE3p \u0111\u01B0\u1EE3c c\u1EA5u h\xECnh",
      subtitle: "\u0110\xE3 x\u1EA3y ra l\u1ED7i",
      title: "Kh\xF4ng th\u1EC3 \u0111\u0103ng nh\u1EADp"
    },
    passkey: {
      subtitle: "S\u1EED d\u1EE5ng m\xE3 passkey x\xE1c minh danh t\xEDnh c\u1EE7a b\u1EA1n. Thi\u1EBFt b\u1ECB c\u1EE7a b\u1EA1n c\xF3 th\u1EC3 y\xEAu c\u1EA7u v\xE2n tay, khu\xF4n m\u1EB7t ho\u1EB7c kh\xF3a m\xE0n h\xECnh.",
      title: "S\u1EED d\u1EE5ng m\xE3 passkey"
    },
    password: {
      actionLink: "S\u1EED d\u1EE5ng ph\u01B0\u01A1ng th\u1EE9c kh\xE1c",
      subtitle: "Nh\u1EADp m\u1EADt kh\u1EA9u \u0111\u01B0\u1EE3c li\xEAn k\u1EBFt v\u1EDBi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n",
      title: "Nh\u1EADp m\u1EADt kh\u1EA9u"
    },
    passwordPwned: {
      title: "M\u1EADt kh\u1EA9u b\u1ECB r\xF2 r\u1EC9"
    },
    phoneCode: {
      formTitle: "M\xE3 x\xE1c minh",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c \u0111\u1EBFn {{applicationName}}",
      title: "Ki\u1EC3m tra \u0111i\u1EC7n tho\u1EA1i"
    },
    phoneCodeMfa: {
      formTitle: "M\xE3 x\xE1c minh",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c \u0111\u1EBFn {{applicationName}}",
      title: "Ki\u1EC3m tra \u0111i\u1EC7n tho\u1EA1i"
    },
    resetPassword: {
      formButtonPrimary: "\u0110\u1EB7t l\u1EA1i m\u1EADt kh\u1EA9u",
      requiredMessage: "V\xEC l\xFD do b\u1EA3o m\u1EADt, vi\u1EC7c \u0111\u1EB7t l\u1EA1i m\u1EADt kh\u1EA9u l\xE0 b\u1EAFt bu\u1ED9c.",
      successMessage: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n \u0111\xE3 \u0111\u01B0\u1EE3c thay \u0111\u1ED5i th\xE0nh c\xF4ng. \u0110ang \u0111\u0103ng nh\u1EADp, vui l\xF2ng ch\u1EDD m\u1ED9t l\xE1t.",
      title: "\u0110\u1EB7t m\u1EADt kh\u1EA9u m\u1EDBi"
    },
    resetPasswordMfa: {
      detailsLabel: "Ch\xFAng t\xF4i c\u1EA7n x\xE1c minh danh t\xEDnh c\u1EE7a b\u1EA1n tr\u01B0\u1EDBc khi \u0111\u1EB7t l\u1EA1i m\u1EADt kh\u1EA9u."
    },
    start: {
      actionLink: "\u0110\u0103ng k\xFD",
      actionLink__join_waitlist: "Tham gia danh s\xE1ch ch\u1EDD",
      actionLink__use_email: "S\u1EED d\u1EE5ng email",
      actionLink__use_email_username: "S\u1EED d\u1EE5ng email ho\u1EB7c t\xEAn ng\u01B0\u1EDDi d\xF9ng",
      actionLink__use_passkey: "S\u1EED d\u1EE5ng m\xE3 passkey thay v\xEC",
      actionLink__use_phone: "S\u1EED d\u1EE5ng \u0111i\u1EC7n tho\u1EA1i",
      actionLink__use_username: "S\u1EED d\u1EE5ng t\xEAn ng\u01B0\u1EDDi d\xF9ng",
      actionText: "Kh\xF4ng c\xF3 t\xE0i kho\u1EA3n?",
      actionText__join_waitlist: "Mu\u1ED1n tr\u1EA3i nghi\u1EC7m s\u1EDBm?",
      alternativePhoneCodeProvider: {
        actionLink: "S\u1EED d\u1EE5ng ph\u01B0\u01A1ng th\u1EE9c kh\xE1c",
        label: "{{provider}} s\u1ED1 \u0111i\u1EC7n tho\u1EA1i",
        subtitle: "Nh\u1EADp s\u1ED1 \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n \u0111\u1EC3 nh\u1EADn m\xE3 x\xE1c minh tr\xEAn {{provider}}.",
        title: "\u0110\u0103ng nh\u1EADp v\xE0o {{applicationName}} v\u1EDBi {{provider}}"
      },
      subtitle: "Ch\xE0o m\u1EEBng tr\u1EDF l\u1EA1i! Vui l\xF2ng \u0111\u0103ng nh\u1EADp \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
      subtitleCombined: void 0,
      title: "\u0110\u0103ng nh\u1EADp v\xE0o {{applicationName}}",
      titleCombined: "Ti\u1EBFp t\u1EE5c \u0111\u1EBFn {{applicationName}}"
    },
    totpMfa: {
      formTitle: "M\xE3 x\xE1c minh",
      subtitle: "\u0110\u1EC3 ti\u1EBFp t\u1EE5c, vui l\xF2ng nh\u1EADp m\xE3 x\xE1c minh \u0111\u01B0\u1EE3c t\u1EA1o b\u1EDFi \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c c\u1EE7a b\u1EA1n",
      title: "X\xE1c th\u1EF1c hai b\u01B0\u1EDBc"
    }
  },
  signInEnterPasswordTitle: "Nh\u1EADp m\u1EADt kh\u1EA9u",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "Nh\u1EADp m\xE3 x\xE1c minh \u0111\xE3 g\u1EEDi \u0111\u1EBFn {{provider}}",
      title: "X\xE1c minh {{provider}}"
    },
    continue: {
      actionLink: "\u0110\u0103ng nh\u1EADp",
      actionText: "\u0110\xE3 c\xF3 t\xE0i kho\u1EA3n?",
      subtitle: "Vui l\xF2ng \u0111i\u1EC1n c\xE1c chi ti\u1EBFt c\xF2n l\u1EA1i \u0111\u1EC3 ti\u1EBFp t\u1EE5c.",
      title: "\u0110i\u1EC1n c\xE1c tr\u01B0\u1EDDng c\xF2n thi\u1EBFu"
    },
    emailCode: {
      formSubtitle: "Nh\u1EADp m\xE3 x\xE1c minh \u0111\xE3 g\u1EEDi \u0111\u1EBFn email c\u1EE7a b\u1EA1n",
      formTitle: "M\xE3 x\xE1c minh",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "Nh\u1EADp m\xE3 x\xE1c minh \u0111\xE3 g\u1EEDi \u0111\u1EBFn email c\u1EE7a b\u1EA1n",
      title: "X\xE1c minh email"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "\u0110\u1EC3 ti\u1EBFp t\u1EE5c, m\u1EDF li\xEAn k\u1EBFt x\xE1c minh tr\xEAn thi\u1EBFt b\u1ECB v\xE0 tr\xECnh duy\u1EC7t t\u1EEB \u0111\xF3 b\u1EA1n \u0111\xE3 kh\u1EDFi \u0111\u1ED9ng \u0111\u0103ng k\xFD",
        title: "Li\xEAn k\u1EBFt x\xE1c minh kh\xF4ng h\u1EE3p l\u1EC7 cho thi\u1EBFt b\u1ECB n\xE0y"
      },
      formSubtitle: "S\u1EED d\u1EE5ng li\xEAn k\u1EBFt x\xE1c minh \u0111\xE3 g\u1EEDi \u0111\u1EBFn email c\u1EE7a b\u1EA1n",
      formTitle: "Li\xEAn k\u1EBFt x\xE1c minh",
      loading: {
        title: "\u0110ang \u0111\u0103ng k\xFD..."
      },
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c li\xEAn k\u1EBFt? G\u1EEDi l\u1EA1i",
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c \u0111\u1EBFn {{applicationName}}",
      title: "X\xE1c minh email",
      verified: {
        title: "\u0110\u0103ng k\xFD th\xE0nh c\xF4ng"
      },
      verifiedSwitchTab: {
        subtitle: "Quay l\u1EA1i tab m\u1EDBi \u0111\u01B0\u1EE3c m\u1EDF \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
        subtitleNewTab: "Quay l\u1EA1i tab tr\u01B0\u1EDBc \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
        title: "X\xE1c minh email th\xE0nh c\xF4ng"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: 'T\xF4i \u0111\u1ED3ng \xFD v\u1EDBi {{ privacyPolicyLink || link("Ch\xEDnh s\xE1ch b\u1EA3o m\u1EADt") }}',
        label__onlyTermsOfService: 'T\xF4i \u0111\u1ED3ng \xFD v\u1EDBi {{ termsOfServiceLink || link("\u0110i\u1EC1u kho\u1EA3n d\u1ECBch v\u1EE5") }}',
        label__termsOfServiceAndPrivacyPolicy: 'T\xF4i \u0111\u1ED3ng \xFD v\u1EDBi {{ termsOfServiceLink || link("\u0110i\u1EC1u kho\u1EA3n d\u1ECBch v\u1EE5") }} v\xE0 {{ privacyPolicyLink || link("Ch\xEDnh s\xE1ch b\u1EA3o m\u1EADt") }}'
      },
      continue: {
        subtitle: "Vui l\xF2ng \u0111\u1ECDc v\xE0 \u0111\u1ED3ng \xFD v\u1EDBi c\xE1c \u0111i\u1EC1u kho\u1EA3n \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
        title: "\u0110\u1ED3ng \xFD v\u1EDBi \u0111i\u1EC1u kho\u1EA3n"
      }
    },
    phoneCode: {
      formSubtitle: "Nh\u1EADp m\xE3 x\xE1c minh \u0111\xE3 g\u1EEDi \u0111\u1EBFn s\u1ED1 \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n",
      formTitle: "M\xE3 x\xE1c minh",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "Nh\u1EADp m\xE3 x\xE1c minh \u0111\xE3 g\u1EEDi \u0111\u1EBFn s\u1ED1 \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n",
      title: "X\xE1c minh \u0111i\u1EC7n tho\u1EA1i"
    },
    restrictedAccess: {
      actionLink: "\u0110\u0103ng nh\u1EADp",
      actionText: "\u0110\xE3 c\xF3 t\xE0i kho\u1EA3n?",
      blockButton__emailSupport: "Email h\u1ED7 tr\u1EE3",
      blockButton__joinWaitlist: "Tham gia danh s\xE1ch ch\u1EDD",
      subtitle: "\u0110\u0103ng k\xFD hi\u1EC7n kh\xF4ng kh\u1EA3 d\u1EE5ng. N\u1EBFu b\u1EA1n tin r\u1EB1ng b\u1EA1n c\xF3 quy\u1EC1n truy c\u1EADp, vui l\xF2ng li\xEAn h\u1EC7 h\u1ED7 tr\u1EE3.",
      subtitleWaitlist: "\u0110\u0103ng k\xFD hi\u1EC7n kh\xF4ng kh\u1EA3 d\u1EE5ng. \u0110\u1EC3 bi\u1EBFt th\xF4ng tin s\u1EDBm nh\u1EA5t khi ch\xFAng t\xF4i kh\u1EDFi ch\u1EA1y, h\xE3y tham gia danh s\xE1ch ch\u1EDD.",
      title: "Quy\u1EC1n truy c\u1EADp b\u1ECB gi\u1EDBi h\u1EA1n"
    },
    start: {
      actionLink: "\u0110\u0103ng nh\u1EADp",
      actionLink__use_email: "S\u1EED d\u1EE5ng email thay v\xEC",
      actionLink__use_phone: "S\u1EED d\u1EE5ng \u0111i\u1EC7n tho\u1EA1i thay v\xEC",
      actionText: "\u0110\xE3 c\xF3 t\xE0i kho\u1EA3n?",
      alternativePhoneCodeProvider: {
        actionLink: "S\u1EED d\u1EE5ng ph\u01B0\u01A1ng th\u1EE9c kh\xE1c",
        label: "{{provider}} s\u1ED1 \u0111i\u1EC7n tho\u1EA1i",
        subtitle: "Nh\u1EADp s\u1ED1 \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n \u0111\u1EC3 nh\u1EADn m\xE3 x\xE1c minh tr\xEAn {{provider}}.",
        title: "\u0110\u0103ng k\xFD v\xE0o {{applicationName}} v\u1EDBi {{provider}}"
      },
      subtitle: "Ch\xE0o m\u1EEBng! Vui l\xF2ng \u0111i\u1EC1n c\xE1c chi ti\u1EBFt \u0111\u1EC3 b\u1EAFt \u0111\u1EA7u.",
      subtitleCombined: "Ch\xE0o m\u1EEBng! Vui l\xF2ng \u0111i\u1EC1n c\xE1c chi ti\u1EBFt \u0111\u1EC3 b\u1EAFt \u0111\u1EA7u.",
      title: "T\u1EA1o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n",
      titleCombined: "T\u1EA1o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n"
    }
  },
  socialButtonsBlockButton: "Ti\u1EBFp t\u1EE5c v\u1EDBi {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: "{{provider|titleize}}",
  unstable__errors: {
    already_a_member_in_organization: "{{email}} \u0111\xE3 l\xE0 th\xE0nh vi\xEAn c\u1EE7a t\u1ED5 ch\u1EE9c.",
    captcha_invalid: void 0,
    captcha_unavailable: "\u0110\u0103ng k\xFD kh\xF4ng th\xE0nh c\xF4ng do l\u1ED7i bot. Vui l\xF2ng t\u1EA3i l\u1EA1i trang \u0111\u1EC3 th\u1EED l\u1EA1i ho\u1EB7c li\xEAn h\u1EC7 h\u1ED7 tr\u1EE3 \u0111\u1EC3 \u0111\u01B0\u1EE3c h\u1ED7 tr\u1EE3.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: void 0,
    form_identifier_exists__phone_number: void 0,
    form_identifier_exists__username: void 0,
    form_identifier_not_found: void 0,
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: void 0,
    form_param_format_invalid__phone_number: void 0,
    form_param_max_length_exceeded__first_name: void 0,
    form_param_max_length_exceeded__last_name: void 0,
    form_param_max_length_exceeded__name: void 0,
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n qu\xE1 ng\u1EAFn. N\xF3 ph\u1EA3i c\xF3 \xEDt nh\u1EA5t 8 k\xFD t\u1EF1.",
    form_password_not_strong_enough: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n kh\xF4ng \u0111\u1EE7 m\u1EA1nh.",
    form_password_pwned: "M\u1EADt kh\u1EA9u n\xE0y \u0111\xE3 \u0111\u01B0\u1EE3c t\xECm th\u1EA5y trong m\u1ED9t r\xF2 r\u1EC9 v\xE0 kh\xF4ng th\u1EC3 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng, vui l\xF2ng th\u1EED m\u1ED9t m\u1EADt kh\u1EA9u kh\xE1c.",
    form_password_pwned__sign_in: "M\u1EADt kh\u1EA9u n\xE0y \u0111\xE3 \u0111\u01B0\u1EE3c t\xECm th\u1EA5y trong m\u1ED9t r\xF2 r\u1EC9 v\xE0 kh\xF4ng th\u1EC3 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng, vui l\xF2ng \u0111\u1EB7t l\u1EA1i m\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n.",
    form_password_size_in_bytes_exceeded: void 0,
    form_password_validation_failed: void 0,
    form_username_invalid_character: void 0,
    form_username_invalid_length: "T\xEAn ng\u01B0\u1EDDi d\xF9ng c\u1EE7a b\u1EA1n ph\u1EA3i c\xF3 gi\u1EEFa {{min_length}} v\xE0 {{max_length}} k\xFD t\u1EF1.",
    identification_deletion_failed: void 0,
    not_allowed_access: void 0,
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: "M\xE3 passkey \u0111\xE3 \u0111\u01B0\u1EE3c \u0111\u0103ng k\xFD v\u1EDBi thi\u1EBFt b\u1ECB n\xE0y.",
    passkey_not_supported: "M\xE3 passkey kh\xF4ng \u0111\u01B0\u1EE3c h\u1ED7 tr\u1EE3 tr\xEAn thi\u1EBFt b\u1ECB n\xE0y.",
    passkey_pa_not_supported: "\u0110\u0103ng k\xFD y\xEAu c\u1EA7u m\u1ED9t b\u1ED9 x\xE1c th\u1EF1c n\u1EC1n t\u1EA3ng nh\u01B0ng thi\u1EBFt b\u1ECB kh\xF4ng h\u1ED7 tr\u1EE3 n\xF3.",
    passkey_registration_cancelled: "\u0110\u0103ng k\xFD m\xE3 passkey \u0111\xE3 b\u1ECB h\u1EE7y ho\u1EB7c h\u1EBFt h\u1EA1n.",
    passkey_retrieval_cancelled: "X\xE1c minh m\xE3 passkey \u0111\xE3 b\u1ECB h\u1EE7y ho\u1EB7c h\u1EBFt h\u1EA1n.",
    passwordComplexity: {
      maximumLength: "\xEDt h\u01A1n {{length}} k\xFD t\u1EF1",
      minimumLength: "{{length}} ho\u1EB7c nhi\u1EC1u h\u01A1n k\xFD t\u1EF1",
      requireLowercase: "m\u1ED9t ch\u1EEF c\xE1i vi\u1EBFt th\u01B0\u1EDDng",
      requireNumbers: "m\u1ED9t s\u1ED1",
      requireSpecialCharacter: "m\u1ED9t k\xFD t\u1EF1 \u0111\u1EB7c bi\u1EC7t",
      requireUppercase: "m\u1ED9t ch\u1EEF c\xE1i vi\u1EBFt hoa",
      sentencePrefix: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n ph\u1EA3i ch\u1EE9a"
    },
    phone_number_exists: void 0,
    session_exists: void 0,
    web3_missing_identifier: "Kh\xF4ng t\xECm th\u1EA5y ph\u1EA7n m\u1EDF r\u1ED9ng Web3 Wallet. Vui l\xF2ng c\xE0i \u0111\u1EB7t m\u1ED9t ph\u1EA7n m\u1EDF r\u1ED9ng \u0111\u1EC3 ti\u1EBFp t\u1EE5c.",
    zxcvbn: {
      couldBeStronger: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n ho\u1EA1t \u0111\u1ED9ng, nh\u01B0ng c\xF3 th\u1EC3 m\u1EA1nh h\u01A1n. H\xE3y th\u1EED th\xEAm nhi\u1EC1u k\xFD t\u1EF1.",
      goodPassword: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n \u0111\xE1p \u1EE9ng t\u1EA5t c\u1EA3 c\xE1c y\xEAu c\u1EA7u c\u1EA7n thi\u1EBFt.",
      notEnough: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n kh\xF4ng \u0111\u1EE7 m\u1EA1nh.",
      suggestions: {
        allUppercase: "Vi\u1EBFt hoa m\u1ED9t s\u1ED1 ch\u1EEF c\xE1i, nh\u01B0ng kh\xF4ng ph\u1EA3i t\u1EA5t c\u1EA3.",
        anotherWord: "Th\xEAm nhi\u1EC1u t\u1EEB kh\xE1c nhau h\u01A1n.",
        associatedYears: "Tr\xE1nh n\u0103m m\xE0 b\u1EA1n li\xEAn quan \u0111\u1EBFn.",
        capitalization: "Vi\u1EBFt hoa nhi\u1EC1u h\u01A1n ch\u1EEF c\xE1i \u0111\u1EA7u ti\xEAn.",
        dates: "Tr\xE1nh ng\xE0y v\xE0 n\u0103m m\xE0 b\u1EA1n li\xEAn quan \u0111\u1EBFn.",
        l33t: "Tr\xE1nh c\xE1c thay th\u1EBF ch\u1EEF c\xE1i d\u1EC5 d\u1EF1 \u0111o\xE1n nh\u01B0 '@' thay v\xEC 'a'.",
        longerKeyboardPattern: "S\u1EED d\u1EE5ng c\xE1c khu\xF4n m\u1EABu b\xE0n ph\xEDm d\xE0i h\u01A1n v\xE0 thay \u0111\u1ED5i h\u01B0\u1EDBng g\xF5 nhi\u1EC1u l\u1EA7n.",
        noNeed: "B\u1EA1n c\xF3 th\u1EC3 t\u1EA1o m\u1EADt kh\u1EA9u m\u1EA1nh m\xE0 kh\xF4ng s\u1EED d\u1EE5ng c\xE1c k\xFD t\u1EF1 \u0111\u1EB7c bi\u1EC7t, s\u1ED1 ho\u1EB7c ch\u1EEF c\xE1i vi\u1EBFt hoa.",
        pwned: "N\u1EBFu b\u1EA1n s\u1EED d\u1EE5ng m\u1EADt kh\u1EA9u n\xE0y \u1EDF n\u01A1i kh\xE1c, b\u1EA1n n\xEAn thay \u0111\u1ED5i n\xF3.",
        recentYears: "Tr\xE1nh n\u0103m g\u1EA7n \u0111\xE2y.",
        repeated: "Tr\xE1nh c\xE1c t\u1EEB v\xE0 k\xFD t\u1EF1 l\u1EB7p l\u1EA1i.",
        reverseWords: "Tr\xE1nh c\xE1c t\u1EEB vi\u1EBFt ng\u01B0\u1EE3c c\u1EE7a c\xE1c t\u1EEB th\xF4ng d\u1EE5ng.",
        sequences: "Tr\xE1nh c\xE1c chu\u1ED7i k\xFD t\u1EF1 th\xF4ng d\u1EE5ng.",
        useWords: "S\u1EED d\u1EE5ng nhi\u1EC1u t\u1EEB, nh\u01B0ng tr\xE1nh c\xE1c c\u1EE5m t\u1EEB th\xF4ng d\u1EE5ng."
      },
      warnings: {
        common: "\u0110\xE2y l\xE0 m\u1ED9t m\u1EADt kh\u1EA9u th\u01B0\u1EDDng \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng.",
        commonNames: "T\xEAn v\xE0 h\u1ECD th\u01B0\u1EDDng \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng.",
        dates: "Ng\xE0y th\u01B0\u1EDDng \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng.",
        extendedRepeat: 'C\xE1c m\u1EABu k\xFD t\u1EF1 l\u1EB7p l\u1EA1i nh\u01B0 "abcabcabc" d\u1EC5 d\u1EF1 \u0111o\xE1n.',
        keyPattern: "C\xE1c m\u1EABu b\xE0n ph\xEDm ng\u1EAFn d\u1EC5 d\u1EF1 \u0111o\xE1n.",
        namesByThemselves: "T\xEAn ho\u1EB7c h\u1ECD d\u1EC5 d\u1EF1 \u0111o\xE1n.",
        pwned: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n \u0111\xE3 b\u1ECB r\xF2 r\u1EC9 qua m\u1ED9t r\xF2 r\u1EC9 d\u1EEF li\u1EC7u tr\xEAn Internet.",
        recentYears: "N\u0103m g\u1EA7n \u0111\xE2y d\u1EC5 d\u1EF1 \u0111o\xE1n.",
        sequences: 'C\xE1c chu\u1ED7i k\xFD t\u1EF1 th\xF4ng d\u1EE5ng nh\u01B0 "abc" d\u1EC5 d\u1EF1 \u0111o\xE1n.',
        similarToCommon: "\u0110\xE2y l\xE0 t\u01B0\u01A1ng t\u1EF1 nh\u01B0 m\u1ED9t m\u1EADt kh\u1EA9u th\u01B0\u1EDDng \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng.",
        simpleRepeat: 'C\xE1c k\xFD t\u1EF1 l\u1EB7p l\u1EA1i nh\u01B0 "aaa" d\u1EC5 d\u1EF1 \u0111o\xE1n.',
        straightRow: "C\xE1c h\xE0ng k\xFD t\u1EF1 tr\xEAn b\xE0n ph\xEDm c\u1EE7a b\u1EA1n d\u1EC5 d\u1EF1 \u0111o\xE1n.",
        topHundred: "\u0110\xE2y l\xE0 m\u1ED9t m\u1EADt kh\u1EA9u th\u01B0\u1EDDng \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng.",
        topTen: "\u0110\xE2y l\xE0 m\u1ED9t m\u1EADt kh\u1EA9u th\u01B0\u1EDDng \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng.",
        userInputs: "Kh\xF4ng n\xEAn c\xF3 b\u1EA5t k\u1EF3 d\u1EEF li\u1EC7u c\xE1 nh\xE2n ho\u1EB7c li\xEAn quan \u0111\u1EBFn trang n\xE0o.",
        wordByItself: "C\xE1c t\u1EEB \u0111\u01A1n d\u1EC5 d\u1EF1 \u0111o\xE1n."
      }
    }
  },
  userButton: {
    action__addAccount: "Th\xEAm t\xE0i kho\u1EA3n",
    action__manageAccount: "Qu\u1EA3n l\xFD t\xE0i kho\u1EA3n",
    action__signOut: "\u0110\u0103ng xu\u1EA5t",
    action__signOutAll: "\u0110\u0103ng xu\u1EA5t t\u1EA5t c\u1EA3 t\xE0i kho\u1EA3n"
  },
  userProfile: {
    apiKeysPage: {
      title: "Kho\xE1 API"
    },
    backupCodePage: {
      actionLabel__copied: "\u0110\xE3 sao ch\xE9p!",
      actionLabel__copy: "Sao ch\xE9p t\u1EA5t c\u1EA3",
      actionLabel__download: "T\u1EA3i xu\u1ED1ng .txt",
      actionLabel__print: "In",
      infoText1: "M\xE3 sao l\u01B0u s\u1EBD \u0111\u01B0\u1EE3c b\u1EADt cho t\xE0i kho\u1EA3n n\xE0y.",
      infoText2: "Gi\u1EEF m\xE3 sao l\u01B0u b\xED m\u1EADt v\xE0 l\u01B0u tr\u1EEF ch\xFAng m\u1ED9t c\xE1ch an to\xE0n. B\u1EA1n c\xF3 th\u1EC3 t\u1EA1o l\u1EA1i m\xE3 sao l\u01B0u n\u1EBFu b\u1EA1n nghi ng\u1EDD ch\xFAng \u0111\xE3 b\u1ECB r\xF2 r\u1EC9.",
      subtitle__codelist: "L\u01B0u tr\u1EEF ch\xFAng m\u1ED9t c\xE1ch an to\xE0n v\xE0 gi\u1EEF b\xED m\u1EADt.",
      successMessage: "M\xE3 sao l\u01B0u \u0111\xE3 \u0111\u01B0\u1EE3c b\u1EADt. B\u1EA1n c\xF3 th\u1EC3 s\u1EED d\u1EE5ng m\u1ED9t trong s\u1ED1 ch\xFAng \u0111\u1EC3 \u0111\u0103ng nh\u1EADp v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n, n\u1EBFu b\u1EA1n m\u1EA5t quy\u1EC1n truy c\u1EADp v\xE0o thi\u1EBFt b\u1ECB x\xE1c th\u1EF1c c\u1EE7a b\u1EA1n. M\u1ED7i m\xE3 ch\u1EC9 c\xF3 th\u1EC3 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng m\u1ED9t l\u1EA7n.",
      successSubtitle: "B\u1EA1n c\xF3 th\u1EC3 s\u1EED d\u1EE5ng m\u1ED9t trong s\u1ED1 ch\xFAng \u0111\u1EC3 \u0111\u0103ng nh\u1EADp v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n, n\u1EBFu b\u1EA1n m\u1EA5t quy\u1EC1n truy c\u1EADp v\xE0o thi\u1EBFt b\u1ECB x\xE1c th\u1EF1c c\u1EE7a b\u1EA1n.",
      title: "Th\xEAm x\xE1c minh m\xE3 sao l\u01B0u",
      title__codelist: "M\xE3 sao l\u01B0u"
    },
    billingPage: {
      paymentHistorySection: {
        empty: "Kh\xF4ng c\xF3 l\u1ECBch s\u1EED thanh to\xE1n",
        notFound: "Kh\xF4ng t\xECm th\u1EA5y l\u1ECBch s\u1EED thanh to\xE1n",
        tableHeader__amount: "S\u1ED1 ti\u1EC1n",
        tableHeader__date: "Ng\xE0y",
        tableHeader__status: "Tr\u1EA1ng th\xE1i"
      },
      paymentSourcesSection: {
        actionLabel__default: "L\xE0m m\u1EB7c \u0111\u1ECBnh",
        actionLabel__remove: "X\xF3a",
        add: "Th\xEAm ph\u01B0\u01A1ng th\u1EE9c thanh to\xE1n m\u1EDBi",
        addSubtitle: "Th\xEAm m\u1ED9t ph\u01B0\u01A1ng th\u1EE9c thanh to\xE1n m\u1EDBi v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
        cancelButton: "H\u1EE7y",
        formButtonPrimary__add: "Th\xEAm ph\u01B0\u01A1ng th\u1EE9c thanh to\xE1n",
        formButtonPrimary__pay: "Thanh to\xE1n {{amount}}",
        payWithTestCardButton: "Thanh to\xE1n v\u1EDBi th\u1EBB th\u1EED",
        removeResource: {
          messageLine1: "{{identifier}} s\u1EBD b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n n\xE0y.",
          messageLine2: "B\u1EA1n s\u1EBD kh\xF4ng c\xF2n th\u1EC3 s\u1EED d\u1EE5ng ngu\u1ED3n thanh to\xE1n n\xE0y v\xE0 b\u1EA5t k\u1EF3 \u0111\u0103ng k\xFD l\u1EB7p l\u1EA1i n\xE0o ph\u1EE5 thu\u1ED9c v\xE0o n\xF3 s\u1EBD kh\xF4ng c\xF2n ho\u1EA1t \u0111\u1ED9ng.",
          successMessage: "{{paymentSource}} \u0111\xE3 b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
          title: "X\xF3a ph\u01B0\u01A1ng th\u1EE9c thanh to\xE1n"
        },
        title: "Ph\u01B0\u01A1ng th\u1EE9c thanh to\xE1n"
      },
      start: {
        headerTitle__payments: "Thanh to\xE1n",
        headerTitle__plans: "G\xF3i",
        headerTitle__statements: "B\xE1o c\xE1o",
        headerTitle__subscriptions: "\u0110\u0103ng k\xFD"
      },
      statementsSection: {
        empty: "Kh\xF4ng c\xF3 b\xE1o c\xE1o \u0111\u1EC3 hi\u1EC3n th\u1ECB",
        itemCaption__paidForPlan: "Thanh to\xE1n cho {{plan}} {{period}} g\xF3i",
        itemCaption__proratedCredit: "T\xEDn d\u1EE5ng ph\xE2n chia cho s\u1EED d\u1EE5ng m\u1ED9t ph\u1EA7n c\u1EE7a \u0111\u0103ng k\xFD tr\u01B0\u1EDBc",
        itemCaption__subscribedAndPaidForPlan: "\u0110\u0103ng k\xFD v\xE0 thanh to\xE1n cho {{plan}} {{period}} g\xF3i",
        notFound: "Kh\xF4ng t\xECm th\u1EA5y b\xE1o c\xE1o",
        tableHeader__amount: "S\u1ED1 ti\u1EC1n",
        tableHeader__date: "Ng\xE0y",
        title: "B\xE1o c\xE1o",
        totalPaid: "T\u1ED5ng thanh to\xE1n"
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: "\u0110\u0103ng k\xFD g\xF3i",
        actionLabel__switchPlan: "Chuy\u1EC3n g\xF3i",
        tableHeader__edit: "S\u1EEDa",
        tableHeader__plan: "G\xF3i",
        tableHeader__startDate: "Ng\xE0y b\u1EAFt \u0111\u1EA7u",
        title: "\u0110\u0103ng k\xFD"
      },
      subscriptionsSection: {
        actionLabel__default: "Qu\u1EA3n l\xFD"
      },
      switchPlansSection: {
        title: "Chuy\u1EC3n g\xF3i"
      },
      title: "Thanh to\xE1n"
    },
    connectedAccountPage: {
      formHint: "Ch\u1ECDn nh\xE0 cung c\u1EA5p \u0111\u1EC3 k\u1EBFt n\u1ED1i t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
      formHint__noAccounts: "Kh\xF4ng c\xF3 nh\xE0 cung c\u1EA5p t\xE0i kho\u1EA3n b\xEAn ngo\xE0i.",
      removeResource: {
        messageLine1: "{{identifier}} s\u1EBD b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n n\xE0y.",
        messageLine2: "B\u1EA1n s\u1EBD kh\xF4ng c\xF2n th\u1EC3 s\u1EED d\u1EE5ng t\xE0i kho\u1EA3n k\u1EBFt n\u1ED1i n\xE0y v\xE0 b\u1EA5t k\u1EF3 t\xEDnh n\u0103ng ph\u1EE5 thu\u1ED9c n\xE0o s\u1EBD kh\xF4ng c\xF2n ho\u1EA1t \u0111\u1ED9ng.",
        successMessage: "{{connectedAccount}} \u0111\xE3 b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
        title: "X\xF3a t\xE0i kho\u1EA3n k\u1EBFt n\u1ED1i"
      },
      socialButtonsBlockButton: "{{provider|titleize}}",
      successMessage: "Nh\xE0 cung c\u1EA5p \u0111\xE3 \u0111\u01B0\u1EE3c th\xEAm v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n",
      title: "Th\xEAm t\xE0i kho\u1EA3n k\u1EBFt n\u1ED1i"
    },
    deletePage: {
      actionDescription: 'Nh\u1EADp "X\xF3a t\xE0i kho\u1EA3n" d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 ti\u1EBFp t\u1EE5c.',
      confirm: "X\xF3a t\xE0i kho\u1EA3n",
      messageLine1: "B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n x\xF3a t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n?",
      messageLine2: "H\xE0nh \u0111\u1ED9ng n\xE0y l\xE0 v\u0129nh vi\u1EC5n v\xE0 kh\xF4ng th\u1EC3 ho\xE0n t\xE1c.",
      title: "X\xF3a t\xE0i kho\u1EA3n"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "M\u1ED9t email ch\u1EE9a m\xE3 x\xE1c th\u1EF1c s\u1EBD \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn \u0111\u1ECBa ch\u1EC9 email n\xE0y.",
        formSubtitle: "Nh\u1EADp m\xE3 x\xE1c th\u1EF1c \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn {{identifier}}",
        formTitle: "M\xE3 x\xE1c th\u1EF1c",
        resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
        successMessage: "Email {{identifier}} \u0111\xE3 \u0111\u01B0\u1EE3c th\xEAm v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n."
      },
      emailLink: {
        formHint: "M\u1ED9t email ch\u1EE9a li\xEAn k\u1EBFt x\xE1c th\u1EF1c s\u1EBD \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn \u0111\u1ECBa ch\u1EC9 email n\xE0y.",
        formSubtitle: "Nh\u1EA5p v\xE0o li\xEAn k\u1EBFt x\xE1c th\u1EF1c trong email \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn {{identifier}}",
        formTitle: "Li\xEAn k\u1EBFt x\xE1c th\u1EF1c",
        resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c li\xEAn k\u1EBFt? G\u1EEDi l\u1EA1i",
        successMessage: "Email {{identifier}} \u0111\xE3 \u0111\u01B0\u1EE3c th\xEAm v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n."
      },
      enterpriseSSOLink: {
        formButton: "Nh\u1EA5p \u0111\u1EC3 \u0111\u0103ng nh\u1EADp",
        formSubtitle: "Ho\xE0n th\xE0nh \u0111\u0103ng nh\u1EADp v\u1EDBi {{identifier}}"
      },
      formHint: "B\u1EA1n c\u1EA7n x\xE1c th\u1EF1c \u0111\u1ECBa ch\u1EC9 email n\xE0y tr\u01B0\u1EDBc khi n\xF3 c\xF3 th\u1EC3 \u0111\u01B0\u1EE3c th\xEAm v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
      removeResource: {
        messageLine1: "{{identifier}} s\u1EBD b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n n\xE0y.",
        messageLine2: "B\u1EA1n s\u1EBD kh\xF4ng c\xF2n th\u1EC3 \u0111\u0103ng nh\u1EADp b\u1EB1ng \u0111\u1ECBa ch\u1EC9 email n\xE0y.",
        successMessage: "{{emailAddress}} \u0111\xE3 b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
        title: "X\xF3a \u0111\u1ECBa ch\u1EC9 email"
      },
      title: "Th\xEAm \u0111\u1ECBa ch\u1EC9 email",
      verifyTitle: "X\xE1c th\u1EF1c \u0111\u1ECBa ch\u1EC9 email"
    },
    formButtonPrimary__add: "Th\xEAm",
    formButtonPrimary__continue: "Ti\u1EBFp t\u1EE5c",
    formButtonPrimary__finish: "Ho\xE0n th\xE0nh",
    formButtonPrimary__remove: "X\xF3a",
    formButtonPrimary__save: "L\u01B0u",
    formButtonReset: "H\u1EE7y",
    mfaPage: {
      formHint: "Ch\u1ECDn ph\u01B0\u01A1ng th\u1EE9c \u0111\u1EC3 th\xEAm.",
      title: "Th\xEAm x\xE1c th\u1EF1c hai b\u01B0\u1EDBc"
    },
    mfaPhoneCodePage: {
      backButton: "S\u1EED d\u1EE5ng s\u1ED1 \u0111i\u1EC7n tho\u1EA1i hi\u1EC7n c\xF3",
      primaryButton__addPhoneNumber: "Th\xEAm s\u1ED1 \u0111i\u1EC7n tho\u1EA1i",
      removeResource: {
        messageLine1: "{{identifier}} s\u1EBD kh\xF4ng c\xF2n nh\u1EADn \u0111\u01B0\u1EE3c m\xE3 x\xE1c th\u1EF1c khi \u0111\u0103ng nh\u1EADp.",
        messageLine2: "T\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n c\xF3 th\u1EC3 kh\xF4ng an to\xE0n h\u01A1n. B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n ti\u1EBFp t\u1EE5c?",
        successMessage: "X\xE1c th\u1EF1c m\xE3 SMS hai b\u01B0\u1EDBc \u0111\xE3 b\u1ECB x\xF3a cho {{mfaPhoneCode}}",
        title: "X\xF3a x\xE1c th\u1EF1c hai b\u01B0\u1EDBc"
      },
      subtitle__availablePhoneNumbers: "Ch\u1ECDn s\u1ED1 \u0111i\u1EC7n tho\u1EA1i hi\u1EC7n c\xF3 \u0111\u1EC3 \u0111\u0103ng k\xFD x\xE1c th\u1EF1c m\xE3 SMS hai b\u01B0\u1EDBc ho\u1EB7c th\xEAm s\u1ED1 \u0111i\u1EC7n tho\u1EA1i m\u1EDBi.",
      subtitle__unavailablePhoneNumbers: "Kh\xF4ng c\xF3 s\u1ED1 \u0111i\u1EC7n tho\u1EA1i n\xE0o \u0111\u1EC3 \u0111\u0103ng k\xFD x\xE1c th\u1EF1c m\xE3 SMS hai b\u01B0\u1EDBc, vui l\xF2ng th\xEAm s\u1ED1 \u0111i\u1EC7n tho\u1EA1i m\u1EDBi.",
      successMessage1: "Khi \u0111\u0103ng nh\u1EADp, b\u1EA1n s\u1EBD c\u1EA7n nh\u1EADp m\xE3 x\xE1c th\u1EF1c \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn s\u1ED1 \u0111i\u1EC7n tho\u1EA1i n\xE0y l\xE0m b\u01B0\u1EDBc th\xEAm.",
      successMessage2: "L\u01B0u c\xE1c m\xE3 sao l\u01B0u n\xE0y v\xE0 l\u01B0u tr\u1EEF ch\xFAng \u1EDF m\u1ED9t n\u01A1i an to\xE0n. N\u1EBFu b\u1EA1n m\u1EA5t quy\u1EC1n truy c\u1EADp v\xE0o thi\u1EBFt b\u1ECB x\xE1c th\u1EF1c c\u1EE7a b\u1EA1n, b\u1EA1n c\xF3 th\u1EC3 s\u1EED d\u1EE5ng m\xE3 sao l\u01B0u \u0111\u1EC3 \u0111\u0103ng nh\u1EADp.",
      successTitle: "X\xE1c th\u1EF1c m\xE3 SMS \u0111\xE3 \u0111\u01B0\u1EE3c b\u1EADt",
      title: "Th\xEAm x\xE1c th\u1EF1c m\xE3 SMS"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Qu\xE9t m\xE3 QR thay v\xEC",
        buttonUnableToScan__nonPrimary: "Kh\xF4ng th\u1EC3 qu\xE9t m\xE3 QR?",
        infoText__ableToScan: "Thi\u1EBFt l\u1EADp ph\u01B0\u01A1ng th\u1EE9c \u0111\u0103ng nh\u1EADp m\u1EDBi trong \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c c\u1EE7a b\u1EA1n v\xE0 qu\xE9t m\xE3 QR sau \u0111\u1EC3 k\u1EBFt n\u1ED1i v\u1EDBi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
        infoText__unableToScan: "Thi\u1EBFt l\u1EADp ph\u01B0\u01A1ng th\u1EE9c \u0111\u0103ng nh\u1EADp m\u1EDBi trong \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c c\u1EE7a b\u1EA1n v\xE0 nh\u1EADp kh\xF3a \u0111\u01B0\u1EE3c cung c\u1EA5p d\u01B0\u1EDBi \u0111\xE2y.",
        inputLabel__unableToScan1: "\u0110\u1EA3m b\u1EA3o m\xE3 th\u1EDDi gian ho\u1EB7c m\xE3 m\u1ED9t l\u1EA7n \u0111\xE3 \u0111\u01B0\u1EE3c b\u1EADt, sau \u0111\xF3 ho\xE0n th\xE0nh vi\u1EC7c k\u1EBFt n\u1ED1i t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
        inputLabel__unableToScan2: "Ngo\xE0i ra, n\u1EBFu \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c c\u1EE7a b\u1EA1n h\u1ED7 tr\u1EE3 TOTP URIs, b\u1EA1n c\u0169ng c\xF3 th\u1EC3 sao ch\xE9p URI \u0111\u1EA7y \u0111\u1EE7."
      },
      removeResource: {
        messageLine1: "M\xE3 x\xE1c th\u1EF1c t\u1EEB \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c n\xE0y s\u1EBD kh\xF4ng c\xF2n \u0111\u01B0\u1EE3c y\xEAu c\u1EA7u khi \u0111\u0103ng nh\u1EADp.",
        messageLine2: "T\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n c\xF3 th\u1EC3 kh\xF4ng an to\xE0n h\u01A1n. B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n ti\u1EBFp t\u1EE5c?",
        successMessage: "X\xE1c th\u1EF1c hai b\u01B0\u1EDBc qua \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c \u0111\xE3 b\u1ECB x\xF3a.",
        title: "X\xF3a x\xE1c th\u1EF1c hai b\u01B0\u1EDBc"
      },
      successMessage: "X\xE1c th\u1EF1c hai b\u01B0\u1EDBc \u0111\xE3 \u0111\u01B0\u1EE3c b\u1EADt. Khi \u0111\u0103ng nh\u1EADp, b\u1EA1n s\u1EBD c\u1EA7n nh\u1EADp m\xE3 x\xE1c th\u1EF1c t\u1EEB \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c n\xE0y l\xE0m b\u01B0\u1EDBc th\xEAm.",
      title: "Th\xEAm \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c",
      verifySubtitle: "Nh\u1EADp m\xE3 x\xE1c th\u1EF1c \u0111\u01B0\u1EE3c t\u1EA1o b\u1EDFi \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c c\u1EE7a b\u1EA1n",
      verifyTitle: "M\xE3 x\xE1c th\u1EF1c"
    },
    mobileButton__menu: "Menu",
    navbar: {
      account: "T\xE0i kho\u1EA3n",
      apiKeys: "Kho\xE1 API",
      billing: "Thanh to\xE1n",
      description: "Qu\u1EA3n l\xFD th\xF4ng tin t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
      security: "B\u1EA3o m\u1EADt",
      title: "T\xE0i kho\u1EA3n"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} s\u1EBD b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n n\xE0y.",
        title: "X\xF3a passkey"
      },
      subtitle__rename: "B\u1EA1n c\xF3 th\u1EC3 thay \u0111\u1ED5i t\xEAn passkey \u0111\u1EC3 d\u1EC5 d\xE0ng t\xECm ki\u1EBFm.",
      title__rename: "\u0110\u1ED5i t\xEAn Passkey"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "\u0110\u01B0\u1EE3c khuy\u1EBFn ngh\u1ECB \u0111\u0103ng xu\u1EA5t kh\u1ECFi t\u1EA5t c\u1EA3 c\xE1c thi\u1EBFt b\u1ECB kh\xE1c c\xF3 th\u1EC3 \u0111\xE3 s\u1EED d\u1EE5ng m\u1EADt kh\u1EA9u c\u0169 c\u1EE7a b\u1EA1n.",
      readonly: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n hi\u1EC7n kh\xF4ng th\u1EC3 \u0111\u01B0\u1EE3c ch\u1EC9nh s\u1EEDa v\xEC b\u1EA1n ch\u1EC9 c\xF3 th\u1EC3 \u0111\u0103ng nh\u1EADp th\xF4ng qua k\u1EBFt n\u1ED1i doanh nghi\u1EC7p.",
      successMessage__set: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n \u0111\xE3 \u0111\u01B0\u1EE3c thi\u1EBFt l\u1EADp.",
      successMessage__signOutOfOtherSessions: "T\u1EA5t c\u1EA3 c\xE1c thi\u1EBFt b\u1ECB kh\xE1c \u0111\xE3 \u0111\u01B0\u1EE3c \u0111\u0103ng xu\u1EA5t.",
      successMessage__update: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n \u0111\xE3 \u0111\u01B0\u1EE3c c\u1EADp nh\u1EADt.",
      title__set: "Thi\u1EBFt l\u1EADp m\u1EADt kh\u1EA9u",
      title__update: "C\u1EADp nh\u1EADt m\u1EADt kh\u1EA9u"
    },
    phoneNumberPage: {
      infoText: "M\u1ED9t tin nh\u1EAFn v\u0103n b\u1EA3n ch\u1EE9a m\xE3 x\xE1c th\u1EF1c s\u1EBD \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn s\u1ED1 \u0111i\u1EC7n tho\u1EA1i n\xE0y. C\xF3 th\u1EC3 \xE1p d\u1EE5ng t\u1EF7 l\u1EC7 ph\xED tin nh\u1EAFn v\xE0 d\u1EEF li\u1EC7u.",
      removeResource: {
        messageLine1: "{{identifier}} s\u1EBD b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n n\xE0y.",
        messageLine2: "B\u1EA1n s\u1EBD kh\xF4ng c\xF2n th\u1EC3 \u0111\u0103ng nh\u1EADp b\u1EB1ng s\u1ED1 \u0111i\u1EC7n tho\u1EA1i n\xE0y.",
        successMessage: "{{phoneNumber}} \u0111\xE3 b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
        title: "X\xF3a s\u1ED1 \u0111i\u1EC7n tho\u1EA1i"
      },
      successMessage: "{{identifier}} \u0111\xE3 \u0111\u01B0\u1EE3c th\xEAm v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
      title: "Th\xEAm s\u1ED1 \u0111i\u1EC7n tho\u1EA1i",
      verifySubtitle: "Nh\u1EADp m\xE3 x\xE1c th\u1EF1c \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn {{identifier}}",
      verifyTitle: "X\xE1c th\u1EF1c s\u1ED1 \u0111i\u1EC7n tho\u1EA1i"
    },
    plansPage: {
      title: "G\xF3i"
    },
    profilePage: {
      fileDropAreaHint: "K\xEDch th\u01B0\u1EDBc khuy\u1EBFn ngh\u1ECB 1:1, t\u1ED1i \u0111a 10MB.",
      imageFormDestructiveActionSubtitle: "X\xF3a",
      imageFormSubtitle: "T\u1EA3i l\xEAn",
      imageFormTitle: "\u1EA2nh h\u1ED3 s\u01A1",
      readonly: "Th\xF4ng tin h\u1ED3 s\u01A1 c\u1EE7a b\u1EA1n \u0111\xE3 \u0111\u01B0\u1EE3c cung c\u1EA5p th\xF4ng qua k\u1EBFt n\u1ED1i doanh nghi\u1EC7p v\xE0 kh\xF4ng th\u1EC3 \u0111\u01B0\u1EE3c ch\u1EC9nh s\u1EEDa.",
      successMessage: "H\u1ED3 s\u01A1 c\u1EE7a b\u1EA1n \u0111\xE3 \u0111\u01B0\u1EE3c c\u1EADp nh\u1EADt.",
      title: "C\u1EADp nh\u1EADt h\u1ED3 s\u01A1"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "\u0110\u0103ng xu\u1EA5t kh\u1ECFi thi\u1EBFt b\u1ECB",
        title: "Thi\u1EBFt b\u1ECB ho\u1EA1t \u0111\u1ED9ng"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "K\u1EBFt n\u1ED1i l\u1EA1i",
        actionLabel__reauthorize: "X\xE1c th\u1EF1c ngay",
        destructiveActionTitle: "X\xF3a",
        primaryButton: "K\u1EBFt n\u1ED1i t\xE0i kho\u1EA3n",
        subtitle__disconnected: "T\xE0i kho\u1EA3n n\xE0y \u0111\xE3 b\u1ECB ng\u1EAFt k\u1EBFt n\u1ED1i.",
        subtitle__reauthorize: "C\xE1c ph\u1EA1m vi \u0111\u01B0\u1EE3c y\xEAu c\u1EA7u \u0111\xE3 \u0111\u01B0\u1EE3c c\u1EADp nh\u1EADt, v\xE0 b\u1EA1n c\xF3 th\u1EC3 \u0111ang g\u1EB7p ph\u1EA3i ch\u1EE9c n\u0103ng gi\u1EDBi h\u1EA1n. Vui l\xF2ng x\xE1c th\u1EF1c l\u1EA1i \u1EE9ng d\u1EE5ng n\xE0y \u0111\u1EC3 tr\xE1nh b\u1EA5t k\u1EF3 v\u1EA5n \u0111\u1EC1 n\xE0o",
        title: "T\xE0i kho\u1EA3n k\u1EBFt n\u1ED1i"
      },
      dangerSection: {
        deleteAccountButton: "X\xF3a t\xE0i kho\u1EA3n",
        title: "X\xF3a t\xE0i kho\u1EA3n"
      },
      emailAddressesSection: {
        destructiveAction: "X\xF3a email",
        detailsAction__nonPrimary: "\u0110\u1EB7t l\xE0m ch\xEDnh",
        detailsAction__primary: "Ho\xE0n th\xE0nh x\xE1c th\u1EF1c",
        detailsAction__unverified: "X\xE1c th\u1EF1c",
        primaryButton: "Th\xEAm \u0111\u1ECBa ch\u1EC9 email",
        title: "\u0110\u1ECBa ch\u1EC9 email"
      },
      enterpriseAccountsSection: {
        title: "T\xE0i kho\u1EA3n doanh nghi\u1EC7p"
      },
      headerTitle__account: "Chi ti\u1EBFt h\u1ED3 s\u01A1",
      headerTitle__security: "B\u1EA3o m\u1EADt",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "T\u1EA1o l\u1EA1i",
          headerTitle: "M\xE3 sao l\u01B0u",
          subtitle__regenerate: "L\u1EA5y m\u1ED9t b\u1ED9 m\xE3 sao l\u01B0u m\u1EDBi v\xE0 an to\xE0n. C\xE1c m\xE3 sao l\u01B0u tr\u01B0\u1EDBc \u0111\xF3 s\u1EBD b\u1ECB x\xF3a v\xE0 kh\xF4ng th\u1EC3 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng.",
          title__regenerate: "T\u1EA1o l\u1EA1i m\xE3 sao l\u01B0u"
        },
        phoneCode: {
          actionLabel__setDefault: "\u0110\u1EB7t l\xE0m ch\xEDnh",
          destructiveActionLabel: "X\xF3a"
        },
        primaryButton: "Th\xEAm x\xE1c th\u1EF1c hai b\u01B0\u1EDBc",
        title: "X\xE1c th\u1EF1c hai b\u01B0\u1EDBc",
        totp: {
          destructiveActionTitle: "X\xF3a",
          headerTitle: "\u1EE8ng d\u1EE5ng x\xE1c th\u1EF1c"
        }
      },
      passkeysSection: {
        menuAction__destructive: "X\xF3a",
        menuAction__rename: "\u0110\u1ED5i t\xEAn",
        primaryButton: "Th\xEAm passkey",
        title: "Passkeys"
      },
      passwordSection: {
        primaryButton__setPassword: "Thi\u1EBFt l\u1EADp m\u1EADt kh\u1EA9u",
        primaryButton__updatePassword: "C\u1EADp nh\u1EADt m\u1EADt kh\u1EA9u",
        title: "M\u1EADt kh\u1EA9u"
      },
      phoneNumbersSection: {
        destructiveAction: "X\xF3a s\u1ED1 \u0111i\u1EC7n tho\u1EA1i",
        detailsAction__nonPrimary: "\u0110\u1EB7t l\xE0m ch\xEDnh",
        detailsAction__primary: "Ho\xE0n th\xE0nh x\xE1c th\u1EF1c",
        detailsAction__unverified: "X\xE1c th\u1EF1c s\u1ED1 \u0111i\u1EC7n tho\u1EA1i",
        primaryButton: "Th\xEAm s\u1ED1 \u0111i\u1EC7n tho\u1EA1i",
        title: "S\u1ED1 \u0111i\u1EC7n tho\u1EA1i"
      },
      profileSection: {
        primaryButton: "C\u1EADp nh\u1EADt h\u1ED3 s\u01A1",
        title: "H\u1ED3 s\u01A1"
      },
      usernameSection: {
        primaryButton__setUsername: "Thi\u1EBFt l\u1EADp t\xEAn ng\u01B0\u1EDDi d\xF9ng",
        primaryButton__updateUsername: "C\u1EADp nh\u1EADt t\xEAn ng\u01B0\u1EDDi d\xF9ng",
        title: "T\xEAn ng\u01B0\u1EDDi d\xF9ng"
      },
      web3WalletsSection: {
        destructiveAction: "X\xF3a v\xED",
        detailsAction__nonPrimary: "\u0110\u1EB7t l\xE0m ch\xEDnh",
        primaryButton: "K\u1EBFt n\u1ED1i v\xED",
        title: "V\xED Web3"
      }
    },
    usernamePage: {
      successMessage: "T\xEAn ng\u01B0\u1EDDi d\xF9ng c\u1EE7a b\u1EA1n \u0111\xE3 \u0111\u01B0\u1EE3c c\u1EADp nh\u1EADt.",
      title__set: "Thi\u1EBFt l\u1EADp t\xEAn ng\u01B0\u1EDDi d\xF9ng",
      title__update: "C\u1EADp nh\u1EADt t\xEAn ng\u01B0\u1EDDi d\xF9ng"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} s\u1EBD b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n n\xE0y.",
        messageLine2: "B\u1EA1n s\u1EBD kh\xF4ng c\xF2n th\u1EC3 \u0111\u0103ng nh\u1EADp b\u1EB1ng v\xED web3 n\xE0y.",
        successMessage: "{{web3Wallet}} \u0111\xE3 b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
        title: "X\xF3a v\xED web3"
      },
      subtitle__availableWallets: "Ch\u1ECDn m\u1ED9t v\xED web3 \u0111\u1EC3 k\u1EBFt n\u1ED1i v\u1EDBi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
      subtitle__unavailableWallets: "Kh\xF4ng c\xF3 v\xED web3 n\xE0o c\xF3 s\u1EB5n.",
      successMessage: "V\xED \u0111\xE3 \u0111\u01B0\u1EE3c th\xEAm v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
      title: "Th\xEAm v\xED web3",
      web3WalletButtonsBlockButton: "{{provider|titleize}}"
    }
  },
  waitlist: {
    start: {
      actionLink: "\u0110\u0103ng nh\u1EADp",
      actionText: "\u0110\xE3 c\xF3 quy\u1EC1n truy c\u1EADp?",
      formButton: "Tham gia danh s\xE1ch ch\u1EDD",
      subtitle: "Nh\u1EADp \u0111\u1ECBa ch\u1EC9 email c\u1EE7a b\u1EA1n v\xE0 ch\xFAng t\xF4i s\u1EBD th\xF4ng b\xE1o khi v\u1ECB tr\xED c\u1EE7a b\u1EA1n \u0111\xE3 s\u1EB5n s\xE0ng",
      title: "Tham gia danh s\xE1ch ch\u1EDD"
    },
    success: {
      message: "B\u1EA1n s\u1EBD \u0111\u01B0\u1EE3c chuy\u1EC3n h\u01B0\u1EDBng s\u1EDBm...",
      subtitle: "Ch\xFAng t\xF4i s\u1EBD li\xEAn h\u1EC7 khi v\u1ECB tr\xED c\u1EE7a b\u1EA1n \u0111\xE3 s\u1EB5n s\xE0ng",
      title: "C\u1EA3m \u01A1n b\u1EA1n \u0111\xE3 tham gia danh s\xE1ch ch\u1EDD!"
    }
  }
};
export {
  viVN
};
//# sourceMappingURL=vi-VN.mjs.map