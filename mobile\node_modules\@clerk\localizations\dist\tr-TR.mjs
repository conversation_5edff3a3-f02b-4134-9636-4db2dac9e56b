// src/tr-TR.ts
var trTR = {
  locale: "tr-TR",
  apiKeys: {
    action__add: void 0,
    action__search: void 0,
    createdAndExpirationStatus__expiresOn: void 0,
    createdAndExpirationStatus__never: void 0,
    detailsTitle__emptyRow: void 0,
    formButtonPrimary__add: void 0,
    formFieldCaption__expiration__expiresOn: void 0,
    formFieldCaption__expiration__never: void 0,
    formFieldOption__expiration__180d: void 0,
    formFieldOption__expiration__1d: void 0,
    formFieldOption__expiration__1y: void 0,
    formFieldOption__expiration__30d: void 0,
    formFieldOption__expiration__60d: void 0,
    formFieldOption__expiration__7d: void 0,
    formFieldOption__expiration__90d: void 0,
    formFieldOption__expiration__never: void 0,
    formHint: void 0,
    formTitle: void 0,
    lastUsed__days: void 0,
    lastUsed__hours: void 0,
    lastUsed__minutes: void 0,
    lastUsed__months: void 0,
    lastUsed__seconds: void 0,
    lastUsed__years: void 0,
    menuAction__revoke: void 0,
    revokeConfirmation: {
      confirmationText: void 0,
      formButtonPrimary__revoke: void 0,
      formHint: void 0,
      formTitle: void 0
    }
  },
  backButton: "Geri",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Varsay\u0131lan",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Di\u011Fer taklit eden cihaz",
  badge__primary: "Birincil",
  badge__renewsAt: void 0,
  badge__requiresAction: "Eylem gerekli",
  badge__startsAt: void 0,
  badge__thisDevice: "Bu cihaz",
  badge__unverified: "Do\u011Frulanmam\u0131\u015F",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Kullan\u0131c\u0131 cihaz\u0131",
  badge__you: "Siz",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      pastDueNotice: void 0,
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    credit: void 0,
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDue: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Olu\u015Ftur",
    invitePage: {
      formButtonReset: "Atla"
    },
    title: "Organizasyon olu\u015Ftur"
  },
  dates: {
    lastDay: "D\xFCn saat {{ date | timeString('tr-TR') }}",
    next6Days: "{{ date | weekday('tr-TR','long') }} saat {{ date | timeString('tr-TR') }}",
    nextDay: "Yar\u0131n saat {{ date | timeString('tr-TR') }}",
    numeric: "{{ date | numeric('tr-TR') }}",
    previous6Days: "Ge\xE7en hafta {{ date | weekday('tr-TR','long') }} saat {{ date | timeString('tr-TR') }}",
    sameDay: "Bug\xFCn saat {{ date | timeString('tr-TR') }}"
  },
  dividerText: "veya",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Ba\u015Fka bir y\xF6ntem kullan",
  footerPageLink__help: "Yard\u0131m",
  footerPageLink__privacy: "Gizlilik",
  footerPageLink__terms: "\u015Eartlar",
  formButtonPrimary: "\u0130leri",
  formButtonPrimary__verify: "Do\u011Frula",
  formFieldAction__forgotPassword: "\u015Eifremi unuttum",
  formFieldError__matchingPasswords: "\u015Eifreler e\u015Fle\u015Fmeli.",
  formFieldError__notMatchingPasswords: "\u015Eifreler e\u015Fle\u015Fmiyor.",
  formFieldError__verificationLinkExpired: "Do\u011Frulama ba\u011Flant\u0131s\u0131n\u0131n s\xFCresi doldu. L\xFCtfen yeni bir ba\u011Flant\u0131 isteyin.",
  formFieldHintText__optional: "\u0130ste\u011Fe ba\u011Fl\u0131",
  formFieldHintText__slug: "Slug, okunabilir bir ID'dir ve benzersiz olmal\u0131d\u0131r. Genellikle URL'lerde kullan\u0131l\u0131r.",
  formFieldInputPlaceholder__apiKeyDescription: void 0,
  formFieldInputPlaceholder__apiKeyExpirationDate: void 0,
  formFieldInputPlaceholder__apiKeyName: void 0,
  formFieldInputPlaceholder__backupCode: "Yedek kodu girin",
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Hesab\u0131 sil",
  formFieldInputPlaceholder__emailAddress: "<EMAIL>",
  formFieldInputPlaceholder__emailAddress_username: "email veya kullan\u0131c\u0131 ad\u0131",
  formFieldInputPlaceholder__emailAddresses: "<EMAIL>, <EMAIL>",
  formFieldInputPlaceholder__firstName: "Ad\u0131n\u0131z\u0131 girin",
  formFieldInputPlaceholder__lastName: "Soyad\u0131n\u0131z\u0131 girin",
  formFieldInputPlaceholder__organizationDomain: "Kurulu\u015F alan ad\u0131n\u0131 girin",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "Kurulu\u015F e-posta adresini girin",
  formFieldInputPlaceholder__organizationName: "Kurulu\u015F ad\u0131n\u0131 girin",
  formFieldInputPlaceholder__organizationSlug: "kurulus-adi",
  formFieldInputPlaceholder__password: "\u015Eifrenizi girin",
  formFieldInputPlaceholder__phoneNumber: "Telefon numaran\u0131z\u0131 girin",
  formFieldInputPlaceholder__username: "Kullan\u0131c\u0131 ad\u0131n\u0131z\u0131 girin",
  formFieldLabel__apiKeyDescription: void 0,
  formFieldLabel__apiKeyExpiration: void 0,
  formFieldLabel__apiKeyName: void 0,
  formFieldLabel__automaticInvitations: "Bu alan ad\u0131 i\xE7in otomatik davetleri etkinle\u015Ftir",
  formFieldLabel__backupCode: "Yedekleme kodu",
  formFieldLabel__confirmDeletion: "Onayla",
  formFieldLabel__confirmPassword: "\u015Eifreyi onayla",
  formFieldLabel__currentPassword: "Mevcut \u015Fifre",
  formFieldLabel__emailAddress: "E-posta adresi",
  formFieldLabel__emailAddress_username: "E-posta adresi veya kullan\u0131c\u0131 ad\u0131",
  formFieldLabel__emailAddresses: "E-posta adresleri",
  formFieldLabel__firstName: "Ad",
  formFieldLabel__lastName: "Soyad",
  formFieldLabel__newPassword: "Yeni \u015Fifre",
  formFieldLabel__organizationDomain: "Alan ad\u0131",
  formFieldLabel__organizationDomainDeletePending: "Bekleyen davetiyeleri ve \xF6nerileri sil",
  formFieldLabel__organizationDomainEmailAddress: "Do\u011Frulama e-posta adresi",
  formFieldLabel__organizationDomainEmailAddressDescription: "Bu alan ad\u0131 i\xE7in bir e-posta adresi girin, do\u011Frulama kodunu al\u0131n ve alan ad\u0131n\u0131 do\u011Frulay\u0131n.",
  formFieldLabel__organizationName: "Organizasyon ad\u0131",
  formFieldLabel__organizationSlug: "Organizasyon ba\u011Flant\u0131 metni",
  formFieldLabel__passkeyName: "Ge\xE7i\u015F anahtar\u0131n\u0131n ad\u0131",
  formFieldLabel__password: "\u015Eifre",
  formFieldLabel__phoneNumber: "Telefon numaras\u0131",
  formFieldLabel__role: "Rol",
  formFieldLabel__signOutOfOtherSessions: "Di\u011Fer cihazlardaki oturumlardan \xE7\u0131k",
  formFieldLabel__username: "Kullan\u0131c\u0131 ad\u0131",
  impersonationFab: {
    action__signOut: "\xC7\u0131k\u0131\u015F yap",
    title: "{{identifier}} olarak giri\u015F yap\u0131ld\u0131"
  },
  maintenanceMode: "\u015Eu anda bak\u0131mday\u0131z, ancak endi\u015Felenmeyin, k\u0131sa bir s\xFCre i\xE7inde tamamlanacakt\u0131r.",
  membershipRole__admin: "Y\xF6netici",
  membershipRole__basicMember: "\xDCye",
  membershipRole__guestMember: "Misafir",
  organizationList: {
    action__createOrganization: "Organizasyon olu\u015Ftur",
    action__invitationAccept: "Kat\u0131l",
    action__suggestionsAccept: "Kat\u0131lmak i\xE7in talepte bulun",
    createOrganization: "Organizasyon Olu\u015Ftur",
    invitationAcceptedLabel: "Kat\u0131l\u0131m ba\u015Far\u0131l\u0131",
    subtitle: "{{applicationName}} ile devam edin",
    suggestionsAcceptedLabel: "Onay bekleniyor",
    title: "Bir hesap se\xE7in",
    titleWithoutPersonal: "Bir organizasyon se\xE7in"
  },
  organizationProfile: {
    apiKeysPage: {
      title: void 0
    },
    badge__automaticInvitation: "Otomatik davetler",
    badge__automaticSuggestion: "Otomatik \xF6neriler",
    badge__manualInvitation: "Davetler",
    badge__unverified: "Do\u011Frulanmam\u0131\u015F",
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Alan ad\u0131n\u0131 ekleyerek do\u011Frulama yap\u0131n. Bu alana sahip e-posta adresleri ile kaydolan kullan\u0131c\u0131lar organizasyona otomatik olarak kat\u0131labilir veya kat\u0131lma talebi g\xF6nderebilir.",
      title: "Alan ad\u0131 ekle"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Davetiyeler g\xF6nderilemedi. A\u015Fa\u011F\u0131daki e-posta adresleri i\xE7in zaten bekleyen davetler var: {{email_addresses}}.",
      formButtonPrimary__continue: "Davetiye g\xF6nder",
      selectDropdown__role: "Rol se\xE7in",
      subtitle: "Bo\u015Fluk veya virg\xFClle ay\u0131rarak bir veya daha fazla e-posta adresi girin veya yap\u0131\u015Ft\u0131r\u0131n.",
      successMessage: "Davetler ba\u015Far\u0131yla g\xF6nderildi",
      title: "Yeni \xFCyeler davet edin"
    },
    membersPage: {
      action__invite: "Davet et",
      action__search: "Ara",
      activeMembersTab: {
        menuAction__remove: "\xDCyeyi kald\u0131r",
        tableHeader__actions: "\u0130\u015Flemler",
        tableHeader__joined: "Kat\u0131lma tarihi",
        tableHeader__role: "Rol",
        tableHeader__user: "Kullan\u0131c\u0131"
      },
      detailsTitle__emptyRow: "G\xF6r\xFCnt\xFClenecek \xFCye yok",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Organizasyonunuzla ili\u015Fkili bir alan ad\u0131 uzant\u0131s\u0131na sahip e-posta adresini girerek kullan\u0131c\u0131lar\u0131 otomatik olarak davet edin. Bu alana sahip e-posta ile kaydolan herkes, istedi\u011Fi zaman organizasyona kat\u0131labilir.",
          headerTitle: "Otomatik davetler",
          primaryButton: "Do\u011Frulanm\u0131\u015F alan adlar\u0131n\u0131 y\xF6net"
        },
        table__emptyRow: "G\xF6sterilecek davetiye yok"
      },
      invitedMembersTab: {
        menuAction__revoke: "Daveti iptal et",
        tableHeader__invited: "Davet Edilenler"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "E\u015Fle\u015Fen alan ad\u0131 uzant\u0131s\u0131na sahip e-posta ile kaydolan kullan\u0131c\u0131lar, kat\u0131lma talebi g\xF6nderebilmek i\xE7in otomatik \xF6neri alacaklar.",
          headerTitle: "Otomatik \xF6neriler",
          primaryButton: "Do\u011Frulanm\u0131\u015F alan adlar\u0131n\u0131 y\xF6net"
        },
        menuAction__approve: "Onayla",
        menuAction__reject: "Reddet",
        tableHeader__requested: "Kat\u0131lma iste\u011Fi",
        table__emptyRow: "G\xF6r\xFCnt\xFClenecek herhangi bir istek yok"
      },
      start: {
        headerTitle__invitations: "Davetler",
        headerTitle__members: "\xDCyeler",
        headerTitle__requests: "\u0130stekler"
      }
    },
    navbar: {
      apiKeys: void 0,
      billing: void 0,
      description: "Organizasyonunuzu y\xF6netin.",
      general: "Genel",
      members: "\xDCyeler",
      title: "Organizasyon"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: "Devam etmek i\xE7in a\u015Fa\u011F\u0131ya \u201C{{organizationName}}\u201D yaz\u0131n.",
          messageLine1: "Bu organizasyonu silmek istedi\u011Finizden emin misiniz?",
          messageLine2: "Bu i\u015Flem kal\u0131c\u0131d\u0131r ve geri al\u0131namaz.",
          successMessage: "Organizasyonu sildiniz.",
          title: "Organizasyonu sil"
        },
        leaveOrganization: {
          actionDescription: "Devam etmek i\xE7in a\u015Fa\u011F\u0131ya \u201C{{organizationName}}\u201D yaz\u0131n.",
          messageLine1: "Bu organizasyondan ayr\u0131lmak istedi\u011Finizden emin misiniz? Bu organizasyona ve uygulamalar\u0131na eri\u015Fiminizi kaybedeceksiniz.",
          messageLine2: "Bu i\u015Flem kal\u0131c\u0131d\u0131r ve geri al\u0131namaz.",
          successMessage: "Organizasyondan ayr\u0131ld\u0131n\u0131z.",
          title: "Organizasyondan ayr\u0131l"
        },
        title: "Tehlike"
      },
      domainSection: {
        menuAction__manage: "Y\xF6net",
        menuAction__remove: "Sil",
        menuAction__verify: "Do\u011Frula",
        primaryButton: "Alan ad\u0131 ekle",
        subtitle: "Kullan\u0131c\u0131lar\u0131n organizasyona otomatik olarak kat\u0131lmas\u0131na veya do\u011Frulanm\u0131\u015F bir alan ad\u0131 uzant\u0131l\u0131 e-posta ile kat\u0131lma iste\u011Finde bulunmas\u0131na izin verin.",
        title: "Do\u011Frulanm\u0131\u015F alan adlar\u0131"
      },
      successMessage: "Organizasyon g\xFCncellendi.",
      title: "Organizasyon profili"
    },
    removeDomainPage: {
      messageLine1: "{{domain}} Alan ad\u0131 kald\u0131r\u0131lacakt\u0131r.",
      messageLine2: "Kullan\u0131c\u0131lar bundan sonra organizasyona otomatik olarak kat\u0131lamayacaklar.",
      successMessage: "{{domain}} kald\u0131r\u0131ld\u0131.",
      title: "Alan ad\u0131n\u0131 kald\u0131r"
    },
    start: {
      headerTitle__general: "Genel",
      headerTitle__members: "\xDCyeler",
      profileSection: {
        primaryButton: "G\xFCncelle",
        title: "Organizasyon Profili",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Bu alan ad\u0131n\u0131 kald\u0131rmak, davet edilmi\u015F kullan\u0131c\u0131lar\u0131 etkileyecektir.",
        removeDomainActionLabel__remove: "Alan ad\u0131n\u0131 kald\u0131r",
        removeDomainSubtitle: "Bu alan ad\u0131n\u0131 do\u011Frulanm\u0131\u015F alan adlar\u0131n\u0131zdan kald\u0131r\u0131n",
        removeDomainTitle: "Alan ad\u0131n\u0131 kald\u0131r"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Kullan\u0131c\u0131lar, kay\u0131t olduklar\u0131nda otomatik olarak organizasyona davet edilir ve istedikleri zaman kat\u0131labilirler.",
        automaticInvitationOption__label: "Otomatik davetler",
        automaticSuggestionOption__description: "Kullan\u0131c\u0131lar, kat\u0131lma talebinde bulunmak i\xE7in otomatik \xF6neri al\u0131rlar; ancak organizasyona kat\u0131labilmeleri i\xE7in bir y\xF6netici onay\u0131 gereklidir.",
        automaticSuggestionOption__label: "Otomatik \xF6neriler",
        calloutInfoLabel: "Kay\u0131t t\xFCr\xFCn\xFCn de\u011Fi\u015Ftirilmesi yaln\u0131zca yeni kullan\u0131c\u0131lar\u0131 etkileyecektir.",
        calloutInvitationCountLabel: "Kullan\u0131c\u0131lara g\xF6nderilen ve bekleyen davetler: {{count}}",
        calloutSuggestionCountLabel: "Kullan\u0131c\u0131lara g\xF6nderilen ve bekleyen \xF6neriler: {{count}}",
        manualInvitationOption__description: "Kullan\u0131c\u0131lar organizasyona sadece manuel olarak davet edilebilir.",
        manualInvitationOption__label: "Manuel davet",
        subtitle: "Bu alan ad\u0131 alt\u0131ndaki kullan\u0131c\u0131lar\u0131n organizasyona nas\u0131l kat\u0131lacaklar\u0131n\u0131 se\xE7in."
      },
      start: {
        headerTitle__danger: "Tehlike",
        headerTitle__enrollment: "Kay\u0131t se\xE7enekleri"
      },
      subtitle: "Alan ad\u0131 {{domain}} \u015Fimdi do\u011Fruland\u0131. Devam etmek i\xE7in kay\u0131t modunu se\xE7in.",
      title: "{{domain}} adl\u0131 alan ad\u0131n\u0131 g\xFCncelle"
    },
    verifyDomainPage: {
      formSubtitle: "E-posta adresinize g\xF6nderilen do\u011Frulama kodunu girin",
      formTitle: "Do\u011Frulama kodu",
      resendButton: "Kod almad\u0131n\u0131z m\u0131? Tekrar g\xF6nderin",
      subtitle: "{{domainName}} Alan ad\u0131n\u0131n e-posta ile do\u011Frulanmas\u0131 gerekiyor.",
      subtitleVerificationCodeScreen: "{{emailAddress}} adresine bir do\u011Frulama kodu g\xF6nderildi. Devam etmek i\xE7in kodu giriniz.",
      title: "Alan ad\u0131n\u0131 do\u011Frula"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Organizasyon olu\u015Ftur",
    action__invitationAccept: "Kat\u0131l",
    action__manageOrganization: "Organizasyonu y\xF6net",
    action__suggestionsAccept: "Kat\u0131lmak i\xE7in talepte bulun",
    notSelected: "Organizasyon se\xE7ilmedi",
    personalWorkspace: "Ki\u015Fisel \xC7al\u0131\u015Fma Alan\u0131",
    suggestionsAcceptedLabel: "Onay bekleniyor"
  },
  paginationButton__next: "\u0130leri",
  paginationButton__previous: "Geri",
  paginationRowText__displaying: "Sayfa bilgisi:",
  paginationRowText__of: "-",
  reverification: {
    alternativeMethods: {
      actionLink: "Yard\u0131m almak i\xE7in buraya t\u0131klay\u0131n",
      actionText: "Alternatif do\u011Frulama y\xF6ntemlerini kullanmak ister misiniz?",
      blockButton__backupCode: "Yedek kodu kullan",
      blockButton__emailCode: "E-posta kodu g\xF6nder",
      blockButton__passkey: void 0,
      blockButton__password: "\u015Eifreyi gir",
      blockButton__phoneCode: "Telefon kodu g\xF6nder",
      blockButton__totp: "TOTP kodu kullan",
      getHelp: {
        blockButton__emailSupport: "E-posta ile destek al\u0131n",
        content: "Hala yard\u0131ma ihtiyac\u0131n\u0131z varsa, destek ekibimizle ileti\u015Fime ge\xE7in.",
        title: "Yard\u0131m Al"
      },
      subtitle: "Do\u011Frulama i\u015Flemi i\xE7in alternatif y\xF6ntemler.",
      title: "Alternatif Do\u011Frulama Y\xF6ntemleri"
    },
    backupCodeMfa: {
      subtitle: "Yedek kodu girerek do\u011Frulaman\u0131z\u0131 tamamlayabilirsiniz.",
      title: "Yedek Kod ile Do\u011Frulama"
    },
    emailCode: {
      formTitle: "E-posta ile Do\u011Frulama",
      resendButton: "Tekrar g\xF6nder",
      subtitle: "E-posta adresinize g\xF6nderilen do\u011Frulama kodunu girin.",
      title: "E-posta Kodunu Girin"
    },
    noAvailableMethods: {
      message: "Alternatif do\u011Frulama y\xF6ntemleri mevcut de\u011Fil.",
      subtitle: "L\xFCtfen farkl\u0131 bir do\u011Frulama y\xF6ntemi deneyin.",
      title: "Do\u011Frulama Y\xF6ntemleri Yok"
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "\u015Eifremi unuttum",
      subtitle: "\u015Eifrenizi girerek devam edebilirsiniz.",
      title: "\u015Eifre ile Giri\u015F Yap\u0131n"
    },
    phoneCode: {
      formTitle: "Telefon Kodu ile Do\u011Frulama",
      resendButton: "Telefon kodunu tekrar g\xF6nder",
      subtitle: "Telefonunuza g\xF6nderilen do\u011Frulama kodunu girin.",
      title: "Telefon Kodu Girin"
    },
    phoneCodeMfa: {
      formTitle: "Telefon Koduyla MFA Do\u011Frulamas\u0131",
      resendButton: "Telefon kodunu tekrar g\xF6nder",
      subtitle: "Telefonunuza g\xF6nderilen do\u011Frulama kodunu girin.",
      title: "Telefon Koduyla Do\u011Frulama"
    },
    totpMfa: {
      formTitle: "TOTP ile MFA Do\u011Frulamas\u0131",
      subtitle: "Mobil uygulaman\u0131zdan TOTP kodunu girin.",
      title: "TOTP Kodunu Girin"
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Hesap ekle",
      action__signOutAll: "Mevcut t\xFCm oturumlar\u0131 kapat\u0131n",
      subtitle: "Devam etmek istedi\u011Finiz hesab\u0131 se\xE7in.",
      title: "Bir hesap se\xE7iniz"
    },
    alternativeMethods: {
      actionLink: "Yard\u0131m al",
      actionText: "Bunlardan hi\xE7biri yok mu?",
      blockButton__backupCode: "Yedekleme kodu kullan",
      blockButton__emailCode: "{{identifier}} adresine do\u011Frulama kodu g\xF6nder",
      blockButton__emailLink: "{{identifier}} adresine do\u011Frulama ba\u011Flant\u0131s\u0131 g\xF6nder",
      blockButton__passkey: "Ge\xE7i\u015F anahtar\u0131n\u0131zla oturum a\xE7\u0131n",
      blockButton__password: "\u015Eifreyle giri\u015F yap",
      blockButton__phoneCode: "{{identifier}} numaras\u0131na do\u011Frulama kodu g\xF6nder",
      blockButton__totp: "Authenticator uygulamas\u0131 kullan",
      getHelp: {
        blockButton__emailSupport: "E-posta deste\u011Fi",
        content: "E\u011Fer hesab\u0131n\u0131za giri\u015F yapmakta zorluk ya\u015F\u0131yorsan\u0131z, hesab\u0131n\u0131za eri\u015Fiminizi sa\u011Flayabilmemiz i\xE7in bize bir e-posta g\xF6nderin ve size yard\u0131mc\u0131 olal\u0131m.",
        title: "Yard\u0131m al"
      },
      subtitle: "Sorunlarla m\u0131 kar\u015F\u0131la\u015F\u0131yorsunuz? Oturum a\xE7mak i\xE7in bu y\xF6ntemlerden birini deneyebilirsiniz.",
      title: "Farkl\u0131 bir y\xF6ntem kullan"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "{{applicationName}} ile devam etmek i\xE7in",
      title: "Yedekleme kodu giri\u015Fi"
    },
    emailCode: {
      formTitle: "Do\u011Frulama kodu",
      resendButton: "Kodu tekrar g\xF6nder",
      subtitle: "{{applicationName}} ile devam etmek i\xE7in",
      title: "E-posta kutunuzu kontrol edin"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Devam etmek i\xE7in, oturum a\xE7ma i\u015Flemini ba\u015Flatt\u0131\u011F\u0131n\u0131z cihaz ve taray\u0131c\u0131daki do\u011Frulama ba\u011Flant\u0131s\u0131n\u0131 a\xE7\u0131n",
        title: "Do\u011Frulama ba\u011Flant\u0131s\u0131 bu cihaz i\xE7in ge\xE7ersiz"
      },
      expired: {
        subtitle: "Devam etmek i\xE7in en ba\u015Ftaki sekmeye d\xF6n\xFCn",
        title: "Bu do\u011Frulama ba\u011Flant\u0131s\u0131n\u0131n s\xFCresi dolmu\u015F"
      },
      failed: {
        subtitle: "Devam etmek i\xE7in en ba\u015Ftaki sekmeye d\xF6n\xFCn",
        title: "Bu do\u011Frulama ba\u011Flant\u0131s\u0131 ge\xE7ersiz"
      },
      formSubtitle: "E-posta adresinize g\xF6nderdi\u011Fimiz do\u011Frulama ba\u011Flant\u0131s\u0131na t\u0131klay\u0131n",
      formTitle: "Do\u011Frulama ba\u011Flant\u0131s\u0131",
      loading: {
        subtitle: "Birazdan yeniden y\xF6nlendirileceksiniz",
        title: "Giri\u015F yap\u0131l\u0131yor..."
      },
      resendButton: "Tekrar g\xF6nder",
      subtitle: "{{applicationName}} ile devam etmek i\xE7in",
      title: "E-posta kutunuzu kontrol edin",
      unusedTab: {
        title: "Bu sekmeyi kapatabilirsiniz"
      },
      verified: {
        subtitle: "Birazdan yeniden y\xF6nlendirileceksiniz",
        title: "Ba\u015Far\u0131yla giri\u015F yap\u0131ld\u0131"
      },
      verifiedSwitchTab: {
        subtitle: "Devam etmek i\xE7in en ba\u015Ftaki sekmeye d\xF6n\xFCn",
        subtitleNewTab: "Devam etmek i\xE7in yeni a\xE7\u0131lm\u0131\u015F sekmeye d\xF6n\xFCn",
        titleNewTab: "Farkl\u0131 bir sekmede giri\u015F yap\u0131ld\u0131"
      }
    },
    forgotPassword: {
      formTitle: "\u015Eifre s\u0131f\u0131rlama kodu",
      resendButton: "Tekrar g\xF6nder",
      subtitle: "\u015Eifrenizi s\u0131f\u0131rlamak i\xE7in",
      subtitle_email: "\u0130lk olarak, e-posta kimli\u011Finize g\xF6nderilen kodu girin",
      subtitle_phone: "\u0130lk olarak, telefonunuza g\xF6nderilen kodu girin",
      title: "\u015Eifreyi s\u0131f\u0131rla"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "\u015Eifremi s\u0131f\u0131rla",
      label__alternativeMethods: "Veya ba\u015Fka bir y\xF6ntem kullan\u0131n:",
      title: "\u015Eifremi unuttum"
    },
    noAvailableMethods: {
      message: "Hesab\u0131n\u0131zda giri\u015F yapmak i\xE7in kullanabilece\u011Finiz bir y\xF6ntem bulunmuyor.",
      subtitle: "Bir hata olu\u015Ftu",
      title: "Giri\u015F yap\u0131lam\u0131yor"
    },
    passkey: {
      subtitle: "Ge\xE7i\u015F anahtar\u0131n\u0131z\u0131 kullanarak siz oldu\u011Funuzu onaylay\u0131n. Cihaz\u0131n\u0131z parmak izinizi, y\xFCz\xFCn\xFCz\xFC veya ekran kilidinizi isteyebilir.",
      title: "Ge\xE7i\u015F anahtar\u0131n\u0131z\u0131 kullan\u0131n"
    },
    password: {
      actionLink: "Ba\u015Fka bir y\xF6ntem kullan",
      subtitle: "{{applicationName}} ile devam etmek i\xE7in",
      title: "\u015Eifrenizi girin"
    },
    passwordPwned: {
      title: "\u015Eifre ele ge\xE7irildi"
    },
    phoneCode: {
      formTitle: "Do\u011Frulama kodu",
      resendButton: "Kod almad\u0131n\u0131z m\u0131? Tekrar g\xF6nderin",
      subtitle: "{{applicationName}} ile devam etmek i\xE7in",
      title: "Telefonuza g\xF6nderilen kodu girin"
    },
    phoneCodeMfa: {
      formTitle: "Do\u011Frulama kodu",
      resendButton: "Kod almad\u0131n\u0131z m\u0131? Tekrar g\xF6nderin",
      subtitle: "Devam etmek i\xE7in l\xFCtfen telefonunuza g\xF6nderilen do\u011Frulama kodunu girin",
      title: "Telefonuza g\xF6nderilen kodu girin"
    },
    resetPassword: {
      formButtonPrimary: "\u015Eifremi s\u0131f\u0131rla",
      requiredMessage: "G\xFCvenlik nedeniyle \u015Fifrenizi s\u0131f\u0131rlaman\u0131z gerekmektedir.",
      successMessage: "\u015Eifreniz ba\u015Far\u0131yla de\u011Fi\u015Ftirildi. Oturumunuz a\xE7\u0131l\u0131yor, l\xFCtfen biraz bekleyin.",
      title: "\u015Eifre s\u0131f\u0131rlama"
    },
    resetPasswordMfa: {
      detailsLabel: "\u015Eifrenizi s\u0131f\u0131rlamadan \xF6nce kimli\u011Finizi do\u011Frulamam\u0131z gerekiyor."
    },
    start: {
      actionLink: "Kay\u0131t ol",
      actionLink__join_waitlist: "Bekleme listesine kat\u0131l",
      actionLink__use_email: "E-posta kullan",
      actionLink__use_email_username: "E-posta veya kullan\u0131c\u0131 ad\u0131 kullan",
      actionLink__use_passkey: "Bunun yerine ge\xE7i\u015F anahtar\u0131n\u0131 kullan\u0131n",
      actionLink__use_phone: "Telefon kullan",
      actionLink__use_username: "Kullan\u0131c\u0131 ad\u0131 kullan",
      actionText: "Hesab\u0131n\u0131z yok mu?",
      actionText__join_waitlist: "Bekleme listesine kat\u0131l\u0131n",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "{{applicationName}} ile devam etmek i\xE7in",
      subtitleCombined: void 0,
      title: "Giri\u015F yap",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Do\u011Frulama kodu",
      subtitle: "Devam etmek i\xE7in l\xFCtfen kimlik do\u011Frulay\u0131c\u0131 uygulaman\u0131z taraf\u0131ndan olu\u015Fturulan do\u011Frulama kodunu girin",
      title: "\u0130ki a\u015Famal\u0131 do\u011Frulama"
    }
  },
  signInEnterPasswordTitle: "\u015Eifrenizi girin",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Giri\u015F yap",
      actionText: "Hesab\u0131n\u0131z var m\u0131?",
      subtitle: "{{applicationName}} ile devam etmek i\xE7in",
      title: "Eksik bilgileri tamamlay\u0131n"
    },
    emailCode: {
      formSubtitle: "E-posta adresinize g\xF6nderdi\u011Fimiz do\u011Frulama kodunu giriniz",
      formTitle: "Do\u011Frulama kodu",
      resendButton: "Kodu tekrar g\xF6nder",
      subtitle: "{{applicationName}} ile devam etmek i\xE7in",
      title: "E-posta adresinizi do\u011Frulay\u0131n"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Devam etmek i\xE7in, kay\u0131t i\u015Flemini ba\u015Flatt\u0131\u011F\u0131n\u0131z cihazda ve taray\u0131c\u0131da do\u011Frulama ba\u011Flant\u0131s\u0131n\u0131 a\xE7\u0131n",
        title: "Do\u011Frulama ba\u011Flant\u0131s\u0131 bu cihaz i\xE7in ge\xE7ersiz"
      },
      formSubtitle: "E-posta adresinize g\xF6nderdi\u011Fimiz do\u011Frulama ba\u011Flant\u0131s\u0131na t\u0131klay\u0131n",
      formTitle: "Do\u011Frulama ba\u011Flant\u0131s\u0131",
      loading: {
        title: "Giri\u015F yap\u0131l\u0131yor..."
      },
      resendButton: "Ba\u011Flant\u0131 almad\u0131n\u0131z m\u0131? Tekrar g\xF6nder",
      subtitle: "{{applicationName}} ile devam etmek i\xE7in",
      title: "E-posta adresinizi do\u011Frulay\u0131n",
      verified: {
        title: "Ba\u015Far\u0131yla do\u011Fruland\u0131"
      },
      verifiedSwitchTab: {
        subtitle: "Devam etmek i\xE7in yeni a\xE7\u0131lm\u0131\u015F sekmeye d\xF6n\xFCn",
        subtitleNewTab: "Devam etmek i\xE7in \xF6nceki sekmeye d\xF6n\xFCn",
        title: "E-posta adresiniz ba\u015Far\u0131yla do\u011Fruland\u0131"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: "Gizlilik Politikas\u0131'n\u0131 kabul ediyorum",
        label__onlyTermsOfService: "Hizmet \u015Eartlar\u0131'n\u0131 kabul ediyorum",
        label__termsOfServiceAndPrivacyPolicy: "{{ termsOfServiceLink || link('Hizmet \u015Eartlar\u0131') }} ve {{ privacyPolicyLink || link('Gizlilik Politikas\u0131') }}'n\u0131 kabul ediyorum"
      },
      continue: {
        subtitle: "Devam etmek i\xE7in l\xFCtfen gerekli ad\u0131mlar\u0131 takip edin.",
        title: "Devam Et"
      }
    },
    phoneCode: {
      formSubtitle: "Telefon numaran\u0131za g\xF6nderdi\u011Fimiz do\u011Frulama kodunu giriniz",
      formTitle: "Do\u011Frulama kodu",
      resendButton: "Kodu tekrar g\xF6nder",
      subtitle: "{{applicationName}} ile devam etmek i\xE7in",
      title: "Telefon numaran\u0131z\u0131 do\u011Frulay\u0131n"
    },
    restrictedAccess: {
      actionLink: "Yard\u0131m almak i\xE7in destekle ileti\u015Fime ge\xE7in",
      actionText: "Eri\u015Fiminiz k\u0131s\u0131tland\u0131 m\u0131?",
      blockButton__emailSupport: "E-posta ile destek al",
      blockButton__joinWaitlist: "Bekleme listesine kat\u0131l",
      subtitle: "Eri\u015Fim k\u0131s\u0131tlamas\u0131 nedeniyle bu sayfaya eri\u015Fim sa\u011Flanam\u0131yor.",
      subtitleWaitlist: "Eri\u015Fim i\xE7in bekleme listesine kayd\u0131n\u0131z al\u0131nd\u0131.",
      title: "K\u0131s\u0131tl\u0131 Eri\u015Fim"
    },
    start: {
      actionLink: "Giri\u015F yap",
      actionLink__use_email: "Bunun yerine e-posta kullan\u0131n",
      actionLink__use_phone: "Bunun yerine telefon kullan\u0131n",
      actionText: "Hesab\u0131n\u0131z var m\u0131?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "{{applicationName}} ile devam etmek i\xE7in",
      subtitleCombined: "{{applicationName}} ile devam etmek i\xE7in",
      title: "Hesap olu\u015Ftur",
      titleCombined: "Hesap olu\u015Ftur"
    }
  },
  socialButtonsBlockButton: "{{provider|titleize}} ile giri\u015F yap\u0131n",
  socialButtonsBlockButtonManyInView: "{{provider|titleize}}",
  unstable__errors: {
    already_a_member_in_organization: "Bu organizasyonda zaten \xFCyesiniz.",
    captcha_invalid: "G\xFCvenlik do\u011Frulamalar\u0131ndaki hatalar nedeniyle kay\u0131t yap\u0131lamad\u0131. L\xFCtfen tekrar denemek i\xE7in sayfay\u0131 yenileyin veya daha fazla yard\u0131m i\xE7in destek ekibi ile ileti\u015Fime ge\xE7in.",
    captcha_unavailable: "Bot do\u011Frulamas\u0131 ba\u015Far\u0131s\u0131z oldu\u011Fu i\xE7in kay\u0131t yap\u0131lamad\u0131. L\xFCtfen tekrar denemek i\xE7in sayfay\u0131 yenileyin veya daha fazla yard\u0131m i\xE7in destek ekibi ile ileti\u015Fime ge\xE7in.",
    form_code_incorrect: "Hatal\u0131 kod.",
    form_identifier_exists__email_address: "Bu e-posta adresi zaten kullan\u0131l\u0131yor.",
    form_identifier_exists__phone_number: "Bu telefon numaras\u0131 zaten kullan\u0131l\u0131yor.",
    form_identifier_exists__username: "Bu kullan\u0131c\u0131 ad\u0131 zaten kullan\u0131l\u0131yor.",
    form_identifier_not_found: "Bu bilgilere sahip bir hesap bulunamad\u0131.",
    form_param_format_invalid: "Parametre format\u0131 ge\xE7ersiz.",
    form_param_format_invalid__email_address: "E-posta adresi ge\xE7erli olmal\u0131d\u0131r.",
    form_param_format_invalid__phone_number: "Telefon numaras\u0131 ge\xE7erli olmal\u0131d\u0131r.",
    form_param_max_length_exceeded__first_name: "Ad 256 karakteri a\u015Fmamal\u0131d\u0131r.",
    form_param_max_length_exceeded__last_name: "Soyad 256 karakteri a\u015Fmamal\u0131d\u0131r.",
    form_param_max_length_exceeded__name: "\u0130sim 256 karakteri a\u015Fmamal\u0131d\u0131r.",
    form_param_nil: "Parametre bo\u015F olamaz.",
    form_param_value_invalid: "Parametre de\u011Feri ge\xE7ersiz.",
    form_password_incorrect: "\u015Eifre yanl\u0131\u015F.",
    form_password_length_too_short: "\u015Eifre \xE7ok k\u0131sa.",
    form_password_not_strong_enough: "\u015Eifreniz yeterince g\xFC\xE7l\xFC de\u011Fil.",
    form_password_pwned: "Bu \u015Fifre bir veri ihlalinde tespit edildi ve kullan\u0131lamaz. L\xFCtfen ba\u015Fka bir \u015Fifre deneyin.",
    form_password_pwned__sign_in: "Bu \u015Fifre bir veri ihlalinde tespit edildi ve oturum a\xE7mak i\xE7in kullan\u0131lamaz. L\xFCtfen ba\u015Fka bir \u015Fifre se\xE7in.",
    form_password_size_in_bytes_exceeded: "\u015Eifreniz izin verilen maksimum byte say\u0131s\u0131n\u0131 a\u015Ft\u0131, l\xFCtfen k\u0131salt\u0131n veya baz\u0131 \xF6zel karakterleri \xE7\u0131kar\u0131n.",
    form_password_validation_failed: "\u015Eifre do\u011Frulamas\u0131 ba\u015Far\u0131s\u0131z.",
    form_username_invalid_character: "Kullan\u0131c\u0131 ad\u0131 ge\xE7ersiz karakterler i\xE7eriyor.",
    form_username_invalid_length: "Kullan\u0131c\u0131 ad\u0131 3 ile 50 karakter aras\u0131nda olmal\u0131d\u0131r.",
    identification_deletion_failed: "Son kimli\u011Finizi silemezsiniz.",
    not_allowed_access: "E-posta adresiniz veya telefon numaran\u0131z kay\u0131t i\xE7in izin verilmiyor. Bu, e-posta adresinizde '+', '=', '#' veya '.' kullanman\u0131z, ge\xE7ici e-posta hizmetiyle ili\u015Fkilendirilmi\u015F bir alan ad\u0131 kullanman\u0131z veya a\xE7\u0131k bir engellemeyle ilgili olabilir.",
    organization_domain_blocked: "Organizasyon alan ad\u0131 engellendi.",
    organization_domain_common: "Organizasyon alan ad\u0131 \xE7ok yayg\u0131n.",
    organization_domain_exists_for_enterprise_connection: "Kurumsal ba\u011Flant\u0131 i\xE7in bu alan ad\u0131 zaten mevcut.",
    organization_membership_quota_exceeded: "Organizasyon \xFCye kotas\u0131 a\u015F\u0131ld\u0131.",
    organization_minimum_permissions_needed: "Bu i\u015Flemi ger\xE7ekle\u015Ftirmek i\xE7in gerekli asgari izinlere sahip olmal\u0131s\u0131n\u0131z.",
    passkey_already_exists: "Bu hesaba zaten bir ge\xE7i\u015F anahtar\u0131 ba\u011Fl\u0131.",
    passkey_not_supported: "Ge\xE7i\u015F anahtarlar\u0131 \u015Fu anda desteklenmiyor.",
    passkey_pa_not_supported: "Bu platform i\xE7in ge\xE7i\u015F anahtarlar\u0131 desteklenmiyor.",
    passkey_registration_cancelled: "Ge\xE7i\u015F anahtar\u0131 kayd\u0131 iptal edildi.",
    passkey_retrieval_cancelled: "Ge\xE7i\u015F anahtar\u0131n\u0131 geri y\xFCkleme i\u015Flemi iptal edildi.",
    passwordComplexity: {
      maximumLength: "En fazla {{length}} karakter olmal\u0131",
      minimumLength: "En az {{length}} karakter olmal\u0131",
      requireLowercase: "bir k\xFC\xE7\xFCk harf i\xE7ermeli",
      requireNumbers: "bir say\u0131 i\xE7ermeli",
      requireSpecialCharacter: "bir \xF6zel karakter i\xE7ermeli",
      requireUppercase: "bir b\xFCy\xFCk harf i\xE7ermeli",
      sentencePrefix: "\u015Eifreniz;"
    },
    phone_number_exists: "Bu telefon numaras\u0131 zaten kullan\u0131l\u0131yor. L\xFCtfen ba\u015Fka bir numara deneyin.",
    session_exists: "Zaten giri\u015F yapm\u0131\u015Fs\u0131n\u0131z.",
    web3_missing_identifier: "Web3 i\xE7in tan\u0131mlay\u0131c\u0131 eksik.",
    zxcvbn: {
      couldBeStronger: "\u015Eifreniz kriterleri kar\u015F\u0131l\u0131yor; fakat birka\xE7 karakter daha ekleyerek daha g\xFC\xE7l\xFC bir \u015Fifre olu\u015Fturabilirsiniz.",
      goodPassword: "Harika! Parolan\u0131z gerekli t\xFCm gereksinimleri kar\u015F\u0131l\u0131yor.",
      notEnough: "\u015Eifreniz yeterince g\xFC\xE7l\xFC de\u011Fil.",
      suggestions: {
        allUppercase: "T\xFCm harfleri b\xFCy\xFCk yazmak yerine rastgele b\xFCy\xFCk harfler kullan\u0131n.",
        anotherWord: "\u015Eifreniz daha az bilinen kelimeler i\xE7ermeli.",
        associatedYears: "Kendinizle alakas\u0131 olan y\u0131llar\u0131 kullanmay\u0131n.",
        capitalization: "Sadece ilk harfi b\xFCy\xFCk yazmak yerine rastgele b\xFCy\xFCk harfler kullan\u0131n.",
        dates: "Kendinizle alakas\u0131 olan tarihleri kullanmay\u0131n.",
        l33t: '"a" yerine "@" kullanmak gibi tahmin edilebilir harf ikamelerinden ka\xE7\u0131n\u0131n.',
        longerKeyboardPattern: "Daha uzun ve karma\u015F\u0131k klavye desenleri kullan\u0131n.",
        noNeed: "\xD6zel karakter, say\u0131 veya b\xFCy\xFCk harf kullanmadan da g\xFC\xE7l\xFC bir \u015Fifre olu\u015Fturabilirsiniz.",
        pwned: "Bu \u015Fifre bir veri ihlalinde tespit edildi. Ba\u015Fka bir yerde kullan\u0131yorsan\u0131z, de\u011Fi\u015Ftirmeniz \xF6nerilir.",
        recentYears: "G\xFCn\xFCm\xFCze yak\u0131n y\u0131llar\u0131 kullanmay\u0131n.",
        repeated: "Kelime veya karakter tekrarlar\u0131n\u0131 azalt\u0131n.",
        reverseWords: "Yayg\u0131n kelimelerin tersini kullanmaktan ka\xE7\u0131n\u0131n.",
        sequences: "Yayg\u0131n desenleri kullanmay\u0131n.",
        useWords: "Birden fazla s\xF6zc\xFCk kullan\u0131n ama yayg\u0131n deyi\u015Flerden ka\xE7\u0131n\u0131n."
      },
      warnings: {
        common: "Bu yayg\u0131n bir \u015Fifre.",
        commonNames: "Yayg\u0131n adlar ve soyadlar kolay tahmin edilir.",
        dates: "Tarihleri tahmin etmek kolayd\u0131r.",
        extendedRepeat: '"abcabcabc" gibi tekrarlanan desenler kolay tahmin edilir.',
        keyPattern: "K\u0131sa klavye desenleri kolay tahmin edilir.",
        namesByThemselves: "Adlar ya da soyadlar kolay tahmin edilir.",
        pwned: "Bu \u015Fifre bir veri ihlalinde tespit edilmi\u015F.",
        recentYears: "Yak\u0131n y\u0131llar kolay tahmin edilir.",
        sequences: '"abc" gibi yayg\u0131n desenlerin tahmini kolayd\u0131r.',
        similarToCommon: "Bu yayg\u0131n kullan\u0131lan \u015Fifrelere \xE7ok benziyor.",
        simpleRepeat: "Tekrarlanan karakterler kolay tahmin edilir.",
        straightRow: "Klavyede ard\u0131\u015F\u0131k karakterler kolay tahmin edilir.",
        topHundred: "Bu \xE7ok yayg\u0131n bir \u015Fifre.",
        topTen: "Bu epey yayg\u0131n bir \u015Fifre.",
        userInputs: "\u015Eifreniz ki\u015Fisel bilgiler i\xE7ermemeli.",
        wordByItself: "Tek s\xF6zc\xFCkler kolay tahmin edilir."
      }
    }
  },
  userButton: {
    action__addAccount: "Hesap ekle",
    action__manageAccount: "Hesab\u0131 y\xF6net",
    action__signOut: "\xC7\u0131k\u0131\u015F yap",
    action__signOutAll: "T\xFCm hesaplardan \xE7\u0131k\u0131\u015F yap"
  },
  userProfile: {
    apiKeysPage: {
      title: void 0
    },
    backupCodePage: {
      actionLabel__copied: "Kopyaland\u0131!",
      actionLabel__copy: "Hepsini kopyala",
      actionLabel__download: ".txt olarak indir",
      actionLabel__print: "Yazd\u0131r",
      infoText1: "Yedekleme kodlar\u0131 bu hesap i\xE7in etkinle\u015Ftirilecektir.",
      infoText2: "Yedekleme kodlar\u0131n\u0131z\u0131 g\xFCvenli bir yerde saklay\u0131n. E\u011Fer bu kodlar\u0131n\u0131z\u0131n ba\u015Fkas\u0131n\u0131n eline ge\xE7ti\u011Fini d\xFC\u015F\xFCn\xFCrseniz, yenilerini olu\u015Fturabilirsiniz.",
      subtitle__codelist: "Yedekleme kodlar\u0131n\u0131z\u0131 g\xFCvenli bir yerde saklay\u0131n.",
      successMessage: "Yedekleme kodlar\u0131 ba\u015Far\u0131yla eklendi. E\u011Fer Authenticator uygulaman\u0131z\u0131n oldu\u011Fu cihaza eri\u015Fiminizi kaybettiyseniz, oturum a\xE7arken bu kodlardan birini girebilirsiniz. Her kod en fazla bir kez kullan\u0131labilir.",
      successSubtitle: "E\u011Fer Authenticator uygulaman\u0131z\u0131n oldu\u011Fu cihaz\u0131n\u0131za eri\u015Fiminizi kaybederseniz, bu kodlardan birini kullanarak hesab\u0131n\u0131za giri\u015F yapabilirsiniz.",
      title: "Yedekleme kodu do\u011Frulamas\u0131 ekle",
      title__codelist: "Yedekleme kodlar\u0131"
    },
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Yeni bir hesap ba\u011Flamak i\xE7in bir sa\u011Flay\u0131c\u0131 se\xE7iniz.",
      formHint__noAccounts: "Kullan\u0131labilir bir sa\u011Flay\u0131c\u0131 yok.",
      removeResource: {
        messageLine1: "{{identifier}} hesab\u0131n\u0131zdan kald\u0131r\u0131lacakt\u0131r.",
        messageLine2: "Art\u0131k bu ba\u011Fl\u0131 hesab\u0131 kullanarak oturum a\xE7man\u0131z m\xFCmk\xFCn olmayacakt\u0131r ve buna ba\u011Fl\u0131 \xF6zellikler \xE7al\u0131\u015Fmayacakt\u0131r.",
        successMessage: "{{connectedAccount}} hesab\u0131n\u0131zdan kald\u0131r\u0131ld\u0131.",
        title: "Ba\u011Fl\u0131 hesab\u0131 kald\u0131r"
      },
      socialButtonsBlockButton: "{{provider|titleize}} hesab\u0131 ba\u011Fla",
      successMessage: "Sa\u011Flay\u0131c\u0131 hesab\u0131n\u0131za ba\u011Fland\u0131.",
      title: "Hesap ba\u011Fla"
    },
    deletePage: {
      actionDescription: "Devam etmek i\xE7in a\u015Fa\u011F\u0131ya \u201CHesab\u0131 sil\u201D yaz\u0131n.",
      confirm: "Hesab\u0131 sil",
      messageLine1: "Hesab\u0131n\u0131z\u0131 silmek istedi\u011Finizden emin misiniz?",
      messageLine2: "Bu i\u015Flem kal\u0131c\u0131d\u0131r ve geri al\u0131namaz.",
      title: "Hesab\u0131 sil"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "Do\u011Frulama kodunu i\xE7eren bir e-posta belirtti\u011Finiz adrese g\xF6nderilecektir.",
        formSubtitle: "{{identifier}} adresine g\xF6nderilen do\u011Frulama kodunu giriniz",
        formTitle: "Do\u011Frulama kodu",
        resendButton: "Yeniden g\xF6nder",
        successMessage: "{{identifier}} adresi hesab\u0131n\u0131za eklendi."
      },
      emailLink: {
        formHint: "Do\u011Frulama ba\u011Flant\u0131s\u0131n\u0131 i\xE7eren bir e-posta belirtti\u011Finiz adrese g\xF6nderilecektir.",
        formSubtitle: "{{identifier}} adresine g\xF6nderilen do\u011Frulama ba\u011Flant\u0131s\u0131n\u0131 t\u0131klay\u0131n\u0131z",
        formTitle: "Do\u011Frulama ba\u011Flant\u0131s\u0131",
        resendButton: "Yeniden g\xF6nder",
        successMessage: "{{identifier}} adresi hesab\u0131n\u0131za eklendi."
      },
      enterpriseSSOLink: {
        formButton: "Ba\u011Flan",
        formSubtitle: "Kurumsal SSO kullan\u0131n"
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} adresi hesab\u0131n\u0131zdan kald\u0131r\u0131lacakt\u0131r.",
        messageLine2: "Art\u0131k bu e-posta adresini kullanarak oturum a\xE7man\u0131z m\xFCmk\xFCn olmayacakt\u0131r.",
        successMessage: "{{emailAddress}} adresi hesab\u0131n\u0131zdan kald\u0131r\u0131ld\u0131.",
        title: "E-posta adresini kald\u0131r"
      },
      title: "E-posta adresi ekle",
      verifyTitle: "E-posta adresini do\u011Frulay\u0131n"
    },
    formButtonPrimary__add: "Ekle",
    formButtonPrimary__continue: "\u0130lerle",
    formButtonPrimary__finish: "Bitir",
    formButtonPrimary__remove: "Kald\u0131r",
    formButtonPrimary__save: "Kaydet",
    formButtonReset: "\u0130ptal",
    mfaPage: {
      formHint: "Eklemek i\xE7in bir y\xF6ntem se\xE7iniz.",
      title: "\u0130ki a\u015Famal\u0131 do\u011Frulama y\xF6ntemi ekle"
    },
    mfaPhoneCodePage: {
      backButton: "Mevcut numaray\u0131 kullan",
      primaryButton__addPhoneNumber: "Telefon numaras\u0131 ekle",
      removeResource: {
        messageLine1: "Giri\u015F yaparken art\u0131k {{identifier}} numaras\u0131na SMS kodu g\xF6nderilmeyecektir.",
        messageLine2: "Hesab\u0131n\u0131z\u0131n g\xFCvenli\u011Fi azalabilir. Devam etmek istedi\u011Finizden emin misiniz?",
        successMessage: "\u0130ki a\u015Famal\u0131 SMS kodu do\u011Frulamas\u0131 {{mfaPhoneCode}} numaras\u0131ndan kald\u0131r\u0131ld\u0131.",
        title: "\u0130ki a\u015Famal\u0131 do\u011Frulamay\u0131 kald\u0131r"
      },
      subtitle__availablePhoneNumbers: "\u0130ki a\u015Famal\u0131 SMS kodu do\u011Frulamas\u0131 i\xE7in bir telefon numaras\u0131 se\xE7in.",
      subtitle__unavailablePhoneNumbers: "\u0130ki a\u015Famal\u0131 SMS kodu do\u011Frulamas\u0131 i\xE7in kullan\u0131labilir bir telefon numaras\u0131 yok.",
      successMessage1: "Oturum a\xE7arken, ek bir ad\u0131m olarak bu telefon numaras\u0131na g\xF6nderilen bir do\u011Frulama kodunu girmeniz gerekecektir.",
      successMessage2: "Bu yedek kodlar\u0131 kaydedin ve g\xFCvenli bir yerde saklay\u0131n. Kimlik do\u011Frulama cihaz\u0131n\u0131za eri\u015Fiminizi kaybederseniz oturum a\xE7mak i\xE7in yedek kodlar\u0131 kullanabilirsiniz.",
      successTitle: "SMS kodu do\u011Frulamas\u0131 etkin",
      title: "SMS kodu do\u011Frulamas\u0131 ekle"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Veya QR kodunu tara",
        buttonUnableToScan__nonPrimary: "QR kodunu tarayam\u0131yorum",
        infoText__ableToScan: "Authenticator uygulaman\u0131zda yeni bir giri\u015F y\xF6ntemi ayarlay\u0131n ve hesab\u0131n\u0131zla ba\u011Flamak i\xE7in a\u015Fa\u011F\u0131daki QR kodunu taray\u0131n.",
        infoText__unableToScan: "Authenticator uygulaman\u0131zda yeni bir giri\u015F y\xF6ntemi ekleme se\xE7ene\u011Fini bulun ve a\u015Fa\u011F\u0131da verilen de\u011Feri girin:",
        inputLabel__unableToScan1: "Zaman bazl\u0131 veya tek seferlik \u015Fifrelerin etkinle\u015Ftirildi\u011Finden emin olun, ard\u0131ndan hesab\u0131n\u0131z\u0131 ba\u011Flamay\u0131 tamamlay\u0131n.",
        inputLabel__unableToScan2: "Alternatif olarak do\u011Frulay\u0131c\u0131n\u0131z TOTP URI\u2019leri destekliyorsa, tam URI\u2019yi de kopyalayabilirsiniz."
      },
      removeResource: {
        messageLine1: "Art\u0131k giri\u015F yaparken authenticator'dan gelecek do\u011Frulama kodlar\u0131 gerekmeyecektir.",
        messageLine2: "Hesab\u0131n\u0131z\u0131n g\xFCvenli\u011Fi azalabilir. Devam etmek istedi\u011Finizden emin misiniz?",
        successMessage: "\u0130ki a\u015Famal\u0131 do\u011Frulama y\xF6ntemi kald\u0131r\u0131ld\u0131.",
        title: "\u0130ki a\u015Famal\u0131 do\u011Frulamay\u0131 kald\u0131r"
      },
      successMessage: "\u0130ki a\u015Famal\u0131 do\u011Frulama y\xF6ntemi ba\u015Far\u0131yla eklendi. Oturum a\xE7arken, ek bir ad\u0131m olarak bu do\u011Frulay\u0131c\u0131dan bir do\u011Frulama kodu girmeniz gerekecektir.",
      title: "Authenticator uygulamas\u0131 ekle",
      verifySubtitle: "Authenticator uygulaman\u0131zda olu\u015Fturulan do\u011Frulama kodunu giriniz",
      verifyTitle: "Do\u011Frulama kodu"
    },
    mobileButton__menu: "Men\xFC",
    navbar: {
      account: "Profil",
      apiKeys: void 0,
      billing: void 0,
      description: "Hesap bilgilerinizi y\xF6netin.",
      security: "G\xFCvenlik",
      title: "Hesap"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} bu hesaptan kald\u0131r\u0131lacakt\u0131r.",
        title: "Ge\xE7i\u015F anahtar\u0131n\u0131 kald\u0131r"
      },
      subtitle__rename: "Bulmay\u0131 kolayla\u015Ft\u0131rmak i\xE7in ge\xE7i\u015F anahtar\u0131 ad\u0131n\u0131 de\u011Fi\u015Ftirebilirsiniz.",
      title__rename: "Ge\xE7i\u015F Anahtar\u0131n\u0131 Yeniden Adland\u0131r"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Eski \u015Fifrenizi kullanm\u0131\u015F olabilecek di\u011Fer t\xFCm cihazlardan \xE7\u0131k\u0131\u015F yapman\u0131z \xF6nerilir.",
      readonly: "Profil bilgileriniz kurumsal ba\u011Flant\u0131 \xFCzerinden sa\u011Fland\u0131\u011F\u0131 i\xE7in d\xFCzenlenemez.",
      successMessage__set: "\u015Eifreniz ba\u015Far\u0131yla de\u011Fi\u015Ftirildi.",
      successMessage__signOutOfOtherSessions: "Di\u011Fer t\xFCm cihazlardaki oturumlar\u0131n\u0131z sonland\u0131r\u0131ld\u0131.",
      successMessage__update: "\u015Eifreniz g\xFCncellendi.",
      title__set: "\u015Eifreyi de\u011Fi\u015Ftir",
      title__update: "Yeni \u015Fifre girin"
    },
    phoneNumberPage: {
      infoText: "Belirtilen numaraya do\u011Frulama kodunu i\xE7eren bir SMS g\xF6nderilecektir.",
      removeResource: {
        messageLine1: "{{identifier}} numaras\u0131 hesab\u0131n\u0131zdan kald\u0131r\u0131lacakt\u0131r.",
        messageLine2: "Art\u0131k bu telefon numaras\u0131n\u0131 kullanarak oturum a\xE7man\u0131z m\xFCmk\xFCn olmayacakt\u0131r.",
        successMessage: "{{phoneNumber}} numaras\u0131 hesab\u0131n\u0131zdan kald\u0131r\u0131ld\u0131.",
        title: "Telefon numaras\u0131n\u0131 kald\u0131r"
      },
      successMessage: "{{identifier}} numaras\u0131 hesab\u0131n\u0131za eklendi.",
      title: "Telefon numaras\u0131 ekle",
      verifySubtitle: "{{identifier}} numaras\u0131na g\xF6nderilen do\u011Frulama kodunu girin",
      verifyTitle: "Telefon numaras\u0131n\u0131 do\u011Frulay\u0131n"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "10 MB'tan k\xFC\xE7\xFCk boyutta bir JPG, PNG, GIF, veya WEBP dosyas\u0131 y\xFCkle",
      imageFormDestructiveActionSubtitle: "G\xF6rseli kald\u0131r",
      imageFormSubtitle: "G\xF6rsel y\xFCkle",
      imageFormTitle: "Profil g\xF6rseli",
      readonly: "Profil bilgileriniz kurumsal ba\u011Flant\u0131 \xFCzerinden sa\u011Fland\u0131\u011F\u0131 i\xE7in d\xFCzenlenemez.",
      successMessage: "Profiliniz g\xFCncellendi.",
      title: "Profili g\xFCncelle"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Cihaz oturumunu sonland\u0131r",
        title: "Aktif cihazlar"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Yeniden dene",
        actionLabel__reauthorize: "Yetkilendir",
        destructiveActionTitle: "Kald\u0131r",
        primaryButton: "Hesap ba\u011Fla",
        subtitle__disconnected: "Bu hesab\u0131n ba\u011Flant\u0131s\u0131 kesildi.",
        subtitle__reauthorize: "Gerekli kapsamlar g\xFCncellendi ve s\u0131n\u0131rl\u0131 i\u015Flevsellik ya\u015Fayabilirsiniz. Sorunlar\u0131 \xF6nlemek i\xE7in l\xFCtfen bu uygulamay\u0131 yeniden yetkilendirin",
        title: "Ba\u011Fl\u0131 hesaplar"
      },
      dangerSection: {
        deleteAccountButton: "Hesab\u0131 sil",
        title: "Tehlike"
      },
      emailAddressesSection: {
        destructiveAction: "E-posta adresini kald\u0131r",
        detailsAction__nonPrimary: "Birincil e-posta adresi yap",
        detailsAction__primary: "Do\u011Frulamay\u0131 tamamla",
        detailsAction__unverified: "Do\u011Frulamay\u0131 tamamla",
        primaryButton: "E-posta adresi ekle",
        title: "E-posta adresleri"
      },
      enterpriseAccountsSection: {
        title: "Kurumsal hesaplar"
      },
      headerTitle__account: "Hesap",
      headerTitle__security: "G\xFCvenlik",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Kodlar\u0131 yenile",
          headerTitle: "Yedekleme kodlar\u0131",
          subtitle__regenerate: "Yeni bir dizi g\xFCvenli yedekleme kodu al\u0131n. \xD6nceki yedekleme kodlar\u0131 silinecek ve kullan\u0131lamayacakt\u0131r.",
          title__regenerate: "Yedekleme kodlar\u0131n\u0131 yenile"
        },
        phoneCode: {
          actionLabel__setDefault: "Varsay\u0131lan olarak ayarla",
          destructiveActionLabel: "Telefon numaras\u0131n\u0131 kald\u0131r"
        },
        primaryButton: "\u0130ki a\u015Famal\u0131 do\u011Frulama ekle",
        title: "\u0130ki a\u015Famal\u0131 do\u011Frulama",
        totp: {
          destructiveActionTitle: "Kald\u0131r",
          headerTitle: "Authenticator uygulamas\u0131"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Kald\u0131r",
        menuAction__rename: "Yeniden Adland\u0131r",
        primaryButton: void 0,
        title: "Ge\xE7i\u015F Anahtarlar\u0131"
      },
      passwordSection: {
        primaryButton__setPassword: "\u015Eifreyi g\xFCncelle",
        primaryButton__updatePassword: "\u015Eifreyi de\u011Fi\u015Ftir",
        title: "\u015Eifre"
      },
      phoneNumbersSection: {
        destructiveAction: "Telefon numaras\u0131n\u0131 kald\u0131r",
        detailsAction__nonPrimary: "Birincil yap",
        detailsAction__primary: "Do\u011Frulamay\u0131 tamamla",
        detailsAction__unverified: "Do\u011Frulamay\u0131 tamamla",
        primaryButton: "Telefon numaras\u0131 ekle",
        title: "Telefon numaralar\u0131"
      },
      profileSection: {
        primaryButton: "Profili g\xFCncelle",
        title: "Profil"
      },
      usernameSection: {
        primaryButton__setUsername: "Kullan\u0131c\u0131 ad\u0131n\u0131 ayarla",
        primaryButton__updateUsername: "Kullan\u0131c\u0131 ad\u0131n\u0131 de\u011Fi\u015Ftir",
        title: "Kullan\u0131c\u0131 ad\u0131"
      },
      web3WalletsSection: {
        destructiveAction: "C\xFCzdan\u0131 kald\u0131r",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3 c\xFCzdanlar\u0131",
        title: "Web3 c\xFCzdanlar\u0131"
      }
    },
    usernamePage: {
      successMessage: "Kullan\u0131c\u0131 ad\u0131n\u0131z g\xFCncellendi.",
      title__set: "Kullan\u0131c\u0131 ad\u0131n\u0131 g\xFCncelle",
      title__update: "Kullan\u0131c\u0131 ad\u0131n\u0131 g\xFCncelle"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} c\xFCzdan\u0131 hesab\u0131n\u0131zdan kald\u0131r\u0131lacakt\u0131r.",
        messageLine2: "Art\u0131k bu c\xFCzdan\u0131 kullanarak oturum a\xE7man\u0131z m\xFCmk\xFCn olmayacakt\u0131r.",
        successMessage: "{{web3Wallet}} c\xFCzdan\u0131 hesab\u0131n\u0131zdan kald\u0131r\u0131ld\u0131.",
        title: "Web3 c\xFCzdan\u0131n\u0131 kald\u0131r"
      },
      subtitle__availableWallets: "Hesab\u0131n\u0131za eklemek i\xE7in bir web3 c\xFCzdan\u0131 se\xE7iniz.",
      subtitle__unavailableWallets: "Kullan\u0131labilir bir web3 c\xFCzdan\u0131 yok.",
      successMessage: "Web3 c\xFCzdan\u0131n\u0131z hesab\u0131n\u0131za eklendi.",
      title: "Web3 c\xFCzdan\u0131 ekle",
      web3WalletButtonsBlockButton: "Web3 c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in t\u0131klay\u0131n"
    }
  },
  waitlist: {
    start: {
      actionLink: "Bekleme listesine kat\u0131l",
      actionText: "Hala bir hesab\u0131n\u0131z yok mu?",
      formButton: "Kay\u0131t Ol",
      subtitle: "Kaydolduktan sonra erken eri\u015Fim kazanabilirsiniz.",
      title: "Bekleme Listesine Kat\u0131l\u0131n"
    },
    success: {
      message: "Ba\u015Far\u0131yla bekleme listesine eklendiniz. Erken eri\u015Fim i\xE7in sizi bilgilendirece\u011Fiz.",
      subtitle: "Bekleme listesinde oldu\u011Funuz i\xE7in te\u015Fekk\xFCr ederiz.",
      title: "Bekleme Listesine Kat\u0131ld\u0131n\u0131z"
    }
  }
};
export {
  trTR
};
//# sourceMappingURL=tr-TR.mjs.map