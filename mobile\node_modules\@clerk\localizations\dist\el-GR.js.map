{"version": 3, "sources": ["../src/el-GR.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const elGR: LocalizationResource = {\n  locale: 'el-GR',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Πίσω',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Προεπιλογή',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Άλλη συσκευή υποδυόμενου',\n  badge__primary: 'Κύριο',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Απαιτεί ενέργεια',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Αυτή η συσκευή',\n  badge__unverified: 'Μη επαληθευμένο',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Συσκευή χρήστη',\n  badge__you: 'Εσείς',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Δημιουργία οργανισμού',\n    invitePage: {\n      formButtonReset: 'Παράλειψη',\n    },\n    title: 'Δημιουργία Οργανισμού',\n  },\n  dates: {\n    lastDay: \"Χθες στις {{ date | timeString('el') }}\",\n    next6Days: \"{{ date | weekday('el','long') }} στις {{ date | timeString('el') }}\",\n    nextDay: \"Αύριο στις {{ date | timeString('el') }}\",\n    numeric: \"{{ date | numeric('el') }}\",\n    previous6Days: \"Τελευταία {{ date | weekday('el','long') }} στις {{ date | timeString('el') }}\",\n    sameDay: \"Σήμερα στις {{ date | timeString('el') }}\",\n  },\n  dividerText: 'ή',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Χρησιμοποιήστε άλλη μέθοδο',\n  footerPageLink__help: 'Βοήθεια',\n  footerPageLink__privacy: 'Προστασία προσωπικών δεδομένων',\n  footerPageLink__terms: 'Όροι',\n  formButtonPrimary: 'Συνέχεια',\n  formButtonPrimary__verify: 'Verify',\n  formFieldAction__forgotPassword: 'Ξεχάσατε τον κωδικό;',\n  formFieldError__matchingPasswords: 'Οι κωδικοί ταιριάζουν.',\n  formFieldError__notMatchingPasswords: 'Οι κωδικοί δεν ταιριάζουν.',\n  formFieldError__verificationLinkExpired: 'The verification link expired. Please request a new link.',\n  formFieldHintText__optional: 'Προαιρετικό',\n  formFieldHintText__slug: 'A slug is a human-readable ID that must be unique. It’s often used in URLs.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Delete account',\n  formFieldInputPlaceholder__emailAddress: 'Εισάγετε τη διεύθυνση email σας',\n  formFieldInputPlaceholder__emailAddress_username: 'Εισάγετε email ή όνομα χρήστη',\n  formFieldInputPlaceholder__emailAddresses:\n    'Εισαγάγετε ή επικολλήστε μία ή περισσότερες διευθύνσεις email, χωρισμένες με κενά ή κόμματα',\n  formFieldInputPlaceholder__firstName: 'Όνομα',\n  formFieldInputPlaceholder__lastName: 'Επώνυμο',\n  formFieldInputPlaceholder__organizationDomain: 'example.com',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: '<EMAIL>',\n  formFieldInputPlaceholder__organizationName: 'Όνομα οργανισμού',\n  formFieldInputPlaceholder__organizationSlug: 'my-org',\n  formFieldInputPlaceholder__password: 'Εισάγετε τον κωδικό σας',\n  formFieldInputPlaceholder__phoneNumber: 'Εισάγετε τον αριθμό τηλεφώνου σας',\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Ενεργοποίηση αυτόματων προσκλήσεων για αυτόν τον τομέα',\n  formFieldLabel__backupCode: 'Αντίγραφο ασφαλείας κωδικού',\n  formFieldLabel__confirmDeletion: 'Επιβεβαίωση',\n  formFieldLabel__confirmPassword: 'Επιβεβαίωση κωδικού πρόσβασης',\n  formFieldLabel__currentPassword: 'Τρέχων κωδικός πρόσβασης',\n  formFieldLabel__emailAddress: 'Διεύθυνση email',\n  formFieldLabel__emailAddress_username: 'Διεύθυνση email ή όνομα χρήστη',\n  formFieldLabel__emailAddresses: 'Διευθύνσεις email',\n  formFieldLabel__firstName: 'Όνομα',\n  formFieldLabel__lastName: 'Επώνυμο',\n  formFieldLabel__newPassword: 'Νέος κωδικός πρόσβασης',\n  formFieldLabel__organizationDomain: 'Domain',\n  formFieldLabel__organizationDomainDeletePending: 'Διαγραφή εκκρεμών προσκλήσεων και προτάσεων',\n  formFieldLabel__organizationDomainEmailAddress: 'Διεύθυνση email επαλήθευσης',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Εισάγετε μια διεύθυνση email σε αυτόν τον τομέα για να λάβετε κωδικό και να επαληθεύσετε τον τομέα.',\n  formFieldLabel__organizationName: 'Όνομα οργανισμού',\n  formFieldLabel__organizationSlug: 'Συντόμευση URL',\n  formFieldLabel__passkeyName: undefined,\n  formFieldLabel__password: 'Κωδικός πρόσβασης',\n  formFieldLabel__phoneNumber: 'Αριθμός τηλεφώνου',\n  formFieldLabel__role: 'Ρόλος',\n  formFieldLabel__signOutOfOtherSessions: 'Αποσύνδεση από όλες τις άλλες συσκευές',\n  formFieldLabel__username: 'Όνομα χρήστη',\n  impersonationFab: {\n    action__signOut: 'Αποσύνδεση',\n    title: 'Είστε συνδεδεμένος ως {{identifier}}',\n  },\n  maintenanceMode: undefined,\n  membershipRole__admin: 'Διαχειριστής',\n  membershipRole__basicMember: 'Μέλος',\n  membershipRole__guestMember: 'Επισκέπτης',\n  organizationList: {\n    action__createOrganization: 'Δημιουργία οργανισμού',\n    action__invitationAccept: 'Συμμετοχή',\n    action__suggestionsAccept: 'Αίτηση συμμετοχής',\n    createOrganization: 'Δημιουργία Οργανισμού',\n    invitationAcceptedLabel: 'Συμμετέχετε',\n    subtitle: 'για να συνεχίσετε στο {{applicationName}}',\n    suggestionsAcceptedLabel: 'Εκκρεμεί έγκριση',\n    title: 'Επιλέξτε λογαριασμό',\n    titleWithoutPersonal: 'Επιλέξτε οργανισμό',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Αυτόματες προσκλήσεις',\n    badge__automaticSuggestion: 'Αυτόματες προτάσεις',\n    badge__manualInvitation: 'Χωρίς αυτόματη εγγραφή',\n    badge__unverified: 'Μη επαληθευμένο',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Προσθέστε τον τομέα για επαλήθευση. Χρήστες με διευθύνσεις email σε αυτόν τον τομέα μπορούν να συμμετάσχουν στον οργανισμό αυτόματα ή να αιτηθούν συμμετοχή.',\n      title: 'Προσθήκη τομέα',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Οι προσκλήσεις δεν μπορούσαν να σταλούν. Διορθώστε τα παρακάτω στοιχεία και δοκιμάστε ξανά:',\n      formButtonPrimary__continue: 'Αποστολή προσκλήσεων',\n      selectDropdown__role: 'Επιλογή ρόλου',\n      subtitle: 'Προσκαλέστε νέα μέλη σε αυτόν τον οργανισμό',\n      successMessage: 'Οι προσκλήσεις εστάλησαν με επιτυχία',\n      title: 'Πρόσκληση μελών',\n    },\n    membersPage: {\n      action__invite: 'Πρόσκληση',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Αφαίρεση μέλους',\n        tableHeader__actions: 'Ενέργειες',\n        tableHeader__joined: 'Εγγραφήκατε',\n        tableHeader__role: 'Ρόλος',\n        tableHeader__user: 'Χρήστης',\n      },\n      detailsTitle__emptyRow: 'Δεν υπάρχουν μέλη για εμφάνιση',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Invite users by connecting an email domain with your organization. Anyone who signs up with a matching email domain will be able to join the organization anytime.',\n          headerTitle: 'Automatic invitations',\n          primaryButton: 'Manage verified domains',\n        },\n        table__emptyRow: 'No invitations to display',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Ανάκληση πρόσκλησης',\n        tableHeader__invited: 'Προσκεκλημένο',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Users who sign up with a matching email domain, will be able to see a suggestion to request to join your organization.',\n          headerTitle: 'Automatic suggestions',\n          primaryButton: 'Manage verified domains',\n        },\n        menuAction__approve: 'Approve',\n        menuAction__reject: 'Reject',\n        tableHeader__requested: 'Requested access',\n        table__emptyRow: 'No requests to display',\n      },\n      start: {\n        headerTitle__invitations: 'Invitations',\n        headerTitle__members: 'Members',\n        headerTitle__requests: 'Requests',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Manage your organization.',\n      general: 'General',\n      members: 'Members',\n      title: 'Organization',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Πληκτρολογήστε το {{organizationName}} παρακάτω για να συνεχίσετε.',\n          messageLine1: 'Είστε σίγουρος ότι θέλετε να διαγράψετε αυτόν τον οργανισμό;',\n          messageLine2: 'Αυτή η ενέργεια είναι μόνιμη και και μη αναστρέψιμη.',\n          successMessage: 'Έχετε διαγράψει τον οργανισμό.',\n          title: 'Διαγραφή οργανισμού',\n        },\n        leaveOrganization: {\n          actionDescription: 'Πληκτρολογήστε το {{organizationName}} παρακάτω για να συνεχίσετε.',\n          messageLine1:\n            'Είστε σίγουρος ότι θέλετε να αποχωρήσετε από αυτόν τον οργανισμό; Θα χάσετε την πρόσβαση σε αυτόν τον οργανισμό και τις εφαρμογές του.',\n          messageLine2: 'Αυτή η ενέργεια είναι μόνιμη και και μη αναστρέψιμη.',\n          successMessage: 'Έχετε αποχωρήσει από τον οργανισμό.',\n          title: 'Αποχώρηση από τον οργανισμό',\n        },\n        title: 'Κίνδυνος',\n      },\n      domainSection: {\n        menuAction__manage: 'Manage',\n        menuAction__remove: 'Delete',\n        menuAction__verify: 'Verify',\n        primaryButton: 'Add domain',\n        subtitle:\n          'Allow users to join the organization automatically or request to join based on a verified email domain.',\n        title: 'Verified domains',\n      },\n      successMessage: 'Ο οργανισμός έχει ενημερωθεί.',\n      title: 'Προφίλ Οργανισμού',\n    },\n    removeDomainPage: {\n      messageLine1: 'The email domain {{domain}} will be removed.',\n      messageLine2: 'Users won’t be able to join the organization automatically after this.',\n      successMessage: '{{domain}} has been removed.',\n      title: 'Remove domain',\n    },\n    start: {\n      headerTitle__general: 'General',\n      headerTitle__members: 'Μέλη',\n      profileSection: {\n        primaryButton: 'Ενημέρωση προφίλ',\n        title: 'Organization Profile',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Removing this domain will affect invited users.',\n        removeDomainActionLabel__remove: 'Remove domain',\n        removeDomainSubtitle: 'Remove this domain from your verified domains',\n        removeDomainTitle: 'Remove domain',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Users are automatically invited to join the organization when they sign-up and can join anytime.',\n        automaticInvitationOption__label: 'Automatic invitations',\n        automaticSuggestionOption__description:\n          'Users receive a suggestion to request to join, but must be approved by an admin before they are able to join the organization.',\n        automaticSuggestionOption__label: 'Automatic suggestions',\n        calloutInfoLabel: 'Changing the enrollment mode will only affect new users.',\n        calloutInvitationCountLabel: 'Pending invitations sent to users: {{count}}',\n        calloutSuggestionCountLabel: 'Pending suggestions sent to users: {{count}}',\n        manualInvitationOption__description: 'Users can only be invited manually to the organization.',\n        manualInvitationOption__label: 'No automatic enrollment',\n        subtitle: 'Choose how users from this domain can join the organization.',\n      },\n      start: {\n        headerTitle__danger: 'Danger',\n        headerTitle__enrollment: 'Enrollment options',\n      },\n      subtitle: 'The domain {{domain}} is now verified. Continue by selecting enrollment mode.',\n      title: 'Update {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Enter the verification code sent to your email address',\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'The domain {{domainName}} needs to be verified via email.',\n      subtitleVerificationCodeScreen: 'A verification code was sent to {{emailAddress}}. Enter the code to continue.',\n      title: 'Verify domain',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Δημιουργία Οργανισμού',\n    action__invitationAccept: 'Join',\n    action__manageOrganization: 'Διαχείριση Οργανισμού',\n    action__suggestionsAccept: 'Request to join',\n    notSelected: 'Δεν έχει επιλεγεί οργανισμός',\n    personalWorkspace: 'Προσωπικός Χώρος Εργασίας',\n    suggestionsAcceptedLabel: 'Pending approval',\n  },\n  paginationButton__next: 'Επόμενο',\n  paginationButton__previous: 'Προηγούμενο',\n  paginationRowText__displaying: 'Εμφάνιση',\n  paginationRowText__of: 'από',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Add account',\n      action__signOutAll: 'Sign out of all accounts',\n      subtitle: 'Select the account with which you wish to continue.',\n      title: 'Choose an account',\n    },\n    alternativeMethods: {\n      actionLink: 'Λήψη βοήθειας',\n      actionText: 'Δεν έχετε κανένα από αυτά;',\n      blockButton__backupCode: 'Χρήση ενός εφεδρικού κωδικού',\n      blockButton__emailCode: 'Αποστολή κωδικού με email στο {{identifier}}',\n      blockButton__emailLink: 'Αποστολή συνδέσμου στο {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Σύνδεση με τον κωδικό πρόσβασής σας',\n      blockButton__phoneCode: 'Αποστολή κωδικού SMS στο {{identifier}}',\n      blockButton__totp: 'Χρήση της εφαρμογής αυθεντικοποίησης',\n      getHelp: {\n        blockButton__emailSupport: 'Υποστήριξη μέσω email',\n        content:\n          'Εάν αντιμετωπίζετε δυσκολία στη σύνδεση στον λογαριασμό σας, στείλτε μας email και θα επικοινωνήσουμε μαζί σας για να αποκαταστήσουμε την πρόσβαση το συντομότερο δυνατόν.',\n        title: 'Λήψη βοήθειας',\n      },\n      subtitle:\n        'Αντιμετωπίζετε δυσκολίες; Μπορείτε να χρησιμοποιήσετε οποιαδήποτε από αυτές τις μεθόδους για να συνδεθείτε.',\n      title: 'Χρήση μιας άλλης μεθόδου',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'για να συνεχίσετε στο {{applicationName}}',\n      title: 'Εισαγωγή ενός εφεδρικού κωδικού',\n    },\n    emailCode: {\n      formTitle: 'Κωδικός επαλήθευσης',\n      resendButton: 'Δεν λάβατε κωδικό; Αποστολή ξανά',\n      subtitle: 'για να συνεχίσετε στο {{applicationName}}',\n      title: 'Ελέγξτε το email σας',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: 'Επιστροφή στην αρχική καρτέλα για να συνεχίσετε.',\n        title: 'Αυτός ο σύνδεσμος επαλήθευσης έχει λήξει',\n      },\n      failed: {\n        subtitle: 'Επιστροφή στην αρχική καρτέλα για να συνεχίσετε.',\n        title: 'Αυτός ο σύνδεσμος επαλήθευσης δεν είναι έγκυρος',\n      },\n      formSubtitle: 'Χρησιμοποιήστε τον σύνδεσμο επαλήθευσης που απεστάλη στο email σας',\n      formTitle: 'Σύνδεσμος επαλήθευσης',\n      loading: {\n        subtitle: 'Θα ανακατευθυνθείτε σύντομα',\n        title: 'Σύνδεση σε εξέλιξη...',\n      },\n      resendButton: 'Δεν λάβατε σύνδεσμο; Αποστολή ξανά',\n      subtitle: 'για να συνεχίσετε στο {{applicationName}}',\n      title: 'Ελέγξτε το email σας',\n      unusedTab: {\n        title: 'Μπορείτε να κλείσετε αυτήν την καρτέλα',\n      },\n      verified: {\n        subtitle: 'Θα ανακατευθυνθείτε σύντομα',\n        title: 'Επιτυχής σύνδεση',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Επιστροφή στην αρχική καρτέλα για να συνεχίσετε',\n        subtitleNewTab: 'Επιστροφή στη νέα καρτέλα που άνοιξε για να συνεχίσετε',\n        titleNewTab: 'Έχετε συνδεθεί σε άλλη καρτέλα',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Επαναφορά κωδικού πρόσβασης',\n      resendButton: 'Δεν λάβατε κωδικό; Αποστολή ξανά',\n      subtitle: 'για να επαναφέρετε τον κωδικό σας',\n      subtitle_email: 'Πρώτα, εισάγετε τον κωδικό που στάλθηκε στη διεύθυνση email σας',\n      subtitle_phone: 'Πρώτα, εισάγετε τον κωδικό που στάλθηκε στο τηλέφωνό σας',\n      title: 'Επαναφορά κωδικού πρόσβασης',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Επαναφορά κωδικού πρόσβασης',\n      label__alternativeMethods: 'Ή, συνδεθείτε με μια άλλη μέθοδο.',\n      title: 'Ξεχάσατε τον κωδικό πρόσβασης;',\n    },\n    noAvailableMethods: {\n      message: 'Δεν είναι δυνατή η σύνδεση. Δεν υπάρχει διαθέσιμος παράγοντας αυθεντικοποίησης.',\n      subtitle: 'Προέκυψε σφάλμα',\n      title: 'Δεν είναι δυνατή η σύνδεση',\n    },\n    passkey: {\n      subtitle:\n        'Η χρήση του passkey σας επιβεβαιώνει την ταυτότητά σας. Η συσκευή σας μπορεί να ζητήσει δακτυλικό αποτύπωμα, αναγνώριση προσώπου ή κλείδωμα οθόνης.',\n      title: 'Χρησιμοποιήστε το passkey σας',\n    },\n    password: {\n      actionLink: 'Χρήση άλλης μεθόδου',\n      subtitle: 'για να συνεχίσετε στο {{applicationName}}',\n      title: 'Εισαγωγή κωδικού πρόσβασης',\n    },\n    passwordPwned: {\n      title: 'Παραβιασμένος κωδικός',\n    },\n    phoneCode: {\n      formTitle: 'Κωδικός επαλήθευσης',\n      resendButton: 'Δεν λάβατε κωδικό; Αποστολή ξανά',\n      subtitle: 'για να συνεχίσετε στο {{applicationName}}',\n      title: 'Ελέγξτε το τηλέφωνό σας',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Κωδικός επαλήθευσης',\n      resendButton: 'Δεν λάβατε κωδικό; Αποστολή ξανά',\n      subtitle: undefined,\n      title: 'Ελέγξτε το τηλέφωνό σας',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Επαναφορά κωδικού πρόσβασης',\n      requiredMessage:\n        'Υπάρχει ήδη λογαριασμός με μη επαληθευμένη διεύθυνση email. Παρακαλούμε επαναφέρετε τον κωδικό σας για λόγους ασφαλείας.',\n      successMessage: 'Ο κωδικός πρόσβασής σας έχει αλλάξει με επιτυχία. Σας συνδέουμε, παρακαλώ περιμένετε.',\n      title: 'Επαναφορά κωδικού πρόσβασης',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Πρέπει να επαληθεύσουμε την ταυτότητά σας πριν επαναφέρουμε τον κωδικό πρόσβασής σας.',\n    },\n    start: {\n      actionLink: 'Εγγραφή',\n      actionLink__join_waitlist: 'Εγγραφή στη λίστα αναμονής',\n      actionLink__use_email: 'Χρήση email',\n      actionLink__use_email_username: 'Χρήση email ή ονόματος χρήστη',\n      actionLink__use_passkey: 'Χρήση passkey',\n      actionLink__use_phone: 'Χρήση τηλεφώνου',\n      actionLink__use_username: 'Χρήση ονόματος χρήστη',\n      actionText: 'Δεν έχετε λογαριασμό;',\n      actionText__join_waitlist: 'Θέλετε πρώιμη πρόσβαση;',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'για να συνεχίσετε στο {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Σύνδεση',\n      titleCombined: 'Συνέχεια στο {{applicationName}}',\n    },\n    totpMfa: {\n      formTitle: 'Κωδικός επαλήθευσης',\n      subtitle: undefined,\n      title: 'Aυθεντικοποίηση δύο βημάτων',\n    },\n  },\n  signInEnterPasswordTitle: 'Εισαγωγή κωδικού πρόσβασης',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Σύνδεση',\n      actionText: 'Έχετε ήδη λογαριασμό;',\n      subtitle: 'για να συνεχίσετε στο {{applicationName}}',\n      title: 'Συμπληρώστε τα απαραίτητα πεδία',\n    },\n    emailCode: {\n      formSubtitle: 'Εισαγάγετε τον κωδικό επαλήθευσης που απεστάλη στο email σας',\n      formTitle: 'Κωδικός επαλήθευσης',\n      resendButton: 'Δεν λάβατε κωδικό; Αποστολή ξανά',\n      subtitle: 'για να συνεχίσετε στο {{applicationName}}',\n      title: 'Επαληθεύστε το email σας',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: 'Χρησιμοποιήστε τον σύνδεσμο επαλήθευσης που απεστάλη στη διεύθυνση email σας',\n      formTitle: 'Σύνδεσμος επαλήθευσης',\n      loading: {\n        title: 'Εγγραφή σε εξέλιξη...',\n      },\n      resendButton: 'Δεν λάβατε σύνδεσμο; Αποστολή ξανά',\n      subtitle: 'για να συνεχίσετε στο {{applicationName}}',\n      title: 'Επαληθεύστε το email σας',\n      verified: {\n        title: 'Επιτυχής εγγραφή',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Επιστροφή στη νέα καρτέλα για να συνεχίσετε',\n        subtitleNewTab: 'Επιστροφή στην προηγούμενη καρτέλα για να συνεχίσετε',\n        title: 'Επιτυχής επαλήθευση email',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Συμφωνώ με την {{ privacyPolicyLink || link(\"Πολιτική Απορρήτου\") }}',\n        label__onlyTermsOfService: 'Συμφωνώ με τους {{ termsOfServiceLink || link(\"Όρους Χρήσης\") }}',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Συμφωνώ με τους {{ termsOfServiceLink || link(\"Όρους Χρήσης\") }} και την {{ privacyPolicyLink || link(\"Πολιτική Απορρήτου\") }}',\n      },\n      continue: {\n        subtitle: 'Παρακαλώ διαβάστε και αποδεχτείτε τους όρους για να συνεχίσετε',\n        title: 'Νομική συναίνεση',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Εισαγάγετε τον κωδικό επαλήθευσης που απεστάλη στον αριθμό τηλεφώνου σας',\n      formTitle: 'Κωδικός επαλήθευσης',\n      resendButton: 'Δεν λάβατε κωδικό; Αποστολή ξανά',\n      subtitle: 'για να συνεχίσετε στο {{applicationName}}',\n      title: 'Επαληθεύστε το τηλέφωνό σας',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Σύνδεση',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: 'Έχετε ήδη λογαριασμό;',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'για να συνεχίσετε στο {{applicationName}}',\n      subtitleCombined: 'για να συνεχίσετε στο {{applicationName}}',\n      title: 'Δημιουργήστε τον λογαριασμό σας',\n      titleCombined: 'Δημιουργήστε τον λογαριασμό σας',\n    },\n  },\n  socialButtonsBlockButton: 'Συνέχεια με {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'Η εγγραφή απέτυχε λόγω αποτυχημένων ελέγχων ασφαλείας. Ανανεώστε τη σελίδα για να δοκιμάσετε ξανά ή επικοινωνήστε με το κέντρο υποστήριξης για περισσότερη βοήθεια.',\n    captcha_unavailable:\n      'Sign up unsuccessful due to failed bot validation. Please refresh the page to try again or reach out to support for more assistance.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: 'Δεν βρέθηκε λογαριασμός με αυτές τις λεπτομέρειες.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'Η διεύθυνση email πρέπει να είναι μια έγκυρη διεύθυνση email.',\n    form_param_format_invalid__phone_number: 'Phone number must be in a valid international format',\n    form_param_max_length_exceeded__first_name: 'First name should not exceed 256 characters.',\n    form_param_max_length_exceeded__last_name: 'Last name should not exceed 256 characters.',\n    form_param_max_length_exceeded__name: 'Name should not exceed 256 characters.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: 'Ο κωδικός πρόσβασής σας δεν είναι αρκετά ισχυρός.',\n    form_password_pwned:\n      'Αυτός ο κωδικός πρόσβασης έχει διαρρεύσει online στο παρελθόν και δεν μπορεί να χρησιμοποιηθεί. Δοκιμάστε έναν άλλο κωδικό πρόσβασης αντί για αυτόν.',\n    form_password_pwned__sign_in: undefined,\n    form_password_size_in_bytes_exceeded:\n      'Ο κωδικός πρόσβασής σας έχει υπερβεί το μέγιστο αριθμό bytes που επιτρέπεται. Παρακαλούμε, συντομεύστε τον ή αφαιρέστε μερικούς ειδικούς χαρακτήρες.',\n    form_password_validation_failed: 'Λανθασμένος κωδικός',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: 'Δεν μπορείτε να διαγράψετε το τελευταίο στοιχείο ταυτοποιησής σας.',\n    not_allowed_access:\n      \"Η διεύθυνση email ή το τηλέφωνο δεν επιτρέπεται για την εγγραφή. Αυτό μπορεί να οφείλεται στη χρήση '+', '=', '#' ή '.' στην διεύθυνση email σας, χρήση πεδίου που συνδέεται με υπηρεσία email, ή εμφανής αποκλεισμός. Αν πιστεύετε ότι αυτό είναι ένα σφάλμα, παρακαλούμε επικοινωνήστε με την υποστήριξη.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: 'λιγότερους από {{length}} χαρακτήρες',\n      minimumLength: '{{length}} ή περισσότερους χαρακτήρες',\n      requireLowercase: 'ένα πεζό γράμμα',\n      requireNumbers: 'έναν αριθμό',\n      requireSpecialCharacter: 'ένα ειδικό χαρακτήρα',\n      requireUppercase: 'ένα κεφαλαίο γράμμα',\n      sentencePrefix: 'Ο κωδικός πρόσβασής σας πρέπει να περιέχει',\n    },\n    phone_number_exists: 'Αυτός ο αριθμός τηλεφώνου χρησιμοποιείται ήδη. Δοκιμάστε έναν άλλο.',\n    session_exists: 'Έχετε ήδη συνδεθεί.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger:\n        'Ο κωδικός πρόσβασής σας είναι αρκετός, αλλά θα μπορούσε να είναι πιο ισχυρός. Δοκιμάστε να προσθέσετε περισσότερους χαρακτήρες.',\n      goodPassword: 'Ο κωδικός πρόσβασής σας πληροί όλες τις απαιτούμενες προδιαγραφές.',\n      notEnough: 'Ο κωδικός πρόσβασής σας δεν είναι αρκετά ισχυρός.',\n      suggestions: {\n        allUppercase: 'Έχετε μόνο μερικά κεφαλαία γράμματα.',\n        anotherWord: 'Προσθέστε περισσότερες λέξεις που είναι λιγότερο συνηθισμένες.',\n        associatedYears: 'Αποφύγετε έτη που σας αφορούν.',\n        capitalization: 'Μην έχετε κεφαλαίο μόνο το πρώτο γράμμα.',\n        dates: 'Αποφύγετε ημερομηνίες και έτη που σας αφορούν.',\n        l33t: \"Αποφύγετε προβλέψιμες αντικαταστάσεις γραμμάτων όπως '@' για 'a'.\",\n        longerKeyboardPattern:\n          'Χρησιμοποιήστε μεγαλύτερα μοτίβα πληκτρολογίου και αλλάξτε πολλές φορές την κατεύθυνση πληκτρολόγησης.',\n        noNeed:\n          'Μπορείτε να δημιουργήσετε ισχυρούς κωδικούς πρόσβασης χωρίς τη χρήση συμβόλων, αριθμών ή κεφαλαίων γραμμάτων.',\n        pwned: 'Αν χρησιμοποιείτε αυτόν τον κωδικό πρόσβασης και αλλού, θα πρέπει να τον αλλάξετε.',\n        recentYears: 'Αποφύγετε πρόσφατα έτη.',\n        repeated: 'Αποφύγετε επαναλαμβανόμενες λέξεις και χαρακτήρες.',\n        reverseWords: 'Αποφύγετε αντιστροφές συνηθισμένων λέξεων.',\n        sequences: 'Αποφύγετε κοινές ακολουθίες χαρακτήρων.',\n        useWords: 'Χρησιμοποιήστε πολλές λέξεις, αλλά αποφύγετε κοινές φράσεις.',\n      },\n      warnings: {\n        common: 'Αυτός είναι ένας κοινός κωδικός πρόσβασης.',\n        commonNames: 'Συνηθισμένα ονόματα και επώνυμα είναι εύκολα να μαντευτούν.',\n        dates: 'Ημερομηνίες είναι εύκολες να μαντευτούν.',\n        extendedRepeat: 'Επαναλαμβανόμενα μοτίβα χαρακτήρων όπως \"abcabcabc\" είναι εύκολα να μαντευτούν.',\n        keyPattern: 'Σύντομα μοτίβα πληκτρολογίου είναι εύκολα να μαντευτούν.',\n        namesByThemselves: 'Μεμονωμένα ονόματα ή επώνυμα είναι εύκολα να μαντευτούν.',\n        pwned: 'Ο κωδικός πρόσβασής σας αποκαλύφθηκε από παραβίαση δεδομένων στο διαδίκτυο.',\n        recentYears: 'Πρόσφατα έτη είναι εύκολα να μαντευτούν.',\n        sequences: 'Συνηθισμένες ακολουθίες χαρακτήρων όπως \"abc\" είναι εύκολα να μαντευτούν.',\n        similarToCommon: 'Αυτός είναι παρόμοιος με έναν κοινό κωδικό πρόσβασης.',\n        simpleRepeat: 'Επαναλαμβανόμενοι χαρακτήρες όπως \"aaa\" είναι εύκολο να μαντευτούν.',\n        straightRow: 'Σειρές γραμμάτων στο πληκτρολόγιο είναι εύκολα να μαντευτούν.',\n        topHundred: 'Αυτός είναι ένας συχνά χρησιμοποιούμενος κωδικός πρόσβασης.',\n        topTen: 'Αυτός είναι ένας πολύ διαδεδομένος κωδικός πρόσβασης.',\n        userInputs: 'Δεν πρέπει να υπάρχουν προσωπικά ή σχετικά με τη σελίδα δεδομένα.',\n        wordByItself: 'Οι μεμονωμένες λέξεις είναι εύκολες να μαντευτούν.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Προσθήκη λογαριασμού',\n    action__manageAccount: 'Διαχείριση λογαριασμού',\n    action__signOut: 'Αποσύνδεση',\n    action__signOutAll: 'Αποσύνδεση από όλους τους λογαριασμούς',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Αντιγράφηκαν!',\n      actionLabel__copy: 'Αντιγραφή όλων',\n      actionLabel__download: 'Λήψη .txt',\n      actionLabel__print: 'Εκτύπωση',\n      infoText1: 'Οι εφεδρικοί κωδικοί θα είναι ενεργοποιημένοι για αυτόν τον λογαριασμό.',\n      infoText2:\n        'Φυλάξτε τους εφεδρικούς κωδικούς μυστικούς και αποθηκεύστε τους με ασφάλεια. Μπορείτε να δημιουργήσετε νέους εφεδρικούς κωδικούς εάν υποψιάζεστε ότι έχουν διαρρεύσει.',\n      subtitle__codelist: 'Φυλάξτε τους με ασφάλεια και κρατήστε τους μυστικούς.',\n      successMessage:\n        'Οι εφεδρικοί κωδικοί είναι πλέον ενεργοποιημένοι. Μπορείτε να χρησιμοποιήσετε έναν από αυτούς για να συνδεθείτε στον λογαριασμό σας, εάν χάσετε την πρόσβαση στη συσκευή επαλήθευσής σας. Κάθε κωδικός μπορεί να χρησιμοποιηθεί μόνο μία φορά.',\n      successSubtitle:\n        'Μπορείτε να χρησιμοποιήσετε έναν από αυτούς για να συνδεθείτε στον λογαριασμό σας, εάν χάσετε την πρόσβαση στη συσκευή επαλήθευσής σας.',\n      title: 'Προσθήκη επαλήθευσης με εφεδρικούς κωδικούς',\n      title__codelist: 'Εφεδρικοί κωδικοί',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Επιλέξτε έναν πάροχο για να συνδέσετε τον λογαριασμό σας.',\n      formHint__noAccounts: 'Δεν υπάρχουν διαθέσιμοι πάροχοι εξωτερικών λογαριασμών.',\n      removeResource: {\n        messageLine1: 'Ο {{identifier}} θα αφαιρεθεί από αυτόν τον λογαριασμό.',\n        messageLine2:\n          'Δεν θα μπορείτε πλέον να χρησιμοποιήσετε αυτόν τον συνδεδεμένο λογαριασμό και οποιεσδήποτε εξαρτημένες λειτουργίες δεν θα λειτουργούν πλέον.',\n        successMessage: 'Ο {{connectedAccount}} έχει αφαιρεθεί από τον λογαριασμό σας.',\n        title: 'Αφαίρεση συνδεδεμένου λογαριασμού',\n      },\n      socialButtonsBlockButton: 'Σύνδεση με τον λογαριασμό {{provider|titleize}}',\n      successMessage: 'Ο πάροχος έχει προστεθεί στον λογαριασμό σας',\n      title: 'Προσθήκη συνδεδεμένου λογαριασμού',\n    },\n    deletePage: {\n      actionDescription: 'Πληκτρολογήστε \"Διαγραφή λογαριασμού\" παρακάτω για να συνεχίσετε.',\n      confirm: 'Διαγραφή λογαριασμού',\n      messageLine1: 'Είστε βέβαιος ότι θέλετε να διαγράψετε τον λογαριασμό σας;',\n      messageLine2: 'Αυτή η ενέργεια είναι μόνιμη και μη αναστρέψιμη.',\n      title: 'Διαγραφή λογαριασμού',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Θα σταλεί ένα email που περιέχει έναν κωδικό επαλήθευσης σε αυτήν τη διεύθυνση email.',\n        formSubtitle: 'Εισαγάγετε τον κωδικό επαλήθευσης που εστάλη στην {{identifier}}',\n        formTitle: 'Κωδικός επαλήθευσης',\n        resendButton: 'Δεν λάβατε κωδικό; Επανάληψη αποστολής',\n        successMessage: 'Το email {{identifier}} έχει προστεθεί στον λογαριασμό σας.',\n      },\n      emailLink: {\n        formHint: 'Θα σταλεί ένα email που περιέχει έναν σύνδεσμο επαλήθευσης σε αυτήν τη διεύθυνση email.',\n        formSubtitle: 'Κάντε κλικ στον σύνδεσμο επαλήθευσης στο email που εστάλη στην {{identifier}}',\n        formTitle: 'Σύνδεσμος επαλήθευσης',\n        resendButton: 'Δεν λάβατε κάποιον σύνδεσμο; Επανάληψη αποστολής',\n        successMessage: 'Το email {{identifier}} έχει προστεθεί στον λογαριασμό σας.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: 'Η διεύθυνση {{identifier}} θα αφαιρεθεί από αυτόν τον λογαριασμό.',\n        messageLine2: 'Δεν θα μπορείτε πλέον να συνδεθείτε χρησιμοποιώντας αυτήν τη διεύθυνση email.',\n        successMessage: 'Η διεύθυνση {{emailAddress}} έχει αφαιρεθεί από τον λογαριασμό σας.',\n        title: 'Αφαίρεση διεύθυνσης email',\n      },\n      title: 'Προσθήκη διεύθυνσης email',\n      verifyTitle: 'Verify email address',\n    },\n    formButtonPrimary__add: 'Προσθήκη',\n    formButtonPrimary__continue: 'Συνέχεια',\n    formButtonPrimary__finish: 'Ολοκλήρωση',\n    formButtonPrimary__remove: 'Αφαίρεση',\n    formButtonPrimary__save: 'Αποθήκευση',\n    formButtonReset: 'Ακύρωση',\n    mfaPage: {\n      formHint: 'Επιλέξτε μια μέθοδο για προσθήκη.',\n      title: 'Προσθήκη διπλής επαλήθευσης',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Use existing number',\n      primaryButton__addPhoneNumber: 'Προσθήκη αριθμού τηλεφώνου',\n      removeResource: {\n        messageLine1: 'Ο {{identifier}} δεν θα λαμβάνει πλέον κωδικούς επαλήθευσης κατά τη σύνδεση.',\n        messageLine2: 'Ο λογαριασμός σας ενδέχεται να μην είναι τόσο ασφαλής. Είστε σίγουροι ότι θέλετε να συνεχίσετε;',\n        successMessage: 'Η διπλή επαλήθευση με κωδικούς SMS έχει αφαιρεθεί για τον αριθμό τηλεφώνου {{mfaPhoneCode}}',\n        title: 'Αφαίρεση διπλής επαλήθευσης',\n      },\n      subtitle__availablePhoneNumbers:\n        'Επιλέξτε έναν αριθμό τηλεφώνου για να εγγραφείτε για τη διπλή επαλήθευση με κωδικούς SMS.',\n      subtitle__unavailablePhoneNumbers:\n        'Δεν υπάρχουν διαθέσιμοι αριθμοί τηλεφώνου για εγγραφή στην διπλή επαλήθευση με κωδικούς SMS.',\n      successMessage1:\n        'When signing in, you will need to enter a verification code sent to this phone number as an additional step.',\n      successMessage2:\n        'Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.',\n      successTitle: 'SMS code verification enabled',\n      title: 'Προσθήκη επαλήθευσης κωδικού SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Σάρωση QR κωδικού αντί αυτού',\n        buttonUnableToScan__nonPrimary: 'Δεν μπορείτε να σαρώσετε τον QR κωδικό;',\n        infoText__ableToScan:\n          'Ρυθμίστε μια νέα μέθοδο σύνδεσης στην εφαρμογή αυθεντικοποίησης σας και σαρώστε τον παρακάτω QR κωδικό για να τον συνδέσετε με τον λογαριασμό σας.',\n        infoText__unableToScan:\n          'Ρυθμίστε μια νέα μέθοδο σύνδεσης στην αυθεντικοποίηση και εισαγάγετε τον κλειδί που παρέχεται παρακάτω.',\n        inputLabel__unableToScan1:\n          'Βεβαιωθείτε ότι οι κωδικοί που βασίζονται στον χρόνο ή μίας χρήσης είναι ενεργοποιημένοι και ολοκληρώστε τη σύνδεση του λογαριασμού σας.',\n        inputLabel__unableToScan2:\n          'Εναλλακτικά, εάν η εφαρμογή αυθεντικοποίησής σας υποστηρίζει TOTP URIs, μπορείτε επίσης να αντιγράψετε τον πλήρη URI.',\n      },\n      removeResource: {\n        messageLine1:\n          'Δεν θα απαιτούνται πλέον κωδικοί επαλήθευσης από αυτήν την εφαρμογή αυθεντικοποίησης κατά τη σύνδεση.',\n        messageLine2: 'Ο λογαριασμός σας ενδέχεται να μην είναι τόσο ασφαλής. Είστε σίγουροι ότι θέλετε να συνεχίσετε;',\n        successMessage: 'Η διπλή επαλήθευση μέσω εφαρμογής αυθεντικοποίησης έχει αφαιρεθεί.',\n        title: 'Αφαίρεση διπλής επαλήθευσης',\n      },\n      successMessage:\n        'Η διπλή επαλήθευση είναι πλέον ενεργοποιημένη. Κατά τη σύνδεση, θα πρέπει να εισαγάγετε έναν κωδικό επαλήθευσης από αυτήν την εφαρμογή αυθεντικοποίησης ως επιπλέον βήμα.',\n      title: 'Προσθήκη εφαρμογής αυθεντικοποίησης',\n      verifySubtitle: 'Εισαγάγετε τον κωδικό επαλήθευσης που δημιουργήθηκε από την εφαρμογή αυθεντικοποίησης',\n      verifyTitle: 'Κωδικός επαλήθευσης',\n    },\n    mobileButton__menu: 'Μενού',\n    navbar: {\n      account: 'Προφίλ',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Διαχειριστείτε τις πληροφορίες του λογαριασμού σας.',\n      security: 'Ασφάλεια',\n      title: 'Λογαριασμός',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: undefined,\n        title: undefined,\n      },\n      subtitle__rename: undefined,\n      title__rename: undefined,\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'It is recommended to sign out of all other devices which may have used your old password.',\n      readonly:\n        'Ο κωδικός πρόσβασής σας δεν μπορεί να επεξεργαστεί αυτήν τη στιγμή επειδή μπορείτε να συνδεθείτε μόνο μέσω της σύνδεσης με την επιχείρηση.',\n      successMessage__set: 'Ο κωδικός πρόσβασής σας έχει οριστεί.',\n      successMessage__signOutOfOtherSessions: 'Όλες οι άλλες συνεδρίες έχουν αποσυνδεθεί.',\n      successMessage__update: 'Ο κωδικός πρόσβασής σας έχει ενημερωθεί.',\n      title__set: 'Ορισμός κωδικού πρόσβασης',\n      title__update: 'Αλλαγή κωδικού πρόσβασης',\n    },\n    phoneNumberPage: {\n      infoText: 'Θα σταλεί ένα μήνυμα κειμένου που περιέχει ένα σύνδεσμο επαλήθευσης σε αυτόν τον αριθμό τηλεφώνου.',\n      removeResource: {\n        messageLine1: 'Ο αριθμός {{identifier}} θα αφαιρεθεί από αυτόν τον λογαριασμό.',\n        messageLine2: 'Δεν θα μπορείτε πλέον να συνδεθείτε χρησιμοποιώντας αυτόν τον αριθμό τηλεφώνου.',\n        successMessage: 'Ο αριθμός τηλεφώνου {{phoneNumber}} έχει αφαιρεθεί από τον λογαριασμό σας.',\n        title: 'Αφαίρεση αριθμού τηλεφώνου',\n      },\n      successMessage: 'Ο αριθμός τηλεφώνου {{identifier}} έχει προστεθεί στον λογαριασμό σας.',\n      title: 'Προσθήκη αριθμού τηλεφώνου',\n      verifySubtitle: 'Enter the verification code sent to {{identifier}}',\n      verifyTitle: 'Verify phone number',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Ανεβάστε μια εικόνα σε μορφή JPG, PNG, GIF ή WEBP μικρότερη των 10 MB',\n      imageFormDestructiveActionSubtitle: 'Αφαίρεση εικόνας',\n      imageFormSubtitle: 'Ανέβασμα εικόνας',\n      imageFormTitle: 'Εικόνα προφίλ',\n      readonly:\n        'Οι πληροφορίες του προφίλ σας έχουν παρασχεθεί από τη σύνδεση με την επιχείρηση και δεν μπορούν να επεξεργαστούν.',\n      successMessage: 'Το προφίλ σας έχει ενημερωθεί.',\n      title: 'Ενημέρωση προφίλ',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Αποσύνδεση από τη συσκευή',\n        title: 'Ενεργές συσκευές',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Προσπάθεια ξανά',\n        actionLabel__reauthorize: 'Εξουσιοδοτήστε τώρα',\n        destructiveActionTitle: 'Αφαίρεση',\n        primaryButton: 'Σύνδεση λογαριασμού',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues',\n        title: 'Συνδεδεμένοι λογαριασμοί',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Διαγραφή λογαριασμού',\n        title: 'Κίνδυνος',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Αφαίρεση διεύθυνσης email',\n        detailsAction__nonPrimary: 'Ορισμός ως κύρια',\n        detailsAction__primary: 'Ολοκλήρωση επαλήθευσης',\n        detailsAction__unverified: 'Ολοκλήρωση επαλήθευσης',\n        primaryButton: 'Προσθήκη διεύθυνσης email',\n        title: 'Διευθύνσεις email',\n      },\n      enterpriseAccountsSection: {\n        title: 'Επιχειρησιακοί λογαριασμοί',\n      },\n      headerTitle__account: 'Λογαριασμός',\n      headerTitle__security: 'Ασφάλεια',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Επαναδημιουργία κωδικών',\n          headerTitle: 'Εφεδρικοί κωδικοί',\n          subtitle__regenerate:\n            'Λάβετε ένα νέο σετ ασφαλών εφεδρικών κωδικών. Οι προηγούμενοι εφεδρικοί κωδικοί θα διαγραφούν και δεν μπορούν να χρησιμοποιηθούν.',\n          title__regenerate: 'Επαναδημιουργία εφεδρικών κωδικών',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Ορισμός ως προεπιλεγμένος',\n          destructiveActionLabel: 'Αφαίρεση αριθμού τηλεφώνου',\n        },\n        primaryButton: 'Προσθήκη αυθεντικοποίησης δύο βημάτων',\n        title: 'Αυθεντικοποίηση δύο βημάτων',\n        totp: {\n          destructiveActionTitle: 'Αφαίρεση',\n          headerTitle: 'Εφαρμογή αυθεντικοποίησης',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: undefined,\n        menuAction__rename: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Ορισμός κωδικού πρόσβασης',\n        primaryButton__updatePassword: 'Αλλαγή κωδικού πρόσβασης',\n        title: 'Κωδικός πρόσβασης',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Αφαίρεση αριθμού τηλεφώνου',\n        detailsAction__nonPrimary: 'Ορισμός ως κύριος',\n        detailsAction__primary: 'Ολοκλήρωση επαλήθευσης',\n        detailsAction__unverified: 'Ολοκλήρωση επαλήθευσης',\n        primaryButton: 'Προσθήκη αριθμού τηλεφώνου',\n        title: 'Αριθμοί τηλεφώνου',\n      },\n      profileSection: {\n        primaryButton: 'Ενημέρωση προφίλ',\n        title: 'Προφίλ',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Ορισμός ονόματος χρήστη',\n        primaryButton__updateUsername: 'Αλλαγή ονόματος χρήστη',\n        title: 'Όνομα χρήστη',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Αφαίρεση πορτοφολιού',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Πορτοφόλια Web3',\n        title: 'Πορτοφόλια Web3',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Το όνομα χρήστη σας έχει ενημερωθεί.',\n      title__set: 'Ενημέρωση ονόματος χρήστη',\n      title__update: 'Ενημέρωση ονόματος χρήστη',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: 'Το {{identifier}} θα αφαιρεθεί από αυτόν τον λογαριασμό.',\n        messageLine2: 'Δεν θα μπορείτε πλέον να συνδεθείτε χρησιμοποιώντας αυτό το web3 πορτοφόλι.',\n        successMessage: 'Το {{web3Wallet}} έχει αφαιρεθεί από τον λογαριασμό σας.',\n        title: 'Αφαίρεση web3 πορτοφολιού',\n      },\n      subtitle__availableWallets: 'Επιλέξτε ένα web3 πορτοφόλι για να το συνδέσετε στον λογαριασμό σας.',\n      subtitle__unavailableWallets: 'Δεν υπάρχουν διαθέσιμα web3 πορτοφόλια.',\n      successMessage: 'Το πορτοφόλι έχει προστεθεί στον λογαριασμό σας.',\n      title: 'Προσθήκη web3 πορτοφολιού',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBACE;AAAA,MACF,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBACE;AAAA,MACF,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBACE;AAAA,QACF,QACE;AAAA,QACF,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cACE;AAAA,QACF,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UACE;AAAA,MACF,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}