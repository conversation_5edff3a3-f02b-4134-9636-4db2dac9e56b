// src/pt-BR.ts
var ptBR = {
  locale: "pt-BR",
  apiKeys: {
    action__add: void 0,
    action__search: void 0,
    createdAndExpirationStatus__expiresOn: void 0,
    createdAndExpirationStatus__never: void 0,
    detailsTitle__emptyRow: void 0,
    formButtonPrimary__add: void 0,
    formFieldCaption__expiration__expiresOn: void 0,
    formFieldCaption__expiration__never: void 0,
    formFieldOption__expiration__180d: void 0,
    formFieldOption__expiration__1d: void 0,
    formFieldOption__expiration__1y: void 0,
    formFieldOption__expiration__30d: void 0,
    formFieldOption__expiration__60d: void 0,
    formFieldOption__expiration__7d: void 0,
    formFieldOption__expiration__90d: void 0,
    formFieldOption__expiration__never: void 0,
    formHint: void 0,
    formTitle: void 0,
    lastUsed__days: void 0,
    lastUsed__hours: void 0,
    lastUsed__minutes: void 0,
    lastUsed__months: void 0,
    lastUsed__seconds: void 0,
    lastUsed__years: void 0,
    menuAction__revoke: void 0,
    revokeConfirmation: {
      confirmationText: void 0,
      formButtonPrimary__revoke: void 0,
      formHint: void 0,
      formTitle: void 0
    }
  },
  backButton: "Voltar",
  badge__activePlan: "Ativo",
  badge__canceledEndsAt: "Cancelado \u2022 Termina {{ date | shortDate('pt-BR') }}",
  badge__currentPlan: "Plano atual",
  badge__default: "Padr\xE3o",
  badge__endsAt: "Termina {{ date | shortDate('pt-BR') }}",
  badge__expired: "Expirado",
  badge__otherImpersonatorDevice: "Personificar outro dispositivo",
  badge__primary: "Principal",
  badge__renewsAt: "Renova {{ date | shortDate('pt-BR') }}",
  badge__requiresAction: "Requer a\xE7\xE3o",
  badge__startsAt: "Inicia {{ date | shortDate('pt-BR') }}",
  badge__thisDevice: "Este dispositivo",
  badge__unverified: "N\xE3o verificado",
  badge__upcomingPlan: "Pr\xF3ximo plano",
  badge__userDevice: "Dispositivo do usu\xE1rio",
  badge__you: "Voc\xEA",
  commerce: {
    addPaymentMethod: "Adicionar m\xE9todo de pagamento",
    alwaysFree: "Gratuito",
    annually: "Anualmente",
    availableFeatures: "Recursos dispon\xEDveis",
    billedAnnually: "Cobran\xE7a anual",
    billedMonthlyOnly: "Apenas cobran\xE7a mensal",
    cancelSubscription: "Cancelar assinatura",
    cancelSubscriptionAccessUntil: "Voc\xEA pode continuar usando os recursos de {{plan}} at\xE9 {{ date | longDate('pt-BR') }}, ap\xF3s o qual voc\xEA n\xE3o ter\xE1 mais acesso.",
    cancelSubscriptionNoCharge: "Voc\xEA n\xE3o ser\xE1 cobrado por esta assinatura.",
    cancelSubscriptionTitle: "Cancelar assinatura do plano {{plan}}?",
    cannotSubscribeMonthly: "Voc\xEA n\xE3o pode assinar este plano pagando mensalmente. Para assinar este plano, voc\xEA precisa escolher pagar anualmente.",
    checkout: {
      description__paymentSuccessful: "Seu pagamento foi realizado com sucesso.",
      description__subscriptionSuccessful: "Sua nova assinatura est\xE1 pronta.",
      downgradeNotice: "Voc\xEA manter\xE1 sua assinatura atual e seus recursos at\xE9 o final do ciclo de faturamento, ap\xF3s o qual voc\xEA ser\xE1 transferido para este plano.",
      emailForm: {
        subtitle: "Antes de concluir sua compra, voc\xEA deve adicionar um endere\xE7o de e-mail para o qual os recibos ser\xE3o enviados.",
        title: "Adicionar endere\xE7o de e-mail"
      },
      lineItems: {
        title__paymentMethod: "M\xE9todo de pagamento",
        title__statementId: "ID da declara\xE7\xE3o",
        title__subscriptionBegins: "Assinatura come\xE7a",
        title__totalPaid: "Total pago"
      },
      pastDueNotice: "Sua assinatura anterior estava em atraso, sem pagamento.",
      perMonth: "por m\xEAs",
      title: "Checkout",
      title__paymentSuccessful: "Pagamento realizado com sucesso!",
      title__subscriptionSuccessful: "Sucesso!"
    },
    credit: "Cr\xE9dito",
    creditRemainder: "Cr\xE9dito para o restante da sua assinatura atual.",
    defaultFreePlanActive: "Voc\xEA est\xE1 atualmente no plano Gratuito",
    free: "Gratuito",
    getStarted: "Come\xE7ar",
    keepSubscription: "Manter assinatura",
    manage: "Gerenciar",
    manageSubscription: "Gerenciar assinatura",
    month: "M\xEAs",
    monthly: "Mensal",
    pastDue: "Atrasado",
    pay: "Pagar {{amount}}",
    paymentMethods: "M\xE9todos de pagamento",
    paymentSource: {
      applePayDescription: {
        annual: "Pagamento anual",
        monthly: "Pagamento mensal"
      },
      dev: {
        anyNumbers: "Qualquer n\xFAmero",
        cardNumber: "N\xFAmero do cart\xE3o",
        cvcZip: "CVC, CEP",
        developmentMode: "Modo de desenvolvimento",
        expirationDate: "Data de validade",
        testCardInfo: "Informa\xE7\xF5es do cart\xE3o de teste"
      }
    },
    popular: "Popular",
    pricingTable: {
      billingCycle: "Ciclo de faturamento",
      included: "Incluso"
    },
    reSubscribe: "Assinar novamente",
    seeAllFeatures: "Ver todos os recursos",
    subscribe: "Assinar",
    subtotal: "Subtotal",
    switchPlan: "Mudar de plano",
    switchToAnnual: "Mudar para anual",
    switchToMonthly: "Mudar para mensal",
    totalDue: "Total devido",
    totalDueToday: "Total devido hoje",
    viewFeatures: "Ver recursos",
    year: "Ano"
  },
  createOrganization: {
    formButtonSubmit: "Criar organiza\xE7\xE3o",
    invitePage: {
      formButtonReset: "Pular"
    },
    title: "Criar organiza\xE7\xE3o"
  },
  dates: {
    lastDay: "Ontem \xE0s {{ date | timeString('pt-BR') }}",
    next6Days: "{{ date | weekday('pt-BR','long') }} \xE0s {{ date | timeString('pt-BR') }}",
    nextDay: "Amanh\xE3 \xE0s {{ date | timeString('pt-BR') }}",
    numeric: "{{ date | numeric('pt-BR') }}",
    previous6Days: "\xDAltimo {{ date | weekday('pt-BR','long') }} \xE0s {{ date | timeString('pt-BR') }}",
    sameDay: "Hoje \xE0s {{ date | timeString('pt-BR') }}"
  },
  dividerText: "ou",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Utilize outro m\xE9todo",
  footerPageLink__help: "Ajuda",
  footerPageLink__privacy: "Privacidade",
  footerPageLink__terms: "Termos de uso",
  formButtonPrimary: "Continuar",
  formButtonPrimary__verify: "Verificar",
  formFieldAction__forgotPassword: "Esqueceu a senha?",
  formFieldError__matchingPasswords: "Senhas conferem.",
  formFieldError__notMatchingPasswords: "Senhas n\xE3o conferem.",
  formFieldError__verificationLinkExpired: "O link de verifica\xE7\xE3o expirou. Por favor solicite um novo link.",
  formFieldHintText__optional: "Opcional",
  formFieldHintText__slug: "Um r\xF3tulo \xE9 um identificador leg\xEDvel por humanos que deve ser \xFAnico. \xC9 comumente usado em URLs.",
  formFieldInputPlaceholder__apiKeyDescription: void 0,
  formFieldInputPlaceholder__apiKeyExpirationDate: void 0,
  formFieldInputPlaceholder__apiKeyName: void 0,
  formFieldInputPlaceholder__backupCode: "Insira o c\xF3digo de backup",
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Excluir conta",
  formFieldInputPlaceholder__emailAddress: "Digite o endere\xE7o de e-mail",
  formFieldInputPlaceholder__emailAddress_username: "Digite seu e-mail ou nome de usu\xE1rio",
  formFieldInputPlaceholder__emailAddresses: "Insira um ou mais endere\xE7os de e-mail separados por espa\xE7os ou v\xEDrgulas",
  formFieldInputPlaceholder__firstName: "Digite seu primeiro nome",
  formFieldInputPlaceholder__lastName: "Digite seu sobrenome",
  formFieldInputPlaceholder__organizationDomain: "Digite o dom\xEDnio da organiza\xE7\xE3o",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "Digite o e-mail associado ao dom\xEDnio da organiza\xE7\xE3o",
  formFieldInputPlaceholder__organizationName: "Digite o nome da organiza\xE7\xE3o",
  formFieldInputPlaceholder__organizationSlug: "minha-org",
  formFieldInputPlaceholder__password: "Digite sua senha",
  formFieldInputPlaceholder__phoneNumber: "Digite seu n\xFAmero de telefone",
  formFieldInputPlaceholder__username: "Digite seu nome de usu\xE1rio",
  formFieldLabel__apiKeyDescription: void 0,
  formFieldLabel__apiKeyExpiration: void 0,
  formFieldLabel__apiKeyName: void 0,
  formFieldLabel__automaticInvitations: "Ativar convites autom\xE1ticos para este dom\xEDnio",
  formFieldLabel__backupCode: "C\xF3digo de backup",
  formFieldLabel__confirmDeletion: "Confirmar exclus\xE3o",
  formFieldLabel__confirmPassword: "Confirmar senha",
  formFieldLabel__currentPassword: "Senha atual",
  formFieldLabel__emailAddress: "Seu e-mail",
  formFieldLabel__emailAddress_username: "E-mail ou nome de usu\xE1rio",
  formFieldLabel__emailAddresses: "Endere\xE7os de e-mail",
  formFieldLabel__firstName: "Nome",
  formFieldLabel__lastName: "Sobrenome",
  formFieldLabel__newPassword: "Nova senha",
  formFieldLabel__organizationDomain: "Dom\xEDnio",
  formFieldLabel__organizationDomainDeletePending: "Excluir convites e sugest\xF5es pendentes",
  formFieldLabel__organizationDomainEmailAddress: "Endere\xE7o de e-mail de verifica\xE7\xE3o",
  formFieldLabel__organizationDomainEmailAddressDescription: "Endere\xE7o de e-mail para receber um c\xF3digo e verificar este dom\xEDnio",
  formFieldLabel__organizationName: "Nome da organiza\xE7\xE3o",
  formFieldLabel__organizationSlug: "R\xF3tulo do URL",
  formFieldLabel__passkeyName: "Nome da chave de acesso",
  formFieldLabel__password: "Senha",
  formFieldLabel__phoneNumber: "Telefone",
  formFieldLabel__role: "Fun\xE7\xE3o",
  formFieldLabel__signOutOfOtherSessions: "Desconectar de todos os outros dispositivos",
  formFieldLabel__username: "Nome de usu\xE1rio",
  impersonationFab: {
    action__signOut: "Sair",
    title: "Logado como {{identifier}}"
  },
  maintenanceMode: "Estamos em manuten\xE7\xE3o, mas n\xE3o se preocupe, n\xE3o deve levar mais do que alguns minutos",
  membershipRole__admin: "Administrador",
  membershipRole__basicMember: "Membro",
  membershipRole__guestMember: "Convidado",
  organizationList: {
    action__createOrganization: "Criar organiza\xE7\xE3o",
    action__invitationAccept: "Participar",
    action__suggestionsAccept: "Solicitar participa\xE7\xE3o",
    createOrganization: "Criar organiza\xE7\xE3o",
    invitationAcceptedLabel: "Participando",
    subtitle: "para continuar no {{applicationName}}",
    suggestionsAcceptedLabel: "Aprova\xE7\xE3o pendente",
    title: "Selecione uma conta",
    titleWithoutPersonal: "Selecione uma organiza\xE7\xE3o"
  },
  organizationProfile: {
    apiKeysPage: {
      title: void 0
    },
    badge__automaticInvitation: "Convites autom\xE1ticos",
    badge__automaticSuggestion: "Sugest\xF5es autom\xE1ticas",
    badge__manualInvitation: "Sem inscri\xE7\xE3o autom\xE1tica",
    badge__unverified: "N\xE3o verificado",
    billingPage: {
      paymentHistorySection: {
        empty: "Nenhum hist\xF3rico de pagamento",
        notFound: "Pagamento n\xE3o encontrado",
        tableHeader__amount: "Valor",
        tableHeader__date: "Data",
        tableHeader__status: "Status"
      },
      paymentSourcesSection: {
        actionLabel__default: "Tornar padr\xE3o",
        actionLabel__remove: "Remover",
        add: "Adicionar novo m\xE9todo de pagamento",
        addSubtitle: "Adicione um novo m\xE9todo de pagamento \xE0 sua conta.",
        cancelButton: "Cancelar",
        formButtonPrimary__add: "Adicionar M\xE9todo de Pagamento",
        formButtonPrimary__pay: "Pagar {{amount}}",
        payWithTestCardButton: "Pagar com cart\xE3o de teste",
        removeResource: {
          messageLine1: "{{identifier}} ser\xE1 removido desta conta.",
          messageLine2: "Voc\xEA n\xE3o poder\xE1 mais usar esta forma de pagamento e quaisquer assinaturas recorrentes dependentes dela deixar\xE3o de funcionar.",
          successMessage: "{{paymentSource}} foi removido da sua conta.",
          title: "Remover m\xE9todo de pagamento"
        },
        title: "M\xE9todos de pagamento"
      },
      start: {
        headerTitle__payments: "Pagamentos",
        headerTitle__plans: "Planos",
        headerTitle__statements: "Extratos",
        headerTitle__subscriptions: "Assinaturas"
      },
      statementsSection: {
        empty: "Nenhum extrato para exibir",
        itemCaption__paidForPlan: "Pago para plano {{plan}} {{period}}",
        itemCaption__proratedCredit: "Cr\xE9dito proporcional para uso parcial do plano anterior",
        itemCaption__subscribedAndPaidForPlan: "Assinado e pago para plano {{plan}} {{period}}",
        notFound: "Extrato n\xE3o encontrado",
        tableHeader__amount: "Valor",
        tableHeader__date: "Data",
        title: "Extratos",
        totalPaid: "Total pago"
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: "Assinar um plano",
        actionLabel__switchPlan: "Mudar de plano",
        tableHeader__edit: "Editar",
        tableHeader__plan: "Plano",
        tableHeader__startDate: "Data de in\xEDcio",
        title: "Assinatura"
      },
      subscriptionsSection: {
        actionLabel__default: "Gerenciar"
      },
      switchPlansSection: {
        title: "Mudar de plano"
      },
      title: "Faturamento"
    },
    createDomainPage: {
      subtitle: "Adicione o dom\xEDnio para verificar. Usu\xE1rios com endere\xE7os de e-mail neste dom\xEDnio podem se juntar \xE0 organiza\xE7\xE3o automaticamente ou solicitar participa\xE7\xE3o.",
      title: "Adicionar dom\xEDnio"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Os convites n\xE3o puderam ser enviados. J\xE1 existem convites pendentes para os seguintes endere\xE7os de e-mail: {{email_addresses}}.",
      formButtonPrimary__continue: "Enviar convites",
      selectDropdown__role: "Selecione a fun\xE7\xE3o",
      subtitle: "Insira ou cole um ou mais endere\xE7os de e-mail, separados por espa\xE7os ou v\xEDrgulas.",
      successMessage: "Convites enviados com sucesso",
      title: "Convidar membros"
    },
    membersPage: {
      action__invite: "Convidar",
      action__search: "Pesquisar",
      activeMembersTab: {
        menuAction__remove: "Remover membro",
        tableHeader__actions: "A\xE7\xF5es",
        tableHeader__joined: "Entrou",
        tableHeader__role: "Fun\xE7\xE3o",
        tableHeader__user: "Usu\xE1rio"
      },
      detailsTitle__emptyRow: "Nenhum membro para exibir",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Convide usu\xE1rios conectando um dom\xEDnio de e-mail com sua organiza\xE7\xE3o. Qualquer pessoa que se inscrever com um dom\xEDnio de e-mail correspondente poder\xE1 se juntar \xE0 organiza\xE7\xE3o a qualquer momento.",
          headerTitle: "Convites autom\xE1ticos",
          primaryButton: "Gerenciar dom\xEDnios verificados"
        },
        table__emptyRow: "Nenhum convite para exibir"
      },
      invitedMembersTab: {
        menuAction__revoke: "Cancelar convite",
        tableHeader__invited: "Convidado"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Usu\xE1rios que se inscrevem com um dom\xEDnio de e-mail correspondente podem ver uma sugest\xE3o para solicitar participa\xE7\xE3o em sua organiza\xE7\xE3o.",
          headerTitle: "Sugest\xF5es autom\xE1ticas",
          primaryButton: "Gerenciar dom\xEDnios verificados"
        },
        menuAction__approve: "Aprovar",
        menuAction__reject: "Rejeitar",
        tableHeader__requested: "Acesso solicitado",
        table__emptyRow: "Nenhuma solicita\xE7\xE3o para exibir"
      },
      start: {
        headerTitle__invitations: "Convites",
        headerTitle__members: "Membros",
        headerTitle__requests: "Solicita\xE7\xF5es"
      }
    },
    navbar: {
      apiKeys: void 0,
      billing: "Faturamento",
      description: "Gerencie sua organiza\xE7\xE3o.",
      general: "Geral",
      members: "Membros",
      title: "Organiza\xE7\xE3o"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: "Voc\xEA n\xE3o tem permiss\xF5es para gerenciar o faturamento desta organiza\xE7\xE3o."
      },
      title: "Planos"
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: "Digite {{organizationName}} abaixo para continuar.",
          messageLine1: "Tem certeza de que deseja excluir esta organiza\xE7\xE3o?",
          messageLine2: "Esta a\xE7\xE3o \xE9 permanente e irrevers\xEDvel.",
          successMessage: "Voc\xEA excluiu a organiza\xE7\xE3o.",
          title: "Excluir organiza\xE7\xE3o"
        },
        leaveOrganization: {
          actionDescription: "Digite {{organizationName}} abaixo para continuar.",
          messageLine1: "Tem certeza de que deseja sair desta organiza\xE7\xE3o? Voc\xEA perder\xE1 o acesso a esta organiza\xE7\xE3o e suas aplica\xE7\xF5es.",
          messageLine2: "Esta a\xE7\xE3o \xE9 permanente e n\xE3o pode ser desfeita.",
          successMessage: "Voc\xEA saiu da organiza\xE7\xE3o.",
          title: "Sair da organiza\xE7\xE3o"
        },
        title: "Perigo"
      },
      domainSection: {
        menuAction__manage: "Gerenciar",
        menuAction__remove: "Excluir",
        menuAction__verify: "Verificar",
        primaryButton: "Adicionar dom\xEDnio",
        subtitle: "Permita que os usu\xE1rios se juntem \xE0 organiza\xE7\xE3o automaticamente ou solicitem participa\xE7\xE3o com base em um dom\xEDnio de e-mail verificado.",
        title: "Dom\xEDnios verificados"
      },
      successMessage: "A organiza\xE7\xE3o foi atualizada.",
      title: "Perfil da organiza\xE7\xE3o"
    },
    removeDomainPage: {
      messageLine1: "O dom\xEDnio de e-mail {{domain}} ser\xE1 removido.",
      messageLine2: "Os usu\xE1rios n\xE3o poder\xE3o mais se juntar \xE0 organiza\xE7\xE3o automaticamente ap\xF3s isso.",
      successMessage: "{{domain}} foi removido.",
      title: "Excluir dom\xEDnio"
    },
    start: {
      headerTitle__general: "Geral",
      headerTitle__members: "Membros",
      profileSection: {
        primaryButton: "Atualizar perfil",
        title: "Perfil da Organiza\xE7\xE3o",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "A exclus\xE3o deste dom\xEDnio afetar\xE1 os usu\xE1rios convidados.",
        removeDomainActionLabel__remove: "Excluir dom\xEDnio",
        removeDomainSubtitle: "Remova este dom\xEDnio de seus dom\xEDnios verificados",
        removeDomainTitle: "Excluir dom\xEDnio"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Os usu\xE1rios s\xE3o convidados automaticamente a se juntar \xE0 organiza\xE7\xE3o quando se inscrevem e podem se juntar a qualquer momento.",
        automaticInvitationOption__label: "Convites autom\xE1ticos",
        automaticSuggestionOption__description: "Os usu\xE1rios recebem uma sugest\xE3o para solicitar participa\xE7\xE3o, mas devem ser aprovados por um administrador antes de poderem se juntar \xE0 organiza\xE7\xE3o.",
        automaticSuggestionOption__label: "Sugest\xF5es autom\xE1ticas",
        calloutInfoLabel: "Alterar o modo de inscri\xE7\xE3o afetar\xE1 apenas os novos usu\xE1rios.",
        calloutInvitationCountLabel: "Convites pendentes enviados aos usu\xE1rios: {{count}}",
        calloutSuggestionCountLabel: "Sugest\xF5es pendentes enviadas aos usu\xE1rios: {{count}}",
        manualInvitationOption__description: "Os usu\xE1rios s\xF3 podem ser convidados manualmente para a organiza\xE7\xE3o.",
        manualInvitationOption__label: "Sem inscri\xE7\xE3o autom\xE1tica",
        subtitle: "Escolha como os usu\xE1rios deste dom\xEDnio podem se juntar \xE0 organiza\xE7\xE3o."
      },
      start: {
        headerTitle__danger: "Perigo",
        headerTitle__enrollment: "Op\xE7\xF5es de inscri\xE7\xE3o"
      },
      subtitle: "O dom\xEDnio {{domain}} agora est\xE1 verificado. Continue selecionando o modo de inscri\xE7\xE3o.",
      title: "Atualizar {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Insira o c\xF3digo de verifica\xE7\xE3o enviado para o seu endere\xE7o de e-mail",
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "N\xE3o recebeu um c\xF3digo? Reenviar",
      subtitle: "O dom\xEDnio {{domainName}} precisa ser verificado por e-mail.",
      subtitleVerificationCodeScreen: "Um c\xF3digo de verifica\xE7\xE3o foi enviado para {{emailAddress}}. Insira o c\xF3digo para continuar.",
      title: "Verificar dom\xEDnio"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Criar organiza\xE7\xE3o",
    action__invitationAccept: "Participar",
    action__manageOrganization: "Gerenciar organiza\xE7\xE3o",
    action__suggestionsAccept: "Solicitar participa\xE7\xE3o",
    notSelected: "Nenhuma organiza\xE7\xE3o selecionada",
    personalWorkspace: "Conta pessoal",
    suggestionsAcceptedLabel: "Aprova\xE7\xE3o pendente"
  },
  paginationButton__next: "Pr\xF3ximo",
  paginationButton__previous: "Anterior",
  paginationRowText__displaying: "Exibindo",
  paginationRowText__of: "de",
  reverification: {
    alternativeMethods: {
      actionLink: "Solicitar ajuda",
      actionText: "N\xE3o tem nenhum dos m\xE9todos? Tente outra forma.",
      blockButton__backupCode: "Usar c\xF3digo de backup",
      blockButton__emailCode: "Enviar c\xF3digo para {{identifier}}",
      blockButton__passkey: "Usar sua chave de acesso",
      blockButton__password: "Usar senha",
      blockButton__phoneCode: "Enviar c\xF3digo de telefone",
      blockButton__totp: "Usar autentica\xE7\xE3o TOTP",
      getHelp: {
        blockButton__emailSupport: "Entrar em contato com o suporte",
        content: "Se voc\xEA n\xE3o tem nenhum dos m\xE9todos listados, entre em contato com nosso suporte.",
        title: "Solicitar ajuda"
      },
      subtitle: "Escolha um dos m\xE9todos alternativos para verificar sua identidade.",
      title: "M\xE9todos alternativos de verifica\xE7\xE3o"
    },
    backupCodeMfa: {
      subtitle: "Digite seu c\xF3digo de backup para continuar.",
      title: "Verifica\xE7\xE3o com c\xF3digo de backup"
    },
    emailCode: {
      formTitle: "C\xF3digo enviado para seu e-mail",
      resendButton: "Reenviar c\xF3digo",
      subtitle: "Verifique seu e-mail e insira o c\xF3digo para continuar.",
      title: "Verifique seu e-mail"
    },
    noAvailableMethods: {
      message: "Nenhum m\xE9todo de verifica\xE7\xE3o dispon\xEDvel. Entre em contato com o suporte.",
      subtitle: "N\xE3o h\xE1 m\xE9todos de verifica\xE7\xE3o dispon\xEDveis no momento.",
      title: "M\xE9todos de verifica\xE7\xE3o indispon\xEDveis"
    },
    passkey: {
      blockButton__passkey: "Usar sua chave de acesso",
      subtitle: "Usar sua chave de acesso confirma a sua identidade. Seu dispositivo pode solicitar sua impress\xE3o digital, reconhecimento facial ou PIN.",
      title: "Use sua chave de acesso."
    },
    password: {
      actionLink: "Usar outro m\xE9todo",
      subtitle: "Digite sua senha para continuar.",
      title: "Digite sua senha"
    },
    phoneCode: {
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "Reenviar c\xF3digo",
      subtitle: "Verifique seu celular para o c\xF3digo de verifica\xE7\xE3o.",
      title: "Verifique seu celular"
    },
    phoneCodeMfa: {
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "Reenviar c\xF3digo",
      subtitle: "Verifique seu celular para o c\xF3digo de verifica\xE7\xE3o.",
      title: "Verifique seu celular"
    },
    totpMfa: {
      formTitle: "C\xF3digo de verifica\xE7\xE3o TOTP",
      subtitle: "Digite o c\xF3digo de verifica\xE7\xE3o gerado pelo seu aplicativo de autentica\xE7\xE3o.",
      title: "Autentica\xE7\xE3o TOTP"
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Adicionar conta",
      action__signOutAll: "Sair de todas as contas",
      subtitle: "Selecione a conta com a qual gostaria de continuar.",
      title: "Escolha uma conta."
    },
    alternativeMethods: {
      actionLink: "Ajuda",
      actionText: "N\xE3o tem nenhum destes?",
      blockButton__backupCode: "Utilize um c\xF3digo de backup",
      blockButton__emailCode: "Enviar c\xF3digo para {{identifier}}",
      blockButton__emailLink: "Enviar link para {{identifier}}",
      blockButton__passkey: "Acessar com sua chave de acesso",
      blockButton__password: "Acessar com sua senha",
      blockButton__phoneCode: "Enviar c\xF3digo para {{identifier}}",
      blockButton__totp: "Utilize seu aplicativo autenticador",
      getHelp: {
        blockButton__emailSupport: "E-mail de suporte",
        content: "Se estiver com dificuldades para entrar em sua conta, envie um e-mail para n\xF3s que iremos te ajudar a restaurar seu acesso o mais r\xE1pido poss\xEDvel.",
        title: "Ajuda"
      },
      subtitle: "Encontrando dificuldades? Voc\xEA pode utilizar qualquer um destes m\xE9todos para acessar.",
      title: "Utilize outro m\xE9todo"
    },
    alternativePhoneCodeProvider: {
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "Reenviar c\xF3digo",
      subtitle: "Verifique seu celular para o c\xF3digo de verifica\xE7\xE3o.",
      title: "Verifique seu celular"
    },
    backupCodeMfa: {
      subtitle: "para continuar em {{applicationName}}",
      title: "Insira um c\xF3digo de backup"
    },
    emailCode: {
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "Reenviar c\xF3digo",
      subtitle: "para continuar em {{applicationName}}",
      title: "Verifique seu e-mail"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Para continuar, abra o link de verifica\xE7\xE3o no mesmo dispositivo e navegador em que iniciou o login",
        title: "Link de verifica\xE7\xE3o \xE9 inv\xE1lido para este dispositivo"
      },
      expired: {
        subtitle: "Retorne para a aba original para continuar",
        title: "Este link de verifica\xE7\xE3o expirou"
      },
      failed: {
        subtitle: "Retorne para a aba original para continuar",
        title: "Este link de verifica\xE7\xE3o \xE9 inv\xE1lido"
      },
      formSubtitle: "Utilize o link enviado no seu e-mail",
      formTitle: "Link de verifica\xE7\xE3o",
      loading: {
        subtitle: "Voc\xEA ser\xE1 redirecionado em breve",
        title: "Conectando..."
      },
      resendButton: "N\xE3o recebeu um link? Reenviar",
      subtitle: "para continuar em {{applicationName}}",
      title: "Verifique seu e-mail",
      unusedTab: {
        title: "Voc\xEA pode fechar esta aba"
      },
      verified: {
        subtitle: "Voc\xEA ser\xE1 redirecionado em breve",
        title: "Login realizado com sucesso"
      },
      verifiedSwitchTab: {
        subtitle: "Retorne para a aba original para continuar",
        subtitleNewTab: "Retorne para a nova aba que foi aberta para continuar",
        titleNewTab: "Conectado em outra aba"
      }
    },
    forgotPassword: {
      formTitle: "C\xF3digo de redefini\xE7\xE3o de senha",
      resendButton: "N\xE3o recebeu um c\xF3digo? Reenviar",
      subtitle: "para redefinir sua senha",
      subtitle_email: "Primeiro, digite o c\xF3digo enviado para seu e-mail",
      subtitle_phone: "Primeiro, digite o c\xF3digo enviado para seu telefone",
      title: "Redefinir senha"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Redefinir sua senha",
      label__alternativeMethods: "Ou, fa\xE7a login com outro m\xE9todo.",
      title: "Esqueceu a senha?"
    },
    noAvailableMethods: {
      message: "N\xE3o foi poss\xEDvel fazer login. N\xE3o h\xE1 nenhum m\xE9todo de autentica\xE7\xE3o dispon\xEDvel.",
      subtitle: "Aconteceu um erro",
      title: "N\xE3o foi poss\xEDvel fazer login"
    },
    passkey: {
      subtitle: "Usar sua chave de acesso confirma a sua identidade. Seu dispositivo pode solicitar sua impress\xE3o digital, reconhecimento facial ou PIN.",
      title: "Use sua chave de acesso."
    },
    password: {
      actionLink: "Utilize outro m\xE9todo",
      subtitle: "para continuar em {{applicationName}}",
      title: "Insira sua senha"
    },
    passwordPwned: {
      title: "Senha comprometida"
    },
    phoneCode: {
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "Reenviar c\xF3digo",
      subtitle: "para continuar em {{applicationName}}",
      title: "Verifique seu telefone"
    },
    phoneCodeMfa: {
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "Reenviar c\xF3digo",
      subtitle: "Para continuar, insira o c\xF3digo enviado para o seu telefone.",
      title: "Verifique seu telefone"
    },
    resetPassword: {
      formButtonPrimary: "Redefinir Senha",
      requiredMessage: "Por raz\xF5es de seguran\xE7a, \xE9 necess\xE1rio redefinir sua senha.",
      successMessage: "Sua senha foi alterada com sucesso. Entrando, por favor aguarde um momento.",
      title: "Redefinir Senha"
    },
    resetPasswordMfa: {
      detailsLabel: "Precisamos verificar sua identidade antes de redefinir sua senha."
    },
    start: {
      actionLink: "Registre-se",
      actionLink__join_waitlist: "Entrar na lista de espera",
      actionLink__use_email: "Usar e-mail",
      actionLink__use_email_username: "Usar e-mail ou nome de usu\xE1rio",
      actionLink__use_passkey: "Ou use sua chave de acesso",
      actionLink__use_phone: "Usar telefone",
      actionLink__use_username: "Usar nome de usu\xE1rio",
      actionText: "N\xE3o possui uma conta?",
      actionText__join_waitlist: "Quer ser notificado quando estivermos prontos?",
      alternativePhoneCodeProvider: {
        actionLink: "Usar outro m\xE9todo",
        label: "{{provider}} telefone",
        subtitle: "Insira seu n\xFAmero de telefone para receber um c\xF3digo de verifica\xE7\xE3o em {{provider}}.",
        title: "Entrar no {{applicationName}} com {{provider}}"
      },
      subtitle: "para continuar em {{applicationName}}",
      subtitleCombined: void 0,
      title: "Entrar",
      titleCombined: "Continuar em {{applicationName}}"
    },
    totpMfa: {
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      subtitle: "Para continuar, insira o c\xF3digo gerado pelo seu aplicativo autenticador.",
      title: "Verifica\xE7\xE3o em duas etapas"
    }
  },
  signInEnterPasswordTitle: "Insira sua senha",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: "N\xE3o recebeu um c\xF3digo? Reenviar",
      subtitle: "Insira o c\xF3digo de verifica\xE7\xE3o enviado para seu {{provider}}",
      title: "Verifique seu {{provider}}"
    },
    continue: {
      actionLink: "Entrar",
      actionText: "Possui uma conta?",
      subtitle: "para continuar em {{applicationName}}",
      title: "Preencha os campos ausentes"
    },
    emailCode: {
      formSubtitle: "Insira o c\xF3digo enviado para seu e-mail",
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "N\xE3o recebeu o c\xF3digo? Reenviar",
      subtitle: "para continuar em {{applicationName}}",
      title: "Verifique seu e-mail"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Para continuar, abra o link de verifica\xE7\xE3o no mesmo dispositivo e navegador em que iniciou o cadastro",
        title: "Link de verifica\xE7\xE3o \xE9 inv\xE1lido para este dispositivo"
      },
      formSubtitle: "Utilize o link enviado no seu e-mail",
      formTitle: "Link de verifica\xE7\xE3o",
      loading: {
        title: "Conectando..."
      },
      resendButton: "Reenviar link",
      subtitle: "para continuar em {{applicationName}}",
      title: "Verifique seu e-mail",
      verified: {
        title: "Cadastro realizado com sucesso"
      },
      verifiedSwitchTab: {
        subtitle: "Retorne para a nova aba que foi aberta para continuar",
        subtitleNewTab: "Retorne para a aba anterior para continuar",
        title: "E-mail verificado com sucesso"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: 'Eu concordo com a {{privacyPolicyLink || link("Pol\xEDtica de Privacidade")}}',
        label__onlyTermsOfService: 'Eu concordo com os {{termsOfServiceLink || link("Termos de Uso")}}',
        label__termsOfServiceAndPrivacyPolicy: 'Eu concordo com os {{termsOfServiceLink || link("Termos de Uso")}} e com a {{ privacyPolicyLink || link("Pol\xEDtica de Privacidade") }}'
      },
      continue: {
        subtitle: "Por favor leia e aceite os termos para continuar",
        title: "Continuar"
      }
    },
    phoneCode: {
      formSubtitle: "Insira o c\xF3digo enviado para seu telefone",
      formTitle: "C\xF3digo de verifica\xE7\xE3o",
      resendButton: "N\xE3o recebeu o c\xF3digo? Reenviar",
      subtitle: "para continuar em {{applicationName}}",
      title: "Verifique seu telefone"
    },
    restrictedAccess: {
      actionLink: "Entrar",
      actionText: "J\xE1 possui uma conta?",
      blockButton__emailSupport: "Suporte por e-mail",
      blockButton__joinWaitlist: "Entre na lista de espera",
      subtitle: "Cadastros est\xE3o desabilitados no momento. Se voc\xEA deveria ter acesso, por favor entre em contato com o suporte.",
      subtitleWaitlist: "Cadastros est\xE3o desabilitados no momento. Para ser um dos primeiros a saber quando lan\xE7aremos, entre na lista de espera.",
      title: "Acesso restrito"
    },
    start: {
      actionLink: "Entrar",
      actionLink__use_email: "Ou use e-mail",
      actionLink__use_phone: "Ou use telefone",
      actionText: "Possui uma conta?",
      alternativePhoneCodeProvider: {
        actionLink: "Usar outro m\xE9todo",
        label: "{{provider}} telefone",
        subtitle: "Insira seu n\xFAmero de telefone para receber um c\xF3digo de verifica\xE7\xE3o em {{provider}}.",
        title: "Entrar no {{applicationName}} com {{provider}}"
      },
      subtitle: "para continuar em {{applicationName}}",
      subtitleCombined: "para continuar em {{applicationName}}",
      title: "Criar sua conta",
      titleCombined: "Criar sua conta"
    }
  },
  socialButtonsBlockButton: "Continuar com {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: "{{email}} j\xE1 \xE9 membro da organiza\xE7\xE3o.",
    captcha_invalid: "N\xE3o foi poss\xEDvel se inscrever devido a falhas nas valida\xE7\xF5es de seguran\xE7a. Por favor, atualize a p\xE1gina para tentar novamente ou entre em contato com o suporte para obter mais ajuda.",
    captcha_unavailable: "N\xE3o foi poss\xEDvel se inscrever devido \xE0 indisponibilidade do captcha. Por favor atualize a p\xE1gina para tentar novamente ou entre em contato com o suporte para obter mais ajuda.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: "E-mail j\xE1 est\xE1 em uso. Por favor, tente outro.",
    form_identifier_exists__phone_number: "Telefone j\xE1 est\xE1 em uso. Por favor, tente outro.",
    form_identifier_exists__username: "Nome de usu\xE1rio j\xE1 est\xE1 em uso. Por favor, tente outro.",
    form_identifier_not_found: "N\xE3o foi poss\xEDvel encontrar o usu\xE1rio.",
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: "O endere\xE7o de e-mail deve ser um endere\xE7o de e-mail v\xE1lido.",
    form_param_format_invalid__phone_number: "N\xFAmero de telefone precisa estar num formato internacional v\xE1lido.",
    form_param_max_length_exceeded__first_name: "O primeiro nome n\xE3o deve exceder 256 caracteres.",
    form_param_max_length_exceeded__last_name: "O sobrenome n\xE3o deve exceder 256 caracteres.",
    form_param_max_length_exceeded__name: "O nome n\xE3o deve exceder 256 caracteres.",
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: "Senha incorreta.",
    form_password_length_too_short: "Sua senha \xE9 muito curta. Por favor, tente novamente.",
    form_password_not_strong_enough: "Sua senha n\xE3o \xE9 forte o suficiente.",
    form_password_pwned: "Esta senha foi comprometida e n\xE3o pode ser usada, por favor, tente outra senha.",
    form_password_pwned__sign_in: "Esta senha foi comprometida, por favor redefina sua senha.",
    form_password_size_in_bytes_exceeded: "Sua senha excedeu o n\xFAmero m\xE1ximo de bytes permitidos, por favor, encurte-a ou remova alguns caracteres especiais.",
    form_password_validation_failed: "Senha incorreta",
    form_username_invalid_character: "Nome de usu\xE1rio cont\xE9m caracteres inv\xE1lidos. Por favor, tente outro.",
    form_username_invalid_length: "Nome de usu\xE1rio deve ter entre 3 e 256 caracteres.",
    identification_deletion_failed: "Voc\xEA n\xE3o pode excluir sua \xFAltima identifica\xE7\xE3o.",
    not_allowed_access: "O endere\xE7o de e-mail ou n\xFAmero de telefone n\xE3o \xE9 permitido para registro. Isso pode ser devido ao uso de '+', '=', '#' ou '.' no endere\xE7o de e-mail, o uso de um dom\xEDnio associado a um servi\xE7o de e-mail tempor\xE1rio ou uma exclus\xE3o expl\xEDcita.",
    organization_domain_blocked: "Este \xE9 um provedor de dom\xEDnio de e-mail bloqueado. Por favor, use um diferente.",
    organization_domain_common: "Este \xE9 um provedor de dom\xEDnio de e-mail comum. Por favor, use um diferente.",
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: "Voc\xEA chegou ao seu limite de membros da organiza\xE7\xE3o, incluindo convites pendentes.",
    organization_minimum_permissions_needed: "\xC9 necess\xE1rio que haja pelo menos um membro da organiza\xE7\xE3o com as permiss\xF5es m\xEDnimas necess\xE1rias.",
    passkey_already_exists: "Uma chave de acesso j\xE1 est\xE1 registrada neste dispositivo.",
    passkey_not_supported: "Chaves de acesso n\xE3o s\xE3o suportadas neste dispositivo.",
    passkey_pa_not_supported: "Registro precisa de chave de acesso mas dispositivo n\xE3o a suporta.",
    passkey_registration_cancelled: "Registro de chave de acesso cancelado ou expirado.",
    passkey_retrieval_cancelled: "Verifica\xE7\xE3o de chave de acesso cancelada ou expirada.",
    passwordComplexity: {
      maximumLength: "menos de {{length}} caracteres",
      minimumLength: "{{length}} ou mais caracteres",
      requireLowercase: "uma letra min\xFAscula",
      requireNumbers: "um n\xFAmero",
      requireSpecialCharacter: "um caractere especial",
      requireUppercase: "uma letra mai\xFAscula",
      sentencePrefix: "Sua senha deve conter"
    },
    phone_number_exists: "Este n\xFAmero de telefone j\xE1 est\xE1 em uso. Por favor, tente outro.",
    session_exists: "Voc\xEA j\xE1 est\xE1 conectado.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "Sua senha funciona, mas poderia ser mais forte. Tente adicionar mais caracteres.",
      goodPassword: "Sua senha atende a todos os requisitos necess\xE1rios.",
      notEnough: "Sua senha n\xE3o \xE9 forte o suficiente.",
      suggestions: {
        allUppercase: "Utilize apenas algumas letras mai\xFAsculas, n\xE3o todas.",
        anotherWord: "Adicione mais palavras que s\xE3o menos comuns.",
        associatedYears: "Evite anos associados a voc\xEA.",
        capitalization: "Utilize outras letras mai\xFAsculas, al\xE9m do que primeira.",
        dates: "Evite datas e anos associados a voc\xEA.",
        l33t: "Evite substitui\xE7\xF5es previs\xEDveis de letras, como '@' por 'a'.",
        longerKeyboardPattern: "Use padr\xF5es de teclado mais longos e mude a dire\xE7\xE3o da digita\xE7\xE3o v\xE1rias vezes.",
        noNeed: "Voc\xEA pode criar senhas fortes sem usar s\xEDmbolos, n\xFAmeros ou letras mai\xFAsculas.",
        pwned: "Se voc\xEA usar esta senha em outro lugar, voc\xEA deve mud\xE1-la.",
        recentYears: "Evite anos recentes.",
        repeated: "Evite palavras e caracteres repetidos.",
        reverseWords: 'Evite utilizar palavras comuns escritas de "tr\xE1s para frente".',
        sequences: "Evite sequ\xEAncias comuns de caracteres.",
        useWords: "Use v\xE1rias palavras, mas evite frases comuns."
      },
      warnings: {
        common: "Esta \xE9 uma senha comumente usada.",
        commonNames: "Nomes e sobrenomes comuns s\xE3o f\xE1ceis de adivinhar.",
        dates: "Datas s\xE3o f\xE1ceis de adivinhar.",
        extendedRepeat: 'Padr\xF5es de caracteres repetidos, como "abcabcabc" s\xE3o f\xE1ceis de adivinhar.',
        keyPattern: "Padr\xF5es curtos de teclado s\xE3o f\xE1ceis de adivinhar.",
        namesByThemselves: "Nomes ou sobrenomes s\xE3o f\xE1ceis de adivinhar.",
        pwned: "Sua senha foi exposta por uma viola\xE7\xE3o de dados na Internet.",
        recentYears: "Anos recentes s\xE3o f\xE1ceis de adivinhar.",
        sequences: 'Sequ\xEAncias comuns de caracteres, como "abc" s\xE3o f\xE1ceis de adivinhar.',
        similarToCommon: "Esta \xE9 semelhante a uma senha comumente usada.",
        simpleRepeat: 'Caracteres repetidos, como "aaa" s\xE3o f\xE1ceis de adivinhar.',
        straightRow: "Letras que v\xEAm em sequ\xEAncia teclado s\xE3o f\xE1ceis de adivinhar.",
        topHundred: "Esta \xE9 uma senha usada frequentemente.",
        topTen: "Esta \xE9 uma senha muito usada.",
        userInputs: "N\xE3o deve haver nenhum dado pessoal ou relacionado \xE0 p\xE1gina.",
        wordByItself: "Palavras simples s\xE3o f\xE1ceis de adivinhar."
      }
    }
  },
  userButton: {
    action__addAccount: "Adicionar conta",
    action__manageAccount: "Gerenciar conta",
    action__signOut: "Sair",
    action__signOutAll: "Sair de todas as contas"
  },
  userProfile: {
    apiKeysPage: {
      title: void 0
    },
    backupCodePage: {
      actionLabel__copied: "Copiado!",
      actionLabel__copy: "Copiar tudo",
      actionLabel__download: "Download .txt",
      actionLabel__print: "Imprimir",
      infoText1: "C\xF3digos de backup ser\xE3o ativados para esta conta.",
      infoText2: "Guarde-os em seguran\xE7a e mantenha-os em sigilo. Voc\xEA pode gerar novos c\xF3digos de backup se suspeitar que eles tenham sido comprometidos.",
      subtitle__codelist: "Guarde-os em seguran\xE7a e mantenha-os em sigilo.",
      successMessage: "C\xF3digos de backup foram ativados para esta conta. Voc\xEA pode usar um deles para fazer login na sua conta caso perca o acesso ao seu dispositivo de autentica\xE7\xE3o. Cada c\xF3digo poder\xE1 ser utilizado apenas uma vez.",
      successSubtitle: "Voc\xEA pode usar um deles para fazer login na sua conta caso perca o acesso ao seu dispositivo de autentica\xE7\xE3o.",
      title: "Adicionar c\xF3digo de backup para verifica\xE7\xE3o",
      title__codelist: "C\xF3digos de backup"
    },
    billingPage: {
      paymentHistorySection: {
        empty: "Nenhum hist\xF3rico de pagamento",
        notFound: "Pagamento n\xE3o encontrado",
        tableHeader__amount: "Valor",
        tableHeader__date: "Data",
        tableHeader__status: "Status"
      },
      paymentSourcesSection: {
        actionLabel__default: "Tornar padr\xE3o",
        actionLabel__remove: "Remover",
        add: "Adicionar novo m\xE9todo de pagamento",
        addSubtitle: "Adicione um novo m\xE9todo de pagamento \xE0 sua conta.",
        cancelButton: "Cancelar",
        formButtonPrimary__add: "Adicionar M\xE9todo de Pagamento",
        formButtonPrimary__pay: "Pagar {{amount}}",
        payWithTestCardButton: "Pagar com cart\xE3o de teste",
        removeResource: {
          messageLine1: "{{identifier}} ser\xE1 removido desta conta.",
          messageLine2: "Voc\xEA n\xE3o poder\xE1 mais usar esta forma de pagamento e quaisquer assinaturas recorrentes dependentes dela deixar\xE3o de funcionar.",
          successMessage: "{{paymentSource}} foi removido da sua conta.",
          title: "Remover m\xE9todo de pagamento"
        },
        title: "M\xE9todos de pagamento"
      },
      start: {
        headerTitle__payments: "Pagamentos",
        headerTitle__plans: "Planos",
        headerTitle__statements: "Extratos",
        headerTitle__subscriptions: "Assinaturas"
      },
      statementsSection: {
        empty: "Nenhum extrato para exibir",
        itemCaption__paidForPlan: "Pago para plano {{plan}} {{period}}",
        itemCaption__proratedCredit: "Cr\xE9dito proporcional para uso parcial do plano anterior",
        itemCaption__subscribedAndPaidForPlan: "Assinado e pago para plano {{plan}} {{period}}",
        notFound: "Extrato n\xE3o encontrado",
        tableHeader__amount: "Valor",
        tableHeader__date: "Data",
        title: "Extratos",
        totalPaid: "Total pago"
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: "Assinar um plano",
        actionLabel__switchPlan: "Mudar de plano",
        tableHeader__edit: "Editar",
        tableHeader__plan: "Plano",
        tableHeader__startDate: "Data de in\xEDcio",
        title: "Assinatura"
      },
      subscriptionsSection: {
        actionLabel__default: "Gerenciar"
      },
      switchPlansSection: {
        title: "Mudar de plano"
      },
      title: "Faturamento"
    },
    connectedAccountPage: {
      formHint: "Selecione um provedor para conectar \xE0 sua conta.",
      formHint__noAccounts: "N\xE3o h\xE1 provedores de conta externos dispon\xEDveis.",
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 removido desta conta.",
        messageLine2: "Voc\xEA n\xE3o conseguir\xE1 mais usar esta conta e quaisquer recursos dependentes dela deixar\xE3o de funcionar.",
        successMessage: "{{connectedAccount}} foi removido da sua conta.",
        title: "Remover conta conectada"
      },
      socialButtonsBlockButton: "Conectar conta {{provider|titleize}}",
      successMessage: "O provedor foi adicionado \xE0 sua conta",
      title: "Conecte uma conta"
    },
    deletePage: {
      actionDescription: "Digite Excluir conta abaixo para continuar.",
      confirm: "Excluir conta",
      messageLine1: "Tem certeza de que deseja excluir sua conta?",
      messageLine2: "Esta a\xE7\xE3o \xE9 permanente e irrevers\xEDvel.",
      title: "Excluir conta"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "Um e-mail contendo um c\xF3digo de verifica\xE7\xE3o ser\xE1 enviado para este endere\xE7o de e-mail.",
        formSubtitle: "Insira o c\xF3digo de verifica\xE7\xE3o enviado para {{identifier}}",
        formTitle: "C\xF3digo de verifica\xE7\xE3o",
        resendButton: "N\xE3o recebeu um c\xF3digo? Reenviar",
        successMessage: "O e-mail {{identifier}} foi adicionado na sua conta."
      },
      emailLink: {
        formHint: "Um e-mail contendo um link de verifica\xE7\xE3o ser\xE1 enviado para este endere\xE7o de e-mail.",
        formSubtitle: "Clique no link de verifica\xE7\xE3o enviado para {{identifier}}",
        formTitle: "Link de verifica\xE7\xE3o",
        resendButton: "N\xE3o recebeu um c\xF3digo? Reenviar",
        successMessage: "O e-mail {{identifier}} foi adicionado na sua conta."
      },
      enterpriseSSOLink: {
        formButton: "Clique para autenticar",
        formSubtitle: "Complete a autentica\xE7\xE3o com {{identifier}}"
      },
      formHint: "Voc\xEA precisar\xE1 verificar este endere\xE7o de email antes de poder adicion\xE1-lo \xE0 sua conta.",
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 removido desta conta.",
        messageLine2: "Voc\xEA n\xE3o conseguir\xE1 fazer login novamente com este endere\xE7o de e-mail.",
        successMessage: "{{emailAddress}} foi removido da sua conta.",
        title: "Remover e-mail"
      },
      title: "Adicionar e-mail",
      verifyTitle: "Verificar endere\xE7o de e-mail"
    },
    formButtonPrimary__add: "Add",
    formButtonPrimary__continue: "Continuar",
    formButtonPrimary__finish: "Finalizar",
    formButtonPrimary__remove: "Excluir",
    formButtonPrimary__save: "Salvar",
    formButtonReset: "Cancelar",
    mfaPage: {
      formHint: "Selecione um m\xE9todo para adicionar.",
      title: "Adicione verifica\xE7\xE3o em duas etapas"
    },
    mfaPhoneCodePage: {
      backButton: "Usar n\xFAmero existente",
      primaryButton__addPhoneNumber: "Adicione um n\xFAmero de telefone",
      removeResource: {
        messageLine1: "{{identifier}} n\xE3o receber\xE1 mais c\xF3digos de verifica\xE7\xE3o ao realizar o login.",
        messageLine2: "Sua conta pode ficar menos segura. Tem certeza que deseja continuar?",
        successMessage: "C\xF3digo SMS de verifica\xE7\xE3o em duas etapas foi removido para {{mfaPhoneCode}}",
        title: "Remover verifica\xE7\xE3o em duas etapas"
      },
      subtitle__availablePhoneNumbers: "Selecione um n\xFAmero de telefone para registrar a verifica\xE7\xE3o em duas etapas por c\xF3digo SMS.",
      subtitle__unavailablePhoneNumbers: "N\xE3o h\xE1 n\xFAmeros de telefone dispon\xEDveis para registrar a verifica\xE7\xE3o em duas etapas por c\xF3digo SMS.",
      successMessage1: "Ao acessar, ser\xE1 necess\xE1rio o passo adicional de digitar o c\xF3digo de verifica\xE7\xE3o enviado a este telefone.",
      successMessage2: "Salve estes c\xF3digos de backup e os armazene em um lugar seguro. Se voc\xEA perder acesso ao seu dispositivo de autentica\xE7\xE3o, voc\xEA pode utiliz\xE1-los para acessar o sistema.",
      successTitle: "Verifica\xE7\xE3o por SMS habilitada",
      title: "Adicionar verifica\xE7\xE3o por SMS"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Escanear c\xF3digo QR em vez disso",
        buttonUnableToScan__nonPrimary: "N\xE3o pode escanear o c\xF3digo QR?",
        infoText__ableToScan: "Configure um novo m\xE9todo de login no seu aplicativo autenticador e escaneie o seguinte c\xF3digo QR para vincul\xE1-lo \xE0 sua conta.",
        infoText__unableToScan: "Configure um novo m\xE9todo de login no seu aplicativo autenticador e insira a chave informada abaixo.",
        inputLabel__unableToScan1: "Certifique-se de que o 'One-time passwords' est\xE1 habilitado, em seguida, conclua a vincula\xE7\xE3o de sua conta.",
        inputLabel__unableToScan2: "Alternativamente, se seu autenticador suportar URIs TOTP, voc\xEA tamb\xE9m pode copiar a URI completa."
      },
      removeResource: {
        messageLine1: "Os c\xF3digos de verifica\xE7\xE3o deste aplicativo autenticador n\xE3o ser\xE3o mais necess\xE1rios ao fazer login.",
        messageLine2: "Sua conta pode ficar menos segura. Tem certeza que deseja continuar?",
        successMessage: "A verifica\xE7\xE3o em duas etapas via aplicativo autenticador foi removida.",
        title: "Remover verifica\xE7\xE3o em duas etapas"
      },
      successMessage: "A verifica\xE7\xE3o em duas etapas est\xE1 ativa agora. Ao fazer login, voc\xEA precisar\xE1 inserir um c\xF3digo de verifica\xE7\xE3o deste aplicativo autenticador como uma etapa adicional.",
      title: "Adicionar um aplicativo autenticador",
      verifySubtitle: "Insira o c\xF3digo de verifica\xE7\xE3o gerado pelo seu aplicativo autenticador",
      verifyTitle: "C\xF3digo de verifica\xE7\xE3o"
    },
    mobileButton__menu: "Menu",
    navbar: {
      account: "Perfil",
      apiKeys: void 0,
      billing: "Faturamento",
      description: "Gerencie seus dados de perfil.",
      security: "Seguran\xE7a",
      title: "Conta"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} ser\xE1 removido desta conta.",
        title: "Remover chave de acesso"
      },
      subtitle__rename: "Voc\xEA pode renomear a chave de acesso para que seja mais f\xE1cil encontr\xE1-la.",
      title__rename: "Renomear chave de acesso"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "\xC9 recomendado sair de todos os demais dispositivos que podem ter utilizado sua senha antiga.",
      readonly: "Sua senha atualmente n\xE3o pode ser editada porque voc\xEA s\xF3 pode fazer login por meio da conex\xE3o da empresa.",
      successMessage__set: "Sua senha foi salva.",
      successMessage__signOutOfOtherSessions: "Todos os outros dispositivos foram desconectados.",
      successMessage__update: "Sua senha foi atualizada.",
      title__set: "Defina a senha",
      title__update: "Trocar senha"
    },
    phoneNumberPage: {
      infoText: "Um SMS contendo um link de verifica\xE7\xE3o ser\xE1 enviado para este telefone.",
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 removido desta conta.",
        messageLine2: "Voc\xEA n\xE3o conseguir\xE1 fazer login novamente utilizando este n\xFAmero de telefone.",
        successMessage: "{{phoneNumber}} foi removido da sua conta.",
        title: "Remover telefone"
      },
      successMessage: "{{identifier}} foi adicionado na sua conta.",
      title: "Adicionar telefone",
      verifySubtitle: "Insira o c\xF3digo de verifica\xE7\xE3o enviado para {{identifier}}",
      verifyTitle: "Verificar n\xFAmero de telefone"
    },
    plansPage: {
      title: "Planos"
    },
    profilePage: {
      fileDropAreaHint: "Carregue uma imagem JPG, PNG, GIF ou WEBP menor que 10 MB",
      imageFormDestructiveActionSubtitle: "Remover imagem",
      imageFormSubtitle: "Carregar imagem",
      imageFormTitle: "Imagem do perfil",
      readonly: "As informa\xE7\xF5es do seu perfil foram fornecidas pela conex\xE3o corporativa e n\xE3o podem ser editadas.",
      successMessage: "Seu perfil foi atualizado.",
      title: "Atualizar perfil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Sair do dispositivo",
        title: "Dispositivos ativos"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Tentar novamente",
        actionLabel__reauthorize: "Reautorizar agora",
        destructiveActionTitle: "Remover",
        primaryButton: "Conectar conta",
        subtitle__disconnected: "Esta conta foi desconectada",
        subtitle__reauthorize: "Os escopos necess\xE1rios foram atualizados, e voc\xEA pode estar experimentado funcionalidades limitadas. Por favor, reautorize esta aplica\xE7\xE3o para evitar outros problemas",
        title: "Contas conectadas"
      },
      dangerSection: {
        deleteAccountButton: "Excluir Conta",
        title: "Perigo"
      },
      emailAddressesSection: {
        destructiveAction: "Remover e-mail",
        detailsAction__nonPrimary: "Definir como principal",
        detailsAction__primary: "Completar verifica\xE7\xE3o",
        detailsAction__unverified: "Completar verifica\xE7\xE3o",
        primaryButton: "Adicionar um e-mail",
        title: "Endere\xE7os de e-mail"
      },
      enterpriseAccountsSection: {
        title: "Contas corporativas"
      },
      headerTitle__account: "Conta",
      headerTitle__security: "Seguran\xE7a",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Gerar c\xF3digos novamente",
          headerTitle: "C\xF3digos de backup",
          subtitle__regenerate: "Obtenha um novo conjunto de c\xF3digos de backup seguros. Os c\xF3digos de backup anteriores ser\xE3o exclu\xEDdos e n\xE3o poder\xE3o ser usados.",
          title__regenerate: "Gerar c\xF3digos de backup novamente"
        },
        phoneCode: {
          actionLabel__setDefault: "Definir como principal",
          destructiveActionLabel: "Remover telefone"
        },
        primaryButton: "Adicione verifica\xE7\xE3o",
        title: "Verifica\xE7\xE3o em duas etapas",
        totp: {
          destructiveActionTitle: "Remover",
          headerTitle: "Aplicativo autenticador"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Remover",
        menuAction__rename: "Renomear",
        primaryButton: void 0,
        title: "Chaves de acesso"
      },
      passwordSection: {
        primaryButton__setPassword: "Defina a senha",
        primaryButton__updatePassword: "Trocar a senha",
        title: "Senha"
      },
      phoneNumbersSection: {
        destructiveAction: "Remover telefone",
        detailsAction__nonPrimary: "Definir como principal",
        detailsAction__primary: "Completar verifica\xE7\xE3o",
        detailsAction__unverified: "Completar verifica\xE7\xE3o",
        primaryButton: "Adicione um telefone",
        title: "N\xFAmeros de telefone"
      },
      profileSection: {
        primaryButton: "Atualizar perfil",
        title: "Perfil"
      },
      usernameSection: {
        primaryButton__setUsername: "Definir nome de usu\xE1rio",
        primaryButton__updateUsername: "Trocar nome de usu\xE1rio",
        title: "Nome de usu\xE1rio"
      },
      web3WalletsSection: {
        destructiveAction: "Remover carteira",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Carteiras Web3",
        title: "Carteiras Web3"
      }
    },
    usernamePage: {
      successMessage: "Seu nome de usu\xE1rio foi atualizado.",
      title__set: "Atualizar nome de usu\xE1rio",
      title__update: "Atualizar nome de usu\xE1rio"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 removido desta conta.",
        messageLine2: "Voc\xEA n\xE3o poder\xE1 mais usar esta carteira Web3 para entrar na sua conta.",
        successMessage: "{{Web3Wallet}} foi removido da sua conta.",
        title: "Remover carteira Web3"
      },
      subtitle__availableWallets: "Selecione uma carteira Web3 para conectar \xE0 sua conta.",
      subtitle__unavailableWallets: "N\xE3o h\xE1 carteiras Web3 dispon\xEDveis.",
      successMessage: "A carteira foi adicionada \xE0 sua conta.",
      title: "Adicionar carteira Web3",
      web3WalletButtonsBlockButton: "Conectar carteira Web3"
    }
  },
  waitlist: {
    start: {
      actionLink: "Entrar",
      actionText: "J\xE1 possui acesso?",
      formButton: "Entrar na lista de espera",
      subtitle: "Entre com seu e-mail e entraremos em contato quando seu lugar estiver dispon\xEDvel",
      title: "Entre na lista de espera"
    },
    success: {
      message: "Te redirecionando em breve...",
      subtitle: "Entraremos em contato quando seu lugar estiver dispon\xEDvel",
      title: "Obrigado por entrar na lista de espera!"
    }
  }
};
export {
  ptBR
};
//# sourceMappingURL=pt-BR.mjs.map