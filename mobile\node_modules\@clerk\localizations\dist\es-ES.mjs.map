{"version": 3, "sources": ["../src/es-ES.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const esES: LocalizationResource = {\n  locale: 'es-ES',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Atrás',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Por defecto',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Otro dispositivo de imitación',\n  badge__primary: 'Primario',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Requiere acción',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Este dispositivo',\n  badge__unverified: 'No confirmado',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Dispositivo de usuario',\n  badge__you: 'Usted',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Crear organización',\n    invitePage: {\n      formButtonReset: 'Saltar',\n    },\n    title: 'Crear organización',\n  },\n  dates: {\n    lastDay: \"Ayer a las {{ date | timeString('es-ES') }}\",\n    next6Days: \"{{ date | weekday('es-ES','long') }} a las {{ date | timeString('es-ES') }}\",\n    nextDay: \"Mañana a las {{ date | timeString('es-ES') }}\",\n    numeric: \"{{ date | numeric('es-ES') }}\",\n    previous6Days: \"Último {{ date | weekday('es-ES','long') }} en {{ date | timeString('es-ES') }}\",\n    sameDay: \"Hoy a las {{ date | timeString('es-ES') }}\",\n  },\n  dividerText: 'o',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Usar otro método',\n  footerPageLink__help: 'Ayuda',\n  footerPageLink__privacy: 'Privacidad',\n  footerPageLink__terms: 'Términos',\n  formButtonPrimary: 'Continuar',\n  formButtonPrimary__verify: 'Verificar',\n  formFieldAction__forgotPassword: 'Has olvidado tu contraseña?',\n  formFieldError__matchingPasswords: 'Las contraseñas coinciden.',\n  formFieldError__notMatchingPasswords: 'Las contraseñas no coinciden.',\n  formFieldError__verificationLinkExpired: 'El enlace de verificación ha expirado. Por favor solicite uno nuevo.',\n  formFieldHintText__optional: 'Opcional',\n  formFieldHintText__slug: 'Un slug es un ID legible que debe ser único. Es comúnmente usado en URLs.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: 'Ingrese su código de respaldo',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Eliminar cuenta',\n  formFieldInputPlaceholder__emailAddress: 'Ingrese su dirección de correo electrónico',\n  formFieldInputPlaceholder__emailAddress_username: 'Ingrese su correo electrónico o nombre de usuario',\n  formFieldInputPlaceholder__emailAddresses:\n    'Ingrese o pegue una o más direcciones de correo electrónico, separadas por espacios o comas',\n  formFieldInputPlaceholder__firstName: 'Ingrese su nombre',\n  formFieldInputPlaceholder__lastName: 'Ingrese su apellido',\n  formFieldInputPlaceholder__organizationDomain: 'Ingrese el dominio de la organización',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: 'Ingrese un correo electrónico del dominio',\n  formFieldInputPlaceholder__organizationName: 'Ingrese el nombre de la organización',\n  formFieldInputPlaceholder__organizationSlug: 'Ingrese un slug único para la organización',\n  formFieldInputPlaceholder__password: 'Ingrese su contraseña',\n  formFieldInputPlaceholder__phoneNumber: 'Ingrese su número telefónico',\n  formFieldInputPlaceholder__username: 'Ingrese su nombre de usuario',\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Activar invitaciones automáticas para este dominio',\n  formFieldLabel__backupCode: 'Código de respaldo',\n  formFieldLabel__confirmDeletion: 'Confirmación',\n  formFieldLabel__confirmPassword: 'Confirme la contraseña',\n  formFieldLabel__currentPassword: 'Contraseña actual',\n  formFieldLabel__emailAddress: 'Correo electrónico',\n  formFieldLabel__emailAddress_username: 'Correo electrónico o nombre de usuario',\n  formFieldLabel__emailAddresses: 'Direcciones de correo',\n  formFieldLabel__firstName: 'Nombre',\n  formFieldLabel__lastName: 'Apellido',\n  formFieldLabel__newPassword: 'Nueva contraseña',\n  formFieldLabel__organizationDomain: 'Dominio',\n  formFieldLabel__organizationDomainDeletePending: 'Borrar invitaciones y sugerencias pendientes',\n  formFieldLabel__organizationDomainEmailAddress: 'Correo de verificación',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Ingrese una dirección de correo electrónico bajo este dominio para recibir un código y verificarlo.',\n  formFieldLabel__organizationName: 'Nombre de la Organización',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Nombre de la clave de acceso',\n  formFieldLabel__password: 'Contraseña',\n  formFieldLabel__phoneNumber: 'Número telefónico',\n  formFieldLabel__role: 'Rol',\n  formFieldLabel__signOutOfOtherSessions: 'Cerrar sesión en todos los demás dispositivos',\n  formFieldLabel__username: 'Nombre de usuario',\n  impersonationFab: {\n    action__signOut: 'Cerrar',\n    title: 'Registrado como {{identifier}}',\n  },\n  maintenanceMode: 'Modo de mantenimiento',\n  membershipRole__admin: 'Administrador',\n  membershipRole__basicMember: 'Miembro',\n  membershipRole__guestMember: 'Invitado',\n  organizationList: {\n    action__createOrganization: 'Crear organización',\n    action__invitationAccept: 'Unirse',\n    action__suggestionsAccept: 'Solicitud a unirse',\n    createOrganization: 'Crear Organización',\n    invitationAcceptedLabel: 'Unido',\n    subtitle: 'para continuar a {{applicationName}}',\n    suggestionsAcceptedLabel: 'Aprobación pendiente',\n    title: 'Choose an account',\n    titleWithoutPersonal: 'Escoja una organización',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Invitaciones automáticas',\n    badge__automaticSuggestion: 'Sugerencias automáticas',\n    badge__manualInvitation: 'Sin inscripción automática',\n    badge__unverified: 'No verificado',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Agregue el dominio para verificar. Los usuarios con direcciones de correo electrónico en este dominio pueden unirse a la organización automáticamente o solicitar unirse.',\n      title: 'Agregar dominio',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'No se pudieron enviar las invitaciones. Solucione lo siguiente y vuelva a intentarlo:',\n      formButtonPrimary__continue: 'Enviar invitaciones',\n      selectDropdown__role: 'Seleccionar rol',\n      subtitle: 'Invitar nuevos miembros a esta organización',\n      successMessage: 'Invitaciones enviadas con éxito',\n      title: 'Invitar miembros',\n    },\n    membersPage: {\n      action__invite: 'Invitar',\n      action__search: 'Buscar',\n      activeMembersTab: {\n        menuAction__remove: 'Quitar miembro',\n        tableHeader__actions: 'Acciones',\n        tableHeader__joined: 'Unido',\n        tableHeader__role: 'Rol',\n        tableHeader__user: 'Usuario',\n      },\n      detailsTitle__emptyRow: 'No hay miembros para mostrar',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Invite a usuarios conectando un dominio de correo electrónico con su organización. Cualquiera que se registre con un dominio de correo electrónico coincidente podrá unirse a la organización en cualquier momento.',\n          headerTitle: 'Invitaciones automáticas',\n          primaryButton: 'Gestionar dominios verificados',\n        },\n        table__emptyRow: 'Sin invitaciones para mostrar',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Revocar invitación',\n        tableHeader__invited: 'Invitado',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Los usuarios que se registren con un dominio de correo electrónico coincidente podrán ver una sugerencia para solicitar unirse a su organización.',\n          headerTitle: 'Sugerencias automáticas',\n          primaryButton: 'Gestionar dominios verificados',\n        },\n        menuAction__approve: 'Aprobar',\n        menuAction__reject: 'Rechazar',\n        tableHeader__requested: 'Acceso solicitado',\n        table__emptyRow: 'Sin solicitudes para mostrar',\n      },\n      start: {\n        headerTitle__invitations: 'Invitaciones',\n        headerTitle__members: 'Miembros',\n        headerTitle__requests: 'Solicitudes',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Gestione su organización.',\n      general: 'General',\n      members: 'Miembros',\n      title: 'Organización',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Escriba \"{{organizationName}}\" a continuación para continuar.',\n          messageLine1: '¿Está seguro de que desea eliminar esta organización?',\n          messageLine2: 'Esta acción es permanente e irreversible.',\n          successMessage: 'Ha eliminado la organización.',\n          title: 'Borrar organización',\n        },\n        leaveOrganization: {\n          actionDescription: 'Escriba \"{{organizationName}}\" a continuación para continuar.',\n          messageLine1:\n            '¿Está seguro de que desea abandonar esta organización? Perderá el acceso a esta organización y sus aplicaciones.',\n          messageLine2: 'Esta acción es permanente e irreversible.',\n          successMessage: 'Has dejado la organización.',\n          title: 'Abandonar la organización',\n        },\n        title: 'Peligro',\n      },\n      domainSection: {\n        menuAction__manage: 'Gestione',\n        menuAction__remove: 'Eliminar',\n        menuAction__verify: 'Verificar',\n        primaryButton: 'Añadir dominio',\n        subtitle:\n          'Permita que los usuarios se unan a la organización automáticamente o soliciten unirse basándose en un dominio de correo electrónico verificado.',\n        title: 'Dominios verificados',\n      },\n      successMessage: 'La organización ha sido actualizada.',\n      title: 'Perfil de la organización',\n    },\n    removeDomainPage: {\n      messageLine1: 'Se eliminará el dominio de correo electrónico {{domain}}.',\n      messageLine2: 'Los usuarios no podrán unirse a la organización automáticamente después de esto.',\n      successMessage: '{{dominio}} ha sido eliminado.',\n      title: 'Eliminar dominio',\n    },\n    start: {\n      headerTitle__general: 'General',\n      headerTitle__members: 'Miembros',\n      profileSection: {\n        primaryButton: 'Actualizar perfil',\n        title: 'Perfil de la Organización',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'La eliminación de este dominio afectará a los usuarios invitados.',\n        removeDomainActionLabel__remove: 'Eliminar dominio',\n        removeDomainSubtitle: 'Elimine este dominio de sus dominios verificados',\n        removeDomainTitle: 'Eliminar dominio',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Los usuarios son invitados automáticamente a unirse a la organización cuando se registran y pueden unirse en cualquier momento.',\n        automaticInvitationOption__label: 'Invitaciones automáticas',\n        automaticSuggestionOption__description:\n          'Los usuarios reciben una sugerencia para solicitar unirse, pero deben ser aprobados por un administrador antes de poder unirse a la organización.',\n        automaticSuggestionOption__label: 'Sugerencias automáticas',\n        calloutInfoLabel: 'Cambiar el modo de inscripción solo afectará a nuevos usuarios.',\n        calloutInvitationCountLabel: 'Invitaciones pendientes enviadas a los usuarios: {{count}}',\n        calloutSuggestionCountLabel: 'Sugerencias pendientes enviadas a los usuarios: {{count}}',\n        manualInvitationOption__description: 'Los usuarios solo pueden ser invitados manualmente a la organización.',\n        manualInvitationOption__label: 'Sin inscripción automática',\n        subtitle: 'Elija cómo los usuarios de este dominio pueden unirse a la organización.',\n      },\n      start: {\n        headerTitle__danger: 'Peligro',\n        headerTitle__enrollment: 'Opciones de inscripción',\n      },\n      subtitle: 'El dominio {{domain}} está ahora verificado. Continúe seleccionando el modo de inscripción.',\n      title: 'Actualizar {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Ingrese el código de verificación enviado a su dirección de correo electrónico',\n      formTitle: 'Código de verificación',\n      resendButton: '¿No recibió un código? Reenviar',\n      subtitle: 'El dominio {{domainName}} necesita ser verificado por correo electrónico.',\n      subtitleVerificationCodeScreen:\n        'Se envió un código de verificación a {{emailAddress}}. Ingrese el código para continuar.',\n      title: 'Verificar dominio',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Crear Organización',\n    action__invitationAccept: 'Unirse',\n    action__manageOrganization: 'Administrar Organización',\n    action__suggestionsAccept: 'Solicitar unirse',\n    notSelected: 'Ninguna organización seleccionada',\n    personalWorkspace: 'Espacio de trabajo personal',\n    suggestionsAcceptedLabel: 'Aprobación pendiente',\n  },\n  paginationButton__next: 'Próximo',\n  paginationButton__previous: 'Previo',\n  paginationRowText__displaying: 'Mostrando',\n  paginationRowText__of: 'de',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Probar otro método',\n      actionText: '¿No tienes acceso a este método? Prueba otra opción.',\n      blockButton__backupCode: 'Usar código de respaldo',\n      blockButton__emailCode: 'Usar código de correo electrónico',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Usar contraseña',\n      blockButton__phoneCode: 'Usar código de teléfono',\n      blockButton__totp: 'Usar verificación TOTP',\n      getHelp: {\n        blockButton__emailSupport: 'Contactar soporte por correo electrónico',\n        content:\n          'Si no puedes verificar tu identidad con los métodos anteriores, comunícate con nuestro equipo de soporte.',\n        title: 'Necesitas ayuda con la verificación?',\n      },\n      subtitle: 'Selecciona uno de los métodos disponibles para verificar tu identidad.',\n      title: 'Reverificación de identidad',\n    },\n    backupCodeMfa: {\n      subtitle: 'Introduce tu código de respaldo para continuar con el acceso.',\n      title: 'Verificación por código de respaldo',\n    },\n    emailCode: {\n      formTitle: 'Ingresa el código que hemos enviado a tu correo electrónico.',\n      resendButton: 'Reenviar código',\n      subtitle: 'Revisa tu bandeja de entrada para el código de verificación.',\n      title: 'Verificación por correo electrónico',\n    },\n    noAvailableMethods: {\n      message: 'Lo sentimos, no tienes ningún método de verificación disponible. Contacta con soporte.',\n      subtitle: 'No se encontraron métodos alternativos disponibles.',\n      title: 'Métodos de verificación no disponibles',\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: '¿Olvidaste tu contraseña? Recupérala aquí.',\n      subtitle: 'Usa tu contraseña para verificar tu identidad.',\n      title: 'Verificación por contraseña',\n    },\n    phoneCode: {\n      formTitle: 'Introduce el código enviado a tu teléfono.',\n      resendButton: 'Reenviar código',\n      subtitle: 'Recibirás un código SMS para verificar tu identidad.',\n      title: 'Verificación por teléfono',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Código de verificación de 2 pasos',\n      resendButton: 'Reenviar código',\n      subtitle: 'Introduce el código de verificación de dos factores enviado a tu teléfono.',\n      title: 'Verificación por teléfono (2FA)',\n    },\n    totpMfa: {\n      formTitle: 'Código TOTP',\n      subtitle: 'Introduce el código de autenticación TOTP para completar la verificación.',\n      title: 'Verificación por TOTP (2FA)',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Añadir cuenta',\n      action__signOutAll: 'Cerrar sesión de todas las cuentas',\n      subtitle: 'Seleccione la cuenta con la que desea continuar.',\n      title: 'Elija una cuenta',\n    },\n    alternativeMethods: {\n      actionLink: 'Consigue ayuda',\n      actionText: '¿No tienes ninguno de estos?',\n      blockButton__backupCode: 'Usa un código de respaldo',\n      blockButton__emailCode: 'Enviar código a {{identifier}}',\n      blockButton__emailLink: 'Enviar enlace a {{identifier}}',\n      blockButton__passkey: 'Usar llave de acceso',\n      blockButton__password: 'Entra con tu contraseña',\n      blockButton__phoneCode: 'Enviar código a {{identifier}}',\n      blockButton__totp: 'Usa tu aplicación de autenticación',\n      getHelp: {\n        blockButton__emailSupport: 'Soporte de correo electrónico',\n        content:\n          'Si tiene dificultades para iniciar sesión en su cuenta, envíenos un correo electrónico y trabajaremos con usted para restablecer el acceso lo antes posible.',\n        title: 'Consigue ayuda',\n      },\n      subtitle: '¿Tienes problemas? Puedes usar cualquiera de estos métodos para iniciar sesión.',\n      title: 'Usa otro método',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Introduce un código de seguridad',\n    },\n    emailCode: {\n      formTitle: 'Código de verificación',\n      resendButton: 'Reenviar código',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Revise su correo electrónico',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'El cliente no coincide con el solicitado. Por favor, intente de nuevo.',\n        title: 'Error de cliente no coincidente',\n      },\n      expired: {\n        subtitle: 'Regrese a la pestaña original para continuar.',\n        title: 'Este enlace de verificación ha expirado',\n      },\n      failed: {\n        subtitle: 'Regrese a la pestaña original para continuar.',\n        title: 'Este enlace de verificación es inválido',\n      },\n      formSubtitle: 'Utilice el enlace de verificación enviado a su correo electrónico',\n      formTitle: 'Enlace de verificación',\n      loading: {\n        subtitle: 'Serás redirigido pronto',\n        title: 'Iniciando sesión...',\n      },\n      resendButton: 'Reenviar enlace',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Revise su correo electrónico',\n      unusedTab: {\n        title: 'Puede cerrar esta pestaña',\n      },\n      verified: {\n        subtitle: 'Serás redirigido pronto',\n        title: 'Inició sesión con éxito',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Regrese a la pestaña original para continuar',\n        subtitleNewTab: 'Regrese a la pestaña recién abierta para continuar',\n        titleNewTab: 'Inició sesión en otra pestaña',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Código de restablecimiento de contraseña',\n      resendButton: '¿No recibiste un código? Reenviar',\n      subtitle: 'para restablecer tu contraseña',\n      subtitle_email: 'Primero, introduce el código enviado a tu correo electrónico',\n      subtitle_phone: 'Primero, introduce el código enviado a tu teléfono',\n      title: 'Restablecer contraseña',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Restablecer tu contraseña',\n      label__alternativeMethods: 'O, inicia sesión con otro método',\n      title: '¿Olvidaste tu contraseña?',\n    },\n    noAvailableMethods: {\n      message: 'No se puede continuar con el inicio de sesión. No hay ningún factor de autenticación disponible.',\n      subtitle: 'Ocurrió un error',\n      title: 'No puedo iniciar sesión',\n    },\n    passkey: {\n      subtitle: 'Use su clave de acceso para continuar con la autenticación.',\n      title: 'Clave de acceso',\n    },\n    password: {\n      actionLink: 'Usa otro método',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Introduzca su contraseña',\n    },\n    passwordPwned: {\n      title: 'Tu contraseña ha sido comprometida',\n    },\n    phoneCode: {\n      formTitle: 'Código de verificación',\n      resendButton: 'Reenviar código',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Revisa tu teléfono',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Código de verificación',\n      resendButton: 'Reenviar código',\n      subtitle: 'Introduce el código enviado a tu teléfono para continuar.',\n      title: 'Revisa tu teléfono',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Restablecer Contraseña',\n      requiredMessage: 'Por razones de seguridad, se requiere restablecer su contraseña.',\n      successMessage: 'Tu contraseña ha sido cambiada exitosamente. Iniciando sesión, por favor espera un momento.',\n      title: 'Establecer nueva contraseña',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Necesitamos verificar tu identidad antes de restablecer tu contraseña.',\n    },\n    start: {\n      actionLink: 'Regístrese',\n      actionLink__join_waitlist: 'Únase a la lista de espera',\n      actionLink__use_email: 'Usar correo electrónico',\n      actionLink__use_email_username: 'Usar correo electrónico o nombre de usuario',\n      actionLink__use_passkey: 'Usar una clave de acceso',\n      actionLink__use_phone: 'Usar teléfono',\n      actionLink__use_username: 'Usar nombre de usuario',\n      actionText: '¿No tienes cuenta?',\n      actionText__join_waitlist: '¿Te gustaría unirte a la lista de espera?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'para continuar a {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Entrar',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Código de verificación',\n      subtitle: 'Introduce el código que te enviamos a tu dispositivo',\n      title: 'Verificación de dos pasos',\n    },\n  },\n  signInEnterPasswordTitle: 'Ingresa tu contraseña',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Entrar',\n      actionText: '¿Tiene una cuenta?',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Rellene los campos que faltan',\n    },\n    emailCode: {\n      formSubtitle: 'Introduzca el código de verificación enviado a su correo electrónico',\n      formTitle: 'Código de verificación',\n      resendButton: 'Re-enviar código',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Verifique su correo electrónico',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Parece que no estás usando el dispositivo correcto para verificar tu cuenta.',\n        title: 'Error de dispositivo',\n      },\n      formSubtitle: 'Utilice el enlace de verificación enviado a su dirección de correo electrónico',\n      formTitle: 'Enlace de verificación',\n      loading: {\n        title: 'Registrando...',\n      },\n      resendButton: 'Reenviar enlace',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Verifica tu correo electrónico',\n      verified: {\n        title: 'Registrado con éxito',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Regrese a la pestaña recién abierta para continuar',\n        subtitleNewTab: 'Volver a la pestaña anterior para continuar',\n        title: 'Correo electrónico verificado con éxito',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'He leído y acepto la Política de Privacidad',\n        label__onlyTermsOfService: 'He leído y acepto los Términos de Servicio',\n        label__termsOfServiceAndPrivacyPolicy:\n          'He leído y acepto los {{ termsOfServiceLink || link(\"Términos de Servicio\") }} y la {{ privacyPolicyLink || link(\"Política de Privacidad\") }}',\n      },\n      continue: {\n        subtitle: 'Al continuar, aceptas las condiciones mencionadas.',\n        title: 'Por favor, acepta nuestros términos y políticas para continuar',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Introduzca el código de verificación enviado a su número de teléfono',\n      formTitle: 'Código de verificación',\n      resendButton: 'Re-enviar código',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Verifique su teléfono',\n    },\n    restrictedAccess: {\n      actionLink: 'Contáctanos para más información',\n      actionText: '¿Tienes problemas? Obtén ayuda',\n      blockButton__emailSupport: 'Soporte por correo electrónico',\n      blockButton__joinWaitlist: 'Unirte a la lista de espera',\n      subtitle: 'El acceso a esta funcionalidad está restringido en este momento.',\n      subtitleWaitlist: 'Te has unido a la lista de espera. Nos pondremos en contacto contigo pronto.',\n      title: 'Acceso restringido',\n    },\n    start: {\n      actionLink: 'Iniciar sesión',\n      actionLink__use_email: 'Usar correo electrónico',\n      actionLink__use_phone: 'Usar teléfono',\n      actionText: '¿Ya tienes una cuenta?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'para continuar en {{applicationName}}',\n      subtitleCombined: 'para continuar en {{applicationName}}',\n      title: 'Crea tu cuenta',\n      titleCombined: 'Crea tu cuenta',\n    },\n  },\n  socialButtonsBlockButton: 'Continuar con {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: '{{email}} ya es miembro de la organización.',\n    captcha_invalid:\n      'Registro fallido debido a validaciones de seguridad fallidas. Por favor, actualice la página para intentarlo de nuevo o comuníquese con el soporte para más asistencia.',\n    captcha_unavailable:\n      'Registro fallido debido a una validación de bot fallida. Por favor, actualice la página para intentarlo de nuevo o comuníquese con el soporte para más asistencia.',\n    form_code_incorrect: 'El código ingresado es incorrecto.',\n    form_identifier_exists__email_address: 'Ya existe una cuenta con esta dirección de correo electrónico.',\n    form_identifier_exists__phone_number: 'Ya existe una cuenta con este número de teléfono.',\n    form_identifier_exists__username: 'Ya existe una cuenta con este nombre de usuario.',\n    form_identifier_not_found: 'No se ha encontrado ninguna cuenta con este identificador.',\n    form_param_format_invalid: 'Formato de parámetro inválido.',\n    form_param_format_invalid__email_address:\n      'La dirección de correo electrónico debe ser una dirección de correo electrónico válida.',\n    form_param_format_invalid__phone_number: 'El número de teléfono debe estar en un formato internacional válido.',\n    form_param_max_length_exceeded__first_name: 'El nombre no debe exceder los 256 caracteres.',\n    form_param_max_length_exceeded__last_name: 'El apellido no debe exceder los 256 caracteres.',\n    form_param_max_length_exceeded__name: 'El nombre no debe exceder los 256 caracteres.',\n    form_param_nil: 'Este campo es obligatorio.',\n    form_param_value_invalid: 'Valor inválido.',\n    form_password_incorrect: 'Contraseña incorrecta.',\n    form_password_length_too_short: 'La contraseña es demasiado corta.',\n    form_password_not_strong_enough: 'Tu contraseña no es lo suficientemente fuerte.',\n    form_password_pwned: 'Tu contraseña ha sido comprometida en una violación de seguridad.',\n    form_password_pwned__sign_in: 'La contraseña ya está en uso en otro servicio.',\n    form_password_size_in_bytes_exceeded:\n      'Tu contraseña ha excedido el número máximo de bytes permitidos, por favor acórtala o elimina algunos caracteres especiales.',\n    form_password_validation_failed: 'La validación de la contraseña falló.',\n    form_username_invalid_character: 'El nombre de usuario contiene caracteres inválidos.',\n    form_username_invalid_length: 'El nombre de usuario debe tener entre 3 y 20 caracteres.',\n    identification_deletion_failed: 'No puedes eliminar tu última identificación.',\n    not_allowed_access:\n      \"La dirección de correo electrónico o el número de teléfono no está permitido para registrarse. Esto puede deberse al uso de '+', '=', '#' o '.' en tu dirección de correo electrónico, el uso de un dominio conectado a un servicio de correo electrónico temporal o la exclusión explícita. Si cree que se trata de un error, póngase en contacto con el soporte.\",\n    organization_domain_blocked: 'Este es un dominio bloqueado, por favor usa otro.',\n    organization_domain_common: 'Este es un dominio común, por favor usa otro.',\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded:\n      'Has alcanzado tu límite de miembros de la organización, incluyendo invitaciones pendientes.',\n    organization_minimum_permissions_needed:\n      'Es necesario que haya al menos un miembro de la organización con los permisos mínimos necesarios.',\n    passkey_already_exists: 'Ya existe una clave de acceso.',\n    passkey_not_supported: 'Las claves de acceso no son compatibles.',\n    passkey_pa_not_supported: 'La clave de acceso no es compatible con la autenticación de dispositivos.',\n    passkey_registration_cancelled: 'El registro de la clave de acceso fue cancelado.',\n    passkey_retrieval_cancelled: 'La recuperación de la clave de acceso fue cancelada.',\n    passwordComplexity: {\n      maximumLength: 'Menos de {{length}} caracteres',\n      minimumLength: '{{length}} caracteres o más',\n      requireLowercase: 'Al menos una letra minúscula',\n      requireNumbers: 'Al menos un número',\n      requireSpecialCharacter: 'Al menos un carácter especial',\n      requireUppercase: 'Al menos una letra mayúscula',\n      sentencePrefix: 'Tu contraseña debe contener:',\n    },\n    phone_number_exists: 'Este número de teléfono ya está en uso. Por favor, inténtelo con otro.',\n    session_exists: 'Ya has iniciado sesión',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: undefined,\n      goodPassword: undefined,\n      notEnough: undefined,\n      suggestions: {\n        allUppercase: undefined,\n        anotherWord: undefined,\n        associatedYears: undefined,\n        capitalization: undefined,\n        dates: undefined,\n        l33t: undefined,\n        longerKeyboardPattern: undefined,\n        noNeed: undefined,\n        pwned: undefined,\n        recentYears: undefined,\n        repeated: undefined,\n        reverseWords: undefined,\n        sequences: undefined,\n        useWords: undefined,\n      },\n      warnings: {\n        common: undefined,\n        commonNames: undefined,\n        dates: undefined,\n        extendedRepeat: undefined,\n        keyPattern: undefined,\n        namesByThemselves: undefined,\n        pwned: undefined,\n        recentYears: undefined,\n        sequences: undefined,\n        similarToCommon: undefined,\n        simpleRepeat: undefined,\n        straightRow: undefined,\n        topHundred: undefined,\n        topTen: undefined,\n        userInputs: undefined,\n        wordByItself: undefined,\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Añadir cuenta',\n    action__manageAccount: 'Administrar cuenta',\n    action__signOut: 'Cerrar sesión',\n    action__signOutAll: 'Salir de todas las cuentas',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: '¡Copiado!',\n      actionLabel__copy: 'Copiar todo',\n      actionLabel__download: 'Descargar .txt',\n      actionLabel__print: 'Imprimir',\n      infoText1: 'Se habilitarán códigos de respaldo para esta cuenta.',\n      infoText2:\n        'Mantenga los códigos de respaldo en secreto y guárdelos de forma segura. Puede regenerar códigos de respaldo si sospecha que se han visto comprometidos.',\n      subtitle__codelist: 'Guárdelos de forma segura y manténgalos en secreto.',\n      successMessage:\n        'Los códigos de respaldo ahora están habilitados. Puede usar uno de estos para iniciar sesión en su cuenta, si pierde el acceso a su dispositivo de autenticación. Cada código solo se puede utilizar una vez.',\n      successSubtitle:\n        'Puede usar uno de estos para iniciar sesión en su cuenta, si pierde el acceso a su dispositivo de autenticación.',\n      title: 'Agregar verificación de código de respaldo',\n      title__codelist: 'Códigos de respaldo',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Seleccione un proveedor para conectar su cuenta.',\n      formHint__noAccounts: 'No hay proveedores de cuentas externas disponibles.',\n      removeResource: {\n        messageLine1: '{{identifier}} será eliminado de esta cuenta.',\n        messageLine2: 'Ya no podrá usar esta cuenta activa y las funciones dependientes ya no funcionarán.',\n        successMessage: '{{connectedAccount}} ha sido eliminado de su cuenta.',\n        title: 'Eliminar cuenta conectada',\n      },\n      socialButtonsBlockButton: 'Conectar cuenta de {{provider | titleize}}',\n      successMessage: 'El proveedor ha sido agregado a su cuenta',\n      title: 'Agregar cuenta conectada',\n    },\n    deletePage: {\n      actionDescription: 'Escribe \"Eliminar cuenta\" a continuación para continuar.',\n      confirm: 'Eliminar cuenta',\n      messageLine1: '¿Estás seguro de que quieres eliminar tu cuenta?',\n      messageLine2: 'Esta acción es permanente e irreversible.',\n      title: 'Eliminar cuenta',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint:\n          'A esta dirección de correo electrónico se le enviará un correo electrónico con un Código de verificación.',\n        formSubtitle: 'Introduzca el código de verificación enviado a {{identifier}}',\n        formTitle: 'Código de verificación',\n        resendButton: 'Re-enviar código',\n        successMessage: 'El correo electrónico {{identifier}} se ha agregado a su cuenta.',\n      },\n      emailLink: {\n        formHint:\n          'Se enviará un correo electrónico con un enlace de verificación a esta dirección de correo electrónico.',\n        formSubtitle: 'Haga clic en el enlace de verificación en el correo electrónico enviado a {{identifier}}',\n        formTitle: 'Enlace de verificación',\n        resendButton: 'Reenviar enlace',\n        successMessage: 'El correo electrónico {{identifier}} se ha agregado a su cuenta.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} será eliminado de esta cuenta.',\n        messageLine2: 'Ya no podrá iniciar sesión con esta dirección de correo electrónico.',\n        successMessage: '{{emailAddress}} ha sido eliminado de su cuenta.',\n        title: 'Eliminar dirección de correo electrónico',\n      },\n      title: 'Agregar dirección de correo electrónico',\n      verifyTitle: 'Verificar correo electrónico',\n    },\n    formButtonPrimary__add: 'Agregar',\n    formButtonPrimary__continue: 'Continuar',\n    formButtonPrimary__finish: 'Terminar',\n    formButtonPrimary__remove: 'Eliminar',\n    formButtonPrimary__save: 'Guardar',\n    formButtonReset: 'Cancelar',\n    mfaPage: {\n      formHint: 'Seleccione un método para agregar.',\n      title: 'Agregar verificación en dos pasos',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Usar número existente',\n      primaryButton__addPhoneNumber: 'Agregar un número de teléfono',\n      removeResource: {\n        messageLine1: '{{identifier}} dejará de recibir el Código de verificación al iniciar sesión.',\n        messageLine2: 'Es posible que su cuenta no sea tan segura. ¿Estás seguro de que quieres continuar?',\n        successMessage: 'Se eliminó la verificación de dos pasos del código SMS para {{mfaPhoneCode}}',\n        title: 'Eliminar la verificación en dos pasos',\n      },\n      subtitle__availablePhoneNumbers:\n        'Seleccione un número de teléfono para registrarse para la verificación en dos pasos del código SMS.',\n      subtitle__unavailablePhoneNumbers:\n        'No hay números de teléfono disponibles para registrarse para la verificación en dos pasos del código SMS.',\n      successMessage1:\n        'Al iniciar sesión, deberá ingresar un código de verificación enviado a este número de teléfono como un paso adicional.',\n      successMessage2:\n        'Guarde estos códigos de respaldo y almacénelos en un lugar seguro. Si pierde el acceso a su dispositivo de autenticación, puede usar los códigos de respaldo para iniciar sesión.',\n      successTitle: 'Verificación de código SMS habilitada',\n      title: 'Agregar verificación de código SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Escanea el código QR en su lugar',\n        buttonUnableToScan__nonPrimary: '¿No puedes escanear el código QR?',\n        infoText__ableToScan:\n          'Configure un nuevo método de inicio de sesión en su aplicación de autenticación y escanee el siguiente código QR para vincularlo a su cuenta.',\n        infoText__unableToScan:\n          'Configure un nuevo método de inicio de sesión en su autenticador e ingrese la clave que se proporciona a continuación.',\n        inputLabel__unableToScan1:\n          'Asegúrese de que las contraseñas basadas en el tiempo o de un solo uso estén habilitadas, luego termine de vincular su cuenta.',\n        inputLabel__unableToScan2:\n          'Alternativamente, si su autenticador admite TOTP URIs, también puede copiar el URI completo.',\n      },\n      removeResource: {\n        messageLine1: 'El código de verificación de este autenticador ya no será necesario al iniciar sesión.',\n        messageLine2: 'Es posible que su cuenta no sea tan segura. ¿Estás seguro de que quieres continuar?',\n        successMessage: 'Se eliminó la verificación en dos pasos a través de la aplicación de autenticación.',\n        title: 'Eliminar la verificación en dos pasos',\n      },\n      successMessage:\n        'La verificación en dos pasos ahora está habilitada. Al iniciar sesión, deberá ingresar un código de verificación de este autenticador como un paso adicional.',\n      title: 'Agregar aplicación de autenticación',\n      verifySubtitle: 'Ingrese el código de verificación generado por su autenticador',\n      verifyTitle: 'Código de verificación',\n    },\n    mobileButton__menu: 'Menú',\n    navbar: {\n      account: 'Perfil',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Gestiona la información de tu cuenta.',\n      security: 'Seguridad',\n      title: 'Cuenta',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '¿Estás seguro de que deseas eliminar este recurso?',\n        title: 'Eliminar Recurso',\n      },\n      subtitle__rename: 'Ingresa el nuevo nombre para la clave de acceso.',\n      title__rename: 'Renombrar Clave de Acceso',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Se recomienda cerrar sesión en todos los demás dispositivos que puedan haber usado su contraseña anterior.',\n      readonly:\n        'Su contraseña actualmente no puede ser editada porque solo puede iniciar sesión a través de la conexión empresarial.',\n      successMessage__set: 'Su contraseña ha sido establecida.',\n      successMessage__signOutOfOtherSessions: 'Todos los demás dispositivos han cerrado sesión.',\n      successMessage__update: 'Tu contraseña ha sido actualizada.',\n      title__set: 'Configurar la clave',\n      title__update: 'Cambiar contraseña',\n    },\n    phoneNumberPage: {\n      infoText: 'Se enviará un mensaje de texto con un enlace de verificación a este número de teléfono.',\n      removeResource: {\n        messageLine1: '{{identifier}} será eliminado de esta cuenta.',\n        messageLine2: 'Ya no podrá iniciar sesión con este número de teléfono.',\n        successMessage: '{{phoneNumber}} ha sido eliminado de su cuenta.',\n        title: 'Eliminar número de teléfono',\n      },\n      successMessage: '{{identifier}} ha sido añadido a tu cuenta.',\n      title: 'Agregar el número de teléfono',\n      verifySubtitle: 'Introduzca el código de verificación enviado a {{identifier}}',\n      verifyTitle: 'Verificar número de teléfono',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Cargue una imagen JPG, PNG, GIF o WEBP de menos de 10 MB',\n      imageFormDestructiveActionSubtitle: 'Eliminar la imagen',\n      imageFormSubtitle: 'Cargar imagen',\n      imageFormTitle: 'Imagen de perfil',\n      readonly: 'La información de su perfil ha sido proporcionada por la conexión empresarial y no puede ser editada.',\n      successMessage: 'Tu perfil ha sido actualizado.',\n      title: 'Actualizar Cuenta',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Cerrar sesión en el dispositivo',\n        title: 'Dispositivos activos',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Inténtelo nuevamente',\n        actionLabel__reauthorize: 'Autorizar ahora',\n        destructiveActionTitle: 'Quitar',\n        primaryButton: 'Conectar cuenta',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'Los permisos necesarios han sido actualizados, y podría estar experimentando funcionalidad limitada. Por favor, reautorice esta aplicación para evitar problemas.',\n        title: 'Cuentas conectadas',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Eliminar cuenta',\n        title: 'Eliminar cuenta',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Eliminar dirección de correo electrónico',\n        detailsAction__nonPrimary: 'Establecer como primario',\n        detailsAction__primary: 'Completar la verificación',\n        detailsAction__unverified: 'Completar la verificación',\n        primaryButton: 'Agregar una dirección de correo electrónico',\n        title: 'Correos electrónicos',\n      },\n      enterpriseAccountsSection: {\n        title: 'Cuentas empresariales',\n      },\n      headerTitle__account: 'Cuenta',\n      headerTitle__security: 'Seguridad',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Regenerar códigos',\n          headerTitle: 'Códigos de respaldo',\n          subtitle__regenerate:\n            'Obtenga un nuevo conjunto de códigos de respaldo seguros. Los códigos de respaldo anteriores se eliminarán y no podrán ser usados.',\n          title__regenerate: 'Regenerar códigos de respaldo',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Establecer por defecto',\n          destructiveActionLabel: 'Eliminar número telefónico',\n        },\n        primaryButton: 'Añadir verificación de dos pasos',\n        title: 'Verificación de dos pasos',\n        totp: {\n          destructiveActionTitle: 'Eliminar',\n          headerTitle: 'Aplicación de autenticación',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Eliminar Clave de Acceso',\n        menuAction__rename: 'Renombrar Clave de Acceso',\n        primaryButton: undefined,\n        title: 'Sección de Claves de Acceso',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Establecer contraseña',\n        primaryButton__updatePassword: 'Cambiar contraseña',\n        title: 'Contraseña',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Quitar número de teléfono',\n        detailsAction__nonPrimary: 'Establecer como primario',\n        detailsAction__primary: 'Completar la verificación',\n        detailsAction__unverified: 'Completar la verificación',\n        primaryButton: 'Agregar un número de teléfono',\n        title: 'Números telefónicos',\n      },\n      profileSection: {\n        primaryButton: 'Actualizar perfil',\n        title: 'Perfil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Crear nombre de usuario',\n        primaryButton__updateUsername: 'Cambiar nombre de usuario',\n        title: 'Nombre de usuario',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Quitar cartera',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Agregar cartera Web3',\n        title: 'Cartera Web3',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Su nombre de usuario ha sido actualizado.',\n      title__set: 'Actualizar nombre de usuario',\n      title__update: 'Actualizar nombre de usuario',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} será eliminado de esta cuenta.',\n        messageLine2: 'Ya no podrá iniciar sesión con esta billetera web3.',\n        successMessage: '{{web3Wallet}} ha sido eliminado de su cuenta.',\n        title: 'Eliminar la billetera web3',\n      },\n      subtitle__availableWallets: 'Seleccione una billetera web3 para conectarse a su cuenta.',\n      subtitle__unavailableWallets: 'No hay billeteras web3 disponibles.',\n      successMessage: 'La billetera ha sido agregada a su cuenta.',\n      title: 'Añadir billetera Web3',\n      web3WalletButtonsBlockButton: 'Conectar billetera',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: '¡Únete a la lista de espera!',\n      actionText:\n        'Si no tienes acceso, puedes unirte a nuestra lista de espera para recibir una invitación más adelante.',\n      formButton: 'Unirse ahora',\n      subtitle: 'Sé uno de los primeros en acceder a {{applicationName}}.',\n      title: 'Únete a la lista de espera',\n    },\n    success: {\n      message: '¡Felicidades! Te has unido exitosamente a la lista de espera.',\n      subtitle: 'Recibirás una invitación para unirte cuando haya espacio disponible.',\n      title: '¡Te has unido a la lista de espera!',\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CACE;AAAA,IACF,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCACE;AAAA,IACF,yCACE;AAAA,IACF,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UACE;AAAA,QACF,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UACE;AAAA,QACF,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YACE;AAAA,MACF,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}