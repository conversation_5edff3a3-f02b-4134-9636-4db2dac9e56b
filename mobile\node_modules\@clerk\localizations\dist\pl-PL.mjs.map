{"version": 3, "sources": ["../src/pl-PL.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const plPL: LocalizationResource = {\n  locale: 'pl-PL',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Powrót',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Domyślny',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Inne urządzenie osobiste',\n  badge__primary: 'Podstawowy',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Wymaga działania',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'To urządzenie',\n  badge__unverified: 'Niezweryfikowany',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Urządzenie użytkownika',\n  badge__you: 'Ty',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Utwórz organizację',\n    invitePage: {\n      formButtonReset: 'Pomiń',\n    },\n    title: 'Utwórz organizację',\n  },\n  dates: {\n    lastDay: \"Wczoraj o godzinie {{ date | timeString('pl-PL') }}\",\n    next6Days: \"{{ date | weekday('pl-PL','long') }} o godzinie {{ date | timeString('pl-PL') }}\",\n    nextDay: \"Jutro o godzinie {{ date | timeString('pl-PL') }}\",\n    numeric: \"{{ date | numeric('pl-PL') }}\",\n    previous6Days: \"Ostatni(a) {{ date | weekday('pl-PL','long') }} o godzinie {{ date | timeString('pl-PL') }}\",\n    sameDay: \"Dzisiaj o godzinie {{ date | timeString('pl-PL') }}\",\n  },\n  dividerText: 'lub',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Użyj innej metody',\n  footerPageLink__help: 'Pomoc',\n  footerPageLink__privacy: 'Prywatność',\n  footerPageLink__terms: 'Warunki',\n  formButtonPrimary: 'Kontynuuj',\n  formButtonPrimary__verify: 'Zweryfikuj',\n  formFieldAction__forgotPassword: 'Zapomniałem/am hasła',\n  formFieldError__matchingPasswords: 'Hasła się zgadzają.',\n  formFieldError__notMatchingPasswords: 'Hasła się nie zgadzają.',\n  formFieldError__verificationLinkExpired: 'Link weryfikacyjny wygasł. Spróbuj ponownie.',\n  formFieldHintText__optional: 'Opcjonalne',\n  formFieldHintText__slug: 'Jest to unikalne ID, które jest czytelne dla człowieka, często używane w adresach URL.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: 'Wprowadź kod zapasowy',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Usuń konto',\n  formFieldInputPlaceholder__emailAddress: 'Wprowadź adres email',\n  formFieldInputPlaceholder__emailAddress_username: 'Adres e-mail lub nazwa użytkownika',\n  formFieldInputPlaceholder__emailAddresses:\n    'Wprowadź lub wklej jeden lub więcej adresów e-mail, oddzielonych spacjami lub przecinkami',\n  formFieldInputPlaceholder__firstName: 'Imię',\n  formFieldInputPlaceholder__lastName: 'Nazwisko',\n  formFieldInputPlaceholder__organizationDomain: 'example.com',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: '<EMAIL>',\n  formFieldInputPlaceholder__organizationName: 'Nazwa organizacji',\n  formFieldInputPlaceholder__organizationSlug: 'moja-organizacja',\n  formFieldInputPlaceholder__password: 'Wprowadź swoje hasło',\n  formFieldInputPlaceholder__phoneNumber: 'Wprowadź numer telefonu',\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Włącz automatyczne zaproszenia dla tej domeny',\n  formFieldLabel__backupCode: 'Kod zapasowy',\n  formFieldLabel__confirmDeletion: 'Potwierdzenie',\n  formFieldLabel__confirmPassword: 'Potwierdź hasło',\n  formFieldLabel__currentPassword: 'Obecne hasło',\n  formFieldLabel__emailAddress: 'Adres e-mail',\n  formFieldLabel__emailAddress_username: 'Adres e-mail lub nazwa użytkownika',\n  formFieldLabel__emailAddresses: 'Adresy e-mail',\n  formFieldLabel__firstName: 'Imię',\n  formFieldLabel__lastName: 'Nazwisko',\n  formFieldLabel__newPassword: 'Nowe hasło',\n  formFieldLabel__organizationDomain: 'Domena',\n  formFieldLabel__organizationDomainDeletePending: 'Usuń oczekujące zaproszenia i propozycje',\n  formFieldLabel__organizationDomainEmailAddress: 'Weryfikacyjny adres e-mail',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Wprowadź adres e-mail w tej domenie, aby otrzymać kod i zweryfikować tę domenę.',\n  formFieldLabel__organizationName: 'Nazwa organizacji',\n  formFieldLabel__organizationSlug: 'Slug URL',\n  formFieldLabel__passkeyName: 'Nazwa klucza dostępu',\n  formFieldLabel__password: 'Hasło',\n  formFieldLabel__phoneNumber: 'Numer telefonu',\n  formFieldLabel__role: 'Rola',\n  formFieldLabel__signOutOfOtherSessions: 'Wyloguj się ze wszystkich innych urządzeń',\n  formFieldLabel__username: 'Nazwa użytkownika',\n  impersonationFab: {\n    action__signOut: 'Wyloguj',\n    title: 'Zalogowano jako {{identifier}}',\n  },\n  maintenanceMode: 'Aktualnie trwają prace konserwacyjne, ale nie powinno to zająć dłużej niż kilka minut.',\n  membershipRole__admin: 'Administrator',\n  membershipRole__basicMember: 'Użytkownik',\n  membershipRole__guestMember: 'Gość',\n  organizationList: {\n    action__createOrganization: 'Stwórz organizację',\n    action__invitationAccept: 'Dołącz',\n    action__suggestionsAccept: 'Poproś o dołączenie',\n    createOrganization: 'Stwórz organizację',\n    invitationAcceptedLabel: 'Dołączono',\n    subtitle: 'to continue to {{applicationName}}',\n    suggestionsAcceptedLabel: 'Pending approval',\n    title: 'Wybierz konto',\n    titleWithoutPersonal: 'Wybierz organizację',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Automatyczne zaproszenia',\n    badge__automaticSuggestion: 'Automatyczne sugestie',\n    badge__manualInvitation: 'Brak automatycznej rejestracji',\n    badge__unverified: 'Niezweryfikowany',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Dodaj domenę do weryfikacji. Użytkownicy z adresami e-mail w tej domenie mogą dołączyć do organizacji automatycznie lub poprosić o dołączenie.',\n      title: 'Dodaj domenę',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed: 'Nie udało się wysłać zaproszeń. Napraw poniższe problemy i spróbuj ponownie:',\n      formButtonPrimary__continue: 'Wyślij zaproszenia',\n      selectDropdown__role: 'Wybierz rolę',\n      subtitle: 'Zaproś nowych użytkowników do tej organizacji',\n      successMessage: 'Zaproszenia zostały pomyślnie wysłane',\n      title: 'Zaproś użytkowników',\n    },\n    membersPage: {\n      action__invite: 'Zaproś',\n      action__search: 'Wyszukaj',\n      activeMembersTab: {\n        menuAction__remove: 'Usuń użytkownika',\n        tableHeader__actions: 'Akcje',\n        tableHeader__joined: 'Dołączył',\n        tableHeader__role: 'Rola',\n        tableHeader__user: 'Użytkownik',\n      },\n      detailsTitle__emptyRow: 'Brak użytkowników do wyświetlenia',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Zaproś użytkowników, łącząc domenę e-mail ze swoją organizacją. Każdy, kto zarejestruje się za pomocą pasującej domeny e-mail, będzie mógł dołączyć do organizacji w dowolnym momencie.',\n          headerTitle: 'Automatyczne zaproszenia',\n          primaryButton: 'Zarządzanie zweryfikowanymi domenami',\n        },\n        table__emptyRow: 'Brak zaproszeń do wyświetlenia',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Anuluj zaproszenie',\n        tableHeader__invited: 'Zaproszony',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Użytkownicy, którzy zarejestrują się za pomocą pasującej domeny e-mail, będą mogli zobaczyć propozycję, aby poprosić o dołączenie do Twojej organizacji.',\n          headerTitle: 'Automatyczne propozycje',\n          primaryButton: 'Zarządzanie zweryfikowanymi domenami',\n        },\n        menuAction__approve: 'Zatwierdź',\n        menuAction__reject: 'Odrzuć',\n        tableHeader__requested: 'Prośby o dostęp',\n        table__emptyRow: 'Brak próśb do wyświetlenia',\n      },\n      start: {\n        headerTitle__invitations: 'Zaproszenia',\n        headerTitle__members: 'Członkowie',\n        headerTitle__requests: 'Prośby',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Zarządzaj organizacją.',\n      general: 'Główne',\n      members: 'Członkowie',\n      title: 'Organizacja',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Wpisz \"{{organizationName}}\" poniżej aby kontynuować.',\n          messageLine1: 'Czy na pewno chcesz usunąć tę organizację?',\n          messageLine2: 'To działanie jest trwałe i nieodwracalne.',\n          successMessage: 'Organizacja została usunięta.',\n          title: 'Usuń organizację',\n        },\n        leaveOrganization: {\n          actionDescription: 'Wpisz \"{{organizationName}}\" poniżej aby kontynuować.',\n          messageLine1:\n            'Czy na pewno chcesz opuścić tę organizację? Stracisz dostęp do tej organizacji i jej aplikacji.',\n          messageLine2: 'Ta akcja jest trwała i nieodwracalna.',\n          successMessage: 'Opuściłeś organizację.',\n          title: 'Opuść organizację',\n        },\n        title: 'Zagrożenie',\n      },\n      domainSection: {\n        menuAction__manage: 'Zarządzaj',\n        menuAction__remove: 'Usuń',\n        menuAction__verify: 'Zweryfikuj',\n        primaryButton: 'Dodaj domenę',\n        subtitle:\n          'Zezwalaj użytkownikom na automatyczne dołączanie do organizacji lub żądaj dołączenia na podstawie zweryfikowanej domeny e-mail.',\n        title: 'Zweryfikowane domeny',\n      },\n      successMessage: 'Organizacja została zaktualizowana.',\n      title: 'Profil organizacji',\n    },\n    removeDomainPage: {\n      messageLine1: 'Domena e-mail {{domain}} zostanie usunięta.',\n      messageLine2: 'Po wykonaniu tej czynności użytkownicy nie będą mogli automatycznie dołączyć do organizacji.',\n      successMessage: 'Domena {{domain}} została usunięta.',\n      title: 'Usuń domenę',\n    },\n    start: {\n      headerTitle__general: 'Ogólne',\n      headerTitle__members: 'Członkowie',\n      profileSection: {\n        primaryButton: undefined,\n        title: 'Profil organizacji',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Usunięcie tej domeny wpłynie na zaproszonych użytkowników.',\n        removeDomainActionLabel__remove: 'Usuń domenę',\n        removeDomainSubtitle: 'Usuń tą domenę ze zweryfikowanych domen',\n        removeDomainTitle: 'Usuń domenę',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Użytkownicy są automatycznie zapraszani do dołączenia do organizacji podczas rejestracji i mogą dołączyć w dowolnym momencie.',\n        automaticInvitationOption__label: 'Automatyczne zaproszenia',\n        automaticSuggestionOption__description:\n          'Użytkownicy otrzymują propozycję, aby poprosić o dołączenie, ale muszą zostać zatwierdzeni przez administratora, zanim będą mogli dołączyć do organizacji.',\n        automaticSuggestionOption__label: 'Automatyczne propozycje',\n        calloutInfoLabel: 'Zmiana trybu rejestracji będzie miała wpływ tylko na nowych użytkowników.',\n        calloutInvitationCountLabel: 'Oczekujące zaproszenia wysłane do użytkowników: {{count}}',\n        calloutSuggestionCountLabel: 'Oczekujące propozycje wysłane do użytkowników: {{count}}',\n        manualInvitationOption__description: 'Użytkowników można zapraszać do organizacji wyłącznie ręcznie.',\n        manualInvitationOption__label: 'Brak automatycznej rejestracji',\n        subtitle: 'Wybierz sposób, w jaki użytkownicy z tej domeny mogą dołączyć do organizacji.',\n      },\n      start: {\n        headerTitle__danger: 'Zagrożenie',\n        headerTitle__enrollment: 'Opcje rejestracji',\n      },\n      subtitle: 'Domena {{domain}} została zweryfikowana. Kontynuuj, wybierając opcje rejestracji',\n      title: 'Zaktualizuj {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Wprowadź kod weryfikacyjny wysłany na Twój adres e-mail',\n      formTitle: 'Kod weryfikacyjny',\n      resendButton: 'Nie otrzymałeś kodu? Wyślij ponownie',\n      subtitle: 'Domena {{domainName}} musi zostać zweryfikowana przez e-mail.',\n      subtitleVerificationCodeScreen:\n        'Kod weryfikacyjny został wysłany na adres {{emailAddress}}. Wprowadź kod, aby kontynuować.',\n      title: 'Zweryfikuj domenę',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Utwórz organizację',\n    action__invitationAccept: 'Dołącz',\n    action__manageOrganization: 'Zarządzaj organizacją',\n    action__suggestionsAccept: 'Prośba o dołączenie',\n    notSelected: 'Nie wybrano organizacji',\n    personalWorkspace: 'Przestrzeń osobista',\n    suggestionsAcceptedLabel: 'Oczekiwanie na zatwierdzenie',\n  },\n  paginationButton__next: 'Następny',\n  paginationButton__previous: 'Poprzedni',\n  paginationRowText__displaying: 'Wyświetlanie',\n  paginationRowText__of: 'z',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Uzyskaj pomoc',\n      actionText: 'Nie używasz żadnej z tych metod?',\n      blockButton__backupCode: 'Użyj kodu zapasowego',\n      blockButton__emailCode: 'Wyślij kod e-mailem do {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Zaloguj się za pomocą hasła',\n      blockButton__phoneCode: 'Wyślij kod SMS-em do {{identifier}}',\n      blockButton__totp: 'Użyj aplikacji uwierzytelniającej',\n      getHelp: {\n        blockButton__emailSupport: 'Skontaktuj się z pomocą',\n        content:\n          'Jeśli masz problem z weryfikacją konta, wyślij do nas e-mail, a postaramy się jak najszybciej przywrócić dostęp.',\n        title: 'Uzyskaj wsparcie',\n      },\n      subtitle: 'Masz problem? Możesz użyć dowolnej z tych metod weryfikacji.',\n      title: 'Użyj innej metody',\n    },\n    backupCodeMfa: {\n      subtitle: 'Twój kod zapasowy to ten, który otrzymałeś podczas konfigurowania uwierzytelniania dwuetapowego.',\n      title: 'Wprowadź kod zapasowy',\n    },\n    emailCode: {\n      formTitle: 'Kod weryfikacyjny',\n      resendButton: 'Nie otrzymałeś kodu? Wyślij ponownie',\n      subtitle: 'Wprowadź kod wysłany na Twój adres e-mail, aby kontynuować',\n      title: 'Wymagana weryfikacja',\n    },\n    noAvailableMethods: {\n      message: 'Nie można kontynuować weryfikacji. Brak dostępnych czynników uwierzytelniania.',\n      subtitle: 'Wystąpił błąd',\n      title: 'Nie możemy zweryfikować twojego konta',\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Użyj innej metody',\n      subtitle: 'Wprowadź hasło, aby kontynuować',\n      title: 'Wymagana weryfikacja',\n    },\n    phoneCode: {\n      formTitle: 'Kod weryfikacyjny',\n      resendButton: 'Nie otrzymałeś kodu? Wyślij ponownie',\n      subtitle: 'Wprowadź kod wysłany na Twój telefon, aby kontynuować',\n      title: 'Wymagana weryfikacja',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Kod weryfikacyjny',\n      resendButton: 'Nie otrzymałeś kodu? Wyślij ponownie',\n      subtitle: 'Aby kontynuować, wprowadź kod weryfikacyjny wysłany na twój telefon',\n      title: 'Wymagana weryfikacja',\n    },\n    totpMfa: {\n      formTitle: 'Kod weryfikacyjny',\n      subtitle: 'Aby kontynuować, wprowadź kod weryfikacyjny wygenerowany przez swoją aplikację uwierzytelniającą',\n      title: 'Wymagana weryfikacja',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Dodaj konto',\n      action__signOutAll: 'Wyloguj się ze wszystkich kont',\n      subtitle: 'Wybierz konto, na którym chcesz kontynuować.',\n      title: 'Wybierz konto',\n    },\n    alternativeMethods: {\n      actionLink: 'Uzyskaj pomoc',\n      actionText: 'Nie używasz żadnej z tych metod?',\n      blockButton__backupCode: 'Użyj kodu zapasowego',\n      blockButton__emailCode: 'Wyślij kod do {{identifier}}',\n      blockButton__emailLink: 'Wyślij link do {{identifier}}',\n      blockButton__passkey: 'Zaloguj się za pomocą klucza dostępowego',\n      blockButton__password: 'Zaloguj się za pomocą hasła',\n      blockButton__phoneCode: 'Wyślij kod do {{identifier}}',\n      blockButton__totp: 'Użyj aplikacji uwierzytelniającej',\n      getHelp: {\n        blockButton__emailSupport: 'Wyślij e-mail do pomocy technicznej',\n        content:\n          'Jeśli masz problem z zalogowaniem się do swojego konta, wyślij do nas e-mail, a postaramy się jak najszybciej przywrócić dostęp.',\n        title: 'Uzyskaj pomoc',\n      },\n      subtitle: 'Masz problem? Możesz użyć dowolnej z tych metod weryfikacji.',\n      title: 'Użyj innego sposobu',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'aby przejść do {{applicationName}}',\n      title: 'Wprowadź kod zapasowy',\n    },\n    emailCode: {\n      formTitle: 'Kod weryfikacyjny',\n      resendButton: 'Wyślij kod ponownie',\n      subtitle: 'aby kontynuować w {{applicationName}}',\n      title: 'Sprawdź swoją pocztę e-mail',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Aby kontynuować, otwórz link weryfikacyjny na urządzeniu i w przeglądarce, w której rozpocząłeś logowanie',\n        title: 'Link weryfikacyjny jest nieprawidłowy dla tego urządzenia',\n      },\n      expired: {\n        subtitle: 'Powróć do oryginalnej karty, aby kontynuować.',\n        title: 'Ten link weryfikacyjny wygasł',\n      },\n      failed: {\n        subtitle: 'Powróć do oryginalnej karty, aby kontynuować.',\n        title: 'Ten link weryfikacyjny jest nieprawidłowy',\n      },\n      formSubtitle: 'Użyj linku weryfikacyjnego wysłanego na Twój adres e-mail',\n      formTitle: 'Link weryfikacyjny',\n      loading: {\n        subtitle: 'Zostaniesz przekierowany wkrótce',\n        title: 'Logowanie...',\n      },\n      resendButton: 'Wyślij link ponownie',\n      subtitle: 'aby kontynuować w {{applicationName}}',\n      title: 'Sprawdź swoją pocztę e-mail',\n      unusedTab: {\n        title: 'Możesz zamknąć tę kartę',\n      },\n      verified: {\n        subtitle: 'Zostaniesz przekierowany wkrótce',\n        title: 'Pomyślnie zalogowano',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Powróć do oryginalnej karty, aby kontynuować',\n        subtitleNewTab: 'Powróć do nowo otwartej karty, aby kontynuować',\n        titleNewTab: 'Zalogowano na innej karcie',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Kod werfikacyjny resetowania hasła',\n      resendButton: 'Nie otrzymałeś kodu? Wyślij ponownie',\n      subtitle: 'aby zresetować hasło',\n      subtitle_email: 'Najpierw wprowadź kod wysłany na Twój adres e-mail',\n      subtitle_phone: 'Najpierw wprowadź kod wysłany na Twój numer telefonu',\n      title: 'Zmień hasło',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Zresetuj hasło',\n      label__alternativeMethods: 'Lub zaloguj się za pomocą innej metody',\n      title: 'Zapomniałeś hasła?',\n    },\n    noAvailableMethods: {\n      message: 'Nie można kontynuować logowania. Brak dostępnych czynników uwierzytelniających.',\n      subtitle: 'Wystąpił błąd',\n      title: 'Nie można się zalogować',\n    },\n    passkey: {\n      subtitle:\n        'Użycie klucza dostępu potwierdza, że to Ty. Urządzenie może poprosić o twój odcisk palca, twarz lub blokadę ekranu.',\n      title: 'Użyj swojego klucza dostępowego',\n    },\n    password: {\n      actionLink: 'Użyj innego sposobu',\n      subtitle: 'aby kontynuować w {{applicationName}}',\n      title: 'Wprowadź swoje hasło',\n    },\n    passwordPwned: {\n      title: 'Hasło skompromitowane',\n    },\n    phoneCode: {\n      formTitle: 'Kod weryfikacyjny',\n      resendButton: 'Wyślij kod ponownie',\n      subtitle: 'aby przejść do {{applicationName}}',\n      title: 'Sprawdź swój telefon',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Kod weryfikacyjny',\n      resendButton: 'Wyślij kod ponownie',\n      subtitle: 'Aby kontynuować, wprowadź kod weryfikacyjny wysłany na Twój telefon',\n      title: 'Sprawdź swój telefon',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Zmień hasło',\n      requiredMessage: 'Z powodów bezpieczeństwa konieczne jest zresetowanie hasła.',\n      successMessage: 'Twoje hasło zostało pomyślnie zresetowane. Logujemy Cię, proszę czekać...',\n      title: 'Ustaw nowe hasło',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Przed zresetowaniem hasła musimy zweryfikować tożsamość użytkownika.',\n    },\n    start: {\n      actionLink: 'Zarejestruj się',\n      actionLink__join_waitlist: 'Dołącz do listy oczekujących',\n      actionLink__use_email: 'Użyj adresu e-mail',\n      actionLink__use_email_username: 'Użyj adresu e-mail lub nazwy użytkownika',\n      actionLink__use_passkey: 'Użyj klucza dostępowego',\n      actionLink__use_phone: 'Użyj numeru telefonu',\n      actionLink__use_username: 'Użyj nazwy użytkownika',\n      actionText: 'Nie masz konta?',\n      actionText__join_waitlist: 'Chcesz otrzymać wczesny dostęp?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'by przejść do aplikacji {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Zaloguj się',\n      titleCombined: 'Kontynuuj do {{applicationName}}',\n    },\n    totpMfa: {\n      formTitle: 'Kod weryfikacyjny',\n      subtitle: 'Aby kontynuować, wprowadź kod weryfikacyjny wygenerowany przez aplikację uwierzytelniającą',\n      title: 'Weryfikacja dwustopniowa',\n    },\n  },\n  signInEnterPasswordTitle: 'Wprowadź swoje hasło',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Zaloguj się',\n      actionText: 'Masz już konto?',\n      subtitle: 'by przejść do aplikacji {{applicationName}}',\n      title: 'Uzupełnij brakujące pola',\n    },\n    emailCode: {\n      formSubtitle: 'Wprowadź kod weryfikacyjny wysłany na Twój adres e-mail',\n      formTitle: 'Kod weryfikacyjny',\n      resendButton: 'Nie otrzymałeś kodu? Wyślij ponownie',\n      subtitle: 'by przejść do aplikacji {{applicationName}}',\n      title: 'Zweryfikuj swój adres e-mail',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Aby kontynuować, otwórz link weryfikacyjny na urządzeniu i w przeglądarce, w której rozpocząłeś rejestrację',\n        title: 'Link weryfikacyjny jest nieprawidłowy dla tego urządzenia',\n      },\n      formSubtitle: 'Użyj linku weryfikacyjnego wysłanego na Twój adres e-mail',\n      formTitle: 'Link weryfikacyjny',\n      loading: {\n        title: 'Rejestrowanie...',\n      },\n      resendButton: 'Nie otrzymałeś linku? Wyślij ponownie',\n      subtitle: 'by przejść do aplikacji {{applicationName}}',\n      title: 'Zweryfikuj swój adres e-mail',\n      verified: {\n        title: 'Pomyślnie zarejestrowano',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Powróć do nowo otwartej karty, aby kontynuować',\n        subtitleNewTab: 'Powróć do poprzedniej karty, aby kontynuować',\n        title: 'Adres e-mail został pomyślnie zweryfikowany',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Akceptuję {{ privacyPolicyLink || link(\"Politykę prywatności\") }}',\n        label__onlyTermsOfService: 'Akceptuję {{ termsOfServiceLink || link(\"Warunki świadczenia usługi\") }}\"',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Akceptuję {{ termsOfServiceLink || link(\"Warunki świadczenia usługi\") }} i {{ privacyPolicyLink || link(\"Politykę prywatności\") }}',\n      },\n      continue: {\n        subtitle: 'Przeczytaj i zaakceptuj warunki, aby kontynuować',\n        title: 'Zgoda prawna',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Wprowadź kod weryfikacyjny wysłany na Twój numer telefonu',\n      formTitle: 'Kod weryfikacyjny',\n      resendButton: 'Nie otrzymałeś kodu? Wyślij ponownie',\n      subtitle: 'by przejść do aplikacji {{applicationName}}',\n      title: 'Zweryfikuj swój numer telefonu',\n    },\n    restrictedAccess: {\n      actionLink: 'Zaloguj się',\n      actionText: 'Masz już konto?',\n      blockButton__emailSupport: 'Skontaktuj się z pomocą',\n      blockButton__joinWaitlist: 'Dołącz do listy oczekujących',\n      subtitle:\n        'Rejestracja jest obecnie wyłączona. Jeśli uważasz, że powinieneś mieć dostęp, skontaktuj się z pomocą.',\n      subtitleWaitlist:\n        'Rejestracja jest obecnie wyłączona. Aby dowiedzieć się jako pierwszy o naszym starcie, dołącz do listy oczekujących.',\n      title: 'Dostęp ograniczony',\n    },\n    start: {\n      actionLink: 'Zaloguj się',\n      actionLink__use_email: 'Użyj adresu e-mail',\n      actionLink__use_phone: 'Użyj numeru telefonu',\n      actionText: 'Masz już konto?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'by przejść do aplikacji {{applicationName}}',\n      subtitleCombined: 'by przejść do aplikacji {{applicationName}}',\n      title: 'Utwórz swoje konto',\n      titleCombined: 'Utwórz swoje konto',\n    },\n  },\n  socialButtonsBlockButton: 'Kontynuuj z {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: '{{email}} jest już członkiem organizacji.',\n    captcha_invalid:\n      'Rejestracja nie powiodła się z powodu niepowodzenia weryfikacji zabezpieczeń. Odśwież stronę, aby spróbować ponownie lub skontaktuj się z pomocą, aby uzyskać wsparcie.',\n    captcha_unavailable:\n      'Rejestracja nie powiodła się z powodu niedostępności weryfikacji botów. Odśwież stronę, aby spróbować ponownie lub skontaktuj się z pomocą, aby uzyskać wsparcie.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: 'Adres e-mail jest już zajęty. Proszę spróbować innego.',\n    form_identifier_exists__phone_number: 'Ten numer telefonu jest zajęty. Spróbuj użyć innego.',\n    form_identifier_exists__username: 'Ta nazwa użytkownika jest zajęta. Spróbuj użyć innej.',\n    form_identifier_not_found: 'Nie znaleziono konta o tym identyfikatorze. Sprawdź i spróbuj ponownie.',\n    form_param_format_invalid: 'Wprowadzona wartość ma nieprawidłowy format. Sprawdź i popraw ją.',\n    form_param_format_invalid__email_address: 'Adres e-mail powinien być poprawnym adresem e-mail.',\n    form_param_format_invalid__phone_number: 'Numer telefonu powinien mieć prawidłowy format międzynarodowy',\n    form_param_max_length_exceeded__first_name: 'Imię nie powinno przekraczać 256 znaków.',\n    form_param_max_length_exceeded__last_name: 'Nazwisko nie powinno przekraczać 256 znaków.',\n    form_param_max_length_exceeded__name: 'Nazwa nie powinna przekraczać 256 znaków.',\n    form_param_nil: 'To pole jest wymagane i nie może być puste.',\n    form_param_value_invalid: 'Wprowadzona wartość jest nieprawidłowa. Popraw ją.',\n    form_password_incorrect: 'Wprowadzone hasło jest nieprawidłowe. Spróbuj ponownie.',\n    form_password_length_too_short: 'Twoje hasło jest zbyt krótkie. Musi mieć co najmniej 8 znaków.',\n    form_password_not_strong_enough: 'Twoje hasło nie jest wystarczająco silne',\n    form_password_pwned:\n      'To hasło zostało znalezione w wyniku włamania i nie można go użyć. Zamiast tego spróbuj użyć innego hasła.',\n    form_password_pwned__sign_in: 'To hasło zostało znalezione w wyniku włamania i nie można go użyć. Zresetuj hasło.',\n    form_password_size_in_bytes_exceeded:\n      'Twoje hasło przekroczyło maksymalną dozwoloną liczbę bajtów, skróć je lub usuń niektóre znaki specjalne.',\n    form_password_validation_failed: 'Podane hasło jest nieprawidłowe',\n    form_username_invalid_character:\n      'Twoja nazwa użytkownika zawiera nieprawidłowe znaki. Prosimy o używanie wyłącznie liter, cyfr i podkreśleń.',\n    form_username_invalid_length: 'Nazwa użytkownika musi zawierać od {{min_length}} do {{max_length}} znaków.',\n    identification_deletion_failed: 'Nie można usunąć ostatniego identyfikatora.',\n    not_allowed_access:\n      \"Adres e-mail lub numer telefonu nie jest dozwolony do rejestracji. Może to być spowodowane użyciem '+', '=', '#' lub '.' w adresie e-mail, użyciem domeny skojarzonej z usługą poczty e-mail tymczasowej lub jawnego wykluczenia.\",\n    organization_domain_blocked: 'To jest zablokowana domena dostawcy poczty e-mail. Użyj innej domeny.',\n    organization_domain_common: 'To jest popularna domena dostawcy poczty e-mail. Użyj innej domeny.',\n    organization_domain_exists_for_enterprise_connection:\n      'Ta domena jest już używana do logowania jednokrotnego w organizacji.',\n    organization_membership_quota_exceeded: 'Osiągnięto limit członkostwa w organizacji, w tym zaległych zaproszeń.',\n    organization_minimum_permissions_needed:\n      'Musi istnieć co najmniej jeden członek organizacji z minimalnymi wymaganymi uprawnieniami.',\n    passkey_already_exists: 'Klucz dostępu jest już zarejestrowany w tym urządzeniu.',\n    passkey_not_supported: 'Klucze dostępu nie są obsługiwane przez to urządzenie.',\n    passkey_pa_not_supported: 'Rejestracja wymaga platformy uwierzytelniającej, ale urządzenie jej nie obsługuje.',\n    passkey_registration_cancelled: 'Rejestracja klucza dostępu została anulowana lub upłynął jej limit czasu.',\n    passkey_retrieval_cancelled: 'Weryfikacja klucza dostępu została anulowana lub upłynął limit czasu.',\n    passwordComplexity: {\n      maximumLength: 'mniej niż {{length}} znaków',\n      minimumLength: '{{length}} lub więcej znaków',\n      requireLowercase: 'małą literę',\n      requireNumbers: 'cyfrę',\n      requireSpecialCharacter: 'znak specjalny',\n      requireUppercase: 'wielką literę',\n      sentencePrefix: 'Twoje hasło musi zawierać',\n    },\n    phone_number_exists: 'Numer telefonu jest już zajęty. Proszę spróbować innego.',\n    session_exists: 'Jesteś już zalogowany.',\n    web3_missing_identifier: 'Nie można znaleźć rozszerzenia Web3 Wallet. Zainstaluj je, aby kontynuować.',\n    zxcvbn: {\n      couldBeStronger: 'Twoje hasło jest odpowiednie, ale mogłoby być silniejsze. Spróbuj dodać więcej znaków.',\n      goodPassword: 'Twoje hasło jest wystarczająco silne.',\n      notEnough: 'Twoje hasło jest zbyt słabe. Spróbuj dodać więcej znaków.',\n      suggestions: {\n        allUppercase: 'Unikaj używania samych wielkich liter.',\n        anotherWord: 'Dodaj więcej słów, które są rzadsze.',\n        associatedYears: 'Unikaj lat związanych z Tobą.',\n        capitalization: 'Używaj wielkich liter częsciej.',\n        dates: 'Unikaj dat związanych z Tobą.',\n        l33t: \"Unikaj przewidywalnego zamieniania liter, takich jak '@' za 'a'.\",\n        longerKeyboardPattern: 'Używaj długich wzorów na klawiaturze, zmieniając kierunek pisania wielokrotnie.',\n        noNeed: 'Możesz tworzyć silne hasła bez używania symboli, cyfr lub wielkich liter.',\n        pwned: 'Jeżeli używasz tego hasła gdzie indziej, zmień je jak najszybciej.',\n        recentYears: 'Unikaj ostatnich lat.',\n        repeated: 'Unikaj powtarzanych słów i znaków.',\n        reverseWords: 'Unikaj wpisywania popularnych słów od tyłu.',\n        sequences: 'Unikaj popularnych kombinacji znaków.',\n        useWords: 'Używaj wielu słów, ale unikaj popularnych fraz.',\n      },\n      warnings: {\n        common: 'Jest to często używane hasło.',\n        commonNames: 'Imiona i nazwiska są łatwe do odgadnięcia.',\n        dates: 'Daty są łatwe do odgadnięcia.',\n        extendedRepeat: 'Powtarzające się wzorce znaków, takie jak \"abcabcabc\", są łatwe do odgadnięcia.',\n        keyPattern: 'Krótkie wzory klawiatury są łatwe do odgadnięcia.',\n        namesByThemselves: 'Pojedyncze imiona lub nazwiska są łatwe do odgadnięcia.',\n        pwned: 'Twoje hasło zostało ujawnione w wyniku wycieku danych w Internecie.',\n        recentYears: 'Ostatnie lata są łatwe do odgadnięcia.',\n        sequences: 'Typowe sekwencje znaków, takie jak \"abc\", są łatwe do odgadnięcia.',\n        similarToCommon: 'Jest to podobne do powszechnie używanego hasła.',\n        simpleRepeat: 'Powtarzające się znaki, takie jak \"aaa\", są łatwe do odgadnięcia.',\n        straightRow: 'Proste rzędy klawiszy na klawiaturze są łatwe do odgadnięcia.',\n        topHundred: 'To jest często używane hasło.',\n        topTen: 'To jest bardzo często używane hasło.',\n        userInputs: 'Nie powinno być żadnych danych osobowych ani danych związanych ze stroną.',\n        wordByItself: 'Pojedyncze słowa są łatwe do odgadnięcia.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Dodaj konto',\n    action__manageAccount: 'Zarządzaj kontem',\n    action__signOut: 'Wyloguj',\n    action__signOutAll: 'Wyloguj ze wszystkich kont',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Skopiowane!',\n      actionLabel__copy: 'Skopiuj wszystkie',\n      actionLabel__download: 'Pobierz .txt',\n      actionLabel__print: 'Drukuj',\n      infoText1: 'Kody zapasowe zostaną włączone dla tego konta.',\n      infoText2:\n        'Przechowuj kody zapasowe w tajemnicy i bezpiecznie. Możesz wygenerować nowe kody, jeśli podejrzewasz, że zostały skompromitowane.',\n      subtitle__codelist: 'Przechowuj je bezpiecznie i zachowaj w tajemnicy.',\n      successMessage:\n        'Kody zapasowe są teraz włączone. Możesz użyć jednego z tych kodów do zalogowania się na swoje konto, jeśli utracisz dostęp do urządzenia uwierzytelniającego. Każdy kod można użyć tylko raz.',\n      successSubtitle:\n        'Możesz użyć jednego z tych kodów do zalogowania się na swoje konto, jeśli utracisz dostęp do urządzenia uwierzytelniającego.',\n      title: 'Dodaj weryfikację kodem zapasowym',\n      title__codelist: 'Kody zapasowe',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Wybierz dostawcę, aby połączyć konto.',\n      formHint__noAccounts: 'Nie ma dostępnych zewnętrznych dostawców kont.',\n      removeResource: {\n        messageLine1: '{{identifier}} zostanie usunięte z tego konta.',\n        messageLine2:\n          'Nie będziesz już mógł korzystać z tego połączonego konta i wszystkie zależne funkcje przestaną działać.',\n        successMessage: '{{connectedAccount}} zostało usunięte z Twojego konta.',\n        title: 'Usuń połączone konto',\n      },\n      socialButtonsBlockButton: 'Połącz konto {{provider|titleize}}',\n      successMessage: 'Dostawca został dodany do Twojego konta.',\n      title: 'Dodaj połączone konto',\n    },\n    deletePage: {\n      actionDescription: 'Wpisz \"Usuń konto\" poniżej aby kontynuować.',\n      confirm: 'Usuń konto',\n      messageLine1: 'Czy na pewno chcesz usunąć to konto?',\n      messageLine2: 'Ta operacja jest nieodwracalna i nie można jej cofnąć.',\n      title: 'Usuń konto',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'E-mail zawierający kod weryfikacyjny zostanie wysłany na ten adres e-mail.',\n        formSubtitle: 'Wprowadź kod weryfikacyjny wysłany na adres {{identifier}}',\n        formTitle: 'Kod weryfikacyjny',\n        resendButton: 'Wyślij ponownie kod',\n        successMessage: 'Adres e-mail {{identifier}} został dodany do twojego konta.',\n      },\n      emailLink: {\n        formHint: 'E-mail zawierający link weryfikacyjny zostanie wysłany na ten adres e-mail.',\n        formSubtitle: 'Kliknij w link weryfikacyjny w e-mailu wysłanym na adres {{identifier}}',\n        formTitle: 'Link weryfikacyjny',\n        resendButton: 'Wyślij ponownie link',\n        successMessage: 'Adres e-mail {{identifier}} został dodany do twojego konta.',\n      },\n      enterpriseSSOLink: {\n        formButton: 'Kliknij, aby się zalogować',\n        formSubtitle: 'Ukończ logowanie za pomocą {{identifier}}',\n      },\n      formHint: 'Przed dodaniem adresu e-mail do konta należy go zweryfikować.',\n      removeResource: {\n        messageLine1: '{{identifier}} zostanie usunięty z tego konta.',\n        messageLine2: 'Nie będzie już możliwe zalogowanie się za pomocą tego adresu e-mail.',\n        successMessage: '{{emailAddress}} został usunięty z twojego konta.',\n        title: 'Usuń adres e-mail',\n      },\n      title: 'Dodaj adres e-mail',\n      verifyTitle: 'Zweryfikuj adres e-mail',\n    },\n    formButtonPrimary__add: 'Dodaj',\n    formButtonPrimary__continue: 'Kontynuuj',\n    formButtonPrimary__finish: 'Zakończ',\n    formButtonPrimary__remove: 'Usuń',\n    formButtonPrimary__save: 'Zapisz',\n    formButtonReset: 'Anuluj',\n    mfaPage: {\n      formHint: 'Wybierz metodę dodania.',\n      title: 'Dodaj weryfikację dwuetapową',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Użyj istniejącego numeru',\n      primaryButton__addPhoneNumber: 'Dodaj numer telefonu',\n      removeResource: {\n        messageLine1: '{{identifier}} nie będzie już otrzymywał kodów weryfikacyjnych podczas logowania.',\n        messageLine2: 'Twoje konto może być mniej bezpieczne. Czy na pewno chcesz kontynuować?',\n        successMessage:\n          'Weryfikacja kodem SMS w dwustopniowym procesie uwierzytelniania została usunięta dla {{mfaPhoneCode}}',\n        title: 'Usuń dwustopniową weryfikację',\n      },\n      subtitle__availablePhoneNumbers:\n        'Wybierz numer telefonu, aby zarejestrować weryfikację kodem SMS w dwustopniowym procesie uwierzytelniania.',\n      subtitle__unavailablePhoneNumbers:\n        'Brak dostępnych numerów telefonów do zarejestrowania weryfikacji kodem SMS w dwustopniowym procesie uwierzytelniania.',\n      successMessage1: 'Podczas logowania należy dodatkowo wprowadzić kod weryfikacyjny wysłany na ten numer telefonu.',\n      successMessage2:\n        'Zapisz te kody zapasowe i przechowuj je w bezpiecznym miejscu. Jeśli utracisz dostęp do urządzenia uwierzytelniającego, możesz użyć kodów zapasowych, aby się zalogować.',\n      successTitle: 'Weryfikacja kodem SMS włączona',\n      title: 'Dodaj weryfikację kodem SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Zamiast tego zeskanuj kod QR',\n        buttonUnableToScan__nonPrimary: 'Nie można zeskanować kodu QR?',\n        infoText__ableToScan:\n          'Ustaw nową metodę logowania w swojej aplikacji autentykacyjnej i zeskanuj następujący kod QR, aby połączyć go z Twoim kontem.',\n        infoText__unableToScan:\n          'Ustaw nową metodę logowania w swojej aplikacji autentykacyjnej i wprowadź poniższy klucz.',\n        inputLabel__unableToScan1:\n          'Upewnij się, że włączona jest opcja jednorazowe hasła lub hasła oparte na czasie, a następnie zakończ łączenie konta.',\n        inputLabel__unableToScan2:\n          'Alternatywnie, jeśli Twoja aplikacja autentykacyjna obsługuje URI TOTP, możesz również skopiować pełny URI.',\n      },\n      removeResource: {\n        messageLine1: 'Kody weryfikacyjne z tej aplikacji autentykacyjnej nie będą już wymagane podczas logowania.',\n        messageLine2: 'Twoje konto może być mniej bezpieczne. Czy na pewno chcesz kontynuować?',\n        successMessage: 'Weryfikacja dwuetapowa za pomocą aplikacji autentykacyjnej została usunięta.',\n        title: 'Usuń weryfikację dwuetapową',\n      },\n      successMessage:\n        'Weryfikacja dwuetapowa jest teraz włączona. Przy logowaniu będziesz musiał wprowadzić kod weryfikacyjny z tej aplikacji jako dodatkowy krok.',\n      title: 'Dodaj aplikację autentykacyjną',\n      verifySubtitle: 'Wprowadź kod weryfikacyjny wygenerowany przez Twoją aplikację autentykacyjną',\n      verifyTitle: 'Kod weryfikacyjny',\n    },\n    mobileButton__menu: 'Menu',\n    navbar: {\n      account: 'Profil',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Zarządzaj danymi konta.',\n      security: 'Bezpieczeństwo',\n      title: 'Konto',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} zostanie usunięty z tego konta.',\n        title: 'Usuń klucz dostępu',\n      },\n      subtitle__rename: 'Możesz zmienić nazwę klucza dostępu aby go łatwiej znaleźć.',\n      title__rename: 'Zmień nazwę klucza dostępu',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Zaleca się wylogowanie z innych urządzeń, które mogły używać starego hasła.',\n      readonly:\n        'Obecnie nie można edytować hasła, ponieważ możesz zalogować się tylko za pośrednictwem połączenia firmowego.',\n      successMessage__set: 'Twoje hasło zostało ustawione.',\n      successMessage__signOutOfOtherSessions: 'Wylogowano z wszystkich innych urządzeń.',\n      successMessage__update: 'Twoje hasło zostało zaktualizowane.',\n      title__set: 'Ustaw hasło',\n      title__update: 'Zmień hasło',\n    },\n    phoneNumberPage: {\n      infoText: 'Wiadomość tekstowa zawierająca link weryfikacyjny zostanie wysłana na ten numer telefonu.',\n      removeResource: {\n        messageLine1: '{{identifier}} zostanie usunięty z tego konta.',\n        messageLine2: 'Nie będzie już możliwe zalogowanie się za pomocą tego numeru telefonu.',\n        successMessage: '{{phoneNumber}} został usunięty z twojego konta.',\n        title: 'Usuń numer telefonu',\n      },\n      successMessage: '{{identifier}} został dodany do twojego konta.',\n      title: 'Dodaj numer telefonu',\n      verifySubtitle: 'Wpisz kod weryfikacyjny wysłany na {{identifier}}',\n      verifyTitle: 'Zweryfikuj numer telefonu',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Prześlij zdjęcie w formacie JPG, PNG, GIF lub WEBP mniejsze niż 10 MB',\n      imageFormDestructiveActionSubtitle: 'Usuń zdjęcie',\n      imageFormSubtitle: 'Prześlij zdjęcie',\n      imageFormTitle: 'Zdjęcie profilowe',\n      readonly: 'Informacje o Twoim profilu zostały udostępnione przez połączenie firmowe i nie można ich edytować.',\n      successMessage: 'Twój profil został zaktualizowany.',\n      title: 'Edytuj profil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Wyloguj z urządzenia',\n        title: 'Aktywne urządzenia',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Spróbuj ponownie',\n        actionLabel__reauthorize: 'Autoryzuj teraz',\n        destructiveActionTitle: 'Odłącz',\n        primaryButton: 'Połącz konto',\n        subtitle__disconnected: 'To konto zostało odłączone',\n        subtitle__reauthorize:\n          'Wymagane zakresy zostały zaktualizowane i funkcjonalność aplikacji może być ograniczona. Aby uniknąć problemów, należy ponownie autoryzować aplikację',\n        title: 'Połączone konta',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Usuń konto',\n        title: 'Niebezpieczeństwo',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Usuń adres email',\n        detailsAction__nonPrimary: 'Ustaw jako główny',\n        detailsAction__primary: 'Zakończ weryfikację',\n        detailsAction__unverified: 'Zakończ weryfikację',\n        primaryButton: 'Dodaj adres email',\n        title: 'Adresy email',\n      },\n      enterpriseAccountsSection: {\n        title: 'Konta firmowe',\n      },\n      headerTitle__account: 'Konto',\n      headerTitle__security: 'Bezpieczeństwo',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Wygeneruj kody',\n          headerTitle: 'Kody zapasowe',\n          subtitle__regenerate:\n            'Otrzymaj nowy zestaw bezpiecznych kodów zapasowych. Poprzednie kody zapasowe zostaną usunięte i nie będą działać.',\n          title__regenerate: 'Wygeneruj nowe kody zapasowe',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Ustaw jako domyślny',\n          destructiveActionLabel: 'Usuń numer telefonu',\n        },\n        primaryButton: 'Dodaj weryfikację dwuetapową',\n        title: 'Weryfikacja dwuetapowa',\n        totp: {\n          destructiveActionTitle: 'Usuń',\n          headerTitle: 'Aplikacja autoryzacyjna',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Usuń',\n        menuAction__rename: 'Zmień nazwę',\n        primaryButton: undefined,\n        title: 'Klucze dostępu',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Ustaw hasło',\n        primaryButton__updatePassword: 'Zmień hasło',\n        title: 'Hasło',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Usuń numer telefonu',\n        detailsAction__nonPrimary: 'Ustaw jako główny',\n        detailsAction__primary: 'Zakończ weryfikację',\n        detailsAction__unverified: 'Zakończ weryfikację',\n        primaryButton: 'Dodaj numer telefonu',\n        title: 'Numery telefonów',\n      },\n      profileSection: {\n        primaryButton: 'Zaaktualizuj profil',\n        title: 'Profil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Ustaw nazwę użytkownika',\n        primaryButton__updateUsername: 'Zmień nazwę użytkownika',\n        title: 'Nazwa użytkownika',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Usuń portfel',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Portfele Web3',\n        title: 'Portfele Web3',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Twoja nazwa użytkownika została zaktualizowana.',\n      title__set: 'Zmień nazwę użytkownika',\n      title__update: 'Zmień nazwę użytkownika',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} zostanie usunięty z tego konta.',\n        messageLine2: 'Nie będziesz już mógł się zalogować za pomocą tego portfela web3.',\n        successMessage: '{{web3Wallet}} został usunięty z Twojego konta.',\n        title: 'Usuń portfel web3',\n      },\n      subtitle__availableWallets: 'Wybierz portfel web3 do połączenia z Twoim kontem.',\n      subtitle__unavailableWallets: 'Nie ma dostępnych portfeli web3.',\n      successMessage: 'Portfel został dodany do Twojego konta.',\n      title: 'Dodaj portfel web3',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: 'Zaloguj się',\n      actionText: 'Masz już konto?',\n      formButton: 'Dołącz do listy oczekujących',\n      subtitle: 'Wpisz swój adres e-mail, a my powiadomimy Cię, gdy miejsce dla Ciebie będzie gotowe.',\n      title: 'Dołącz do listy oczekujących',\n    },\n    success: {\n      message: 'Wkrótce nastąpi przekierowanie...',\n      subtitle: 'Skontaktujemy się z Tobą, gdy miejsce dla Ciebie będzie gotowe',\n      title: 'Dziękujemy za dołączenie do listy oczekujących!',\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UACE;AAAA,MACF,kBACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCACE;AAAA,IACF,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDACE;AAAA,IACF,wCAAwC;AAAA,IACxC,yCACE;AAAA,IACF,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBAAiB;AAAA,MACjB,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}