{"version": 3, "sources": ["../src/sk-SK.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const skSK: LocalizationResource = {\n  locale: 'sk-SK',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Späť',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Predvolené',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Iné zariadenie zástupcu',\n  badge__primary: 'Hlavný',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Vyžaduje akciu',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Toto zariadenie',\n  badge__unverified: 'Nepotvrdené',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Zariadenie používateľa',\n  badge__you: 'Vy',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Vytvoriť organizáciu',\n    invitePage: {\n      formButtonReset: 'Preskočiť',\n    },\n    title: 'Vytvoriť organizáciu',\n  },\n  dates: {\n    lastDay: \"Včera o {{ date | timeString('sk-SK') }}\",\n    next6Days: \"Příští {{ date | weekday('sk-SK','long') }} o {{ date | timeString('sk-SK') }}\",\n    nextDay: \"Zajtra o {{ date | timeString('sk-SK') }}\",\n    numeric: \"{{ date | numeric('sk-SK') }}\",\n    previous6Days: \"Minulý {{ date | weekday('sk-SK','long') }} o {{ date | timeString('sk-SK') }}\",\n    sameDay: \"Dnes o {{ date | timeString('sk-SK') }}\",\n  },\n  dividerText: 'alebo',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Použiť inú metódu',\n  footerPageLink__help: 'Pomoc',\n  footerPageLink__privacy: 'Ochrana súkromia',\n  footerPageLink__terms: 'Podmienky',\n  formButtonPrimary: 'Pokračovať',\n  formButtonPrimary__verify: 'Verify',\n  formFieldAction__forgotPassword: 'Zabudli ste heslo?',\n  formFieldError__matchingPasswords: 'Heslá sa zhodujú.',\n  formFieldError__notMatchingPasswords: 'Heslá sa nezhodujú.',\n  formFieldError__verificationLinkExpired: 'The verification link expired. Please request a new link.',\n  formFieldHintText__optional: 'Voliteľné',\n  formFieldHintText__slug: 'A slug is a human-readable ID that must be unique. It’s often used in URLs.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Delete account',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses:\n    'Zadajte alebo vložte jednu alebo viac emailových adries oddelených medzerou alebo čiarkou',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: undefined,\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Enable automatic invitations for this domain',\n  formFieldLabel__backupCode: 'Záložný kód',\n  formFieldLabel__confirmDeletion: 'Confirmation',\n  formFieldLabel__confirmPassword: 'Potvrdiť heslo',\n  formFieldLabel__currentPassword: 'Súčasné heslo',\n  formFieldLabel__emailAddress: 'Emailová adresa',\n  formFieldLabel__emailAddress_username: 'Emailová adresa alebo užívateľské meno',\n  formFieldLabel__emailAddresses: 'Emailové adresy',\n  formFieldLabel__firstName: 'Meno',\n  formFieldLabel__lastName: 'Priezvisko',\n  formFieldLabel__newPassword: 'Nové heslo',\n  formFieldLabel__organizationDomain: 'Domain',\n  formFieldLabel__organizationDomainDeletePending: 'Delete pending invitations and suggestions',\n  formFieldLabel__organizationDomainEmailAddress: 'Verification email address',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Enter an email address under this domain to receive a code and verify this domain.',\n  formFieldLabel__organizationName: 'Názov organizácie',\n  formFieldLabel__organizationSlug: 'URL adresa',\n  formFieldLabel__passkeyName: undefined,\n  formFieldLabel__password: 'Heslo',\n  formFieldLabel__phoneNumber: 'Telefónne číslo',\n  formFieldLabel__role: 'Rola',\n  formFieldLabel__signOutOfOtherSessions: 'Odhlásiť sa zo všetkých ostatných zariadení',\n  formFieldLabel__username: 'Užívateľské meno',\n  impersonationFab: {\n    action__signOut: 'Odhlásiť sa',\n    title: 'Prihlásený(á) ako {{identifier}}',\n  },\n  maintenanceMode: undefined,\n  membershipRole__admin: 'Správca',\n  membershipRole__basicMember: 'Člen',\n  membershipRole__guestMember: 'Host',\n  organizationList: {\n    action__createOrganization: 'Create organization',\n    action__invitationAccept: 'Join',\n    action__suggestionsAccept: 'Request to join',\n    createOrganization: 'Create Organization',\n    invitationAcceptedLabel: 'Joined',\n    subtitle: 'to continue to {{applicationName}}',\n    suggestionsAcceptedLabel: 'Pending approval',\n    title: 'Choose an account',\n    titleWithoutPersonal: 'Choose an organization',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Automatic invitations',\n    badge__automaticSuggestion: 'Automatic suggestions',\n    badge__manualInvitation: 'No automatic enrollment',\n    badge__unverified: 'Unverified',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Add the domain to verify. Users with email addresses at this domain can join the organization automatically or request to join.',\n      title: 'Add domain',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed: 'Pozvánky sa nepodarilo odoslať. Opravte nasledujúce a skúste to znovu:',\n      formButtonPrimary__continue: 'Odoslať pozvánky',\n      selectDropdown__role: 'Select role',\n      subtitle: 'Pozvať nových členov do tejto organizácie',\n      successMessage: 'Pozvánky boli úspešne odoslané.',\n      title: 'Pozvať členov',\n    },\n    membersPage: {\n      action__invite: 'Pozvať',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Odstrániť člena',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'Pripojil sa',\n        tableHeader__role: 'Rola',\n        tableHeader__user: 'Užívateľ',\n      },\n      detailsTitle__emptyRow: 'Žiadni členovia na zobrazenie',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Invite users by connecting an email domain with your organization. Anyone who signs up with a matching email domain will be able to join the organization anytime.',\n          headerTitle: 'Automatic invitations',\n          primaryButton: 'Manage verified domains',\n        },\n        table__emptyRow: 'No invitations to display',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Zrušiť pozvanie',\n        tableHeader__invited: 'Pozvaní',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Users who sign up with a matching email domain, will be able to see a suggestion to request to join your organization.',\n          headerTitle: 'Automatic suggestions',\n          primaryButton: 'Manage verified domains',\n        },\n        menuAction__approve: 'Approve',\n        menuAction__reject: 'Reject',\n        tableHeader__requested: 'Requested access',\n        table__emptyRow: 'No requests to display',\n      },\n      start: {\n        headerTitle__invitations: 'Invitations',\n        headerTitle__members: 'Members',\n        headerTitle__requests: 'Requests',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Manage your organization.',\n      general: 'General',\n      members: 'Members',\n      title: 'Organization',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Type \"{{organizationName}}\" below to continue.',\n          messageLine1: 'Are you sure you want to delete this organization?',\n          messageLine2: 'This action is permanent and irreversible.',\n          successMessage: 'You have deleted the organization.',\n          title: 'Delete organization',\n        },\n        leaveOrganization: {\n          actionDescription: 'Type \"{{organizationName}}\" below to continue.',\n          messageLine1:\n            'Naozaj chcete opustiť túto organizáciu? Stratíte prístup k tejto organizácii a jej aplikáciám.',\n          messageLine2: 'Táto akcia je trvalá a nezvratná.',\n          successMessage: 'Opustili ste organizáciu.',\n          title: 'Opustiť organizáciu',\n        },\n        title: 'Upozornenie',\n      },\n      domainSection: {\n        menuAction__manage: 'Manage',\n        menuAction__remove: 'Delete',\n        menuAction__verify: 'Verify',\n        primaryButton: 'Add domain',\n        subtitle:\n          'Allow users to join the organization automatically or request to join based on a verified email domain.',\n        title: 'Verified domains',\n      },\n      successMessage: 'Organizácia bola aktualizovaná.',\n      title: 'Profil organizácie',\n    },\n    removeDomainPage: {\n      messageLine1: 'The email domain {{domain}} will be removed.',\n      messageLine2: 'Users won’t be able to join the organization automatically after this.',\n      successMessage: '{{domain}} has been removed.',\n      title: 'Remove domain',\n    },\n    start: {\n      headerTitle__general: 'General',\n      headerTitle__members: 'Členovia',\n      profileSection: {\n        primaryButton: undefined,\n        title: 'Organization Profile',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Removing this domain will affect invited users.',\n        removeDomainActionLabel__remove: 'Remove domain',\n        removeDomainSubtitle: 'Remove this domain from your verified domains',\n        removeDomainTitle: 'Remove domain',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Users are automatically invited to join the organization when they sign-up and can join anytime.',\n        automaticInvitationOption__label: 'Automatic invitations',\n        automaticSuggestionOption__description:\n          'Users receive a suggestion to request to join, but must be approved by an admin before they are able to join the organization.',\n        automaticSuggestionOption__label: 'Automatic suggestions',\n        calloutInfoLabel: 'Changing the enrollment mode will only affect new users.',\n        calloutInvitationCountLabel: 'Pending invitations sent to users: {{count}}',\n        calloutSuggestionCountLabel: 'Pending suggestions sent to users: {{count}}',\n        manualInvitationOption__description: 'Users can only be invited manually to the organization.',\n        manualInvitationOption__label: 'No automatic enrollment',\n        subtitle: 'Choose how users from this domain can join the organization.',\n      },\n      start: {\n        headerTitle__danger: 'Danger',\n        headerTitle__enrollment: 'Enrollment options',\n      },\n      subtitle: 'The domain {{domain}} is now verified. Continue by selecting enrollment mode.',\n      title: 'Update {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Enter the verification code sent to your email address',\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'The domain {{domainName}} needs to be verified via email.',\n      subtitleVerificationCodeScreen: 'A verification code was sent to {{emailAddress}}. Enter the code to continue.',\n      title: 'Verify domain',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Vytvoriť organizáciu',\n    action__invitationAccept: 'Join',\n    action__manageOrganization: 'Spravovať organizáciu',\n    action__suggestionsAccept: 'Request to join',\n    notSelected: 'Nie je vybraná žiadna organizácia',\n    personalWorkspace: 'Osobný pracovný priestor',\n    suggestionsAcceptedLabel: 'Pending approval',\n  },\n  paginationButton__next: 'Ďalšie',\n  paginationButton__previous: 'Predchádzajúce',\n  paginationRowText__displaying: 'Zobrazuje sa',\n  paginationRowText__of: 'z',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Add account',\n      action__signOutAll: 'Sign out of all accounts',\n      subtitle: 'Select the account with which you wish to continue.',\n      title: 'Choose an account',\n    },\n    alternativeMethods: {\n      actionLink: 'Získať pomoc',\n      actionText: 'Don’t have any of these?',\n      blockButton__backupCode: 'Použiť záložný kód',\n      blockButton__emailCode: 'Odoslať overovací kód na email {{identifier}}',\n      blockButton__emailLink: 'Odoslať odkaz na email {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Prihlásiť sa pomocou hesla',\n      blockButton__phoneCode: 'Poslať SMS kód na telefónne číslo {{identifier}}',\n      blockButton__totp: 'Použiť autentifikačnú aplikáciu',\n      getHelp: {\n        blockButton__emailSupport: 'Podpora cez email',\n        content:\n          'Ak máte problémy s prihlásením do svojho účtu, kontaktujte nás emailom a pokúsime sa vám čo najskôr obnoviť prístup.',\n        title: 'Získať pomoc',\n      },\n      subtitle: 'Facing issues? You can use any of these methods to sign in.',\n      title: 'Použiť inú metódu',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'pre pokračovanie do {{applicationName}}',\n      title: 'Zadajte záložný kód',\n    },\n    emailCode: {\n      formTitle: 'Overovací kód',\n      resendButton: 'Znovu poslať kód',\n      subtitle: 'pre pokračovanie do {{applicationName}}',\n      title: 'Skontrolujte svoj email',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: 'Vráťte sa do pôvodného okna pre pokračovanie.',\n        title: 'Tento overovací odkaz vypršal',\n      },\n      failed: {\n        subtitle: 'Vráťte sa do pôvodného okna pre pokračovanie.',\n        title: 'Tento overovací odkaz je neplatný',\n      },\n      formSubtitle: 'Použite overovací odkaz zaslaný na váš email',\n      formTitle: 'Overovací odkaz',\n      loading: {\n        subtitle: 'Čoskoro budete presmerovaní',\n        title: 'Prihlasujem...',\n      },\n      resendButton: 'Znovu poslať odkaz',\n      subtitle: 'pre pokračovanie do {{applicationName}}',\n      title: 'Skontrolujte svoj email',\n      unusedTab: {\n        title: 'Môžete zatvoriť toto okno',\n      },\n      verified: {\n        subtitle: 'Čoskoro budete presmerovaní',\n        title: 'Úspešne prihlásené',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Vráťte sa do pôvodného okna pre pokračovanie',\n        subtitleNewTab: 'Vráťte sa do novootvoreného okna pre pokračovanie',\n        titleNewTab: 'Prihlásené v inom okne',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Overovací kód pre obnovenie hesla',\n      resendButton: 'Znovu poslať kód',\n      subtitle: 'to reset your password',\n      subtitle_email: 'First, enter the code sent to your email ID',\n      subtitle_phone: 'First, enter the code sent to your phone',\n      title: 'Reset password',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Obnoviť heslo',\n      label__alternativeMethods: 'Alebo sa prihláste pomocou inej metódy.',\n      title: 'Zabudli ste heslo?',\n    },\n    noAvailableMethods: {\n      message: 'Nemožno pokračovať v prihlásení. Nie je k dispozícii žiadna dostupná autentifikačná metóda.',\n      subtitle: 'Došlo k chybe',\n      title: 'Nie je možné sa prihlásiť',\n    },\n    passkey: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Použiť inú metódu',\n      subtitle: 'pre pokračovanie do {{applicationName}}',\n      title: 'Zadajte svoje heslo',\n    },\n    passwordPwned: {\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: 'Overovací kód',\n      resendButton: 'Znova odoslať kód',\n      subtitle: 'pre pokračovanie do {{applicationName}}',\n      title: 'Skontrolujte váš telefón',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Overovací kód',\n      resendButton: 'Znova odoslať kód',\n      subtitle: undefined,\n      title: 'Skontrolujte váš telefón',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Obnoviť heslo',\n      requiredMessage: 'For security reasons, it is required to reset your password.',\n      successMessage: 'Vaše heslo bolo úspešne zmenené. Prihlasujem vás, prosím počkajte okamžite.',\n      title: 'Obnoviť heslo',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Pred obnovením hesla je potrebné overiť vašu totožnosť.',\n    },\n    start: {\n      actionLink: 'Registrovať sa',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Použiť email',\n      actionLink__use_email_username: 'Použiť email alebo užívateľské meno',\n      actionLink__use_passkey: undefined,\n      actionLink__use_phone: 'Použiť telefón',\n      actionLink__use_username: 'Použiť užívateľské meno',\n      actionText: 'Nemáte účet?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'pre pokračovanie do {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Prihlásiť sa',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Overovací kód',\n      subtitle: undefined,\n      title: 'Dvojfaktorové overenie',\n    },\n  },\n  signInEnterPasswordTitle: 'Zadajte svoje heslo',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Prihlásiť sa',\n      actionText: 'Máte účet?',\n      subtitle: 'pre pokračovanie do {{applicationName}}',\n      title: 'Vyplňte chýbajúce polia',\n    },\n    emailCode: {\n      formSubtitle: 'Zadajte overovací kód poslaný na vašu emailovú adresu',\n      formTitle: 'Overovací kód',\n      resendButton: 'Znovu poslať kód',\n      subtitle: 'pre pokračovanie do {{applicationName}}',\n      title: 'Overte svoj email',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: 'Použite overovací odkaz poslaný na vašu emailovú adresu',\n      formTitle: 'Overovací odkaz',\n      loading: {\n        title: 'Prebieha registrácia...',\n      },\n      resendButton: 'Znovu poslať odkaz',\n      subtitle: 'pre pokračovanie do {{applicationName}}',\n      title: 'Overte svoj email',\n      verified: {\n        title: 'Úspešne zaregistrované',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Vráťte sa do novootvoreného okna pre pokračovanie',\n        subtitleNewTab: 'Vráťte sa do predchádzajúceho okna pre pokračovanie',\n        title: 'Email úspešne overený',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Zadajte overovací kód poslaný na vaše telefónne číslo',\n      formTitle: 'Overovací kód',\n      resendButton: 'Znovu poslať kód',\n      subtitle: 'pre pokračovanie do {{applicationName}}',\n      title: 'Overte svoj telefón',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Prihlásiť sa',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: 'Máte účet?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'pre pokračovanie do {{applicationName}}',\n      subtitleCombined: 'pre pokračovanie do {{applicationName}}',\n      title: 'Vytvorte si účet',\n      titleCombined: 'Vytvorte si účet',\n    },\n  },\n  socialButtonsBlockButton: 'Pokračovať s {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'Sign up unsuccessful due to failed security validations. Please refresh the page to try again or reach out to support for more assistance.',\n    captcha_unavailable:\n      'Sign up unsuccessful due to failed bot validation. Please refresh the page to try again or reach out to support for more assistance.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: 'Nie je možné nájsť účet s tými istými údajmi.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'Email address must be a valid email address.',\n    form_param_format_invalid__phone_number: 'Phone number must be in a valid international format',\n    form_param_max_length_exceeded__first_name: 'First name should not exceed 256 characters.',\n    form_param_max_length_exceeded__last_name: 'Last name should not exceed 256 characters.',\n    form_param_max_length_exceeded__name: 'Name should not exceed 256 characters.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: 'Vaše heslo nie je dostatočne silné.',\n    form_password_pwned: 'Toto heslo bolo nájdené v rámci úniku dát a nemôže byť použité, prosím zvoľte iné heslo.',\n    form_password_pwned__sign_in: undefined,\n    form_password_size_in_bytes_exceeded:\n      'Vaše heslo prekročilo maximálny povolený počet bytov, prosím skráťte ho alebo odstráňte niektoré špeciálne znaky.',\n    form_password_validation_failed: 'Nesprávne heslo',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: 'You cannot delete your last identification.',\n    not_allowed_access:\n      \"Adresa e-mailu alebo telefónneho čísla nie je povolená pre registráciu. Toto môže byť spôsobené použitím '+', '=', '#' alebo '.' v adresári e-mailu, použitím domény, ktorá je pripojená k dočasnej e-mailovej službe, alebo explicitnému vylúčeniu.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: 'menej ako {{length}} znakov',\n      minimumLength: '{{length}} alebo viac znakov',\n      requireLowercase: 'malé písmeno',\n      requireNumbers: 'číslicu',\n      requireSpecialCharacter: 'špeciálny znak',\n      requireUppercase: 'veľké písmeno',\n      sentencePrefix: 'Vaše heslo musí obsahovať',\n    },\n    phone_number_exists: 'This phone number is taken. Please try another.',\n    session_exists: 'Jste už přihlášen.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Vaše heslo funguje, ale mohlo by byť silnejšie. Skúste pridať viac znakov.',\n      goodPassword: 'Dobrá práca. Toto je vynikajúce heslo.',\n      notEnough: 'Vaše heslo není dostatočne silné.',\n      suggestions: {\n        allUppercase: 'Použite veľké písmena len u niektorých, nie všetkých písmen.',\n        anotherWord: 'Pridajte viac slov, ktoré nie sú tak bežné.',\n        associatedYears: 'Vyhnite sa rokom, ktoré sú s vami spojené.',\n        capitalization: 'Písmená píšte s veľkým počiatočným písmenom a viac ako len prvým písmenom.',\n        dates: 'Vyhnite sa dátumom a rokom, ktoré sú s vami spojené.',\n        l33t: \"Vyhnite sa predvídateľným náhradám písmen, napríklad '@' miesto 'a'.\",\n        longerKeyboardPattern: 'Použite dlhšie vzory na klávesnici a menite smer písania viackrát.',\n        noNeed: 'Môžete vytvárať silné heslá aj bez použitia symbolov, čísel alebo veľkých písmen.',\n        pwned: 'Ak používate toto heslo aj niekde inde, mali by ste ho zmeniť.',\n        recentYears: 'Vyhnite sa nedávnym rokom.',\n        repeated: 'Vyhnite sa opakujúcim sa slovám a znakom.',\n        reverseWords: 'Vyhnite sa obráteným pravopisom bežných slov.',\n        sequences: 'Vyhnite sa bežným sekvenciám znakov.',\n        useWords: 'Použite viac slov, ale vyhnite sa bežným frázam.',\n      },\n      warnings: {\n        common: 'Toto je bežne používané heslo.',\n        commonNames: 'Bežné mená a priezviská sú ľahko uhádnuteľné.',\n        dates: 'Dátum je ľahko uhádnuteľný.',\n        extendedRepeat: 'Opakujúce sa vzory znakov ako \"abcabcabc\" sú ľahko uhádnuteľné.',\n        keyPattern: 'Krátke vzory na klávesnici sú ľahko uhádnuteľné.',\n        namesByThemselves: 'Samostatné mená alebo priezviská sú ľahko uhádnuteľné.',\n        pwned: 'Vaše heslo bolo odhalené pri úniku údajov na internete.',\n        recentYears: 'Nedávne roky sú ľahko uhádnuteľné.',\n        sequences: 'Bežné sekvencie znakov ako \"abc\" sú ľahko uhádnuteľné.',\n        similarToCommon: 'Toto je podobné bežne používanému heslu.',\n        simpleRepeat: 'Opakujúce sa znaky ako \"aaa\" sú ľahko uhádnuteľné.',\n        straightRow: 'Rady klávesnice sú ľahko uhádnuteľné.',\n        topHundred: 'Toto je často používané heslo.',\n        topTen: 'Toto je často používané heslo.',\n        userInputs: 'Heslo by nemalo obsahovať osobné alebo stránkou súvisiace údaje.',\n        wordByItself: 'Samostatné slová sú ľahko uhádnuteľné.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Pridať účet',\n    action__manageAccount: 'Spravovať účet',\n    action__signOut: 'Odhlásiť sa',\n    action__signOutAll: 'Odhlásiť sa zo všetkých účtov',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Skopírované!',\n      actionLabel__copy: 'Kopírovať všetko',\n      actionLabel__download: 'Stiahnuť .txt',\n      actionLabel__print: 'Vytlačiť',\n      infoText1: 'Pre tento účet budú povolené záložné kódy.',\n      infoText2:\n        'Záložné kódy uchovávajte tajne a bezpečne. Môžete vygenerovať nové záložné kódy, ak máte podozrenie, že boli skompromitované.',\n      subtitle__codelist: 'Uchovávajte ich bezpečne a tajne.',\n      successMessage:\n        'Záložné kódy sú teraz povolené. Ak stratíte prístup k vášmu overovaciemu zariadeniu, môžete použiť jeden z týchto kódov na prihlásenie do vášho účtu. Každý kód možno použiť iba raz.',\n      successSubtitle:\n        'Použite jeden z týchto kódov na prihlásenie do vášho účtu, ak stratíte prístup k vášmu overovaciemu zariadeniu.',\n      title: 'Pridať overovanie pomocou záložných kódov',\n      title__codelist: 'Záložné kódy',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Vyberte poskytovateľa pre pripojenie vášho účtu.',\n      formHint__noAccounts: 'Nie sú k dispozícii žiadni dostupní externí poskytovatelia účtov.',\n      removeResource: {\n        messageLine1: '{{identifier}} bude odobraný z tohto účtu.',\n        messageLine2: 'Nebudete už môcť používať tento pripojený účet a akékoľvek závislé funkcie prestanú fungovať.',\n        successMessage: '{{connectedAccount}} bol odstránený z vášho účtu.',\n        title: 'Odstrániť pripojený účet',\n      },\n      socialButtonsBlockButton: 'Pripojiť účet {{provider|titleize}}',\n      successMessage: 'Poskytovateľ bol pridaný k vášmu účtu.',\n      title: 'Pridať pripojený účet',\n    },\n    deletePage: {\n      actionDescription: 'Type \"Delete account\" below to continue.',\n      confirm: 'Delete account',\n      messageLine1: 'Are you sure you want to delete your account?',\n      messageLine2: 'This action is permanent and irreversible.',\n      title: 'Delete account',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Na túto e-mailovú adresu bude odoslaný overovací kód.',\n        formSubtitle: 'Zadajte overovací kód zaslaný na adresu {{identifier}}',\n        formTitle: 'Overovací kód',\n        resendButton: 'Znovu odoslať kód',\n        successMessage: 'E-mailová adresa {{identifier}} bola pridaná k vášmu účtu.',\n      },\n      emailLink: {\n        formHint: 'Na túto e-mailovú adresu bude odoslaný overovací odkaz.',\n        formSubtitle: 'Kliknite na overovací odkaz v e-maile zaslanom na adresu {{identifier}}',\n        formTitle: 'Overovací odkaz',\n        resendButton: 'Znovu odoslať odkaz',\n        successMessage: 'E-mailová adresa {{identifier}} bola pridaná k vášmu účtu.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} bude odstránená z tohto účtu.',\n        messageLine2: 'Nebudete sa môcť prihlásiť pomocou tejto e-mailovej adresy.',\n        successMessage: '{{emailAddress}} bola odobraná z vášho účtu.',\n        title: 'Odstrániť e-mailovú adresu',\n      },\n      title: 'Pridať e-mailovú adresu',\n      verifyTitle: 'Verify email address',\n    },\n    formButtonPrimary__add: 'Add',\n    formButtonPrimary__continue: 'Pokračovať',\n    formButtonPrimary__finish: 'Dokončiť',\n    formButtonPrimary__remove: 'Remove',\n    formButtonPrimary__save: 'Save',\n    formButtonReset: 'Zrušiť',\n    mfaPage: {\n      formHint: 'Vyberte spôsob pridania.',\n      title: 'Pridať dvojfaktorové overenie',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Use existing number',\n      primaryButton__addPhoneNumber: 'Pridať telefónne číslo',\n      removeResource: {\n        messageLine1: '{{identifier}} už nebude dostávať overovacie kódy pri prihlasovaní.',\n        messageLine2: 'Váš účet nemusí byť tak bezpečný. Naozaj chcete pokračovať?',\n        successMessage: 'Dvojfaktorové overovanie pomocou SMS kódu bolo odstránené pre {{mfaPhoneCode}}',\n        title: 'Odstrániť dvojfaktorové overovanie',\n      },\n      subtitle__availablePhoneNumbers:\n        'Vyberte telefónne číslo pre registráciu dvojfaktorového overovania pomocou SMS kódu.',\n      subtitle__unavailablePhoneNumbers:\n        'Nie sú k dispozícii žiadne dostupné telefónne čísla pre registráciu dvojfaktorového overovania pomocou SMS kódu.',\n      successMessage1:\n        'When signing in, you will need to enter a verification code sent to this phone number as an additional step.',\n      successMessage2:\n        'Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.',\n      successTitle: 'SMS code verification enabled',\n      title: 'Pridať overovanie pomocou SMS kódu',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Namiesto toho naskenujte QR kód',\n        buttonUnableToScan__nonPrimary: 'Nemôžete naskenovať QR kód?',\n        infoText__ableToScan:\n          'Nastavte novú metódu prihlásenia vo vašej aplikácii pre overovanie a naskenujte nasledujúci QR kód, aby ste ju spojili so svojím účtom.',\n        infoText__unableToScan:\n          'Nastavte novú metódu prihlásenia vo vašej aplikácii pre overovanie a zadajte nižšie uvedený kľúč.',\n        inputLabel__unableToScan1:\n          'Uistite sa, že je povolené časovo závislé alebo jednorázové heslo a dokončite spojenie vášho účtu.',\n        inputLabel__unableToScan2:\n          'Alternatívne, ak vaša aplikácia pre overovanie podporuje TOTP URI, môžete tiež skopírovať celý URI.',\n      },\n      removeResource: {\n        messageLine1: 'Pri prihlasovaní už nebudú vyžadované overovacie kódy z tejto aplikácie pre overovanie.',\n        messageLine2: 'Váš účet nemusí byť tak bezpečný. Naozaj chcete pokračovať?',\n        successMessage: 'Dvojfaktorové overovanie pomocou aplikácie pre overovanie bolo odstránené.',\n        title: 'Odstrániť dvojfaktorové overovanie',\n      },\n      successMessage:\n        'Dvojfaktorové overovanie je teraz povolené. Pri prihlásení budete musieť zadať overovací kód z tejto aplikácie pre overovanie ako ďalší krok.',\n      title: 'Pridať aplikáciu pre overovanie',\n      verifySubtitle: 'Zadajte overovací kód generovaný vašou aplikáciou pre overovanie.',\n      verifyTitle: 'Overovací kód',\n    },\n    mobileButton__menu: 'Menu',\n    navbar: {\n      account: 'Profile',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Manage your account info.',\n      security: 'Security',\n      title: 'Account',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: undefined,\n        title: undefined,\n      },\n      subtitle__rename: undefined,\n      title__rename: undefined,\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'It is recommended to sign out of all other devices which may have used your old password.',\n      readonly: 'Your password can currently not be edited because you can sign in only via the enterprise connection.',\n      successMessage__set: 'Vaše heslo bolo nastavené.',\n      successMessage__signOutOfOtherSessions: 'Všetky ostatné zariadenia boli odhlásené.',\n      successMessage__update: 'Vaše heslo bolo aktualizované.',\n      title__set: 'Nastaviť heslo',\n      title__update: 'Zmeniť heslo',\n    },\n    phoneNumberPage: {\n      infoText: 'Na toto telefónne číslo bude odoslaná textová správa obsahujúca overovací odkaz.',\n      removeResource: {\n        messageLine1: '{{identifier}} bude odobrané z tohto účtu.',\n        messageLine2: 'Nebudete sa môcť prihlásiť pomocou tohto telefónneho čísla.',\n        successMessage: '{{phoneNumber}} bolo odstránené z vášho účtu.',\n        title: 'Odstrániť telefónne číslo',\n      },\n      successMessage: '{{identifier}} bolo pridané k vášmu účtu.',\n      title: 'Pridať telefónne číslo',\n      verifySubtitle: 'Enter the verification code sent to {{identifier}}',\n      verifyTitle: 'Verify phone number',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Nahrajte obrázok vo formátoch JPG, PNG, GIF alebo WEBP s veľkosťou menšou než 10 MB',\n      imageFormDestructiveActionSubtitle: 'Odstrániť obrázok',\n      imageFormSubtitle: 'Nahrať obrázok',\n      imageFormTitle: 'Profilový obrázok',\n      readonly: 'Your profile information has been provided by the enterprise connection and cannot be edited.',\n      successMessage: 'Váš profil bol aktualizovaný.',\n      title: 'Aktualizovať profil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Odhlásiť sa zo zariadenia',\n        title: 'Aktívne zariadenia',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Skúsiť znovu',\n        actionLabel__reauthorize: 'Autorizovať teraz',\n        destructiveActionTitle: 'Odstrániť',\n        primaryButton: 'Pripojiť účet',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues',\n        title: 'Pripojené účty',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Delete Account',\n        title: 'Account termination',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Odstrániť emailovú adresu',\n        detailsAction__nonPrimary: 'Nastaviť ako hlavnú',\n        detailsAction__primary: 'Dokončiť overenie',\n        detailsAction__unverified: 'Dokončiť overenie',\n        primaryButton: 'Pridať emailovú adresu',\n        title: 'Emailové adresy',\n      },\n      enterpriseAccountsSection: {\n        title: 'Enterprise accounts',\n      },\n      headerTitle__account: 'Účet',\n      headerTitle__security: 'Bezpečnosť',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Obnoviť kódy',\n          headerTitle: 'Záložné kódy',\n          subtitle__regenerate:\n            'Získajte novú sadu zabezpečených záložných kódov. Predchádzajúce záložné kódy budú zmazané a nebudú použiteľné.',\n          title__regenerate: 'Obnoviť záložné kódy',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Nastaviť ako predvolené',\n          destructiveActionLabel: 'Odstrániť telefónne číslo',\n        },\n        primaryButton: 'Pridať dvojfaktorovú autentifikáciu',\n        title: 'Dvojfaktorová autentifikácia',\n        totp: {\n          destructiveActionTitle: 'Odstrániť',\n          headerTitle: 'Aplikácia Authenticator',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: undefined,\n        menuAction__rename: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Nastaviť heslo',\n        primaryButton__updatePassword: 'Zmeniť heslo',\n        title: 'Heslo',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Odstrániť telefónne číslo',\n        detailsAction__nonPrimary: 'Nastaviť ako hlavné',\n        detailsAction__primary: 'Dokončiť overenie',\n        detailsAction__unverified: 'Dokončiť overenie',\n        primaryButton: 'Pridať telefónne číslo',\n        title: 'Telefónne čísla',\n      },\n      profileSection: {\n        primaryButton: undefined,\n        title: 'Profil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Nastaviť užívateľské meno',\n        primaryButton__updateUsername: 'Zmeniť užívateľské meno',\n        title: 'Užívateľské meno',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Odstrániť peňaženku',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 peňaženky',\n        title: 'Web3 peňaženky',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Vaše užívateľské meno bolo aktualizované.',\n      title__set: 'Aktualizovať užívateľské meno',\n      title__update: 'Aktualizovať užívateľské meno',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} bude odobraná z tohto účtu.',\n        messageLine2: 'Nebudete sa už môcť prihlásiť pomocou tejto web3 peňaženky.',\n        successMessage: '{{web3Wallet}} bola odstránená z vášho účtu.',\n        title: 'Odstrániť web3 peňaženku',\n      },\n      subtitle__availableWallets: 'Vyberte web3 peňaženku na pripojenie k vášmu účtu.',\n      subtitle__unavailableWallets: 'Nie sú k dispozícii žiadne dostupné web3 peňaženky.',\n      successMessage: 'Peňaženka bola pridaná k vášmu účtu.',\n      title: 'Pridať web3 peňaženku',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}