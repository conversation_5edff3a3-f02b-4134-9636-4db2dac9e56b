{"version": 3, "sources": ["../src/ru-RU.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const ruRU: LocalizationResource = {\n  locale: 'ru-RU',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Назад',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'По-умолчанию',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Другое устройство',\n  badge__primary: 'Основной',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Требуется действие',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Это устройство',\n  badge__unverified: 'Неверифицированный',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Пользовательское устройство',\n  badge__you: 'Вы',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Создать организацию',\n    invitePage: {\n      formButtonReset: 'Пропустить',\n    },\n    title: 'Создать организацию',\n  },\n  dates: {\n    lastDay: \"Вчера в {{ date | timeString('ru-RU') }}\",\n    next6Days: \"{{ date | weekday('ru-RU','long') }} в {{ date | timeString('ru-RU') }}\",\n    nextDay: \"Завтра в {{ date | timeString('ru-RU') }}\",\n    numeric: \"{{ date | numeric('ru-RU') }}\",\n    previous6Days: \"Последний {{ date | weekday('ru-RU','long') }} в {{ date | timeString('ru-RU') }}\",\n    sameDay: \"Сегодня в {{ date | timeString('ru-RU') }}\",\n  },\n  dividerText: 'или',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Использовать другой метод',\n  footerPageLink__help: 'Помощь',\n  footerPageLink__privacy: 'Приватность',\n  footerPageLink__terms: 'Условия',\n  formButtonPrimary: 'Продолжить',\n  formButtonPrimary__verify: 'Верификация',\n  formFieldAction__forgotPassword: 'Забыли пароль?',\n  formFieldError__matchingPasswords: 'Пароли совпадают.',\n  formFieldError__notMatchingPasswords: 'Пароли не совпадают.',\n  formFieldError__verificationLinkExpired:\n    'Срок действия ссылки верификации истек. Пожалуйста, запросити новую ссылку.',\n  formFieldHintText__optional: 'Опционально',\n  formFieldHintText__slug:\n    'Slug - это человекочитаемый идентификатор, который должен быть уникальным. Он часто используется в URL-адресах.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Удалить учетную запись',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses:\n    'Введите или вставьте один или более адресов почты, разделенных пробелами или запятыми',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: 'my-org',\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Enable automatic invitations for this domain',\n  formFieldLabel__backupCode: 'Код восстановления',\n  formFieldLabel__confirmDeletion: 'Подтверждение',\n  formFieldLabel__confirmPassword: 'Подтверждение пароля',\n  formFieldLabel__currentPassword: 'Текущий пароль',\n  formFieldLabel__emailAddress: 'Почта',\n  formFieldLabel__emailAddress_username: 'Почта или имя пользователя',\n  formFieldLabel__emailAddresses: 'Почтовые адреса',\n  formFieldLabel__firstName: 'Имя',\n  formFieldLabel__lastName: 'Фамилия',\n  formFieldLabel__newPassword: 'Новый пароль',\n  formFieldLabel__organizationDomain: 'Домен',\n  formFieldLabel__organizationDomainDeletePending: 'Удалить ожидающие приглашения и предложения',\n  formFieldLabel__organizationDomainEmailAddress: 'Верифицировать адрес электронной почты',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Введите адрес электронной почты с этим доменом, чтобы получить код и подтвердить домен.',\n  formFieldLabel__organizationName: 'Название организации',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Название ключа доступа',\n  formFieldLabel__password: 'Пароль',\n  formFieldLabel__phoneNumber: 'Номер телефона',\n  formFieldLabel__role: 'Роль',\n  formFieldLabel__signOutOfOtherSessions: 'Выйти со всех других устройств',\n  formFieldLabel__username: 'Имя пользователя',\n  impersonationFab: {\n    action__signOut: 'Выйти',\n    title: 'Вы вошли как {{identifier}}',\n  },\n  maintenanceMode:\n    'В данный момент мы проводим техническое обслуживание, но не беспокойтесь, это не займет больше нескольких минут.',\n  membershipRole__admin: 'Администратор',\n  membershipRole__basicMember: 'Участник',\n  membershipRole__guestMember: 'Гость',\n  organizationList: {\n    action__createOrganization: 'Создать организацию',\n    action__invitationAccept: 'Присоединиться',\n    action__suggestionsAccept: 'Запрос на присоединение',\n    createOrganization: 'Создать Организацию',\n    invitationAcceptedLabel: 'Присоединился',\n    subtitle: 'для продолжения в {{applicationName}}',\n    suggestionsAcceptedLabel: 'Ожидает одобрения',\n    title: 'Выбрать учетную запись',\n    titleWithoutPersonal: 'Выбрать организацию',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Автоматические приглашения',\n    badge__automaticSuggestion: 'Автоматические предложения',\n    badge__manualInvitation: 'Нет автоматической регистрации',\n    badge__unverified: 'Неверифицированный',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Добавьте верифицированный домен. Пользователи, чья электронная почта зарегистрирована на верифицированном домене, могут присоединяться к организации автоматически или по запросу.',\n      title: 'Добавить домен',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Приглашения не могли быть отправлены. Уже есть ожидающие приглашения для следующих адресов электронной почты: {{email_addresses}}.',\n      formButtonPrimary__continue: 'Отправить приглашения',\n      selectDropdown__role: 'Выбрать роль',\n      subtitle: 'Пригласите новых участников в эту организацию',\n      successMessage: 'Приглашения успешно отправлены',\n      title: 'Пригласить участников',\n    },\n    membersPage: {\n      action__invite: 'Пригласить',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Удалить участника',\n        tableHeader__actions: 'Действия',\n        tableHeader__joined: 'Присоединился',\n        tableHeader__role: 'Роль',\n        tableHeader__user: 'Пользователь',\n      },\n      detailsTitle__emptyRow: 'Нет участников для отображения',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Пригласите пользователей, подключив домен электронной почты к вашей организации. Любой, кто зарегистрируется с соответствующим доменом электронной почты, сможет присоединиться к организации в любое время.',\n          headerTitle: 'Автоматические приглашения',\n          primaryButton: 'Управлять верифицированными доменами',\n        },\n        table__emptyRow: 'Нет приглашений для отображения',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Отозвать приглашение',\n        tableHeader__invited: 'Приглашенные',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Пользователи, которые регистрируются с соответствующим доменом электронной почты, смогут увидеть предложение запроса на присоединение к вашей организации',\n          headerTitle: 'Автоматические предложения',\n          primaryButton: 'Управлять верифицированными доменами',\n        },\n        menuAction__approve: 'Одобрить',\n        menuAction__reject: 'Отклонить',\n        tableHeader__requested: 'Запрашиваемый доступ',\n        table__emptyRow: 'Нет запросов для отображения',\n      },\n      start: {\n        headerTitle__invitations: 'Приглашения',\n        headerTitle__members: 'Участники',\n        headerTitle__requests: 'Заявки',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Управлять вашей организацией.',\n      general: 'Общее',\n      members: 'Участники',\n      title: 'Организация',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Напишите {{organizationName}} в поле ниже, чтобы продолжить.',\n          messageLine1: 'Вы уверены, что хотите удалить эту организацию?',\n          messageLine2: 'Это действие нельзя отменить.',\n          successMessage: 'Вы удалили организацию.',\n          title: 'Удалить организацию',\n        },\n        leaveOrganization: {\n          actionDescription: 'Введите \"{{organizationName}}\" в поле ниже, чтобы продолжить.',\n          messageLine1:\n            'Вы уверены, что хотите покинуть эту организацию? Вы потеряете доступ к этой организации и ее приложениям.',\n          messageLine2: 'Это действие нельзя отменить.',\n          successMessage: 'Вы покинули организацию.',\n          title: 'Покинуть организацию',\n        },\n        title: 'Опасность',\n      },\n      domainSection: {\n        menuAction__manage: 'Управлять',\n        menuAction__remove: 'Удалить',\n        menuAction__verify: 'Верифицировать',\n        primaryButton: 'Добавить домен',\n        subtitle:\n          'Разрешите пользователям присоединяться к организации автоматически или по запросу, если домен их электронной почты верифицирован.',\n        title: 'Верифицированные домены',\n      },\n      successMessage: 'Организация была обновлена.',\n      title: 'Профиль организации',\n    },\n    removeDomainPage: {\n      messageLine1: 'Домен электронной почты {{domain}} был удален.',\n      messageLine2: 'Пользователи не смогут автоматически присоединяться к организации после этого',\n      successMessage: '{{domain}} был удален.',\n      title: 'Удалить домен',\n    },\n    start: {\n      headerTitle__general: 'Общее',\n      headerTitle__members: 'Участники',\n      profileSection: {\n        primaryButton: 'Обновить профиль',\n        title: 'Профиль Организации',\n        uploadAction__title: 'Логотип',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Удаление домена повлияет на приглашенных пользователей.',\n        removeDomainActionLabel__remove: 'Удалить домен',\n        removeDomainSubtitle: 'Удалить этот домен из числа верифицированных',\n        removeDomainTitle: 'Удалить домен',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Пользователи автоматически получают приглашение присоединиться к организации после регистрации и могут принять его в любое время без одобрения администратора',\n        automaticInvitationOption__label: 'Автоматические приглашения',\n        automaticSuggestionOption__description:\n          'Пользователи автоматически получают приглашение подать заявку на присоединение к организации. Чтобы пользователь смог присоединиться к организации, администратор должен одобрить заявку.',\n        automaticSuggestionOption__label: 'Автоматические рекомендации',\n        calloutInfoLabel: 'Изменение способа присоединения повлияет только на новых пользователей.',\n        calloutInvitationCountLabel: 'Приглашений отправлено: {{count}}',\n        calloutSuggestionCountLabel: 'Рекомендаций отправлено: {{count}}',\n        manualInvitationOption__description:\n          'Пользователи не смогут присоединяться самостоятельно, их можно добавлять только вручную.',\n        manualInvitationOption__label: 'Только ручное добавление',\n        subtitle:\n          'Выберите, каким способом пользователи с этим доменом электронной почты будут присоединяться к организации.',\n      },\n      start: {\n        headerTitle__danger: 'Опасность',\n        headerTitle__enrollment: 'Способы присоединения',\n      },\n      subtitle:\n        'Домен {{domain}} верифицирован. Теперь выберите, как пользователи с этим доменом будут присоединяться к организации.',\n      title: 'Обновить {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Введите код подтверждения, отправленный на указанную почту',\n      formTitle: 'Код подтверждения',\n      resendButton: 'Не получили код? Отправить снова',\n      subtitle: 'Домен {{domainName}} должен быть верифицирован через электронную почту.',\n      subtitleVerificationCodeScreen:\n        'Верификационный код отправлен на почту {{emailAddress}}. Введите его, чтобы продолжить.',\n      title: 'Верификация домена',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Создать организацию',\n    action__invitationAccept: 'Присоединиться',\n    action__manageOrganization: 'Настройки',\n    action__suggestionsAccept: 'Запрос на присоединение',\n    notSelected: 'Организация не выбрана',\n    personalWorkspace: 'Личный профиль',\n    suggestionsAcceptedLabel: 'Ожидает одобрения',\n  },\n  paginationButton__next: 'Вперед',\n  paginationButton__previous: 'Назад',\n  paginationRowText__displaying: 'Отображение',\n  paginationRowText__of: 'из',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Получить помощь',\n      actionText: 'У вас нет ничего из этого?',\n      blockButton__backupCode: 'Использовать резервный код',\n      blockButton__emailCode: 'Отправить код на {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Продолжить с вашим паролем',\n      blockButton__phoneCode: 'Отправить SMS код на {{identifier}}',\n      blockButton__totp: 'Использовать приложение аутентификации',\n      getHelp: {\n        blockButton__emailSupport: 'Написать в поддержку',\n        content:\n          'Если у вас возникли проблемы с проверкой учетной записи, напишите нам, и мы вместе с вами восстановим доступ как можно скорее.',\n        title: 'Получить помощь',\n      },\n      subtitle: 'Столкнулись с проблемами? Вы можете использовать любой из этих методов для верификации',\n      title: 'Использовать другой метод',\n    },\n    backupCodeMfa: {\n      subtitle: 'Ваш резервный код - это код, который вы получили при настройке двухфакторной аутентификации.',\n      title: 'Введите резервный код',\n    },\n    emailCode: {\n      formTitle: 'Верификационный код',\n      resendButton: 'Не получили код? Отправить повторно',\n      subtitle: 'для продолжения {{applicationName}}',\n      title: 'Проверьте вашу почту',\n    },\n    noAvailableMethods: {\n      message: 'Невозможно продолжить верификацию. Нет доступного фактора аутентификации.',\n      subtitle: 'Произошла ошибка',\n      title: 'Невозможно подтвердить учетную запись',\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Используйте другой метод',\n      subtitle: 'Введите пароль, связанный с вашей учетной записью',\n      title: 'Введите ваш пароль',\n    },\n    phoneCode: {\n      formTitle: 'Верификационный код',\n      resendButton: 'Не получили код? Отправить повторно',\n      subtitle: 'для продолжения {{applicationName}}',\n      title: 'Проверьте ваш телефон',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Верификационный код',\n      resendButton: 'Не получили код? Отправить повторно',\n      subtitle: 'Для продолжения, введите верификационный код, отправленный на ваш телефон',\n      title: 'Проверьте ваш телефон',\n    },\n    totpMfa: {\n      formTitle: 'Верификационный код',\n      subtitle: 'Чтобы продолжить, введите верификационный код, сгенерированный вашим приложением-аутентификатором',\n      title: 'Двухфакторная верификация',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Добавить учетную запись',\n      action__signOutAll: 'Выйти из всех учетных записей',\n      subtitle: 'Выберите учетную запись, с которой вы хотите продолжить.',\n      title: 'Выбрать учетную запись',\n    },\n    alternativeMethods: {\n      actionLink: 'Помощь',\n      actionText: 'Ничего из представленного?',\n      blockButton__backupCode: 'Использовать резервный код.',\n      blockButton__emailCode: 'Отправить код на {{identifier}}',\n      blockButton__emailLink: 'Отправить ссылку на {{identifier}}',\n      blockButton__passkey: 'Войти с помощью вашего ключа доступа.',\n      blockButton__password: 'Войти с паролем',\n      blockButton__phoneCode: 'Отправить код на {{identifier}}',\n      blockButton__totp: 'Используйте аутентификатор',\n      getHelp: {\n        blockButton__emailSupport: 'Написать в поддержку',\n        content:\n          'Если вы испытваете сложности со входом в вашу учетную запись, напишите нам и мы постараемся восстаносить Ваш доступ в кратчайшие сроки.',\n        title: 'Помощь',\n      },\n      subtitle: 'Возникают проблемы? Вы можете использовать любой из этих методов для входа.',\n      title: 'Использовать другой метод',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'для продолжения работы в \"{{applicationName}}\"',\n      title: 'Введите резервный код',\n    },\n    emailCode: {\n      formTitle: 'Верификационный код',\n      resendButton: 'Не получили код? Отправить снова.',\n      subtitle: 'для продолжения работы в \"{{applicationName}}\"',\n      title: 'Проверьте Вашу почту',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Чтобы продолжить, откройте ссылку для проверки на устройстве и в браузере, с которых вы начали вход.',\n        title: 'Ссылка для проверки недействительна для этого устройства.',\n      },\n      expired: {\n        subtitle: 'Вернитесь на начальную вкладку, чтобы продолжить.',\n        title: 'Эта верификационная ссылка истекла',\n      },\n      failed: {\n        subtitle: 'Вернитесь на начальную вкладку, чтобы продолжить.',\n        title: 'Эта верификационная ссылка невалидная.',\n      },\n      formSubtitle: 'Используйте верификационную ссылку, отправленную на Вашу почту',\n      formTitle: 'Верификационная ссылка',\n      loading: {\n        subtitle: 'Вы скоро будете перенаправлены',\n        title: 'Входим...',\n      },\n      resendButton: 'Не получили ссылку? Отправить снова.',\n      subtitle: 'чтобы продолжить работу в \"{{applicationName}}\"',\n      title: 'Проверьте Вашу почту',\n      unusedTab: {\n        title: 'Вкладку можно закрыть',\n      },\n      verified: {\n        subtitle: 'Вы скоро будете перенаправлены',\n        title: 'Успешный вход',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Вернитесь на начальную вкладку, чтобы продолжить',\n        subtitleNewTab: 'Вернитесь на только что открытую вкладку, чтобы продолжить',\n        titleNewTab: 'Залогиньтесь на другой вкладке',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Код восстановления пароля',\n      resendButton: 'Отправить код еще раз',\n      subtitle: 'для сброса вашего пароля',\n      subtitle_email: 'Сначала введите код, отправленный на ваш адрес электронной почты.',\n      subtitle_phone: 'Сначала введите код, отправленный на ваш телефон.',\n      title: 'Сбросить пароль',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Восстановить пароль',\n      label__alternativeMethods: 'Или, войти другим способом',\n      title: 'Забыли пароль?',\n    },\n    noAvailableMethods: {\n      message: 'Невозможно войти. Нет доступных факторов аутентификации.',\n      subtitle: 'Произошла ошибка',\n      title: 'Невозможно войти',\n    },\n    passkey: {\n      subtitle:\n        'Использование вашего ключа доступа подтверждает вашу личность. Ваше устройство может запросить ваш отпечаток пальца, лицо или блокировку экрана.',\n      title: 'Используйте ваш ключ доступа',\n    },\n    password: {\n      actionLink: 'Использовать другой метод',\n      subtitle: 'чтобы продолжить работу в \"{{applicationName}}\"',\n      title: 'Введите пароль',\n    },\n    passwordPwned: {\n      title: 'Пароль скомпрометирован',\n    },\n    phoneCode: {\n      formTitle: 'Верификационный код',\n      resendButton: 'Не получили код? Отправить снова.',\n      subtitle: 'чтобы продолжить работу в \"{{applicationName}}\"',\n      title: 'Проверьте ваш телефон',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Верификационный код',\n      resendButton: 'Не получили код? Отправить снова.',\n      subtitle: 'Чтобы продолжить, пожалуйста, введите код проверки, отправленный на ваш телефон.',\n      title: 'Проверьте ваш телефон',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Сбросить пароль',\n      requiredMessage: 'По соображениям безопасности необходимо сбросить ваш пароль.',\n      successMessage: 'Ваш пароль успешно изменен. Выполняется вход, подождите.',\n      title: 'Сбросить пароль',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Необходимо верифицировать вашу личность перед восстановлением пароля',\n    },\n    start: {\n      actionLink: 'Зарегистрироваться',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Использовать почту',\n      actionLink__use_email_username: 'Использовать почту или имя пользователя',\n      actionLink__use_passkey: 'Использовать ключ доступа вместо этого',\n      actionLink__use_phone: 'Использовать номер телефона',\n      actionLink__use_username: 'Использовать имя пользователя',\n      actionText: 'Нет учетной записи?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'чтобы продолжить работу в \"{{applicationName}}\"',\n      subtitleCombined: undefined,\n      title: 'Войти',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Верификационный код',\n      subtitle: 'Чтобы продолжить, пожалуйста, введите код проверки, сгенерированный вашим приложением аутентификации.',\n      title: 'Двухфакторая верификация',\n    },\n  },\n  signInEnterPasswordTitle: 'Введите Ваш пароль',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Войти',\n      actionText: 'Уже есть учетная запись?',\n      subtitle: 'чтобы продолжить работу в \"{{applicationName}}\"',\n      title: 'Заполните все поля',\n    },\n    emailCode: {\n      formSubtitle: 'Введите верификационный код, отправленный Вам на почту',\n      formTitle: 'Верификационный код',\n      resendButton: 'Не получили код? Отправить снова.',\n      subtitle: 'чтобы продолжить работу в \"{{applicationName}}\"',\n      title: 'Верифицируйте Вашу почту',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Чтобы продолжить, откройте верификационную ссылку на устройстве и в браузере, с которых вы начали регистрацию.',\n        title: 'Верификационная ссылка недействительна для этого устройства.',\n      },\n      formSubtitle: 'Используйте верификационную ссылку, оправленную Вам на почту',\n      formTitle: 'Верификационная ссылка',\n      loading: {\n        title: 'Входим...',\n      },\n      resendButton: 'Переотправить ссылку',\n      subtitle: 'чтобы продолжить работу в \"{{applicationName}}\"',\n      title: 'Верифицируйте Вашу почту',\n      verified: {\n        title: 'Успешный вход',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Вернитесь на новую открытую вкладку, чтобы продолжить',\n        subtitleNewTab: 'Вернитесь на предыдущую вкладку, чтобы продолжить',\n        title: 'Почта верифицирована',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Я согласен с {{ privacyPolicyLink || link(\"Политика конфиденциальности\") }}',\n        label__onlyTermsOfService: 'Я согласен с {{ termsOfServiceLink || link(\"Условия обслуживания\") }}',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Я согласен с {{ termsOfServiceLink || link(\"Условия обслуживания\") }} и {{ privacyPolicyLink || link(\"Политика конфиденциальности\") }}',\n      },\n      continue: {\n        subtitle: 'Пожалуйста, прочитайте и примите условия, чтобы продолжить.',\n        title: 'Юридическое согласие',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Введите верификационный код, отправленный на Ваш телефон',\n      formTitle: 'Верификационный код',\n      resendButton: 'Не получили код? Отправить снова.',\n      subtitle: 'чтобы продолжить работу в \"{{applicationName}}\"',\n      title: 'Верифицируйте Ваш номер телефона',\n    },\n    restrictedAccess: {\n      actionLink: 'Войти',\n      actionText: 'Уже есть учетная запись?',\n      blockButton__emailSupport: 'Написать в поддержку',\n      blockButton__joinWaitlist: undefined,\n      subtitle:\n        'Регистрация в данный момент отключена. Если вы считаете, что у вас должен быть доступ, пожалуйста, свяжитесь с поддержкой.',\n      subtitleWaitlist: undefined,\n      title: 'Доступ ограничен',\n    },\n    start: {\n      actionLink: 'Войти',\n      actionLink__use_email: 'Использовать электронную почту вместо этого',\n      actionLink__use_phone: 'Использовать телефон вместо этого',\n      actionText: 'Уже есть учетная запись?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'чтобы продолжить работу в \"{{applicationName}}\"',\n      subtitleCombined: 'чтобы продолжить работу в \"{{applicationName}}\"',\n      title: 'Создайте Вашу учетную запись',\n      titleCombined: 'Создайте Вашу учетную запись',\n    },\n  },\n  socialButtonsBlockButton: 'Продолжить с помощью {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: '{{email}} уже является членом организации.',\n    captcha_invalid:\n      'Регистрация не удалась из-за неудачных проверок безопасности. Пожалуйста, обновите страницу, чтобы попробовать снова, или обратитесь в службу поддержки для получения дополнительной помощи.',\n    captcha_unavailable:\n      'Регистрация не удалась из-за неудачной проверки бота. Пожалуйста, обновите страницу, чтобы попробовать снова, или обратитесь в службу поддержки для получения дополнительной помощи.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: 'Этот адрес электронной почты уже занят. Пожалуйста, попробуйте другой.',\n    form_identifier_exists__phone_number: 'Этот номер телефона уже занят. Пожалуйста, попробуйте другой.',\n    form_identifier_exists__username: 'Это имя пользователя уже занято. Пожалуйста, попробуйте другое.',\n    form_identifier_not_found: 'Мы не смогли найти учетную запись с этими данными.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'Адрес электронной почты должен быть действительным.',\n    form_param_format_invalid__phone_number: 'Номер телефона должен быть в действующем международном формате.',\n    form_param_max_length_exceeded__first_name: 'Имя не должно превышать 256 символов.',\n    form_param_max_length_exceeded__last_name: 'Фамилия не должна превышать 256 символов.',\n    form_param_max_length_exceeded__name: 'Имя не должно превышать 256 символов.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: 'Ваш пароль недостаточно надежный.',\n    form_password_pwned: 'Этот пароль был взломан и не может быть использован, попробуйте другой пароль.',\n    form_password_pwned__sign_in:\n      'Этот пароль был найден в утечке данных и не может быть использован. Пожалуйста, сбросьте пароль.',\n    form_password_size_in_bytes_exceeded:\n      'Ваш пароль превышает максимально допустимое количество байтов, сократите его или удалите некоторые специальные символы.',\n    form_password_validation_failed: 'Неверный пароль',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: 'Вы не можете удалить последнюю идентификацию.',\n    not_allowed_access:\n      \"Адрес электронной почты или номер телефона не разрешен для регистрации. Это может быть связано с использованием '+', '=', '#' или '.' в вашем адресе электронной почты, использованием домена, связанного с временной электронной почтой, или явным исключением.\",\n    organization_domain_blocked: 'Это заблокированный домен почтового провайдера. Пожалуйста, используйте другой.',\n    organization_domain_common: 'Это распространенный домен почтового провайдера. Пожалуйста, используйте другой.',\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded:\n      'Вы достигли предела количества участий в организациях, включая ожидающие приглашения.',\n    organization_minimum_permissions_needed:\n      'Должен быть как минимум один участник организации с минимально необходимыми разрешениями.',\n    passkey_already_exists: 'Ключ доступа уже зарегистрирован на этом устройстве.',\n    passkey_not_supported: 'Ключи доступа не поддерживаются на этом устройстве.',\n    passkey_pa_not_supported: 'Для регистрации требуется платформа аутентификатор, но устройство его не поддерживает.',\n    passkey_registration_cancelled: 'Регистрация ключа доступа была отменена или истекло время ожидания.',\n    passkey_retrieval_cancelled: 'Проверка ключа доступа была отменена или истекло время ожидания.',\n    passwordComplexity: {\n      maximumLength: 'менее {{length}} символов',\n      minimumLength: '{{length}} или более символов',\n      requireLowercase: 'букву в нижнем регистре',\n      requireNumbers: 'цифру',\n      requireSpecialCharacter: 'специальный символ',\n      requireUppercase: 'букву в верхнем регистре',\n      sentencePrefix: 'Ваш пароль должен содержать',\n    },\n    phone_number_exists: 'Этот номер телефона уже занят. Пожалуйста, попробуйте другой.',\n    session_exists: 'Вы уже вошли в систему.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Ваш пароль подходит, но мог бы быть надежнее. Попробуйте добавить больше символов.',\n      goodPassword: 'Хорошая работа. Это отличный пароль.',\n      notEnough: 'Ваш пароль недостаточно надежный.',\n      suggestions: {\n        allUppercase: 'Делайте заглавными некоторые, но не все буквы.',\n        anotherWord: 'Добавьте больше слов, которые менее распространены.',\n        associatedYears: 'Избегайте лет, которые связаны с вами.',\n        capitalization: 'Делайте заглавными не только первую букву',\n        dates: 'Избегайте дат и лет, которые связаны с вами.',\n        l33t: 'Избегайте предсказуемых замен букв, таких как «@» вместо «a».',\n        longerKeyboardPattern: 'Используйте более длинные сочетания клавиш и несколько раз меняйте направление ввода.',\n        noNeed: 'Вы можете создавать надежные пароли без использования символов, цифр или заглавных букв.',\n        pwned: 'Если вы используете этот пароль в другом месте, вам следует изменить его.',\n        recentYears: 'Избегайте последних лет.',\n        repeated: 'Избегайте повторяющихся слов и символов.',\n        reverseWords: 'Избегайте обратного написания часто используемых слов.',\n        sequences: 'Избегайте частых последовательностей символов.',\n        useWords: 'Используйте несколько слов, но избегайте распространенных фраз.',\n      },\n      warnings: {\n        common: 'Это распространенный пароль.',\n        commonNames: 'Распространенные имена и фамилии легко угадать.',\n        dates: 'Даты легко угадать.',\n        extendedRepeat: 'Повторяющиеся шаблоны символов, такие как \"abcabcabc\", легко угадать.',\n        keyPattern: 'Короткие сочетания клавиш легко угадать.',\n        namesByThemselves: 'Одни имена или фамилии легко угадать.',\n        pwned: 'Ваш пароль был раскрыт в результате утечки данных в Интернете.',\n        recentYears: 'Последние годы легко угадать.',\n        sequences: 'Частые последовательности символов, такие как \"abc\", легко угадать.',\n        similarToCommon: 'Этот пароль похож на часто используемый пароль.',\n        simpleRepeat: 'Повторяющиеся символы, такие как \"aaa\", легко угадать.',\n        straightRow: 'Прямые ряды клавиш на клавиатуре легко угадать.',\n        topHundred: 'Это часто используемый пароль.',\n        topTen: 'Это очень часто используемый пароль.',\n        userInputs: 'Не должно быть никаких личных данных или данных, связанных со страницей.',\n        wordByItself: 'Отдельные слова легко угадать.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Добавить учетную запись',\n    action__manageAccount: 'Управление учетной записью',\n    action__signOut: 'Выйти',\n    action__signOutAll: 'Выйти из всех учетных записей',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Скопировано!',\n      actionLabel__copy: 'Копировать все',\n      actionLabel__download: 'Скачать .txt',\n      actionLabel__print: 'Печать',\n      infoText1: 'Резервные коды будут включены для этой учетной записи.',\n      infoText2:\n        'Держите резервные коды в секрете и храните их в надежном месте. Вы можете создать новые резервные коды, если подозреваете, что они были скомпрометированы.',\n      subtitle__codelist: 'Храните их в безопасности и не сообщайте никому.',\n      successMessage:\n        'Резервные коды включены. Вы можете использовать один из этих кодов для входа в свою учетную запись, если вы потеряете доступ к своему устройству аутентификации. Каждый код может быть использован только один раз.',\n      successSubtitle:\n        'Вы можете использовать один из этих кодов для входа в свою учетную запись, если вы потеряете доступ к своему устройству аутентификации.',\n      title: 'Добавить резервный код подтверждения',\n      title__codelist: 'Резервные коды',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Выберите провайдера для подключения вашей учетной записи.',\n      formHint__noAccounts: 'Нет доступных провайдеров внешних учетных записей.',\n      removeResource: {\n        messageLine1: '{{identifier}} будет удален из вашей учетной записи.',\n        messageLine2:\n          'Вы больше не сможете использовать эту подключенную учетную запись, и любые зависимые функции больше не будут работать.',\n        successMessage: '{{connectedAccount}} был удален из вашей учетной записи.',\n        title: 'Удалить подключенную учетную запись',\n      },\n      socialButtonsBlockButton: 'Подключить учетную запись {{provider|titleize}}',\n      successMessage: 'Провайдер был добавлен в вашу учетную запись',\n      title: 'Добавить подключенную учетную запись',\n    },\n    deletePage: {\n      actionDescription: 'Введите \"Удалить учетную запись\" ниже, чтобы продолжить.',\n      confirm: 'Удалить учетную запись',\n      messageLine1: 'Вы уверены, что хотите удалить свою учетную запись?',\n      messageLine2: 'Это действие является окончательным и необратимым.',\n      title: 'Удалить учетную запись',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'На этот адрес электронной почты будет отправлено письмо с верификационным кодом.',\n        formSubtitle: 'Введите верификационный код, отправленный на {{identifier}}',\n        formTitle: 'Верификационный код',\n        resendButton: 'Не получили код? Отправить повторно',\n        successMessage: 'Адрес электронной почты {{identifier}} был добавлен в вашу учетную запись.',\n      },\n      emailLink: {\n        formHint: 'На этот адрес электронной почты будет отправлена верификационная ссылка.',\n        formSubtitle: 'Нажмите на верификационную ссылку в письме, отправленном на {{identifier}}',\n        formTitle: 'Верификационная ссылка',\n        resendButton: 'Отправить ссылку повторно',\n        successMessage: 'Адрес электронной почты {{identifier}} был добавлен в вашу учетную запись.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} будет удален из этой учетной записи.',\n        messageLine2: 'Вы больше не сможете войти с использованием этого адреса электронной почты.',\n        successMessage: '{{emailAddress}} был удален из вашей учетной записи.',\n        title: 'Удалить адрес электронной почты',\n      },\n      title: 'Добавить адрес электронной почты',\n      verifyTitle: 'Верифицировать адрес электроной почты',\n    },\n    formButtonPrimary__add: 'Добавить',\n    formButtonPrimary__continue: 'Продолжить',\n    formButtonPrimary__finish: 'Готово',\n    formButtonPrimary__remove: 'Удалить',\n    formButtonPrimary__save: 'Сохранить',\n    formButtonReset: 'Отмена',\n    mfaPage: {\n      formHint: 'Выберите метод для добавления.',\n      title: 'Добавить двухфакторную аутентификацию',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Использовать существующий номер телефона',\n      primaryButton__addPhoneNumber: 'Добавить номер телефона',\n      removeResource: {\n        messageLine1: '{{identifier}} больше не будет получать коды подтверждения при входе в систему.',\n        messageLine2: 'Ваша учетная запись будет менее защищенной. Вы уверены, что хотите продолжить?',\n        successMessage: 'Двухфакторная аутентификация SMS-кодом была удалена для {{mfaPhoneCode}}',\n        title: 'Удалить двухфакторную аутентификацию',\n      },\n      subtitle__availablePhoneNumbers:\n        'Выберите номер телефона для регистрации двухфакторной аутентификиции SMS-кодом.',\n      subtitle__unavailablePhoneNumbers:\n        'Нет доступных номеров телефона для регистрации двухфакторной аутентификация SMS-кодом. Добавьте новый номер.',\n      successMessage1:\n        'При входе вам потребуется ввести верификационный код отправленный на этот номер телефона как дополнительный шаг',\n      successMessage2:\n        'Сохраните эти резервные коды и храните их в надежном месте. Если вы потеряете доступ к устройству аутентификации, вы сможете использовать резервные коды для входа.',\n      successTitle: 'Проверка с помощью SMS-кода включена',\n      title: 'Добавить проверку кодом из SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Вместо этого отсканируйте QR-код',\n        buttonUnableToScan__nonPrimary: 'Не удается отсканировать QR-код?',\n        infoText__ableToScan:\n          'Настройте новый метод входа в вашем приложении аутентификации и отсканируйте следующий QR-код, чтобы связать его с вашей учетной записью.',\n        infoText__unableToScan:\n          'Настройте новый метод входа в вашем приложении аутентификации и введите ниже предоставленный ключ.',\n        inputLabel__unableToScan1:\n          'Убедитесь что включены одноразовые или привязанные ко времени пароли затем завершите привязку вашей учетной записи.',\n        inputLabel__unableToScan2:\n          'Кроме того, если ваше приложение аутентификации поддерживает URI TOTP, вы также можете скопировать полный URI.',\n      },\n      removeResource: {\n        messageLine1:\n          'Верификационный код из этого приложения аутентификации больше не потребуется при входе в систему.',\n        messageLine2: 'Ваша учетная запись будет менее защищенной. Вы уверены, что хотите продолжить?',\n        successMessage: 'Двухфакторная аутентификация через приложение аутентификации была удалена.',\n        title: 'Удаление двухфакторной аутентификации',\n      },\n      successMessage:\n        'Двухфакторная аутентификация в настоящее время включена. При входе в систему вам нужно будет ввести верификационный код из этого приложения в качестве дополнительного шага.',\n      title: 'Добавить приложение аутентификации',\n      verifySubtitle: 'Введите верификационный код, созданный вашим приложением аутентификации',\n      verifyTitle: 'Верификационный код',\n    },\n    mobileButton__menu: 'Меню',\n    navbar: {\n      account: 'Профиль',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Управление информацией вашей учетной записи.',\n      security: 'Безопасность',\n      title: 'Учетная запись',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} будет удален из этой учетной записи.',\n        title: 'Удалить ключ доступа',\n      },\n      subtitle__rename: 'Вы можете изменить название ключа доступа чтобы его было легче найти.',\n      title__rename: 'Переименовать ключ доступа',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Рекомендуется выйти из всех других устройств, на которых использовался ваш старый пароль.',\n      readonly: 'Ваш пароль сейчас нельзя изменить, так как вы можете войти только через корпоративное подключение.',\n      successMessage__set: 'Ваш пароль установлен.',\n      successMessage__signOutOfOtherSessions: 'Все другие устройства были выведены из системы.',\n      successMessage__update: 'Ваш пароль был обновлен.',\n      title__set: 'Установить пароль',\n      title__update: 'Изменить пароль',\n    },\n    phoneNumberPage: {\n      infoText:\n        'На этот номер телефона будет отправлено текстовое сообщение со ссылкой верификации. Могут применяться тарифы на сообщения и передачу данных.',\n      removeResource: {\n        messageLine1: '{{identifier}} будет удален из этой учетной записи.',\n        messageLine2: 'Вы больше не сможете войти, используя этот номер телефона.',\n        successMessage: '{{phoneNumber}} был удален из вашей учетной записи.',\n        title: 'Удалить номер телефона',\n      },\n      successMessage: '{{identifier}} был добавлен к вашей учетной записи.',\n      title: 'Добавить номер телефона',\n      verifySubtitle: 'Введите верификационный код отправленный на {{identifier}}',\n      verifyTitle: 'Верифицировать номер телефона',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Загрузите изображение в форматах JPG, PNG, GIF или WEBP размером меньше 10 МБ',\n      imageFormDestructiveActionSubtitle: 'Удалить изображение',\n      imageFormSubtitle: 'Загрузить изображение',\n      imageFormTitle: 'Изображение профиля',\n      readonly:\n        'Информация вашего профиля была предоставлена корпоративным подключением и не может быть отредактирована.',\n      successMessage: 'Ваш профиль был обновлен.',\n      title: 'Обновить профиль',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Выйти из устройства',\n        title: 'Активные устройства',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Попробовать снова',\n        actionLabel__reauthorize: 'Авторизовать сейчас',\n        destructiveActionTitle: 'Удалить',\n        primaryButton: 'Подключить учетную запись',\n        subtitle__disconnected: 'Эта учетная запись была отключена.',\n        subtitle__reauthorize:\n          'Необходимые области доступа были обновлены, и вы можете столкнуться с ограниченной функциональностью. Пожалуйста, повторно авторизуйте приложение, чтобы избежать возможных проблем',\n        title: 'Подключенные учетные записи',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Удалить учетную запись',\n        title: 'Удаление учетной записи',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Удалить адрес электронной почты',\n        detailsAction__nonPrimary: 'Установить в качестве основного',\n        detailsAction__primary: 'Завершить верификацию',\n        detailsAction__unverified: 'Верифицировать',\n        primaryButton: 'Добавить адрес электронной почты',\n        title: 'Адреса электронной почты',\n      },\n      enterpriseAccountsSection: {\n        title: 'Корпоративные учетные записи',\n      },\n      headerTitle__account: 'Учетная запись',\n      headerTitle__security: 'Безопасность',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Сгенерировать коды',\n          headerTitle: 'Резервные коды',\n          subtitle__regenerate:\n            'Получите новый набор безопасных резервных кодов. Предыдущие резервные коды будут удалены и не могут быть использованы.',\n          title__regenerate: 'Сгенерировать новые резервные коды',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Установить по умолчанию',\n          destructiveActionLabel: 'Удалить номер телефона',\n        },\n        primaryButton: 'Добавить двухфакторную аутентификацию',\n        title: 'Двухфакторная аутентификация',\n        totp: {\n          destructiveActionTitle: 'Удалить',\n          headerTitle: 'Приложение аутентификации',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Удалить',\n        menuAction__rename: 'Переименовать',\n        primaryButton: undefined,\n        title: 'Ключи доступа',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Установить пароль',\n        primaryButton__updatePassword: 'Изменить пароль',\n        title: 'Пароль',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Удалить номер телефона',\n        detailsAction__nonPrimary: 'Установить как основной',\n        detailsAction__primary: 'Завершить верификацию',\n        detailsAction__unverified: 'Завершить верификацию',\n        primaryButton: 'Добавить номер телефона',\n        title: 'Номера телефонов',\n      },\n      profileSection: {\n        primaryButton: 'Обновить профиль',\n        title: 'Профиль',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Установить имя пользователя',\n        primaryButton__updateUsername: 'Изменить имя пользователя',\n        title: 'Имя пользователя',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Удалить кошелек',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 кошельки',\n        title: 'Web3 кошельки',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Имя пользователя было обновлено.',\n      title__set: 'Обновить имя пользователя',\n      title__update: 'Обновить имя пользователя',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} будет удален из этой учетной записи.',\n        messageLine2: 'Вы больше не сможете Войти с использованием этого web3 кошелька.',\n        successMessage: '{{web3Wallet}} был удален из вашей учетной записи.',\n        title: 'Удалить web3 кошелек',\n      },\n      subtitle__availableWallets: 'Выберите web3 кошелек для подключения к вашей учетной записи.',\n      subtitle__unavailableWallets: 'Нет доступных web3 кошельков.',\n      successMessage: 'Кошелек был добавлен к вашей учетной записи.',\n      title: 'Добавить web3 кошелек',\n      web3WalletButtonsBlockButton: '{{provider|titleize}}',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCACE;AAAA,EACF,6BAA6B;AAAA,EAC7B,yBACE;AAAA,EACF,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBACE;AAAA,EACF,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCACE;AAAA,QACF,+BAA+B;AAAA,QAC/B,UACE;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UACE;AAAA,MACF,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BACE;AAAA,IACF,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCACE;AAAA,IACF,yCACE;AAAA,IACF,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cACE;AAAA,QACF,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UACE;AAAA,MACF,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UACE;AAAA,MACF,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}