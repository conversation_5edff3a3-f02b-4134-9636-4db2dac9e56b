{"version": 3, "sources": ["../src/ja-JP.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const jaJP: LocalizationResource = {\n  locale: 'ja-JP',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: '戻る',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'デフォルト',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: '他の模倣者デバイス',\n  badge__primary: 'プライマリ',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'アクションが必要',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'このデバイス',\n  badge__unverified: '未確認',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'ユーザーデバイス',\n  badge__you: 'あなた',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: '組織を作成する',\n    invitePage: {\n      formButtonReset: 'スキップ',\n    },\n    title: '組織の作成',\n  },\n  dates: {\n    lastDay: \"昨日の{{ date | timeString('ja-JP') }}に\",\n    next6Days: \"{{ date | weekday('ja-JP','long') }}の{{ date | timeString('ja-JP') }}に\",\n    nextDay: \"明日の{{ date | timeString('ja-JP') }}に\",\n    numeric: \"{{ date | numeric('ja-JP') }}に\",\n    previous6Days: \"{{ date | weekday('ja-JP','long') }}の{{ date | timeString('ja-JP') }}に\",\n    sameDay: \"今日の{{ date | timeString('ja-JP') }}に\",\n  },\n  dividerText: 'または',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: '別の方法を使用する',\n  footerPageLink__help: 'ヘルプ',\n  footerPageLink__privacy: 'プライバシー',\n  footerPageLink__terms: '利用規約',\n  formButtonPrimary: '続ける',\n  formButtonPrimary__verify: '確認する',\n  formFieldAction__forgotPassword: 'パスワードをお忘れですか？',\n  formFieldError__matchingPasswords: 'パスワードが一致します。',\n  formFieldError__notMatchingPasswords: 'パスワードが一致しません。',\n  formFieldError__verificationLinkExpired: '検証リンクの有効期限が切れています。新しいリンクをリクエストしてください。',\n  formFieldHintText__optional: '任意',\n  formFieldHintText__slug: 'スラグは人間が読めるユニークなIDです。URLで良く使われます。',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: 'バックアップコード',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'アカウント削除',\n  formFieldInputPlaceholder__emailAddress: 'メールアドレス',\n  formFieldInputPlaceholder__emailAddress_username: 'メールアドレスまたはユーザー名',\n  formFieldInputPlaceholder__emailAddresses:\n    'スペースまたはカンマで区切って、1つ以上のメールアドレスを入力または貼り付けてください',\n  formFieldInputPlaceholder__firstName: '名',\n  formFieldInputPlaceholder__lastName: '姓',\n  formFieldInputPlaceholder__organizationDomain: '組織ドメイン',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: '組織ドメインのメールアドレス',\n  formFieldInputPlaceholder__organizationName: '組織名',\n  formFieldInputPlaceholder__organizationSlug: 'my-organization',\n  formFieldInputPlaceholder__password: 'パスワード',\n  formFieldInputPlaceholder__phoneNumber: '電話番号',\n  formFieldInputPlaceholder__username: 'ユーザー名',\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'このドメインの自動招待を有効にする',\n  formFieldLabel__backupCode: 'バックアップコード',\n  formFieldLabel__confirmDeletion: '削除の確認',\n  formFieldLabel__confirmPassword: 'パスワードの確認',\n  formFieldLabel__currentPassword: '現在のパスワード',\n  formFieldLabel__emailAddress: 'メールアドレス',\n  formFieldLabel__emailAddress_username: 'メールアドレスまたはユーザー名',\n  formFieldLabel__emailAddresses: 'メールアドレス',\n  formFieldLabel__firstName: '名',\n  formFieldLabel__lastName: '姓',\n  formFieldLabel__newPassword: '新しいパスワード',\n  formFieldLabel__organizationDomain: 'ドメイン',\n  formFieldLabel__organizationDomainDeletePending: '保留中の招待と提案を削除',\n  formFieldLabel__organizationDomainEmailAddress: '確認用のメールアドレス',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'このドメインを確認するためのコードを受け取るメールアドレスを入力してください。',\n  formFieldLabel__organizationName: '組織名',\n  formFieldLabel__organizationSlug: 'スラグURL',\n  formFieldLabel__passkeyName: undefined,\n  formFieldLabel__password: 'パスワード',\n  formFieldLabel__phoneNumber: '電話番号',\n  formFieldLabel__role: '役割',\n  formFieldLabel__signOutOfOtherSessions: '他のデバイスからサインアウト',\n  formFieldLabel__username: 'ユーザー名',\n  impersonationFab: {\n    action__signOut: 'サインアウト',\n    title: '{{identifier}}としてサインイン中',\n  },\n  maintenanceMode: undefined,\n  membershipRole__admin: '管理者',\n  membershipRole__basicMember: 'メンバー',\n  membershipRole__guestMember: 'ゲスト',\n  organizationList: {\n    action__createOrganization: '組織を作成する',\n    action__invitationAccept: '参加する',\n    action__suggestionsAccept: '参加をリクエストする',\n    createOrganization: '組織を作成',\n    invitationAcceptedLabel: '参加しました',\n    subtitle: '{{applicationName}}',\n    suggestionsAcceptedLabel: '承認待ち',\n    title: 'アカウントを選択',\n    titleWithoutPersonal: '組織を選択',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: '自動招待',\n    badge__automaticSuggestion: '自動サジェスト',\n    badge__manualInvitation: '自動登録なし',\n    badge__unverified: '未承認',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'ドメインを追加して検証します。このドメインのメールアドレスを持つユーザーは、自動的に組織に参加するか、参加をリクエストすることができます。',\n      title: 'ドメインを追加',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed: '招待状を送信できませんでした。以下を修正してもう一度試してください:',\n      formButtonPrimary__continue: '招待状を送信する',\n      selectDropdown__role: '役割を選択',\n      subtitle: 'この組織に新しいメンバーを招待する',\n      successMessage: '招待状が正常に送信されました',\n      title: 'メンバーを招待',\n    },\n    membersPage: {\n      action__invite: '招待',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'メンバーの削除',\n        tableHeader__actions: undefined,\n        tableHeader__joined: '参加日時',\n        tableHeader__role: '役割',\n        tableHeader__user: 'ユーザー',\n      },\n      detailsTitle__emptyRow: '表示するメンバーはありません',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'メールドメインを組織に接続することでユーザーを招待します。一致するメールドメインを持つユーザーは、いつでも組織に参加することができます。',\n          headerTitle: '自動招待',\n          primaryButton: '検証済みドメインを管理',\n        },\n        table__emptyRow: '表示する招待はありません',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: '招待を取り消す',\n        tableHeader__invited: '招待済み',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            '一致するメールドメインを持つユーザーは、組織への参加をリクエストする提案を受け取ることができます。',\n          headerTitle: '自動提案',\n          primaryButton: '検証済みドメインを管理',\n        },\n        menuAction__approve: '承認',\n        menuAction__reject: '拒否',\n        tableHeader__requested: 'アクセスをリクエストしました',\n        table__emptyRow: '表示するリクエストはありません',\n      },\n      start: {\n        headerTitle__invitations: '招待',\n        headerTitle__members: 'メンバー',\n        headerTitle__requests: 'リクエスト',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: '組織を管理します。',\n      general: '一般',\n      members: 'メンバー',\n      title: '組織',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: '続行するには \"{{organizationName}}\" と入力してください。',\n          messageLine1: 'この組織を削除してもよろしいですか？',\n          messageLine2: 'この操作は永久的で取り消すことはできません。',\n          successMessage: '組織が削除されました。',\n          title: '組織の削除',\n        },\n        leaveOrganization: {\n          actionDescription: '続行するには \"{{organizationName}}\" と入力してください。',\n          messageLine1:\n            'この組織から脱退してもよろしいですか？この組織とそのアプリケーションへのアクセスが失われます。',\n          messageLine2: 'この操作は永久的で取り消すことはできません。',\n          successMessage: '組織から脱退しました。',\n          title: '組織を脱退',\n        },\n        title: '注意',\n      },\n      domainSection: {\n        menuAction__manage: '管理',\n        menuAction__remove: '削除',\n        menuAction__verify: '検証',\n        primaryButton: 'ドメインを追加',\n        subtitle:\n          '検証済みのメールドメインに基づいて、ユーザーが自動的に組織に参加するか、参加をリクエストすることを許可します。',\n        title: '検証済みドメイン',\n      },\n      successMessage: '組織が更新されました。',\n      title: '組織プロフィール',\n    },\n    removeDomainPage: {\n      messageLine1: 'メールドメイン {{domain}} が削除されます。',\n      messageLine2: 'この後、ユーザーは自動的に組織に参加することができなくなります。',\n      successMessage: '{{domain}} が削除されました。',\n      title: 'ドメインの削除',\n    },\n    start: {\n      headerTitle__general: '一般',\n      headerTitle__members: 'メンバー',\n      profileSection: {\n        primaryButton: undefined,\n        title: '組織プロフィール',\n        uploadAction__title: 'ロゴ',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'このドメインを削除すると、招待されたユーザーに影響が出ます。',\n        removeDomainActionLabel__remove: 'ドメインを削除',\n        removeDomainSubtitle: '検証済みドメインからこのドメインを削除します',\n        removeDomainTitle: 'ドメインの削除',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'サインアップ時にユーザーは自動的に組織に招待され、いつでも参加することができます。',\n        automaticInvitationOption__label: '自動招待',\n        automaticSuggestionOption__description:\n          'ユーザーは組織への参加をリクエストする提案を受け取りますが、参加する前に管理者の承認が必要です。',\n        automaticSuggestionOption__label: '自動提案',\n        calloutInfoLabel: '新しいユーザーにのみ登録モードの変更が影響します。',\n        calloutInvitationCountLabel: 'ユーザーに送信された保留中の招待状: {{count}}',\n        calloutSuggestionCountLabel: 'ユーザーに送信された保留中の提案: {{count}}',\n        manualInvitationOption__description: 'ユーザーは組織に手動で招待されることのみが可能です。',\n        manualInvitationOption__label: '自動登録なし',\n        subtitle: 'このドメインのユーザーが組織に参加する方法を選択してください。',\n      },\n      start: {\n        headerTitle__danger: '危険',\n        headerTitle__enrollment: '登録オプション',\n      },\n      subtitle: 'ドメイン {{domain}} が検証されました。登録モードを選択して続行してください。',\n      title: '{{domain}} の更新',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'メールアドレスに送信された検証コードを入力してください',\n      formTitle: '検証コード',\n      resendButton: 'コードを再送信',\n      subtitle: 'ドメイン {{domainName}} はメールで検証する必要があります。',\n      subtitleVerificationCodeScreen:\n        '{{emailAddress}} に検証コードが送信されました。コードを入力して続行してください。',\n      title: 'ドメインを検証',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: '組織の作成',\n    action__invitationAccept: '参加する',\n    action__manageOrganization: '組織の管理',\n    action__suggestionsAccept: '参加をリクエストする',\n    notSelected: '組織が選択されていません',\n    personalWorkspace: '個人ワークスペース',\n    suggestionsAcceptedLabel: '承認待ち',\n  },\n  paginationButton__next: '次へ',\n  paginationButton__previous: '前へ',\n  paginationRowText__displaying: '表示中',\n  paginationRowText__of: '全',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'アカウントを追加',\n      action__signOutAll: '全てのアカウントからサインアウト',\n      subtitle: '続行するアカウントを選択してください。',\n      title: 'アカウントを選択',\n    },\n    alternativeMethods: {\n      actionLink: 'ヘルプを取得',\n      actionText: 'これらのいずれも持っていませんか？',\n      blockButton__backupCode: 'バックアップコードを使用する',\n      blockButton__emailCode: '{{identifier}}にメールコードを送信',\n      blockButton__emailLink: '{{identifier}}にメールリンクを送信',\n      blockButton__passkey: undefined,\n      blockButton__password: 'パスワードでサインインする',\n      blockButton__phoneCode: '{{identifier}}にSMSコードを送信',\n      blockButton__totp: '認証アプリを使用する',\n      getHelp: {\n        blockButton__emailSupport: 'メールサポート',\n        content:\n          'アカウントにサインインできない場合は、メールでお問い合わせいただければ、できるだけ早くアクセスを回復するためにお手伝いいたします。',\n        title: 'ヘルプを取得',\n      },\n      subtitle: '問題が発生していますか？これらの方法を使用してサインインすることができます。',\n      title: '別の方法を使用',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: '{{applicationName}}へのアクセスを続ける',\n      title: 'バックアップコードを入力',\n    },\n    emailCode: {\n      formTitle: '検証コード',\n      resendButton: 'コードを再送信',\n      subtitle: '{{applicationName}}へのアクセスを続ける',\n      title: 'メールを確認',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: '元のタブに戻って続行してください。',\n        title: 'この検証リンクは期限切れです',\n      },\n      failed: {\n        subtitle: '元のタブに戻って続行してください。',\n        title: 'この検証リンクは無効です',\n      },\n      formSubtitle: 'メールに送信された検証リンクを使用してください',\n      formTitle: '検証リンク',\n      loading: {\n        subtitle: 'まもなくリダイレクトされます',\n        title: 'サインイン中...',\n      },\n      resendButton: 'リンクを再送信',\n      subtitle: '{{applicationName}}へのアクセスを続ける',\n      title: 'メールを確認',\n      unusedTab: {\n        title: 'このタブを閉じてもかまいません',\n      },\n      verified: {\n        subtitle: 'まもなくリダイレクトされます',\n        title: '正常にサインインしました',\n      },\n      verifiedSwitchTab: {\n        subtitle: '続行するには元のタブに戻ってください',\n        subtitleNewTab: '新しく開いたタブに戻って続行してください',\n        titleNewTab: '他のタブでサインイン済み',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'パスワードリセットコード',\n      resendButton: 'コードを再送信',\n      subtitle: 'パスワードをリセットするために',\n      subtitle_email: 'まず、メールIDに送信されたコードを入力してください',\n      subtitle_phone: 'まず、電話に送信されたコードを入力してください',\n      title: 'パスワードをリセット',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'パスワードをリセット',\n      label__alternativeMethods: 'または、別の方法でサインインしてください。',\n      title: 'パスワードをお忘れですか？',\n    },\n    noAvailableMethods: {\n      message: 'サインインできません。利用可能な認証方法がありません。',\n      subtitle: 'エラーが発生しました',\n      title: 'サインインできません',\n    },\n    passkey: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: '別の方法を使用',\n      subtitle: '{{applicationName}}へのアクセスを続ける',\n      title: 'パスワードを入力',\n    },\n    passwordPwned: {\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: '検証コード',\n      resendButton: 'コードを再送信',\n      subtitle: '{{applicationName}} への続行のため',\n      title: '電話を確認してください',\n    },\n    phoneCodeMfa: {\n      formTitle: '検証コード',\n      resendButton: 'コードを再送信',\n      subtitle: undefined,\n      title: '電話を確認してください',\n    },\n    resetPassword: {\n      formButtonPrimary: 'パスワードをリセット',\n      requiredMessage:\n        '未確認のメールアドレスを持つアカウントが既に存在します。セキュリティのためにパスワードをリセットしてください。',\n      successMessage: 'パスワードが正常に変更されました。お待ちください、サインインしています。',\n      title: 'パスワードをリセット',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'パスワードをリセットする前に、身元を確認する必要があります。',\n    },\n    start: {\n      actionLink: 'サインアップ',\n      actionLink__join_waitlist: 'ウェイトリストに登録',\n      actionLink__use_email: 'メールアドレスを使用',\n      actionLink__use_email_username: 'メールアドレスまたはユーザー名を使用',\n      actionLink__use_passkey: 'パスキーを使用',\n      actionLink__use_phone: '電話番号を使用',\n      actionLink__use_username: 'ユーザー名を使用',\n      actionText: 'アカウントをお持ちでないですか？',\n      actionText__join_waitlist: '先行体験にご興味ありますか？',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: '{{applicationName}}へのアクセスを続ける',\n      subtitleCombined: undefined,\n      title: 'サインイン',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: '検証コード',\n      subtitle: undefined,\n      title: '二段階認証',\n    },\n  },\n  signInEnterPasswordTitle: 'パスワードを入力してください',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'サインイン',\n      actionText: 'アカウントをお持ちですか？',\n      subtitle: '{{applicationName}}へのアクセスを続ける',\n      title: '未入力のフィールドを入力',\n    },\n    emailCode: {\n      formSubtitle: 'メールアドレスに送信された確認コードを入力してください',\n      formTitle: '確認コード',\n      resendButton: 'コードを再送信',\n      subtitle: '{{applicationName}}へのアクセスを続ける',\n      title: 'メールアドレスを確認',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: 'メールアドレスに送信された確認リンクを使用してください',\n      formTitle: '確認リンク',\n      loading: {\n        title: '登録中...',\n      },\n      resendButton: 'リンクを再送信',\n      subtitle: '{{applicationName}}へのアクセスを続ける',\n      title: 'メールアドレスを確認',\n      verified: {\n        title: '登録が完了しました',\n      },\n      verifiedSwitchTab: {\n        subtitle: '続行するために新しく開いたタブに戻ってください',\n        subtitleNewTab: '続行するために前のタブに戻ってください',\n        title: 'メールアドレスが正常に確認されました',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: '{{privacyPolicyLink || link(\"個人情報保護方針\")}}に同意します',\n        label__onlyTermsOfService: '{{termsOfServiceLink || link(\"利用規約\")}}に同意します',\n        label__termsOfServiceAndPrivacyPolicy:\n          '{{termsOfServiceLink || link(\"利用規約\")}}と{{privacyPolicyLink || link(\"個人情報保護方針\")}}に同意します',\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: '電話番号に送信された確認コードを入力してください',\n      formTitle: '確認コード',\n      resendButton: 'コードを再送信',\n      subtitle: '{{applicationName}}へのアクセスを続ける',\n      title: '電話番号を確認',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'サインイン',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: 'アカウントをお持ちですか？',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: '{{applicationName}}へのアクセスを続ける',\n      subtitleCombined: '{{applicationName}}へのアクセスを続ける',\n      title: 'アカウントを作成',\n      titleCombined: 'アカウントを作成',\n    },\n  },\n  socialButtonsBlockButton: '{{provider|titleize}}で続ける',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'セキュリティ検証に失敗したため、サインアップに失敗しました。ページを更新して再試行するか、サポートセンターに連絡した上でサポートを受けてください。',\n    captcha_unavailable:\n      'ボット検証に失敗したため、サインアップに失敗しました。ページを更新して再試行するか、サポートに連絡してさらに支援を受けてください。',\n    form_code_incorrect: 'フォームコードが正しくありません。',\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: 'これらの詳細に一致するアカウントは見つかりませんでした。',\n    form_param_format_invalid: 'パラメータ形式が無効です。',\n    form_param_format_invalid__email_address: 'メールアドレスは有効なメールアドレスである必要があります。',\n    form_param_format_invalid__phone_number: '電話番号は有効な国際形式である必要があります',\n    form_param_max_length_exceeded__first_name: '氏名は256文字を超えることはできません。',\n    form_param_max_length_exceeded__last_name: '姓は256文字を超えることはできません。',\n    form_param_max_length_exceeded__name: '名前は256文字を超えることはできません。',\n    form_param_nil: 'パラメータが存在しません。',\n    form_param_value_invalid: undefined,\n    form_password_incorrect: 'パスワードが正しくありません。',\n    form_password_length_too_short: 'パスワードの長さが短すぎます。',\n    form_password_not_strong_enough: 'パスワードの強度が不十分です。',\n    form_password_pwned:\n      'このパスワードは侵害の一部として見つかったため使用できません。別のパスワードを試してください。',\n    form_password_pwned__sign_in: undefined,\n    form_password_size_in_bytes_exceeded:\n      'パスワードのバイト数が上限を超えています。短くするか、一部の特殊文字を削除してください。',\n    form_password_validation_failed: 'パスワードが間違っています',\n    form_username_invalid_character: 'ユーザー名に無効な文字が含まれています。',\n    form_username_invalid_length: 'ユーザー名の長さが無効です。',\n    identification_deletion_failed: '最後の識別情報は削除できません。',\n    not_allowed_access:\n      \"メールアドレスまたは電話番号は登録に使用できません。これは、'+', '=', '#' または '.' がメールアドレスに使用されているか、一時的な電子メールサービスに接続されたドメインが使用されているか、明示的な除外が行われているためです。エラーが発生した場合は、サポートに連絡してください。\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: '{{length}}文字未満',\n      minimumLength: '{{length}}文字以上',\n      requireLowercase: '小文字を含む',\n      requireNumbers: '数字を含む',\n      requireSpecialCharacter: '特殊文字を含む',\n      requireUppercase: '大文字を含む',\n      sentencePrefix: 'パスワードは次の条件を満たす必要があります：',\n    },\n    phone_number_exists: 'この電話番号は既に使用されています。別の電話番号を試してください。',\n    session_exists: 'すでにサインインしています。',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'パスワードは有効ですが、もう少し強化できます。文字を追加してみてください。',\n      goodPassword: '素晴らしい仕事です。これは優れたパスワードです。',\n      notEnough: 'パスワードの強度が十分ではありません。',\n      suggestions: {\n        allUppercase: '全ての文字を大文字にするのではなく、一部の文字を大文字にしてください。',\n        anotherWord: 'より一般的でない単語を追加してください。',\n        associatedYears: '自分に関連する年号は避けてください。',\n        capitalization: '最初の文字以外も大文字にしてください。',\n        dates: '自分に関連する日付や年号は避けてください。',\n        l33t: \"予測可能な文字の代替（例：'@' で 'a' を置き換える）を避けてください。\",\n        longerKeyboardPattern: '長いキーボードパターンを使用し、タイピングの方向を複数回変えてください。',\n        noNeed: 'シンボル、数字、大文字の使用なしでも強力なパスワードを作成できます。',\n        pwned: '他の場所でこのパスワードを使用している場合は、変更する必要があります。',\n        recentYears: '最近の年号は避けてください。',\n        repeated: '繰り返される単語や文字を避けてください。',\n        reverseWords: '一般的な単語の逆さ読みは避けてください。',\n        sequences: '一般的な文字の並びを避けてください。',\n        useWords: '複数の単語を使用してくださいが、一般的なフレーズは避けてください。',\n      },\n      warnings: {\n        common: 'これは一般的に使われるパスワードです。',\n        commonNames: '一般的な名前や姓は推測しやすいです。',\n        dates: '日付は推測しやすいです。',\n        extendedRepeat: '「abcabcabc」といった繰り返しパターンは推測しやすいです。',\n        keyPattern: '短いキーボードパターンは推測しやすいです。',\n        namesByThemselves: '単体の名前や姓は推測しやすいです。',\n        pwned: 'このパスワードはインターネット上のデータ侵害によって公開されています。',\n        recentYears: '最近の年号は推測しやすいです。',\n        sequences: '「abc」といった一般的な文字の並びは推測しやすいです。',\n        similarToCommon: 'これは一般的に使われるパスワードに類似しています。',\n        simpleRepeat: '「aaa」といった繰り返し文字は推測しやすいです。',\n        straightRow: 'キーボード上の連続した行は推測しやすいです。',\n        topHundred: 'これは頻繁に使われるパスワードです。',\n        topTen: 'これはよく使われるパスワードです。',\n        userInputs: '個人情報やページに関連するデータは含まれていないはずです。',\n        wordByItself: '単語単体では推測しやすいです。',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'アカウントの追加',\n    action__manageAccount: 'アカウントの管理',\n    action__signOut: 'サインアウト',\n    action__signOutAll: '全てのアカウントからサインアウト',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'コピー済み！',\n      actionLabel__copy: 'すべてコピー',\n      actionLabel__download: '.txtでダウンロード',\n      actionLabel__print: '印刷',\n      infoText1: 'このアカウントではバックアップコードが有効になります。',\n      infoText2:\n        'バックアップコードは秘密に保管し、安全に保存してください。疑わしい場合はバックアップコードを再生成することができます。',\n      subtitle__codelist: 'バックアップコードを安全に保管し、秘密にしてください。',\n      successMessage:\n        'バックアップコードが有効になりました。認証デバイスにアクセスできない場合、これらのいずれかを使用してアカウントにサインインできます。各コードは一度しか使用できません。',\n      successSubtitle: '認証デバイスにアクセスできない場合、これらのいずれかを使用してアカウントにサインインできます。',\n      title: 'バックアップコードの追加',\n      title__codelist: 'バックアップコード',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'アカウントを連携するプロバイダを選択してください。',\n      formHint__noAccounts: '利用可能な外部アカウントプロバイダはありません。',\n      removeResource: {\n        messageLine1: '{{identifier}}はこのアカウントから削除されます。',\n        messageLine2: 'この連携アカウントを使用することはできなくなり、関連する機能も使用できなくなります。',\n        successMessage: '{{connectedAccount}}がアカウントから削除されました。',\n        title: '連携アカウントの削除',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}アカウントを連携する',\n      successMessage: 'プロバイダがアカウントに追加されました',\n      title: '連携アカウントの追加',\n    },\n    deletePage: {\n      actionDescription: '続行するには下記に「Delete account」を入力してください。',\n      confirm: 'アカウント削除',\n      messageLine1: 'アカウントを削除してもよろしいですか？',\n      messageLine2: 'この操作は永久的で取り消すことはできません。',\n      title: 'アカウントの削除',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'このメールアドレスには検証コードが含まれたメールが送信されます。',\n        formSubtitle: '{{identifier}}に送信された検証コードを入力してください。',\n        formTitle: '検証コード',\n        resendButton: 'コードを再送信',\n        successMessage: 'メールアドレス{{identifier}}がアカウントに追加されました。',\n      },\n      emailLink: {\n        formHint: 'このメールアドレスには検証リンクが含まれたメールが送信されます。',\n        formSubtitle: '{{identifier}}に送信されたメールの検証リンクをクリックしてください。',\n        formTitle: '検証リンク',\n        resendButton: 'リンクを再送信',\n        successMessage: 'メールアドレス{{identifier}}がアカウントに追加されました。',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}}はこのアカウントから削除されます。',\n        messageLine2: 'このメールアドレスを使用してのサインインはできなくなります。',\n        successMessage: '{{emailAddress}}がアカウントから削除されました。',\n        title: 'メールアドレスの削除',\n      },\n      title: 'メールアドレスの追加',\n      verifyTitle: 'メールアドレスの確認',\n    },\n    formButtonPrimary__add: '追加',\n    formButtonPrimary__continue: '続行',\n    formButtonPrimary__finish: '完了',\n    formButtonPrimary__remove: '削除',\n    formButtonPrimary__save: '保存',\n    formButtonReset: 'キャンセル',\n    mfaPage: {\n      formHint: '追加する方法を選択してください。',\n      title: '二段階認証の追加',\n    },\n    mfaPhoneCodePage: {\n      backButton: '既存の番号を使用',\n      primaryButton__addPhoneNumber: '電話番号を追加',\n      removeResource: {\n        messageLine1: '{{identifier}}はサインイン時に認証コードを受け取らなくなります。',\n        messageLine2: 'アカウントのセキュリティが低下する可能性があります。本当に削除しますか？',\n        successMessage: '{{mfaPhoneCode}}のSMSコード二段階認証が削除されました。',\n        title: '二段階認証の削除',\n      },\n      subtitle__availablePhoneNumbers: 'SMSコード二段階認証のために登録する電話番号を選択してください。',\n      subtitle__unavailablePhoneNumbers: 'SMSコード二段階認証のために利用可能な電話番号はありません。',\n      successMessage1:\n        'When signing in, you will need to enter a verification code sent to this phone number as an additional step.',\n      successMessage2:\n        'Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.',\n      successTitle: 'SMSコード認証が有効になりました',\n      title: 'SMSコード認証の追加',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: '代わりにQRコードをスキャンする',\n        buttonUnableToScan__nonPrimary: 'QRコードをスキャンできませんか？',\n        infoText__ableToScan:\n          '認証アプリで新しいサインイン方法を設定し、以下のQRコードをスキャンしてアカウントとリンクさせます。',\n        infoText__unableToScan: '認証アプリで新しいサインイン方法を設定し、以下のキーを入力してください。',\n        inputLabel__unableToScan1:\n          'タイムベースまたはワンタイムパスワードが有効になっていることを確認し、アカウントのリンクを完了してください。',\n        inputLabel__unableToScan2:\n          'また、認証アプリがTOTP URIをサポートしている場合は、完全なURIをコピーすることもできます。',\n      },\n      removeResource: {\n        messageLine1: 'この認証アプリからの検証コードは、サインイン時には不要になります。',\n        messageLine2: 'アカウントのセキュリティが低下する可能性があります。本当に削除しますか？',\n        successMessage: '認証アプリを使用した二段階認証が削除されました。',\n        title: '二段階認証の削除',\n      },\n      successMessage:\n        '二段階認証が有効になりました。サインイン時には、この認証アプリからの検証コードを追加のステップとして入力する必要があります。',\n      title: '認証アプリの追加',\n      verifySubtitle: '認証アプリで生成された検証コードを入力してください。',\n      verifyTitle: '検証コード',\n    },\n    mobileButton__menu: 'メニュー',\n    navbar: {\n      account: 'プロファイル',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'アカウント情報管理',\n      security: 'セキュリティ',\n      title: 'アカウント',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: undefined,\n        title: undefined,\n      },\n      subtitle__rename: undefined,\n      title__rename: undefined,\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        '古いパスワードを使用している可能性のあるすべてのデバイスからサインアウトすることをお勧めします。',\n      readonly: 'プロファイル情報はエンタープライズ接続によって提供されており、編集できません。',\n      successMessage__set: 'パスワードが設定されました。',\n      successMessage__signOutOfOtherSessions: '他のすべてのデバイスからサインアウトされました。',\n      successMessage__update: 'パスワードが更新されました。',\n      title__set: 'パスワードの設定',\n      title__update: 'パスワードの更新',\n    },\n    phoneNumberPage: {\n      infoText: 'この電話番号には検証リンクが含まれたテキストメッセージが送信されます。',\n      removeResource: {\n        messageLine1: '{{identifier}}はこのアカウントから削除されます。',\n        messageLine2: 'この電話番号を使用してのサインインはできなくなります。',\n        successMessage: '{{phoneNumber}}がアカウントから削除されました。',\n        title: '電話番号の削除',\n      },\n      successMessage: '{{identifier}}がアカウントに追加されました。',\n      title: '電話番号の追加',\n      verifySubtitle: '{{identifier}}に送信された検証コードを入力してください',\n      verifyTitle: '電話番号の確認',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: '10MB未満のJPG、PNG、GIF、またはWEBP画像をアップロードしてください',\n      imageFormDestructiveActionSubtitle: '画像の削除',\n      imageFormSubtitle: '画像のアップロード',\n      imageFormTitle: 'プロフィール画像',\n      readonly: 'プロファイル情報はエンタープライズ接続によって提供されており、編集できません。',\n      successMessage: 'プロフィールが更新されました。',\n      title: 'プロフィールの更新',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'デバイスからサインアウト',\n        title: 'アクティブなデバイス',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: '再試行',\n        actionLabel__reauthorize: '今すぐ認証',\n        destructiveActionTitle: '削除',\n        primaryButton: 'アカウントを連携する',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          '必要なスコープが更新され、機能が制限されている可能性があります。問題を避けるために、このアプリケーションを再認証してください。',\n        title: '連携アカウント',\n      },\n      dangerSection: {\n        deleteAccountButton: 'アカウントの削除',\n        title: 'アカウントの終了',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'メールアドレスの削除',\n        detailsAction__nonPrimary: 'プライマリに設定する',\n        detailsAction__primary: '確認を完了する',\n        detailsAction__unverified: '確認を完了する',\n        primaryButton: 'メールアドレスの追加',\n        title: 'メールアドレス',\n      },\n      enterpriseAccountsSection: {\n        title: 'エンタープライズアカウント',\n      },\n      headerTitle__account: 'アカウント',\n      headerTitle__security: 'セキュリティ',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'コードを再生成',\n          headerTitle: 'バックアップコード',\n          subtitle__regenerate:\n            '安全な新しいバックアップコードを取得します。以前のバックアップコードは削除され、使用することはできません。',\n          title__regenerate: 'バックアップコードの再生成',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'デフォルトに設定',\n          destructiveActionLabel: '電話番号の削除',\n        },\n        primaryButton: '二段階認証を追加する',\n        title: '二段階認証',\n        totp: {\n          destructiveActionTitle: '削除',\n          headerTitle: '認証アプリケーション',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: undefined,\n        menuAction__rename: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'パスワードを設定する',\n        primaryButton__updatePassword: 'パスワードを変更する',\n        title: 'パスワード',\n      },\n      phoneNumbersSection: {\n        destructiveAction: '電話番号の削除',\n        detailsAction__nonPrimary: 'プライマリに設定する',\n        detailsAction__primary: '確認を完了する',\n        detailsAction__unverified: '確認を完了する',\n        primaryButton: '電話番号の追加',\n        title: '電話番号',\n      },\n      profileSection: {\n        primaryButton: undefined,\n        title: 'プロフィール',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'ユーザー名の設定',\n        primaryButton__updateUsername: 'ユーザー名の変更',\n        title: 'ユーザー名',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'ウォレットの削除',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3ウォレット',\n        title: 'Web3ウォレット',\n      },\n    },\n    usernamePage: {\n      successMessage: 'ユーザー名が更新されました。',\n      title__set: 'ユーザー名の更新',\n      title__update: 'ユーザー名の更新',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}}はこのアカウントから削除されます。',\n        messageLine2: 'このWeb3ウォレットを使用してのサインインはできなくなります。',\n        successMessage: '{{web3Wallet}}がアカウントから削除されました。',\n        title: 'Web3ウォレットの削除',\n      },\n      subtitle__availableWallets: 'アカウントに接続するWeb3ウォレットを選択してください。',\n      subtitle__unavailableWallets: '利用可能なWeb3ウォレットはありません。',\n      successMessage: 'ウォレットがアカウントに追加されました。',\n      title: 'Web3ウォレットの追加',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBACE;AAAA,MACF,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCAAiC;AAAA,MACjC,mCAAmC;AAAA,MACnC,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBAAwB;AAAA,QACxB,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}