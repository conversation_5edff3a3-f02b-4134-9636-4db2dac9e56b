{"version": 3, "sources": ["../src/nb-NO.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const nbNO: LocalizationResource = {\n  locale: 'nb-NO',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Tilbake',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Standard',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Annen imitators enhet',\n  badge__primary: 'Primær',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Krever handling',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Denne enheten',\n  badge__unverified: 'Ikke verifisert',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Brukerens enhet',\n  badge__you: 'Du',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Opprett organisasjon',\n    invitePage: {\n      formButtonReset: 'Hopp over',\n    },\n    title: 'Opprett organisasjon',\n  },\n  dates: {\n    lastDay: \"I går kl. {{ date | timeString('nb-NO') }}\",\n    next6Days: \"{{ date | weekday('nb-NO','long') }} kl. {{ date | timeString('nb-NO') }}\",\n    nextDay: \"I morgen kl. {{ date | timeString('nb-NO') }}\",\n    numeric: \"{{ date | numeric('nb-NO') }}\",\n    previous6Days: \"Sist {{ date | weekday('nb-NO','long') }} kl. {{ date | timeString('nb-NO') }}\",\n    sameDay: \"I dag kl. {{ date | timeString('nb-NO') }}\",\n  },\n  dividerText: 'eller',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Bruk en annen metode',\n  footerPageLink__help: 'Hjelp',\n  footerPageLink__privacy: 'Personvern',\n  footerPageLink__terms: 'Vilkår',\n  formButtonPrimary: 'Fortsett',\n  formButtonPrimary__verify: 'Verifiser',\n  formFieldAction__forgotPassword: 'Glemt passord?',\n  formFieldError__matchingPasswords: 'Passordene stemmer overens.',\n  formFieldError__notMatchingPasswords: 'Passordene stemmer ikke overens.',\n  formFieldError__verificationLinkExpired: 'Verifikasjonslenken har utløpt. Vennligst be om en ny lenke.',\n  formFieldHintText__optional: 'Valgfritt',\n  formFieldHintText__slug: 'En slug er en menneskelesbar ID som må være unik. Den brukes ofte i URL-er.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Slett konto',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses:\n    'Skriv inn eller lim inn én eller flere e-postadresser, separert med mellomrom eller komma',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: undefined,\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Skru på automatiske invitasjoner for dette domenet',\n  formFieldLabel__backupCode: 'Sikkerhetskode',\n  formFieldLabel__confirmDeletion: 'Bekreftelse',\n  formFieldLabel__confirmPassword: 'Bekreft passord',\n  formFieldLabel__currentPassword: 'Nåværende passord',\n  formFieldLabel__emailAddress: 'E-postadresse',\n  formFieldLabel__emailAddress_username: 'E-postadresse eller brukernavn',\n  formFieldLabel__emailAddresses: 'E-postadresser',\n  formFieldLabel__firstName: 'Fornavn',\n  formFieldLabel__lastName: 'Etternavn',\n  formFieldLabel__newPassword: 'Nytt passord',\n  formFieldLabel__organizationDomain: 'Domene',\n  formFieldLabel__organizationDomainDeletePending: 'Slett ventende invitasjoner og forslag',\n  formFieldLabel__organizationDomainEmailAddress: 'Verifikasjon e-postadresse',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Oppgi en e-postadresse under dette domenet for å motta en kode og verifisere domenet.',\n  formFieldLabel__organizationName: 'Organisasjonsnavn',\n  formFieldLabel__organizationSlug: 'Slug URL',\n  formFieldLabel__passkeyName: undefined,\n  formFieldLabel__password: 'Passord',\n  formFieldLabel__phoneNumber: 'Telefonnummer',\n  formFieldLabel__role: 'Rolle',\n  formFieldLabel__signOutOfOtherSessions: 'Logg ut fra alle andre enheter',\n  formFieldLabel__username: 'Brukernavn',\n  impersonationFab: {\n    action__signOut: 'Logg ut',\n    title: 'Logget inn som {{identifier}}',\n  },\n  maintenanceMode: undefined,\n  membershipRole__admin: 'Administrator',\n  membershipRole__basicMember: 'Medlem',\n  membershipRole__guestMember: 'Gjest',\n  organizationList: {\n    action__createOrganization: 'Lag organisasjon',\n    action__invitationAccept: 'Bli med',\n    action__suggestionsAccept: 'Spør om å bli med',\n    createOrganization: 'Lag Organisasjon',\n    invitationAcceptedLabel: 'Blitt med',\n    subtitle: 'for å fortsette til {{applicationName}}',\n    suggestionsAcceptedLabel: 'Venter på godkjenning',\n    title: 'Velg en bruker',\n    titleWithoutPersonal: 'Velg en organiasjon',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Automatisk invitasjon',\n    badge__automaticSuggestion: 'Automatisk forslag',\n    badge__manualInvitation: 'Ingen automatisk registrering',\n    badge__unverified: 'Uverifisert',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Legg til domenet som skal verifiseres. Brukere med e-postadresser på dette domenet kan automatisk bli med i organisasjonen eller be om å få bli med.',\n      title: 'Legg til domene',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed: 'Invitasjonene kunne ikke sendes. Fiks følgende og prøv igjen:',\n      formButtonPrimary__continue: 'Send invitasjoner',\n      selectDropdown__role: 'Velg rolle',\n      subtitle: 'Inviter nye medlemmer til denne organisasjonen',\n      successMessage: 'Invitasjoner er sendt',\n      title: 'Inviter medlemmer',\n    },\n    membersPage: {\n      action__invite: 'Inviter',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Fjern medlem',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'Ble med',\n        tableHeader__role: 'Rolle',\n        tableHeader__user: 'Bruker',\n      },\n      detailsTitle__emptyRow: 'Ingen medlemmer å vise',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Inviter brukere ved å koble et e-postdomene til organisasjonen din. Alle som registrerer seg med et matchende e-postdomene vil kunne bli med i organisasjonen når som helst.',\n          headerTitle: 'Automatiske invitasjoner',\n          primaryButton: 'Administrer verifiserte domener',\n        },\n        table__emptyRow: 'Ingen invitasjoner å vise',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Tilbakekall invitasjon',\n        tableHeader__invited: 'Invitert',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Brukere som registrerer seg med et matchende e-postdomene, vil kunne se et forslag om å be om å bli med i organisasjonen din.',\n          headerTitle: 'Automatiske forslag',\n          primaryButton: 'Administrer verifiserte domener',\n        },\n        menuAction__approve: 'Godta',\n        menuAction__reject: 'Avslå',\n        tableHeader__requested: 'Tilgangsforespøsler',\n        table__emptyRow: 'Ingen forsespørsler å vise',\n      },\n      start: {\n        headerTitle__invitations: 'Invitasjoner',\n        headerTitle__members: 'Medlemmer',\n        headerTitle__requests: 'Forespørsler',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Administrer organisasjonen din.',\n      general: 'Generelt',\n      members: 'Medlemmer',\n      title: 'Organisasjon',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Skriv \"{{organizationName}}\" under for å bekrefte.',\n          messageLine1: 'Er du sikker på at du vil slette denne organisasjonen?',\n          messageLine2: 'Denne handlingen er permanent og kan ikke reverseres.',\n          successMessage: 'Du har slettet organisasjonen.',\n          title: 'Slett organisasjonen',\n        },\n        leaveOrganization: {\n          actionDescription: 'Skriv \"{{organizationName}}\" under for å fortsette.',\n          messageLine1:\n            'Er du sikker på at du vil forlate denne organisasjonen? Du vil miste tilgangen til denne organisasjonen og dens applikasjoner.',\n          messageLine2: 'Denne handlingen er permanent og kan ikke reverseres.',\n          successMessage: 'Du har forlatt organisasjonen.',\n          title: 'Forlat organisasjonen',\n        },\n        title: 'Fare',\n      },\n      domainSection: {\n        menuAction__manage: 'Administrer',\n        menuAction__remove: 'Slett',\n        menuAction__verify: 'Verifiser',\n        primaryButton: 'Legg til domene',\n        subtitle:\n          'Tillat brukere å bli med i organisasjonen automatisk eller be om å bli med basert på et verifisert e-postdomene.',\n        title: 'Verifiserte domener',\n      },\n      successMessage: 'Organisasjonen er oppdatert.',\n      title: 'Organisasjonsprofil',\n    },\n    removeDomainPage: {\n      messageLine1: 'E-postdomenet {{domain}} vil bli fjernet.',\n      messageLine2: 'Brukere vil ikke kunne bli med i organisasjonen automatisk etter dette.',\n      successMessage: '{{domain}} har blitt fjernet.',\n      title: 'Fjern domene',\n    },\n    start: {\n      headerTitle__general: 'Generelt',\n      headerTitle__members: 'Medlemmer',\n      profileSection: {\n        primaryButton: undefined,\n        title: 'Organisasjonsprofil',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Å fjerne dette domenet vil påvirke inviterte brukere.',\n        removeDomainActionLabel__remove: 'Fjern domene',\n        removeDomainSubtitle: 'Fjern domenet fra dine verifiserte domener',\n        removeDomainTitle: 'Fjern domene',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Brukere blir automatisk invitert til å bli med i organisasjonen når de registrerer seg og kan bli med når som helst.',\n        automaticInvitationOption__label: 'Automatiske invitasjoner',\n        automaticSuggestionOption__description:\n          'Brukere mottar et forslag om å be om å bli med, men må godkjennes av en administrator før de kan bli med i organisasjonen.',\n        automaticSuggestionOption__label: 'Automatiske forslag',\n        calloutInfoLabel: 'Å endre påmeldingsmodus vil kun påvirke nye brukere.',\n        calloutInvitationCountLabel: 'Ventende invitasjoner sendt til brukere: {{count}}',\n        calloutSuggestionCountLabel: 'Ventende forslag sendt til brukere: {{count}}',\n        manualInvitationOption__description: 'Brukere kan kun bli invitert manuelt til organisasjonen.',\n        manualInvitationOption__label: 'Ingen automatisk registrering',\n        subtitle: 'Velg hvordan brukere fra dette domenet kan bli med i organisasjonen.',\n      },\n      start: {\n        headerTitle__danger: 'Fare',\n        headerTitle__enrollment: 'Registreringsalternativer',\n      },\n      subtitle: 'Domenet {{domain}} har blitt verifisert. Fortsett ved å velge registreringsmodus.',\n      title: 'Oppdater {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Skriv inn verifiseringskoden som ble sendt til e-postadressen din',\n      formTitle: 'Verifiseringskode',\n      resendButton: 'Ikke mottatt kode? Send på nytt',\n      subtitle: 'Domenet {{domainName}} må verifiseres gjennom e-post.',\n      subtitleVerificationCodeScreen:\n        'En verifiseringskode har blitt sendt til {{emailAddress}}. Skriv inn koden for å fortsette.',\n      title: 'Verifiser domene',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Opprett organisasjon',\n    action__invitationAccept: 'Bli med',\n    action__manageOrganization: 'Administrer organisasjon',\n    action__suggestionsAccept: 'Spør om å bli med',\n    notSelected: 'Ingen organisasjon valgt',\n    personalWorkspace: 'Personlig arbeidsområde',\n    suggestionsAcceptedLabel: 'Venter på godkjenning',\n  },\n  paginationButton__next: 'Neste',\n  paginationButton__previous: 'Forrige',\n  paginationRowText__displaying: 'Viser',\n  paginationRowText__of: 'av',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Legg til konto',\n      action__signOutAll: 'Logg ut av alle kontoer',\n      subtitle: 'Velg kontoen du ønsker å fortsette med.',\n      title: 'Velg konto',\n    },\n    alternativeMethods: {\n      actionLink: 'Få hjelp',\n      actionText: 'Har du ingen av disse?',\n      blockButton__backupCode: 'Bruk en sikkerhetskopi-kode',\n      blockButton__emailCode: 'Send e-postkode til {{identifier}}',\n      blockButton__emailLink: 'Send lenke til {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Logg inn med passordet ditt',\n      blockButton__phoneCode: 'Send SMS-kode til {{identifier}}',\n      blockButton__totp: 'Bruk autentiseringsappen din',\n      getHelp: {\n        blockButton__emailSupport: 'Kontakt kundestøtte via e-post',\n        content:\n          'Hvis du har problemer med å logge inn på kontoen din, kan du sende oss en e-post, og vi vil jobbe med deg for å gjenopprette tilgangen så snart som mulig.',\n        title: 'Få hjelp',\n      },\n      subtitle: 'Opplever du problemer? Du kan bruke hvilken som helst av disse metodene for å logge inn.',\n      title: 'Bruk en annen metode',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'for å fortsette til {{applicationName}}',\n      title: 'Skriv inn en sikkerhetskopi-kode',\n    },\n    emailCode: {\n      formTitle: 'Verifiseringskode',\n      resendButton: 'Send kode på nytt',\n      subtitle: 'for å fortsette til {{applicationName}}',\n      title: 'Sjekk e-posten din',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: 'Gå tilbake til den opprinnelige fanen for å fortsette.',\n        title: 'Denne verifiseringslenken er utløpt',\n      },\n      failed: {\n        subtitle: 'Gå tilbake til den opprinnelige fanen for å fortsette.',\n        title: 'Denne verifiseringslenken er ugyldig',\n      },\n      formSubtitle: 'Bruk verifiseringslenken som er sendt til e-postadressen din',\n      formTitle: 'Verifiseringslenke',\n      loading: {\n        subtitle: 'Du blir omdirigert snart',\n        title: 'Logger inn...',\n      },\n      resendButton: 'Send lenke på nytt',\n      subtitle: 'for å fortsette til {{applicationName}}',\n      title: 'Sjekk e-posten din',\n      unusedTab: {\n        title: 'Du kan lukke denne fanen',\n      },\n      verified: {\n        subtitle: 'Du blir omdirigert snart',\n        title: 'Innloggingen var vellykket',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Gå tilbake til den opprinnelige fanen for å fortsette',\n        subtitleNewTab: 'Gå tilbake til den nyåpnede fanen for å fortsette',\n        titleNewTab: 'Logget inn på en annen fane',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Tilbakestill passord-kode',\n      resendButton: 'Send kode på nytt',\n      subtitle: 'for å tilbakestille passordet ditt',\n      subtitle_email: 'Først, skriv inn koden som ble sendt til e-posten din',\n      subtitle_phone: 'Først, skriv inn koden som ble sendt til telefonen din',\n      title: 'Tilbakestill passord',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Tilbakestill passordet ditt',\n      label__alternativeMethods: 'Eller logg inn med en annen metode.',\n      title: 'Glemt passord?',\n    },\n    noAvailableMethods: {\n      message: 'Kan ikke fortsette med innloggingen. Det er ingen tilgjengelige autentiseringsfaktorer.',\n      subtitle: 'En feil oppstod',\n      title: 'Kan ikke logge inn',\n    },\n    passkey: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Bruk en annen metode',\n      subtitle: 'for å fortsette til {{applicationName}}',\n      title: 'Skriv inn passordet ditt',\n    },\n    passwordPwned: {\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: 'Verifiseringskode',\n      resendButton: 'Send kode på nytt',\n      subtitle: 'for å fortsette til {{applicationName}}',\n      title: 'Sjekk telefonen din',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Verifiseringskode',\n      resendButton: 'Send kode på nytt',\n      subtitle: undefined,\n      title: 'Sjekk telefonen din',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Tilbakestill passordet',\n      requiredMessage:\n        'En konto eksisterer allerede med en uverifisert e-postadresse. Vennligst tilbakestill passordet ditt av sikkerhetshensyn.',\n      successMessage: 'Passordet ditt er blitt tilbakestilt. Logger deg inn, vennligst vent et øyeblikk.',\n      title: 'Tilbakestill passordet',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Vi må bekrefte identiteten din før vi tilbakestiller passordet ditt.',\n    },\n    start: {\n      actionLink: 'Opprett konto',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Bruk e-post',\n      actionLink__use_email_username: 'Bruk e-post eller brukernavn',\n      actionLink__use_passkey: undefined,\n      actionLink__use_phone: 'Bruk telefon',\n      actionLink__use_username: 'Bruk brukernavn',\n      actionText: 'Ingen konto?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'for å fortsette til {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Logg inn',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Verifiseringskode',\n      subtitle: undefined,\n      title: 'To-trinns verifisering',\n    },\n  },\n  signInEnterPasswordTitle: 'Skriv inn passordet ditt',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Logg inn',\n      actionText: 'Har du allerede en konto?',\n      subtitle: 'for å fortsette til {{applicationName}}',\n      title: 'Fyll ut manglende felt',\n    },\n    emailCode: {\n      formSubtitle: 'Skriv inn verifiseringskoden som er sendt til e-postadressen din',\n      formTitle: 'Verifiseringskode',\n      resendButton: 'Send kode på nytt',\n      subtitle: 'for å fortsette til {{applicationName}}',\n      title: 'Verifiser e-posten din',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: 'Bruk verifiseringslenken som er sendt til e-postadressen din',\n      formTitle: 'Verifiseringslenke',\n      loading: {\n        title: 'Registrerer deg...',\n      },\n      resendButton: 'Send lenke på nytt',\n      subtitle: 'for å fortsette til {{applicationName}}',\n      title: 'Verifiser e-posten din',\n      verified: {\n        title: 'Registreringen var vellykket',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Gå tilbake til den nylig åpnede fanen for å fortsette',\n        subtitleNewTab: 'Gå tilbake til forrige fane for å fortsette',\n        title: 'E-posten ble verifisert',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Skriv inn verifiseringskoden som er sendt til telefonnummeret ditt',\n      formTitle: 'Verifiseringskode',\n      resendButton: 'Send kode på nytt',\n      subtitle: 'for å fortsette til {{applicationName}}',\n      title: 'Verifiser telefonen din',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Logg inn',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: 'Har du allerede en konto?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'for å fortsette til {{applicationName}}',\n      subtitleCombined: 'for å fortsette til {{applicationName}}',\n      title: 'Opprett kontoen din',\n      titleCombined: 'Opprett kontoen din',\n    },\n  },\n  socialButtonsBlockButton: 'Fortsett med {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'Registreringen mislyktes på grunn av mislykkede sikkerhetsvalideringer. Vennligst oppdater siden og prøv igjen, eller ta kontakt med brukerstøtte for mer hjelp.',\n    captcha_unavailable:\n      'Registreringen mislyktes på grunn av mislykkede bot-valideringer. Vennligst oppdater siden og prøv igjen, eller ta kontakt med brukerstøtte for mer hjelp.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: 'Vi klarte ikke finne en konto med disse detaljene.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'E-postadressen må være en gyldig e-postadresse',\n    form_param_format_invalid__phone_number: 'Telefonnummeret må være i et gyldig internasjonalt format',\n    form_param_max_length_exceeded__first_name: 'Fornavn kan ikke være lengre enn 256 bokstaver.',\n    form_param_max_length_exceeded__last_name: 'Etternavn kan ikke være lengre enn 256 bokstaver.',\n    form_param_max_length_exceeded__name: 'Navn kan ikke være lengre enn 256 bokstaver.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: 'Passordet ditt er ikke sterkt nok.',\n    form_password_pwned:\n      'Dette passordet er funnet som en del av et datainnbrudd og kan ikke brukes. Vennligst prøv et annet passord.',\n    form_password_pwned__sign_in: undefined,\n    form_password_size_in_bytes_exceeded:\n      'Passordet ditt har overskredet maksimalt antall byte tillatt. Vennligst forkort det eller fjern noen spesialtegn.',\n    form_password_validation_failed: 'Feil passord',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: 'You cannot delete your last identification.',\n    not_allowed_access:\n      \"E-postadressen eller telefonnummeret ditt er ikke tillatt for registrering. Dette kan være på grunn av bruk av '+', '=', '#' eller '.' i e-postadressen din, bruk av et domenn som er tilknyttet en midlertidig e-posttjeneste, eller eksplisitt blokkering. Hvis du mener dette er en feil, vennligst kontakt støtte.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: 'mindre enn {{length}} tegn',\n      minimumLength: '{{length}} eller flere tegn',\n      requireLowercase: 'en liten bokstav',\n      requireNumbers: 'et tall',\n      requireSpecialCharacter: 'et spesialtegn',\n      requireUppercase: 'en stor bokstav',\n      sentencePrefix: 'Passordet ditt må inneholde',\n    },\n    phone_number_exists: 'Dette telefonnummeret er allerede i bruk. Vennligst bruk et annet telefonnummer.',\n    session_exists: 'Du er allerede logget inn.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Passordet ditt fungerer, men det kan være sterkere. Prøv å legge til flere tegn.',\n      goodPassword: 'Godt jobbet. Dette er et utmerket passord.',\n      notEnough: 'Passordet ditt er ikke sterkt nok.',\n      suggestions: {\n        allUppercase: 'Stor bokstav på noen, men ikke alle bokstaver.',\n        anotherWord: 'Legg til flere ord som er mindre vanlige.',\n        associatedYears: 'Unngå år som er knyttet til deg.',\n        capitalization: 'Sett stor bokstav på mer enn den første bokstaven.',\n        dates: 'Unngå datoer og år som er knyttet til deg.',\n        l33t: \"Unngå forutsigbare bokstavbytter som '@' for 'a'.\",\n        longerKeyboardPattern: 'Bruk lengre tastaturmønstre og endre skrivretning flere ganger.',\n        noNeed: 'Du kan lage sterke passord uten å bruke symboler, tall eller store bokstaver.',\n        pwned: 'Hvis du bruker dette passordet andre steder, bør du endre det.',\n        recentYears: 'Unngå nylige år.',\n        repeated: 'Unngå gjentatte ord og tegn.',\n        reverseWords: 'Unngå omvendte stavelser av vanlige ord.',\n        sequences: 'Unngå vanlige tegnsekvenser.',\n        useWords: 'Bruk flere ord, men unngå vanlige fraser.',\n      },\n      warnings: {\n        common: 'Dette er et vanlig brukt passord.',\n        commonNames: 'Vanlige navn og etternavn er lett å gjette.',\n        dates: 'Datoer er lett å gjette.',\n        extendedRepeat: 'Gjentatte tegnmønstre som \"abcabcabc\" er lett å gjette.',\n        keyPattern: 'Korte tastaturmønstre er lett å gjette.',\n        namesByThemselves: 'Enkelt navn eller etternavn er lett å gjette.',\n        pwned: 'Passordet ditt ble eksponert i et datainnbrudd på internett.',\n        recentYears: 'Nylige år er lett å gjette.',\n        sequences: 'Vanlige tegnsekvenser som \"abc\" er lett å gjette.',\n        similarToCommon: 'Dette ligner på et vanlig brukt passord.',\n        simpleRepeat: 'Gjentatte tegn som \"aaa\" er lett å gjette.',\n        straightRow: 'Rette rader med tastene på tastaturet ditt er lett å gjette.',\n        topHundred: 'Dette er et ofte brukt passord.',\n        topTen: 'Dette er et mye brukt passord.',\n        userInputs: 'Det bør ikke være personlige eller sidetilknyttede data.',\n        wordByItself: 'Enkeltord er lett å gjette.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Legg til konto',\n    action__manageAccount: 'Administrer konto',\n    action__signOut: 'Logg ut',\n    action__signOutAll: 'Logg ut av alle kontoer',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Kopiert!',\n      actionLabel__copy: 'Kopier alle',\n      actionLabel__download: 'Last ned .txt',\n      actionLabel__print: 'Skriv ut',\n      infoText1: 'Sikkerhetskoder vil bli aktivert for denne kontoen.',\n      infoText2:\n        'Hold sikkerhetskodene hemmelige og oppbevar dem sikkert. Du kan generere nye sikkerhetskoder hvis du mistenker at de er kompromittert.',\n      subtitle__codelist: 'Oppbevar dem sikkert og hold dem hemmelige.',\n      successMessage:\n        'Sikkerhetskoder er nå aktivert. Du kan bruke en av disse til å logge inn på kontoen din hvis du mister tilgangen til autentiseringsenheten. Hver kode kan bare brukes én gang.',\n      successSubtitle:\n        'Du kan bruke en av disse til å logge inn på kontoen din hvis du mister tilgangen til autentiseringsenheten.',\n      title: 'Legg til sikkerhetskopieringskodeverifisering',\n      title__codelist: 'Sikkerhetskoder',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Velg en tilbyder for å koble til kontoen din.',\n      formHint__noAccounts: 'Det er ingen tilgjengelige eksterne konto-tilbydere.',\n      removeResource: {\n        messageLine1: '{{identifier}} vil bli fjernet fra denne kontoen.',\n        messageLine2:\n          'Du vil ikke lenger kunne bruke denne tilknyttede kontoen, og eventuelle avhengige funksjoner vil ikke lenger fungere.',\n        successMessage: '{{connectedAccount}} har blitt fjernet fra kontoen din.',\n        title: 'Fjern tilknyttet konto',\n      },\n      socialButtonsBlockButton: 'Koble til {{provider|titleize}}-konto',\n      successMessage: 'Tilbyderen har blitt lagt til kontoen din.',\n      title: 'Legg til tilknyttet konto',\n    },\n    deletePage: {\n      actionDescription: 'Skriv inn \"Slett konto\" under for å fortsette.',\n      confirm: 'Slett konto',\n      messageLine1: 'Er du sikker på at du vil slette kontoen din?',\n      messageLine2: 'Denne handlingen er permanent og kan ikke reverseres.',\n      title: 'Slett konto',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'En e-post med en verifiseringskode vil bli sendt til denne e-postadressen.',\n        formSubtitle: 'Skriv inn verifiseringskoden som er sendt til {{identifier}}',\n        formTitle: 'Verifiseringskode',\n        resendButton: 'Send kode på nytt',\n        successMessage: 'E-posten {{identifier}} har blitt lagt til kontoen din.',\n      },\n      emailLink: {\n        formHint: 'En e-post med en verifiseringslenke vil bli sendt til denne e-postadressen.',\n        formSubtitle: 'Klikk på verifiseringslenken i e-posten sendt til {{identifier}}',\n        formTitle: 'Verifiseringslenke',\n        resendButton: 'Send lenke på nytt',\n        successMessage: 'E-posten {{identifier}} har blitt lagt til kontoen din.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} vil bli fjernet fra denne kontoen.',\n        messageLine2: 'Du vil ikke lenger kunne logge inn med denne e-postadressen.',\n        successMessage: '{{emailAddress}} har blitt fjernet fra kontoen din.',\n        title: 'Fjern e-postadresse',\n      },\n      title: 'Legg til e-postadresse',\n      verifyTitle: 'Verifiser e-postadresse',\n    },\n    formButtonPrimary__add: 'Legg til',\n    formButtonPrimary__continue: 'Fortsett',\n    formButtonPrimary__finish: 'Fullfør',\n    formButtonPrimary__remove: 'Fjern',\n    formButtonPrimary__save: 'Lagre',\n    formButtonReset: 'Avbryt',\n    mfaPage: {\n      formHint: 'Velg en metode for å legge til.',\n      title: 'Legg til to-trinns verifisering',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Bruk eksisterende nummer',\n      primaryButton__addPhoneNumber: 'Legg til et telefonnummer',\n      removeResource: {\n        messageLine1: '{{identifier}} vil ikke lenger motta verifiseringskoder ved pålogging.',\n        messageLine2: 'Kontoen din kan bli mindre sikker. Er du sikker på at du vil fortsette?',\n        successMessage: 'SMS-kode to-trinns verifisering er fjernet for {{mfaPhoneCode}}',\n        title: 'Fjern to-trinns verifisering',\n      },\n      subtitle__availablePhoneNumbers:\n        'Velg et telefonnummer for å registrere deg for SMS-kode to-trinns verifisering.',\n      subtitle__unavailablePhoneNumbers:\n        'Det er ingen tilgjengelige telefonnummer å registrere seg for SMS-kode to-trinns verifisering.',\n      successMessage1:\n        'Ved innlogging vil du måtte skrive inn en verifiseringskode sendt til dette telfonnummeret som et tilleggssteg.',\n      successMessage2:\n        'Ta vare på disse reservekodene og oppbevar dem på et trygt sted. Hvis du mister tilgang til autentiseringsenheten din kan du bruke reservekodene for å logge inn.',\n      successTitle: 'SMS-kodeverifisering lagt til',\n      title: 'Legg til SMS-kodeverifisering',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Skan QR-kode i stedet',\n        buttonUnableToScan__nonPrimary: 'Kan ikke skanne QR-kode?',\n        infoText__ableToScan:\n          'Sett opp en ny innloggingsmetode i autentiseringsappen din og skann følgende QR-kode for å koble den til kontoen din.',\n        infoText__unableToScan:\n          'Sett opp en ny innloggingsmetode i autentiseringsappen og skriv inn nøkkelen som er oppgitt nedenfor.',\n        inputLabel__unableToScan1:\n          'Sørg for at tidsbaserte eller engangspassord er aktivert, og fullfør deretter koblingen av kontoen din.',\n        inputLabel__unableToScan2:\n          'Alternativt, hvis autentiseringsappen din støtter TOTP URI-er, kan du også kopiere hele URI-en.',\n      },\n      removeResource: {\n        messageLine1: 'Verifiseringskoder fra denne autentiseringsappen vil ikke lenger være påkrevd ved pålogging.',\n        messageLine2: 'Kontoen din kan bli mindre sikker. Er du sikker på at du vil fortsette?',\n        successMessage: 'To-trinns verifisering via autentiseringsappen er fjernet.',\n        title: 'Fjern to-trinns verifisering',\n      },\n      successMessage:\n        'To-trinns verifisering er nå aktivert. Når du logger inn, må du angi en verifiseringskode fra denne autentiseringsappen som et ekstra trinn.',\n      title: 'Legg til autentiseringsapp',\n      verifySubtitle: 'Skriv inn verifiseringskoden generert av autentiseringsappen din',\n      verifyTitle: 'Verifiseringskode',\n    },\n    mobileButton__menu: 'Meny',\n    navbar: {\n      account: 'Profil',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Administrer kontoinformasjonen din.',\n      security: 'Sikkerhet',\n      title: 'Konto',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: undefined,\n        title: undefined,\n      },\n      subtitle__rename: undefined,\n      title__rename: undefined,\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Det er anbefalt å logge ut av alle de andre enhetene dine som kan ha brukt ditt gamle passord.',\n      readonly: 'Passordet ditt kan for øyeblikket ikke endres fordi du kun kan logge inn via bedriftstilkoblingen.',\n      successMessage__set: 'Passordet ditt er satt.',\n      successMessage__signOutOfOtherSessions: 'Alle andre enheter har blitt logget ut.',\n      successMessage__update: 'Passordet ditt har blitt oppdatert.',\n      title__set: 'Sett passord',\n      title__update: 'Endre passord',\n    },\n    phoneNumberPage: {\n      infoText: 'En tekstmelding med en verifiseringslenke vil bli sendt til dette telefonnummeret.',\n      removeResource: {\n        messageLine1: '{{identifier}} vil bli fjernet fra denne kontoen.',\n        messageLine2: 'Du vil ikke lenger kunne logge inn med dette telefonnummeret.',\n        successMessage: '{{phoneNumber}} har blitt fjernet fra kontoen din.',\n        title: 'Fjern telefonnummer',\n      },\n      successMessage: '{{identifier}} har blitt lagt til kontoen din.',\n      title: 'Legg til telefonnummer',\n      verifySubtitle: 'Skriv inn verifiseringskoden som ble sendt til {{identifier}}',\n      verifyTitle: 'Verifiser telefonnummer',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Last opp et JPG, PNG, GIF eller WEBP-bilde som er mindre enn 10 MB',\n      imageFormDestructiveActionSubtitle: 'Fjern bilde',\n      imageFormSubtitle: 'Last opp bilde',\n      imageFormTitle: 'Profilbilde',\n      readonly: 'Informasjonen om profilen din er levert av bedriftstilkoblingen og kan ikke redigeres.',\n      successMessage: 'Profilen din har blitt oppdatert.',\n      title: 'Oppdater profil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Logg ut fra enhet',\n        title: 'Aktive enheter',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Prøv på nytt',\n        actionLabel__reauthorize: 'Autoriser nå',\n        destructiveActionTitle: 'Fjern',\n        primaryButton: 'Koble til konto',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues',\n        title: 'Tilkoblede kontoer',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Slett konto',\n        title: 'Fare',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Fjern e-postadresse',\n        detailsAction__nonPrimary: 'Angi som primær',\n        detailsAction__primary: 'Fullfør verifisering',\n        detailsAction__unverified: 'Fullfør verifisering',\n        primaryButton: 'Legg til en e-postadresse',\n        title: 'E-postadresser',\n      },\n      enterpriseAccountsSection: {\n        title: 'Bedriftskontoer',\n      },\n      headerTitle__account: 'Konto',\n      headerTitle__security: 'Sikkerhet',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Generer koder på nytt',\n          headerTitle: 'Sikkerhetskoder',\n          subtitle__regenerate:\n            'Få en ny serie med sikre sikkerhetskoder. Tidligere sikkerhetskoder vil bli slettet og kan ikke brukes.',\n          title__regenerate: 'Generer nye sikkerhetskoder',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Angi som standard',\n          destructiveActionLabel: 'Fjern telefonnummer',\n        },\n        primaryButton: 'Legg til to-trinns verifisering',\n        title: 'To-trinns verifisering',\n        totp: {\n          destructiveActionTitle: 'Fjern',\n          headerTitle: 'Autentiseringsapplikasjon',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: undefined,\n        menuAction__rename: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Opprett passord',\n        primaryButton__updatePassword: 'Endre passord',\n        title: 'Passord',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Fjern telefonnummer',\n        detailsAction__nonPrimary: 'Angi som primær',\n        detailsAction__primary: 'Fullfør verifisering',\n        detailsAction__unverified: 'Fullfør verifisering',\n        primaryButton: 'Legg til et telefonnummer',\n        title: 'Telefonnumre',\n      },\n      profileSection: {\n        primaryButton: undefined,\n        title: 'Profil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Angi brukernavn',\n        primaryButton__updateUsername: 'Endre brukernavn',\n        title: 'Brukernavn',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Fjern lommebok',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3-lommebøker',\n        title: 'Web3-lommebøker',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Brukernavnet ditt har blitt oppdatert.',\n      title__set: 'Oppdater brukernavn',\n      title__update: 'Oppdater brukernavn',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} vil bli fjernet fra denne kontoen.',\n        messageLine2: 'Du vil ikke lenger kunne logge inn med denne web3-lommeboken.',\n        successMessage: '{{web3Wallet}} har blitt fjernet fra kontoen din.',\n        title: 'Fjern web3-lommebok',\n      },\n      subtitle__availableWallets: 'Velg en web3-lommebok for å koble til kontoen din.',\n      subtitle__unavailableWallets: 'Det er ingen tilgjengelige web3-lommebøker.',\n      successMessage: 'Lommeboken har blitt lagt til kontoen din.',\n      title: 'Legg til web3-lommebok',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBACE;AAAA,MACF,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}