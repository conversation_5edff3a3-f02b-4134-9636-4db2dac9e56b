// src/is-IS.ts
var isIS = {
  locale: "is-IS",
  apiKeys: {
    action__add: void 0,
    action__search: void 0,
    createdAndExpirationStatus__expiresOn: void 0,
    createdAndExpirationStatus__never: void 0,
    detailsTitle__emptyRow: void 0,
    formButtonPrimary__add: void 0,
    formFieldCaption__expiration__expiresOn: void 0,
    formFieldCaption__expiration__never: void 0,
    formFieldOption__expiration__180d: void 0,
    formFieldOption__expiration__1d: void 0,
    formFieldOption__expiration__1y: void 0,
    formFieldOption__expiration__30d: void 0,
    formFieldOption__expiration__60d: void 0,
    formFieldOption__expiration__7d: void 0,
    formFieldOption__expiration__90d: void 0,
    formFieldOption__expiration__never: void 0,
    formHint: void 0,
    formTitle: void 0,
    lastUsed__days: void 0,
    lastUsed__hours: void 0,
    lastUsed__minutes: void 0,
    lastUsed__months: void 0,
    lastUsed__seconds: void 0,
    lastUsed__years: void 0,
    menuAction__revoke: void 0,
    revokeConfirmation: {
      confirmationText: void 0,
      formButtonPrimary__revoke: void 0,
      formHint: void 0,
      formTitle: void 0
    }
  },
  backButton: "Til baka",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Sj\xE1lfgefi\xF0",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "\xD6nnur t\xE6ki sem herma eftir",
  badge__primary: "A\xF0al",
  badge__renewsAt: void 0,
  badge__requiresAction: "Krefst a\xF0ger\xF0a",
  badge__startsAt: void 0,
  badge__thisDevice: "\xDEetta t\xE6ki",
  badge__unverified: "\xD3sta\xF0fest",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Notendat\xE6ki",
  badge__you: "\xDE\xFA",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      pastDueNotice: void 0,
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    credit: void 0,
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDue: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Stofna samt\xF6k",
    invitePage: {
      formButtonReset: "Sleppa"
    },
    title: "Stofna samt\xF6k"
  },
  dates: {
    lastDay: "\xCD g\xE6r kl {{ date | timeString('is-IS') }}",
    next6Days: "{{ date | weekday('is-IS','long') }} kl {{ date | timeString('is-IS') }}",
    nextDay: "\xC1 morgun kl {{ date | timeString('is-IS') }}",
    numeric: "{{ date | numeric('is-IS') }}",
    previous6Days: "S\xED\xF0asta {{ date | weekday('is-IS','long') }} kl {{ date | timeString('is-IS') }}",
    sameDay: "\xCD dag kl {{ date | timeString('is-IS') }}"
  },
  dividerText: "e\xF0a",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Nota a\xF0ra a\xF0fer\xF0",
  footerPageLink__help: "Hj\xE1lp",
  footerPageLink__privacy: "Pers\xF3nuvernd",
  footerPageLink__terms: "Skilm\xE1lar",
  formButtonPrimary: "Halda \xE1fram",
  formButtonPrimary__verify: "Sta\xF0festa",
  formFieldAction__forgotPassword: "Gleymt lykilor\xF0?",
  formFieldError__matchingPasswords: "Lykilor\xF0 passa saman.",
  formFieldError__notMatchingPasswords: "Lykilor\xF0 passa ekki saman.",
  formFieldError__verificationLinkExpired: "Sta\xF0festingartengillinn er \xFAtrunninn. Vinsamlegast bi\xF0ji\xF0 um n\xFDjan tengil.",
  formFieldHintText__optional: "Valfrj\xE1lst",
  formFieldHintText__slug: "Stubbur (e. slug) er au\xF0lesanlegt au\xF0kenni sem ver\xF0ur a\xF0 vera einstakt. \xDEa\xF0 er oft nota\xF0 \xED vefsl\xF3\xF0um.",
  formFieldInputPlaceholder__apiKeyDescription: void 0,
  formFieldInputPlaceholder__apiKeyExpirationDate: void 0,
  formFieldInputPlaceholder__apiKeyName: void 0,
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Ey\xF0a a\xF0gangi",
  formFieldInputPlaceholder__emailAddress: void 0,
  formFieldInputPlaceholder__emailAddress_username: void 0,
  formFieldInputPlaceholder__emailAddresses: "d\<EMAIL>, d\<EMAIL>",
  formFieldInputPlaceholder__firstName: void 0,
  formFieldInputPlaceholder__lastName: void 0,
  formFieldInputPlaceholder__organizationDomain: void 0,
  formFieldInputPlaceholder__organizationDomainEmailAddress: void 0,
  formFieldInputPlaceholder__organizationName: void 0,
  formFieldInputPlaceholder__organizationSlug: "min-samtok",
  formFieldInputPlaceholder__password: void 0,
  formFieldInputPlaceholder__phoneNumber: void 0,
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__apiKeyDescription: void 0,
  formFieldLabel__apiKeyExpiration: void 0,
  formFieldLabel__apiKeyName: void 0,
  formFieldLabel__automaticInvitations: "Virkja sj\xE1lfvirk bo\xF0 fyrir \xFEetta l\xE9n",
  formFieldLabel__backupCode: "\xD6ryggisk\xF3\xF0i",
  formFieldLabel__confirmDeletion: "Sta\xF0festing",
  formFieldLabel__confirmPassword: "Sta\xF0festa lykilor\xF0",
  formFieldLabel__currentPassword: "N\xFAverandi lykilor\xF0",
  formFieldLabel__emailAddress: "Netfang",
  formFieldLabel__emailAddress_username: "Netfang e\xF0a notendanafn",
  formFieldLabel__emailAddresses: "Netf\xF6ng",
  formFieldLabel__firstName: "Fornafn",
  formFieldLabel__lastName: "Eftirnafn",
  formFieldLabel__newPassword: "N\xFDtt lykilor\xF0",
  formFieldLabel__organizationDomain: "L\xE9n",
  formFieldLabel__organizationDomainDeletePending: "Ey\xF0a bo\xF0um \xED bi\xF0 og till\xF6gum",
  formFieldLabel__organizationDomainEmailAddress: "Sta\xF0festingarnetfang",
  formFieldLabel__organizationDomainEmailAddressDescription: "Sl\xE1\xF0u inn netfang undir \xFEessu l\xE9ni til a\xF0 f\xE1 k\xF3\xF0a og sta\xF0festa \xFEetta l\xE9n.",
  formFieldLabel__organizationName: "Nafn",
  formFieldLabel__organizationSlug: "Stubbur (e. slug)",
  formFieldLabel__passkeyName: "Nafn lykils",
  formFieldLabel__password: "Lykilor\xF0",
  formFieldLabel__phoneNumber: "S\xEDman\xFAmer",
  formFieldLabel__role: "Hlutverk",
  formFieldLabel__signOutOfOtherSessions: "Skr\xE1 \xFAt af \xF6llum \xF6\xF0rum t\xE6kjum",
  formFieldLabel__username: "Notendanafn",
  impersonationFab: {
    action__signOut: "Skr\xE1 \xFAt",
    title: "Skr\xE1\xF0ur inn sem {{identifier}}"
  },
  maintenanceMode: "Vi\xF0 erum n\xFA \xED vi\xF0haldi, en ekki hafa \xE1hyggjur, \xFEa\xF0 \xE6tti ekki a\xF0 taka meira en nokkrar m\xEDn\xFAtur.",
  membershipRole__admin: "Stj\xF3rnandi",
  membershipRole__basicMember: "Me\xF0limur",
  membershipRole__guestMember: "Gestur",
  organizationList: {
    action__createOrganization: "Stofna samt\xF6k",
    action__invitationAccept: "Ganga \xED",
    action__suggestionsAccept: "Bi\xF0ja um a\xF0 ganga \xED",
    createOrganization: "Stofna samt\xF6k",
    invitationAcceptedLabel: "Gengi\xF0 \xED",
    subtitle: "til a\xF0 halda \xE1fram \xED {{applicationName}}",
    suggestionsAcceptedLabel: "B\xED\xF0ur sam\xFEykkis",
    title: "Veldu reikning",
    titleWithoutPersonal: "Veldu samt\xF6k"
  },
  organizationProfile: {
    apiKeysPage: {
      title: void 0
    },
    badge__automaticInvitation: "Sj\xE1lfvirk bo\xF0",
    badge__automaticSuggestion: "Sj\xE1lfvirkar till\xF6gur",
    badge__manualInvitation: "Engin sj\xE1lfvirk skr\xE1ning",
    badge__unverified: "\xD3sta\xF0fest",
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "B\xE6ttu vi\xF0 l\xE9ni til a\xF0 sta\xF0festa. Notendur me\xF0 netf\xF6ng undir \xFEessu l\xE9ni geta gengi\xF0 \xED samt\xF6kin sj\xE1lfkrafa e\xF0a be\xF0i\xF0 um a\xF0 ganga \xED.",
      title: "B\xE6ta vi\xF0 l\xE9ni"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Ekki t\xF3kst a\xF0 senda bo\xF0in. \xDEa\xF0 eru \xFEegar bi\xF0bo\xF0 fyrir eftirfarandi netf\xF6ng: {{email_addresses}}.",
      formButtonPrimary__continue: "Senda bo\xF0",
      selectDropdown__role: "Veldu hlutverk",
      subtitle: "Sl\xE1\xF0u inn e\xF0a l\xEDmdu eitt e\xF0a fleiri netf\xF6ng, a\xF0skilin me\xF0 bilum e\xF0a kommum.",
      successMessage: "Bo\xF0 send me\xF0 g\xF3\xF0um \xE1rangri",
      title: "Bj\xF3\xF0a n\xFDja me\xF0limi"
    },
    membersPage: {
      action__invite: "Bj\xF3\xF0a",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "Fjarl\xE6gja me\xF0lim",
        tableHeader__actions: void 0,
        tableHeader__joined: "Gengi\xF0 \xED",
        tableHeader__role: "Hlutverk",
        tableHeader__user: "Notandi"
      },
      detailsTitle__emptyRow: "Engir me\xF0limir til a\xF0 s\xFDna",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Bj\xF3\xF0a notendum me\xF0 \xFEv\xED a\xF0 tengja netfangal\xE9n vi\xF0 samt\xF6kin \xFE\xEDn. Allir sem skr\xE1 sig me\xF0 samsvarandi netfangal\xE9n geta gengi\xF0 \xED samt\xF6kin hven\xE6r sem er.",
          headerTitle: "Sj\xE1lfvirk bo\xF0",
          primaryButton: "Stj\xF3rna sta\xF0festum l\xE9num"
        },
        table__emptyRow: "Engin bo\xF0 til a\xF0 s\xFDna"
      },
      invitedMembersTab: {
        menuAction__revoke: "Afturkalla bo\xF0",
        tableHeader__invited: "Bo\xF0i\xF0"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Notendur sem skr\xE1 sig me\xF0 samsvarandi netfangal\xE9n, munu sj\xE1 till\xF6gu um a\xF0 bi\xF0ja um a\xF0 ganga \xED samt\xF6kin \xFE\xEDn.",
          headerTitle: "Sj\xE1lfvirkar till\xF6gur",
          primaryButton: "Stj\xF3rna sta\xF0festum l\xE9num"
        },
        menuAction__approve: "Sam\xFEykkja",
        menuAction__reject: "Hafna",
        tableHeader__requested: "Be\xF0i\xF0 um a\xF0gang",
        table__emptyRow: "Engar bei\xF0nir til a\xF0 s\xFDna"
      },
      start: {
        headerTitle__invitations: "Bo\xF0",
        headerTitle__members: "Me\xF0limir",
        headerTitle__requests: "Bei\xF0nir"
      }
    },
    navbar: {
      apiKeys: void 0,
      billing: void 0,
      description: "Stj\xF3rna samt\xF6kunum \xFE\xEDnum.",
      general: "Almennt",
      members: "Me\xF0limir",
      title: "Samt\xF6k"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Sl\xE1\xF0u inn "{{organizationName}}" h\xE9r a\xF0 ne\xF0an til a\xF0 halda \xE1fram.',
          messageLine1: "Ertu viss um a\xF0 \xFE\xFA viljir ey\xF0a \xFEessum samt\xF6kum?",
          messageLine2: "\xDEessi a\xF0ger\xF0 er varanleg og \xF3afturkr\xE6f.",
          successMessage: "\xDE\xFA hefur eytt samt\xF6kunum.",
          title: "Ey\xF0a samt\xF6kum"
        },
        leaveOrganization: {
          actionDescription: 'Sl\xE1\xF0u inn "{{organizationName}}" h\xE9r a\xF0 ne\xF0an til a\xF0 halda \xE1fram.',
          messageLine1: "Ertu viss um a\xF0 \xFE\xFA viljir yfirgefa \xFEessi samt\xF6k? \xDE\xFA munt missa a\xF0gang a\xF0 \xFEessum samt\xF6kum og forritum \xFEeirra.",
          messageLine2: "\xDEessi a\xF0ger\xF0 er varanleg og \xF3afturkr\xE6f.",
          successMessage: "\xDE\xFA hefur yfirgefi\xF0 samt\xF6kin.",
          title: "Yfirgefa samt\xF6k"
        },
        title: "H\xE6tta"
      },
      domainSection: {
        menuAction__manage: "Stj\xF3rna",
        menuAction__remove: "Ey\xF0a",
        menuAction__verify: "Sta\xF0festa",
        primaryButton: "B\xE6ta vi\xF0 l\xE9ni",
        subtitle: "Leyfa notendum a\xF0 ganga \xED samt\xF6kin sj\xE1lfkrafa e\xF0a bi\xF0ja um a\xF0 ganga \xED byggt \xE1 sta\xF0festu netfangal\xE9ni.",
        title: "Sta\xF0fest l\xE9n"
      },
      successMessage: "Samt\xF6kin hafa veri\xF0 uppf\xE6r\xF0.",
      title: "Uppf\xE6ra pr\xF3f\xEDl"
    },
    removeDomainPage: {
      messageLine1: "Netfangal\xE9n {{domain}} ver\xF0ur fjarl\xE6gt.",
      messageLine2: "Notendur munu ekki geta gengi\xF0 \xED samt\xF6kin sj\xE1lfkrafa eftir \xFEetta.",
      successMessage: "{{domain}} hefur veri\xF0 fjarl\xE6gt.",
      title: "Fjarl\xE6gja l\xE9n"
    },
    start: {
      headerTitle__general: "Almennt",
      headerTitle__members: "Me\xF0limir",
      profileSection: {
        primaryButton: "Uppf\xE6ra pr\xF3f\xEDl",
        title: "Pr\xF3f\xEDll samtaka",
        uploadAction__title: "Merki"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "A\xF0 fjarl\xE6gja \xFEetta l\xE9n mun hafa \xE1hrif \xE1 bo\xF0na notendur.",
        removeDomainActionLabel__remove: "Fjarl\xE6gja l\xE9n",
        removeDomainSubtitle: "Fjarl\xE6gja \xFEetta l\xE9n \xFAr sta\xF0festum l\xE9num \xFE\xEDnum",
        removeDomainTitle: "Fjarl\xE6gja l\xE9n"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Notendur eru sj\xE1lfkrafa bo\xF0nir a\xF0 ganga \xED samt\xF6kin \xFEegar \xFEeir skr\xE1 sig og geta gengi\xF0 \xED hven\xE6r sem er.",
        automaticInvitationOption__label: "Sj\xE1lfvirk bo\xF0",
        automaticSuggestionOption__description: "Notendur f\xE1 till\xF6gu um a\xF0 bi\xF0ja um a\xF0 ganga \xED, en ver\xF0a a\xF0 vera sam\xFEykktir af stj\xF3rnanda \xE1\xF0ur en \xFEeir geta gengi\xF0 \xED samt\xF6kin.",
        automaticSuggestionOption__label: "Sj\xE1lfvirkar till\xF6gur",
        calloutInfoLabel: "A\xF0 breyta skr\xE1ningara\xF0fer\xF0 mun a\xF0eins hafa \xE1hrif \xE1 n\xFDja notendur.",
        calloutInvitationCountLabel: "Bi\xF0bo\xF0 send til notenda: {{count}}",
        calloutSuggestionCountLabel: "Bi\xF0till\xF6gur sendar til notenda: {{count}}",
        manualInvitationOption__description: "Notendur geta a\xF0eins veri\xF0 bo\xF0nir handvirkt \xED samt\xF6kin.",
        manualInvitationOption__label: "Engin sj\xE1lfvirk skr\xE1ning",
        subtitle: "Veldu hvernig notendur fr\xE1 \xFEessu l\xE9ni geta gengi\xF0 \xED samt\xF6kin."
      },
      start: {
        headerTitle__danger: "H\xE6tta",
        headerTitle__enrollment: "Skr\xE1ningarm\xF6guleikar"
      },
      subtitle: "L\xE9ni\xF0 {{domain}} er n\xFA sta\xF0fest. Haltu \xE1fram me\xF0 \xFEv\xED a\xF0 velja skr\xE1ningarm\xE1ta.",
      title: "Uppf\xE6ra {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Sl\xE1\xF0u inn sta\xF0festingark\xF3\xF0ann sem sendur var \xE1 netfangi\xF0 \xFEitt",
      formTitle: "Sta\xF0festingark\xF3\xF0i",
      resendButton: "F\xE9kkstu ekki k\xF3\xF0a? Senda aftur",
      subtitle: "L\xE9ni\xF0 {{domainName}} \xFEarf a\xF0 vera sta\xF0fest me\xF0 t\xF6lvup\xF3sti.",
      subtitleVerificationCodeScreen: "Sta\xF0festingark\xF3\xF0i var sendur \xE1 {{emailAddress}}. Sl\xE1\xF0u inn k\xF3\xF0ann til a\xF0 halda \xE1fram.",
      title: "Sta\xF0festa l\xE9n"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Stofna samt\xF6k",
    action__invitationAccept: "Ganga \xED",
    action__manageOrganization: "Stj\xF3rna",
    action__suggestionsAccept: "Bi\xF0ja um a\xF0 ganga \xED",
    notSelected: "Engin samt\xF6k valin",
    personalWorkspace: "Pers\xF3nulegur reikningur",
    suggestionsAcceptedLabel: "B\xED\xF0ur sam\xFEykkis"
  },
  paginationButton__next: "N\xE6sta",
  paginationButton__previous: "Fyrri",
  paginationRowText__displaying: "S\xFDnir",
  paginationRowText__of: "af",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "B\xE6ta vi\xF0 reikningi",
      action__signOutAll: "Skr\xE1 \xFAt af \xF6llum reikningum",
      subtitle: "Veldu reikninginn sem \xFE\xFA vilt halda \xE1fram me\xF0.",
      title: "Veldu reikning"
    },
    alternativeMethods: {
      actionLink: "F\xE1 hj\xE1lp",
      actionText: "Ertu ekki me\xF0 neitt af \xFEessu?",
      blockButton__backupCode: "Nota \xF6ryggisk\xF3\xF0a",
      blockButton__emailCode: "Senda k\xF3\xF0a \xE1 {{identifier}}",
      blockButton__emailLink: "Senda tengil \xE1 {{identifier}}",
      blockButton__passkey: "Skr\xE1 inn me\xF0 lykli",
      blockButton__password: "Skr\xE1 inn me\xF0 lykilor\xF0i",
      blockButton__phoneCode: "Senda SMS k\xF3\xF0a \xE1 {{identifier}}",
      blockButton__totp: "Nota au\xF0kennisforriti\xF0 \xFEitt",
      getHelp: {
        blockButton__emailSupport: "Senda t\xF6lvup\xF3st \xE1 stu\xF0ning",
        content: "Ef \xFE\xFA \xE1tt \xED erfi\xF0leikum me\xF0 a\xF0 skr\xE1 \xFEig inn \xE1 reikninginn \xFEinn, sendu okkur t\xF6lvup\xF3st og vi\xF0 munum vinna me\xF0 \xFE\xE9r til a\xF0 endurheimta a\xF0gang eins flj\xF3tt og au\xF0i\xF0 er.",
        title: "F\xE1 hj\xE1lp"
      },
      subtitle: "\xC1ttu \xED vandr\xE6\xF0um? \xDE\xFA getur nota\xF0 einhverja af \xFEessum a\xF0fer\xF0um til a\xF0 skr\xE1 \xFEig inn.",
      title: "Nota a\xF0ra a\xF0fer\xF0"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "\xD6ryggisk\xF3\xF0inn \xFEinn er s\xE1 sem \xFE\xFA f\xE9kkst \xFEegar \xFE\xFA stilltir tveggja \xFErepa au\xF0kenningu.",
      title: "Sl\xE1\xF0u inn \xF6ryggisk\xF3\xF0a"
    },
    emailCode: {
      formTitle: "Sta\xF0festingark\xF3\xF0i",
      resendButton: "F\xE9kkstu ekki k\xF3\xF0a? Senda aftur",
      subtitle: "til a\xF0 halda \xE1fram \xED {{applicationName}}",
      title: "Athuga\xF0u t\xF6lvup\xF3stinn \xFEinn"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Til a\xF0 halda \xE1fram, opna\xF0u sta\xF0festingartengilinn \xE1 t\xE6kinu og vafranum sem \xFE\xFA byrja\xF0ir innskr\xE1ninguna me\xF0",
        title: "Sta\xF0festingartengill er \xF3gildur fyrir \xFEetta t\xE6ki"
      },
      expired: {
        subtitle: "Far\xF0u aftur \xED upprunalega flipann til a\xF0 halda \xE1fram.",
        title: "\xDEessi sta\xF0festingartengill er \xFAtrunninn"
      },
      failed: {
        subtitle: "Far\xF0u aftur \xED upprunalega flipann til a\xF0 halda \xE1fram.",
        title: "\xDEessi sta\xF0festingartengill er \xF3gildur"
      },
      formSubtitle: "Nota sta\xF0festingartengilinn sem sendur var \xE1 t\xF6lvup\xF3stinn \xFEinn",
      formTitle: "Sta\xF0festingartengill",
      loading: {
        subtitle: "\xDE\xFA ver\xF0ur flj\xF3tlega v\xEDsa\xF0 \xE1fram",
        title: "Skr\xE1 inn..."
      },
      resendButton: "F\xE9kkstu ekki tengil? Senda aftur",
      subtitle: "til a\xF0 halda \xE1fram \xED {{applicationName}}",
      title: "Athuga\xF0u t\xF6lvup\xF3stinn \xFEinn",
      unusedTab: {
        title: "\xDE\xFA getur loka\xF0 \xFEessum flipa"
      },
      verified: {
        subtitle: "\xDE\xFA ver\xF0ur flj\xF3tlega v\xEDsa\xF0 \xE1fram",
        title: "T\xF3kst a\xF0 skr\xE1 inn"
      },
      verifiedSwitchTab: {
        subtitle: "Far\xF0u aftur \xED upprunalega flipann til a\xF0 halda \xE1fram",
        subtitleNewTab: "Far\xF0u aftur \xED n\xFDopna\xF0a flipann til a\xF0 halda \xE1fram",
        titleNewTab: "Skr\xE1\xF0ur inn \xE1 \xF6\xF0rum flipa"
      }
    },
    forgotPassword: {
      formTitle: "Endurstilla lykilor\xF0 k\xF3\xF0a",
      resendButton: "F\xE9kkstu ekki k\xF3\xF0a? Senda aftur",
      subtitle: "til a\xF0 endurstilla lykilor\xF0i\xF0 \xFEitt",
      subtitle_email: "Fyrst, sl\xE1\xF0u inn k\xF3\xF0ann sem sendur var \xE1 netfangi\xF0 \xFEitt",
      subtitle_phone: "Fyrst, sl\xE1\xF0u inn k\xF3\xF0ann sem sendur var \xE1 s\xEDmann \xFEinn",
      title: "Endurstilla lykilor\xF0"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Endurstilla lykilor\xF0i\xF0 \xFEitt",
      label__alternativeMethods: "E\xF0a, skr\xE1\xF0u \xFEig inn me\xF0 annarri a\xF0fer\xF0",
      title: "Gleymt lykilor\xF0?"
    },
    noAvailableMethods: {
      message: "Ekki er h\xE6gt a\xF0 halda \xE1fram me\xF0 innskr\xE1ningu. Engin tilt\xE6k au\xF0kenningara\xF0fer\xF0.",
      subtitle: "Villa kom upp",
      title: "Ekki h\xE6gt a\xF0 skr\xE1 inn"
    },
    passkey: {
      subtitle: "A\xF0 nota lykilinn \xFEinn sta\xF0festir a\xF0 \xFE\xFA ert \xFEa\xF0. T\xE6ki\xF0 \xFEitt g\xE6ti be\xF0i\xF0 um fingrafar, andlit e\xF0a skj\xE1l\xE1s.",
      title: "Nota lykilinn \xFEinn"
    },
    password: {
      actionLink: "Nota a\xF0ra a\xF0fer\xF0",
      subtitle: "Sl\xE1\xF0u inn lykilor\xF0i\xF0 sem tengist reikningnum \xFE\xEDnum",
      title: "Sl\xE1\xF0u inn lykilor\xF0i\xF0 \xFEitt"
    },
    passwordPwned: {
      title: "Lykilor\xF0 broti\xF0"
    },
    phoneCode: {
      formTitle: "Sta\xF0festingark\xF3\xF0i",
      resendButton: "F\xE9kkstu ekki k\xF3\xF0a? Senda aftur",
      subtitle: "til a\xF0 halda \xE1fram \xED {{applicationName}}",
      title: "Athuga\xF0u s\xEDmann \xFEinn"
    },
    phoneCodeMfa: {
      formTitle: "Sta\xF0festingark\xF3\xF0i",
      resendButton: "F\xE9kkstu ekki k\xF3\xF0a? Senda aftur",
      subtitle: "Til a\xF0 halda \xE1fram, vinsamlegast sl\xE1\xF0u inn sta\xF0festingark\xF3\xF0ann sem sendur var \xE1 s\xEDmann \xFEinn",
      title: "Athuga\xF0u s\xEDmann \xFEinn"
    },
    resetPassword: {
      formButtonPrimary: "Endurstilla lykilor\xF0",
      requiredMessage: "Af \xF6ryggis\xE1st\xE6\xF0um er nau\xF0synlegt a\xF0 endurstilla lykilor\xF0i\xF0 \xFEitt.",
      successMessage: "Lykilor\xF0i\xF0 \xFEitt var endurstillt me\xF0 g\xF3\xF0um \xE1rangri. Skr\xE1 \xFEig inn, vinsamlegast b\xEDddu augnablik.",
      title: "Setja n\xFDtt lykilor\xF0"
    },
    resetPasswordMfa: {
      detailsLabel: "Vi\xF0 \xFEurfum a\xF0 sta\xF0festa au\xF0kenni \xFEitt \xE1\xF0ur en vi\xF0 endurstillum lykilor\xF0i\xF0 \xFEitt."
    },
    start: {
      actionLink: "Skr\xE1 sig",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "Nota netfang",
      actionLink__use_email_username: "Nota netfang e\xF0a notendanafn",
      actionLink__use_passkey: "Nota lykil \xED sta\xF0inn",
      actionLink__use_phone: "Nota s\xEDma",
      actionLink__use_username: "Nota notendanafn",
      actionText: "Ertu ekki me\xF0 reikning?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "Velkomin aftur! Vinsamlegast skr\xE1\xF0u \xFEig inn til a\xF0 halda \xE1fram",
      subtitleCombined: void 0,
      title: "Skr\xE1 inn \xED {{applicationName}}",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Sta\xF0festingark\xF3\xF0i",
      subtitle: "Til a\xF0 halda \xE1fram, vinsamlegast sl\xE1\xF0u inn sta\xF0festingark\xF3\xF0ann sem au\xF0kennisforriti\xF0 \xFEitt bj\xF3 til",
      title: "Tveggja \xFErepa au\xF0kenning"
    }
  },
  signInEnterPasswordTitle: "Sl\xE1\xF0u inn lykilor\xF0i\xF0 \xFEitt",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Skr\xE1 inn",
      actionText: "Ertu me\xF0 reikning?",
      subtitle: "Vinsamlegast fylltu \xFAt eftirfarandi uppl\xFDsingar til a\xF0 halda \xE1fram.",
      title: "Fylltu \xFAt vantar reiti"
    },
    emailCode: {
      formSubtitle: "Sl\xE1\xF0u inn sta\xF0festingark\xF3\xF0ann sem sendur var \xE1 netfangi\xF0 \xFEitt",
      formTitle: "Sta\xF0festingark\xF3\xF0i",
      resendButton: "F\xE9kkstu ekki k\xF3\xF0a? Senda aftur",
      subtitle: "Sl\xE1\xF0u inn sta\xF0festingark\xF3\xF0ann sem sendur var \xE1 netfangi\xF0 \xFEitt",
      title: "Sta\xF0festa netfang"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Til a\xF0 halda \xE1fram, opna\xF0u sta\xF0festingartengilinn \xE1 t\xE6kinu og vafranum sem \xFE\xFA byrja\xF0ir skr\xE1ninguna me\xF0",
        title: "Sta\xF0festingartengill er \xF3gildur fyrir \xFEetta t\xE6ki"
      },
      formSubtitle: "Nota sta\xF0festingartengilinn sem sendur var \xE1 netfangi\xF0 \xFEitt",
      formTitle: "Sta\xF0festingartengill",
      loading: {
        title: "Skr\xE1 sig..."
      },
      resendButton: "F\xE9kkstu ekki tengil? Senda aftur",
      subtitle: "til a\xF0 halda \xE1fram \xED {{applicationName}}",
      title: "Sta\xF0festa netfang",
      verified: {
        title: "T\xF3kst a\xF0 skr\xE1 sig"
      },
      verifiedSwitchTab: {
        subtitle: "Far\xF0u aftur \xED n\xFDopna\xF0a flipann til a\xF0 halda \xE1fram",
        subtitleNewTab: "Far\xF0u aftur \xED fyrri flipann til a\xF0 halda \xE1fram",
        title: "T\xF3kst a\xF0 sta\xF0festa netfang"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: void 0,
        label__onlyTermsOfService: void 0,
        label__termsOfServiceAndPrivacyPolicy: void 0
      },
      continue: {
        subtitle: void 0,
        title: void 0
      }
    },
    phoneCode: {
      formSubtitle: "Sl\xE1\xF0u inn sta\xF0festingark\xF3\xF0ann sem sendur var \xE1 s\xEDman\xFAmeri\xF0 \xFEitt",
      formTitle: "Sta\xF0festingark\xF3\xF0i",
      resendButton: "F\xE9kkstu ekki k\xF3\xF0a? Senda aftur",
      subtitle: "Sl\xE1\xF0u inn sta\xF0festingark\xF3\xF0ann sem sendur var \xE1 s\xEDman\xFAmeri\xF0 \xFEitt",
      title: "Sta\xF0festa s\xEDman\xFAmer"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "Skr\xE1 inn",
      actionLink__use_email: "Nota netfang \xED sta\xF0inn",
      actionLink__use_phone: "Nota s\xEDma \xED sta\xF0inn",
      actionText: "Ertu me\xF0 reikning?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "Velkomin! Vinsamlegast fylltu \xFAt uppl\xFDsingar til a\xF0 byrja.",
      subtitleCombined: "Velkomin! Vinsamlegast fylltu \xFAt uppl\xFDsingar til a\xF0 byrja.",
      title: "Stofna reikning",
      titleCombined: "Stofna reikning"
    }
  },
  socialButtonsBlockButton: "Halda \xE1fram me\xF0 {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: "{{provider|titleize}}",
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "Skr\xE1ning mist\xF3kst vegna misheppna\xF0ra \xF6ryggissta\xF0festinga. Vinsamlegast endurhla\xF0i\xF0 s\xED\xF0una til a\xF0 reyna aftur e\xF0a hafi\xF0 samband vi\xF0 stu\xF0ning til a\xF0 f\xE1 frekari a\xF0sto\xF0.",
    captcha_unavailable: "Skr\xE1ning mist\xF3kst vegna misheppna\xF0rar v\xE9lmenna sta\xF0festingar. Vinsamlegast endurhla\xF0i\xF0 s\xED\xF0una til a\xF0 reyna aftur e\xF0a hafi\xF0 samband vi\xF0 stu\xF0ning til a\xF0 f\xE1 frekari a\xF0sto\xF0.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: "\xDEetta netfang er \xFEegar \xED notkun. Vinsamlegast reyndu anna\xF0.",
    form_identifier_exists__phone_number: "\xDEetta s\xEDman\xFAmer er \xFEegar \xED notkun. Vinsamlegast reyndu anna\xF0.",
    form_identifier_exists__username: "\xDEetta notendanafn er \xFEegar \xED notkun. Vinsamlegast reyndu anna\xF0.",
    form_identifier_not_found: "Vi\xF0 getum ekki fundi\xF0 reikning me\xF0 \xFEessum uppl\xFDsingum.",
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: "Netfang ver\xF0ur a\xF0 vera gilt netfang.",
    form_param_format_invalid__phone_number: "S\xEDman\xFAmer ver\xF0ur a\xF0 vera \xE1 giltu al\xFEj\xF3\xF0legu formi",
    form_param_max_length_exceeded__first_name: "Fornafn m\xE1 ekki vera lengra en 256 stafir.",
    form_param_max_length_exceeded__last_name: "Eftirnafn m\xE1 ekki vera lengra en 256 stafir.",
    form_param_max_length_exceeded__name: "Nafn m\xE1 ekki vera lengra en 256 stafir.",
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: void 0,
    form_password_not_strong_enough: "Lykilor\xF0i\xF0 \xFEitt er ekki n\xF3gu sterkt.",
    form_password_pwned: "\xDEetta lykilor\xF0 hefur fundist sem hluti af \xF6ryggisbresti og m\xE1 ekki nota, vinsamlegast reyndu anna\xF0 lykilor\xF0.",
    form_password_pwned__sign_in: "\xDEetta lykilor\xF0 hefur fundist sem hluti af \xF6ryggisbresti og m\xE1 ekki nota, vinsamlegast endurstilltu lykilor\xF0i\xF0 \xFEitt.",
    form_password_size_in_bytes_exceeded: "Lykilor\xF0i\xF0 \xFEitt hefur fari\xF0 yfir h\xE1marksfj\xF6lda b\xE6ta sem leyf\xF0ir eru, vinsamlegast styttu \xFEa\xF0 e\xF0a fjarl\xE6g\xF0u nokkra s\xE9rstafi.",
    form_password_validation_failed: "Rangt lykilor\xF0",
    form_username_invalid_character: void 0,
    form_username_invalid_length: void 0,
    identification_deletion_failed: "\xDE\xFA getur ekki eytt s\xED\xF0asta au\xF0kenni \xFE\xEDnu.",
    not_allowed_access: "Netfang e\xF0a s\xEDman\xFAmer \xFEitt er ekki leyft til a\xF0 skr\xE1 sig. \xDEetta g\xE6ti veri\xF0 vegna \xFEess a\xF0 \xFE\xFA ert a\xF0 nota '+', '=', '#' e\xF0a '.' \xED netfangi \xFE\xEDnu, a\xF0 nota domen sem tengist t\xEDmabundnum t\xF6lvup\xF3stur, e\xF0a a\xF0 \xFE\xFA ert b\xFAinn til a\xF0 nota \xFEa\xF0. Ef \xFE\xFA reynir a\xF0 skr\xE1 sig og f\xE6r\xF0 villu, vinsamlegast hafi\xF0 samband vi\xF0 stu\xF0ning.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: "Lykill er \xFEegar skr\xE1\xF0ur me\xF0 \xFEessu t\xE6ki.",
    passkey_not_supported: "Lyklar eru ekki studdir \xE1 \xFEessu t\xE6ki.",
    passkey_pa_not_supported: "Skr\xE1ning krefst vettvangs au\xF0kennis en t\xE6ki\xF0 sty\xF0ur \xFEa\xF0 ekki.",
    passkey_registration_cancelled: "Skr\xE1ning lykils var h\xE6tt e\xF0a rann \xFAt.",
    passkey_retrieval_cancelled: "Sta\xF0festing lykils var h\xE6tt e\xF0a rann \xFAt.",
    passwordComplexity: {
      maximumLength: "minna en {{length}} stafir",
      minimumLength: "{{length}} e\xF0a fleiri stafir",
      requireLowercase: "l\xE1gstaf",
      requireNumbers: "t\xF6lu",
      requireSpecialCharacter: "s\xE9rstaf",
      requireUppercase: "h\xE1staf",
      sentencePrefix: "Lykilor\xF0i\xF0 \xFEitt ver\xF0ur a\xF0 innihalda"
    },
    phone_number_exists: "\xDEetta s\xEDman\xFAmer er \xFEegar \xED notkun. Vinsamlegast reyndu anna\xF0.",
    session_exists: "\xDE\xFA ert n\xFA \xFEegar innskr\xE1\xF0ur.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "Lykilor\xF0i\xF0 \xFEitt virkar, en g\xE6ti veri\xF0 sterkara. Reyndu a\xF0 b\xE6ta vi\xF0 fleiri st\xF6fum.",
      goodPassword: "Lykilor\xF0i\xF0 \xFEitt uppfyllir allar nau\xF0synlegar kr\xF6fur.",
      notEnough: "Lykilor\xF0i\xF0 \xFEitt er ekki n\xF3gu sterkt.",
      suggestions: {
        allUppercase: "St\xF3rstafa sum, en ekki alla stafi.",
        anotherWord: "B\xE6ttu vi\xF0 fleiri or\xF0um sem eru minna algeng.",
        associatedYears: "For\xF0astu \xE1r sem tengjast \xFE\xE9r.",
        capitalization: "St\xF3rstafa fleiri en fyrsta staf.",
        dates: "For\xF0astu dagsetningar og \xE1r sem tengjast \xFE\xE9r.",
        l33t: "For\xF0astu fyrirsj\xE1anlegar stafaskiptingar eins og '@' fyrir 'a'.",
        longerKeyboardPattern: "Nota\xF0u lengri lyklabor\xF0smynstur og breyttu sl\xE1ttarstefnu nokkrum sinnum.",
        noNeed: "\xDE\xFA getur b\xFAi\xF0 til sterk lykilor\xF0 \xE1n \xFEess a\xF0 nota t\xE1kn, t\xF6lur e\xF0a h\xE1stafi.",
        pwned: "Ef \xFE\xFA notar \xFEetta lykilor\xF0 annars sta\xF0ar, \xE6ttir \xFE\xFA a\xF0 breyta \xFEv\xED.",
        recentYears: "For\xF0astu n\xFDleg \xE1r.",
        repeated: "For\xF0astu endurtekin or\xF0 og stafi.",
        reverseWords: "For\xF0astu \xF6fug stafsetning algengra or\xF0a.",
        sequences: "For\xF0astu algengar stafara\xF0ir.",
        useWords: "Nota\xF0u m\xF6rg or\xF0, en for\xF0astu algengar setningar."
      },
      warnings: {
        common: "\xDEetta er algengt lykilor\xF0.",
        commonNames: "Algeng n\xF6fn og eftirn\xF6fn eru au\xF0velt a\xF0 giska \xE1.",
        dates: "Dagsetningar eru au\xF0velt a\xF0 giska \xE1.",
        extendedRepeat: 'Endurtekin stafamynstur eins og "abcabcabc" eru au\xF0velt a\xF0 giska \xE1.',
        keyPattern: "Stutt lyklabor\xF0smynstur eru au\xF0velt a\xF0 giska \xE1.",
        namesByThemselves: "Einst\xF6k n\xF6fn e\xF0a eftirn\xF6fn eru au\xF0velt a\xF0 giska \xE1.",
        pwned: "Lykilor\xF0i\xF0 \xFEitt var afhj\xFApa\xF0 \xED \xF6ryggisbresti \xE1 netinu.",
        recentYears: "N\xFDleg \xE1r eru au\xF0velt a\xF0 giska \xE1.",
        sequences: 'Algengar stafara\xF0ir eins og "abc" eru au\xF0velt a\xF0 giska \xE1.',
        similarToCommon: "\xDEetta er svipa\xF0 og algengt lykilor\xF0.",
        simpleRepeat: 'Endurteknir stafir eins og "aaa" eru au\xF0velt a\xF0 giska \xE1.',
        straightRow: "Beinar ra\xF0ir af lyklum \xE1 lyklabor\xF0inu eru au\xF0velt a\xF0 giska \xE1.",
        topHundred: "\xDEetta er oft nota\xF0 lykilor\xF0.",
        topTen: "\xDEetta er mj\xF6g oft nota\xF0 lykilor\xF0.",
        userInputs: "\xDEa\xF0 \xE6tti ekki a\xF0 vera neinar pers\xF3nulegar e\xF0a s\xED\xF0u tengdar uppl\xFDsingar.",
        wordByItself: "Einst\xF6k or\xF0 eru au\xF0velt a\xF0 giska \xE1."
      }
    }
  },
  userButton: {
    action__addAccount: "B\xE6ta vi\xF0 reikningi",
    action__manageAccount: "Stj\xF3rna reikningi",
    action__signOut: "Skr\xE1 \xFAt",
    action__signOutAll: "Skr\xE1 \xFAt af \xF6llum reikningum"
  },
  userProfile: {
    apiKeysPage: {
      title: void 0
    },
    backupCodePage: {
      actionLabel__copied: "Afrita\xF0!",
      actionLabel__copy: "Afrita allt",
      actionLabel__download: "S\xE6kja .txt",
      actionLabel__print: "Prenta",
      infoText1: "\xD6ryggisk\xF3\xF0ar ver\xF0a virkja\xF0ir fyrir \xFEennan reikning.",
      infoText2: "Geymdu \xF6ryggisk\xF3\xF0ana leynilega og geymdu \xFE\xE1 \xE1 \xF6ruggum sta\xF0. \xDE\xFA getur endurmynda\xF0 \xF6ryggisk\xF3\xF0a ef \xFE\xFA grunar a\xF0 \xFEeir hafi veri\xF0 afhj\xFApa\xF0ir.",
      subtitle__codelist: "Geymdu \xFE\xE1 \xE1 \xF6ruggum sta\xF0 og haltu \xFEeim leynilegum.",
      successMessage: "\xD6ryggisk\xF3\xF0ar eru n\xFA virkja\xF0ir. \xDE\xFA getur nota\xF0 einn af \xFEessum til a\xF0 skr\xE1 \xFEig inn \xE1 reikninginn \xFEinn, ef \xFE\xFA missir a\xF0gang a\xF0 au\xF0kennis t\xE6kinu \xFE\xEDnu. Hver k\xF3\xF0i getur a\xF0eins veri\xF0 nota\xF0ur einu sinni.",
      successSubtitle: "\xDE\xFA getur nota\xF0 einn af \xFEessum til a\xF0 skr\xE1 \xFEig inn \xE1 reikninginn \xFEinn, ef \xFE\xFA missir a\xF0gang a\xF0 au\xF0kennis t\xE6kinu \xFE\xEDnu.",
      title: "B\xE6ta vi\xF0 \xF6ryggisk\xF3\xF0a sta\xF0festingu",
      title__codelist: "\xD6ryggisk\xF3\xF0ar"
    },
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Veldu \xFEj\xF3nustua\xF0ila til a\xF0 tengja reikninginn \xFEinn.",
      formHint__noAccounts: "Engir tilt\xE6kir ytri reiknings\xFEj\xF3nustua\xF0ilar.",
      removeResource: {
        messageLine1: "{{identifier}} ver\xF0ur fjarl\xE6gt \xFAr \xFEessum reikningi.",
        messageLine2: "\xDE\xFA munt ekki lengur geta nota\xF0 \xFEennan tengda reikning og \xF6ll h\xE1\xF0 eiginleikar munu ekki lengur virka.",
        successMessage: "{{connectedAccount}} hefur veri\xF0 fjarl\xE6gt \xFAr reikningnum \xFE\xEDnum.",
        title: "Fjarl\xE6gja tengdan reikning"
      },
      socialButtonsBlockButton: "{{provider|titleize}}",
      successMessage: "\xDEj\xF3nustua\xF0ilinn hefur veri\xF0 b\xE6tt vi\xF0 reikninginn \xFEinn",
      title: "B\xE6ta vi\xF0 tengdum reikningi"
    },
    deletePage: {
      actionDescription: 'Sl\xE1\xF0u inn "Ey\xF0a reikningi" h\xE9r a\xF0 ne\xF0an til a\xF0 halda \xE1fram.',
      confirm: "Ey\xF0a reikningi",
      messageLine1: "Ertu viss um a\xF0 \xFE\xFA viljir ey\xF0a reikningnum \xFE\xEDnum?",
      messageLine2: "\xDEessi a\xF0ger\xF0 er varanleg og \xF3afturkr\xE6f.",
      title: "Ey\xF0a reikningi"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "T\xF6lvup\xF3stur sem inniheldur sta\xF0festingark\xF3\xF0a ver\xF0ur sendur \xE1 \xFEetta netfang.",
        formSubtitle: "Sl\xE1\xF0u inn sta\xF0festingark\xF3\xF0ann sem sendur var \xE1 {{identifier}}",
        formTitle: "Sta\xF0festingark\xF3\xF0i",
        resendButton: "F\xE9kkstu ekki k\xF3\xF0a? Senda aftur",
        successMessage: "Netfangi\xF0 {{identifier}} hefur veri\xF0 b\xE6tt vi\xF0 reikninginn \xFEinn."
      },
      emailLink: {
        formHint: "T\xF6lvup\xF3stur sem inniheldur sta\xF0festingartengil ver\xF0ur sendur \xE1 \xFEetta netfang.",
        formSubtitle: "Smelltu \xE1 sta\xF0festingartengilinn \xED t\xF6lvup\xF3stinum sem sendur var \xE1 {{identifier}}",
        formTitle: "Sta\xF0festingartengill",
        resendButton: "F\xE9kkstu ekki tengil? Senda aftur",
        successMessage: "Netfangi\xF0 {{identifier}} hefur veri\xF0 b\xE6tt vi\xF0 reikninginn \xFEinn."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} ver\xF0ur fjarl\xE6gt \xFAr \xFEessum reikningi.",
        messageLine2: "\xDE\xFA munt ekki lengur geta skr\xE1\xF0 \xFEig inn me\xF0 \xFEessu netfangi.",
        successMessage: "{{emailAddress}} hefur veri\xF0 fjarl\xE6gt \xFAr reikningnum \xFE\xEDnum.",
        title: "Fjarl\xE6gja netfang"
      },
      title: "B\xE6ta vi\xF0 netfangi",
      verifyTitle: "Sta\xF0festa netfang"
    },
    formButtonPrimary__add: "B\xE6ta vi\xF0",
    formButtonPrimary__continue: "Halda \xE1fram",
    formButtonPrimary__finish: "Lj\xFAka",
    formButtonPrimary__remove: "Fjarl\xE6gja",
    formButtonPrimary__save: "Vista",
    formButtonReset: "H\xE6tta vi\xF0",
    mfaPage: {
      formHint: "Veldu a\xF0fer\xF0 til a\xF0 b\xE6ta vi\xF0.",
      title: "B\xE6ta vi\xF0 tveggja \xFErepa au\xF0kenningu"
    },
    mfaPhoneCodePage: {
      backButton: "Nota n\xFAverandi n\xFAmer",
      primaryButton__addPhoneNumber: "B\xE6ta vi\xF0 s\xEDman\xFAmeri",
      removeResource: {
        messageLine1: "{{identifier}} mun ekki lengur f\xE1 sta\xF0festingark\xF3\xF0a vi\xF0 innskr\xE1ningu.",
        messageLine2: "Reikningurinn \xFEinn g\xE6ti ekki veri\xF0 eins \xF6ruggur. Ertu viss um a\xF0 \xFE\xFA viljir halda \xE1fram?",
        successMessage: "SMS k\xF3\xF0a tveggja \xFErepa au\xF0kenning hefur veri\xF0 fjarl\xE6g\xF0 fyrir {{mfaPhoneCode}}",
        title: "Fjarl\xE6gja tveggja \xFErepa au\xF0kenningu"
      },
      subtitle__availablePhoneNumbers: "Veldu n\xFAverandi s\xEDman\xFAmer til a\xF0 skr\xE1 fyrir SMS k\xF3\xF0a tveggja \xFErepa au\xF0kenningu e\xF0a b\xE6ttu vi\xF0 n\xFDju.",
      subtitle__unavailablePhoneNumbers: "Engin tilt\xE6k s\xEDman\xFAmer til a\xF0 skr\xE1 fyrir SMS k\xF3\xF0a tveggja \xFErepa au\xF0kenningu, vinsamlegast b\xE6ttu vi\xF0 n\xFDju.",
      successMessage1: "Vi\xF0 innskr\xE1ningu \xFEarftu a\xF0 sl\xE1 inn sta\xF0festingark\xF3\xF0a sem sendur er \xE1 \xFEetta s\xEDman\xFAmer sem vi\xF0b\xF3tar skref.",
      successMessage2: "Vista\xF0u \xFEessa \xF6ryggisk\xF3\xF0a og geymdu \xFE\xE1 \xE1 \xF6ruggum sta\xF0. Ef \xFE\xFA missir a\xF0gang a\xF0 au\xF0kennis t\xE6kinu \xFE\xEDnu, getur \xFE\xFA nota\xF0 \xF6ryggisk\xF3\xF0a til a\xF0 skr\xE1 \xFEig inn.",
      successTitle: "SMS k\xF3\xF0a sta\xF0festing virkja\xF0",
      title: "B\xE6ta vi\xF0 SMS k\xF3\xF0a sta\xF0festingu"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Skanna QR k\xF3\xF0a \xED sta\xF0inn",
        buttonUnableToScan__nonPrimary: "Getur ekki skanna\xF0 QR k\xF3\xF0a?",
        infoText__ableToScan: "Settu upp n\xFDja innskr\xE1ningara\xF0fer\xF0 \xED au\xF0kennisforritinu \xFE\xEDnu og skanna\xF0u eftirfarandi QR k\xF3\xF0a til a\xF0 tengja \xFEa\xF0 vi\xF0 reikninginn \xFEinn.",
        infoText__unableToScan: "Settu upp n\xFDja innskr\xE1ningara\xF0fer\xF0 \xED au\xF0kennisforritinu \xFE\xEDnu og sl\xE1\xF0u inn lykilinn h\xE9r a\xF0 ne\xF0an.",
        inputLabel__unableToScan1: "Gakktu \xFAr skugga um a\xF0 T\xEDmatengdir e\xF0a Einnota lykilor\xF0 s\xE9u virkja\xF0, og lj\xFAktu s\xED\xF0an vi\xF0 a\xF0 tengja reikninginn \xFEinn.",
        inputLabel__unableToScan2: "A\xF0 \xF6\xF0rum kosti, ef au\xF0kennisforriti\xF0 \xFEitt sty\xF0ur TOTP URI, getur\xF0u einnig afrita\xF0 fullan URI."
      },
      removeResource: {
        messageLine1: "Sta\xF0festingark\xF3\xF0ar fr\xE1 \xFEessu au\xF0kennisforriti ver\xF0a ekki lengur nau\xF0synlegir vi\xF0 innskr\xE1ningu.",
        messageLine2: "Reikningurinn \xFEinn g\xE6ti ekki veri\xF0 eins \xF6ruggur. Ertu viss um a\xF0 \xFE\xFA viljir halda \xE1fram?",
        successMessage: "Tveggja \xFErepa au\xF0kenning me\xF0 au\xF0kennisforriti hefur veri\xF0 fjarl\xE6g\xF0.",
        title: "Fjarl\xE6gja tveggja \xFErepa au\xF0kenningu"
      },
      successMessage: "Tveggja \xFErepa au\xF0kenning er n\xFA virkja\xF0. Vi\xF0 innskr\xE1ningu \xFEarftu a\xF0 sl\xE1 inn sta\xF0festingark\xF3\xF0a fr\xE1 \xFEessu au\xF0kennisforriti sem vi\xF0b\xF3tar skref.",
      title: "B\xE6ta vi\xF0 au\xF0kennisforriti",
      verifySubtitle: "Sl\xE1\xF0u inn sta\xF0festingark\xF3\xF0ann sem au\xF0kennisforriti\xF0 \xFEitt bj\xF3 til",
      verifyTitle: "Sta\xF0festingark\xF3\xF0i"
    },
    mobileButton__menu: "Valmynd",
    navbar: {
      account: "Pr\xF3f\xEDll",
      apiKeys: void 0,
      billing: void 0,
      description: "Stj\xF3rna reikningsuppl\xFDsingum \xFE\xEDnum.",
      security: "\xD6ryggi",
      title: "Reikningur"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} ver\xF0ur fjarl\xE6gt \xFAr \xFEessum reikningi.",
        title: "Fjarl\xE6gja lykil"
      },
      subtitle__rename: "\xDE\xFA getur breytt nafni lykilsins til a\xF0 au\xF0velda a\xF0 finna hann.",
      title__rename: "Endurnefna lykil"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "M\xE6lt er me\xF0 a\xF0 skr\xE1 \xFEig \xFAt af \xF6llum \xF6\xF0rum t\xE6kjum sem g\xE6tu hafa nota\xF0 gamla lykilor\xF0i\xF0 \xFEitt.",
      readonly: "Lykilor\xF0i\xF0 \xFEitt er ekki h\xE6gt a\xF0 breyta n\xFAna vegna \xFEess a\xF0 \xFE\xFA getur a\xF0eins skr\xE1\xF0 \xFEig inn \xED gegnum fyrirt\xE6kjatengingu.",
      successMessage__set: "Lykilor\xF0i\xF0 \xFEitt hefur veri\xF0 sett.",
      successMessage__signOutOfOtherSessions: "\xD6ll \xF6nnur t\xE6ki hafa veri\xF0 skr\xE1\xF0 \xFAt.",
      successMessage__update: "Lykilor\xF0i\xF0 \xFEitt hefur veri\xF0 uppf\xE6rt.",
      title__set: "Setja lykilor\xF0",
      title__update: "Uppf\xE6ra lykilor\xF0"
    },
    phoneNumberPage: {
      infoText: "SMS sem inniheldur sta\xF0festingark\xF3\xF0a ver\xF0ur sent \xE1 \xFEetta s\xEDman\xFAmer. Skilabo\xF0 og gagnagj\xF6ld geta \xE1tt vi\xF0.",
      removeResource: {
        messageLine1: "{{identifier}} ver\xF0ur fjarl\xE6gt \xFAr \xFEessum reikningi.",
        messageLine2: "\xDE\xFA munt ekki lengur geta skr\xE1\xF0 \xFEig inn me\xF0 \xFEessu s\xEDman\xFAmeri.",
        successMessage: "{{phoneNumber}} hefur veri\xF0 fjarl\xE6gt \xFAr reikningnum \xFE\xEDnum.",
        title: "Fjarl\xE6gja s\xEDman\xFAmer"
      },
      successMessage: "{{identifier}} hefur veri\xF0 b\xE6tt vi\xF0 reikninginn \xFEinn.",
      title: "B\xE6ta vi\xF0 s\xEDman\xFAmeri",
      verifySubtitle: "Sl\xE1\xF0u inn sta\xF0festingark\xF3\xF0ann sem sendur var \xE1 {{identifier}}",
      verifyTitle: "Sta\xF0festa s\xEDman\xFAmer"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "M\xE6lt st\xE6r\xF0 1:1, allt a\xF0 10MB.",
      imageFormDestructiveActionSubtitle: "Fjarl\xE6gja",
      imageFormSubtitle: "Hla\xF0a upp",
      imageFormTitle: "Pr\xF3f\xEDlmynd",
      readonly: "Pr\xF3f\xEDluppl\xFDsingar \xFE\xEDnar hafa veri\xF0 veittar af fyrirt\xE6kjatengingu og er ekki h\xE6gt a\xF0 breyta.",
      successMessage: "Pr\xF3f\xEDllinn \xFEinn hefur veri\xF0 uppf\xE6r\xF0ur.",
      title: "Uppf\xE6ra pr\xF3f\xEDl"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Skr\xE1 \xFAt af t\xE6ki",
        title: "Virk t\xE6ki"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Endurtengja",
        actionLabel__reauthorize: "Heimila n\xFAna",
        destructiveActionTitle: "Fjarl\xE6gja",
        primaryButton: "Tengja reikning",
        subtitle__disconnected: "\xDEessi reikningur hefur veri\xF0 aftengdur.",
        subtitle__reauthorize: "Nau\xF0synlegar heimildir hafa veri\xF0 uppf\xE6r\xF0ar, og \xFE\xFA g\xE6tir veri\xF0 a\xF0 upplifa takmarka\xF0a virkni. Vinsamlegast endurheimila\xF0u \xFEetta forrit til a\xF0 for\xF0ast vandam\xE1l",
        title: "Tengdir reikningar"
      },
      dangerSection: {
        deleteAccountButton: "Ey\xF0a reikningi",
        title: "Ey\xF0a reikningi"
      },
      emailAddressesSection: {
        destructiveAction: "Fjarl\xE6gja netfang",
        detailsAction__nonPrimary: "Setja sem a\xF0al",
        detailsAction__primary: "Lj\xFAka sta\xF0festingu",
        detailsAction__unverified: "Sta\xF0festa",
        primaryButton: "B\xE6ta vi\xF0 netfangi",
        title: "Netf\xF6ng"
      },
      enterpriseAccountsSection: {
        title: "Fyrirt\xE6kjareikningar"
      },
      headerTitle__account: "Pr\xF3f\xEDluppl\xFDsingar",
      headerTitle__security: "\xD6ryggi",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Endurmynda",
          headerTitle: "\xD6ryggisk\xF3\xF0ar",
          subtitle__regenerate: "F\xE1\xF0u n\xFDtt sett af \xF6ruggum \xF6ryggisk\xF3\xF0um. Fyrri \xF6ryggisk\xF3\xF0ar ver\xF0a eyttir og ekki h\xE6gt a\xF0 nota.",
          title__regenerate: "Endurmynda \xF6ryggisk\xF3\xF0a"
        },
        phoneCode: {
          actionLabel__setDefault: "Setja sem sj\xE1lfgefi\xF0",
          destructiveActionLabel: "Fjarl\xE6gja"
        },
        primaryButton: "B\xE6ta vi\xF0 tveggja \xFErepa au\xF0kenningu",
        title: "Tveggja \xFErepa au\xF0kenning",
        totp: {
          destructiveActionTitle: "Fjarl\xE6gja",
          headerTitle: "Au\xF0kennisforrit"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Fjarl\xE6gja",
        menuAction__rename: "Endurnefna",
        primaryButton: void 0,
        title: "Lyklar"
      },
      passwordSection: {
        primaryButton__setPassword: "Setja lykilor\xF0",
        primaryButton__updatePassword: "Uppf\xE6ra lykilor\xF0",
        title: "Lykilor\xF0"
      },
      phoneNumbersSection: {
        destructiveAction: "Fjarl\xE6gja s\xEDman\xFAmer",
        detailsAction__nonPrimary: "Setja sem a\xF0al",
        detailsAction__primary: "Lj\xFAka sta\xF0festingu",
        detailsAction__unverified: "Sta\xF0festa s\xEDman\xFAmer",
        primaryButton: "B\xE6ta vi\xF0 s\xEDman\xFAmeri",
        title: "S\xEDman\xFAmer"
      },
      profileSection: {
        primaryButton: "Uppf\xE6ra pr\xF3f\xEDl",
        title: "Pr\xF3f\xEDll"
      },
      usernameSection: {
        primaryButton__setUsername: "Setja notendanafn",
        primaryButton__updateUsername: "Uppf\xE6ra notendanafn",
        title: "Notendanafn"
      },
      web3WalletsSection: {
        destructiveAction: "Fjarl\xE6gja veski",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3 veski",
        title: "Web3 veski"
      }
    },
    usernamePage: {
      successMessage: "Notendanafni\xF0 \xFEitt hefur veri\xF0 uppf\xE6rt.",
      title__set: "Setja notendanafn",
      title__update: "Uppf\xE6ra notendanafn"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} ver\xF0ur fjarl\xE6gt \xFAr \xFEessum reikningi.",
        messageLine2: "\xDE\xFA munt ekki lengur geta skr\xE1\xF0 \xFEig inn me\xF0 \xFEessu web3 veski.",
        successMessage: "{{web3Wallet}} hefur veri\xF0 fjarl\xE6gt \xFAr reikningnum \xFE\xEDnum.",
        title: "Fjarl\xE6gja web3 veski"
      },
      subtitle__availableWallets: "Veldu web3 veski til a\xF0 tengja vi\xF0 reikninginn \xFEinn.",
      subtitle__unavailableWallets: "Engin tilt\xE6k web3 veski.",
      successMessage: "Veski\xF0 hefur veri\xF0 b\xE6tt vi\xF0 reikninginn \xFEinn.",
      title: "B\xE6ta vi\xF0 web3 veski",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  isIS
};
//# sourceMappingURL=is-IS.mjs.map