{"version": 3, "sources": ["../src/ca-ES.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const caES: LocalizationResource = {\n  locale: 'ca-ES',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Enrere',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Per defecte',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Un altre dispositiu impostor',\n  badge__primary: 'Principal',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Requereix acció',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Aquest dispositiu',\n  badge__unverified: 'No verificat',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: \"Dispositiu de l'usuari\",\n  badge__you: 'Tu',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Crea organització',\n    invitePage: {\n      formButtonReset: 'Omet',\n    },\n    title: 'Crea organització',\n  },\n  dates: {\n    lastDay: \"Ahir a les {{ date | timeString('en-US') }}\",\n    next6Days: \"{{ date | weekday('en-US','long') }} a les {{ date | timeString('en-US') }}\",\n    nextDay: \"Demà a les {{ date | timeString('en-US') }}\",\n    numeric: \"{{ date | numeric('en-US') }}\",\n    previous6Days: \"El darrer {{ date | weekday('en-US','long') }} a les {{ date | timeString('en-US') }}\",\n    sameDay: \"Avui a les {{ date | timeString('en-US') }}\",\n  },\n  dividerText: 'o',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Utilitza un altre mètode',\n  footerPageLink__help: 'Ajuda',\n  footerPageLink__privacy: 'Privacitat',\n  footerPageLink__terms: 'Termes',\n  formButtonPrimary: 'Continua',\n  formButtonPrimary__verify: 'Verifica',\n  formFieldAction__forgotPassword: 'Has oblidat la contrasenya?',\n  formFieldError__matchingPasswords: 'Les contrasenyes coincideixen.',\n  formFieldError__notMatchingPasswords: 'Les contrasenyes no coincideixen.',\n  formFieldError__verificationLinkExpired: \"L'enllaç de verificació ha caducat. Si us plau, sol·licita un nou enllaç.\",\n  formFieldHintText__optional: 'Opcional',\n  formFieldHintText__slug: \"Un slug és un ID llegible per humans que ha de ser únic. Sovint s'utilitza en URL.\",\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: 'Codi de seguretat',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Eliminar compte',\n  formFieldInputPlaceholder__emailAddress: '<EMAIL>',\n  formFieldInputPlaceholder__emailAddress_username: \"<EMAIL> o nom d'usuari\",\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: 'Nom',\n  formFieldInputPlaceholder__lastName: 'Cognoms',\n  formFieldInputPlaceholder__organizationDomain: 'exemple.com',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: '<EMAIL>',\n  formFieldInputPlaceholder__organizationName: \"Nom de l'organització\",\n  formFieldInputPlaceholder__organizationSlug: 'la-meva-org',\n  formFieldInputPlaceholder__password: 'Contrasenya',\n  formFieldInputPlaceholder__phoneNumber: 'Número de telèfon',\n  formFieldInputPlaceholder__username: \"Nom d'usuari\",\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Activa invitacions automàtiques per a aquest domini',\n  formFieldLabel__backupCode: 'Codi de seguretat',\n  formFieldLabel__confirmDeletion: 'Confirmació',\n  formFieldLabel__confirmPassword: 'Confirma la contrasenya',\n  formFieldLabel__currentPassword: 'Contrasenya actual',\n  formFieldLabel__emailAddress: 'Adreça de correu electrònic',\n  formFieldLabel__emailAddress_username: \"Adreça de correu electrònic o nom d'usuari\",\n  formFieldLabel__emailAddresses: 'Adreces de correu electrònic',\n  formFieldLabel__firstName: 'Nom',\n  formFieldLabel__lastName: 'Cognoms',\n  formFieldLabel__newPassword: 'Nova contrasenya',\n  formFieldLabel__organizationDomain: 'Domini',\n  formFieldLabel__organizationDomainDeletePending: 'Elimina invitacions pendents i suggeriments',\n  formFieldLabel__organizationDomainEmailAddress: 'Adreça de correu electrònic de verificació',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Introdueix una adreça de correu electrònic sota aquest domini per rebre un codi i verificar aquest domini.',\n  formFieldLabel__organizationName: 'Nom',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: undefined,\n  formFieldLabel__password: 'Contrasenya',\n  formFieldLabel__phoneNumber: 'Número de telèfon',\n  formFieldLabel__role: 'Rol',\n  formFieldLabel__signOutOfOtherSessions: 'Tanca la sessió de tots els altres dispositius',\n  formFieldLabel__username: \"Nom d'usuari\",\n  impersonationFab: {\n    action__signOut: 'Tanca la sessió',\n    title: 'Connectat com a {{identifier}}',\n  },\n  maintenanceMode: undefined,\n  membershipRole__admin: 'Administrador',\n  membershipRole__basicMember: 'Membre',\n  membershipRole__guestMember: 'Convidat',\n  organizationList: {\n    action__createOrganization: 'Crea organització',\n    action__invitationAccept: 'Uneix-te',\n    action__suggestionsAccept: 'Sol·licita unir-te',\n    createOrganization: 'Crea Organització',\n    invitationAcceptedLabel: 'Unit',\n    subtitle: 'per continuar a {{applicationName}}',\n    suggestionsAcceptedLabel: 'Aprovació pendent',\n    title: 'Trieu un compte',\n    titleWithoutPersonal: 'Trieu una organització',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Invitacions automàtiques',\n    badge__automaticSuggestion: 'Suggeriments automàtics',\n    badge__manualInvitation: 'Sense inscripció automàtica',\n    badge__unverified: 'No verificat',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        \"Afegeix el domini per verificar. Els usuaris amb adreces de correu electrònic en aquest domini poden unir-se a l'organització automàticament o sol·licitar unir-se.\",\n      title: 'Afegeix domini',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        \"Les invitacions no s'han pogut enviar. Ja hi ha invitacions pendents per a les següents adreces de correu electrònic: {{email_addresses}}.\",\n      formButtonPrimary__continue: 'Envia invitacions',\n      selectDropdown__role: 'Selecciona rol',\n      subtitle: 'Introdueix o enganxa una o més adreces de correu electrònic, separades per espais o comes.',\n      successMessage: \"Les invitacions s'han enviat correctament\",\n      title: 'Convida nous membres',\n    },\n    membersPage: {\n      action__invite: 'Convida',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Elimina membre',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'Unit',\n        tableHeader__role: 'Rol',\n        tableHeader__user: 'Usuari',\n      },\n      detailsTitle__emptyRow: 'No hi ha membres per mostrar',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            \"Convida usuaris connectant un domini de correu electrònic amb la teva organització. Qualsevol que es registri amb un domini de correu electrònic coincident podrà unir-se a l'organització en qualsevol moment.\",\n          headerTitle: 'Invitacions automàtiques',\n          primaryButton: 'Gestiona dominis verificats',\n        },\n        table__emptyRow: 'No hi ha invitacions per mostrar',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Revoca invitació',\n        tableHeader__invited: 'Convidat',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Els usuaris que es registren amb un domini de correu electrònic coincident, podran veure una suggerència per sol·licitar unir-se a la teva organització.',\n          headerTitle: 'Suggeriments automàtics',\n          primaryButton: 'Gestiona dominis verificats',\n        },\n        menuAction__approve: 'Aprova',\n        menuAction__reject: 'Rebutja',\n        tableHeader__requested: 'Accés sol·licitat',\n        table__emptyRow: 'No hi ha sol·licituds per mostrar',\n      },\n      start: {\n        headerTitle__invitations: 'Invitacions',\n        headerTitle__members: 'Membres',\n        headerTitle__requests: 'Sol·licituds',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Gestiona la teva organització.',\n      general: 'General',\n      members: 'Membres',\n      title: 'Organització',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Escriu \"{{organizationName}}\" a continuació per continuar.',\n          messageLine1: 'Estàs segur que vols eliminar aquesta organització?',\n          messageLine2: 'Aquesta acció és permanent i irreversible.',\n          successMessage: \"Has eliminat l'organització.\",\n          title: 'Elimina organització',\n        },\n        leaveOrganization: {\n          actionDescription: 'Escriu \"{{organizationName}}\" a continuació per continuar.',\n          messageLine1:\n            \"Estàs segur que vols deixar aquesta organització? Perdràs l'accés a aquesta organització i les seves aplicacions.\",\n          messageLine2: 'Aquesta acció és permanent i irreversible.',\n          successMessage: \"Has deixat l'organització.\",\n          title: 'Deixa organització',\n        },\n        title: 'Perill',\n      },\n      domainSection: {\n        menuAction__manage: 'Gestiona',\n        menuAction__remove: 'Elimina',\n        menuAction__verify: 'Verifica',\n        primaryButton: 'Afegeix domini',\n        subtitle:\n          \"Permet als usuaris unir-se a l'organització automàticament o sol·licitar unir-se basant-se en un domini de correu electrònic verificat.\",\n        title: 'Dominis verificats',\n      },\n      successMessage: \"L'organització s'ha actualitzat.\",\n      title: 'Actualitza perfil',\n    },\n    removeDomainPage: {\n      messageLine1: 'El domini de correu electrònic {{domain}} serà eliminat.',\n      messageLine2: \"Els usuaris ja no podran unir-se a l'organització automàticament després d'això.\",\n      successMessage: '{{domain}} ha estat eliminat.',\n      title: 'Elimina domini',\n    },\n    start: {\n      headerTitle__general: 'General',\n      headerTitle__members: 'Membres',\n      profileSection: {\n        primaryButton: 'Actualitza perfil',\n        title: \"Perfil de l'Organització\",\n        uploadAction__title: 'Logotip',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Eliminar aquest domini afectarà els usuaris convidats.',\n        removeDomainActionLabel__remove: 'Elimina domini',\n        removeDomainSubtitle: 'Elimina aquest domini dels teus dominis verificats',\n        removeDomainTitle: 'Elimina domini',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          \"Els usuaris són convidats automàticament a unir-se a l'organització quan es registren i poden unir-se en qualsevol moment.\",\n        automaticInvitationOption__label: 'Invitacions automàtiques',\n        automaticSuggestionOption__description:\n          \"Els usuaris reben una suggerència per sol·licitar unir-se, però ha de ser aprovat per un administrador abans de poder-se unir a l'organització.\",\n        automaticSuggestionOption__label: 'Suggeriments automàtics',\n        calloutInfoLabel: \"Canviar el mode d'inscripció només afectarà els nous usuaris.\",\n        calloutInvitationCountLabel: 'Invitacions pendents enviades a usuaris: {{count}}',\n        calloutSuggestionCountLabel: 'Suggeriments pendents enviats a usuaris: {{count}}',\n        manualInvitationOption__description: \"Els usuaris només poden ser convidats manualment a l'organització.\",\n        manualInvitationOption__label: 'Sense inscripció automàtica',\n        subtitle: \"Trieu com els usuaris d'aquest domini poden unir-se a l'organització.\",\n      },\n      start: {\n        headerTitle__danger: 'Perill',\n        headerTitle__enrollment: \"Opcions d'inscripció\",\n      },\n      subtitle: \"El domini {{domain}} ara està verificat. Continua seleccionant el mode d'inscripció.\",\n      title: 'Actualitza {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Introdueix el codi de verificació enviat al teu correu electrònic',\n      formTitle: 'Codi de verificació',\n      resendButton: 'No has rebut el codi? Reenvia',\n      subtitle: 'El domini {{domainName}} necessita ser verificat via correu electrònic.',\n      subtitleVerificationCodeScreen:\n        \"S'ha enviat un codi de verificació a {{emailAddress}}. Introdueix el codi per continuar.\",\n      title: 'Verifica domini',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Crea organització',\n    action__invitationAccept: 'Uneix-te',\n    action__manageOrganization: 'Gestiona',\n    action__suggestionsAccept: 'Sol·licita unir-te',\n    notSelected: \"No s'ha seleccionat cap organització\",\n    personalWorkspace: 'Compte personal',\n    suggestionsAcceptedLabel: 'Aprovació pendent',\n  },\n  paginationButton__next: 'Següent',\n  paginationButton__previous: 'Anterior',\n  paginationRowText__displaying: 'Mostrant',\n  paginationRowText__of: 'de',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Afegeix compte',\n      action__signOutAll: 'Tanca la sessió de tots els comptes',\n      subtitle: 'Selecciona el compte amb el qual vols continuar.',\n      title: 'Trieu un compte',\n    },\n    alternativeMethods: {\n      actionLink: 'Obtén ajuda',\n      actionText: \"No tens cap d'aquests?\",\n      blockButton__backupCode: 'Utilitza un codi de seguretat',\n      blockButton__emailCode: 'Envia codi per correu electrònic a {{identifier}}',\n      blockButton__emailLink: 'Envia enllaç per correu electrònic a {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Inicia sessió amb la teva contrasenya',\n      blockButton__phoneCode: 'Envia codi SMS a {{identifier}}',\n      blockButton__totp: \"Utilitza la teva aplicació d'autenticació\",\n      getHelp: {\n        blockButton__emailSupport: 'Suport per correu electrònic',\n        content:\n          \"Si tens dificultats per iniciar sessió al teu compte, envia'ns un correu electrònic i treballarem amb tu per restaurar l'accés tan aviat com sigui possible.\",\n        title: 'Obtén ajuda',\n      },\n      subtitle: \"Tens problemes? Pots utilitzar qualsevol d'aquests mètodes per iniciar sessió.\",\n      title: 'Utilitza un altre mètode',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: \"El teu codi de seguretat és el que vas obtenir quan vas configurar l'autenticació de dos passos.\",\n      title: 'Introdueix un codi de seguretat',\n    },\n    emailCode: {\n      formTitle: 'Codi de verificació',\n      resendButton: 'No has rebut el codi? Reenvia',\n      subtitle: 'per continuar a {{applicationName}}',\n      title: 'Comprova el teu correu electrònic',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: 'Torna a la pestanya original per continuar.',\n        title: 'Aquest enllaç de verificació ha caducat',\n      },\n      failed: {\n        subtitle: 'Torna a la pestanya original per continuar.',\n        title: 'Aquest enllaç de verificació no és vàlid',\n      },\n      formSubtitle: \"Utilitza l'enllaç de verificació enviat al teu correu electrònic\",\n      formTitle: 'Enllaç de verificació',\n      loading: {\n        subtitle: 'Seràs redirigit aviat',\n        title: 'Iniciant sessió...',\n      },\n      resendButton: \"No has rebut l'enllaç? Reenvia\",\n      subtitle: 'per continuar a {{applicationName}}',\n      title: 'Comprova el teu correu electrònic',\n      unusedTab: {\n        title: 'Pots tancar aquesta pestanya',\n      },\n      verified: {\n        subtitle: 'Seràs redirigit aviat',\n        title: 'Has iniciat sessió amb èxit',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Torna a la pestanya original per continuar',\n        subtitleNewTab: 'Torna a la pestanya recentment oberta per continuar',\n        titleNewTab: \"S'ha iniciat sessió en una altra pestanya\",\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Codi de restabliment de contrasenya',\n      resendButton: 'No has rebut el codi? Reenvia',\n      subtitle: 'per restablir la teva contrasenya',\n      subtitle_email: 'Primer, introdueix el codi enviat al teu ID de correu electrònic',\n      subtitle_phone: 'Primer, introdueix el codi enviat al teu telèfon',\n      title: 'Restableix la contrasenya',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Restableix la teva contrasenya',\n      label__alternativeMethods: 'O bé, inicia sessió amb un altre mètode',\n      title: 'Has oblidat la contrasenya?',\n    },\n    noAvailableMethods: {\n      message: \"No es pot procedir amb l'inici de sessió. No hi ha cap factor d'autenticació disponible.\",\n      subtitle: \"S'ha produït un error\",\n      title: 'No es pot iniciar sessió',\n    },\n    passkey: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Utilitza un altre mètode',\n      subtitle: 'Introdueix la contrasenya associada al teu compte',\n      title: 'Introdueix la teva contrasenya',\n    },\n    passwordPwned: {\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: 'Codi de verificació',\n      resendButton: 'No has rebut el codi? Reenvia',\n      subtitle: 'per continuar a {{applicationName}}',\n      title: 'Comprova el teu telèfon',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Codi de verificació',\n      resendButton: 'No has rebut el codi? Reenvia',\n      subtitle: 'Per continuar, introdueix el codi de verificació enviat al teu telèfon',\n      title: 'Comprova el teu telèfon',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Restableix la contrasenya',\n      requiredMessage:\n        'Ja existeix un compte amb una adreça de correu electrònic no verificada. Si us plau, restableix la teva contrasenya per seguretat.',\n      successMessage: \"La teva contrasenya s'ha canviat amb èxit. Iniciant sessió, espera un moment.\",\n      title: 'Estableix una nova contrasenya',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Necessitem verificar la teva identitat abans de restablir la teva contrasenya.',\n    },\n    start: {\n      actionLink: \"Registra't\",\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Utilitza correu electrònic',\n      actionLink__use_email_username: \"Utilitza correu electrònic o nom d'usuari\",\n      actionLink__use_passkey: undefined,\n      actionLink__use_phone: 'Utilitza telèfon',\n      actionLink__use_username: \"Utilitza nom d'usuari\",\n      actionText: 'No tens un compte?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Benvingut de nou! Si us plau, inicia sessió per continuar',\n      subtitleCombined: undefined,\n      title: 'Inicia sessió a {{applicationName}}',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Codi de verificació',\n      subtitle: \"Per continuar, introdueix el codi de verificació generat per la teva aplicació d'autenticació\",\n      title: 'Verificació de dos passos',\n    },\n  },\n  signInEnterPasswordTitle: 'Introdueix la teva contrasenya',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Inicia sessió',\n      actionText: 'Ja tens un compte?',\n      subtitle: 'Si us plau, completa els detalls restants per continuar.',\n      title: 'Completa els camps que falten',\n    },\n    emailCode: {\n      formSubtitle: 'Introdueix el codi de verificació enviat a la teva adreça de correu electrònic',\n      formTitle: 'Codi de verificació',\n      resendButton: 'No has rebut el codi? Reenvia',\n      subtitle: 'Introdueix el codi de verificació enviat al teu correu',\n      title: 'Verifica el teu correu electrònic',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: \"Utilitza l'enllaç de verificació enviat a la teva adreça de correu electrònic\",\n      formTitle: 'Enllaç de verificació',\n      loading: {\n        title: 'Registrant...',\n      },\n      resendButton: \"No has rebut l'enllaç? Reenvia\",\n      subtitle: 'per continuar a {{applicationName}}',\n      title: 'Verifica el teu correu electrònic',\n      verified: {\n        title: 'Registre completat amb èxit',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Torna a la pestanya recentment oberta per continuar',\n        subtitleNewTab: 'Torna a la pestanya anterior per continuar',\n        title: 'Correu electrònic verificat amb èxit',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Introdueix el codi de verificació enviat al teu número de telèfon',\n      formTitle: 'Codi de verificació',\n      resendButton: 'No has rebut el codi? Reenvia',\n      subtitle: 'Introdueix el codi de verificació enviat al teu telèfon',\n      title: 'Verifica el teu telèfon',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Inicia sessió',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: 'Ja tens un compte?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Benvingut! Si us plau, completa els detalls per començar.',\n      subtitleCombined: 'Benvingut! Si us plau, completa els detalls per començar.',\n      title: 'Crea el teu compte',\n      titleCombined: 'Crea el teu compte',\n    },\n  },\n  socialButtonsBlockButton: 'Continua amb {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      \"El registre no ha estat exitós a causa de validacions de seguretat fallides. Si us plau, actualitza la pàgina per tornar-ho a intentar o posa't en contacte amb el suport per obtenir més assistència.\",\n    captcha_unavailable:\n      \"El registre no ha estat exitós a causa de la validació fallida de bot. Si us plau, actualitza la pàgina per tornar-ho a intentar o posa't en contacte amb el suport per obtenir més assistència.\",\n    form_code_incorrect: 'El codi introduït no és vàlid. Si us plau, comprova el codi i torna-ho a intentar.',\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: 'No hem trobat cap compte amb aquests detalls.',\n    form_param_format_invalid: 'Format de paràmetre no vàlid.',\n    form_param_format_invalid__email_address: \"L'adreça de correu electrònic ha de ser una adreça vàlida.\",\n    form_param_format_invalid__phone_number: 'El número de telèfon ha de tenir un format internacional vàlid.',\n    form_param_max_length_exceeded__first_name: 'El nom no ha de superar els 256 caràcters.',\n    form_param_max_length_exceeded__last_name: 'Els cognoms no han de superar els 256 caràcters.',\n    form_param_max_length_exceeded__name: 'El nom no ha de superar els 256 caràcters.',\n    form_param_nil: 'El valor del camp no pot ser nul. Si us plau, completa aquest camp.',\n    form_param_value_invalid: undefined,\n    form_password_incorrect: 'La contrasenya introduïda és incorrecta.',\n    form_password_length_too_short: 'La teva contrasenya ha de tenir almenys 8 caràcters.',\n    form_password_not_strong_enough: 'La teva contrasenya no és prou forta.',\n    form_password_pwned:\n      'Aquesta contrasenya ha aparegut en una filtració i no es pot utilitzar, si us plau, prova una altra contrasenya.',\n    form_password_pwned__sign_in: undefined,\n    form_password_size_in_bytes_exceeded:\n      'La teva contrasenya ha superat el nombre màxim de bytes permesos, si us plau, redueix-la o elimina alguns caràcters especials.',\n    form_password_validation_failed: 'Contrasenya incorrecta',\n    form_username_invalid_character: \"El nom d'usuari conté caràcters no vàlids.\",\n    form_username_invalid_length: \"El nom d'usuari ha de tenir entre 3 i 50 caràcters.\",\n    identification_deletion_failed: 'No pots eliminar la teva última identificació.',\n    not_allowed_access:\n      \"L'adreça de correu electrònic o el número de telèfon no es permet registrar-se. Això podria ser degut a l'ús de '+', '=', '#' o '.' a la vostra adreça de correu electrònic, utilitzant un domini connectat amb un servei de correu electrònic temporal o bloquejant-se explícitament. Si creieu que es tracta d'un error, poseu-vos en contacte amb el servei d'assistència.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: 'menys de {{length}} caràcters',\n      minimumLength: '{{length}} o més caràcters',\n      requireLowercase: 'una lletra minúscula',\n      requireNumbers: 'un número',\n      requireSpecialCharacter: 'un caràcter especial',\n      requireUppercase: 'una lletra majúscula',\n      sentencePrefix: 'La teva contrasenya ha de contenir',\n    },\n    phone_number_exists: \"Aquest número de telèfon ja està en ús. Si us plau, prova'n un altre.\",\n    session_exists: 'Ja estàs connectat.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'La teva contrasenya funciona, però podria ser més forta. Prova afegint més caràcters.',\n      goodPassword: 'La teva contrasenya compleix tots els requisits necessaris.',\n      notEnough: 'La teva contrasenya no és prou forta.',\n      suggestions: {\n        allUppercase: 'Capitalitza algunes, però no totes les lletres.',\n        anotherWord: 'Afegeix més paraules que siguin menys comunes.',\n        associatedYears: 'Evita anys que estiguin associats amb tu.',\n        capitalization: 'Capitalitza més que la primera lletra.',\n        dates: 'Evita dates i anys que estiguin associats amb tu.',\n        l33t: \"Evita substitucions de lletres previsibles com '@' per 'a'.\",\n        longerKeyboardPattern:\n          'Utilitza patrons de teclat més llargs i canvia la direcció de la tipografia diverses vegades.',\n        noNeed: 'Pots crear contrasenyes fortes sense utilitzar símbols, números o lletres majúscules.',\n        pwned: 'Si utilitzes aquesta contrasenya en un altre lloc, hauries de canviar-la.',\n        recentYears: 'Evita anys recents.',\n        repeated: 'Evita paraules i caràcters repetits.',\n        reverseWords: 'Evita ortografies invertides de paraules comunes.',\n        sequences: 'Evita seqüències de caràcters comuns.',\n        useWords: 'Utilitza diverses paraules, però evita frases comunes.',\n      },\n      warnings: {\n        common: 'Aquesta és una contrasenya molt utilitzada.',\n        commonNames: \"Els noms i cognoms comuns són fàcils d'endevinar.\",\n        dates: \"Les dates són fàcils d'endevinar.\",\n        extendedRepeat: 'Els patrons de caràcters repetits com \"abcabcabc\" són fàcils d\\'endevinar.',\n        keyPattern: \"Els patrons curts de teclat són fàcils d'endevinar.\",\n        namesByThemselves: \"Els noms o cognoms sols són fàcils d'endevinar.\",\n        pwned: 'La teva contrasenya va ser exposada per una violació de dades a Internet.',\n        recentYears: \"Els anys recents són fàcils d'endevinar.\",\n        sequences: 'Les seqüències de caràcters comuns com \"abc\" són fàcils d\\'endevinar.',\n        similarToCommon: 'Això és similar a una contrasenya molt utilitzada.',\n        simpleRepeat: 'Els caràcters repetits com \"aaa\" són fàcils d\\'endevinar.',\n        straightRow: \"Les files rectes de tecles del teclat són fàcils d'endevinar.\",\n        topHundred: 'Aquesta és una contrasenya molt utilitzada.',\n        topTen: 'Aquesta és una contrasenya altament utilitzada.',\n        userInputs: 'No hi hauria de tenir cap dada personal ni relacionada amb la pàgina.',\n        wordByItself: \"Les paraules soles són fàcils d'endevinar.\",\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Afegeix compte',\n    action__manageAccount: 'Gestiona compte',\n    action__signOut: 'Tanca sessió',\n    action__signOutAll: 'Tanca sessió de tots els comptes',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Copiat!',\n      actionLabel__copy: 'Copia tot',\n      actionLabel__download: 'Descarrega .txt',\n      actionLabel__print: 'Imprimeix',\n      infoText1: \"Els codis de seguretat s'activaran per a aquest compte.\",\n      infoText2:\n        \"Guarda els codis de seguretat en secret i emmagatzema'ls de manera segura. Pots regenerar els codis de seguretat si sospites que han estat compromesos.\",\n      subtitle__codelist: \"Emmagatzema'ls de manera segura i mantingues-los en secret.\",\n      successMessage:\n        \"Ara els codis de seguretat estan activats. Pots utilitzar un d'aquests per iniciar sessió al teu compte, si perds l'accés al teu dispositiu d'autenticació. Cada codi només es pot utilitzar una vegada.\",\n      successSubtitle:\n        \"Pots utilitzar un d'aquests per iniciar sessió al teu compte, si perds l'accés al teu dispositiu d'autenticació.\",\n      title: 'Afegeix verificació amb codi de seguretat',\n      title__codelist: 'Codis de seguretat',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Selecciona un proveïdor per connectar el teu compte.',\n      formHint__noAccounts: 'No hi ha proveïdors de comptes externs disponibles.',\n      removeResource: {\n        messageLine1: \"{{identifier}} serà eliminat d'aquest compte.\",\n        messageLine2:\n          'Ja no podràs utilitzar aquest compte connectat i qualsevol funcionalitat dependent deixarà de funcionar.',\n        successMessage: '{{connectedAccount}} ha estat eliminat del teu compte.',\n        title: 'Elimina compte connectat',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}',\n      successMessage: \"El proveïdor s'ha afegit al teu compte\",\n      title: 'Afegeix compte connectat',\n    },\n    deletePage: {\n      actionDescription: 'Escriu \"Elimina compte\" a continuació per continuar.',\n      confirm: 'Elimina compte',\n      messageLine1: 'Estàs segur que vols eliminar el teu compte?',\n      messageLine2: 'Aquesta acció és permanent i irreversible.',\n      title: 'Elimina compte',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint:\n          \"S'enviarà un correu electrònic que conté un codi de verificació a aquesta adreça de correu electrònic.\",\n        formSubtitle: 'Introdueix el codi de verificació enviat a {{identifier}}',\n        formTitle: 'Codi de verificació',\n        resendButton: 'No has rebut el codi? Reenvia',\n        successMessage: \"El correu electrònic {{identifier}} s'ha afegit al teu compte.\",\n      },\n      emailLink: {\n        formHint:\n          \"S'enviarà un correu electrònic que conté un enllaç de verificació a aquesta adreça de correu electrònic.\",\n        formSubtitle: \"Fes clic a l'enllaç de verificació al correu enviat a {{identifier}}\",\n        formTitle: 'Enllaç de verificació',\n        resendButton: \"No has rebut l'enllaç? Reenvia\",\n        successMessage: \"El correu electrònic {{identifier}} s'ha afegit al teu compte.\",\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: \"{{identifier}} serà eliminat d'aquest compte.\",\n        messageLine2: 'Ja no podràs iniciar sessió utilitzant aquesta adreça de correu electrònic.',\n        successMessage: '{{emailAddress}} ha estat eliminat del teu compte.',\n        title: 'Elimina adreça de correu electrònic',\n      },\n      title: 'Afegeix adreça de correu electrònic',\n      verifyTitle: 'Verifica adreça de correu electrònic',\n    },\n    formButtonPrimary__add: 'Afegeix',\n    formButtonPrimary__continue: 'Continua',\n    formButtonPrimary__finish: 'Finalitza',\n    formButtonPrimary__remove: 'Elimina',\n    formButtonPrimary__save: 'Guarda',\n    formButtonReset: 'Cancel·la',\n    mfaPage: {\n      formHint: 'Selecciona un mètode per afegir.',\n      title: 'Afegeix verificació en dos passos',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Utilitza el número existent',\n      primaryButton__addPhoneNumber: 'Afegeix número de telèfon',\n      removeResource: {\n        messageLine1: '{{ identifier }} ja no rebrà codis de verificació quan iniciï sessió.',\n        messageLine2: 'El teu compte podria no ser tan segur.Estàs segur que vols continuar ? ',\n        successMessage: 'La verificació en dos passos per SMS ha estat eliminada per { { mfaPhoneCode } } ',\n        title: 'Elimina la verificació en dos passos',\n      },\n      subtitle__availablePhoneNumbers:\n        'Selecciona un número de telèfon existent per registrar - lo per la verificació en dos passos per SMS o afegeix - ne un de nou.',\n      subtitle__unavailablePhoneNumbers:\n        'No hi ha números de telèfon disponibles per registrar - los per la verificació en dos passos per SMS, si us plau, afegeix - ne un de nou.',\n      successMessage1:\n        'Quan iniciïs sessió, necessitaràs introduir un codi de verificació enviat a aquest número de telèfon com un pas addicional.',\n      successMessage2:\n        \"Guarda aquests codis de seguretat i emmagatzema'ls en un lloc segur. Si perds l'accés al teu dispositiu d'autenticació, pots utilitzar els codis de seguretat per iniciar sessió.\",\n      successTitle: 'La verificació per codi SMS activada',\n      title: 'Afegeix verificació per codi SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Escaneja el codi QR en lloc',\n        buttonUnableToScan__nonPrimary: 'No pots escanejar el codi QR? ',\n        infoText__ableToScan:\n          \"Configura un nou mètode d'inici de sessió a la teva aplicació d'autenticador i escaneja el següent codi QR per vincular - lo al teu compte.\",\n        infoText__unableToScan:\n          \"Configura un nou mètode d'inici de sessió al teu autenticador i introdueix la clau proporcionada a continuació.\",\n        inputLabel__unableToScan1:\n          \"Assegura't que els contrasenyes basades en temps o d'ús únic estan habilitats, després finalitza la vinculació del teu compte.\",\n        inputLabel__unableToScan2:\n          \"Alternativament, si el teu autenticador suporta URI de TOTP, també pots copiar l'URI completa.\",\n      },\n      removeResource: {\n        messageLine1: \"Els codis de verificació d'aquest autenticador ja no seran necessaris quan iniciïs sessió.\",\n        messageLine2: 'El teu compte podria no ser tan segur.Estàs segur que vols continuar? ',\n        successMessage: \"La verificació en dos passos mitjançant l'aplicació d'autenticació ha estat eliminada.\",\n        title: 'Elimina la verificació en dos passos',\n      },\n      successMessage:\n        \"La verificació en dos passos ara està habilitada.Quan iniciïs sessió, necessitaràs introduir un codi de verificació d'aquest autenticador com un pas addicional.\",\n      title: \"Afegeix aplicació d'autenticació\",\n      verifySubtitle: 'Introdueix el codi de verificació generat pel teu autenticador',\n      verifyTitle: 'Codi de verificació',\n    },\n    mobileButton__menu: 'Menú',\n    navbar: {\n      account: 'Perfil',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Gestiona la informació del teu compte.',\n      security: 'Seguretat',\n      title: 'Compte',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: undefined,\n        title: undefined,\n      },\n      subtitle__rename: undefined,\n      title__rename: undefined,\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Es recomana tancar sessió en tots els altres dispositius que hagin utilitzat la teva contrasenya antiga.',\n      readonly:\n        \"Actualment no pots editar la teva contrasenya perquè pots iniciar sessió només a través de la connexió d'empresa.\",\n      successMessage__set: 'La teva contrasenya ha estat establerta.',\n      successMessage__signOutOfOtherSessions: \"S'ha tancat sessió en tots els altres dispositius.\",\n      successMessage__update: 'La teva contrasenya ha estat actualitzada.',\n      title__set: 'Estableix contrasenya',\n      title__update: 'Actualitza contrasenya',\n    },\n    phoneNumberPage: {\n      infoText:\n        \"S'enviarà un missatge de text que conté un codi de verificació a aquest número de telèfon. Poden aplicar-se tarifes de missatges i dades.\",\n      removeResource: {\n        messageLine1: \"{ { identifier } } serà eliminat d'aquest compte.\",\n        messageLine2: 'Ja no podràs iniciar sessió utilitzant aquest número de telèfon.',\n        successMessage: '{ { phoneNumber } } ha estat eliminat del teu compte.',\n        title: 'Elimina número de telèfon',\n      },\n      successMessage: \"{ { identifier } } s'ha afegit al teu compte.\",\n      title: 'Afegeix número de telèfon',\n      verifySubtitle: 'Introdueix el codi de verificació enviat a { { identifier } } ',\n      verifyTitle: 'Verifica número de telèfon',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Mida recomanada 1:1, fins a 10MB.',\n      imageFormDestructiveActionSubtitle: 'Elimina',\n      imageFormSubtitle: 'Puja',\n      imageFormTitle: 'Imatge de perfil',\n      readonly: \"La informació del teu perfil ha estat proporcionada per la connexió d'empresa i no pot ser editada.\",\n      successMessage: 'El teu perfil ha estat actualitzat.',\n      title: 'Actualitza perfil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Tanca sessió del dispositiu',\n        title: 'Dispositius actius',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Torna-ho a intentar',\n        actionLabel__reauthorize: 'Autoritza ara',\n        destructiveActionTitle: 'Elimina',\n        primaryButton: 'Connecta compte',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'Els àmbits requerits han estat actualitzats, i podràs estar experimentant funcionalitat limitada. Si us plau, reautoritza aquesta aplicació per evitar qualsevol problema',\n        title: 'Comptes connectats',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Elimina compte',\n        title: 'Elimina compte',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Elimina correu electrònic',\n        detailsAction__nonPrimary: 'Estableix com a principal',\n        detailsAction__primary: 'Completa la verificació',\n        detailsAction__unverified: 'Verifica',\n        primaryButton: 'Afegeix adreça de correu electrònic',\n        title: 'Adreces de correu electrònic',\n      },\n      enterpriseAccountsSection: {\n        title: \"Comptes d'empresa\",\n      },\n      headerTitle__account: 'Detalls del perfil',\n      headerTitle__security: 'Seguretat',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Regenera',\n          headerTitle: 'Codis de seguretat',\n          subtitle__regenerate:\n            'Obtén un nou conjunt de codis de seguretat segurs. Els codis de seguretat anteriors seran eliminats i no podran ser utilitzats.',\n          title__regenerate: 'Regenera codis de seguretat',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Estableix com a predeterminat',\n          destructiveActionLabel: 'Elimina',\n        },\n        primaryButton: 'Afegeix verificació en dos passos',\n        title: 'Verificació en dos passos',\n        totp: {\n          destructiveActionTitle: 'Elimina',\n          headerTitle: \"Aplicació d'autenticació\",\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: undefined,\n        menuAction__rename: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Estableix contrasenya',\n        primaryButton__updatePassword: 'Actualitza contrasenya',\n        title: 'Contrasenya',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Elimina número de telèfon',\n        detailsAction__nonPrimary: 'Estableix com a principal',\n        detailsAction__primary: 'Completa la verificació',\n        detailsAction__unverified: 'Verifica número de telèfon',\n        primaryButton: 'Afegeix número de telèfon',\n        title: 'Números de telèfon',\n      },\n      profileSection: {\n        primaryButton: 'Actualitza perfil',\n        title: 'Perfil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: \"Estableix nom d'usuari\",\n        primaryButton__updateUsername: \"Actualitza nom d'usuari\",\n        title: \"Nom d'usuari\",\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Elimina cartera',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Carteres Web3',\n        title: 'Carteres Web3',\n      },\n    },\n    usernamePage: {\n      successMessage: \"El teu nom d'usuari ha estat actualitzat.\",\n      title__set: \"Estableix nom d'usuari\",\n      title__update: \"Actualitza nom d'usuari\",\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: \"{{identifier}} serà eliminat d'aquest compte.\",\n        messageLine2: 'Ja no podràs iniciar sessió utilitzant aquesta cartera Web3.',\n        successMessage: '{{web3Wallet}} ha estat eliminada del teu compte.',\n        title: 'Elimina cartera Web3',\n      },\n      subtitle__availableWallets: 'Selecciona una cartera Web3 per connectar al teu compte.',\n      subtitle__unavailableWallets: 'No hi ha carteres Web3 disponibles.',\n      successMessage: 'La cartera ha estat afegida al teu compte.',\n      title: 'Afegeix cartera Web3',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBACE;AAAA,MACF,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBACE;AAAA,QACF,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UACE;AAAA,QACF,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UACE;AAAA,QACF,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UACE;AAAA,MACF,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}