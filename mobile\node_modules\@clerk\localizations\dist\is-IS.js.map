{"version": 3, "sources": ["../src/is-IS.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const isIS: LocalizationResource = {\n  locale: 'is-IS',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Til baka',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Sjálfgefið',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Önnur tæki sem herma eftir',\n  badge__primary: 'Aðal',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Krefst aðgerða',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Þetta tæki',\n  badge__unverified: 'Óstaðfest',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Notendatæki',\n  badge__you: 'Þú',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Stofna samtök',\n    invitePage: {\n      formButtonReset: 'Sleppa',\n    },\n    title: 'Stofna samtök',\n  },\n  dates: {\n    lastDay: \"Í gær kl {{ date | timeString('is-IS') }}\",\n    next6Days: \"{{ date | weekday('is-IS','long') }} kl {{ date | timeString('is-IS') }}\",\n    nextDay: \"Á morgun kl {{ date | timeString('is-IS') }}\",\n    numeric: \"{{ date | numeric('is-IS') }}\",\n    previous6Days: \"Síðasta {{ date | weekday('is-IS','long') }} kl {{ date | timeString('is-IS') }}\",\n    sameDay: \"Í dag kl {{ date | timeString('is-IS') }}\",\n  },\n  dividerText: 'eða',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Nota aðra aðferð',\n  footerPageLink__help: 'Hjálp',\n  footerPageLink__privacy: 'Persónuvernd',\n  footerPageLink__terms: 'Skilmálar',\n  formButtonPrimary: 'Halda áfram',\n  formButtonPrimary__verify: 'Staðfesta',\n  formFieldAction__forgotPassword: 'Gleymt lykilorð?',\n  formFieldError__matchingPasswords: 'Lykilorð passa saman.',\n  formFieldError__notMatchingPasswords: 'Lykilorð passa ekki saman.',\n  formFieldError__verificationLinkExpired: 'Staðfestingartengillinn er útrunninn. Vinsamlegast biðjið um nýjan tengil.',\n  formFieldHintText__optional: 'Valfrjálst',\n  formFieldHintText__slug:\n    'Stubbur (e. slug) er auðlesanlegt auðkenni sem verður að vera einstakt. Það er oft notað í vefslóðum.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Eyða aðgangi',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses: 'dæ*************, dæ**************',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: 'min-samtok',\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Virkja sjálfvirk boð fyrir þetta lén',\n  formFieldLabel__backupCode: 'Öryggiskóði',\n  formFieldLabel__confirmDeletion: 'Staðfesting',\n  formFieldLabel__confirmPassword: 'Staðfesta lykilorð',\n  formFieldLabel__currentPassword: 'Núverandi lykilorð',\n  formFieldLabel__emailAddress: 'Netfang',\n  formFieldLabel__emailAddress_username: 'Netfang eða notendanafn',\n  formFieldLabel__emailAddresses: 'Netföng',\n  formFieldLabel__firstName: 'Fornafn',\n  formFieldLabel__lastName: 'Eftirnafn',\n  formFieldLabel__newPassword: 'Nýtt lykilorð',\n  formFieldLabel__organizationDomain: 'Lén',\n  formFieldLabel__organizationDomainDeletePending: 'Eyða boðum í bið og tillögum',\n  formFieldLabel__organizationDomainEmailAddress: 'Staðfestingarnetfang',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Sláðu inn netfang undir þessu léni til að fá kóða og staðfesta þetta lén.',\n  formFieldLabel__organizationName: 'Nafn',\n  formFieldLabel__organizationSlug: 'Stubbur (e. slug)',\n  formFieldLabel__passkeyName: 'Nafn lykils',\n  formFieldLabel__password: 'Lykilorð',\n  formFieldLabel__phoneNumber: 'Símanúmer',\n  formFieldLabel__role: 'Hlutverk',\n  formFieldLabel__signOutOfOtherSessions: 'Skrá út af öllum öðrum tækjum',\n  formFieldLabel__username: 'Notendanafn',\n  impersonationFab: {\n    action__signOut: 'Skrá út',\n    title: 'Skráður inn sem {{identifier}}',\n  },\n  maintenanceMode: 'Við erum nú í viðhaldi, en ekki hafa áhyggjur, það ætti ekki að taka meira en nokkrar mínútur.',\n  membershipRole__admin: 'Stjórnandi',\n  membershipRole__basicMember: 'Meðlimur',\n  membershipRole__guestMember: 'Gestur',\n  organizationList: {\n    action__createOrganization: 'Stofna samtök',\n    action__invitationAccept: 'Ganga í',\n    action__suggestionsAccept: 'Biðja um að ganga í',\n    createOrganization: 'Stofna samtök',\n    invitationAcceptedLabel: 'Gengið í',\n    subtitle: 'til að halda áfram í {{applicationName}}',\n    suggestionsAcceptedLabel: 'Bíður samþykkis',\n    title: 'Veldu reikning',\n    titleWithoutPersonal: 'Veldu samtök',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Sjálfvirk boð',\n    badge__automaticSuggestion: 'Sjálfvirkar tillögur',\n    badge__manualInvitation: 'Engin sjálfvirk skráning',\n    badge__unverified: 'Óstaðfest',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Bættu við léni til að staðfesta. Notendur með netföng undir þessu léni geta gengið í samtökin sjálfkrafa eða beðið um að ganga í.',\n      title: 'Bæta við léni',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Ekki tókst að senda boðin. Það eru þegar biðboð fyrir eftirfarandi netföng: {{email_addresses}}.',\n      formButtonPrimary__continue: 'Senda boð',\n      selectDropdown__role: 'Veldu hlutverk',\n      subtitle: 'Sláðu inn eða límdu eitt eða fleiri netföng, aðskilin með bilum eða kommum.',\n      successMessage: 'Boð send með góðum árangri',\n      title: 'Bjóða nýja meðlimi',\n    },\n    membersPage: {\n      action__invite: 'Bjóða',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Fjarlægja meðlim',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'Gengið í',\n        tableHeader__role: 'Hlutverk',\n        tableHeader__user: 'Notandi',\n      },\n      detailsTitle__emptyRow: 'Engir meðlimir til að sýna',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Bjóða notendum með því að tengja netfangalén við samtökin þín. Allir sem skrá sig með samsvarandi netfangalén geta gengið í samtökin hvenær sem er.',\n          headerTitle: 'Sjálfvirk boð',\n          primaryButton: 'Stjórna staðfestum lénum',\n        },\n        table__emptyRow: 'Engin boð til að sýna',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Afturkalla boð',\n        tableHeader__invited: 'Boðið',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Notendur sem skrá sig með samsvarandi netfangalén, munu sjá tillögu um að biðja um að ganga í samtökin þín.',\n          headerTitle: 'Sjálfvirkar tillögur',\n          primaryButton: 'Stjórna staðfestum lénum',\n        },\n        menuAction__approve: 'Samþykkja',\n        menuAction__reject: 'Hafna',\n        tableHeader__requested: 'Beðið um aðgang',\n        table__emptyRow: 'Engar beiðnir til að sýna',\n      },\n      start: {\n        headerTitle__invitations: 'Boð',\n        headerTitle__members: 'Meðlimir',\n        headerTitle__requests: 'Beiðnir',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Stjórna samtökunum þínum.',\n      general: 'Almennt',\n      members: 'Meðlimir',\n      title: 'Samtök',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Sláðu inn \"{{organizationName}}\" hér að neðan til að halda áfram.',\n          messageLine1: 'Ertu viss um að þú viljir eyða þessum samtökum?',\n          messageLine2: 'Þessi aðgerð er varanleg og óafturkræf.',\n          successMessage: 'Þú hefur eytt samtökunum.',\n          title: 'Eyða samtökum',\n        },\n        leaveOrganization: {\n          actionDescription: 'Sláðu inn \"{{organizationName}}\" hér að neðan til að halda áfram.',\n          messageLine1:\n            'Ertu viss um að þú viljir yfirgefa þessi samtök? Þú munt missa aðgang að þessum samtökum og forritum þeirra.',\n          messageLine2: 'Þessi aðgerð er varanleg og óafturkræf.',\n          successMessage: 'Þú hefur yfirgefið samtökin.',\n          title: 'Yfirgefa samtök',\n        },\n        title: 'Hætta',\n      },\n      domainSection: {\n        menuAction__manage: 'Stjórna',\n        menuAction__remove: 'Eyða',\n        menuAction__verify: 'Staðfesta',\n        primaryButton: 'Bæta við léni',\n        subtitle:\n          'Leyfa notendum að ganga í samtökin sjálfkrafa eða biðja um að ganga í byggt á staðfestu netfangaléni.',\n        title: 'Staðfest lén',\n      },\n      successMessage: 'Samtökin hafa verið uppfærð.',\n      title: 'Uppfæra prófíl',\n    },\n    removeDomainPage: {\n      messageLine1: 'Netfangalén {{domain}} verður fjarlægt.',\n      messageLine2: 'Notendur munu ekki geta gengið í samtökin sjálfkrafa eftir þetta.',\n      successMessage: '{{domain}} hefur verið fjarlægt.',\n      title: 'Fjarlægja lén',\n    },\n    start: {\n      headerTitle__general: 'Almennt',\n      headerTitle__members: 'Meðlimir',\n      profileSection: {\n        primaryButton: 'Uppfæra prófíl',\n        title: 'Prófíll samtaka',\n        uploadAction__title: 'Merki',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Að fjarlægja þetta lén mun hafa áhrif á boðna notendur.',\n        removeDomainActionLabel__remove: 'Fjarlægja lén',\n        removeDomainSubtitle: 'Fjarlægja þetta lén úr staðfestum lénum þínum',\n        removeDomainTitle: 'Fjarlægja lén',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Notendur eru sjálfkrafa boðnir að ganga í samtökin þegar þeir skrá sig og geta gengið í hvenær sem er.',\n        automaticInvitationOption__label: 'Sjálfvirk boð',\n        automaticSuggestionOption__description:\n          'Notendur fá tillögu um að biðja um að ganga í, en verða að vera samþykktir af stjórnanda áður en þeir geta gengið í samtökin.',\n        automaticSuggestionOption__label: 'Sjálfvirkar tillögur',\n        calloutInfoLabel: 'Að breyta skráningaraðferð mun aðeins hafa áhrif á nýja notendur.',\n        calloutInvitationCountLabel: 'Biðboð send til notenda: {{count}}',\n        calloutSuggestionCountLabel: 'Biðtillögur sendar til notenda: {{count}}',\n        manualInvitationOption__description: 'Notendur geta aðeins verið boðnir handvirkt í samtökin.',\n        manualInvitationOption__label: 'Engin sjálfvirk skráning',\n        subtitle: 'Veldu hvernig notendur frá þessu léni geta gengið í samtökin.',\n      },\n      start: {\n        headerTitle__danger: 'Hætta',\n        headerTitle__enrollment: 'Skráningarmöguleikar',\n      },\n      subtitle: 'Lénið {{domain}} er nú staðfest. Haltu áfram með því að velja skráningarmáta.',\n      title: 'Uppfæra {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Sláðu inn staðfestingarkóðann sem sendur var á netfangið þitt',\n      formTitle: 'Staðfestingarkóði',\n      resendButton: 'Fékkstu ekki kóða? Senda aftur',\n      subtitle: 'Lénið {{domainName}} þarf að vera staðfest með tölvupósti.',\n      subtitleVerificationCodeScreen:\n        'Staðfestingarkóði var sendur á {{emailAddress}}. Sláðu inn kóðann til að halda áfram.',\n      title: 'Staðfesta lén',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Stofna samtök',\n    action__invitationAccept: 'Ganga í',\n    action__manageOrganization: 'Stjórna',\n    action__suggestionsAccept: 'Biðja um að ganga í',\n    notSelected: 'Engin samtök valin',\n    personalWorkspace: 'Persónulegur reikningur',\n    suggestionsAcceptedLabel: 'Bíður samþykkis',\n  },\n  paginationButton__next: 'Næsta',\n  paginationButton__previous: 'Fyrri',\n  paginationRowText__displaying: 'Sýnir',\n  paginationRowText__of: 'af',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Bæta við reikningi',\n      action__signOutAll: 'Skrá út af öllum reikningum',\n      subtitle: 'Veldu reikninginn sem þú vilt halda áfram með.',\n      title: 'Veldu reikning',\n    },\n    alternativeMethods: {\n      actionLink: 'Fá hjálp',\n      actionText: 'Ertu ekki með neitt af þessu?',\n      blockButton__backupCode: 'Nota öryggiskóða',\n      blockButton__emailCode: 'Senda kóða á {{identifier}}',\n      blockButton__emailLink: 'Senda tengil á {{identifier}}',\n      blockButton__passkey: 'Skrá inn með lykli',\n      blockButton__password: 'Skrá inn með lykilorði',\n      blockButton__phoneCode: 'Senda SMS kóða á {{identifier}}',\n      blockButton__totp: 'Nota auðkennisforritið þitt',\n      getHelp: {\n        blockButton__emailSupport: 'Senda tölvupóst á stuðning',\n        content:\n          'Ef þú átt í erfiðleikum með að skrá þig inn á reikninginn þinn, sendu okkur tölvupóst og við munum vinna með þér til að endurheimta aðgang eins fljótt og auðið er.',\n        title: 'Fá hjálp',\n      },\n      subtitle: 'Áttu í vandræðum? Þú getur notað einhverja af þessum aðferðum til að skrá þig inn.',\n      title: 'Nota aðra aðferð',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'Öryggiskóðinn þinn er sá sem þú fékkst þegar þú stilltir tveggja þrepa auðkenningu.',\n      title: 'Sláðu inn öryggiskóða',\n    },\n    emailCode: {\n      formTitle: 'Staðfestingarkóði',\n      resendButton: 'Fékkstu ekki kóða? Senda aftur',\n      subtitle: 'til að halda áfram í {{applicationName}}',\n      title: 'Athugaðu tölvupóstinn þinn',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Til að halda áfram, opnaðu staðfestingartengilinn á tækinu og vafranum sem þú byrjaðir innskráninguna með',\n        title: 'Staðfestingartengill er ógildur fyrir þetta tæki',\n      },\n      expired: {\n        subtitle: 'Farðu aftur í upprunalega flipann til að halda áfram.',\n        title: 'Þessi staðfestingartengill er útrunninn',\n      },\n      failed: {\n        subtitle: 'Farðu aftur í upprunalega flipann til að halda áfram.',\n        title: 'Þessi staðfestingartengill er ógildur',\n      },\n      formSubtitle: 'Nota staðfestingartengilinn sem sendur var á tölvupóstinn þinn',\n      formTitle: 'Staðfestingartengill',\n      loading: {\n        subtitle: 'Þú verður fljótlega vísað áfram',\n        title: 'Skrá inn...',\n      },\n      resendButton: 'Fékkstu ekki tengil? Senda aftur',\n      subtitle: 'til að halda áfram í {{applicationName}}',\n      title: 'Athugaðu tölvupóstinn þinn',\n      unusedTab: {\n        title: 'Þú getur lokað þessum flipa',\n      },\n      verified: {\n        subtitle: 'Þú verður fljótlega vísað áfram',\n        title: 'Tókst að skrá inn',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Farðu aftur í upprunalega flipann til að halda áfram',\n        subtitleNewTab: 'Farðu aftur í nýopnaða flipann til að halda áfram',\n        titleNewTab: 'Skráður inn á öðrum flipa',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Endurstilla lykilorð kóða',\n      resendButton: 'Fékkstu ekki kóða? Senda aftur',\n      subtitle: 'til að endurstilla lykilorðið þitt',\n      subtitle_email: 'Fyrst, sláðu inn kóðann sem sendur var á netfangið þitt',\n      subtitle_phone: 'Fyrst, sláðu inn kóðann sem sendur var á símann þinn',\n      title: 'Endurstilla lykilorð',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Endurstilla lykilorðið þitt',\n      label__alternativeMethods: 'Eða, skráðu þig inn með annarri aðferð',\n      title: 'Gleymt lykilorð?',\n    },\n    noAvailableMethods: {\n      message: 'Ekki er hægt að halda áfram með innskráningu. Engin tiltæk auðkenningaraðferð.',\n      subtitle: 'Villa kom upp',\n      title: 'Ekki hægt að skrá inn',\n    },\n    passkey: {\n      subtitle:\n        'Að nota lykilinn þinn staðfestir að þú ert það. Tækið þitt gæti beðið um fingrafar, andlit eða skjálás.',\n      title: 'Nota lykilinn þinn',\n    },\n    password: {\n      actionLink: 'Nota aðra aðferð',\n      subtitle: 'Sláðu inn lykilorðið sem tengist reikningnum þínum',\n      title: 'Sláðu inn lykilorðið þitt',\n    },\n    passwordPwned: {\n      title: 'Lykilorð brotið',\n    },\n    phoneCode: {\n      formTitle: 'Staðfestingarkóði',\n      resendButton: 'Fékkstu ekki kóða? Senda aftur',\n      subtitle: 'til að halda áfram í {{applicationName}}',\n      title: 'Athugaðu símann þinn',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Staðfestingarkóði',\n      resendButton: 'Fékkstu ekki kóða? Senda aftur',\n      subtitle: 'Til að halda áfram, vinsamlegast sláðu inn staðfestingarkóðann sem sendur var á símann þinn',\n      title: 'Athugaðu símann þinn',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Endurstilla lykilorð',\n      requiredMessage: 'Af öryggisástæðum er nauðsynlegt að endurstilla lykilorðið þitt.',\n      successMessage: 'Lykilorðið þitt var endurstillt með góðum árangri. Skrá þig inn, vinsamlegast bíddu augnablik.',\n      title: 'Setja nýtt lykilorð',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Við þurfum að staðfesta auðkenni þitt áður en við endurstillum lykilorðið þitt.',\n    },\n    start: {\n      actionLink: 'Skrá sig',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Nota netfang',\n      actionLink__use_email_username: 'Nota netfang eða notendanafn',\n      actionLink__use_passkey: 'Nota lykil í staðinn',\n      actionLink__use_phone: 'Nota síma',\n      actionLink__use_username: 'Nota notendanafn',\n      actionText: 'Ertu ekki með reikning?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Velkomin aftur! Vinsamlegast skráðu þig inn til að halda áfram',\n      subtitleCombined: undefined,\n      title: 'Skrá inn í {{applicationName}}',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Staðfestingarkóði',\n      subtitle: 'Til að halda áfram, vinsamlegast sláðu inn staðfestingarkóðann sem auðkennisforritið þitt bjó til',\n      title: 'Tveggja þrepa auðkenning',\n    },\n  },\n  signInEnterPasswordTitle: 'Sláðu inn lykilorðið þitt',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Skrá inn',\n      actionText: 'Ertu með reikning?',\n      subtitle: 'Vinsamlegast fylltu út eftirfarandi upplýsingar til að halda áfram.',\n      title: 'Fylltu út vantar reiti',\n    },\n    emailCode: {\n      formSubtitle: 'Sláðu inn staðfestingarkóðann sem sendur var á netfangið þitt',\n      formTitle: 'Staðfestingarkóði',\n      resendButton: 'Fékkstu ekki kóða? Senda aftur',\n      subtitle: 'Sláðu inn staðfestingarkóðann sem sendur var á netfangið þitt',\n      title: 'Staðfesta netfang',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Til að halda áfram, opnaðu staðfestingartengilinn á tækinu og vafranum sem þú byrjaðir skráninguna með',\n        title: 'Staðfestingartengill er ógildur fyrir þetta tæki',\n      },\n      formSubtitle: 'Nota staðfestingartengilinn sem sendur var á netfangið þitt',\n      formTitle: 'Staðfestingartengill',\n      loading: {\n        title: 'Skrá sig...',\n      },\n      resendButton: 'Fékkstu ekki tengil? Senda aftur',\n      subtitle: 'til að halda áfram í {{applicationName}}',\n      title: 'Staðfesta netfang',\n      verified: {\n        title: 'Tókst að skrá sig',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Farðu aftur í nýopnaða flipann til að halda áfram',\n        subtitleNewTab: 'Farðu aftur í fyrri flipann til að halda áfram',\n        title: 'Tókst að staðfesta netfang',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Sláðu inn staðfestingarkóðann sem sendur var á símanúmerið þitt',\n      formTitle: 'Staðfestingarkóði',\n      resendButton: 'Fékkstu ekki kóða? Senda aftur',\n      subtitle: 'Sláðu inn staðfestingarkóðann sem sendur var á símanúmerið þitt',\n      title: 'Staðfesta símanúmer',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Skrá inn',\n      actionLink__use_email: 'Nota netfang í staðinn',\n      actionLink__use_phone: 'Nota síma í staðinn',\n      actionText: 'Ertu með reikning?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Velkomin! Vinsamlegast fylltu út upplýsingar til að byrja.',\n      subtitleCombined: 'Velkomin! Vinsamlegast fylltu út upplýsingar til að byrja.',\n      title: 'Stofna reikning',\n      titleCombined: 'Stofna reikning',\n    },\n  },\n  socialButtonsBlockButton: 'Halda áfram með {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'Skráning mistókst vegna misheppnaðra öryggisstaðfestinga. Vinsamlegast endurhlaðið síðuna til að reyna aftur eða hafið samband við stuðning til að fá frekari aðstoð.',\n    captcha_unavailable:\n      'Skráning mistókst vegna misheppnaðrar vélmenna staðfestingar. Vinsamlegast endurhlaðið síðuna til að reyna aftur eða hafið samband við stuðning til að fá frekari aðstoð.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: 'Þetta netfang er þegar í notkun. Vinsamlegast reyndu annað.',\n    form_identifier_exists__phone_number: 'Þetta símanúmer er þegar í notkun. Vinsamlegast reyndu annað.',\n    form_identifier_exists__username: 'Þetta notendanafn er þegar í notkun. Vinsamlegast reyndu annað.',\n    form_identifier_not_found: 'Við getum ekki fundið reikning með þessum upplýsingum.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'Netfang verður að vera gilt netfang.',\n    form_param_format_invalid__phone_number: 'Símanúmer verður að vera á giltu alþjóðlegu formi',\n    form_param_max_length_exceeded__first_name: 'Fornafn má ekki vera lengra en 256 stafir.',\n    form_param_max_length_exceeded__last_name: 'Eftirnafn má ekki vera lengra en 256 stafir.',\n    form_param_max_length_exceeded__name: 'Nafn má ekki vera lengra en 256 stafir.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: 'Lykilorðið þitt er ekki nógu sterkt.',\n    form_password_pwned:\n      'Þetta lykilorð hefur fundist sem hluti af öryggisbresti og má ekki nota, vinsamlegast reyndu annað lykilorð.',\n    form_password_pwned__sign_in:\n      'Þetta lykilorð hefur fundist sem hluti af öryggisbresti og má ekki nota, vinsamlegast endurstilltu lykilorðið þitt.',\n    form_password_size_in_bytes_exceeded:\n      'Lykilorðið þitt hefur farið yfir hámarksfjölda bæta sem leyfðir eru, vinsamlegast styttu það eða fjarlægðu nokkra sérstafi.',\n    form_password_validation_failed: 'Rangt lykilorð',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: 'Þú getur ekki eytt síðasta auðkenni þínu.',\n    not_allowed_access:\n      \"Netfang eða símanúmer þitt er ekki leyft til að skrá sig. Þetta gæti verið vegna þess að þú ert að nota '+', '=', '#' eða '.' í netfangi þínu, að nota domen sem tengist tímabundnum tölvupóstur, eða að þú ert búinn til að nota það. Ef þú reynir að skrá sig og færð villu, vinsamlegast hafið samband við stuðning.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: 'Lykill er þegar skráður með þessu tæki.',\n    passkey_not_supported: 'Lyklar eru ekki studdir á þessu tæki.',\n    passkey_pa_not_supported: 'Skráning krefst vettvangs auðkennis en tækið styður það ekki.',\n    passkey_registration_cancelled: 'Skráning lykils var hætt eða rann út.',\n    passkey_retrieval_cancelled: 'Staðfesting lykils var hætt eða rann út.',\n    passwordComplexity: {\n      maximumLength: 'minna en {{length}} stafir',\n      minimumLength: '{{length}} eða fleiri stafir',\n      requireLowercase: 'lágstaf',\n      requireNumbers: 'tölu',\n      requireSpecialCharacter: 'sérstaf',\n      requireUppercase: 'hástaf',\n      sentencePrefix: 'Lykilorðið þitt verður að innihalda',\n    },\n    phone_number_exists: 'Þetta símanúmer er þegar í notkun. Vinsamlegast reyndu annað.',\n    session_exists: 'Þú ert nú þegar innskráður.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Lykilorðið þitt virkar, en gæti verið sterkara. Reyndu að bæta við fleiri stöfum.',\n      goodPassword: 'Lykilorðið þitt uppfyllir allar nauðsynlegar kröfur.',\n      notEnough: 'Lykilorðið þitt er ekki nógu sterkt.',\n      suggestions: {\n        allUppercase: 'Stórstafa sum, en ekki alla stafi.',\n        anotherWord: 'Bættu við fleiri orðum sem eru minna algeng.',\n        associatedYears: 'Forðastu ár sem tengjast þér.',\n        capitalization: 'Stórstafa fleiri en fyrsta staf.',\n        dates: 'Forðastu dagsetningar og ár sem tengjast þér.',\n        l33t: \"Forðastu fyrirsjáanlegar stafaskiptingar eins og '@' fyrir 'a'.\",\n        longerKeyboardPattern: 'Notaðu lengri lyklaborðsmynstur og breyttu sláttarstefnu nokkrum sinnum.',\n        noNeed: 'Þú getur búið til sterk lykilorð án þess að nota tákn, tölur eða hástafi.',\n        pwned: 'Ef þú notar þetta lykilorð annars staðar, ættir þú að breyta því.',\n        recentYears: 'Forðastu nýleg ár.',\n        repeated: 'Forðastu endurtekin orð og stafi.',\n        reverseWords: 'Forðastu öfug stafsetning algengra orða.',\n        sequences: 'Forðastu algengar stafaraðir.',\n        useWords: 'Notaðu mörg orð, en forðastu algengar setningar.',\n      },\n      warnings: {\n        common: 'Þetta er algengt lykilorð.',\n        commonNames: 'Algeng nöfn og eftirnöfn eru auðvelt að giska á.',\n        dates: 'Dagsetningar eru auðvelt að giska á.',\n        extendedRepeat: 'Endurtekin stafamynstur eins og \"abcabcabc\" eru auðvelt að giska á.',\n        keyPattern: 'Stutt lyklaborðsmynstur eru auðvelt að giska á.',\n        namesByThemselves: 'Einstök nöfn eða eftirnöfn eru auðvelt að giska á.',\n        pwned: 'Lykilorðið þitt var afhjúpað í öryggisbresti á netinu.',\n        recentYears: 'Nýleg ár eru auðvelt að giska á.',\n        sequences: 'Algengar stafaraðir eins og \"abc\" eru auðvelt að giska á.',\n        similarToCommon: 'Þetta er svipað og algengt lykilorð.',\n        simpleRepeat: 'Endurteknir stafir eins og \"aaa\" eru auðvelt að giska á.',\n        straightRow: 'Beinar raðir af lyklum á lyklaborðinu eru auðvelt að giska á.',\n        topHundred: 'Þetta er oft notað lykilorð.',\n        topTen: 'Þetta er mjög oft notað lykilorð.',\n        userInputs: 'Það ætti ekki að vera neinar persónulegar eða síðu tengdar upplýsingar.',\n        wordByItself: 'Einstök orð eru auðvelt að giska á.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Bæta við reikningi',\n    action__manageAccount: 'Stjórna reikningi',\n    action__signOut: 'Skrá út',\n    action__signOutAll: 'Skrá út af öllum reikningum',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Afritað!',\n      actionLabel__copy: 'Afrita allt',\n      actionLabel__download: 'Sækja .txt',\n      actionLabel__print: 'Prenta',\n      infoText1: 'Öryggiskóðar verða virkjaðir fyrir þennan reikning.',\n      infoText2:\n        'Geymdu öryggiskóðana leynilega og geymdu þá á öruggum stað. Þú getur endurmyndað öryggiskóða ef þú grunar að þeir hafi verið afhjúpaðir.',\n      subtitle__codelist: 'Geymdu þá á öruggum stað og haltu þeim leynilegum.',\n      successMessage:\n        'Öryggiskóðar eru nú virkjaðir. Þú getur notað einn af þessum til að skrá þig inn á reikninginn þinn, ef þú missir aðgang að auðkennis tækinu þínu. Hver kóði getur aðeins verið notaður einu sinni.',\n      successSubtitle:\n        'Þú getur notað einn af þessum til að skrá þig inn á reikninginn þinn, ef þú missir aðgang að auðkennis tækinu þínu.',\n      title: 'Bæta við öryggiskóða staðfestingu',\n      title__codelist: 'Öryggiskóðar',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Veldu þjónustuaðila til að tengja reikninginn þinn.',\n      formHint__noAccounts: 'Engir tiltækir ytri reikningsþjónustuaðilar.',\n      removeResource: {\n        messageLine1: '{{identifier}} verður fjarlægt úr þessum reikningi.',\n        messageLine2:\n          'Þú munt ekki lengur geta notað þennan tengda reikning og öll háð eiginleikar munu ekki lengur virka.',\n        successMessage: '{{connectedAccount}} hefur verið fjarlægt úr reikningnum þínum.',\n        title: 'Fjarlægja tengdan reikning',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}',\n      successMessage: 'Þjónustuaðilinn hefur verið bætt við reikninginn þinn',\n      title: 'Bæta við tengdum reikningi',\n    },\n    deletePage: {\n      actionDescription: 'Sláðu inn \"Eyða reikningi\" hér að neðan til að halda áfram.',\n      confirm: 'Eyða reikningi',\n      messageLine1: 'Ertu viss um að þú viljir eyða reikningnum þínum?',\n      messageLine2: 'Þessi aðgerð er varanleg og óafturkræf.',\n      title: 'Eyða reikningi',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Tölvupóstur sem inniheldur staðfestingarkóða verður sendur á þetta netfang.',\n        formSubtitle: 'Sláðu inn staðfestingarkóðann sem sendur var á {{identifier}}',\n        formTitle: 'Staðfestingarkóði',\n        resendButton: 'Fékkstu ekki kóða? Senda aftur',\n        successMessage: 'Netfangið {{identifier}} hefur verið bætt við reikninginn þinn.',\n      },\n      emailLink: {\n        formHint: 'Tölvupóstur sem inniheldur staðfestingartengil verður sendur á þetta netfang.',\n        formSubtitle: 'Smelltu á staðfestingartengilinn í tölvupóstinum sem sendur var á {{identifier}}',\n        formTitle: 'Staðfestingartengill',\n        resendButton: 'Fékkstu ekki tengil? Senda aftur',\n        successMessage: 'Netfangið {{identifier}} hefur verið bætt við reikninginn þinn.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} verður fjarlægt úr þessum reikningi.',\n        messageLine2: 'Þú munt ekki lengur geta skráð þig inn með þessu netfangi.',\n        successMessage: '{{emailAddress}} hefur verið fjarlægt úr reikningnum þínum.',\n        title: 'Fjarlægja netfang',\n      },\n      title: 'Bæta við netfangi',\n      verifyTitle: 'Staðfesta netfang',\n    },\n    formButtonPrimary__add: 'Bæta við',\n    formButtonPrimary__continue: 'Halda áfram',\n    formButtonPrimary__finish: 'Ljúka',\n    formButtonPrimary__remove: 'Fjarlægja',\n    formButtonPrimary__save: 'Vista',\n    formButtonReset: 'Hætta við',\n    mfaPage: {\n      formHint: 'Veldu aðferð til að bæta við.',\n      title: 'Bæta við tveggja þrepa auðkenningu',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Nota núverandi númer',\n      primaryButton__addPhoneNumber: 'Bæta við símanúmeri',\n      removeResource: {\n        messageLine1: '{{identifier}} mun ekki lengur fá staðfestingarkóða við innskráningu.',\n        messageLine2: 'Reikningurinn þinn gæti ekki verið eins öruggur. Ertu viss um að þú viljir halda áfram?',\n        successMessage: 'SMS kóða tveggja þrepa auðkenning hefur verið fjarlægð fyrir {{mfaPhoneCode}}',\n        title: 'Fjarlægja tveggja þrepa auðkenningu',\n      },\n      subtitle__availablePhoneNumbers:\n        'Veldu núverandi símanúmer til að skrá fyrir SMS kóða tveggja þrepa auðkenningu eða bættu við nýju.',\n      subtitle__unavailablePhoneNumbers:\n        'Engin tiltæk símanúmer til að skrá fyrir SMS kóða tveggja þrepa auðkenningu, vinsamlegast bættu við nýju.',\n      successMessage1:\n        'Við innskráningu þarftu að slá inn staðfestingarkóða sem sendur er á þetta símanúmer sem viðbótar skref.',\n      successMessage2:\n        'Vistaðu þessa öryggiskóða og geymdu þá á öruggum stað. Ef þú missir aðgang að auðkennis tækinu þínu, getur þú notað öryggiskóða til að skrá þig inn.',\n      successTitle: 'SMS kóða staðfesting virkjað',\n      title: 'Bæta við SMS kóða staðfestingu',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Skanna QR kóða í staðinn',\n        buttonUnableToScan__nonPrimary: 'Getur ekki skannað QR kóða?',\n        infoText__ableToScan:\n          'Settu upp nýja innskráningaraðferð í auðkennisforritinu þínu og skannaðu eftirfarandi QR kóða til að tengja það við reikninginn þinn.',\n        infoText__unableToScan:\n          'Settu upp nýja innskráningaraðferð í auðkennisforritinu þínu og sláðu inn lykilinn hér að neðan.',\n        inputLabel__unableToScan1:\n          'Gakktu úr skugga um að Tímatengdir eða Einnota lykilorð séu virkjað, og ljúktu síðan við að tengja reikninginn þinn.',\n        inputLabel__unableToScan2:\n          'Að öðrum kosti, ef auðkennisforritið þitt styður TOTP URI, geturðu einnig afritað fullan URI.',\n      },\n      removeResource: {\n        messageLine1: 'Staðfestingarkóðar frá þessu auðkennisforriti verða ekki lengur nauðsynlegir við innskráningu.',\n        messageLine2: 'Reikningurinn þinn gæti ekki verið eins öruggur. Ertu viss um að þú viljir halda áfram?',\n        successMessage: 'Tveggja þrepa auðkenning með auðkennisforriti hefur verið fjarlægð.',\n        title: 'Fjarlægja tveggja þrepa auðkenningu',\n      },\n      successMessage:\n        'Tveggja þrepa auðkenning er nú virkjað. Við innskráningu þarftu að slá inn staðfestingarkóða frá þessu auðkennisforriti sem viðbótar skref.',\n      title: 'Bæta við auðkennisforriti',\n      verifySubtitle: 'Sláðu inn staðfestingarkóðann sem auðkennisforritið þitt bjó til',\n      verifyTitle: 'Staðfestingarkóði',\n    },\n    mobileButton__menu: 'Valmynd',\n    navbar: {\n      account: 'Prófíll',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Stjórna reikningsupplýsingum þínum.',\n      security: 'Öryggi',\n      title: 'Reikningur',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} verður fjarlægt úr þessum reikningi.',\n        title: 'Fjarlægja lykil',\n      },\n      subtitle__rename: 'Þú getur breytt nafni lykilsins til að auðvelda að finna hann.',\n      title__rename: 'Endurnefna lykil',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Mælt er með að skrá þig út af öllum öðrum tækjum sem gætu hafa notað gamla lykilorðið þitt.',\n      readonly:\n        'Lykilorðið þitt er ekki hægt að breyta núna vegna þess að þú getur aðeins skráð þig inn í gegnum fyrirtækjatengingu.',\n      successMessage__set: 'Lykilorðið þitt hefur verið sett.',\n      successMessage__signOutOfOtherSessions: 'Öll önnur tæki hafa verið skráð út.',\n      successMessage__update: 'Lykilorðið þitt hefur verið uppfært.',\n      title__set: 'Setja lykilorð',\n      title__update: 'Uppfæra lykilorð',\n    },\n    phoneNumberPage: {\n      infoText:\n        'SMS sem inniheldur staðfestingarkóða verður sent á þetta símanúmer. Skilaboð og gagnagjöld geta átt við.',\n      removeResource: {\n        messageLine1: '{{identifier}} verður fjarlægt úr þessum reikningi.',\n        messageLine2: 'Þú munt ekki lengur geta skráð þig inn með þessu símanúmeri.',\n        successMessage: '{{phoneNumber}} hefur verið fjarlægt úr reikningnum þínum.',\n        title: 'Fjarlægja símanúmer',\n      },\n      successMessage: '{{identifier}} hefur verið bætt við reikninginn þinn.',\n      title: 'Bæta við símanúmeri',\n      verifySubtitle: 'Sláðu inn staðfestingarkóðann sem sendur var á {{identifier}}',\n      verifyTitle: 'Staðfesta símanúmer',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Mælt stærð 1:1, allt að 10MB.',\n      imageFormDestructiveActionSubtitle: 'Fjarlægja',\n      imageFormSubtitle: 'Hlaða upp',\n      imageFormTitle: 'Prófílmynd',\n      readonly: 'Prófílupplýsingar þínar hafa verið veittar af fyrirtækjatengingu og er ekki hægt að breyta.',\n      successMessage: 'Prófíllinn þinn hefur verið uppfærður.',\n      title: 'Uppfæra prófíl',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Skrá út af tæki',\n        title: 'Virk tæki',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Endurtengja',\n        actionLabel__reauthorize: 'Heimila núna',\n        destructiveActionTitle: 'Fjarlægja',\n        primaryButton: 'Tengja reikning',\n        subtitle__disconnected: 'Þessi reikningur hefur verið aftengdur.',\n        subtitle__reauthorize:\n          'Nauðsynlegar heimildir hafa verið uppfærðar, og þú gætir verið að upplifa takmarkaða virkni. Vinsamlegast endurheimilaðu þetta forrit til að forðast vandamál',\n        title: 'Tengdir reikningar',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Eyða reikningi',\n        title: 'Eyða reikningi',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Fjarlægja netfang',\n        detailsAction__nonPrimary: 'Setja sem aðal',\n        detailsAction__primary: 'Ljúka staðfestingu',\n        detailsAction__unverified: 'Staðfesta',\n        primaryButton: 'Bæta við netfangi',\n        title: 'Netföng',\n      },\n      enterpriseAccountsSection: {\n        title: 'Fyrirtækjareikningar',\n      },\n      headerTitle__account: 'Prófílupplýsingar',\n      headerTitle__security: 'Öryggi',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Endurmynda',\n          headerTitle: 'Öryggiskóðar',\n          subtitle__regenerate:\n            'Fáðu nýtt sett af öruggum öryggiskóðum. Fyrri öryggiskóðar verða eyttir og ekki hægt að nota.',\n          title__regenerate: 'Endurmynda öryggiskóða',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Setja sem sjálfgefið',\n          destructiveActionLabel: 'Fjarlægja',\n        },\n        primaryButton: 'Bæta við tveggja þrepa auðkenningu',\n        title: 'Tveggja þrepa auðkenning',\n        totp: {\n          destructiveActionTitle: 'Fjarlægja',\n          headerTitle: 'Auðkennisforrit',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Fjarlægja',\n        menuAction__rename: 'Endurnefna',\n        primaryButton: undefined,\n        title: 'Lyklar',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Setja lykilorð',\n        primaryButton__updatePassword: 'Uppfæra lykilorð',\n        title: 'Lykilorð',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Fjarlægja símanúmer',\n        detailsAction__nonPrimary: 'Setja sem aðal',\n        detailsAction__primary: 'Ljúka staðfestingu',\n        detailsAction__unverified: 'Staðfesta símanúmer',\n        primaryButton: 'Bæta við símanúmeri',\n        title: 'Símanúmer',\n      },\n      profileSection: {\n        primaryButton: 'Uppfæra prófíl',\n        title: 'Prófíll',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Setja notendanafn',\n        primaryButton__updateUsername: 'Uppfæra notendanafn',\n        title: 'Notendanafn',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Fjarlægja veski',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 veski',\n        title: 'Web3 veski',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Notendanafnið þitt hefur verið uppfært.',\n      title__set: 'Setja notendanafn',\n      title__update: 'Uppfæra notendanafn',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} verður fjarlægt úr þessum reikningi.',\n        messageLine2: 'Þú munt ekki lengur geta skráð þig inn með þessu web3 veski.',\n        successMessage: '{{web3Wallet}} hefur verið fjarlægt úr reikningnum þínum.',\n        title: 'Fjarlægja web3 veski',\n      },\n      subtitle__availableWallets: 'Veldu web3 veski til að tengja við reikninginn þinn.',\n      subtitle__unavailableWallets: 'Engin tiltæk web3 veski.',\n      successMessage: 'Veskið hefur verið bætt við reikninginn þinn.',\n      title: 'Bæta við web3 veski',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBACE;AAAA,EACF,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BACE;AAAA,IACF,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UACE;AAAA,MACF,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}