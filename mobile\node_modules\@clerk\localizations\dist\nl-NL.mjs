// src/nl-NL.ts
var nlNL = {
  locale: "nl-NL",
  apiKeys: {
    action__add: void 0,
    action__search: void 0,
    createdAndExpirationStatus__expiresOn: void 0,
    createdAndExpirationStatus__never: void 0,
    detailsTitle__emptyRow: void 0,
    formButtonPrimary__add: void 0,
    formFieldCaption__expiration__expiresOn: void 0,
    formFieldCaption__expiration__never: void 0,
    formFieldOption__expiration__180d: void 0,
    formFieldOption__expiration__1d: void 0,
    formFieldOption__expiration__1y: void 0,
    formFieldOption__expiration__30d: void 0,
    formFieldOption__expiration__60d: void 0,
    formFieldOption__expiration__7d: void 0,
    formFieldOption__expiration__90d: void 0,
    formFieldOption__expiration__never: void 0,
    formHint: void 0,
    formTitle: void 0,
    lastUsed__days: void 0,
    lastUsed__hours: void 0,
    lastUsed__minutes: void 0,
    lastUsed__months: void 0,
    lastUsed__seconds: void 0,
    lastUsed__years: void 0,
    menuAction__revoke: void 0,
    revokeConfirmation: {
      confirmationText: void 0,
      formButtonPrimary__revoke: void 0,
      formHint: void 0,
      formTitle: void 0
    }
  },
  backButton: "Terug",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Standaard",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Ander impersonatie apparaat",
  badge__primary: "Primair",
  badge__renewsAt: void 0,
  badge__requiresAction: "Actie vereist",
  badge__startsAt: void 0,
  badge__thisDevice: "Dit apparaat",
  badge__unverified: "Ongeverifieerd",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Gebruikersapparaat",
  badge__you: "Jij",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      pastDueNotice: void 0,
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    credit: void 0,
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDue: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Cre\xEBer organisatie",
    invitePage: {
      formButtonReset: "Overslaan"
    },
    title: "Cre\xEBer organisatie"
  },
  dates: {
    lastDay: "Gisteren om {{ date | timeString('nl-NL') }}",
    next6Days: "{{ date | weekday('nl-NL','long') }} om {{ date | timeString('nl-NL') }}",
    nextDay: "Morgen om {{ date | timeString('nl-NL') }}",
    numeric: "{{ date | numeric('nl-NL') }}",
    previous6Days: "Vorige {{ date | weekday('nl-NL','long') }} om {{ date | timeString('nl-NL') }}",
    sameDay: "Vandaag om {{ date | timeString('nl-NL') }}"
  },
  dividerText: "of",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Een andere methode gebruiken",
  footerPageLink__help: "Helppagina",
  footerPageLink__privacy: "Privacybeleid",
  footerPageLink__terms: "Algemene voorwaarden",
  formButtonPrimary: "Doorgaan",
  formButtonPrimary__verify: "Verifieer",
  formFieldAction__forgotPassword: "Wachtwoord vergeten?",
  formFieldError__matchingPasswords: "Wachtwoorden matchen.",
  formFieldError__notMatchingPasswords: "Wachtwoorden komen niet overeen.",
  formFieldError__verificationLinkExpired: "De verificatielink is verlopen. Vraag een nieuwe link aan.",
  formFieldHintText__optional: "Optioneel",
  formFieldHintText__slug: "Een slug is een leesbare ID die uniek moet zijn. Het wordt vaak gebruikt in URL\u2019s.",
  formFieldInputPlaceholder__apiKeyDescription: void 0,
  formFieldInputPlaceholder__apiKeyExpirationDate: void 0,
  formFieldInputPlaceholder__apiKeyName: void 0,
  formFieldInputPlaceholder__backupCode: "Voer een back-upcode in",
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Verwijder account",
  formFieldInputPlaceholder__emailAddress: "<EMAIL>",
  formFieldInputPlaceholder__emailAddress_username: "<EMAIL>",
  formFieldInputPlaceholder__emailAddresses: "<EMAIL>, <EMAIL>",
  formFieldInputPlaceholder__firstName: "Voornaam",
  formFieldInputPlaceholder__lastName: "Achternaam",
  formFieldInputPlaceholder__organizationDomain: "voorbeeld.domein",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "<EMAIL>",
  formFieldInputPlaceholder__organizationName: "Organisatienaam",
  formFieldInputPlaceholder__organizationSlug: "mijn-org",
  formFieldInputPlaceholder__password: "Wachtwoord",
  formFieldInputPlaceholder__phoneNumber: "Telefoonnummer",
  formFieldInputPlaceholder__username: "Gebruikersnaam",
  formFieldLabel__apiKeyDescription: void 0,
  formFieldLabel__apiKeyExpiration: void 0,
  formFieldLabel__apiKeyName: void 0,
  formFieldLabel__automaticInvitations: "Automatische uitnodigingen inschakelen voor dit domein",
  formFieldLabel__backupCode: "Backupcode",
  formFieldLabel__confirmDeletion: "Bevestiging",
  formFieldLabel__confirmPassword: "Wachtwoord bevestigen",
  formFieldLabel__currentPassword: "Huidig wachtwoord",
  formFieldLabel__emailAddress: "E-mailadres",
  formFieldLabel__emailAddress_username: "E-mailadres of gebruikersnaam",
  formFieldLabel__emailAddresses: "E-mailadressen",
  formFieldLabel__firstName: "Voornaam",
  formFieldLabel__lastName: "Achternaam",
  formFieldLabel__newPassword: "Nieuw wachtwoord",
  formFieldLabel__organizationDomain: "Domain",
  formFieldLabel__organizationDomainDeletePending: "Verwijder uitnodigingen en suggesties",
  formFieldLabel__organizationDomainEmailAddress: "Verificatie-e-mailadres",
  formFieldLabel__organizationDomainEmailAddressDescription: "Voer een e-mailadres onder dit domein in om een code te ontvangen en dit domein te verifi\xEBren.",
  formFieldLabel__organizationName: "Organisatienaam",
  formFieldLabel__organizationSlug: "Slug",
  formFieldLabel__passkeyName: "Naam",
  formFieldLabel__password: "Wachtwoord",
  formFieldLabel__phoneNumber: "Telefoonnummer",
  formFieldLabel__role: "Rol",
  formFieldLabel__signOutOfOtherSessions: "Alle andere apparaten uitloggen",
  formFieldLabel__username: "Gebruikersnaam",
  impersonationFab: {
    action__signOut: "Uitloggen",
    title: "Ingelogd als {{identifier}}"
  },
  maintenanceMode: "Onderhoudsmodus",
  membershipRole__admin: "Beheerder",
  membershipRole__basicMember: "Lid",
  membershipRole__guestMember: "Gast",
  organizationList: {
    action__createOrganization: "Cre\xEBer organisatie",
    action__invitationAccept: "Toetreden",
    action__suggestionsAccept: "Verzoek om toetreden",
    createOrganization: "Cre\xEBer organisatie",
    invitationAcceptedLabel: "Toegetreden",
    subtitle: "om door te gaan naar {{applicationName}}",
    suggestionsAcceptedLabel: "In behandeling",
    title: "Kies een organisatie",
    titleWithoutPersonal: "Kies een organisatie"
  },
  organizationProfile: {
    apiKeysPage: {
      title: void 0
    },
    badge__automaticInvitation: "Automatische Uitnodiging",
    badge__automaticSuggestion: "Automatische Suggesties",
    badge__manualInvitation: "Geen automatische inschrijving",
    badge__unverified: "Ongeverifieerd",
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Voeg het domein toe om te verifi\xEBren. Gebruikers met e-mailadressen in dit domein kunnen de organisatie automatisch toegang krijgen of een verzoek om toegang te maken.",
      title: "Domein toevoegen"
    },
    invitePage: {
      detailsTitle__inviteFailed: "De uitnodigingen konden niet verzonden worden. Los het volgende op en probeer het opnieuw:",
      formButtonPrimary__continue: "Uitnodigingen verzenden",
      selectDropdown__role: "Selecteer rol",
      subtitle: "Nodig nieuwe leden uit voor deze organisatie",
      successMessage: "Uitnodigingen succesvol verzonden",
      title: "Leden uitnodigen"
    },
    membersPage: {
      action__invite: "Uitnodigen",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "Verwijder lid",
        tableHeader__actions: "Acties",
        tableHeader__joined: "Toegetreden",
        tableHeader__role: "Rol",
        tableHeader__user: "Gebruiker"
      },
      detailsTitle__emptyRow: "Geen leden gevonden",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Uitnodig gebruikers door een domein toe te voegen aan je organisatie. Iedereen die zich aanmeldt met een e-mailadres in dit domein kan de organisatie automatisch toegang krijgen of een verzoek om toegang te maken.",
          headerTitle: "Automatische uitnodigingen",
          primaryButton: "Beheer geverifieerde domeinen"
        },
        table__emptyRow: "Geen uitnodigingen gevonden"
      },
      invitedMembersTab: {
        menuAction__revoke: "Uitnodiging intrekken",
        tableHeader__invited: "Uitgenodigd"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Gebruikers die zich aanmelden met een e-mailadres in dit domein, kunnen een verzoek om toegang tot de organisatie zien.",
          headerTitle: "Automatische suggesties",
          primaryButton: "Beheer geverifieerde domeinen"
        },
        menuAction__approve: "Goedkeuren",
        menuAction__reject: "Weigeren",
        tableHeader__requested: "Verzoek om toegang",
        table__emptyRow: "Geen verzoeken om toegang"
      },
      start: {
        headerTitle__invitations: "Uitnodigingen",
        headerTitle__members: "Leden",
        headerTitle__requests: "Verzoeken"
      }
    },
    navbar: {
      apiKeys: void 0,
      billing: void 0,
      description: "Beheer je organisatie.",
      general: "Algemeen",
      members: "Leden",
      title: "Organisatie"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Typ "{{organizationName}}" hieronder om door te gaan.',
          messageLine1: "Weet je zeker dat je deze organisatie wilt verwijderen?",
          messageLine2: "Deze actie is permanent en onomkeerbaar.",
          successMessage: "Je hebt deze organisatie verwijderd.",
          title: "Organisatie verwijderen"
        },
        leaveOrganization: {
          actionDescription: 'Typ "{{organizationName}}" hieronder om door te gaan.',
          messageLine1: "Weet je zeker dat je deze organisatie wilt verlaten? Je zult toegang verliezen tot deze organisatie en haar applicaties.",
          messageLine2: "Deze actie is permanent en onomkeerbaar.",
          successMessage: "Je hebt deze organisatie verlaten.",
          title: "Verlaat organisatie"
        },
        title: "Gevaar"
      },
      domainSection: {
        menuAction__manage: "Beheer",
        menuAction__remove: "Verwijder",
        menuAction__verify: "Verifieer",
        primaryButton: "Domein toevoegen",
        subtitle: "Laat gebruikers de organisatie automatisch toegang krijgen of een verzoek om toegang maken op basis van een geverifieerd e-maildomein.",
        title: "Geverifieerde domeinen"
      },
      successMessage: "De organisatie is bijgewerkt.",
      title: "Organisatieprofiel"
    },
    removeDomainPage: {
      messageLine1: "Het e-maildomein {{domain}} wordt verwijderd.",
      messageLine2: "Gebruikers kunnen de organisatie niet meer automatisch toegang krijgen na dit domein te verwijderen.",
      successMessage: "{{domain}} is verwijderd.",
      title: "Domein verwijderen"
    },
    start: {
      headerTitle__general: "Algemeen",
      headerTitle__members: "Leden",
      profileSection: {
        primaryButton: "",
        title: "Organisatieprofiel",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Verwijderen van dit domein zal uitnodigingen be\xEFnvloeden.",
        removeDomainActionLabel__remove: "Domein verwijderen",
        removeDomainSubtitle: "Verwijder dit domein van je geverifieerde domeinen",
        removeDomainTitle: "Domein verwijderen"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Gebruikers worden automatisch uitgenodigd om lid te worden van de organisatie wanneer ze zich aanmelden en kunnen lid worden wanneer ze dat willen.",
        automaticInvitationOption__label: "Automatische uitnodigingen",
        automaticSuggestionOption__description: "Gebruikers ontvangen een aanbeveling om lid te worden, maar moeten worden goedgekeurd door een beheerder voordat ze toegang kunnen krijgen tot de organisatie.",
        automaticSuggestionOption__label: "Automatische suggesties",
        calloutInfoLabel: "Wijziging van de inschrijfmodus heeft alleen invloed op nieuwe gebruikers.",
        calloutInvitationCountLabel: "Uitnodigingen verzonden aan gebruikers: {{count}}",
        calloutSuggestionCountLabel: "Aanbevelingen verzonden aan gebruikers: {{count}}",
        manualInvitationOption__description: "Gebruikers kunnen alleen handmatig worden uitgenodigd voor de organisatie.",
        manualInvitationOption__label: "Geen automatische inschrijving",
        subtitle: "Kies hoe gebruikers van dit domein toegang kunnen krijgen tot de organisatie."
      },
      start: {
        headerTitle__danger: "Danger",
        headerTitle__enrollment: "Inschrijfopties"
      },
      subtitle: "Het domein {{domain}} is nu geverifieerd. Ga verder door de inschrijfmodus te selecteren.",
      title: "Update {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Voer de verificatiecode in die verzonden is naar je e-mailadres",
      formTitle: "Verificatiecode",
      resendButton: "Niet ontvangen? Verstuur opnieuw",
      subtitle: "Het domein {{domainName}} moet worden geverifieerd via e-mail.",
      subtitleVerificationCodeScreen: "A verification code was sent to {{emailAddress}}. Enter the code to continue.",
      title: "Verifieer domein"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Maak organisatie aan",
    action__invitationAccept: "Join",
    action__manageOrganization: "Beheer organisatie",
    action__suggestionsAccept: "Verzoek om lid te worden",
    notSelected: "Geen organisatie geselecteerd",
    personalWorkspace: "Persoonlijke werkruimte",
    suggestionsAcceptedLabel: "In behandeling"
  },
  paginationButton__next: "Volgende",
  paginationButton__previous: "Vorige",
  paginationRowText__displaying: "Weergeven",
  paginationRowText__of: "van",
  reverification: {
    alternativeMethods: {
      actionLink: "Krijg hulp",
      actionText: "Heb je geen van deze?",
      blockButton__backupCode: "Backupcode gebruiken",
      blockButton__emailCode: "Email code naar {{identifier}}",
      blockButton__passkey: void 0,
      blockButton__password: "Doorgaan met je wachtwoord",
      blockButton__phoneCode: "Verzend SMS code naar {{identifier}}",
      blockButton__totp: "Use your authenticator app",
      getHelp: {
        blockButton__emailSupport: "Email ondersteuning",
        content: "Als je moeite hebt om je account te verifi\xEBren, email ons en we zullen met je werken om toegang te herstellen zo snel mogelijk.",
        title: "Krijg hulp"
      },
      subtitle: "Problemen? Je kunt een van deze methoden gebruiken voor verificatie.",
      title: "Gebruik een andere methode"
    },
    backupCodeMfa: {
      subtitle: "Je backupcode is de code die je kreeg bij het installeren van tweestapsverificatie.",
      title: "Backupcode invoeren"
    },
    emailCode: {
      formTitle: "Verificatiecode",
      resendButton: "Niet ontvangen? Opnieuw verzenden",
      subtitle: "om door te gaan naar {{applicationName}}",
      title: "Controleer je email"
    },
    noAvailableMethods: {
      message: "Kan niet verder gaan met verificatie. Er is geen beschikbare verificatiefactor.",
      subtitle: "Er is een fout opgetreden",
      title: "Kan je account niet verifi\xEBren"
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "Gebruik een andere methode",
      subtitle: "Voer het wachtwoord in dat bij je account hoort",
      title: "Voer je wachtwoord in"
    },
    phoneCode: {
      formTitle: "Verificatiecode",
      resendButton: "Niet ontvangen? Opnieuw verzenden",
      subtitle: "om door te gaan naar {{applicationName}}",
      title: "Controleer je telefoon"
    },
    phoneCodeMfa: {
      formTitle: "Verificatiecode",
      resendButton: "Niet ontvangen? Opnieuw verzenden",
      subtitle: "om door te gaan naar {{applicationName}}",
      title: "Controleer je telefoon"
    },
    totpMfa: {
      formTitle: "Verificatiecode",
      subtitle: "om door te gaan naar {{applicationName}}",
      title: "Tweestapsverificatie"
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Account toevoegen",
      action__signOutAll: "Uitloggen van alle accounts",
      subtitle: "Selecteer het account met welk je door wilt gaan.",
      title: "Kies een account"
    },
    alternativeMethods: {
      actionLink: "Help",
      actionText: "Heb je geen van deze?",
      blockButton__backupCode: "Gebruik een backupcode",
      blockButton__emailCode: "Verzend code naar {{identifier}}",
      blockButton__emailLink: "Verzend link naar {{identifier}}",
      blockButton__passkey: "Gebruik toegangssleutel",
      blockButton__password: "Inloggen met je wachtwoord",
      blockButton__phoneCode: "Verzend code naar {{identifier}}",
      blockButton__totp: "Gebruik je authenticator app",
      getHelp: {
        blockButton__emailSupport: "E-mail {{applicationName}}",
        content: "Als je geen toegang hebt neem dan contact op met {{applicationName}} en we helpen je verder.",
        title: "Help"
      },
      subtitle: "Problemen? Je kan een van deze methoden gebruiken om in te loggen.",
      title: "Gebruik een andere methode"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "om door te gaan naar {{applicationName}}",
      title: "Voer een backupcode in"
    },
    emailCode: {
      formTitle: "Verificatiecode",
      resendButton: "Verstuur code opnieuw",
      subtitle: "om door te gaan naar {{applicationName}}",
      title: "Check je e-mail"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "De client komt niet overeen met wat verwacht werd. Probeer het opnieuw.",
        title: "Clientfout"
      },
      expired: {
        subtitle: "Ga naar de oorspronkelijke tab om verder te gaan.",
        title: "Deze verificatielink is verlopen"
      },
      failed: {
        subtitle: "Ga naar de oorspronkelijke tab om verder te gaan.",
        title: "Deze verificatielink is niet geldig"
      },
      formSubtitle: "Gebruik de verificatielink die verzonden is naar je e-mailadres",
      formTitle: "Verificatielink",
      loading: {
        subtitle: "Je zal weldra doorgestuurd worden",
        title: "Inloggen ..."
      },
      resendButton: "Verstuur link opnieuw",
      subtitle: "om door te gaan naar {{applicationName}}",
      title: "Check je e-mail",
      unusedTab: {
        title: "Je kan deze tab sluiten."
      },
      verified: {
        subtitle: "Je zal weldra doorgestuurd worden",
        title: "Successvol ingelogd"
      },
      verifiedSwitchTab: {
        subtitle: "Ga naar de oorspronkelijke tab om verder te gaan",
        subtitleNewTab: "Ga naar de pasgeopende tab om verder te gaan",
        titleNewTab: "Ingelogd in andere tab"
      }
    },
    forgotPassword: {
      formTitle: "Wachtwoord resetcode",
      resendButton: "Niet ontvangen? Verstuur opnieuw",
      subtitle: "om door te gaan naar {{applicationName}}",
      subtitle_email: "Voer de code in die verzonden is naar je e-mailadres",
      subtitle_phone: "Voer de code in die verzonden is naar je telefoon",
      title: "Wachtwoord resetten"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Wachtwoord resetten",
      label__alternativeMethods: "Of log in met een andere methode",
      title: "Wachtwoord vergeten?"
    },
    noAvailableMethods: {
      message: "Het is niet mogelijk om door te gaan met inloggen. Er is geen beschikbare authenticatiefactor.",
      subtitle: "Er heeft zich een fout voorgedaan",
      title: "Inloggen niet mogelijk"
    },
    passkey: {
      subtitle: "Gebruik je toegangssleutel voor authenticatie.",
      title: "Authenticatie met toegangssleutel"
    },
    password: {
      actionLink: "Gebruik een andere methode",
      subtitle: "om door te gaan naar {{applicationName}}",
      title: "Vul je wachtwoord in"
    },
    passwordPwned: {
      title: "Dit wachtwoord is gelekt bij een datalek. Kies een ander wachtwoord om veiligheidsredenen."
    },
    phoneCode: {
      formTitle: "Verificatiecode",
      resendButton: "Verstuur code opnieuw",
      subtitle: "om verder te gaan naar {{applicationName}}",
      title: "Check je telefoon"
    },
    phoneCodeMfa: {
      formTitle: "Verificatiecode",
      resendButton: "Verstuur code opnieuw",
      subtitle: "",
      title: "Check je telefoon"
    },
    resetPassword: {
      formButtonPrimary: "Wachtwoord resetten",
      requiredMessage: "Voor veiligheidsredenen is het vereist om je wachtwoord te resetten.",
      successMessage: "Je wachtwoord is succesvol gewijzigd. We sturen je door naar de inlogpagina.",
      title: "Wachtwoord resetten"
    },
    resetPasswordMfa: {
      detailsLabel: "Voor veiligheidsredenen is het vereist om je wachtwoord te resetten."
    },
    start: {
      actionLink: "Registreren",
      actionLink__join_waitlist: "Meld je aan voor de wachtlijst",
      actionLink__use_email: "Gebruik e-mail",
      actionLink__use_email_username: "Gebruik e-mail of gebruikersnaam",
      actionLink__use_passkey: "Gebruik toegangssleutel",
      actionLink__use_phone: "Gebruik telefoon",
      actionLink__use_username: "Gebruik gebruikersnaam",
      actionText: "Geen account?",
      actionText__join_waitlist: "Nog geen account?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "om door te gaan naar {{applicationName}}",
      subtitleCombined: void 0,
      title: "Inloggen",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Verificatiecode",
      subtitle: "",
      title: "Tweestapsverificatie"
    }
  },
  signInEnterPasswordTitle: "Vul je wachtwoord in",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Inloggen",
      actionText: "Heb je een account?",
      subtitle: "om door te gaan naar {{applicationName}}",
      title: "Vul de ontbrekende velden in"
    },
    emailCode: {
      formSubtitle: "Voer de verificatiecode in die verzonden is naar je e-mailadres",
      formTitle: "Verificatiecode",
      resendButton: "Verstuur code opnieuw",
      subtitle: "om door te gaan naar {{applicationName}}",
      title: "Bevestig je e-mailadres"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "De client komt niet overeen met het verwachte. Probeer het opnieuw.",
        title: "Clientfout"
      },
      formSubtitle: "Gebruik de verificatielink die verzonden is naar je e-mailadres",
      formTitle: "Verificatielink",
      loading: {
        title: "Registreren ..."
      },
      resendButton: "Verstuur link opnieuw",
      subtitle: "om door te gaan naar {{applicationName}}",
      title: "Bevestig je e-mailadres",
      verified: {
        title: "Succesvol geregistreerd"
      },
      verifiedSwitchTab: {
        subtitle: "Ga naar de pas geopende tab om verder te gaan",
        subtitleNewTab: "Ga naar de vorige tab om verder te gaan",
        title: "E-mail bevestigd"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: "Ik accepteer het Privacybeleid",
        label__onlyTermsOfService: "Ik accepteer de Algemene Voorwaarden",
        label__termsOfServiceAndPrivacyPolicy: 'Ik accepteer de {{ termsOfServiceLink || link("Algemene Voorwaarden") }} en het {{ privacyPolicyLink || link("Privacybeleid") }}'
      },
      continue: {
        subtitle: "Door verder te gaan, ga je akkoord met de bovenstaande voorwaarden.",
        title: "Doorgaan"
      }
    },
    phoneCode: {
      formSubtitle: "Voer de verificatiecode in die verzonden is naar je telefoonnummer",
      formTitle: "Verificatiecode",
      resendButton: "Verstuur code opnieuw",
      subtitle: "om door te gaan naar {{applicationName}}",
      title: "Bevestig je telefoonnummer"
    },
    restrictedAccess: {
      actionLink: "Neem contact op met de ondersteuning",
      actionText: "Hulp nodig?",
      blockButton__emailSupport: "Stuur e-mail naar ondersteuning",
      blockButton__joinWaitlist: "Meld je aan voor de wachtlijst",
      subtitle: "Je toegang is beperkt. Neem contact met ons op voor meer informatie.",
      subtitleWaitlist: "We wachten op je toegang tot de wachtlijst. Bedankt voor je geduld.",
      title: "Beperkte toegang"
    },
    start: {
      actionLink: "Inloggen",
      actionLink__use_email: "Gebruik e-mail",
      actionLink__use_phone: "Gebruik telefoon",
      actionText: "Heb je al een account?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "om door te gaan naar {{applicationName}}",
      subtitleCombined: "om door te gaan naar {{applicationName}}",
      title: "Maak je account aan",
      titleCombined: "Maak je account aan"
    }
  },
  socialButtonsBlockButton: "Ga verder met {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: "Ga verder met {{provider|titleize}}",
  unstable__errors: {
    already_a_member_in_organization: "Je bent al lid van de organisatie.",
    captcha_invalid: "Aanmelding mislukt vanwege mislukte beveiligingsvalidaties. Vernieuw de pagina om het opnieuw te proberen of neem contact op met de ondersteuning voor verdere hulp.",
    captcha_unavailable: "Aanmelding mislukt vanwege mislukte botvalidatie. Vernieuw de pagina om het opnieuw te proberen of neem contact op met de ondersteuning voor verdere hulp.",
    form_code_incorrect: "De ingevoerde code is incorrect.",
    form_identifier_exists__email_address: "Dit e-mailadres is al in gebruik.",
    form_identifier_exists__phone_number: "Dit telefoonnummer is al in gebruik.",
    form_identifier_exists__username: "Deze gebruikersnaam is al in gebruik.",
    form_identifier_not_found: "We konden geen account vinden met deze details.",
    form_param_format_invalid: "Het formaat van het ingevoerde gegeven is ongeldig.",
    form_param_format_invalid__email_address: "E-mailadres moet een geldig e-mailadres zijn.",
    form_param_format_invalid__phone_number: "Telefoonnummer moet een geldig internationaal nummer zijn.",
    form_param_max_length_exceeded__first_name: "Voornaam moet minder dan 256 tekens bevatten.",
    form_param_max_length_exceeded__last_name: "Achternaam moet minder dan 256 tekens bevatten.",
    form_param_max_length_exceeded__name: "Naam moet minder dan 256 tekens bevatten.",
    form_param_nil: "Dit veld mag niet leeg zijn.",
    form_param_value_invalid: "De waarde die je hebt ingevoerd is ongeldig.",
    form_password_incorrect: "Het wachtwoord is incorrect.",
    form_password_length_too_short: "Het wachtwoord is te kort.",
    form_password_not_strong_enough: "Je wachtwoord is niet sterk genoeg.",
    form_password_pwned: "Dit wachtwoord is in een datalek gevonden.",
    form_password_pwned__sign_in: "Als je dit wachtwoord elders gebruikt, moet je het wijzigen.",
    form_password_size_in_bytes_exceeded: "Je wachtwoord heeft het maximum aantal bytes overschreden, vermijd speciale tekens.",
    form_password_validation_failed: "Wachtwoord is incorrect.",
    form_username_invalid_character: "De gebruikersnaam bevat ongeldige tekens.",
    form_username_invalid_length: "De gebruikersnaam is te kort of te lang.",
    identification_deletion_failed: "Je kunt je laatste identificatie niet verwijderen.",
    not_allowed_access: "Je e-mailadres of telefoonnummer is niet toegestaan voor registratie. Dit kan zijn omdat je '+', '=', '#' of '.' in je e-mailadres gebruikt, een domein dat is gekoppeld aan een tijdelijke e-mailservice gebruikt, of een expliciete uitsluiting heeft.",
    organization_domain_blocked: "Het domein van de organisatie is geblokkeerd.",
    organization_domain_common: "Het domein van de organisatie is te algemeen.",
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: "Het lidmaatschapsquotum van de organisatie is overschreden.",
    organization_minimum_permissions_needed: "Minimale machtigingen vereist voor de organisatie.",
    passkey_already_exists: "Deze passkey bestaat al.",
    passkey_not_supported: "Passkeys worden niet ondersteund door deze browser.",
    passkey_pa_not_supported: "Passkeys worden niet ondersteund door deze browser.",
    passkey_registration_cancelled: "Passkey registratie is geannuleerd.",
    passkey_retrieval_cancelled: "Passkey ophalen is geannuleerd.",
    passwordComplexity: {
      maximumLength: "Wachtwoord moet minder dan 256 tekens bevatten.",
      minimumLength: "Wachtwoord moet minstens 8 tekens bevatten.",
      requireLowercase: "Wachtwoord moet minstens 1 kleine letter bevatten.",
      requireNumbers: "Wachtwoord moet minstens 1 cijfer bevatten.",
      requireSpecialCharacter: "Wachtwoord moet minstens 1 speciaal teken bevatten.",
      requireUppercase: "Wachtwoord moet minstens 1 hoofdletter bevatten.",
      sentencePrefix: "Wachtwoord moet minstens 1 speciaal teken bevatten."
    },
    phone_number_exists: "Dit telefoonnummer is al in gebruik. Probeer een ander nummer.",
    session_exists: "Je bent al ingelogd.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "Je wachtwoord werkt, maar kan sterker zijn. Probeer meer tekens toe te voegen.",
      goodPassword: "Je wachtwoord voldoet aan alle vereisten.",
      notEnough: "Je wachtwoord is niet sterk genoeg.",
      suggestions: {
        allUppercase: "Zet een deel in hoofdletters, maar niet alle letters.",
        anotherWord: "Voeg meer woorden toe die minder vaak voorkomen.",
        associatedYears: "Vermijd jaartallen die met jou geassocieerd zijn.",
        capitalization: "Zet meer dan de eerste letter in hoofdletter.",
        dates: "Vermijd data en jaartallen die met jou geassocieerd zijn.",
        l33t: "Vermijd voorspelbare vervangingen, zoals '@' voor 'a'.",
        longerKeyboardPattern: "Gebruik langere toetsenbord patronen, en wissel meerdere keren van richting.",
        noNeed: "Je kan ook een sterk wachtwoord maken zonder speciale tekens, hoofdletters of nummers.",
        pwned: "Als u dit wachtwoord elders gebruikt, moet u het veranderen.",
        recentYears: "Vermijd recente jaartallen.",
        repeated: "Vermijd herhalende woorden en letters.",
        reverseWords: "Vermijd het omdraaien van veelvoorkomende woorden.",
        sequences: "Vermijd veelvoorkomende tekstreeksen.",
        useWords: "Gebruik meerdere woorden, maar vermijd veelvoorkomende zinnen."
      },
      warnings: {
        common: "Dit wachtwoord wordt veel gebruikt.",
        commonNames: "Veelvoorkomende voor- en achternamen zijn makkelijk te raden.",
        dates: "Datums zijn makkelijk te raden.",
        extendedRepeat: 'Herhalende patronen zoals "abcabcabc" zijn makkelijk te raden.',
        keyPattern: "Korte toetsenbord patronen zijn makkelijk te raden.",
        namesByThemselves: "Voor- en achternamen op zich zijn makkelijk te raden.",
        pwned: "Dit wachtwoord is in een datalek gevonden.",
        recentYears: "Recente jaartallen zijn makkelijk te raden.",
        sequences: 'Veelvoorkomende tekstreeksen zoals "abc" zijn makkelijk te raden.',
        similarToCommon: "Dit lijkt op een veelvoorkomend wachtwoord.",
        simpleRepeat: 'Herhalende letters zoals "aaa" zijn makkelijk te raden.',
        straightRow: "Opeenvolgende toetsen op jouw toetsenbord zijn makkelijk te raden.",
        topHundred: "Dit wachtwoord wordt erg veel gebruikt.",
        topTen: "Dit wachtwoord wordt heel erg veel gebruikt.",
        userInputs: "Vermijd persoonlijke of website gerelateerde woorden.",
        wordByItself: "Woorden op zich zijn makkelijk te raden."
      }
    }
  },
  userButton: {
    action__addAccount: "Account toevoegen",
    action__manageAccount: "Account beheren",
    action__signOut: "Uitloggen",
    action__signOutAll: "Uitloggen uit alle accounts"
  },
  userProfile: {
    apiKeysPage: {
      title: void 0
    },
    backupCodePage: {
      actionLabel__copied: "Gekopieerd!",
      actionLabel__copy: "Kopieer",
      actionLabel__download: "Download .txt",
      actionLabel__print: "Print",
      infoText1: "Backupcodes zullen voor dit account ingeschakeld zijn.",
      infoText2: "Houd de backupcodes geheim en bewaar ze veilig. U kunt backupcodes opnieuw genereren als u vermoedt dat ze zijn aangetast.",
      subtitle__codelist: "Sla ze veilig op en hou ze geheim.",
      successMessage: "Backupcodes zijn nu ingeschakeld. U kunt er een van gebruiken om in te loggen op uw account als u geen toegang meer heeft tot uw authenticatieapparaat. Elke code kan maar \xE9\xE9n keer gebruikt worden.",
      successSubtitle: "Je kunt \xE9\xE9n van deze gebruiken om in te loggen op je account als je geen toegang meer hebt tot je authenticatieapparaat.",
      title: "Voeg backup code verificatie toe",
      title__codelist: "Backup codes"
    },
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Kies een provider om je account te verbinden.",
      formHint__noAccounts: "Er zijn geen beschikbare externe accountproviders.",
      removeResource: {
        messageLine1: "{{identifier}} zal verwijderd worden uit dit account.",
        messageLine2: "Je kunt deze verbonden account niet meer gebruiken en afhankelijke functies zullen niet meer werken.",
        successMessage: "{{connectedAccount}} is verwijderd uit je account.",
        title: "Verwijder externe account"
      },
      socialButtonsBlockButton: "Verbind {{provider|titleize}} account",
      successMessage: "Deze provider is toegevoegd aan je account.",
      title: "Verbind externe account"
    },
    deletePage: {
      actionDescription: 'Type "Delete account" below to continue.',
      confirm: "Delete account",
      messageLine1: "Are you sure you want to delete your account?",
      messageLine2: "This action is permanent and irreversible.",
      title: "Delete account"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "Een mail met daarin een verificatiecode is verstuurd naar dit adres.",
        formSubtitle: "Voer de verificatiecode in die verstuurd is naar {{identifier}}",
        formTitle: "Verificatiecode",
        resendButton: "Verstuur code opnieuw",
        successMessage: "Het e-mailadres {{identifier}} is toegevoegd aan je account."
      },
      emailLink: {
        formHint: "Een mail met daarin een verificatielink is verstuurd naar dit adres.",
        formSubtitle: "Klik op de verificatielink die verstuurd is naar {{identifier}}",
        formTitle: "Verificatielink",
        resendButton: "Verstuur link opnieuw",
        successMessage: "Het e-mailadres {{identifier}} is toegevoegd aan je account."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} zal verwijderd worden uit dit account.",
        messageLine2: "Je zal niet meer kunnen inloggen met dit e-mailadres.",
        successMessage: "{{emailAddress}} is verwijderd uit je account.",
        title: "Verwijder e-mailadres"
      },
      title: "E-mailadres toevoegen",
      verifyTitle: "E-mailadres bevestigen"
    },
    formButtonPrimary__add: "Toevoegen",
    formButtonPrimary__continue: "Doorgaan",
    formButtonPrimary__finish: "Afronden",
    formButtonPrimary__remove: "Verwijderen",
    formButtonPrimary__save: "Opslaan",
    formButtonReset: "Annuleren",
    mfaPage: {
      formHint: "Kies een methode om toe te voegen.",
      title: "Tweestapsverificatie toevoegen"
    },
    mfaPhoneCodePage: {
      backButton: "Gebruik bestaand nummer",
      primaryButton__addPhoneNumber: "Telefoonnummer toevoegen",
      removeResource: {
        messageLine1: "{{identifier}} zal niet langer verificatiecodes ontvangen bij het inloggen.",
        messageLine2: "Uw account is mogelijk niet zo veilig. Weet je zeker dat je door wilt gaan?",
        successMessage: "SMS-code tweestapsverificatie is verwijderd voor {{mfaPhoneCode}}",
        title: "Verwijder tweestapsverificatie"
      },
      subtitle__availablePhoneNumbers: "Selecteer een telefoonnummer om je te registreren voor SMS-code twee-stapsverificatie.",
      subtitle__unavailablePhoneNumbers: "Er zijn geen beschikbare telefoonnummers om te registreren voor SMS-code tweestapsverificatie.",
      successMessage1: "When signing in, you will need to enter a verification code sent to this phone number as an additional step.",
      successMessage2: "Sla deze backup codes op en bewaar ze ergens veilig. Als je toegang kwijtraakt tot je authenticatieapparaat, kun je de backup codes gebruiken om in te loggen.",
      successTitle: "SMS-code verificatie ingeschakeld",
      title: "Voeg SMS-code verificatie toe"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Een tweede optie, scan de QR-code",
        buttonUnableToScan__nonPrimary: "Kan je de code niet scannen?",
        infoText__ableToScan: "Scan de QR-code met je authenticator app om de authenticator toe te voegen.",
        infoText__unableToScan: "Stel een nieuwe aanmeldmethode in op je authenticator en voer de onderstaande sleutel in.",
        inputLabel__unableToScan1: "Zorg ervoor dat tijdsgebaseerde of eenmalige wachtwoorden zijn ingeschakeld, en voltooi vervolgens het koppelen van uw account.",
        inputLabel__unableToScan2: "Als je authenticator TOTP-URI's ondersteunt, kun je ook de volledige URI kopi\xEBren."
      },
      removeResource: {
        messageLine1: "Verificatiecodes van deze authenticator zullen niet langer vereist zijn bij het inloggen.",
        messageLine2: "Uw account is mogelijk niet zo veilig. Weet je zeker dat je door wilt gaan?",
        successMessage: "Tweestapsverificatie via authenticator-applicatie is verwijderd.",
        title: "Verwijder tweestapsverificatie"
      },
      successMessage: "Tweestapsverificatie is nu ingesteld. Bij het inloggen zal je een verificatiecode van je authenticator app moeten invoeren.",
      title: "Voeg authenticator toe",
      verifySubtitle: "Voer de verificatiecode in die je authenticator app heeft gegenereerd.",
      verifyTitle: "Verificatiecode"
    },
    mobileButton__menu: "Menu",
    navbar: {
      account: "Profiel",
      apiKeys: void 0,
      billing: void 0,
      description: "Beheer je account informatie.",
      security: "Beveiliging",
      title: "Account"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} zal verwijderd worden uit dit account.",
        title: "Verwijder passkey"
      },
      subtitle__rename: "Je kunt de naam van de passkey wijzigen om deze gemakkelijker te vinden.",
      title__rename: "Passkey hernoemen"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Het is aanbevolen om uit te loggen van alle andere apparaten die mogelijk gebruik hebben gemaakt van je oude wachtwoord.",
      readonly: "Je wachtwoord kan momenteel niet worden gewijzigd omdat je alleen via de enterprise connectie kunt inloggen.",
      successMessage__set: "Je wachtwoord is ingesteld.",
      successMessage__signOutOfOtherSessions: "Alle andere apparaten zijn uitgelogd.",
      successMessage__update: "Je wachtwoord is bijgewerkt.",
      title__set: "Stel wachtwoord in",
      title__update: "Wachtwoord wijzigen"
    },
    phoneNumberPage: {
      infoText: "Een SMS met daarin een verificatiecode is verstuurd naar dit nummer.",
      removeResource: {
        messageLine1: "{{identifier}} zal van dit account verwijderd worden.",
        messageLine2: "Je zal niet meer kunnen inloggen met dit telefoonnummer.",
        successMessage: "{{phoneNumber}} is verwijderd uit je account.",
        title: "Verwijder telefoonnummer"
      },
      successMessage: "{{phoneNumber}} is toegevoegd aan je account.",
      title: "Telefoonnummer toevoegen",
      verifySubtitle: "Voer de verificatiecode in die verstuurd is naar {{phoneNumber}}",
      verifyTitle: "Verifieer telefoonnummer"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "Upload een JPG, PNG, GIF, of WEBP afbeelding kleiner dan 10 MB",
      imageFormDestructiveActionSubtitle: "Verwijder afbeelding",
      imageFormSubtitle: "Afbeelding uploaden",
      imageFormTitle: "Profielfoto",
      readonly: "Je profiel informatie is verstrekt door de enterprise connectie en kan niet worden bewerkt.",
      successMessage: "Je profiel is bijgewerkt.",
      title: "Profiel bijwerken"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Log uit op apparaat",
        title: "Actieve apparaten"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Probeer opnieuw",
        actionLabel__reauthorize: "Authoriseer nu",
        destructiveActionTitle: "Verwijderen",
        primaryButton: "Verbind een account",
        subtitle__disconnected: "Je account is losgekoppeld. Verbind het opnieuw om verder te gaan.",
        subtitle__reauthorize: "De vereiste scopes zijn bijgewerkt, en je kunt mogelijk beperkte functionaliteit ervaren. Autoriseer deze toepassing opnieuw om problemen te voorkomen.",
        title: "Aangesloten accounts"
      },
      dangerSection: {
        deleteAccountButton: "Verwijder account",
        title: "Account be\xEBindigen"
      },
      emailAddressesSection: {
        destructiveAction: "Verwijder e-mailadres",
        detailsAction__nonPrimary: "Stel in als hoofd",
        detailsAction__primary: "Rond verificatie af",
        detailsAction__unverified: "Rond verificatie af",
        primaryButton: "Voeg een e-mailadres toe",
        title: "E-mailadressen"
      },
      enterpriseAccountsSection: {
        title: "Bedrijfsaccounts"
      },
      headerTitle__account: "Account",
      headerTitle__security: "Beveiliging",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Codes hergenereren",
          headerTitle: "Backupcodes",
          subtitle__regenerate: "Genereer een nieuwe set backupcodes. De vorige kunnen niet meer gebruikt worden.",
          title__regenerate: "Backupcodes hergenereren"
        },
        phoneCode: {
          actionLabel__setDefault: "Stel in als standaard",
          destructiveActionLabel: "Verwijder tweestapsverificatie"
        },
        primaryButton: "Tweestapsverificatie instellen",
        title: "Tweestapsverificatie",
        totp: {
          destructiveActionTitle: "Verwijderen",
          headerTitle: "Authenticatorapplicatie"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Verwijderen",
        menuAction__rename: "Hernoemen",
        primaryButton: void 0,
        title: "Passkeys"
      },
      passwordSection: {
        primaryButton__setPassword: "Wachtwoord instellen",
        primaryButton__updatePassword: "Wachtwoord wijzigen",
        title: "Wachtwoord"
      },
      phoneNumbersSection: {
        destructiveAction: "Verwijder telefoonnummer",
        detailsAction__nonPrimary: "Stel in als hoofd",
        detailsAction__primary: "Rond verificatie af",
        detailsAction__unverified: "Rond verificatie af",
        primaryButton: "Voeg een telefoonnummer toe",
        title: "Telefoonnummers"
      },
      profileSection: {
        primaryButton: "Profiel bijwerken",
        title: "Profiel"
      },
      usernameSection: {
        primaryButton__setUsername: "Stel gebruikersnaam in",
        primaryButton__updateUsername: "Wijzig gebruikersnaam",
        title: "Gebruikersnaam"
      },
      web3WalletsSection: {
        destructiveAction: "Verwijder portefeuille",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3 portefeuilles",
        title: "Web3 portefeuilles"
      }
    },
    usernamePage: {
      successMessage: "Je gebruikersnaam is bijgewerkt.",
      title__set: "Gebruikersnaam bijwerken",
      title__update: "Gebruikersnaam bijwerken"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} zal verwijderd worden uit dit account.",
        messageLine2: "Je zal niet meer kunnen inloggen met deze web3 portefeuille.",
        successMessage: "{{web3Wallet}} is verwijderd uit je account.",
        title: "Verwijder web3 portefeuille"
      },
      subtitle__availableWallets: "Selecteer een web3 portefeuille om toe te voegen.",
      subtitle__unavailableWallets: "Er zijn geen beschikbare web3 portefeuilles.",
      successMessage: "De portefeuille is toegevoegd aan dit account.",
      title: "Web3 portefeuille toevoegen.",
      web3WalletButtonsBlockButton: "Voeg een Web3-portefeuille toe"
    }
  },
  waitlist: {
    start: {
      actionLink: "Inloggen",
      actionText: "Heb je al toegang?",
      formButton: "Verstuur",
      subtitle: "Je wordt toegevoegd aan de wachtlijst en op de hoogte gehouden.",
      title: "Wachtlijst aanmelding"
    },
    success: {
      message: "Je bent succesvol toegevoegd aan de wachtlijst!",
      subtitle: "Je ontvangt een bericht zodra er ruimte beschikbaar is.",
      title: "Succes!"
    }
  }
};
export {
  nlNL
};
//# sourceMappingURL=nl-NL.mjs.map