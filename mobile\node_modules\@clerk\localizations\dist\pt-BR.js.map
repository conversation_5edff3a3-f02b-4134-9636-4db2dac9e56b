{"version": 3, "sources": ["../src/pt-BR.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const ptBR: LocalizationResource = {\n  locale: 'pt-BR',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Voltar',\n  badge__activePlan: 'Ativo',\n  badge__canceledEndsAt: \"Cancelado • Termina {{ date | shortDate('pt-BR') }}\",\n  badge__currentPlan: 'Plano atual',\n  badge__default: 'Padrão',\n  badge__endsAt: \"Termina {{ date | shortDate('pt-BR') }}\",\n  badge__expired: 'Expirado',\n  badge__otherImpersonatorDevice: 'Personificar outro dispositivo',\n  badge__primary: 'Principal',\n  badge__renewsAt: \"Renova {{ date | shortDate('pt-BR') }}\",\n  badge__requiresAction: 'Requer ação',\n  badge__startsAt: \"Inicia {{ date | shortDate('pt-BR') }}\",\n  badge__thisDevice: 'Este dispositivo',\n  badge__unverified: 'Não verificado',\n  badge__upcomingPlan: 'Próximo plano',\n  badge__userDevice: 'Dispositivo do usuário',\n  badge__you: 'Você',\n  commerce: {\n    addPaymentMethod: 'Adicionar método de pagamento',\n    alwaysFree: 'Gratuito',\n    annually: 'Anualmente',\n    availableFeatures: 'Recursos disponíveis',\n    billedAnnually: 'Cobrança anual',\n    billedMonthlyOnly: 'Apenas cobrança mensal',\n    cancelSubscription: 'Cancelar assinatura',\n    cancelSubscriptionAccessUntil:\n      \"Você pode continuar usando os recursos de {{plan}} até {{ date | longDate('pt-BR') }}, após o qual você não terá mais acesso.\",\n    cancelSubscriptionNoCharge: 'Você não será cobrado por esta assinatura.',\n    cancelSubscriptionTitle: 'Cancelar assinatura do plano {{plan}}?',\n    cannotSubscribeMonthly:\n      'Você não pode assinar este plano pagando mensalmente. Para assinar este plano, você precisa escolher pagar anualmente.',\n    checkout: {\n      description__paymentSuccessful: 'Seu pagamento foi realizado com sucesso.',\n      description__subscriptionSuccessful: 'Sua nova assinatura está pronta.',\n      downgradeNotice:\n        'Você manterá sua assinatura atual e seus recursos até o final do ciclo de faturamento, após o qual você será transferido para este plano.',\n      emailForm: {\n        subtitle:\n          'Antes de concluir sua compra, você deve adicionar um endereço de e-mail para o qual os recibos serão enviados.',\n        title: 'Adicionar endereço de e-mail',\n      },\n      lineItems: {\n        title__paymentMethod: 'Método de pagamento',\n        title__statementId: 'ID da declaração',\n        title__subscriptionBegins: 'Assinatura começa',\n        title__totalPaid: 'Total pago',\n      },\n      pastDueNotice: 'Sua assinatura anterior estava em atraso, sem pagamento.',\n      perMonth: 'por mês',\n      title: 'Checkout',\n      title__paymentSuccessful: 'Pagamento realizado com sucesso!',\n      title__subscriptionSuccessful: 'Sucesso!',\n    },\n    credit: 'Crédito',\n    creditRemainder: 'Crédito para o restante da sua assinatura atual.',\n    defaultFreePlanActive: 'Você está atualmente no plano Gratuito',\n    free: 'Gratuito',\n    getStarted: 'Começar',\n    keepSubscription: 'Manter assinatura',\n    manage: 'Gerenciar',\n    manageSubscription: 'Gerenciar assinatura',\n    month: 'Mês',\n    monthly: 'Mensal',\n    pastDue: 'Atrasado',\n    pay: 'Pagar {{amount}}',\n    paymentMethods: 'Métodos de pagamento',\n    paymentSource: {\n      applePayDescription: {\n        annual: 'Pagamento anual',\n        monthly: 'Pagamento mensal',\n      },\n      dev: {\n        anyNumbers: 'Qualquer número',\n        cardNumber: 'Número do cartão',\n        cvcZip: 'CVC, CEP',\n        developmentMode: 'Modo de desenvolvimento',\n        expirationDate: 'Data de validade',\n        testCardInfo: 'Informações do cartão de teste',\n      },\n    },\n    popular: 'Popular',\n    pricingTable: {\n      billingCycle: 'Ciclo de faturamento',\n      included: 'Incluso',\n    },\n    reSubscribe: 'Assinar novamente',\n    seeAllFeatures: 'Ver todos os recursos',\n    subscribe: 'Assinar',\n    subtotal: 'Subtotal',\n    switchPlan: 'Mudar de plano',\n    switchToAnnual: 'Mudar para anual',\n    switchToMonthly: 'Mudar para mensal',\n    totalDue: 'Total devido',\n    totalDueToday: 'Total devido hoje',\n    viewFeatures: 'Ver recursos',\n    year: 'Ano',\n  },\n  createOrganization: {\n    formButtonSubmit: 'Criar organização',\n    invitePage: {\n      formButtonReset: 'Pular',\n    },\n    title: 'Criar organização',\n  },\n  dates: {\n    lastDay: \"Ontem às {{ date | timeString('pt-BR') }}\",\n    next6Days: \"{{ date | weekday('pt-BR','long') }} às {{ date | timeString('pt-BR') }}\",\n    nextDay: \"Amanhã às {{ date | timeString('pt-BR') }}\",\n    numeric: \"{{ date | numeric('pt-BR') }}\",\n    previous6Days: \"Último {{ date | weekday('pt-BR','long') }} às {{ date | timeString('pt-BR') }}\",\n    sameDay: \"Hoje às {{ date | timeString('pt-BR') }}\",\n  },\n  dividerText: 'ou',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Utilize outro método',\n  footerPageLink__help: 'Ajuda',\n  footerPageLink__privacy: 'Privacidade',\n  footerPageLink__terms: 'Termos de uso',\n  formButtonPrimary: 'Continuar',\n  formButtonPrimary__verify: 'Verificar',\n  formFieldAction__forgotPassword: 'Esqueceu a senha?',\n  formFieldError__matchingPasswords: 'Senhas conferem.',\n  formFieldError__notMatchingPasswords: 'Senhas não conferem.',\n  formFieldError__verificationLinkExpired: 'O link de verificação expirou. Por favor solicite um novo link.',\n  formFieldHintText__optional: 'Opcional',\n  formFieldHintText__slug:\n    'Um rótulo é um identificador legível por humanos que deve ser único. É comumente usado em URLs.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: 'Insira o código de backup',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Excluir conta',\n  formFieldInputPlaceholder__emailAddress: 'Digite o endereço de e-mail',\n  formFieldInputPlaceholder__emailAddress_username: 'Digite seu e-mail ou nome de usuário',\n  formFieldInputPlaceholder__emailAddresses: 'Insira um ou mais endereços de e-mail separados por espaços ou vírgulas',\n  formFieldInputPlaceholder__firstName: 'Digite seu primeiro nome',\n  formFieldInputPlaceholder__lastName: 'Digite seu sobrenome',\n  formFieldInputPlaceholder__organizationDomain: 'Digite o domínio da organização',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: 'Digite o e-mail associado ao domínio da organização',\n  formFieldInputPlaceholder__organizationName: 'Digite o nome da organização',\n  formFieldInputPlaceholder__organizationSlug: 'minha-org',\n  formFieldInputPlaceholder__password: 'Digite sua senha',\n  formFieldInputPlaceholder__phoneNumber: 'Digite seu número de telefone',\n  formFieldInputPlaceholder__username: 'Digite seu nome de usuário',\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Ativar convites automáticos para este domínio',\n  formFieldLabel__backupCode: 'Código de backup',\n  formFieldLabel__confirmDeletion: 'Confirmar exclusão',\n  formFieldLabel__confirmPassword: 'Confirmar senha',\n  formFieldLabel__currentPassword: 'Senha atual',\n  formFieldLabel__emailAddress: 'Seu e-mail',\n  formFieldLabel__emailAddress_username: 'E-mail ou nome de usuário',\n  formFieldLabel__emailAddresses: 'Endereços de e-mail',\n  formFieldLabel__firstName: 'Nome',\n  formFieldLabel__lastName: 'Sobrenome',\n  formFieldLabel__newPassword: 'Nova senha',\n  formFieldLabel__organizationDomain: 'Domínio',\n  formFieldLabel__organizationDomainDeletePending: 'Excluir convites e sugestões pendentes',\n  formFieldLabel__organizationDomainEmailAddress: 'Endereço de e-mail de verificação',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Endereço de e-mail para receber um código e verificar este domínio',\n  formFieldLabel__organizationName: 'Nome da organização',\n  formFieldLabel__organizationSlug: 'Rótulo do URL',\n  formFieldLabel__passkeyName: 'Nome da chave de acesso',\n  formFieldLabel__password: 'Senha',\n  formFieldLabel__phoneNumber: 'Telefone',\n  formFieldLabel__role: 'Função',\n  formFieldLabel__signOutOfOtherSessions: 'Desconectar de todos os outros dispositivos',\n  formFieldLabel__username: 'Nome de usuário',\n  impersonationFab: {\n    action__signOut: 'Sair',\n    title: 'Logado como {{identifier}}',\n  },\n  maintenanceMode: 'Estamos em manutenção, mas não se preocupe, não deve levar mais do que alguns minutos',\n  membershipRole__admin: 'Administrador',\n  membershipRole__basicMember: 'Membro',\n  membershipRole__guestMember: 'Convidado',\n  organizationList: {\n    action__createOrganization: 'Criar organização',\n    action__invitationAccept: 'Participar',\n    action__suggestionsAccept: 'Solicitar participação',\n    createOrganization: 'Criar organização',\n    invitationAcceptedLabel: 'Participando',\n    subtitle: 'para continuar no {{applicationName}}',\n    suggestionsAcceptedLabel: 'Aprovação pendente',\n    title: 'Selecione uma conta',\n    titleWithoutPersonal: 'Selecione uma organização',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Convites automáticos',\n    badge__automaticSuggestion: 'Sugestões automáticas',\n    badge__manualInvitation: 'Sem inscrição automática',\n    badge__unverified: 'Não verificado',\n    billingPage: {\n      paymentHistorySection: {\n        empty: 'Nenhum histórico de pagamento',\n        notFound: 'Pagamento não encontrado',\n        tableHeader__amount: 'Valor',\n        tableHeader__date: 'Data',\n        tableHeader__status: 'Status',\n      },\n      paymentSourcesSection: {\n        actionLabel__default: 'Tornar padrão',\n        actionLabel__remove: 'Remover',\n        add: 'Adicionar novo método de pagamento',\n        addSubtitle: 'Adicione um novo método de pagamento à sua conta.',\n        cancelButton: 'Cancelar',\n        formButtonPrimary__add: 'Adicionar Método de Pagamento',\n        formButtonPrimary__pay: 'Pagar {{amount}}',\n        payWithTestCardButton: 'Pagar com cartão de teste',\n        removeResource: {\n          messageLine1: '{{identifier}} será removido desta conta.',\n          messageLine2:\n            'Você não poderá mais usar esta forma de pagamento e quaisquer assinaturas recorrentes dependentes dela deixarão de funcionar.',\n          successMessage: '{{paymentSource}} foi removido da sua conta.',\n          title: 'Remover método de pagamento',\n        },\n        title: 'Métodos de pagamento',\n      },\n      start: {\n        headerTitle__payments: 'Pagamentos',\n        headerTitle__plans: 'Planos',\n        headerTitle__statements: 'Extratos',\n        headerTitle__subscriptions: 'Assinaturas',\n      },\n      statementsSection: {\n        empty: 'Nenhum extrato para exibir',\n        itemCaption__paidForPlan: 'Pago para plano {{plan}} {{period}}',\n        itemCaption__proratedCredit: 'Crédito proporcional para uso parcial do plano anterior',\n        itemCaption__subscribedAndPaidForPlan: 'Assinado e pago para plano {{plan}} {{period}}',\n        notFound: 'Extrato não encontrado',\n        tableHeader__amount: 'Valor',\n        tableHeader__date: 'Data',\n        title: 'Extratos',\n        totalPaid: 'Total pago',\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: 'Assinar um plano',\n        actionLabel__switchPlan: 'Mudar de plano',\n        tableHeader__edit: 'Editar',\n        tableHeader__plan: 'Plano',\n        tableHeader__startDate: 'Data de início',\n        title: 'Assinatura',\n      },\n      subscriptionsSection: {\n        actionLabel__default: 'Gerenciar',\n      },\n      switchPlansSection: {\n        title: 'Mudar de plano',\n      },\n      title: 'Faturamento',\n    },\n    createDomainPage: {\n      subtitle:\n        'Adicione o domínio para verificar. Usuários com endereços de e-mail neste domínio podem se juntar à organização automaticamente ou solicitar participação.',\n      title: 'Adicionar domínio',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Os convites não puderam ser enviados. Já existem convites pendentes para os seguintes endereços de e-mail: {{email_addresses}}.',\n      formButtonPrimary__continue: 'Enviar convites',\n      selectDropdown__role: 'Selecione a função',\n      subtitle: 'Insira ou cole um ou mais endereços de e-mail, separados por espaços ou vírgulas.',\n      successMessage: 'Convites enviados com sucesso',\n      title: 'Convidar membros',\n    },\n    membersPage: {\n      action__invite: 'Convidar',\n      action__search: 'Pesquisar',\n      activeMembersTab: {\n        menuAction__remove: 'Remover membro',\n        tableHeader__actions: 'Ações',\n        tableHeader__joined: 'Entrou',\n        tableHeader__role: 'Função',\n        tableHeader__user: 'Usuário',\n      },\n      detailsTitle__emptyRow: 'Nenhum membro para exibir',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Convide usuários conectando um domínio de e-mail com sua organização. Qualquer pessoa que se inscrever com um domínio de e-mail correspondente poderá se juntar à organização a qualquer momento.',\n          headerTitle: 'Convites automáticos',\n          primaryButton: 'Gerenciar domínios verificados',\n        },\n        table__emptyRow: 'Nenhum convite para exibir',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Cancelar convite',\n        tableHeader__invited: 'Convidado',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Usuários que se inscrevem com um domínio de e-mail correspondente podem ver uma sugestão para solicitar participação em sua organização.',\n          headerTitle: 'Sugestões automáticas',\n          primaryButton: 'Gerenciar domínios verificados',\n        },\n        menuAction__approve: 'Aprovar',\n        menuAction__reject: 'Rejeitar',\n        tableHeader__requested: 'Acesso solicitado',\n        table__emptyRow: 'Nenhuma solicitação para exibir',\n      },\n      start: {\n        headerTitle__invitations: 'Convites',\n        headerTitle__members: 'Membros',\n        headerTitle__requests: 'Solicitações',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: 'Faturamento',\n      description: 'Gerencie sua organização.',\n      general: 'Geral',\n      members: 'Membros',\n      title: 'Organização',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: 'Você não tem permissões para gerenciar o faturamento desta organização.',\n      },\n      title: 'Planos',\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Digite {{organizationName}} abaixo para continuar.',\n          messageLine1: 'Tem certeza de que deseja excluir esta organização?',\n          messageLine2: 'Esta ação é permanente e irreversível.',\n          successMessage: 'Você excluiu a organização.',\n          title: 'Excluir organização',\n        },\n        leaveOrganization: {\n          actionDescription: 'Digite {{organizationName}} abaixo para continuar.',\n          messageLine1:\n            'Tem certeza de que deseja sair desta organização? Você perderá o acesso a esta organização e suas aplicações.',\n          messageLine2: 'Esta ação é permanente e não pode ser desfeita.',\n          successMessage: 'Você saiu da organização.',\n          title: 'Sair da organização',\n        },\n        title: 'Perigo',\n      },\n      domainSection: {\n        menuAction__manage: 'Gerenciar',\n        menuAction__remove: 'Excluir',\n        menuAction__verify: 'Verificar',\n        primaryButton: 'Adicionar domínio',\n        subtitle:\n          'Permita que os usuários se juntem à organização automaticamente ou solicitem participação com base em um domínio de e-mail verificado.',\n        title: 'Domínios verificados',\n      },\n      successMessage: 'A organização foi atualizada.',\n      title: 'Perfil da organização',\n    },\n    removeDomainPage: {\n      messageLine1: 'O domínio de e-mail {{domain}} será removido.',\n      messageLine2: 'Os usuários não poderão mais se juntar à organização automaticamente após isso.',\n      successMessage: '{{domain}} foi removido.',\n      title: 'Excluir domínio',\n    },\n    start: {\n      headerTitle__general: 'Geral',\n      headerTitle__members: 'Membros',\n      profileSection: {\n        primaryButton: 'Atualizar perfil',\n        title: 'Perfil da Organização',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'A exclusão deste domínio afetará os usuários convidados.',\n        removeDomainActionLabel__remove: 'Excluir domínio',\n        removeDomainSubtitle: 'Remova este domínio de seus domínios verificados',\n        removeDomainTitle: 'Excluir domínio',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Os usuários são convidados automaticamente a se juntar à organização quando se inscrevem e podem se juntar a qualquer momento.',\n        automaticInvitationOption__label: 'Convites automáticos',\n        automaticSuggestionOption__description:\n          'Os usuários recebem uma sugestão para solicitar participação, mas devem ser aprovados por um administrador antes de poderem se juntar à organização.',\n        automaticSuggestionOption__label: 'Sugestões automáticas',\n        calloutInfoLabel: 'Alterar o modo de inscrição afetará apenas os novos usuários.',\n        calloutInvitationCountLabel: 'Convites pendentes enviados aos usuários: {{count}}',\n        calloutSuggestionCountLabel: 'Sugestões pendentes enviadas aos usuários: {{count}}',\n        manualInvitationOption__description: 'Os usuários só podem ser convidados manualmente para a organização.',\n        manualInvitationOption__label: 'Sem inscrição automática',\n        subtitle: 'Escolha como os usuários deste domínio podem se juntar à organização.',\n      },\n      start: {\n        headerTitle__danger: 'Perigo',\n        headerTitle__enrollment: 'Opções de inscrição',\n      },\n      subtitle: 'O domínio {{domain}} agora está verificado. Continue selecionando o modo de inscrição.',\n      title: 'Atualizar {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Insira o código de verificação enviado para o seu endereço de e-mail',\n      formTitle: 'Código de verificação',\n      resendButton: 'Não recebeu um código? Reenviar',\n      subtitle: 'O domínio {{domainName}} precisa ser verificado por e-mail.',\n      subtitleVerificationCodeScreen:\n        'Um código de verificação foi enviado para {{emailAddress}}. Insira o código para continuar.',\n      title: 'Verificar domínio',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Criar organização',\n    action__invitationAccept: 'Participar',\n    action__manageOrganization: 'Gerenciar organização',\n    action__suggestionsAccept: 'Solicitar participação',\n    notSelected: 'Nenhuma organização selecionada',\n    personalWorkspace: 'Conta pessoal',\n    suggestionsAcceptedLabel: 'Aprovação pendente',\n  },\n  paginationButton__next: 'Próximo',\n  paginationButton__previous: 'Anterior',\n  paginationRowText__displaying: 'Exibindo',\n  paginationRowText__of: 'de',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Solicitar ajuda',\n      actionText: 'Não tem nenhum dos métodos? Tente outra forma.',\n      blockButton__backupCode: 'Usar código de backup',\n      blockButton__emailCode: 'Enviar código para {{identifier}}',\n      blockButton__passkey: 'Usar sua chave de acesso',\n      blockButton__password: 'Usar senha',\n      blockButton__phoneCode: 'Enviar código de telefone',\n      blockButton__totp: 'Usar autenticação TOTP',\n      getHelp: {\n        blockButton__emailSupport: 'Entrar em contato com o suporte',\n        content: 'Se você não tem nenhum dos métodos listados, entre em contato com nosso suporte.',\n        title: 'Solicitar ajuda',\n      },\n      subtitle: 'Escolha um dos métodos alternativos para verificar sua identidade.',\n      title: 'Métodos alternativos de verificação',\n    },\n    backupCodeMfa: {\n      subtitle: 'Digite seu código de backup para continuar.',\n      title: 'Verificação com código de backup',\n    },\n    emailCode: {\n      formTitle: 'Código enviado para seu e-mail',\n      resendButton: 'Reenviar código',\n      subtitle: 'Verifique seu e-mail e insira o código para continuar.',\n      title: 'Verifique seu e-mail',\n    },\n    noAvailableMethods: {\n      message: 'Nenhum método de verificação disponível. Entre em contato com o suporte.',\n      subtitle: 'Não há métodos de verificação disponíveis no momento.',\n      title: 'Métodos de verificação indisponíveis',\n    },\n    passkey: {\n      blockButton__passkey: 'Usar sua chave de acesso',\n      subtitle:\n        'Usar sua chave de acesso confirma a sua identidade. Seu dispositivo pode solicitar sua impressão digital, reconhecimento facial ou PIN.',\n      title: 'Use sua chave de acesso.',\n    },\n    password: {\n      actionLink: 'Usar outro método',\n      subtitle: 'Digite sua senha para continuar.',\n      title: 'Digite sua senha',\n    },\n    phoneCode: {\n      formTitle: 'Código de verificação',\n      resendButton: 'Reenviar código',\n      subtitle: 'Verifique seu celular para o código de verificação.',\n      title: 'Verifique seu celular',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Código de verificação',\n      resendButton: 'Reenviar código',\n      subtitle: 'Verifique seu celular para o código de verificação.',\n      title: 'Verifique seu celular',\n    },\n    totpMfa: {\n      formTitle: 'Código de verificação TOTP',\n      subtitle: 'Digite o código de verificação gerado pelo seu aplicativo de autenticação.',\n      title: 'Autenticação TOTP',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Adicionar conta',\n      action__signOutAll: 'Sair de todas as contas',\n      subtitle: 'Selecione a conta com a qual gostaria de continuar.',\n      title: 'Escolha uma conta.',\n    },\n    alternativeMethods: {\n      actionLink: 'Ajuda',\n      actionText: 'Não tem nenhum destes?',\n      blockButton__backupCode: 'Utilize um código de backup',\n      blockButton__emailCode: 'Enviar código para {{identifier}}',\n      blockButton__emailLink: 'Enviar link para {{identifier}}',\n      blockButton__passkey: 'Acessar com sua chave de acesso',\n      blockButton__password: 'Acessar com sua senha',\n      blockButton__phoneCode: 'Enviar código para {{identifier}}',\n      blockButton__totp: 'Utilize seu aplicativo autenticador',\n      getHelp: {\n        blockButton__emailSupport: 'E-mail de suporte',\n        content:\n          'Se estiver com dificuldades para entrar em sua conta, envie um e-mail para nós que iremos te ajudar a restaurar seu acesso o mais rápido possível.',\n        title: 'Ajuda',\n      },\n      subtitle: 'Encontrando dificuldades? Você pode utilizar qualquer um destes métodos para acessar.',\n      title: 'Utilize outro método',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: 'Código de verificação',\n      resendButton: 'Reenviar código',\n      subtitle: 'Verifique seu celular para o código de verificação.',\n      title: 'Verifique seu celular',\n    },\n    backupCodeMfa: {\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Insira um código de backup',\n    },\n    emailCode: {\n      formTitle: 'Código de verificação',\n      resendButton: 'Reenviar código',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Verifique seu e-mail',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Para continuar, abra o link de verificação no mesmo dispositivo e navegador em que iniciou o login',\n        title: 'Link de verificação é inválido para este dispositivo',\n      },\n      expired: {\n        subtitle: 'Retorne para a aba original para continuar',\n        title: 'Este link de verificação expirou',\n      },\n      failed: {\n        subtitle: 'Retorne para a aba original para continuar',\n        title: 'Este link de verificação é inválido',\n      },\n      formSubtitle: 'Utilize o link enviado no seu e-mail',\n      formTitle: 'Link de verificação',\n      loading: {\n        subtitle: 'Você será redirecionado em breve',\n        title: 'Conectando...',\n      },\n      resendButton: 'Não recebeu um link? Reenviar',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Verifique seu e-mail',\n      unusedTab: {\n        title: 'Você pode fechar esta aba',\n      },\n      verified: {\n        subtitle: 'Você será redirecionado em breve',\n        title: 'Login realizado com sucesso',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Retorne para a aba original para continuar',\n        subtitleNewTab: 'Retorne para a nova aba que foi aberta para continuar',\n        titleNewTab: 'Conectado em outra aba',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Código de redefinição de senha',\n      resendButton: 'Não recebeu um código? Reenviar',\n      subtitle: 'para redefinir sua senha',\n      subtitle_email: 'Primeiro, digite o código enviado para seu e-mail',\n      subtitle_phone: 'Primeiro, digite o código enviado para seu telefone',\n      title: 'Redefinir senha',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Redefinir sua senha',\n      label__alternativeMethods: 'Ou, faça login com outro método.',\n      title: 'Esqueceu a senha?',\n    },\n    noAvailableMethods: {\n      message: 'Não foi possível fazer login. Não há nenhum método de autenticação disponível.',\n      subtitle: 'Aconteceu um erro',\n      title: 'Não foi possível fazer login',\n    },\n    passkey: {\n      subtitle:\n        'Usar sua chave de acesso confirma a sua identidade. Seu dispositivo pode solicitar sua impressão digital, reconhecimento facial ou PIN.',\n      title: 'Use sua chave de acesso.',\n    },\n    password: {\n      actionLink: 'Utilize outro método',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Insira sua senha',\n    },\n    passwordPwned: {\n      title: 'Senha comprometida',\n    },\n    phoneCode: {\n      formTitle: 'Código de verificação',\n      resendButton: 'Reenviar código',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Verifique seu telefone',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Código de verificação',\n      resendButton: 'Reenviar código',\n      subtitle: 'Para continuar, insira o código enviado para o seu telefone.',\n      title: 'Verifique seu telefone',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Redefinir Senha',\n      requiredMessage: 'Por razões de segurança, é necessário redefinir sua senha.',\n      successMessage: 'Sua senha foi alterada com sucesso. Entrando, por favor aguarde um momento.',\n      title: 'Redefinir Senha',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Precisamos verificar sua identidade antes de redefinir sua senha.',\n    },\n    start: {\n      actionLink: 'Registre-se',\n      actionLink__join_waitlist: 'Entrar na lista de espera',\n      actionLink__use_email: 'Usar e-mail',\n      actionLink__use_email_username: 'Usar e-mail ou nome de usuário',\n      actionLink__use_passkey: 'Ou use sua chave de acesso',\n      actionLink__use_phone: 'Usar telefone',\n      actionLink__use_username: 'Usar nome de usuário',\n      actionText: 'Não possui uma conta?',\n      actionText__join_waitlist: 'Quer ser notificado quando estivermos prontos?',\n      alternativePhoneCodeProvider: {\n        actionLink: 'Usar outro método',\n        label: '{{provider}} telefone',\n        subtitle: 'Insira seu número de telefone para receber um código de verificação em {{provider}}.',\n        title: 'Entrar no {{applicationName}} com {{provider}}',\n      },\n      subtitle: 'para continuar em {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Entrar',\n      titleCombined: 'Continuar em {{applicationName}}',\n    },\n    totpMfa: {\n      formTitle: 'Código de verificação',\n      subtitle: 'Para continuar, insira o código gerado pelo seu aplicativo autenticador.',\n      title: 'Verificação em duas etapas',\n    },\n  },\n  signInEnterPasswordTitle: 'Insira sua senha',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: 'Não recebeu um código? Reenviar',\n      subtitle: 'Insira o código de verificação enviado para seu {{provider}}',\n      title: 'Verifique seu {{provider}}',\n    },\n    continue: {\n      actionLink: 'Entrar',\n      actionText: 'Possui uma conta?',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Preencha os campos ausentes',\n    },\n    emailCode: {\n      formSubtitle: 'Insira o código enviado para seu e-mail',\n      formTitle: 'Código de verificação',\n      resendButton: 'Não recebeu o código? Reenviar',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Verifique seu e-mail',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Para continuar, abra o link de verificação no mesmo dispositivo e navegador em que iniciou o cadastro',\n        title: 'Link de verificação é inválido para este dispositivo',\n      },\n      formSubtitle: 'Utilize o link enviado no seu e-mail',\n      formTitle: 'Link de verificação',\n      loading: {\n        title: 'Conectando...',\n      },\n      resendButton: 'Reenviar link',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Verifique seu e-mail',\n      verified: {\n        title: 'Cadastro realizado com sucesso',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Retorne para a nova aba que foi aberta para continuar',\n        subtitleNewTab: 'Retorne para a aba anterior para continuar',\n        title: 'E-mail verificado com sucesso',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Eu concordo com a {{privacyPolicyLink || link(\"Política de Privacidade\")}}',\n        label__onlyTermsOfService: 'Eu concordo com os {{termsOfServiceLink || link(\"Termos de Uso\")}}',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Eu concordo com os {{termsOfServiceLink || link(\"Termos de Uso\")}} e com a {{ privacyPolicyLink || link(\"Política de Privacidade\") }}',\n      },\n      continue: {\n        subtitle: 'Por favor leia e aceite os termos para continuar',\n        title: 'Continuar',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Insira o código enviado para seu telefone',\n      formTitle: 'Código de verificação',\n      resendButton: 'Não recebeu o código? Reenviar',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Verifique seu telefone',\n    },\n    restrictedAccess: {\n      actionLink: 'Entrar',\n      actionText: 'Já possui uma conta?',\n      blockButton__emailSupport: 'Suporte por e-mail',\n      blockButton__joinWaitlist: 'Entre na lista de espera',\n      subtitle:\n        'Cadastros estão desabilitados no momento. Se você deveria ter acesso, por favor entre em contato com o suporte.',\n      subtitleWaitlist:\n        'Cadastros estão desabilitados no momento. Para ser um dos primeiros a saber quando lançaremos, entre na lista de espera.',\n      title: 'Acesso restrito',\n    },\n    start: {\n      actionLink: 'Entrar',\n      actionLink__use_email: 'Ou use e-mail',\n      actionLink__use_phone: 'Ou use telefone',\n      actionText: 'Possui uma conta?',\n      alternativePhoneCodeProvider: {\n        actionLink: 'Usar outro método',\n        label: '{{provider}} telefone',\n        subtitle: 'Insira seu número de telefone para receber um código de verificação em {{provider}}.',\n        title: 'Entrar no {{applicationName}} com {{provider}}',\n      },\n      subtitle: 'para continuar em {{applicationName}}',\n      subtitleCombined: 'para continuar em {{applicationName}}',\n      title: 'Criar sua conta',\n      titleCombined: 'Criar sua conta',\n    },\n  },\n  socialButtonsBlockButton: 'Continuar com {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: '{{email}} já é membro da organização.',\n    captcha_invalid:\n      'Não foi possível se inscrever devido a falhas nas validações de segurança. Por favor, atualize a página para tentar novamente ou entre em contato com o suporte para obter mais ajuda.',\n    captcha_unavailable:\n      'Não foi possível se inscrever devido à indisponibilidade do captcha. Por favor atualize a página para tentar novamente ou entre em contato com o suporte para obter mais ajuda.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: 'E-mail já está em uso. Por favor, tente outro.',\n    form_identifier_exists__phone_number: 'Telefone já está em uso. Por favor, tente outro.',\n    form_identifier_exists__username: 'Nome de usuário já está em uso. Por favor, tente outro.',\n    form_identifier_not_found: 'Não foi possível encontrar o usuário.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'O endereço de e-mail deve ser um endereço de e-mail válido.',\n    form_param_format_invalid__phone_number: 'Número de telefone precisa estar num formato internacional válido.',\n    form_param_max_length_exceeded__first_name: 'O primeiro nome não deve exceder 256 caracteres.',\n    form_param_max_length_exceeded__last_name: 'O sobrenome não deve exceder 256 caracteres.',\n    form_param_max_length_exceeded__name: 'O nome não deve exceder 256 caracteres.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: 'Senha incorreta.',\n    form_password_length_too_short: 'Sua senha é muito curta. Por favor, tente novamente.',\n    form_password_not_strong_enough: 'Sua senha não é forte o suficiente.',\n    form_password_pwned: 'Esta senha foi comprometida e não pode ser usada, por favor, tente outra senha.',\n    form_password_pwned__sign_in: 'Esta senha foi comprometida, por favor redefina sua senha.',\n    form_password_size_in_bytes_exceeded:\n      'Sua senha excedeu o número máximo de bytes permitidos, por favor, encurte-a ou remova alguns caracteres especiais.',\n    form_password_validation_failed: 'Senha incorreta',\n    form_username_invalid_character: 'Nome de usuário contém caracteres inválidos. Por favor, tente outro.',\n    form_username_invalid_length: 'Nome de usuário deve ter entre 3 e 256 caracteres.',\n    identification_deletion_failed: 'Você não pode excluir sua última identificação.',\n    not_allowed_access:\n      \"O endereço de e-mail ou número de telefone não é permitido para registro. Isso pode ser devido ao uso de '+', '=', '#' ou '.' no endereço de e-mail, o uso de um domínio associado a um serviço de e-mail temporário ou uma exclusão explícita.\",\n    organization_domain_blocked: 'Este é um provedor de domínio de e-mail bloqueado. Por favor, use um diferente.',\n    organization_domain_common: 'Este é um provedor de domínio de e-mail comum. Por favor, use um diferente.',\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded:\n      'Você chegou ao seu limite de membros da organização, incluindo convites pendentes.',\n    organization_minimum_permissions_needed:\n      'É necessário que haja pelo menos um membro da organização com as permissões mínimas necessárias.',\n    passkey_already_exists: 'Uma chave de acesso já está registrada neste dispositivo.',\n    passkey_not_supported: 'Chaves de acesso não são suportadas neste dispositivo.',\n    passkey_pa_not_supported: 'Registro precisa de chave de acesso mas dispositivo não a suporta.',\n    passkey_registration_cancelled: 'Registro de chave de acesso cancelado ou expirado.',\n    passkey_retrieval_cancelled: 'Verificação de chave de acesso cancelada ou expirada.',\n    passwordComplexity: {\n      maximumLength: 'menos de {{length}} caracteres',\n      minimumLength: '{{length}} ou mais caracteres',\n      requireLowercase: 'uma letra minúscula',\n      requireNumbers: 'um número',\n      requireSpecialCharacter: 'um caractere especial',\n      requireUppercase: 'uma letra maiúscula',\n      sentencePrefix: 'Sua senha deve conter',\n    },\n    phone_number_exists: 'Este número de telefone já está em uso. Por favor, tente outro.',\n    session_exists: 'Você já está conectado.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Sua senha funciona, mas poderia ser mais forte. Tente adicionar mais caracteres.',\n      goodPassword: 'Sua senha atende a todos os requisitos necessários.',\n      notEnough: 'Sua senha não é forte o suficiente.',\n      suggestions: {\n        allUppercase: 'Utilize apenas algumas letras maiúsculas, não todas.',\n        anotherWord: 'Adicione mais palavras que são menos comuns.',\n        associatedYears: 'Evite anos associados a você.',\n        capitalization: 'Utilize outras letras maiúsculas, além do que primeira.',\n        dates: 'Evite datas e anos associados a você.',\n        l33t: \"Evite substituições previsíveis de letras, como '@' por 'a'.\",\n        longerKeyboardPattern: 'Use padrões de teclado mais longos e mude a direção da digitação várias vezes.',\n        noNeed: 'Você pode criar senhas fortes sem usar símbolos, números ou letras maiúsculas.',\n        pwned: 'Se você usar esta senha em outro lugar, você deve mudá-la.',\n        recentYears: 'Evite anos recentes.',\n        repeated: 'Evite palavras e caracteres repetidos.',\n        reverseWords: 'Evite utilizar palavras comuns escritas de \"trás para frente\".',\n        sequences: 'Evite sequências comuns de caracteres.',\n        useWords: 'Use várias palavras, mas evite frases comuns.',\n      },\n      warnings: {\n        common: 'Esta é uma senha comumente usada.',\n        commonNames: 'Nomes e sobrenomes comuns são fáceis de adivinhar.',\n        dates: 'Datas são fáceis de adivinhar.',\n        extendedRepeat: 'Padrões de caracteres repetidos, como \"abcabcabc\" são fáceis de adivinhar.',\n        keyPattern: 'Padrões curtos de teclado são fáceis de adivinhar.',\n        namesByThemselves: 'Nomes ou sobrenomes são fáceis de adivinhar.',\n        pwned: 'Sua senha foi exposta por uma violação de dados na Internet.',\n        recentYears: 'Anos recentes são fáceis de adivinhar.',\n        sequences: 'Sequências comuns de caracteres, como \"abc\" são fáceis de adivinhar.',\n        similarToCommon: 'Esta é semelhante a uma senha comumente usada.',\n        simpleRepeat: 'Caracteres repetidos, como \"aaa\" são fáceis de adivinhar.',\n        straightRow: 'Letras que vêm em sequência teclado são fáceis de adivinhar.',\n        topHundred: 'Esta é uma senha usada frequentemente.',\n        topTen: 'Esta é uma senha muito usada.',\n        userInputs: 'Não deve haver nenhum dado pessoal ou relacionado à página.',\n        wordByItself: 'Palavras simples são fáceis de adivinhar.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Adicionar conta',\n    action__manageAccount: 'Gerenciar conta',\n    action__signOut: 'Sair',\n    action__signOutAll: 'Sair de todas as contas',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Copiado!',\n      actionLabel__copy: 'Copiar tudo',\n      actionLabel__download: 'Download .txt',\n      actionLabel__print: 'Imprimir',\n      infoText1: 'Códigos de backup serão ativados para esta conta.',\n      infoText2:\n        'Guarde-os em segurança e mantenha-os em sigilo. Você pode gerar novos códigos de backup se suspeitar que eles tenham sido comprometidos.',\n      subtitle__codelist: 'Guarde-os em segurança e mantenha-os em sigilo.',\n      successMessage:\n        'Códigos de backup foram ativados para esta conta. Você pode usar um deles para fazer login na sua conta caso perca o acesso ao seu dispositivo de autenticação. Cada código poderá ser utilizado apenas uma vez.',\n      successSubtitle:\n        'Você pode usar um deles para fazer login na sua conta caso perca o acesso ao seu dispositivo de autenticação.',\n      title: 'Adicionar código de backup para verificação',\n      title__codelist: 'Códigos de backup',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: 'Nenhum histórico de pagamento',\n        notFound: 'Pagamento não encontrado',\n        tableHeader__amount: 'Valor',\n        tableHeader__date: 'Data',\n        tableHeader__status: 'Status',\n      },\n      paymentSourcesSection: {\n        actionLabel__default: 'Tornar padrão',\n        actionLabel__remove: 'Remover',\n        add: 'Adicionar novo método de pagamento',\n        addSubtitle: 'Adicione um novo método de pagamento à sua conta.',\n        cancelButton: 'Cancelar',\n        formButtonPrimary__add: 'Adicionar Método de Pagamento',\n        formButtonPrimary__pay: 'Pagar {{amount}}',\n        payWithTestCardButton: 'Pagar com cartão de teste',\n        removeResource: {\n          messageLine1: '{{identifier}} será removido desta conta.',\n          messageLine2:\n            'Você não poderá mais usar esta forma de pagamento e quaisquer assinaturas recorrentes dependentes dela deixarão de funcionar.',\n          successMessage: '{{paymentSource}} foi removido da sua conta.',\n          title: 'Remover método de pagamento',\n        },\n        title: 'Métodos de pagamento',\n      },\n      start: {\n        headerTitle__payments: 'Pagamentos',\n        headerTitle__plans: 'Planos',\n        headerTitle__statements: 'Extratos',\n        headerTitle__subscriptions: 'Assinaturas',\n      },\n      statementsSection: {\n        empty: 'Nenhum extrato para exibir',\n        itemCaption__paidForPlan: 'Pago para plano {{plan}} {{period}}',\n        itemCaption__proratedCredit: 'Crédito proporcional para uso parcial do plano anterior',\n        itemCaption__subscribedAndPaidForPlan: 'Assinado e pago para plano {{plan}} {{period}}',\n        notFound: 'Extrato não encontrado',\n        tableHeader__amount: 'Valor',\n        tableHeader__date: 'Data',\n        title: 'Extratos',\n        totalPaid: 'Total pago',\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: 'Assinar um plano',\n        actionLabel__switchPlan: 'Mudar de plano',\n        tableHeader__edit: 'Editar',\n        tableHeader__plan: 'Plano',\n        tableHeader__startDate: 'Data de início',\n        title: 'Assinatura',\n      },\n      subscriptionsSection: {\n        actionLabel__default: 'Gerenciar',\n      },\n      switchPlansSection: {\n        title: 'Mudar de plano',\n      },\n      title: 'Faturamento',\n    },\n    connectedAccountPage: {\n      formHint: 'Selecione um provedor para conectar à sua conta.',\n      formHint__noAccounts: 'Não há provedores de conta externos disponíveis.',\n      removeResource: {\n        messageLine1: '{{identifier}} será removido desta conta.',\n        messageLine2:\n          'Você não conseguirá mais usar esta conta e quaisquer recursos dependentes dela deixarão de funcionar.',\n        successMessage: '{{connectedAccount}} foi removido da sua conta.',\n        title: 'Remover conta conectada',\n      },\n      socialButtonsBlockButton: 'Conectar conta {{provider|titleize}}',\n      successMessage: 'O provedor foi adicionado à sua conta',\n      title: 'Conecte uma conta',\n    },\n    deletePage: {\n      actionDescription: 'Digite Excluir conta abaixo para continuar.',\n      confirm: 'Excluir conta',\n      messageLine1: 'Tem certeza de que deseja excluir sua conta?',\n      messageLine2: 'Esta ação é permanente e irreversível.',\n      title: 'Excluir conta',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Um e-mail contendo um código de verificação será enviado para este endereço de e-mail.',\n        formSubtitle: 'Insira o código de verificação enviado para {{identifier}}',\n        formTitle: 'Código de verificação',\n        resendButton: 'Não recebeu um código? Reenviar',\n        successMessage: 'O e-mail {{identifier}} foi adicionado na sua conta.',\n      },\n      emailLink: {\n        formHint: 'Um e-mail contendo um link de verificação será enviado para este endereço de e-mail.',\n        formSubtitle: 'Clique no link de verificação enviado para {{identifier}}',\n        formTitle: 'Link de verificação',\n        resendButton: 'Não recebeu um código? Reenviar',\n        successMessage: 'O e-mail {{identifier}} foi adicionado na sua conta.',\n      },\n      enterpriseSSOLink: {\n        formButton: 'Clique para autenticar',\n        formSubtitle: 'Complete a autenticação com {{identifier}}',\n      },\n      formHint: 'Você precisará verificar este endereço de email antes de poder adicioná-lo à sua conta.',\n      removeResource: {\n        messageLine1: '{{identifier}} será removido desta conta.',\n        messageLine2: 'Você não conseguirá fazer login novamente com este endereço de e-mail.',\n        successMessage: '{{emailAddress}} foi removido da sua conta.',\n        title: 'Remover e-mail',\n      },\n      title: 'Adicionar e-mail',\n      verifyTitle: 'Verificar endereço de e-mail',\n    },\n    formButtonPrimary__add: 'Add',\n    formButtonPrimary__continue: 'Continuar',\n    formButtonPrimary__finish: 'Finalizar',\n    formButtonPrimary__remove: 'Excluir',\n    formButtonPrimary__save: 'Salvar',\n    formButtonReset: 'Cancelar',\n    mfaPage: {\n      formHint: 'Selecione um método para adicionar.',\n      title: 'Adicione verificação em duas etapas',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Usar número existente',\n      primaryButton__addPhoneNumber: 'Adicione um número de telefone',\n      removeResource: {\n        messageLine1: '{{identifier}} não receberá mais códigos de verificação ao realizar o login.',\n        messageLine2: 'Sua conta pode ficar menos segura. Tem certeza que deseja continuar?',\n        successMessage: 'Código SMS de verificação em duas etapas foi removido para {{mfaPhoneCode}}',\n        title: 'Remover verificação em duas etapas',\n      },\n      subtitle__availablePhoneNumbers:\n        'Selecione um número de telefone para registrar a verificação em duas etapas por código SMS.',\n      subtitle__unavailablePhoneNumbers:\n        'Não há números de telefone disponíveis para registrar a verificação em duas etapas por código SMS.',\n      successMessage1:\n        'Ao acessar, será necessário o passo adicional de digitar o código de verificação enviado a este telefone.',\n      successMessage2:\n        'Salve estes códigos de backup e os armazene em um lugar seguro. Se você perder acesso ao seu dispositivo de autenticação, você pode utilizá-los para acessar o sistema.',\n      successTitle: 'Verificação por SMS habilitada',\n      title: 'Adicionar verificação por SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Escanear código QR em vez disso',\n        buttonUnableToScan__nonPrimary: 'Não pode escanear o código QR?',\n        infoText__ableToScan:\n          'Configure um novo método de login no seu aplicativo autenticador e escaneie o seguinte código QR para vinculá-lo à sua conta.',\n        infoText__unableToScan:\n          'Configure um novo método de login no seu aplicativo autenticador e insira a chave informada abaixo.',\n        inputLabel__unableToScan1:\n          \"Certifique-se de que o 'One-time passwords' está habilitado, em seguida, conclua a vinculação de sua conta.\",\n        inputLabel__unableToScan2:\n          'Alternativamente, se seu autenticador suportar URIs TOTP, você também pode copiar a URI completa.',\n      },\n      removeResource: {\n        messageLine1:\n          'Os códigos de verificação deste aplicativo autenticador não serão mais necessários ao fazer login.',\n        messageLine2: 'Sua conta pode ficar menos segura. Tem certeza que deseja continuar?',\n        successMessage: 'A verificação em duas etapas via aplicativo autenticador foi removida.',\n        title: 'Remover verificação em duas etapas',\n      },\n      successMessage:\n        'A verificação em duas etapas está ativa agora. Ao fazer login, você precisará inserir um código de verificação deste aplicativo autenticador como uma etapa adicional.',\n      title: 'Adicionar um aplicativo autenticador',\n      verifySubtitle: 'Insira o código de verificação gerado pelo seu aplicativo autenticador',\n      verifyTitle: 'Código de verificação',\n    },\n    mobileButton__menu: 'Menu',\n    navbar: {\n      account: 'Perfil',\n      apiKeys: undefined,\n      billing: 'Faturamento',\n      description: 'Gerencie seus dados de perfil.',\n      security: 'Segurança',\n      title: 'Conta',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} será removido desta conta.',\n        title: 'Remover chave de acesso',\n      },\n      subtitle__rename: 'Você pode renomear a chave de acesso para que seja mais fácil encontrá-la.',\n      title__rename: 'Renomear chave de acesso',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'É recomendado sair de todos os demais dispositivos que podem ter utilizado sua senha antiga.',\n      readonly:\n        'Sua senha atualmente não pode ser editada porque você só pode fazer login por meio da conexão da empresa.',\n      successMessage__set: 'Sua senha foi salva.',\n      successMessage__signOutOfOtherSessions: 'Todos os outros dispositivos foram desconectados.',\n      successMessage__update: 'Sua senha foi atualizada.',\n      title__set: 'Defina a senha',\n      title__update: 'Trocar senha',\n    },\n    phoneNumberPage: {\n      infoText: 'Um SMS contendo um link de verificação será enviado para este telefone.',\n      removeResource: {\n        messageLine1: '{{identifier}} será removido desta conta.',\n        messageLine2: 'Você não conseguirá fazer login novamente utilizando este número de telefone.',\n        successMessage: '{{phoneNumber}} foi removido da sua conta.',\n        title: 'Remover telefone',\n      },\n      successMessage: '{{identifier}} foi adicionado na sua conta.',\n      title: 'Adicionar telefone',\n      verifySubtitle: 'Insira o código de verificação enviado para {{identifier}}',\n      verifyTitle: 'Verificar número de telefone',\n    },\n    plansPage: {\n      title: 'Planos',\n    },\n    profilePage: {\n      fileDropAreaHint: 'Carregue uma imagem JPG, PNG, GIF ou WEBP menor que 10 MB',\n      imageFormDestructiveActionSubtitle: 'Remover imagem',\n      imageFormSubtitle: 'Carregar imagem',\n      imageFormTitle: 'Imagem do perfil',\n      readonly: 'As informações do seu perfil foram fornecidas pela conexão corporativa e não podem ser editadas.',\n      successMessage: 'Seu perfil foi atualizado.',\n      title: 'Atualizar perfil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Sair do dispositivo',\n        title: 'Dispositivos ativos',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Tentar novamente',\n        actionLabel__reauthorize: 'Reautorizar agora',\n        destructiveActionTitle: 'Remover',\n        primaryButton: 'Conectar conta',\n        subtitle__disconnected: 'Esta conta foi desconectada',\n        subtitle__reauthorize:\n          'Os escopos necessários foram atualizados, e você pode estar experimentado funcionalidades limitadas. Por favor, reautorize esta aplicação para evitar outros problemas',\n        title: 'Contas conectadas',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Excluir Conta',\n        title: 'Perigo',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Remover e-mail',\n        detailsAction__nonPrimary: 'Definir como principal',\n        detailsAction__primary: 'Completar verificação',\n        detailsAction__unverified: 'Completar verificação',\n        primaryButton: 'Adicionar um e-mail',\n        title: 'Endereços de e-mail',\n      },\n      enterpriseAccountsSection: {\n        title: 'Contas corporativas',\n      },\n      headerTitle__account: 'Conta',\n      headerTitle__security: 'Segurança',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Gerar códigos novamente',\n          headerTitle: 'Códigos de backup',\n          subtitle__regenerate:\n            'Obtenha um novo conjunto de códigos de backup seguros. Os códigos de backup anteriores serão excluídos e não poderão ser usados.',\n          title__regenerate: 'Gerar códigos de backup novamente',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Definir como principal',\n          destructiveActionLabel: 'Remover telefone',\n        },\n        primaryButton: 'Adicione verificação',\n        title: 'Verificação em duas etapas',\n        totp: {\n          destructiveActionTitle: 'Remover',\n          headerTitle: 'Aplicativo autenticador',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Remover',\n        menuAction__rename: 'Renomear',\n        primaryButton: undefined,\n        title: 'Chaves de acesso',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Defina a senha',\n        primaryButton__updatePassword: 'Trocar a senha',\n        title: 'Senha',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Remover telefone',\n        detailsAction__nonPrimary: 'Definir como principal',\n        detailsAction__primary: 'Completar verificação',\n        detailsAction__unverified: 'Completar verificação',\n        primaryButton: 'Adicione um telefone',\n        title: 'Números de telefone',\n      },\n      profileSection: {\n        primaryButton: 'Atualizar perfil',\n        title: 'Perfil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Definir nome de usuário',\n        primaryButton__updateUsername: 'Trocar nome de usuário',\n        title: 'Nome de usuário',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Remover carteira',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Carteiras Web3',\n        title: 'Carteiras Web3',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Seu nome de usuário foi atualizado.',\n      title__set: 'Atualizar nome de usuário',\n      title__update: 'Atualizar nome de usuário',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} será removido desta conta.',\n        messageLine2: 'Você não poderá mais usar esta carteira Web3 para entrar na sua conta.',\n        successMessage: '{{Web3Wallet}} foi removido da sua conta.',\n        title: 'Remover carteira Web3',\n      },\n      subtitle__availableWallets: 'Selecione uma carteira Web3 para conectar à sua conta.',\n      subtitle__unavailableWallets: 'Não há carteiras Web3 disponíveis.',\n      successMessage: 'A carteira foi adicionada à sua conta.',\n      title: 'Adicionar carteira Web3',\n      web3WalletButtonsBlockButton: 'Conectar carteira Web3',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: 'Entrar',\n      actionText: 'Já possui acesso?',\n      formButton: 'Entrar na lista de espera',\n      subtitle: 'Entre com seu e-mail e entraremos em contato quando seu lugar estiver disponível',\n      title: 'Entre na lista de espera',\n    },\n    success: {\n      message: 'Te redirecionando em breve...',\n      subtitle: 'Entraremos em contato quando seu lugar estiver disponível',\n      title: 'Obrigado por entrar na lista de espera!',\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BACE;AAAA,IACF,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBACE;AAAA,IACF,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBACE;AAAA,MACF,WAAW;AAAA,QACT,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBACE;AAAA,EACF,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cACE;AAAA,UACF,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UACE;AAAA,MACF,kBACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCACE;AAAA,IACF,yCACE;AAAA,IACF,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cACE;AAAA,UACF,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cACE;AAAA,QACF,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}