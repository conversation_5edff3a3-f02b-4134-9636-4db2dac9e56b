// src/cs-CZ.ts
var csCZ = {
  locale: "cs-CZ",
  apiKeys: {
    action__add: "P\u0159idat nov\xFD kl\xED\u010D",
    action__search: "Vyhledat kl\xED\u010De",
    createdAndExpirationStatus__expiresOn: "Vytvo\u0159eno {{ createdDate | shortDate('cs-CZ') }} \u2022 Plat\xED do {{ expiresDate | longDate('cs-CZ') }}",
    createdAndExpirationStatus__never: "Vytvo\u0159eno {{ createdDate | shortDate('cs-CZ') }} \u2022 Nikdy nevypr\u0161\xED",
    detailsTitle__emptyRow: "Nenalezeny \u017E\xE1dn\xE9 API kl\xED\u010De",
    formButtonPrimary__add: "Vytvo\u0159it kl\xED\u010D",
    formFieldCaption__expiration__expiresOn: "Vypr\u0161\xED {{ date }}",
    formFieldCaption__expiration__never: "Tento kl\xED\u010D nikdy nevypr\u0161\xED",
    formFieldOption__expiration__180d: "180 dn\xED",
    formFieldOption__expiration__1d: "1 den",
    formFieldOption__expiration__1y: "1 rok",
    formFieldOption__expiration__30d: "30 dn\xED",
    formFieldOption__expiration__60d: "60 dn\xED",
    formFieldOption__expiration__7d: "7 dn\xED",
    formFieldOption__expiration__90d: "90 dn\xED",
    formFieldOption__expiration__never: "Nikdy",
    formHint: "Zadejte n\xE1zev pro vygenerov\xE1n\xED nov\xE9ho kl\xED\u010De. Budete ho moci kdykoli zru\u0161it.",
    formTitle: "P\u0159idat nov\xFD API kl\xED\u010D",
    lastUsed__days: "P\u0159ed {{days}} dny",
    lastUsed__hours: "P\u0159ed {{hours}} hodinami",
    lastUsed__minutes: "P\u0159ed {{minutes}} minutami",
    lastUsed__months: "P\u0159ed {{months}} m\u011Bs\xEDci",
    lastUsed__seconds: "P\u0159ed {{seconds}} sekundami",
    lastUsed__years: "P\u0159ed {{years}} lety",
    menuAction__revoke: "Zru\u0161it kl\xED\u010D",
    revokeConfirmation: {
      confirmationText: "Zru\u0161it",
      formButtonPrimary__revoke: "Zru\u0161it kl\xED\u010D",
      formHint: "Jste si jisti, \u017Ee chcete smazat tento tajn\xFD kl\xED\u010D?",
      formTitle: 'Zru\u0161it tajn\xFD kl\xED\u010D "{{apiKeyName}}"?'
    }
  },
  backButton: "Zp\u011Bt",
  badge__activePlan: "Aktivn\xED",
  badge__canceledEndsAt: "Zru\u0161eno \u2022 Kon\u010D\xED {{ date | shortDate('cs-CZ') }}",
  badge__currentPlan: "Aktu\xE1ln\xED pl\xE1n",
  badge__default: "V\xFDchoz\xED",
  badge__endsAt: "Kon\u010D\xED {{ date | shortDate('cs-CZ') }}",
  badge__expired: "Vypr\u0161elo",
  badge__otherImpersonatorDevice: "Jin\xE9 za\u0159\xEDzen\xED pro simulaci",
  badge__primary: "Hlavn\xED",
  badge__renewsAt: "Obnovuje se {{ date | shortDate('cs-CZ') }}",
  badge__requiresAction: "Vy\u017Eaduje akci",
  badge__startsAt: "Za\u010D\xEDn\xE1 {{ date | shortDate('cs-CZ') }}",
  badge__pastDueAt: "Po splatnosti {{ date | shortDate('cs-CZ') }}",
  badge__thisDevice: "Toto za\u0159\xEDzen\xED",
  badge__unverified: "Nepotvrzen\xE9",
  badge__upcomingPlan: "Nadch\xE1zej\xEDc\xED",
  badge__pastDuePlan: "Po splatnosti",
  badge__userDevice: "Za\u0159\xEDzen\xED u\u017Eivatele",
  badge__you: "Vy",
  commerce: {
    addPaymentMethod: "P\u0159idat platebn\xED metodu",
    alwaysFree: "V\u017Edy zdarma",
    annually: "Ro\u010Dn\u011B",
    availableFeatures: "Dostupn\xE9 funkce",
    billedAnnually: "Fakturov\xE1no ro\u010Dn\u011B",
    billedMonthlyOnly: "Fakturov\xE1no pouze m\u011Bs\xED\u010Dn\u011B",
    cancelSubscription: "Zru\u0161it p\u0159edplatn\xE9",
    cancelSubscriptionAccessUntil: "Funkce '{{plan}}' m\u016F\u017Eete pou\u017E\xEDvat do {{ date | longDate('cs-CZ') }}, pot\xE9 k nim ztrat\xEDte p\u0159\xEDstup.",
    cancelSubscriptionNoCharge: "Za toto p\u0159edplatn\xE9 v\xE1m nebudou \xFA\u010Dtov\xE1ny \u017E\xE1dn\xE9 poplatky.",
    cancelSubscriptionTitle: "Zru\u0161it p\u0159edplatn\xE9 {{plan}}?",
    cannotSubscribeMonthly: "Nelze se p\u0159ihl\xE1sit k tomuto pl\xE1nu s m\u011Bs\xED\u010Dn\xED platbou. Abyste se k n\u011Bmu p\u0159ihl\xE1sili, mus\xEDte zvolit ro\u010Dn\xED platbu.",
    checkout: {
      description__paymentSuccessful: "Va\u0161e platba byla \xFAsp\u011B\u0161n\xE1.",
      description__subscriptionSuccessful: "Va\u0161e nov\xE9 p\u0159edplatn\xE9 je nastaveno.",
      downgradeNotice: "Sou\u010Dasn\xE9 p\u0159edplatn\xE9 a jeho funkce si ponech\xE1te do konce faktura\u010Dn\xEDho cyklu, pot\xE9 budete p\u0159evedeni na toto p\u0159edplatn\xE9.",
      emailForm: {
        subtitle: "Ne\u017E budete moci dokon\u010Dit n\xE1kup, mus\xEDte p\u0159idat e-mailovou adresu, na kterou budou zas\xEDl\xE1ny \xFA\u010Dtenky.",
        title: "P\u0159idat e-mailovou adresu"
      },
      lineItems: {
        title__paymentMethod: "Platebn\xED metoda",
        title__statementId: "ID v\xFDpisu",
        title__subscriptionBegins: "P\u0159edplatn\xE9 za\u010D\xEDn\xE1",
        title__totalPaid: "Celkem zaplaceno"
      },
      pastDueNotice: "Va\u0161e p\u0159edchoz\xED p\u0159edplatn\xE9 bylo po splatnosti, bez platby.",
      perMonth: "m\u011Bs\xED\u010Dn\u011B",
      title: "Pokladna",
      title__paymentSuccessful: "Platba byla \xFAsp\u011B\u0161n\xE1!",
      title__subscriptionSuccessful: "\xDAsp\u011Bch!"
    },
    credit: "Kredit",
    creditRemainder: "Kredit za zbytek va\u0161eho sou\u010Dasn\xE9ho p\u0159edplatn\xE9ho.",
    defaultFreePlanActive: "Aktu\xE1ln\u011B pou\u017E\xEDv\xE1te bezplatn\xFD pl\xE1n",
    free: "Zdarma",
    getStarted: "Za\u010D\xEDt",
    keepSubscription: "Ponechat p\u0159edplatn\xE9",
    manage: "Spravovat",
    manageSubscription: "Spravovat p\u0159edplatn\xE9",
    month: "M\u011Bs\xEDc",
    monthly: "M\u011Bs\xED\u010Dn\u011B",
    pastDue: "Po splatnosti",
    pay: "Zaplatit {{amount}}",
    paymentMethods: "Platebn\xED metody",
    paymentSource: {
      applePayDescription: {
        annual: "Ro\u010Dn\xED platba",
        monthly: "M\u011Bs\xED\u010Dn\xED platba"
      },
      dev: {
        anyNumbers: "Jak\xE1koli \u010D\xEDsla",
        cardNumber: "\u010C\xEDslo karty",
        cvcZip: "CVC, PS\u010C",
        developmentMode: "V\xFDvojov\xFD re\u017Eim",
        expirationDate: "Datum platnosti",
        testCardInfo: "Informace o testovac\xED kart\u011B"
      }
    },
    popular: "Popul\xE1rn\xED",
    pricingTable: {
      billingCycle: "Faktura\u010Dn\xED cyklus",
      included: "Zahrnuto"
    },
    subscriptionDetails: {
      title: "P\u0159edplatn\xE9",
      currentBillingCycle: "Aktu\xE1ln\xED faktura\u010Dn\xED cyklus",
      nextPaymentOn: "Dal\u0161\xED platba dne",
      nextPaymentAmount: "V\xFD\u0161e dal\u0161\xED platby",
      subscribedOn: "P\u0159edplaceno dne",
      endsOn: "Kon\u010D\xED dne",
      renewsAt: "Obnovuje se dne",
      beginsOn: "Za\u010D\xEDn\xE1 dne",
      pastDueAt: "Po splatnosti dne"
    },
    reSubscribe: "Znovu se p\u0159ihl\xE1sit",
    seeAllFeatures: "Zobrazit v\u0161echny funkce",
    subscribe: "P\u0159ihl\xE1sit se",
    subtotal: "Mezisou\u010Det",
    switchPlan: "P\u0159epnout na tento pl\xE1n",
    switchToAnnual: "P\u0159epnout na ro\u010Dn\xED",
    switchToMonthly: "P\u0159epnout na m\u011Bs\xED\u010Dn\xED",
    switchToMonthlyWithPrice: "P\u0159epnout na m\u011Bs\xED\u010Dn\xED {{currency}}{{price}} / m\u011Bs\xEDc",
    switchToAnnualWithAnnualPrice: "P\u0159epnout na ro\u010Dn\xED {{currency}}{{price}} / rok",
    totalDue: "Celkem k zaplacen\xED",
    totalDueToday: "Celkem k zaplacen\xED dnes",
    viewFeatures: "Zobrazit funkce",
    year: "Rok"
  },
  createOrganization: {
    formButtonSubmit: "Vytvo\u0159it organizaci",
    invitePage: {
      formButtonReset: "P\u0159esko\u010Dit"
    },
    title: "Vytvo\u0159it organizaci"
  },
  dates: {
    lastDay: "V\u010Dera v {{ date | timeString('cs-CZ') }}",
    next6Days: "P\u0159\xED\u0161t\xED {{ date | weekday('cs-CZ','long') }} v {{ date | timeString('cs-CZ') }}",
    nextDay: "Z\xEDtra v {{ date | timeString('cs-CZ') }}",
    numeric: "{{ date | numeric('cs-CZ') }}",
    previous6Days: "Minul\xFD {{ date | weekday('cs-CZ','long') }} v {{ date | timeString('cs-CZ') }}",
    sameDay: "Dnes v {{ date | timeString('cs-CZ') }}"
  },
  dividerText: "nebo",
  footerActionLink__alternativePhoneCodeProvider: "M\xEDsto toho poslat k\xF3d p\u0159es SMS",
  footerActionLink__useAnotherMethod: "Pou\u017E\xEDt jinou metodu",
  footerPageLink__help: "N\xE1pov\u011Bda",
  footerPageLink__privacy: "Ochrana soukrom\xED",
  footerPageLink__terms: "Podm\xEDnky",
  formButtonPrimary: "Pokra\u010Dovat",
  formButtonPrimary__verify: "Ov\u011B\u0159it",
  formFieldAction__forgotPassword: "Zapomn\u011Bli jste heslo?",
  formFieldError__matchingPasswords: "Hesla se shoduj\xED.",
  formFieldError__notMatchingPasswords: "Hesla se neshoduj\xED.",
  formFieldError__verificationLinkExpired: "Ov\u011B\u0159ovac\xED odkaz vypr\u0161el. Pros\xEDm, po\u017E\xE1dejte o nov\xFD odkaz.",
  formFieldHintText__optional: "Voliteln\xE9",
  formFieldHintText__slug: "Slug je \u010Dlov\u011Bkem \u010Diteln\xFD identifik\xE1tor, kter\xFD mus\xED b\xFDt unik\xE1tn\xED. \u010Casto se pou\u017E\xEDv\xE1 v URL adres\xE1ch.",
  formFieldInputPlaceholder__apiKeyDescription: "Vysv\u011Btlete, pro\u010D generujete tento kl\xED\u010D",
  formFieldInputPlaceholder__apiKeyExpirationDate: "Vyberte datum",
  formFieldInputPlaceholder__apiKeyName: "Zadejte n\xE1zev tajn\xE9ho kl\xED\u010De",
  formFieldInputPlaceholder__backupCode: "Zadejte z\xE1lo\u017En\xED k\xF3d",
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Smazat \xFA\u010Det",
  formFieldInputPlaceholder__emailAddress: "Zadejte svou e-mailovou adresu",
  formFieldInputPlaceholder__emailAddress_username: "Zadejte e-mail nebo u\u017Eivatelsk\xE9 jm\xE9no",
  formFieldInputPlaceholder__emailAddresses: "<EMAIL>, <EMAIL>",
  formFieldInputPlaceholder__firstName: "K\u0159estn\xED jm\xE9no",
  formFieldInputPlaceholder__lastName: "P\u0159\xEDjmen\xED",
  formFieldInputPlaceholder__organizationDomain: "example.com",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "<EMAIL>",
  formFieldInputPlaceholder__organizationName: "N\xE1zev organizace",
  formFieldInputPlaceholder__organizationSlug: "moje-org",
  formFieldInputPlaceholder__password: "Zadejte sv\xE9 heslo",
  formFieldInputPlaceholder__phoneNumber: "Zadejte sv\xE9 telefonn\xED \u010D\xEDslo",
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__apiKeyDescription: "Popis",
  formFieldLabel__apiKeyExpiration: "Platnost",
  formFieldLabel__apiKeyName: "N\xE1zev tajn\xE9ho kl\xED\u010De",
  formFieldLabel__automaticInvitations: "Povolit automatick\xE9 pozv\xE1nky pro tuto dom\xE9nu",
  formFieldLabel__backupCode: "Z\xE1lo\u017En\xED k\xF3d",
  formFieldLabel__confirmDeletion: "Potvrzen\xED",
  formFieldLabel__confirmPassword: "Potvrdit heslo",
  formFieldLabel__currentPassword: "Aktu\xE1ln\xED heslo",
  formFieldLabel__emailAddress: "E-mailov\xE1 adresa",
  formFieldLabel__emailAddress_username: "E-mailov\xE1 adresa nebo u\u017Eivatelsk\xE9 jm\xE9no",
  formFieldLabel__emailAddresses: "E-mailov\xE9 adresy",
  formFieldLabel__firstName: "K\u0159estn\xED jm\xE9no",
  formFieldLabel__lastName: "P\u0159\xEDjmen\xED",
  formFieldLabel__newPassword: "Nov\xE9 heslo",
  formFieldLabel__organizationDomain: "Dom\xE9na",
  formFieldLabel__organizationDomainDeletePending: "Smazat \u010Dekaj\xEDc\xED pozv\xE1nky a n\xE1vrhy",
  formFieldLabel__organizationDomainEmailAddress: "Ov\u011B\u0159ovac\xED e-mailov\xE1 adresa",
  formFieldLabel__organizationDomainEmailAddressDescription: "Zadejte e-mailovou adresu z t\xE9to dom\xE9ny k obdr\u017Een\xED k\xF3du a ov\u011B\u0159en\xED dom\xE9ny.",
  formFieldLabel__organizationName: "N\xE1zev",
  formFieldLabel__organizationSlug: "Slug",
  formFieldLabel__passkeyName: "N\xE1zev p\u0159\xEDstupov\xE9ho kl\xED\u010De",
  formFieldLabel__password: "Heslo",
  formFieldLabel__phoneNumber: "Telefonn\xED \u010D\xEDslo",
  formFieldLabel__role: "Role",
  formFieldLabel__signOutOfOtherSessions: "Odhl\xE1sit se ze v\u0161ech ostatn\xEDch za\u0159\xEDzen\xED",
  formFieldLabel__username: "U\u017Eivatelsk\xE9 jm\xE9no",
  impersonationFab: {
    action__signOut: "Odhl\xE1sit se",
    title: "P\u0159ihl\xE1\u0161en jako {{identifier}}"
  },
  maintenanceMode: "Moment\xE1ln\u011B prov\xE1d\xEDme \xFAdr\u017Ebu, ale nebojte se, nem\u011Blo by to trvat d\xE9le ne\u017E p\xE1r minut.",
  membershipRole__admin: "Spr\xE1vce",
  membershipRole__basicMember: "\u010Clen",
  membershipRole__guestMember: "Host",
  organizationList: {
    action__createOrganization: "Vytvo\u0159it organizaci",
    action__invitationAccept: "P\u0159ipojit se",
    action__suggestionsAccept: "Po\u017E\xE1dat o p\u0159ipojen\xED",
    createOrganization: "Vytvo\u0159it organizaci",
    invitationAcceptedLabel: "P\u0159ipojeno",
    subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
    suggestionsAcceptedLabel: "\u010Cek\xE1 na schv\xE1len\xED",
    title: "Vyberte \xFA\u010Det",
    titleWithoutPersonal: "Vyberte organizaci"
  },
  organizationProfile: {
    apiKeysPage: {
      title: "API kl\xED\u010De"
    },
    badge__automaticInvitation: "Automatick\xE9 pozv\xE1nky",
    badge__automaticSuggestion: "Automatick\xE9 n\xE1vrhy",
    badge__manualInvitation: "\u017D\xE1dn\xE9 automatick\xE9 p\u0159ihl\xE1\u0161en\xED",
    badge__unverified: "Nepotvrzen\xE9",
    billingPage: {
      paymentHistorySection: {
        empty: "\u017D\xE1dn\xE1 historie plateb",
        notFound: "Pokus o platbu nenalezen",
        tableHeader__amount: "\u010C\xE1stka",
        tableHeader__date: "Datum",
        tableHeader__status: "Stav"
      },
      paymentSourcesSection: {
        actionLabel__default: "Nastavit jako v\xFDchoz\xED",
        actionLabel__remove: "Odebrat",
        add: "P\u0159idat novou platebn\xED metodu",
        addSubtitle: "P\u0159idejte novou platebn\xED metodu k va\u0161emu \xFA\u010Dtu.",
        cancelButton: "Zru\u0161it",
        formButtonPrimary__add: "P\u0159idat platebn\xED metodu",
        formButtonPrimary__pay: "Zaplatit {{amount}}",
        payWithTestCardButton: "Zaplatit testovac\xED kartou",
        removeResource: {
          messageLine1: "{{identifier}} bude odstran\u011Bn z tohoto \xFA\u010Dtu.",
          messageLine2: "Tento platebn\xED zdroj ji\u017E nebudete moci pou\u017E\xEDvat a ve\u0161ker\xE1 opakuj\xEDc\xED se p\u0159edplatn\xE1, kter\xE1 na n\u011Bm z\xE1vis\xED, p\u0159estanou fungovat.",
          successMessage: "{{paymentSource}} byl odstran\u011Bn z va\u0161eho \xFA\u010Dtu.",
          title: "Odebrat platebn\xED metodu"
        },
        title: "Platebn\xED metody"
      },
      start: {
        headerTitle__payments: "Platby",
        headerTitle__plans: "Pl\xE1ny",
        headerTitle__statements: "V\xFDpisy",
        headerTitle__subscriptions: "P\u0159edplatn\xE9"
      },
      statementsSection: {
        empty: "\u017D\xE1dn\xE9 v\xFDpisy k zobrazen\xED",
        itemCaption__paidForPlan: "Zaplaceno za pl\xE1n {{plan}} {{period}}",
        itemCaption__proratedCredit: "Pom\u011Brn\xFD kredit za \u010D\xE1ste\u010Dn\xE9 vyu\u017Eit\xED p\u0159edchoz\xEDho p\u0159edplatn\xE9ho",
        itemCaption__subscribedAndPaidForPlan: "P\u0159edplaceno a zaplaceno za pl\xE1n {{plan}} {{period}}",
        notFound: "V\xFDpis nenalezen",
        tableHeader__amount: "\u010C\xE1stka",
        tableHeader__date: "Datum",
        title: "V\xFDpisy",
        totalPaid: "Celkem zaplaceno"
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: "P\u0159ihl\xE1sit se k pl\xE1nu",
        actionLabel__switchPlan: "Zm\u011Bnit pl\xE1ny",
        tableHeader__edit: "Upravit",
        tableHeader__plan: "Pl\xE1n",
        tableHeader__startDate: "Datum zah\xE1jen\xED",
        title: "P\u0159edplatn\xE9"
      },
      subscriptionsSection: {
        actionLabel__default: "Spravovat"
      },
      switchPlansSection: {
        title: "Zm\u011Bnit pl\xE1ny"
      },
      title: "Fakturace"
    },
    createDomainPage: {
      subtitle: "P\u0159idejte dom\xE9nu k ov\u011B\u0159en\xED. U\u017Eivatel\xE9 s e-mailov\xFDmi adresami z t\xE9to dom\xE9ny se mohou p\u0159ipojit k organizaci automaticky nebo po\u017E\xE1dat o p\u0159ipojen\xED.",
      title: "P\u0159idat dom\xE9nu"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Pozv\xE1nky nebylo mo\u017En\xE9 odeslat. Ji\u017E existuj\xED \u010Dekaj\xEDc\xED pozv\xE1nky pro n\xE1sleduj\xEDc\xED e-mailov\xE9 adresy: {{email_addresses}}.",
      formButtonPrimary__continue: "Odeslat pozv\xE1nky",
      selectDropdown__role: "Vyberte roli",
      subtitle: "Zadejte nebo vlo\u017Ete jednu nebo v\xEDce e-mailov\xFDch adres, odd\u011Blen\xFDch mezerami nebo \u010D\xE1rkami.",
      successMessage: "Pozv\xE1nky \xFAsp\u011B\u0161n\u011B odesl\xE1ny",
      title: "Pozvat nov\xE9 \u010Dleny"
    },
    membersPage: {
      action__invite: "Pozvat",
      action__search: "Vyhledat",
      activeMembersTab: {
        menuAction__remove: "Odebrat \u010Dlena",
        tableHeader__actions: "Akce",
        tableHeader__joined: "P\u0159ipojeno",
        tableHeader__role: "Role",
        tableHeader__user: "U\u017Eivatel"
      },
      detailsTitle__emptyRow: "\u017D\xE1dn\xED \u010Dlenov\xE9 k zobrazen\xED",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Pozv\u011Bte u\u017Eivatele propojen\xEDm e-mailov\xE9 dom\xE9ny s va\u0161\xED organizac\xED. Kdokoli, kdo se zaregistruje s odpov\xEDdaj\xEDc\xED e-mailovou dom\xE9nou, se bude moci kdykoli p\u0159ipojit k organizaci.",
          headerTitle: "Automatick\xE9 pozv\xE1nky",
          primaryButton: "Spravovat ov\u011B\u0159en\xE9 dom\xE9ny"
        },
        table__emptyRow: "\u017D\xE1dn\xE9 pozv\xE1nky k zobrazen\xED"
      },
      invitedMembersTab: {
        menuAction__revoke: "Zru\u0161it pozv\xE1nku",
        tableHeader__invited: "Pozv\xE1ni"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "U\u017Eivatel\xE9, kte\u0159\xED se zaregistruj\xED s odpov\xEDdaj\xEDc\xED e-mailovou dom\xE9nou, uvid\xED n\xE1vrh na p\u0159ipojen\xED k va\u0161\xED organizaci.",
          headerTitle: "Automatick\xE9 n\xE1vrhy",
          primaryButton: "Spravovat ov\u011B\u0159en\xE9 dom\xE9ny"
        },
        menuAction__approve: "Schv\xE1lit",
        menuAction__reject: "Odm\xEDtnout",
        tableHeader__requested: "Po\u017E\xE1d\xE1n p\u0159\xEDstup",
        table__emptyRow: "\u017D\xE1dn\xE9 po\u017Eadavky k zobrazen\xED"
      },
      start: {
        headerTitle__invitations: "Pozv\xE1nky",
        headerTitle__members: "\u010Clenov\xE9",
        headerTitle__requests: "\u017D\xE1dosti"
      }
    },
    navbar: {
      apiKeys: "API kl\xED\u010De",
      billing: "Fakturace",
      description: "Spravujte svou organizaci.",
      general: "Obecn\xE9",
      members: "\u010Clenov\xE9",
      title: "Organizace"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: "Nem\xE1te opr\xE1vn\u011Bn\xED spravovat fakturaci pro tuto organizaci."
      },
      title: "Pl\xE1ny"
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Napi\u0161te "{{organizationName}}" n\xED\u017Ee pro pokra\u010Dov\xE1n\xED.',
          messageLine1: "Jste si jisti, \u017Ee chcete smazat tuto organizaci?",
          messageLine2: "Tato akce je trval\xE1 a nevratn\xE1.",
          successMessage: "Organizac\xED jste smazali.",
          title: "Smazat organizaci"
        },
        leaveOrganization: {
          actionDescription: 'Napi\u0161te "{{organizationName}}" n\xED\u017Ee pro pokra\u010Dov\xE1n\xED.',
          messageLine1: "Jste si jisti, \u017Ee chcete opustit tuto organizaci? Ztrat\xEDte p\u0159\xEDstup k t\xE9to organizaci a jej\xEDm aplikac\xEDm.",
          messageLine2: "Tato akce je trval\xE1 a nevratn\xE1.",
          successMessage: "Opustili jste organizaci.",
          title: "Opustit organizaci"
        },
        title: "Nebezpe\u010D\xED"
      },
      domainSection: {
        menuAction__manage: "Spravovat",
        menuAction__remove: "Smazat",
        menuAction__verify: "Ov\u011B\u0159it",
        primaryButton: "P\u0159idat dom\xE9nu",
        subtitle: "Umo\u017En\u011Bte u\u017Eivatel\u016Fm p\u0159ipojit se k organizaci automaticky nebo po\u017E\xE1dat o p\u0159ipojen\xED na z\xE1klad\u011B ov\u011B\u0159en\xE9 e-mailov\xE9 dom\xE9ny.",
        title: "Ov\u011B\u0159en\xE9 dom\xE9ny"
      },
      successMessage: "Organizace byla aktualizov\xE1na.",
      title: "Aktualizovat profil"
    },
    removeDomainPage: {
      messageLine1: "E-mailov\xE1 dom\xE9na {{domain}} bude odstran\u011Bna.",
      messageLine2: "U\u017Eivatel\xE9 se ji\u017E nebudou moci automaticky p\u0159ipojit k organizaci po tomto.",
      successMessage: "{{domain}} byl odstran\u011Bn.",
      title: "Odebrat dom\xE9nu"
    },
    start: {
      headerTitle__general: "Obecn\xE9",
      headerTitle__members: "\u010Clenov\xE9",
      profileSection: {
        primaryButton: "Aktualizovat profil",
        title: "Profil organizace",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Odstran\u011Bn\xED t\xE9to dom\xE9ny ovlivn\xED pozvan\xE9 u\u017Eivatele.",
        removeDomainActionLabel__remove: "Odebrat dom\xE9nu",
        removeDomainSubtitle: "Odebrat tuto dom\xE9nu z va\u0161ich ov\u011B\u0159en\xFDch dom\xE9n",
        removeDomainTitle: "Odebrat dom\xE9nu"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "U\u017Eivatel\xE9 jsou automaticky zv\xE1ni k p\u0159ipojen\xED k organizaci p\u0159i registraci a mohou se p\u0159ipojit kdykoli.",
        automaticInvitationOption__label: "Automatick\xE9 pozv\xE1nky",
        automaticSuggestionOption__description: "U\u017Eivatel\xE9 obdr\u017E\xED n\xE1vrh na p\u0159ipojen\xED, ale mus\xED b\xFDt schv\xE1leni administr\xE1torem, ne\u017E se budou moci p\u0159ipojit k organizaci.",
        automaticSuggestionOption__label: "Automatick\xE9 n\xE1vrhy",
        calloutInfoLabel: "Zm\u011Bna re\u017Eimu registrace ovlivn\xED pouze nov\xE9 u\u017Eivatele.",
        calloutInvitationCountLabel: "\u010Cekaj\xEDc\xED pozv\xE1nky odeslan\xE9 u\u017Eivatel\u016Fm: {{count}}",
        calloutSuggestionCountLabel: "\u010Cekaj\xEDc\xED n\xE1vrhy odeslan\xE9 u\u017Eivatel\u016Fm: {{count}}",
        manualInvitationOption__description: "U\u017Eivatel\xE9 mohou b\xFDt do organizace pozv\xE1ni pouze ru\u010Dn\u011B.",
        manualInvitationOption__label: "\u017D\xE1dn\xE9 automatick\xE9 p\u0159ihl\xE1\u0161en\xED",
        subtitle: "Vyberte, jak se u\u017Eivatel\xE9 z t\xE9to dom\xE9ny mohou p\u0159ipojit k organizaci."
      },
      start: {
        headerTitle__danger: "Nebezpe\u010D\xED",
        headerTitle__enrollment: "Mo\u017Enosti registrace"
      },
      subtitle: "Dom\xE9na {{domain}} je nyn\xED ov\u011B\u0159ena. Pokra\u010Dujte v\xFDb\u011Brem re\u017Eimu registrace.",
      title: "Aktualizovat {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d odeslan\xFD na va\u0161i e-mailovou adresu",
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Neobdr\u017Eeli jste k\xF3d? Znovu poslat",
      subtitle: "Dom\xE9na {{domainName}} mus\xED b\xFDt ov\u011B\u0159ena e-mailem.",
      subtitleVerificationCodeScreen: "Ov\u011B\u0159ovac\xED k\xF3d byl odesl\xE1n na {{emailAddress}}. Zadejte k\xF3d pro pokra\u010Dov\xE1n\xED.",
      title: "Ov\u011B\u0159it dom\xE9nu"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Vytvo\u0159it organizaci",
    action__invitationAccept: "P\u0159ipojit se",
    action__manageOrganization: "Spravovat organizaci",
    action__suggestionsAccept: "Po\u017E\xE1dat o p\u0159ipojen\xED",
    notSelected: "Nen\xED vybr\xE1na \u017E\xE1dn\xE1 organizace",
    personalWorkspace: "Osobn\xED \xFA\u010Det",
    suggestionsAcceptedLabel: "\u010Cek\xE1 na schv\xE1len\xED"
  },
  paginationButton__next: "Dal\u0161\xED",
  paginationButton__previous: "P\u0159edchoz\xED",
  paginationRowText__displaying: "Zobrazuji",
  paginationRowText__of: "z",
  reverification: {
    alternativeMethods: {
      actionLink: "Z\xEDskat n\xE1pov\u011Bdu",
      actionText: "Nem\xE1te \u017E\xE1dnou z t\u011Bchto mo\u017Enost\xED?",
      blockButton__backupCode: "Pou\u017E\xEDt z\xE1lo\u017En\xED k\xF3d",
      blockButton__emailCode: "Odeslat k\xF3d na e-mail {{identifier}}",
      blockButton__passkey: "Pou\u017E\xEDt v\xE1\u0161 p\u0159\xEDstupov\xFD kl\xED\u010D",
      blockButton__password: "Pokra\u010Dovat s va\u0161\xEDm heslem",
      blockButton__phoneCode: "Odeslat SMS k\xF3d na {{identifier}}",
      blockButton__totp: "Pou\u017E\xEDt va\u0161i aplikaci pro ov\u011B\u0159ov\xE1n\xED",
      getHelp: {
        blockButton__emailSupport: "Podpora p\u0159es e-mail",
        content: "Pokud m\xE1te pot\xED\u017Ee s ov\u011B\u0159en\xEDm va\u0161eho \xFA\u010Dtu, napi\u0161te n\xE1m e-mail a my s v\xE1mi budeme spolupracovat na co nejrychlej\u0161\xEDm obnoven\xED p\u0159\xEDstupu.",
        title: "Z\xEDskat n\xE1pov\u011Bdu"
      },
      subtitle: "M\xE1te pot\xED\u017Ee? M\u016F\u017Eete pou\u017E\xEDt kteroukoli z t\u011Bchto metod pro ov\u011B\u0159en\xED.",
      title: "Pou\u017E\xEDt jinou metodu"
    },
    backupCodeMfa: {
      subtitle: "Zadejte z\xE1lo\u017En\xED k\xF3d, kter\xFD jste obdr\u017Eeli p\u0159i nastaven\xED dvouf\xE1zov\xE9 autentizace",
      title: "Zadejte z\xE1lo\u017En\xED k\xF3d"
    },
    emailCode: {
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Neobdr\u017Eeli jste k\xF3d? Znovu poslat",
      subtitle: "Zadejte k\xF3d odeslan\xFD na v\xE1\u0161 e-mail pro pokra\u010Dov\xE1n\xED",
      title: "Vy\u017Eadov\xE1no ov\u011B\u0159en\xED"
    },
    noAvailableMethods: {
      message: "Nelze pokra\u010Dovat s ov\u011B\u0159en\xEDm. Nen\xED nakonfigurov\xE1n \u017E\xE1dn\xFD vhodn\xFD autentiza\u010Dn\xED faktor",
      subtitle: "Do\u0161lo k chyb\u011B",
      title: "Nelze ov\u011B\u0159it v\xE1\u0161 \xFA\u010Det"
    },
    passkey: {
      blockButton__passkey: "Pou\u017E\xEDt v\xE1\u0161 p\u0159\xEDstupov\xFD kl\xED\u010D",
      subtitle: "Pou\u017Eit\xED va\u0161eho p\u0159\xEDstupov\xE9ho kl\xED\u010De potvrzuje va\u0161i identitu. Va\u0161e za\u0159\xEDzen\xED m\u016F\u017Ee po\u017E\xE1dat o otisk prstu, obli\u010Dej nebo z\xE1mek obrazovky.",
      title: "Pou\u017E\xEDt v\xE1\u0161 p\u0159\xEDstupov\xFD kl\xED\u010D"
    },
    password: {
      actionLink: "Pou\u017E\xEDt jinou metodu",
      subtitle: "Zadejte sv\xE9 aktu\xE1ln\xED heslo pro pokra\u010Dov\xE1n\xED",
      title: "Vy\u017Eadov\xE1no ov\u011B\u0159en\xED"
    },
    phoneCode: {
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Neobdr\u017Eeli jste k\xF3d? Znovu poslat",
      subtitle: "Zadejte k\xF3d odeslan\xFD na v\xE1\u0161 telefon pro pokra\u010Dov\xE1n\xED",
      title: "Vy\u017Eadov\xE1no ov\u011B\u0159en\xED"
    },
    phoneCodeMfa: {
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Neobdr\u017Eeli jste k\xF3d? Znovu poslat",
      subtitle: "Zadejte k\xF3d odeslan\xFD na v\xE1\u0161 telefon pro pokra\u010Dov\xE1n\xED",
      title: "Vy\u017Eadov\xE1no ov\u011B\u0159en\xED"
    },
    totpMfa: {
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      subtitle: "Zadejte k\xF3d vygenerovan\xFD va\u0161\xED aplikac\xED pro ov\u011B\u0159ov\xE1n\xED pro pokra\u010Dov\xE1n\xED",
      title: "Vy\u017Eadov\xE1no ov\u011B\u0159en\xED"
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "P\u0159idat \xFA\u010Det",
      action__signOutAll: "Odhl\xE1sit se ze v\u0161ech \xFA\u010Dt\u016F",
      subtitle: "Vyberte \xFA\u010Det, se kter\xFDm chcete pokra\u010Dovat.",
      title: "Vyberte \xFA\u010Det"
    },
    alternativeMethods: {
      actionLink: "Z\xEDskat n\xE1pov\u011Bdu",
      actionText: "Nem\xE1te \u017E\xE1dnou z t\u011Bchto mo\u017Enost\xED?",
      blockButton__backupCode: "Pou\u017E\xEDt z\xE1lo\u017En\xED k\xF3d",
      blockButton__emailCode: "Odeslat e-mailov\xFD k\xF3d na {{identifier}}",
      blockButton__emailLink: "Odeslat e-mailov\xFD odkaz na {{identifier}}",
      blockButton__passkey: "P\u0159ihl\xE1sit se pomoc\xED va\u0161eho p\u0159\xEDstupov\xE9ho kl\xED\u010De",
      blockButton__password: "P\u0159ihl\xE1sit se pomoc\xED va\u0161eho hesla",
      blockButton__phoneCode: "Odeslat SMS k\xF3d na {{identifier}}",
      blockButton__totp: "Pou\u017E\xEDt va\u0161i aplikaci pro ov\u011B\u0159ov\xE1n\xED",
      getHelp: {
        blockButton__emailSupport: "Podpora p\u0159es e-mail",
        content: "Pokud m\xE1te pot\xED\u017Ee s p\u0159ihl\xE1\u0161en\xEDm do va\u0161eho \xFA\u010Dtu, napi\u0161te n\xE1m e-mail a my s v\xE1mi budeme spolupracovat na co nejrychlej\u0161\xEDm obnoven\xED p\u0159\xEDstupu.",
        title: "Z\xEDskat n\xE1pov\u011Bdu"
      },
      subtitle: "M\xE1te pot\xED\u017Ee? M\u016F\u017Eete pou\u017E\xEDt kteroukoli z t\u011Bchto metod k p\u0159ihl\xE1\u0161en\xED.",
      title: "Pou\u017E\xEDt jinou metodu"
    },
    alternativePhoneCodeProvider: {
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Neobdr\u017Eeli jste k\xF3d? Znovu poslat",
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Zkontrolujte sv\u016Fj {{provider}}"
    },
    backupCodeMfa: {
      subtitle: "V\xE1\u0161 z\xE1lo\u017En\xED k\xF3d je ten, kter\xFD jste z\xEDskali p\u0159i nastaven\xED dvouf\xE1zov\xE9 autentizace.",
      title: "Zadejte z\xE1lo\u017En\xED k\xF3d"
    },
    emailCode: {
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Neobdr\u017Eeli jste k\xF3d? Znovu poslat",
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Zkontrolujte sv\u016Fj e-mail"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Pro pokra\u010Dov\xE1n\xED otev\u0159ete ov\u011B\u0159ovac\xED odkaz na za\u0159\xEDzen\xED a prohl\xED\u017Ee\u010Di, ze kter\xE9ho jste zah\xE1jili p\u0159ihl\xE1\u0161en\xED",
        title: "Ov\u011B\u0159ovac\xED odkaz je pro toto za\u0159\xEDzen\xED neplatn\xFD"
      },
      expired: {
        subtitle: "Vra\u0165te se do p\u016Fvodn\xED karty pro pokra\u010Dov\xE1n\xED.",
        title: "Tento ov\u011B\u0159ovac\xED odkaz vypr\u0161el"
      },
      failed: {
        subtitle: "Vra\u0165te se do p\u016Fvodn\xED karty pro pokra\u010Dov\xE1n\xED.",
        title: "Tento ov\u011B\u0159ovac\xED odkaz je neplatn\xFD"
      },
      formSubtitle: "Pou\u017Eijte ov\u011B\u0159ovac\xED odkaz odeslan\xFD na v\xE1\u0161 e-mail",
      formTitle: "Ov\u011B\u0159ovac\xED odkaz",
      loading: {
        subtitle: "Brzy budete p\u0159esm\u011Brov\xE1ni",
        title: "P\u0159ihla\u0161ov\xE1n\xED..."
      },
      resendButton: "Neobdr\u017Eeli jste odkaz? Znovu poslat",
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Zkontrolujte sv\u016Fj e-mail",
      unusedTab: {
        title: "M\u016F\u017Eete zav\u0159\xEDt tuto kartu"
      },
      verified: {
        subtitle: "Brzy budete p\u0159esm\u011Brov\xE1ni",
        title: "\xDAsp\u011B\u0161n\u011B p\u0159ihl\xE1\u0161eno"
      },
      verifiedSwitchTab: {
        subtitle: "Vra\u0165te se na p\u016Fvodn\xED kartu pro pokra\u010Dov\xE1n\xED",
        subtitleNewTab: "Vra\u0165te se na nov\u011B otev\u0159enou kartu pro pokra\u010Dov\xE1n\xED",
        titleNewTab: "P\u0159ihl\xE1\u0161eno na jin\xE9 kart\u011B"
      }
    },
    forgotPassword: {
      formTitle: "K\xF3d pro resetov\xE1n\xED hesla",
      resendButton: "Neobdr\u017Eeli jste k\xF3d? Znovu poslat",
      subtitle: "pro resetov\xE1n\xED hesla",
      subtitle_email: "Nejprve zadejte k\xF3d odeslan\xFD na v\xE1\u0161 e-mail",
      subtitle_phone: "Nejprve zadejte k\xF3d odeslan\xFD na v\xE1\u0161 telefon",
      title: "Resetovat heslo"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Resetovat heslo",
      label__alternativeMethods: "Nebo se p\u0159ihlaste jinou metodou",
      title: "Zapomn\u011Bli jste heslo?"
    },
    noAvailableMethods: {
      message: "Nelze pokra\u010Dovat v p\u0159ihl\xE1\u0161en\xED. Nen\xED k dispozici \u017E\xE1dn\xFD autentiza\u010Dn\xED faktor.",
      subtitle: "Do\u0161lo k chyb\u011B",
      title: "Nelze se p\u0159ihl\xE1sit"
    },
    passkey: {
      subtitle: "Pou\u017Eit\xED va\u0161eho p\u0159\xEDstupov\xE9ho kl\xED\u010De potvrzuje, \u017Ee jste to vy. Va\u0161e za\u0159\xEDzen\xED m\u016F\u017Ee po\u017E\xE1dat o otisk prstu, obli\u010Dej nebo z\xE1mek obrazovky.",
      title: "Pou\u017E\xEDt v\xE1\u0161 p\u0159\xEDstupov\xFD kl\xED\u010D"
    },
    password: {
      actionLink: "Pou\u017E\xEDt jinou metodu",
      subtitle: "Zadejte heslo spojen\xE9 s va\u0161\xEDm \xFA\u010Dtem",
      title: "Zadejte sv\xE9 heslo"
    },
    passwordPwned: {
      title: "Heslo kompromitov\xE1no"
    },
    phoneCode: {
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Neobdr\u017Eeli jste k\xF3d? Znovu poslat",
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Zkontrolujte sv\u016Fj telefon"
    },
    phoneCodeMfa: {
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Neobdr\u017Eeli jste k\xF3d? Znovu poslat",
      subtitle: "Pro pokra\u010Dov\xE1n\xED zadejte ov\u011B\u0159ovac\xED k\xF3d odeslan\xFD na v\xE1\u0161 telefon",
      title: "Zkontrolujte sv\u016Fj telefon"
    },
    resetPassword: {
      formButtonPrimary: "Resetovat heslo",
      requiredMessage: "Z bezpe\u010Dnostn\xEDch d\u016Fvod\u016F je nutn\xE9 resetovat va\u0161e heslo.",
      successMessage: "Va\u0161e heslo bylo \xFAsp\u011B\u0161n\u011B zm\u011Bn\u011Bno. P\u0159ihla\u0161uji v\xE1s, pros\xEDm po\u010Dkejte okam\u017Eik.",
      title: "Nastavit nov\xE9 heslo"
    },
    resetPasswordMfa: {
      detailsLabel: "P\u0159ed resetov\xE1n\xEDm hesla mus\xEDme ov\u011B\u0159it va\u0161i identitu."
    },
    start: {
      actionLink: "Registrovat se",
      actionLink__join_waitlist: "P\u0159ipojit se k \u010Dekac\xED listin\u011B",
      actionLink__use_email: "Pou\u017E\xEDt e-mail",
      actionLink__use_email_username: "Pou\u017E\xEDt e-mail nebo u\u017Eivatelsk\xE9 jm\xE9no",
      actionLink__use_passkey: "Pou\u017E\xEDt p\u0159\xEDstupov\xFD kl\xED\u010D m\xEDsto toho",
      actionLink__use_phone: "Pou\u017E\xEDt telefon",
      actionLink__use_username: "Pou\u017E\xEDt u\u017Eivatelsk\xE9 jm\xE9no",
      actionText: "Nem\xE1te \xFA\u010Det?",
      actionText__join_waitlist: "Chcete d\u0159\xEDv\u011Bj\u0161\xED p\u0159\xEDstup?",
      alternativePhoneCodeProvider: {
        actionLink: "Pou\u017E\xEDt jinou metodu",
        label: "Telefonn\xED \u010D\xEDslo {{provider}}",
        subtitle: "Zadejte sv\xE9 telefonn\xED \u010D\xEDslo, abyste z\xEDskali ov\u011B\u0159ovac\xED k\xF3d na {{provider}}.",
        title: "P\u0159ihl\xE1sit se do {{applicationName}} pomoc\xED {{provider}}"
      },
      subtitle: "V\xEDtejte zp\u011Bt! Pros\xEDm p\u0159ihlaste se pro pokra\u010Dov\xE1n\xED",
      subtitleCombined: void 0,
      title: "P\u0159ihl\xE1sit se do {{applicationName}}",
      titleCombined: "Pokra\u010Dovat do {{applicationName}}"
    },
    totpMfa: {
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      subtitle: "Pro pokra\u010Dov\xE1n\xED zadejte ov\u011B\u0159ovac\xED k\xF3d vygenerovan\xFD va\u0161\xED aplikac\xED pro ov\u011B\u0159ov\xE1n\xED",
      title: "Dvouf\xE1zov\xE9 ov\u011B\u0159en\xED"
    }
  },
  signInEnterPasswordTitle: "Zadejte sv\xE9 heslo",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: "Neobdr\u017Eeli jste k\xF3d? Znovu poslat",
      subtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d odeslan\xFD na v\xE1\u0161 {{provider}}",
      title: "Ov\u011B\u0159te sv\u016Fj {{provider}}"
    },
    continue: {
      actionLink: "P\u0159ihl\xE1sit se",
      actionText: "U\u017E m\xE1te \xFA\u010Det?",
      subtitle: "Pros\xEDm vypl\u0148te zb\xFDvaj\xEDc\xED \xFAdaje pro pokra\u010Dov\xE1n\xED.",
      title: "Vypl\u0148te chyb\u011Bj\xEDc\xED pole"
    },
    emailCode: {
      formSubtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d odeslan\xFD na va\u0161i e-mailovou adresu",
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Neobdr\u017Eeli jste k\xF3d? Znovu poslat",
      subtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d odeslan\xFD na v\xE1\u0161 e-mail",
      title: "Ov\u011B\u0159te sv\u016Fj e-mail"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Pro pokra\u010Dov\xE1n\xED otev\u0159ete ov\u011B\u0159ovac\xED odkaz na za\u0159\xEDzen\xED a prohl\xED\u017Ee\u010Di, ze kter\xE9ho jste zah\xE1jili registraci",
        title: "Ov\u011B\u0159ovac\xED odkaz je pro toto za\u0159\xEDzen\xED neplatn\xFD"
      },
      formSubtitle: "Pou\u017Eijte ov\u011B\u0159ovac\xED odkaz odeslan\xFD na va\u0161i e-mailovou adresu",
      formTitle: "Ov\u011B\u0159ovac\xED odkaz",
      loading: {
        title: "Registruji se..."
      },
      resendButton: "Neobdr\u017Eeli jste odkaz? Znovu poslat",
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Ov\u011B\u0159te sv\u016Fj e-mail",
      verified: {
        title: "\xDAsp\u011B\u0161n\u011B zaregistrov\xE1no"
      },
      verifiedSwitchTab: {
        subtitle: "Vra\u0165te se na nov\u011B otev\u0159enou kartu pro pokra\u010Dov\xE1n\xED",
        subtitleNewTab: "Vra\u0165te se na p\u0159edchoz\xED kartu pro pokra\u010Dov\xE1n\xED",
        title: "E-mail \xFAsp\u011B\u0161n\u011B ov\u011B\u0159en"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: 'Souhlas\xEDm s {{ privacyPolicyLink || link("Z\xE1sadami ochrany osobn\xEDch \xFAdaj\u016F") }}',
        label__onlyTermsOfService: 'Souhlas\xEDm s {{ termsOfServiceLink || link("Podm\xEDnkami slu\u017Eby") }}',
        label__termsOfServiceAndPrivacyPolicy: 'Souhlas\xEDm s {{ termsOfServiceLink || link("Podm\xEDnkami slu\u017Eby") }} a {{ privacyPolicyLink || link("Z\xE1sadami ochrany osobn\xEDch \xFAdaj\u016F") }}'
      },
      continue: {
        subtitle: "Pros\xEDm p\u0159e\u010Dt\u011Bte si a p\u0159ijm\u011Bte podm\xEDnky pro pokra\u010Dov\xE1n\xED",
        title: "Pr\xE1vn\xED souhlas"
      }
    },
    phoneCode: {
      formSubtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d odeslan\xFD na va\u0161e telefonn\xED \u010D\xEDslo",
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Neobdr\u017Eeli jste k\xF3d? Znovu poslat",
      subtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d odeslan\xFD na v\xE1\u0161 telefon",
      title: "Ov\u011B\u0159te sv\u016Fj telefon"
    },
    restrictedAccess: {
      actionLink: "P\u0159ihl\xE1sit se",
      actionText: "U\u017E m\xE1te \xFA\u010Det?",
      blockButton__emailSupport: "Podpora p\u0159es e-mail",
      blockButton__joinWaitlist: "P\u0159ipojit se k \u010Dekac\xED listin\u011B",
      subtitle: "Registrace jsou moment\xE1ln\u011B zak\xE1z\xE1ny. Pokud se domn\xEDv\xE1te, \u017Ee byste m\u011Bli m\xEDt p\u0159\xEDstup, kontaktujte pros\xEDm podporu.",
      subtitleWaitlist: "Registrace jsou moment\xE1ln\u011B zak\xE1z\xE1ny. Chcete-li b\xFDt prvn\xED, kdo se dozv\xED, kdy spust\xEDme, p\u0159ipojte se k \u010Dekac\xED listin\u011B.",
      title: "Omezen\xFD p\u0159\xEDstup"
    },
    start: {
      actionLink: "P\u0159ihl\xE1sit se",
      actionLink__use_email: "Pou\u017E\xEDt e-mail m\xEDsto toho",
      actionLink__use_phone: "Pou\u017E\xEDt telefon m\xEDsto toho",
      actionText: "U\u017E m\xE1te \xFA\u010Det?",
      alternativePhoneCodeProvider: {
        actionLink: "Pou\u017E\xEDt jinou metodu",
        label: "Telefonn\xED \u010D\xEDslo {{provider}}",
        subtitle: "Zadejte sv\xE9 telefonn\xED \u010D\xEDslo, abyste z\xEDskali ov\u011B\u0159ovac\xED k\xF3d na {{provider}}.",
        title: "Zaregistrovat se do {{applicationName}} pomoc\xED {{provider}}"
      },
      subtitle: "V\xEDtejte! Pros\xEDm vypl\u0148te \xFAdaje pro za\u010D\xE1tek.",
      subtitleCombined: "V\xEDtejte! Pros\xEDm vypl\u0148te \xFAdaje pro za\u010D\xE1tek.",
      title: "Vytvo\u0159te si \xFA\u010Det",
      titleCombined: "Vytvo\u0159te si \xFA\u010Det"
    }
  },
  socialButtonsBlockButton: "Pokra\u010Dovat s {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: "{{provider|titleize}}",
  unstable__errors: {
    already_a_member_in_organization: "{{email}} je ji\u017E \u010Dlenem organizace.",
    captcha_invalid: void 0,
    captcha_unavailable: "Registrace nebyla \xFAsp\u011B\u0161n\xE1 kv\u016Fli ne\xFAsp\u011B\u0161n\xE9 validaci bota. Pros\xEDm obnovte str\xE1nku a zkuste to znovu, nebo se obra\u0165te na podporu pro dal\u0161\xED pomoc.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: void 0,
    form_identifier_exists__phone_number: void 0,
    form_identifier_exists__username: void 0,
    form_identifier_not_found: void 0,
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: void 0,
    form_param_format_invalid__phone_number: void 0,
    form_param_max_length_exceeded__first_name: void 0,
    form_param_max_length_exceeded__last_name: void 0,
    form_param_max_length_exceeded__name: void 0,
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: "Va\u0161e heslo je p\u0159\xEDli\u0161 kr\xE1tk\xE9. Mus\xED m\xEDt alespo\u0148 8 znak\u016F.",
    form_password_not_strong_enough: "Va\u0161e heslo nen\xED dostate\u010Dn\u011B siln\xE9.",
    form_password_pwned: "Toto heslo bylo nalezeno jako sou\u010D\xE1st prolomen\xED a nelze ho pou\u017E\xEDt, zkuste pros\xEDm jin\xE9 heslo.",
    form_password_pwned__sign_in: "Toto heslo bylo nalezeno jako sou\u010D\xE1st prolomen\xED a nelze ho pou\u017E\xEDt, pros\xEDm resetujte si heslo.",
    form_password_size_in_bytes_exceeded: void 0,
    form_password_validation_failed: void 0,
    form_username_invalid_character: void 0,
    form_username_invalid_length: "Va\u0161e u\u017Eivatelsk\xE9 jm\xE9no mus\xED m\xEDt mezi {{min_length}} a {{max_length}} znaky.",
    identification_deletion_failed: void 0,
    not_allowed_access: void 0,
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: "P\u0159\xEDstupov\xFD kl\xED\u010D je ji\u017E registrov\xE1n na tomto za\u0159\xEDzen\xED.",
    passkey_not_supported: "P\u0159\xEDstupov\xE9 kl\xED\u010De nejsou podporov\xE1ny na tomto za\u0159\xEDzen\xED.",
    passkey_pa_not_supported: "Registrace vy\u017Eaduje autentiza\u010Dn\xED metodu platformy, ale za\u0159\xEDzen\xED ji nepodporuje.",
    passkey_registration_cancelled: "Registrace p\u0159\xEDstupov\xE9ho kl\xED\u010De byla zru\u0161ena nebo vypr\u0161el \u010Dasov\xFD limit.",
    passkey_retrieval_cancelled: "Ov\u011B\u0159en\xED p\u0159\xEDstupov\xE9ho kl\xED\u010De bylo zru\u0161eno nebo vypr\u0161el \u010Dasov\xFD limit.",
    passwordComplexity: {
      maximumLength: "m\xE9n\u011B ne\u017E {{length}} znak\u016F",
      minimumLength: "{{length}} nebo v\xEDce znak\u016F",
      requireLowercase: "mal\xE9 p\xEDsmeno",
      requireNumbers: "\u010D\xEDslici",
      requireSpecialCharacter: "speci\xE1ln\xED znak",
      requireUppercase: "velk\xE9 p\xEDsmeno",
      sentencePrefix: "Va\u0161e heslo mus\xED obsahovat"
    },
    phone_number_exists: void 0,
    session_exists: void 0,
    web3_missing_identifier: "Roz\u0161\xED\u0159en\xED pen\u011B\u017Eenky Web3 nebylo nalezeno. Pro pokra\u010Dov\xE1n\xED pros\xEDm nainstalujte jednu.",
    zxcvbn: {
      couldBeStronger: "Va\u0161e heslo funguje, ale mohlo by b\xFDt siln\u011Bj\u0161\xED. Zkuste p\u0159idat v\xEDce znak\u016F.",
      goodPassword: "Va\u0161e heslo spl\u0148uje v\u0161echny pot\u0159ebn\xE9 po\u017Eadavky.",
      notEnough: "Va\u0161e heslo nen\xED dostate\u010Dn\u011B siln\xE9.",
      suggestions: {
        allUppercase: "Pou\u017Eijte velk\xE1 p\xEDsmena u n\u011Bkter\xFDch, ale ne v\u0161ech p\xEDsmen.",
        anotherWord: "P\u0159idejte v\xEDce slov, kter\xE1 jsou m\xE9n\u011B b\u011B\u017En\xE1.",
        associatedYears: "Vyhn\u011Bte se rok\u016Fm, kter\xE9 jsou s v\xE1mi spojeny.",
        capitalization: "Pou\u017Eijte velk\xE1 p\xEDsmena v\xEDce ne\u017E jen u prvn\xEDho p\xEDsmene.",
        dates: "Vyhn\u011Bte se dat\u016Fm a rok\u016Fm, kter\xE9 jsou s v\xE1mi spojeny.",
        l33t: "Vyhn\u011Bte se p\u0159edv\xEDdateln\xFDm n\xE1hrad\xE1m p\xEDsmen jako '@' za 'a'.",
        longerKeyboardPattern: "Pou\u017Eijte del\u0161\xED kl\xE1vesnicov\xE9 vzory a n\u011Bkolikr\xE1t zm\u011B\u0148te sm\u011Br psan\xED.",
        noNeed: "M\u016F\u017Eete vytv\xE1\u0159et siln\xE1 hesla bez pou\u017Eit\xED symbol\u016F, \u010D\xEDsel nebo velk\xFDch p\xEDsmen.",
        pwned: "Pokud pou\u017E\xEDv\xE1te toto heslo jinde, m\u011Bli byste ho zm\u011Bnit.",
        recentYears: "Vyhn\u011Bte se ned\xE1vn\xFDm rok\u016Fm.",
        repeated: "Vyhn\u011Bte se opakuj\xEDc\xEDm se slov\u016Fm a znak\u016Fm.",
        reverseWords: "Vyhn\u011Bte se obr\xE1cen\xFDm pravopis\u016Fm b\u011B\u017En\xFDch slov.",
        sequences: "Vyhn\u011Bte se b\u011B\u017En\xFDm sekvenc\xEDm znak\u016F.",
        useWords: "Pou\u017Eijte v\xEDce slov, ale vyhn\u011Bte se b\u011B\u017En\xFDm fr\xE1z\xEDm."
      },
      warnings: {
        common: "Toto je b\u011B\u017En\u011B pou\u017E\xEDvan\xE9 heslo.",
        commonNames: "B\u011B\u017En\xE1 jm\xE9na a p\u0159\xEDjmen\xED jsou snadno uh\xE1dnuteln\xE1.",
        dates: "Data jsou snadno uh\xE1dnuteln\xE1.",
        extendedRepeat: 'Opakovan\xE9 vzory znak\u016F jako "abcabcabc" jsou snadno uh\xE1dnuteln\xE9.',
        keyPattern: "Kr\xE1tk\xE9 kl\xE1vesnicov\xE9 vzory jsou snadno uh\xE1dnuteln\xE9.",
        namesByThemselves: "Samotn\xE1 jm\xE9na nebo p\u0159\xEDjmen\xED jsou snadno uh\xE1dnuteln\xE1.",
        pwned: "Va\u0161e heslo bylo odhaleno datov\xFDm \xFAnikem na internetu.",
        recentYears: "Ned\xE1vn\xE9 roky jsou snadno uh\xE1dnuteln\xE9.",
        sequences: 'B\u011B\u017En\xE9 sekvence znak\u016F jako "abc" jsou snadno uh\xE1dnuteln\xE9.',
        similarToCommon: "Toto je podobn\xE9 b\u011B\u017En\u011B pou\u017E\xEDvan\xE9mu heslu.",
        simpleRepeat: 'Opakovan\xE9 znaky jako "aaa" jsou snadno uh\xE1dnuteln\xE9.',
        straightRow: "P\u0159\xEDm\xE9 \u0159ady kl\xE1ves na kl\xE1vesnici jsou snadno uh\xE1dnuteln\xE9.",
        topHundred: "Toto je \u010Dasto pou\u017E\xEDvan\xE9 heslo.",
        topTen: "Toto je velmi \u010Dasto pou\u017E\xEDvan\xE9 heslo.",
        userInputs: "Nem\u011Bly by zde b\xFDt \u017E\xE1dn\xE9 osobn\xED nebo se str\xE1nkou souvisej\xEDc\xED \xFAdaje.",
        wordByItself: "Jednotliv\xE1 slova jsou snadno uh\xE1dnuteln\xE1."
      }
    }
  },
  userButton: {
    action__addAccount: "P\u0159idat \xFA\u010Det",
    action__manageAccount: "Spravovat \xFA\u010Det",
    action__signOut: "Odhl\xE1sit se",
    action__signOutAll: "Odhl\xE1sit se ze v\u0161ech \xFA\u010Dt\u016F"
  },
  userProfile: {
    apiKeysPage: {
      title: "API kl\xED\u010De"
    },
    backupCodePage: {
      actionLabel__copied: "Zkop\xEDrov\xE1no!",
      actionLabel__copy: "Zkop\xEDrovat v\u0161e",
      actionLabel__download: "St\xE1hnout .txt",
      actionLabel__print: "Vytisknout",
      infoText1: "Z\xE1lo\u017En\xED k\xF3dy budou pro tento \xFA\u010Det povoleny.",
      infoText2: "Uchov\xE1vejte z\xE1lo\u017En\xED k\xF3dy v tajnosti a bezpe\u010Dn\u011B. M\u016F\u017Eete vygenerovat nov\xE9 z\xE1lo\u017En\xED k\xF3dy, pokud m\xE1te podez\u0159en\xED, \u017Ee byly kompromitov\xE1ny.",
      subtitle__codelist: "Uchov\xE1vejte je bezpe\u010Dn\u011B a tajn\u011B.",
      successMessage: "Z\xE1lo\u017En\xED k\xF3dy jsou nyn\xED povoleny. M\u016F\u017Eete pou\u017E\xEDt jeden z t\u011Bchto k\xF3d\u016F k p\u0159ihl\xE1\u0161en\xED do sv\xE9ho \xFA\u010Dtu, pokud ztrat\xEDte p\u0159\xEDstup k va\u0161emu ov\u011B\u0159ovac\xEDmu za\u0159\xEDzen\xED. Ka\u017Ed\xFD k\xF3d lze pou\u017E\xEDt pouze jednou.",
      successSubtitle: "M\u016F\u017Eete pou\u017E\xEDt jeden z t\u011Bchto k\xF3d\u016F k p\u0159ihl\xE1\u0161en\xED do sv\xE9ho \xFA\u010Dtu, pokud ztrat\xEDte p\u0159\xEDstup k va\u0161emu ov\u011B\u0159ovac\xEDmu za\u0159\xEDzen\xED.",
      title: "P\u0159idat ov\u011B\u0159ov\xE1n\xED pomoc\xED z\xE1lo\u017En\xEDch k\xF3d\u016F",
      title__codelist: "Z\xE1lo\u017En\xED k\xF3dy"
    },
    billingPage: {
      paymentHistorySection: {
        empty: "\u017D\xE1dn\xE1 historie plateb",
        notFound: "Pokus o platbu nenalezen",
        tableHeader__amount: "\u010C\xE1stka",
        tableHeader__date: "Datum",
        tableHeader__status: "Stav"
      },
      paymentSourcesSection: {
        actionLabel__default: "Nastavit jako v\xFDchoz\xED",
        actionLabel__remove: "Odebrat",
        add: "P\u0159idat novou platebn\xED metodu",
        addSubtitle: "P\u0159idejte novou platebn\xED metodu k va\u0161emu \xFA\u010Dtu.",
        cancelButton: "Zru\u0161it",
        formButtonPrimary__add: "P\u0159idat platebn\xED metodu",
        formButtonPrimary__pay: "Zaplatit {{amount}}",
        payWithTestCardButton: "Zaplatit testovac\xED kartou",
        removeResource: {
          messageLine1: "{{identifier}} bude odstran\u011Bn z tohoto \xFA\u010Dtu.",
          messageLine2: "Tento platebn\xED zdroj ji\u017E nebudete moci pou\u017E\xEDvat a ve\u0161ker\xE1 opakuj\xEDc\xED se p\u0159edplatn\xE1, kter\xE1 na n\u011Bm z\xE1vis\xED, p\u0159estanou fungovat.",
          successMessage: "{{paymentSource}} byl odstran\u011Bn z va\u0161eho \xFA\u010Dtu.",
          title: "Odebrat platebn\xED metodu"
        },
        title: "Platebn\xED metody"
      },
      start: {
        headerTitle__payments: "Platby",
        headerTitle__plans: "Pl\xE1ny",
        headerTitle__statements: "V\xFDpisy",
        headerTitle__subscriptions: "P\u0159edplatn\xE9"
      },
      statementsSection: {
        empty: "\u017D\xE1dn\xE9 v\xFDpisy k zobrazen\xED",
        itemCaption__paidForPlan: "Zaplaceno za pl\xE1n {{plan}} {{period}}",
        itemCaption__proratedCredit: "Pom\u011Brn\xFD kredit za \u010D\xE1ste\u010Dn\xE9 vyu\u017Eit\xED p\u0159edchoz\xEDho p\u0159edplatn\xE9ho",
        itemCaption__subscribedAndPaidForPlan: "P\u0159edplaceno a zaplaceno za pl\xE1n {{plan}} {{period}}",
        notFound: "V\xFDpis nenalezen",
        tableHeader__amount: "\u010C\xE1stka",
        tableHeader__date: "Datum",
        title: "V\xFDpisy",
        totalPaid: "Celkem zaplaceno"
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: "P\u0159ihl\xE1sit se k pl\xE1nu",
        actionLabel__switchPlan: "Zm\u011Bnit pl\xE1ny",
        tableHeader__edit: "Upravit",
        tableHeader__plan: "Pl\xE1n",
        tableHeader__startDate: "Datum zah\xE1jen\xED",
        title: "P\u0159edplatn\xE9"
      },
      subscriptionsSection: {
        actionLabel__default: "Spravovat"
      },
      switchPlansSection: {
        title: "Zm\u011Bnit pl\xE1ny"
      },
      title: "Fakturace"
    },
    connectedAccountPage: {
      formHint: "Vyberte poskytovatele pro p\u0159ipojen\xED va\u0161eho \xFA\u010Dtu.",
      formHint__noAccounts: "Nejsou k dispozici \u017E\xE1dn\xED extern\xED poskytovatel\xE9 \xFA\u010Dt\u016F.",
      removeResource: {
        messageLine1: "{{identifier}} bude odstran\u011Bn z tohoto \xFA\u010Dtu.",
        messageLine2: "Ji\u017E nebudete moci pou\u017E\xEDvat tento p\u0159ipojen\xFD \xFA\u010Det a ve\u0161ker\xE9 z\xE1visl\xE9 funkce p\u0159estanou fungovat.",
        successMessage: "{{connectedAccount}} byl odstran\u011Bn z va\u0161eho \xFA\u010Dtu.",
        title: "Odebrat p\u0159ipojen\xFD \xFA\u010Det"
      },
      socialButtonsBlockButton: "{{provider|titleize}}",
      successMessage: "Poskytovatel byl p\u0159id\xE1n k va\u0161emu \xFA\u010Dtu",
      title: "P\u0159idat p\u0159ipojen\xFD \xFA\u010Det"
    },
    deletePage: {
      actionDescription: 'Napi\u0161te "Smazat \xFA\u010Det" n\xED\u017Ee pro pokra\u010Dov\xE1n\xED.',
      confirm: "Smazat \xFA\u010Det",
      messageLine1: "Jste si jisti, \u017Ee chcete smazat sv\u016Fj \xFA\u010Det?",
      messageLine2: "Tato akce je trval\xE1 a nevratn\xE1.",
      title: "Smazat \xFA\u010Det"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "Na tuto e-mailovou adresu bude odesl\xE1n ov\u011B\u0159ovac\xED k\xF3d.",
        formSubtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d odeslan\xFD na {{identifier}}",
        formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
        resendButton: "Neobdr\u017Eeli jste k\xF3d? Znovu poslat",
        successMessage: "E-mail {{identifier}} byl p\u0159id\xE1n k va\u0161emu \xFA\u010Dtu."
      },
      emailLink: {
        formHint: "Na tuto e-mailovou adresu bude odesl\xE1n ov\u011B\u0159ovac\xED odkaz.",
        formSubtitle: "Klikn\u011Bte na ov\u011B\u0159ovac\xED odkaz v e-mailu odeslan\xE9m na {{identifier}}",
        formTitle: "Ov\u011B\u0159ovac\xED odkaz",
        resendButton: "Neobdr\u017Eeli jste odkaz? Znovu poslat",
        successMessage: "E-mail {{identifier}} byl p\u0159id\xE1n k va\u0161emu \xFA\u010Dtu."
      },
      enterpriseSSOLink: {
        formButton: "Klikn\u011Bte pro p\u0159ihl\xE1\u0161en\xED",
        formSubtitle: "Dokon\u010Dete p\u0159ihl\xE1\u0161en\xED pomoc\xED {{identifier}}"
      },
      formHint: "Tuto e-mailovou adresu budete muset ov\u011B\u0159it, ne\u017E ji bude mo\u017En\xE9 p\u0159idat k va\u0161emu \xFA\u010Dtu.",
      removeResource: {
        messageLine1: "{{identifier}} bude odstran\u011Bn z tohoto \xFA\u010Dtu.",
        messageLine2: "Ji\u017E nebudete moci p\u0159ihl\xE1sit se pomoc\xED t\xE9to e-mailov\xE9 adresy.",
        successMessage: "{{emailAddress}} byl odstran\u011Bn z va\u0161eho \xFA\u010Dtu.",
        title: "Odebrat e-mailovou adresu"
      },
      title: "P\u0159idat e-mailovou adresu",
      verifyTitle: "Ov\u011B\u0159it e-mailovou adresu"
    },
    formButtonPrimary__add: "P\u0159idat",
    formButtonPrimary__continue: "Pokra\u010Dovat",
    formButtonPrimary__finish: "Dokon\u010Dit",
    formButtonPrimary__remove: "Odebrat",
    formButtonPrimary__save: "Ulo\u017Eit",
    formButtonReset: "Zru\u0161it",
    mfaPage: {
      formHint: "Vyberte metodu k p\u0159id\xE1n\xED.",
      title: "P\u0159idat dvouf\xE1zov\xE9 ov\u011B\u0159en\xED"
    },
    mfaPhoneCodePage: {
      backButton: "Pou\u017E\xEDt st\xE1vaj\xEDc\xED \u010D\xEDslo",
      primaryButton__addPhoneNumber: "P\u0159idat telefonn\xED \u010D\xEDslo",
      removeResource: {
        messageLine1: "{{identifier}} ji\u017E nebude dost\xE1vat ov\u011B\u0159ovac\xED k\xF3dy p\u0159i p\u0159ihla\u0161ov\xE1n\xED.",
        messageLine2: "V\xE1\u0161 \xFA\u010Det nemus\xED b\xFDt tak bezpe\u010Dn\xFD. Jste si jisti, \u017Ee chcete pokra\u010Dovat?",
        successMessage: "Dvouf\xE1zov\xE9 ov\u011B\u0159en\xED SMS k\xF3dem bylo odstran\u011Bno pro {{mfaPhoneCode}}",
        title: "Odebrat dvouf\xE1zov\xE9 ov\u011B\u0159en\xED"
      },
      subtitle__availablePhoneNumbers: "Vyberte st\xE1vaj\xEDc\xED telefonn\xED \u010D\xEDslo pro registraci dvouf\xE1zov\xE9ho ov\u011B\u0159en\xED SMS k\xF3dem nebo p\u0159idejte nov\xE9.",
      subtitle__unavailablePhoneNumbers: "Nejsou k dispozici \u017E\xE1dn\xE1 telefonn\xED \u010D\xEDsla pro registraci dvouf\xE1zov\xE9ho ov\u011B\u0159en\xED SMS k\xF3dem, pros\xEDm p\u0159idejte nov\xE9.",
      successMessage1: "P\u0159i p\u0159ihl\xE1\u0161en\xED budete muset jako dal\u0161\xED krok zadat ov\u011B\u0159ovac\xED k\xF3d odeslan\xFD na toto telefonn\xED \u010D\xEDslo.",
      successMessage2: "Ulo\u017Ete si tyto z\xE1lo\u017En\xED k\xF3dy a ulo\u017Ete je na bezpe\u010Dn\xE9 m\xEDsto. Pokud ztrat\xEDte p\u0159\xEDstup ke sv\xE9mu ov\u011B\u0159ovac\xEDmu za\u0159\xEDzen\xED, m\u016F\u017Eete k p\u0159ihl\xE1\u0161en\xED pou\u017E\xEDt z\xE1lo\u017En\xED k\xF3dy.",
      successTitle: "SMS k\xF3dov\xE9 ov\u011B\u0159en\xED povoleno",
      title: "P\u0159idat ov\u011B\u0159en\xED SMS k\xF3dem"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "M\xEDsto toho naskenovat QR k\xF3d",
        buttonUnableToScan__nonPrimary: "Nem\u016F\u017Eete naskenovat QR k\xF3d?",
        infoText__ableToScan: "Nastavte novou metodu p\u0159ihl\xE1\u0161en\xED ve va\u0161\xED aplikaci pro ov\u011B\u0159ov\xE1n\xED a naskenujte n\xE1sleduj\xEDc\xED QR k\xF3d, abyste ji propojili se sv\xFDm \xFA\u010Dtem.",
        infoText__unableToScan: "Nastavte novou metodu p\u0159ihl\xE1\u0161en\xED ve sv\xE9 aplikaci pro ov\u011B\u0159ov\xE1n\xED a zadejte n\xED\u017Ee uveden\xFD kl\xED\u010D.",
        inputLabel__unableToScan1: "Ujist\u011Bte se, \u017Ee je povoleno \u010Dasov\xE9 nebo jednor\xE1zov\xE9 heslo, a dokon\u010Dete propojen\xED sv\xE9ho \xFA\u010Dtu.",
        inputLabel__unableToScan2: "Alternativn\u011B, pokud va\u0161e aplikace pro ov\u011B\u0159ov\xE1n\xED podporuje TOTP URI, m\u016F\u017Eete tak\xE9 zkop\xEDrovat cel\xFD URI."
      },
      removeResource: {
        messageLine1: "Ov\u011B\u0159ovac\xED k\xF3dy z t\xE9to aplikace pro ov\u011B\u0159ov\xE1n\xED ji\u017E nebudou vy\u017Eadov\xE1ny p\u0159i p\u0159ihla\u0161ov\xE1n\xED.",
        messageLine2: "V\xE1\u0161 \xFA\u010Det nemus\xED b\xFDt tak bezpe\u010Dn\xFD. Jste si jisti, \u017Ee chcete pokra\u010Dovat?",
        successMessage: "Dvouf\xE1zov\xE9 ov\u011B\u0159en\xED pomoc\xED aplikace pro ov\u011B\u0159ov\xE1n\xED bylo odstran\u011Bno.",
        title: "Odebrat dvouf\xE1zov\xE9 ov\u011B\u0159en\xED"
      },
      successMessage: "Dvouf\xE1zov\xE9 ov\u011B\u0159en\xED je nyn\xED povoleno. P\u0159i p\u0159ihl\xE1\u0161en\xED budete muset zadat ov\u011B\u0159ovac\xED k\xF3d z t\xE9to aplikace pro ov\u011B\u0159ov\xE1n\xED jako dal\u0161\xED krok.",
      title: "P\u0159idat aplikaci pro ov\u011B\u0159ov\xE1n\xED",
      verifySubtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d vygenerovan\xFD va\u0161\xED aplikac\xED pro ov\u011B\u0159ov\xE1n\xED",
      verifyTitle: "Ov\u011B\u0159ovac\xED k\xF3d"
    },
    mobileButton__menu: "Menu",
    navbar: {
      account: "Profil",
      apiKeys: "API kl\xED\u010De",
      billing: "Fakturace",
      description: "Spravujte informace o sv\xE9m \xFA\u010Dtu.",
      security: "Zabezpe\u010Den\xED",
      title: "\xDA\u010Det"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} bude odstran\u011Bn z tohoto \xFA\u010Dtu.",
        title: "Odebrat p\u0159\xEDstupov\xFD kl\xED\u010D"
      },
      subtitle__rename: "M\u016F\u017Eete zm\u011Bnit n\xE1zev p\u0159\xEDstupov\xE9ho kl\xED\u010De, aby se sn\xE1ze na\u0161el.",
      title__rename: "P\u0159ejmenovat p\u0159\xEDstupov\xFD kl\xED\u010D"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Doporu\u010Duje se odhl\xE1sit se ze v\u0161ech ostatn\xEDch za\u0159\xEDzen\xED, kter\xE1 mohla pou\u017E\xEDt va\u0161e star\xE9 heslo.",
      readonly: "Va\u0161e heslo nelze aktu\xE1ln\u011B upravit, proto\u017Ee se m\u016F\u017Eete p\u0159ihl\xE1sit pouze prost\u0159ednictv\xEDm podnikov\xE9ho p\u0159ipojen\xED.",
      successMessage__set: "Va\u0161e heslo bylo nastaveno.",
      successMessage__signOutOfOtherSessions: "V\u0161echna ostatn\xED za\u0159\xEDzen\xED byla odhl\xE1\u0161ena.",
      successMessage__update: "Va\u0161e heslo bylo aktualizov\xE1no.",
      title__set: "Nastavit heslo",
      title__update: "Aktualizovat heslo"
    },
    phoneNumberPage: {
      infoText: "Na toto telefonn\xED \u010D\xEDslo bude odesl\xE1na textov\xE1 zpr\xE1va obsahuj\xEDc\xED ov\u011B\u0159ovac\xED k\xF3d. Mohou b\xFDt \xFA\u010Dtov\xE1ny poplatky za zpr\xE1vy a data.",
      removeResource: {
        messageLine1: "{{identifier}} bude odstran\u011Bn z tohoto \xFA\u010Dtu.",
        messageLine2: "Ji\u017E nebudete moci p\u0159ihl\xE1sit se pomoc\xED tohoto telefonn\xEDho \u010D\xEDsla.",
        successMessage: "{{phoneNumber}} byl odstran\u011Bn z va\u0161eho \xFA\u010Dtu.",
        title: "Odebrat telefonn\xED \u010D\xEDslo"
      },
      successMessage: "{{identifier}} byl p\u0159id\xE1n k va\u0161emu \xFA\u010Dtu.",
      title: "P\u0159idat telefonn\xED \u010D\xEDslo",
      verifySubtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d odeslan\xFD na {{identifier}}",
      verifyTitle: "Ov\u011B\u0159it telefonn\xED \u010D\xEDslo"
    },
    plansPage: {
      title: "Pl\xE1ny"
    },
    profilePage: {
      fileDropAreaHint: "Doporu\u010Den\xE1 velikost 1:1, a\u017E 10MB.",
      imageFormDestructiveActionSubtitle: "Odebrat",
      imageFormSubtitle: "Nahr\xE1t",
      imageFormTitle: "Profilov\xFD obr\xE1zek",
      readonly: "Informace o va\u0161em profilu byly poskytnuty podnikov\xFDm p\u0159ipojen\xEDm a nelze je upravovat.",
      successMessage: "V\xE1\u0161 profil byl aktualizov\xE1n.",
      title: "Aktualizovat profil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Odhl\xE1sit se ze za\u0159\xEDzen\xED",
        title: "Aktivn\xED za\u0159\xEDzen\xED"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Znovu p\u0159ipojit",
        actionLabel__reauthorize: "Autorizovat nyn\xED",
        destructiveActionTitle: "Odebrat",
        primaryButton: "P\u0159ipojit \xFA\u010Det",
        subtitle__disconnected: "Tento \xFA\u010Det byl odpojen.",
        subtitle__reauthorize: "Po\u017Eadovan\xE9 rozsahy byly aktualizov\xE1ny a m\u016F\u017Eete zaznamenat omezenou funkcionalitu. Pros\xEDm znovu autorizujte tuto aplikaci, abyste se vyhnuli probl\xE9m\u016Fm",
        title: "P\u0159ipojen\xE9 \xFA\u010Dty"
      },
      dangerSection: {
        deleteAccountButton: "Smazat \xFA\u010Det",
        title: "Smazat \xFA\u010Det"
      },
      emailAddressesSection: {
        destructiveAction: "Odebrat e-mail",
        detailsAction__nonPrimary: "Nastavit jako prim\xE1rn\xED",
        detailsAction__primary: "Dokon\u010Dit ov\u011B\u0159en\xED",
        detailsAction__unverified: "Ov\u011B\u0159it",
        primaryButton: "P\u0159idat e-mailovou adresu",
        title: "E-mailov\xE9 adresy"
      },
      enterpriseAccountsSection: {
        title: "Podnikov\xE9 \xFA\u010Dty"
      },
      headerTitle__account: "Podrobnosti profilu",
      headerTitle__security: "Zabezpe\u010Den\xED",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Znovu vygenerovat",
          headerTitle: "Z\xE1lo\u017En\xED k\xF3dy",
          subtitle__regenerate: "Z\xEDskejte novou sadu zabezpe\u010Den\xFDch z\xE1lo\u017En\xEDch k\xF3d\u016F. P\u0159edchoz\xED z\xE1lo\u017En\xED k\xF3dy budou smaz\xE1ny a nelze je pou\u017E\xEDt.",
          title__regenerate: "Znovu vygenerovat z\xE1lo\u017En\xED k\xF3dy"
        },
        phoneCode: {
          actionLabel__setDefault: "Nastavit jako v\xFDchoz\xED",
          destructiveActionLabel: "Odebrat"
        },
        primaryButton: "P\u0159idat dvouf\xE1zov\xE9 ov\u011B\u0159en\xED",
        title: "Dvouf\xE1zov\xE9 ov\u011B\u0159en\xED",
        totp: {
          destructiveActionTitle: "Odebrat",
          headerTitle: "Aplikace pro ov\u011B\u0159ov\xE1n\xED"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Odebrat",
        menuAction__rename: "P\u0159ejmenovat",
        primaryButton: "P\u0159idat p\u0159\xEDstupov\xFD kl\xED\u010D",
        title: "P\u0159\xEDstupov\xE9 kl\xED\u010De"
      },
      passwordSection: {
        primaryButton__setPassword: "Nastavit heslo",
        primaryButton__updatePassword: "Aktualizovat heslo",
        title: "Heslo"
      },
      phoneNumbersSection: {
        destructiveAction: "Odebrat telefonn\xED \u010D\xEDslo",
        detailsAction__nonPrimary: "Nastavit jako prim\xE1rn\xED",
        detailsAction__primary: "Dokon\u010Dit ov\u011B\u0159en\xED",
        detailsAction__unverified: "Ov\u011B\u0159it telefonn\xED \u010D\xEDslo",
        primaryButton: "P\u0159idat telefonn\xED \u010D\xEDslo",
        title: "Telefonn\xED \u010D\xEDsla"
      },
      profileSection: {
        primaryButton: "Aktualizovat profil",
        title: "Profil"
      },
      usernameSection: {
        primaryButton__setUsername: "Nastavit u\u017Eivatelsk\xE9 jm\xE9no",
        primaryButton__updateUsername: "Aktualizovat u\u017Eivatelsk\xE9 jm\xE9no",
        title: "U\u017Eivatelsk\xE9 jm\xE9no"
      },
      web3WalletsSection: {
        destructiveAction: "Odebrat pen\u011B\u017Eenku",
        detailsAction__nonPrimary: "Nastavit jako prim\xE1rn\xED",
        primaryButton: "P\u0159ipojit pen\u011B\u017Eenku",
        title: "Web3 pen\u011B\u017Eenky"
      }
    },
    usernamePage: {
      successMessage: "Va\u0161e u\u017Eivatelsk\xE9 jm\xE9no bylo aktualizov\xE1no.",
      title__set: "Nastavit u\u017Eivatelsk\xE9 jm\xE9no",
      title__update: "Aktualizovat u\u017Eivatelsk\xE9 jm\xE9no"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} bude odstran\u011Bn z tohoto \xFA\u010Dtu.",
        messageLine2: "Ji\u017E se nebudete moci p\u0159ihl\xE1sit pomoc\xED t\xE9to web3 pen\u011B\u017Eenky.",
        successMessage: "{{web3Wallet}} byl odstran\u011Bn z va\u0161eho \xFA\u010Dtu.",
        title: "Odebrat web3 pen\u011B\u017Eenku"
      },
      subtitle__availableWallets: "Vyberte web3 pen\u011B\u017Eenku k p\u0159ipojen\xED k va\u0161emu \xFA\u010Dtu.",
      subtitle__unavailableWallets: "Nejsou k dispozici \u017E\xE1dn\xE9 web3 pen\u011B\u017Eenky.",
      successMessage: "Pen\u011B\u017Eenka byla p\u0159id\xE1na k va\u0161emu \xFA\u010Dtu.",
      title: "P\u0159idat web3 pen\u011B\u017Eenku",
      web3WalletButtonsBlockButton: "{{provider|titleize}}"
    }
  },
  waitlist: {
    start: {
      actionLink: "P\u0159ihl\xE1sit se",
      actionText: "U\u017E m\xE1te p\u0159\xEDstup?",
      formButton: "P\u0159ipojit se k \u010Dekac\xED listin\u011B",
      subtitle: "Zadejte svou e-mailovou adresu a d\xE1me v\xE1m v\u011Bd\u011Bt, a\u017E bude va\u0161e m\xEDsto p\u0159ipraveno",
      title: "P\u0159ipojit se k \u010Dekac\xED listin\u011B"
    },
    success: {
      message: "Brzy budete p\u0159esm\u011Brov\xE1ni...",
      subtitle: "Ozveme se v\xE1m, a\u017E bude va\u0161e m\xEDsto p\u0159ipraveno",
      title: "D\u011Bkujeme za p\u0159ipojen\xED k \u010Dekac\xED listin\u011B!"
    }
  }
};
export {
  csCZ
};
//# sourceMappingURL=cs-CZ.mjs.map