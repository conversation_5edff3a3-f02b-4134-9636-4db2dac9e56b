{"version": 3, "sources": ["../src/da-DK.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const daDK: LocalizationResource = {\n  locale: 'da-DK',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Tilbage',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Standard',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Anden enhed som efterligner',\n  badge__primary: 'Primær',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Kræver handling',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Denne enhed',\n  badge__unverified: 'Ikke verificeret',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Brugerenhed',\n  badge__you: 'Dig',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Opret organisation',\n    invitePage: {\n      formButtonReset: 'Spring over',\n    },\n    title: 'Opret organisation',\n  },\n  dates: {\n    lastDay: \"I går: {{ date | timeString('en-US') }}\",\n    next6Days: \"{{ date | weekday('en-US','long') }} kl. {{ date | timeString('en-US') }}\",\n    nextDay: \"I morgen: {{ date | timeString('en-US') }}\",\n    numeric: \"{{ date | numeric('en-US') }}\",\n    previous6Days: \"Sidste {{ date | weekday('en-US','long') }} kl. {{ date | timeString('en-US') }}\",\n    sameDay: \"I dag: {{ date | timeString('en-US') }}\",\n  },\n  dividerText: 'eller',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Brug en anden metode',\n  footerPageLink__help: 'Hjælp',\n  footerPageLink__privacy: 'Privatliv',\n  footerPageLink__terms: 'Vilkår',\n  formButtonPrimary: 'Fortsæt',\n  formButtonPrimary__verify: 'Verificer',\n  formFieldAction__forgotPassword: 'Glemt adgangskode?',\n  formFieldError__matchingPasswords: 'Adgangskoderne matcher.',\n  formFieldError__notMatchingPasswords: 'Adgangskoderne matcher ikke.',\n  formFieldError__verificationLinkExpired: 'Bekræftelseslinket er udløbet. Venligst anmod om et nyt link.',\n  formFieldHintText__optional: 'Valgfri',\n  formFieldHintText__slug: 'En slug er en menneskelig læsbar ID, der skal være unik. Det bruges ofte i URL’er.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: 'Indtast sikkerhedskode',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Slet konto',\n  formFieldInputPlaceholder__emailAddress: 'Indtast e-mailadresse',\n  formFieldInputPlaceholder__emailAddress_username: 'Indtast e-mailadresse eller brugernavn',\n  formFieldInputPlaceholder__emailAddresses:\n    'Indtast eller indsæt en eller flere e-mailadresser, adskilt af mellemrum eller kommaer',\n  formFieldInputPlaceholder__firstName: 'Indtast fornavn',\n  formFieldInputPlaceholder__lastName: 'Indtast efternavn',\n  formFieldInputPlaceholder__organizationDomain: 'Indtast domæne',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: 'Indtast e-mailadresse til verifikation',\n  formFieldInputPlaceholder__organizationName: 'Indtast organisationens navn',\n  formFieldInputPlaceholder__organizationSlug: 'Indtast slug URL',\n  formFieldInputPlaceholder__password: 'Indtast adgangskode',\n  formFieldInputPlaceholder__phoneNumber: 'Indtast telefonnummer',\n  formFieldInputPlaceholder__username: 'Indtast brugernavn',\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Aktiver automatiske invitationer for dette domæne',\n  formFieldLabel__backupCode: 'Sikkerhedskode',\n  formFieldLabel__confirmDeletion: 'Bekræftelse',\n  formFieldLabel__confirmPassword: 'Bekræft adgangskode',\n  formFieldLabel__currentPassword: 'Nuværende adgangskode',\n  formFieldLabel__emailAddress: 'E-mailadresse',\n  formFieldLabel__emailAddress_username: 'E-mailadresse eller brugernavn',\n  formFieldLabel__emailAddresses: 'E-mailadresser',\n  formFieldLabel__firstName: 'Fornavn',\n  formFieldLabel__lastName: 'Efternavn',\n  formFieldLabel__newPassword: 'Ny adgangskode',\n  formFieldLabel__organizationDomain: 'Domæne',\n  formFieldLabel__organizationDomainDeletePending: 'Slet ventende invitationer og forslag',\n  formFieldLabel__organizationDomainEmailAddress: 'Verifikation e-mailadresse',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Indtast en e-mailadresse under dette domæne for at modtage en kode og verificere dette domæne.',\n  formFieldLabel__organizationName: 'Organisationens navn',\n  formFieldLabel__organizationSlug: 'Slug URL',\n  formFieldLabel__passkeyName: 'Navn på adgangsnøgle',\n  formFieldLabel__password: 'Adgangskode',\n  formFieldLabel__phoneNumber: 'Telefonnummer',\n  formFieldLabel__role: 'Rolle',\n  formFieldLabel__signOutOfOtherSessions: 'Log ud af alle andre enheder',\n  formFieldLabel__username: 'Brugernavn',\n  impersonationFab: {\n    action__signOut: 'Log ud',\n    title: 'Logget ind som {{identifier}}',\n  },\n  maintenanceMode: 'Vedligeholdelsestilstand',\n  membershipRole__admin: 'Administrator',\n  membershipRole__basicMember: 'Medlem',\n  membershipRole__guestMember: 'Gæst',\n  organizationList: {\n    action__createOrganization: 'Opret organisation',\n    action__invitationAccept: 'Deltag',\n    action__suggestionsAccept: 'Anmod om at deltage',\n    createOrganization: 'Opret Organisation',\n    invitationAcceptedLabel: 'Deltaget',\n    subtitle: 'for at fortsætte til {{applicationName}}',\n    suggestionsAcceptedLabel: 'Afventer godkendelse',\n    title: 'Vælg en konto',\n    titleWithoutPersonal: 'Vælg en organisation',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Automatiske invitationer',\n    badge__automaticSuggestion: 'Automatiske forslag',\n    badge__manualInvitation: 'Ingen automatisk tilmelding',\n    badge__unverified: 'Ikke verificeret',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Tilføj domænet for at verificere. Brugere med e-mailadresser under dette domæne kan automatisk deltage i organisationen eller anmode om at deltage.',\n      title: 'Tilføj domæne',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed: 'Invitationerne kunne ikke sendes. Ret følgende, og prøv igen:',\n      formButtonPrimary__continue: 'Send invitationer',\n      selectDropdown__role: 'Vælg rolle',\n      subtitle: 'Inviter nye medlemmer til denne organisation',\n      successMessage: 'Invitationer blev sendt',\n      title: 'Inviter medlemmer',\n    },\n    membersPage: {\n      action__invite: 'Inviter',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Fjern medlem',\n        tableHeader__actions: 'Handlinger',\n        tableHeader__joined: 'Deltaget',\n        tableHeader__role: 'Rolle',\n        tableHeader__user: 'Bruger',\n      },\n      detailsTitle__emptyRow: 'Ingen medlemmer at vise',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Inviter brugere ved at forbinde et e-maildomæne med din organisation. Alle, der tilmelder sig med en matchende e-maildomæne, vil kunne deltage i organisationen når som helst.',\n          headerTitle: 'Automatiske invitationer',\n          primaryButton: 'Administrer verificerede domæner',\n        },\n        table__emptyRow: 'Ingen invitationer at vise',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Tilbagekald invitation',\n        tableHeader__invited: 'Inviteret',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Brugere, der tilmelder sig med en matchende e-maildomæne, vil kunne se et forslag om at anmode om at deltage i din organisation.',\n          headerTitle: 'Automatiske forslag',\n          primaryButton: 'Administrer verificerede domæner',\n        },\n        menuAction__approve: 'Godkend',\n        menuAction__reject: 'Afvis',\n        tableHeader__requested: 'Anmodet adgang',\n        table__emptyRow: 'Ingen anmodninger at vise',\n      },\n      start: {\n        headerTitle__invitations: 'Invitationer',\n        headerTitle__members: 'Medlemmer',\n        headerTitle__requests: 'Anmodninger',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Administrer din organisation.',\n      general: 'Generelt',\n      members: 'Medlemmer',\n      title: 'Organisation',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Skriv \"{{organizationName}}\" nedenfor for at fortsætte.',\n          messageLine1: 'Er du sikker på, at du vil slette denne organisation?',\n          messageLine2: 'Denne handling er permanent og kan ikke fortrydes.',\n          successMessage: 'Du har slettet organisationen.',\n          title: 'Slet organisation',\n        },\n        leaveOrganization: {\n          actionDescription: 'Skriv \"{{organizationName}}\" nedenfor for at fortsætte.',\n          messageLine1:\n            'Er du sikker på, at du vil forlade denne organisation? Du mister adgang til denne organisation og dens applikationer.',\n          messageLine2: 'Denne handling er permanent og kan ikke fortrydes.',\n          successMessage: 'Du har forladt organisationen.',\n          title: 'Forlad organisationen',\n        },\n        title: 'Fare',\n      },\n      domainSection: {\n        menuAction__manage: 'Administrer',\n        menuAction__remove: 'Slet',\n        menuAction__verify: 'Verificer',\n        primaryButton: 'Tilføj domæne',\n        subtitle:\n          'Tillad brugere at deltage i organisationen automatisk eller anmode om at deltage baseret på et verificeret e-maildomæne.',\n        title: 'Verificerede domæner',\n      },\n      successMessage: 'Organisationen er blevet opdateret.',\n      title: 'Organisationsprofil',\n    },\n    removeDomainPage: {\n      messageLine1: 'E-maildomænet {{domain}} vil blive fjernet.',\n      messageLine2: 'Brugere vil ikke længere kunne deltage i organisationen automatisk efter dette.',\n      successMessage: '{{domain}} er blevet fjernet.',\n      title: 'Fjern domæne',\n    },\n    start: {\n      headerTitle__general: 'Generelt',\n      headerTitle__members: 'Medlemmer',\n      profileSection: {\n        primaryButton: 'Opdater profil',\n        title: 'Organisationsprofil',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Fjernelse af dette domæne vil påvirke inviterede brugere.',\n        removeDomainActionLabel__remove: 'Fjern domæne',\n        removeDomainSubtitle: 'Fjern dette domæne fra dine verificerede domæner',\n        removeDomainTitle: 'Fjern domæne',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Brugere bliver automatisk inviteret til at deltage i organisationen, når de tilmelder sig og kan deltage når som helst.',\n        automaticInvitationOption__label: 'Automatiske invitationer',\n        automaticSuggestionOption__description:\n          'Brugere modtager et forslag om at anmode om at deltage, men skal godkendes af en administrator, før de kan deltage i organisationen.',\n        automaticSuggestionOption__label: 'Automatiske forslag',\n        calloutInfoLabel: 'Ændring af tilmeldingsindstilling vil kun påvirke nye brugere.',\n        calloutInvitationCountLabel: 'Ventende invitationer sendt til brugere: {{count}}',\n        calloutSuggestionCountLabel: 'Ventende forslag sendt til brugere: {{count}}',\n        manualInvitationOption__description: 'Brugere kan kun inviteres manuelt til organisationen.',\n        manualInvitationOption__label: 'Ingen automatisk tilmelding',\n        subtitle: 'Vælg hvordan brugere fra dette domæne kan deltage i organisationen.',\n      },\n      start: {\n        headerTitle__danger: 'Fare',\n        headerTitle__enrollment: 'Tilmeldingsindstillinger',\n      },\n      subtitle: 'Domænet {{domain}} er nu verificeret. Fortsæt ved at vælge tilmeldingsmetode.',\n      title: 'Opdater {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Indtast den bekræftelseskode, der blev sendt til din e-mailadresse',\n      formTitle: 'Bekræftelseskode',\n      resendButton: 'Modtog du ikke en kode? Send igen',\n      subtitle: 'Domænet {{domainName}} skal verificeres via e-mail.',\n      subtitleVerificationCodeScreen:\n        'En bekræftelseskode blev sendt til {{emailAddress}}. Indtast koden for at fortsætte.',\n      title: 'Verificer domæne',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Opret organisation',\n    action__invitationAccept: 'Deltag',\n    action__manageOrganization: 'Administrer organisation',\n    action__suggestionsAccept: 'Anmod om at deltage',\n    notSelected: 'Ingen organisation valgt',\n    personalWorkspace: 'Personligt arbejdsområde',\n    suggestionsAcceptedLabel: 'Afventer godkendelse',\n  },\n  paginationButton__next: 'Næste',\n  paginationButton__previous: 'Forrige',\n  paginationRowText__displaying: 'Viser',\n  paginationRowText__of: 'af',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Tilføj konto',\n      action__signOutAll: 'Log ud af alle konti',\n      subtitle: 'Vælg den konto, du ønsker at fortsætte med.',\n      title: 'Vælg en konto',\n    },\n    alternativeMethods: {\n      actionLink: 'Få hjælp',\n      actionText: 'Har du ingen af disse?',\n      blockButton__backupCode: 'Brug en backup-kode',\n      blockButton__emailCode: 'Send kode til {{identifier}}',\n      blockButton__emailLink: 'Send link til {{identifier}}',\n      blockButton__passkey: 'Brug en adgangsnøgle',\n      blockButton__password: 'Log ind med adgangskode',\n      blockButton__phoneCode: 'Send kode til {{identifier}}',\n      blockButton__totp: 'Brug din godkendelsesapp',\n      getHelp: {\n        blockButton__emailSupport: 'E-mail support',\n        content:\n          'Hvis du har problemer med at logge ind på din konto, skal du sende en e-mail til os, og vi vil samarbejde med dig om at genoprette adgang så hurtigt som muligt.',\n        title: 'Få hjælp',\n      },\n      subtitle: 'Oplever du problemer? Du kan bruge en af disse metoder til at logge ind.',\n      title: 'Brug en anden metode',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'Fortsæt til {{applicationName}}',\n      title: 'Indtast en backup-kode',\n    },\n    emailCode: {\n      formTitle: 'Bekræftelseskode',\n      resendButton: 'Send kode igen',\n      subtitle: 'Fortsæt til {{applicationName}}',\n      title: 'Tjek din email',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Klient uoverensstemmelse. Prøv igen.',\n        title: 'Klient fejl',\n      },\n      expired: {\n        subtitle: 'Vend tilbage til den oprindelige fane for at fortsætte.',\n        title: 'Dette bekræftelseslink er udløbet',\n      },\n      failed: {\n        subtitle: 'Vend tilbage til den oprindelige fane for at fortsætte.',\n        title: 'Dette bekræftelseslink er ugyldigt',\n      },\n      formSubtitle: 'Brug bekræftelseslinket sendt til din e-mail',\n      formTitle: 'Bekræftelseslink',\n      loading: {\n        subtitle: 'Du vil snart blive viderestillet',\n        title: 'Logger ind...',\n      },\n      resendButton: 'Send link igen',\n      subtitle: 'Fortsæt til {{applicationName}}',\n      title: 'Tjek din email',\n      unusedTab: {\n        title: 'Du kan lukke denne fane',\n      },\n      verified: {\n        subtitle: 'Du vil snart blive viderestillet',\n        title: 'Vellykket log ind',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Vend tilbage til den oprindelige fane for at fortsætte',\n        subtitleNewTab: 'Vend tilbage til den nyligt åbnede fane for at fortsætte',\n        titleNewTab: 'Logget ind på anden fane',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Nulstil adgangskode',\n      resendButton: 'Modtog du ikke en kode? Send igen',\n      subtitle: 'for at nulstille din adgangskode',\n      subtitle_email: 'Indtast først koden sendt til din e-mailadresse',\n      subtitle_phone: 'Indtast først koden sendt til din telefon',\n      title: 'Nulstil adgangskode',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Nulstil din adgangskode',\n      label__alternativeMethods: 'Eller, log ind med en anden metode',\n      title: 'Glemt Adgangskode?',\n    },\n    noAvailableMethods: {\n      message: 'Kan ikke fortsætte med login. Der er ingen tilgængelige godkendelsesfaktorer.',\n      subtitle: 'En fejl opstod',\n      title: 'Kan ikke logge ind',\n    },\n    passkey: {\n      subtitle: 'Brug adgangsnøgle til at logge ind.',\n      title: 'Adgangsnøgle',\n    },\n    password: {\n      actionLink: 'Brug en anden metode',\n      subtitle: 'Fortsæt til {{applicationName}}',\n      title: 'Indtast din adgangskode',\n    },\n    passwordPwned: {\n      title: 'Sikkerhedsadvarsel',\n    },\n    phoneCode: {\n      formTitle: 'Bekræftelseskode',\n      resendButton: 'Send kode igen',\n      subtitle: 'gå videre til {{applicationName}}',\n      title: 'Tjek din telefon',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Bekræftelseskode',\n      resendButton: 'Send kode igen',\n      subtitle: undefined,\n      title: 'Tjek din telefon',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Nulstil Adgangskode',\n      requiredMessage: 'Af sikkerhedsmæssige årsager er det nødvendigt at nulstille din adgangskode.',\n      successMessage: 'Din adgangskode blev ændret med succes. Logger dig ind, vent venligst et øjeblik.',\n      title: 'Indstil ny adgangskode',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Vi skal bekræfte din identitet, før du nulstiller din adgangskode.',\n    },\n    start: {\n      actionLink: 'Tilmeld dig',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Brug email',\n      actionLink__use_email_username: 'Brug email eller brugernavn',\n      actionLink__use_passkey: 'Brug adgangsnøgle',\n      actionLink__use_phone: 'Brug telefon',\n      actionLink__use_username: 'Brug brugernavn',\n      actionText: 'Ingen konto?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Forsæt til {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Log ind',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Bekræftelseskode',\n      subtitle: undefined,\n      title: 'Totrinsbekræftelse',\n    },\n  },\n  signInEnterPasswordTitle: 'Indtast din adgangskode',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Log ind',\n      actionText: 'Har du en konto?',\n      subtitle: 'Forsæt til {{applicationName}}',\n      title: 'Udfyld manglende felter',\n    },\n    emailCode: {\n      formSubtitle: 'Indtast bekræftelseskoden sendt til din e-mailadresse',\n      formTitle: 'Bekræftelseskode',\n      resendButton: 'Send kode igen',\n      subtitle: 'Fortsæt til {{applicationName}}',\n      title: 'Bekræft din email',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Klient uoverensstemmelse. Prøv igen.',\n        title: 'Klient fejl',\n      },\n      formSubtitle: 'Brug bekræftelseslinket sendt til din e-mailadresse',\n      formTitle: 'Bekræftelseslink',\n      loading: {\n        title: 'Tilmelding...',\n      },\n      resendButton: 'Send link igen',\n      subtitle: 'Forsæt til {{applicationName}}',\n      title: 'Bekræft din email',\n      verified: {\n        title: 'Vellykket tilmelding',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Vend tilbage til den nyligt åbnede fane for at fortsætte',\n        subtitleNewTab: 'Vend tilbage til forrige fane for at fortsætte',\n        title: 'E-mail er bekræftet',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Indtast bekræftelseskoden sendt til dit telefonnummer',\n      formTitle: 'Bekræftelseskode',\n      resendButton: 'Send kode igen',\n      subtitle: 'Fortsæt til {{applicationName}}',\n      title: 'Bekræft din telefon',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Log ind',\n      actionLink__use_email: 'Brug email',\n      actionLink__use_phone: 'Brug telefon',\n      actionText: 'Har du en konto?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Forsæt til {{applicationName}}',\n      subtitleCombined: 'Forsæt til {{applicationName}}',\n      title: 'Opret din konto',\n      titleCombined: 'Opret din konto',\n    },\n  },\n  socialButtonsBlockButton: 'Forsæt med {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'Tilmelding mislykkedes på grund af fejlede sikkerhedsvalideringer. Opdater siden for at prøve igen, eller kontakt support for yderligere assistance.',\n    captcha_unavailable:\n      'Tilmelding mislykkedes på grund af fejlet botvalidering. Opdater siden for at prøve igen, eller kontakt support for yderligere assistance.',\n    form_code_incorrect: 'Koden er forkert.',\n    form_identifier_exists__email_address: 'E-mailadressen er allerede i brug.',\n    form_identifier_exists__phone_number: 'Telefonnummeret er allerede i brug.',\n    form_identifier_exists__username: 'Brugernavnet er allerede i brug.',\n    form_identifier_not_found: 'Vi kunne ikke finde en konto med disse detaljer.',\n    form_param_format_invalid: 'Formatet er ugyldigt.',\n    form_param_format_invalid__email_address: 'E-mailadressen skal være en gyldig e-mailadresse.',\n    form_param_format_invalid__phone_number: 'Telefonnummeret skal være i et gyldigt internationalt format.',\n    form_param_max_length_exceeded__first_name: 'Fornavnet må ikke overstige 256 tegn.',\n    form_param_max_length_exceeded__last_name: 'Efternavnet må ikke overstige 256 tegn.',\n    form_param_max_length_exceeded__name: 'Navnet må ikke overstige 256 tegn.',\n    form_param_nil: 'Dette felt kan ikke være tomt.',\n    form_param_value_invalid: undefined,\n    form_password_incorrect: 'Adgangskoden er forkert.',\n    form_password_length_too_short: 'Adgangskoden er for kort.',\n    form_password_not_strong_enough: 'Adgangskoden er ikke stærk nok.',\n    form_password_pwned: 'Adgangskoden er blevet kompromitteret.',\n    form_password_pwned__sign_in: 'Din adgangskode er blevet kompromitteret, vælg en ny.',\n    form_password_size_in_bytes_exceeded:\n      'Din adgangskode har overskredet det maksimalt tilladte antal bytes, forkort den eller fjern nogle specialtegn.',\n    form_password_validation_failed: 'Forkert adgangskode.',\n    form_username_invalid_character: 'Brugernavnet indeholder ugyldige tegn.',\n    form_username_invalid_length: 'Brugernavnet har en ugyldig længde.',\n    identification_deletion_failed: 'Du kan ikke slette din sidste identifikation.',\n    not_allowed_access:\n      \"E-mailadressen eller telefonnummeret er ikke tilladt at tilmelde sig. Dette kan skyldes brug af '+', '=', '#' eller '.' i din e-mail-adresse, ved at bruge et domæne, der er forbundet med en midlertidig e-mail-tjeneste, eller ved at blive eksplicit blokeret. Hvis du mener, at dette er en fejl, bedes du kontakte support.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: 'Adgangsnøgle findes allerede.',\n    passkey_not_supported: 'Adgangsnøgler understøttes ikke på denne enhed.',\n    passkey_pa_not_supported: 'Adgangsnøgler understøttes ikke på dette styresystem.',\n    passkey_registration_cancelled: 'Registrering af adgangsnøgle blev annulleret.',\n    passkey_retrieval_cancelled: 'Hentning af adgangsnøgle blev annulleret.',\n    passwordComplexity: {\n      maximumLength: 'mindre end {{length}} tegn',\n      minimumLength: '{{length}} eller flere tegn',\n      requireLowercase: 'et lille bogstav',\n      requireNumbers: 'et tal',\n      requireSpecialCharacter: 'et specialtegn',\n      requireUppercase: 'et stort bogstav',\n      sentencePrefix: 'Din adgangskode skal indeholde',\n    },\n    phone_number_exists: 'Dette telefonnummer er allerede taget. Prøv et andet.',\n    session_exists: 'Du er allerede logget ind.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Din adgangskode virker, men kunne være stærkere. Prøv at tilføje flere tegn.',\n      goodPassword: 'Din adgangskode opfylder alle nødvendige krav.',\n      notEnough: 'Din adgangskode er ikke stærk nok.',\n      suggestions: {\n        allUppercase: 'Brug store bogstaver på nogle, men ikke alle bogstaver.',\n        anotherWord: 'Tilføj flere ord, der er mindre almindelige.',\n        associatedYears: 'Undgå år, der er forbundet med dig.',\n        capitalization: 'Brug store bogstaver på mere end det første bogstav.',\n        dates: 'Undgå datoer og år, der er forbundet med dig.',\n        l33t: \"Undgå forudsigelige bogstavsubstitutioner som '@' for 'a'.\",\n        longerKeyboardPattern: 'Brug længere tastaturmønstre og skift retning flere gange.',\n        noNeed: 'Du kan oprette stærke adgangskoder uden at bruge symboler, tal eller store bogstaver.',\n        pwned: 'Hvis du bruger denne adgangskode andre steder, bør du ændre den.',\n        recentYears: 'Undgå nylige årstal.',\n        repeated: 'Undgå gentagne ord og tegn.',\n        reverseWords: 'Undgå omvendte stavemåder af almindelige ord.',\n        sequences: 'Undgå almindelige tegnsekvenser.',\n        useWords: 'Brug flere ord, men undgå almindelige sætninger.',\n      },\n      warnings: {\n        common: 'Dette er en almindeligt brugt adgangskode.',\n        commonNames: 'Almindelige navne og efternavne er nemme at gætte.',\n        dates: 'Datoer er nemme at gætte.',\n        extendedRepeat: 'Gentagne tegnmønstre som \"abcabcabc\" er nemme at gætte.',\n        keyPattern: 'Korte tastaturmønstre er nemme at gætte.',\n        namesByThemselves: 'Enkeltstående navne eller efternavne er nemme at gætte.',\n        pwned: 'Din adgangskode er blevet eksponeret af et databrud på internettet.',\n        recentYears: 'Nylige årstal er nemme at gætte.',\n        sequences: 'Almindelige tegnsekvenser som \"abc\" er nemme at gætte.',\n        similarToCommon: 'Dette ligner en almindeligt brugt adgangskode.',\n        simpleRepeat: 'Gentagne tegn som \"aaa\" er nemme at gætte.',\n        straightRow: 'Lige rækker af taster på dit tastatur er nemme at gætte.',\n        topHundred: 'Dette er en ofte brugt adgangskode.',\n        topTen: 'Dette er en meget brugt adgangskode.',\n        userInputs: 'Der bør ikke være nogen personlige eller sidespecifikke data.',\n        wordByItself: 'Enkeltstående ord er nemme at gætte.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Tilføj konto',\n    action__manageAccount: 'Administrer konto',\n    action__signOut: 'Log ud',\n    action__signOutAll: 'Log ud af alle konti',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Kopieret!',\n      actionLabel__copy: 'Kopier alle',\n      actionLabel__download: 'Download .txt',\n      actionLabel__print: 'Print',\n      infoText1: 'Backup-koder vil blive aktiveret for denne konto.',\n      infoText2:\n        'Hold backup-koderne hemmelige og gem dem sikkert. Du kan genskabe backup-koder, hvis du har mistanke om, at de er blevet kompromitteret.',\n      subtitle__codelist: 'Opbevar dem sikkert og hold dem hemmelige.',\n      successMessage:\n        'Backup-koder er nu aktiveret. Du kan bruge en af disse til at logge ind på din konto, hvis du mister adgangen til din totrinsbekræftelse. Hver kode kan kun bruges én gang.',\n      successSubtitle:\n        'Du kan bruge en af disse til at logge ind på din konto, hvis du mister adgangen til din totrinsbekræftelse.',\n      title: 'Tilføj bekræftelse af backup-kode',\n      title__codelist: 'Backup-koder',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Vælg en udbyder for at forbinde din konto.',\n      formHint__noAccounts: 'Der er ingen tilgængelige eksterne kontoudbydere.',\n      removeResource: {\n        messageLine1: '{{identifier}} vil blive fjernet fra denne konto.',\n        messageLine2:\n          'Du vil ikke længere være i stand til at bruge denne tilsluttede konto, og eventuelle afhængige funktioner vil ikke længere virke.',\n        successMessage: '{{connectedAccount}} er blevet fjernet fra din konto.',\n        title: 'Fjern tilsluttet konto',\n      },\n      socialButtonsBlockButton: 'Forbind {{provider|titleize}} konto',\n      successMessage: 'Udbyderen er blevet tilføjet til din konto',\n      title: 'Tilføj tilsluttet konto',\n    },\n    deletePage: {\n      actionDescription: 'Skriv \"Slet konto\" nedenfor for at fortsætte.',\n      confirm: 'Slet konto',\n      messageLine1: 'Er du sikker på, at du vil slette din konto?',\n      messageLine2: 'Denne handling er permanent og kan ikke fortrydes.',\n      title: 'Slet konto',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'En e-mail indeholdende en bekræftelseskode vil blive sendt til denne e-mailadresse.',\n        formSubtitle: 'Indtast bekræftelseskoden sendt til {{identifier}}',\n        formTitle: 'Bekræftelseskode',\n        resendButton: 'Send kode igen',\n        successMessage: 'E-mailen {{identifier}} er blevet tilføjet til din konto.',\n      },\n      emailLink: {\n        formHint: 'En e-mail indeholdende et bekræftelseslink vil blive sendt til denne e-mailadresse.',\n        formSubtitle: 'Klik på bekræftelseslinket i e-mailen sendt til {{identifier}}',\n        formTitle: 'Bekræftelseslink',\n        resendButton: 'Send link igen',\n        successMessage: 'E-mailen {{identifier}} er blevet tilføjet til din konto.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} vil blive fjernet fra denne konto.',\n        messageLine2: 'Du vil ikke længere kunne logge ind med denne e-mailadresse.',\n        successMessage: '{{emailAddress}} er blevet fjernet fra din konto.',\n        title: 'Fjern e-mailadresse',\n      },\n      title: 'Tilføj e-mailadresse',\n      verifyTitle: 'Verificer e-mailadresse',\n    },\n    formButtonPrimary__add: 'Tilføj',\n    formButtonPrimary__continue: 'Fortsæt',\n    formButtonPrimary__finish: 'Afslut',\n    formButtonPrimary__remove: 'Fjern',\n    formButtonPrimary__save: 'Gem',\n    formButtonReset: 'Annuller',\n    mfaPage: {\n      formHint: 'Vælg en metode, der skal tilføjes.',\n      title: 'Tilføj totrinsbekræftelse',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Brug eksisterende nummer',\n      primaryButton__addPhoneNumber: 'Tilføj et telefonnummer',\n      removeResource: {\n        messageLine1: '{{identifier}} vil ikke længere modtage bekræftelseskoder, når du logger ind.',\n        messageLine2: 'Din konto er muligvis ikke så sikker. Er du sikker på, at du vil fortsætte?',\n        successMessage: 'SMS-bekræftelse er blevet fjernet for {{mfaPhoneCode}}',\n        title: 'Fjern SMS-bekræftelse',\n      },\n      subtitle__availablePhoneNumbers:\n        'Vælg et telefonnummer for at registrere SMS-bekræftelse til totrinsbekræftelse.',\n      subtitle__unavailablePhoneNumbers:\n        'Der er ingen tilgængelige telefonnumre til at registrere til SMS-bekræftelse til totrinsbekræftelse.',\n      successMessage1:\n        'Når du logger ind, skal du indtaste en bekræftelseskode sendt til dette telefonnummer som et ekstra trin.',\n      successMessage2:\n        'Gem disse backup-koder og opbevar dem et sikkert sted. Hvis du mister adgangen til din godkendelsesenhed, kan du bruge backup-koder til at logge ind.',\n      successTitle: 'SMS-bekræftelse aktiveret',\n      title: 'Tilføj SMS-bekræftelse',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Scan QR-koden i stedet',\n        buttonUnableToScan__nonPrimary: 'Kan du ikke scanne QR-koden?',\n        infoText__ableToScan:\n          'Konfigurer en ny login-metode i din autentificeringsapp, og scan følgende QR-kode for at linke den til din konto.',\n        infoText__unableToScan: 'Konfigurer en ny login-metode i din autentificeringsapp, og indtast nøglen nedenfor.',\n        inputLabel__unableToScan1:\n          'Sørg for, at tidsbaserede eller engangsadgangskoder er aktiveret, og afslut derefter tilknytningen af din konto.',\n        inputLabel__unableToScan2:\n          \"Alternativt, hvis din autentificeringsapp understøtter TOTP URI'er, kan du også kopiere hele URI'en.\",\n      },\n      removeResource: {\n        messageLine1: 'Bekræftelseskoder fra denne autentificeringsapp kræves ikke længere, når du logger ind.',\n        messageLine2: 'Din konto er muligvis ikke så sikker. Er du sikker på, at du vil fortsætte?',\n        successMessage: 'Totrinsbekræftelse via autentificeringsapp er blevet fjernet.',\n        title: 'Fjern totrinsbekræftelse',\n      },\n      successMessage:\n        'Når du logger ind, skal du indtaste en bekræftelseskode fra denne autentificeringsapp som et ekstra trin.',\n      title: 'Tilføj autentificeringsapp',\n      verifySubtitle: 'Indtast bekræftelseskode, der er genereret af din autentificeringsapp',\n      verifyTitle: 'Bekræftelseskode',\n    },\n    mobileButton__menu: 'Menu',\n    navbar: {\n      account: 'Profil',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Administrer dine kontooplysninger.',\n      security: 'Sikkerhed',\n      title: 'Konto',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: 'Adgangsnøglen fjernes fra din konto.',\n        title: 'Fjern adgangsnøgle',\n      },\n      subtitle__rename: 'Omdøb adgangsnøgle',\n      title__rename: 'Omdøb adgangsnøgle',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Det anbefales at logge ud af alle andre enheder, der kan have brugt din gamle adgangskode.',\n      readonly:\n        'Din adgangskode kan i øjeblikket ikke redigeres, fordi du kun kan logge ind via virksomhedens forbindelse.',\n      successMessage__set: 'Din adgangskode er blevet indstillet.',\n      successMessage__signOutOfOtherSessions: 'Alle andre enheder er blevet logget ud.',\n      successMessage__update: 'Din adgangskode er blevet opdateret.',\n      title__set: 'Indstil adgangskode',\n      title__update: 'Skift adgangskode',\n    },\n    phoneNumberPage: {\n      infoText: 'En SMS, der indeholder et bekræftelseslink, sendes til dette telefonnummer.',\n      removeResource: {\n        messageLine1: '{{identifier}} vil blive fjernet fra denne konto.',\n        messageLine2: 'Du vil ikke længere kunne logge ind med dette telefonnummer.',\n        successMessage: '{{phoneNumber}} er blevet fjernet fra din konto.',\n        title: 'Fjern telefonnummer',\n      },\n      successMessage: '{{identifier}} er blevet tilføjet til din konto.',\n      title: 'Tilføj telefonnummer',\n      verifySubtitle: 'Indtast bekræftelseskoden sendt til {{identifier}}',\n      verifyTitle: 'Verificer telefonnummer',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Upload et JPG, PNG, GIF, eller WEBP-billede mindre end 10 MB',\n      imageFormDestructiveActionSubtitle: 'Fjern billede',\n      imageFormSubtitle: 'Upload billede',\n      imageFormTitle: 'Profilbillede',\n      readonly: 'Dine profiloplysninger er leveret af virksomhedens forbindelse og kan ikke redigeres.',\n      successMessage: 'Din profil er blevet opdateret.',\n      title: 'Profil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Log ud af enhed',\n        title: 'Aktive enheder',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Prøv igen',\n        actionLabel__reauthorize: 'Godkend nu',\n        destructiveActionTitle: 'Fjern',\n        primaryButton: 'Tilknyt konto',\n        subtitle__disconnected: 'Ingen tilknyttede konti',\n        subtitle__reauthorize:\n          'De krævede tilladelser er blevet opdateret, og du kan opleve begrænset funktionalitet. Godkend venligst denne applikation igen for at undgå eventuelle problemer',\n        title: 'Tilknyttede konti',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Slet Konto',\n        title: 'Konto afslutning',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Fjern e-mailadresse',\n        detailsAction__nonPrimary: 'Sæt som primær',\n        detailsAction__primary: 'Fuldfør bekræftelse',\n        detailsAction__unverified: 'Fuldfør bekræftelse',\n        primaryButton: 'Tilføj en e-mailadresse',\n        title: 'E-mailadresser',\n      },\n      enterpriseAccountsSection: {\n        title: 'Virksomhedskonti',\n      },\n      headerTitle__account: 'Konto',\n      headerTitle__security: 'Sikkerhed',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Generer koder',\n          headerTitle: 'Backup-koder',\n          subtitle__regenerate:\n            'Få et nyt sæt sikre backup-koder. Tidligere backup-koder vil blive slettet og kan ikke bruges.',\n          title__regenerate: 'Generer backup-koder',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Indstil som standard',\n          destructiveActionLabel: 'Fjern telefonnummer',\n        },\n        primaryButton: 'Tilføj totrinsbekræftelse',\n        title: 'Totrinsbekræftelse',\n        totp: {\n          destructiveActionTitle: 'Fjern',\n          headerTitle: 'Autentificeringsapp',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Fjern adgangsnøgle',\n        menuAction__rename: 'Omdøb adgangsnøgle',\n        primaryButton: undefined,\n        title: 'Adgangsnøgler',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Indtast adgangskode',\n        primaryButton__updatePassword: 'Skift adgangskode',\n        title: 'Adgangskode',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Fjern telefonnummer',\n        detailsAction__nonPrimary: 'Sæt som primær',\n        detailsAction__primary: 'Fuldfør bekræftelse',\n        detailsAction__unverified: 'Fuldfør bekræftelse',\n        primaryButton: 'Tilføj et telefonnummer',\n        title: 'Telefonnumre',\n      },\n      profileSection: {\n        primaryButton: 'Opdater profil',\n        title: 'Profil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Sæt brugernavn',\n        primaryButton__updateUsername: 'Skift brugernavn',\n        title: 'Brugernavn',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Fjern tegnebog',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Tilføj Web3 tegnebøger',\n        title: 'Web3 tegnebøger',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Dit brugernavn er blevet opdateret.',\n      title__set: 'Indstil brugernavn',\n      title__update: 'Opdater brugernavn',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} vil blive fjernet fra denne konto.',\n        messageLine2: 'Du vil ikke længere være i stand til at logge ind med denne web3-tegnebog.',\n        successMessage: '{{web3Wallet}} er blevet fjernet fra din konto.',\n        title: 'Fjern web3-tegnebog',\n      },\n      subtitle__availableWallets: 'Vælg en web3-tegnebog for at oprette forbindelse til din konto.',\n      subtitle__unavailableWallets: 'Der er ingen tilgængelige web3-tegnebøger.',\n      successMessage: 'Tegnebogen er blevet tilføjet til din konto.',\n      title: 'Tilføj web3-tegnebog',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBAAwB;AAAA,QACxB,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}