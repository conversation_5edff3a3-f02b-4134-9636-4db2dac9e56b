{"version": 3, "sources": ["../src/id-ID.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const idID: LocalizationResource = {\n  locale: 'id-ID',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Kembali',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Default',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Perangkat impersonator lain',\n  badge__primary: 'Utama',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Memerlukan tindakan',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Perangkat ini',\n  badge__unverified: 'Belum diverifikasi',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Perangkat pengguna',\n  badge__you: 'Anda',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Buat organisasi',\n    invitePage: {\n      formButtonReset: 'Lewati',\n    },\n    title: 'Buat organisasi',\n  },\n  dates: {\n    lastDay: \"Kemarin pada {{ date | timeString('id-ID') }}\",\n    next6Days: \"{{ date | weekday('id-ID','long') }} pada {{ date | timeString('id-ID') }}\",\n    nextDay: \"Besok pada {{ date | timeString('id-ID') }}\",\n    numeric: \"{{ date | numeric('id-ID') }}\",\n    previous6Days: \"{{ date | weekday('id-ID','long') }} lalu pada {{ date | timeString('id-ID') }}\",\n    sameDay: \"Hari ini pada {{ date | timeString('id-ID') }}\",\n  },\n  dividerText: 'atau',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Gunakan metode lain',\n  footerPageLink__help: 'Bantuan',\n  footerPageLink__privacy: 'Privasi',\n  footerPageLink__terms: 'Ketentuan',\n  formButtonPrimary: 'Lanjutkan',\n  formButtonPrimary__verify: 'Verifikasi',\n  formFieldAction__forgotPassword: 'Lupa kata sandi?',\n  formFieldError__matchingPasswords: 'Kata sandi cocok.',\n  formFieldError__notMatchingPasswords: 'Kata sandi tidak cocok.',\n  formFieldError__verificationLinkExpired: 'Tautan verifikasi telah kedaluwarsa. Silakan minta tautan baru.',\n  formFieldHintText__optional: 'Opsional',\n  formFieldHintText__slug: 'Slug adalah ID yang mudah dibaca dan harus unik. Biasanya digunakan dalam URL.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Hapus akun',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: 'organisasi-saya',\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Aktifkan undangan otomatis untuk domain ini',\n  formFieldLabel__backupCode: 'Kode cadangan',\n  formFieldLabel__confirmDeletion: 'Konfirmasi',\n  formFieldLabel__confirmPassword: 'Konfirmasi kata sandi',\n  formFieldLabel__currentPassword: 'Kata sandi saat ini',\n  formFieldLabel__emailAddress: 'Alamat email',\n  formFieldLabel__emailAddress_username: 'Alamat email atau nama pengguna',\n  formFieldLabel__emailAddresses: 'Alamat email',\n  formFieldLabel__firstName: 'Nama depan',\n  formFieldLabel__lastName: 'Nama belakang',\n  formFieldLabel__newPassword: 'Kata sandi baru',\n  formFieldLabel__organizationDomain: 'Domain',\n  formFieldLabel__organizationDomainDeletePending: 'Hapus undangan dan saran yang tertunda',\n  formFieldLabel__organizationDomainEmailAddress: 'Alamat email verifikasi',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Masukkan alamat email dengan domain ini untuk menerima kode dan memverifikasi domain ini.',\n  formFieldLabel__organizationName: 'Nama',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Nama passkey',\n  formFieldLabel__password: 'Kata sandi',\n  formFieldLabel__phoneNumber: 'Nomor telepon',\n  formFieldLabel__role: 'Peran',\n  formFieldLabel__signOutOfOtherSessions: 'Keluar dari semua perangkat lain',\n  formFieldLabel__username: 'Nama pengguna',\n  impersonationFab: {\n    action__signOut: 'Keluar',\n    title: 'Masuk sebagai {{identifier}}',\n  },\n  maintenanceMode:\n    'Kami sedang dalam pemeliharaan sistem, tapi jangan khawatir, ini tidak akan memakan waktu lebih dari beberapa menit.',\n  membershipRole__admin: 'Admin',\n  membershipRole__basicMember: 'Anggota',\n  membershipRole__guestMember: 'Tamu',\n  organizationList: {\n    action__createOrganization: 'Buat organisasi',\n    action__invitationAccept: 'Gabung',\n    action__suggestionsAccept: 'Minta bergabung',\n    createOrganization: 'Buat Organisasi',\n    invitationAcceptedLabel: 'Bergabung',\n    subtitle: 'untuk melanjutkan ke {{applicationName}}',\n    suggestionsAcceptedLabel: 'Menunggu persetujuan',\n    title: 'Pilih akun',\n    titleWithoutPersonal: 'Pilih organisasi',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Undangan otomatis',\n    badge__automaticSuggestion: 'Saran otomatis',\n    badge__manualInvitation: 'Tanpa pendaftaran otomatis',\n    badge__unverified: 'Belum diverifikasi',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Tambahkan domain untuk verifikasi. Pengguna dengan alamat email di domain ini dapat bergabung dengan organisasi secara otomatis atau meminta untuk bergabung.',\n      title: 'Tambah domain',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Undangan tidak dapat dikirim. Sudah ada undangan tertunda untuk alamat email berikut: {{email_addresses}}.',\n      formButtonPrimary__continue: 'Kirim undangan',\n      selectDropdown__role: 'Pilih peran',\n      subtitle: 'Masukkan atau tempel satu atau lebih alamat email, dipisahkan dengan spasi atau koma.',\n      successMessage: 'Undangan berhasil dikirim',\n      title: 'Undang anggota baru',\n    },\n    membersPage: {\n      action__invite: 'Undang',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Hapus anggota',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'Bergabung',\n        tableHeader__role: 'Peran',\n        tableHeader__user: 'Pengguna',\n      },\n      detailsTitle__emptyRow: 'Tidak ada anggota untuk ditampilkan',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Undang pengguna dengan menghubungkan domain email ke organisasi Anda. Siapa pun yang mendaftar dengan domain email yang cocok akan dapat bergabung dengan organisasi kapan saja.',\n          headerTitle: 'Undangan otomatis',\n          primaryButton: 'Kelola domain terverifikasi',\n        },\n        table__emptyRow: 'Tidak ada undangan untuk ditampilkan',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Batalkan undangan',\n        tableHeader__invited: 'Diundang',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Pengguna yang mendaftar dengan domain email yang cocok akan dapat melihat saran untuk meminta bergabung dengan organisasi Anda.',\n          headerTitle: 'Saran otomatis',\n          primaryButton: 'Kelola domain terverifikasi',\n        },\n        menuAction__approve: 'Setujui',\n        menuAction__reject: 'Tolak',\n        tableHeader__requested: 'Meminta akses',\n        table__emptyRow: 'Tidak ada permintaan untuk ditampilkan',\n      },\n      start: {\n        headerTitle__invitations: 'Undangan',\n        headerTitle__members: 'Anggota',\n        headerTitle__requests: 'Permintaan',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Kelola organisasi Anda.',\n      general: 'Umum',\n      members: 'Anggota',\n      title: 'Organisasi',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Ketik \"{{organizationName}}\" di bawah untuk melanjutkan.',\n          messageLine1: 'Anda yakin ingin menghapus organisasi ini?',\n          messageLine2: 'Tindakan ini permanen dan tidak dapat dibatalkan.',\n          successMessage: 'Anda telah menghapus organisasi.',\n          title: 'Hapus organisasi',\n        },\n        leaveOrganization: {\n          actionDescription: 'Ketik \"{{organizationName}}\" di bawah untuk melanjutkan.',\n          messageLine1:\n            'Anda yakin ingin meninggalkan organisasi ini? Anda akan kehilangan akses ke organisasi ini dan aplikasinya.',\n          messageLine2: 'Tindakan ini permanen dan tidak dapat dibatalkan.',\n          successMessage: 'Anda telah meninggalkan organisasi.',\n          title: 'Tinggalkan organisasi',\n        },\n        title: 'Bahaya',\n      },\n      domainSection: {\n        menuAction__manage: 'Kelola',\n        menuAction__remove: 'Hapus',\n        menuAction__verify: 'Verifikasi',\n        primaryButton: 'Tambah domain',\n        subtitle:\n          'Izinkan pengguna untuk bergabung dengan organisasi secara otomatis atau meminta bergabung berdasarkan domain email terverifikasi.',\n        title: 'Domain terverifikasi',\n      },\n      successMessage: 'Organisasi telah diperbarui.',\n      title: 'Perbarui profil',\n    },\n    removeDomainPage: {\n      messageLine1: 'Domain email {{domain}} akan dihapus.',\n      messageLine2: 'Pengguna tidak akan dapat bergabung dengan organisasi secara otomatis setelah ini.',\n      successMessage: '{{domain}} telah dihapus.',\n      title: 'Hapus domain',\n    },\n    start: {\n      headerTitle__general: 'Umum',\n      headerTitle__members: 'Anggota',\n      profileSection: {\n        primaryButton: 'Perbarui profil',\n        title: 'Profil Organisasi',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Menghapus domain ini akan mempengaruhi pengguna yang diundang.',\n        removeDomainActionLabel__remove: 'Hapus domain',\n        removeDomainSubtitle: 'Hapus domain ini dari domain terverifikasi Anda',\n        removeDomainTitle: 'Hapus domain',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Pengguna otomatis diundang untuk bergabung dengan organisasi saat mereka mendaftar dan dapat bergabung kapan saja.',\n        automaticInvitationOption__label: 'Undangan otomatis',\n        automaticSuggestionOption__description:\n          'Pengguna menerima saran untuk meminta bergabung, tetapi harus disetujui oleh admin sebelum mereka dapat bergabung dengan organisasi.',\n        automaticSuggestionOption__label: 'Saran otomatis',\n        calloutInfoLabel: 'Mengubah mode pendaftaran hanya akan mempengaruhi pengguna baru.',\n        calloutInvitationCountLabel: 'Undangan tertunda yang dikirim ke pengguna: {{count}}',\n        calloutSuggestionCountLabel: 'Saran tertunda yang dikirim ke pengguna: {{count}}',\n        manualInvitationOption__description: 'Pengguna hanya dapat diundang secara manual ke organisasi.',\n        manualInvitationOption__label: 'Tanpa pendaftaran otomatis',\n        subtitle: 'Pilih bagaimana pengguna dari domain ini dapat bergabung dengan organisasi.',\n      },\n      start: {\n        headerTitle__danger: 'Bahaya',\n        headerTitle__enrollment: 'Opsi pendaftaran',\n      },\n      subtitle: 'Domain {{domain}} sekarang terverifikasi. Lanjutkan dengan memilih mode pendaftaran.',\n      title: 'Perbarui {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Masukkan kode verifikasi yang dikirim ke alamat email Anda',\n      formTitle: 'Kode verifikasi',\n      resendButton: 'Tidak menerima kode? Kirim ulang',\n      subtitle: 'Domain {{domainName}} perlu diverifikasi melalui email.',\n      subtitleVerificationCodeScreen:\n        'Kode verifikasi telah dikirim ke {{emailAddress}}. Masukkan kode untuk melanjutkan.',\n      title: 'Verifikasi domain',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Buat organisasi',\n    action__invitationAccept: 'Gabung',\n    action__manageOrganization: 'Kelola',\n    action__suggestionsAccept: 'Minta bergabung',\n    notSelected: 'Tidak ada organisasi dipilih',\n    personalWorkspace: 'Akun pribadi',\n    suggestionsAcceptedLabel: 'Menunggu persetujuan',\n  },\n  paginationButton__next: 'Berikutnya',\n  paginationButton__previous: 'Sebelumnya',\n  paginationRowText__displaying: 'Menampilkan',\n  paginationRowText__of: 'dari',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Dapatkan bantuan',\n      actionText: 'Tidak memiliki salah satu dari ini?',\n      blockButton__backupCode: 'Gunakan kode cadangan',\n      blockButton__emailCode: 'Kirim kode ke {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Lanjutkan dengan kata sandi Anda',\n      blockButton__phoneCode: 'Kirim kode SMS ke {{identifier}}',\n      blockButton__totp: 'Gunakan aplikasi autentikator Anda',\n      getHelp: {\n        blockButton__emailSupport: 'Email dukungan',\n        content:\n          'Jika Anda kesulitan memverifikasi akun, email kami dan kami akan membantu memulihkan akses secepat mungkin.',\n        title: 'Dapatkan bantuan',\n      },\n      subtitle: 'Mengalami masalah? Anda dapat menggunakan salah satu metode ini untuk verifikasi.',\n      title: 'Gunakan metode lain',\n    },\n    backupCodeMfa: {\n      subtitle: 'Masukkan kode cadangan yang Anda terima saat menyiapkan autentikasi dua langkah',\n      title: 'Masukkan kode cadangan',\n    },\n    emailCode: {\n      formTitle: 'Kode verifikasi',\n      resendButton: 'Tidak menerima kode? Kirim ulang',\n      subtitle: 'Masukkan kode yang dikirim ke email Anda untuk melanjutkan',\n      title: 'Verifikasi diperlukan',\n    },\n    noAvailableMethods: {\n      message: 'Tidak dapat melanjutkan verifikasi. Tidak ada faktor autentikasi yang sesuai dikonfigurasi',\n      subtitle: 'Terjadi kesalahan',\n      title: 'Tidak dapat memverifikasi akun Anda',\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Gunakan metode lain',\n      subtitle: 'Masukkan kata sandi Anda untuk melanjutkan',\n      title: 'Verifikasi diperlukan',\n    },\n    phoneCode: {\n      formTitle: 'Kode verifikasi',\n      resendButton: 'Tidak menerima kode? Kirim ulang',\n      subtitle: 'Masukkan kode yang dikirim ke telepon Anda untuk melanjutkan',\n      title: 'Verifikasi diperlukan',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Kode verifikasi',\n      resendButton: 'Tidak menerima kode? Kirim ulang',\n      subtitle: 'Masukkan kode yang dikirim ke telepon Anda untuk melanjutkan',\n      title: 'Verifikasi diperlukan',\n    },\n    totpMfa: {\n      formTitle: 'Kode verifikasi',\n      subtitle: 'Masukkan kode yang dihasilkan oleh aplikasi autentikator Anda untuk melanjutkan',\n      title: 'Verifikasi diperlukan',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Tambah akun',\n      action__signOutAll: 'Keluar dari semua akun',\n      subtitle: 'Pilih akun yang ingin Anda gunakan untuk melanjutkan.',\n      title: 'Pilih akun',\n    },\n    alternativeMethods: {\n      actionLink: 'Dapatkan bantuan',\n      actionText: 'Tidak memiliki metode ini?',\n      blockButton__backupCode: 'Gunakan kode cadangan',\n      blockButton__emailCode: 'Kirim kode ke {{identifier}}',\n      blockButton__emailLink: 'Kirim tautan ke {{identifier}}',\n      blockButton__passkey: 'Masuk dengan passkey Anda',\n      blockButton__password: 'Masuk dengan kata sandi',\n      blockButton__phoneCode: 'Kirim kode SMS ke {{identifier}}',\n      blockButton__totp: 'Gunakan aplikasi autentikator',\n      getHelp: {\n        blockButton__emailSupport: 'Email dukungan',\n        content:\n          'Jika Anda kesulitan masuk ke akun, email kami dan kami akan membantu memulihkan akses secepat mungkin.',\n        title: 'Dapatkan bantuan',\n      },\n      subtitle: 'Mengalami masalah? Anda dapat menggunakan salah satu metode ini untuk masuk.',\n      title: 'Gunakan metode lain',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'Kode cadangan Anda adalah yang Anda dapatkan saat menyiapkan verifikasi dua langkah.',\n      title: 'Masukkan kode cadangan',\n    },\n    emailCode: {\n      formTitle: 'Kode verifikasi',\n      resendButton: 'Tidak menerima kode? Kirim ulang',\n      subtitle: 'untuk melanjutkan ke {{applicationName}}',\n      title: 'Periksa email Anda',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Untuk melanjutkan, buka tautan verifikasi di perangkat dan browser yang Anda gunakan untuk memulai masuk',\n        title: 'Tautan verifikasi tidak valid untuk perangkat ini',\n      },\n      expired: {\n        subtitle: 'Kembali ke tab asal untuk melanjutkan.',\n        title: 'Tautan verifikasi ini telah kedaluwarsa',\n      },\n      failed: {\n        subtitle: 'Kembali ke tab asal untuk melanjutkan.',\n        title: 'Tautan verifikasi ini tidak valid',\n      },\n      formSubtitle: 'Gunakan tautan verifikasi yang dikirim ke email Anda',\n      formTitle: 'Tautan verifikasi',\n      loading: {\n        subtitle: 'Anda akan segera dialihkan',\n        title: 'Sedang masuk...',\n      },\n      resendButton: 'Tidak menerima tautan? Kirim ulang',\n      subtitle: 'untuk melanjutkan ke {{applicationName}}',\n      title: 'Periksa email Anda',\n      unusedTab: {\n        title: 'Anda dapat menutup tab ini',\n      },\n      verified: {\n        subtitle: 'Anda akan segera dialihkan',\n        title: 'Berhasil masuk',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Kembali ke tab asal untuk melanjutkan',\n        subtitleNewTab: 'Kembali ke tab yang baru dibuka untuk melanjutkan',\n        titleNewTab: 'Masuk di tab lain',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Kode reset kata sandi',\n      resendButton: 'Tidak menerima kode? Kirim ulang',\n      subtitle: 'untuk mereset kata sandi Anda',\n      subtitle_email: 'Pertama, masukkan kode yang dikirim ke alamat email Anda',\n      subtitle_phone: 'Pertama, masukkan kode yang dikirim ke telepon Anda',\n      title: 'Reset kata sandi',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Reset kata sandi Anda',\n      label__alternativeMethods: 'Atau, masuk dengan metode lain',\n      title: 'Lupa Kata Sandi?',\n    },\n    noAvailableMethods: {\n      message: 'Tidak dapat melanjutkan masuk. Tidak ada faktor autentikasi yang tersedia.',\n      subtitle: 'Terjadi kesalahan',\n      title: 'Tidak dapat masuk',\n    },\n    passkey: {\n      subtitle:\n        'Menggunakan passkey mengonfirmasi bahwa ini adalah Anda. Perangkat Anda mungkin meminta sidik jari, wajah, atau kunci layar.',\n      title: 'Gunakan passkey Anda',\n    },\n    password: {\n      actionLink: 'Gunakan metode lain',\n      subtitle: 'Masukkan kata sandi yang terkait dengan akun Anda',\n      title: 'Masukkan kata sandi Anda',\n    },\n    passwordPwned: {\n      title: 'Kata sandi terkompromi',\n    },\n    phoneCode: {\n      formTitle: 'Kode verifikasi',\n      resendButton: 'Tidak menerima kode? Kirim ulang',\n      subtitle: 'untuk melanjutkan ke {{applicationName}}',\n      title: 'Periksa telepon Anda',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Kode verifikasi',\n      resendButton: 'Tidak menerima kode? Kirim ulang',\n      subtitle: 'Untuk melanjutkan, masukkan kode verifikasi yang dikirim ke telepon Anda',\n      title: 'Periksa telepon Anda',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Reset Kata Sandi',\n      requiredMessage: 'Untuk alasan keamanan, Anda harus mereset kata sandi.',\n      successMessage: 'Kata sandi Anda berhasil diubah. Sedang memasukkan Anda, mohon tunggu sebentar.',\n      title: 'Atur kata sandi baru',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Kami perlu memverifikasi identitas Anda sebelum mereset kata sandi.',\n    },\n    start: {\n      actionLink: 'Daftar',\n      actionLink__join_waitlist: 'Gabung daftar tunggu',\n      actionLink__use_email: 'Gunakan email',\n      actionLink__use_email_username: 'Gunakan email atau nama pengguna',\n      actionLink__use_passkey: 'Gunakan passkey sebagai gantinya',\n      actionLink__use_phone: 'Gunakan telepon',\n      actionLink__use_username: 'Gunakan nama pengguna',\n      actionText: 'Belum punya akun?',\n      actionText__join_waitlist: 'Ingin akses awal?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Selamat datang kembali! Silakan masuk untuk melanjutkan',\n      subtitleCombined: undefined,\n      title: 'Masuk ke {{applicationName}}',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Kode verifikasi',\n      subtitle: 'Untuk melanjutkan, masukkan kode verifikasi yang dihasilkan oleh aplikasi autentikator Anda',\n      title: 'Verifikasi dua langkah',\n    },\n  },\n  signInEnterPasswordTitle: 'Masukkan kata sandi Anda',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Masuk',\n      actionText: 'Sudah punya akun?',\n      subtitle: 'Silakan isi detail yang tersisa untuk melanjutkan.',\n      title: 'Isi kolom yang kosong',\n    },\n    emailCode: {\n      formSubtitle: 'Masukkan kode verifikasi yang dikirim ke alamat email Anda',\n      formTitle: 'Kode verifikasi',\n      resendButton: 'Tidak menerima kode? Kirim ulang',\n      subtitle: 'Masukkan kode verifikasi yang dikirim ke email Anda',\n      title: 'Verifikasi email Anda',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Untuk melanjutkan, buka tautan verifikasi di perangkat dan browser yang Anda gunakan untuk memulai pendaftaran',\n        title: 'Tautan verifikasi tidak valid untuk perangkat ini',\n      },\n      formSubtitle: 'Gunakan tautan verifikasi yang dikirim ke alamat email Anda',\n      formTitle: 'Tautan verifikasi',\n      loading: {\n        title: 'Sedang mendaftar...',\n      },\n      resendButton: 'Tidak menerima tautan? Kirim ulang',\n      subtitle: 'untuk melanjutkan ke {{applicationName}}',\n      title: 'Verifikasi email Anda',\n      verified: {\n        title: 'Berhasil mendaftar',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Kembali ke tab yang baru dibuka untuk melanjutkan',\n        subtitleNewTab: 'Kembali ke tab sebelumnya untuk melanjutkan',\n        title: 'Berhasil memverifikasi email',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Saya menyetujui {{ privacyPolicyLink || link(\"Kebijakan Privasi\") }}',\n        label__onlyTermsOfService: 'Saya menyetujui {{ termsOfServiceLink || link(\"Ketentuan Layanan\") }}',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Saya menyetujui {{ termsOfServiceLink || link(\"Ketentuan Layanan\") }} dan {{ privacyPolicyLink || link(\"Kebijakan Privasi\") }}',\n      },\n      continue: {\n        subtitle: 'Silakan baca dan setujui ketentuan untuk melanjutkan',\n        title: 'Persetujuan hukum',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Masukkan kode verifikasi yang dikirim ke nomor telepon Anda',\n      formTitle: 'Kode verifikasi',\n      resendButton: 'Tidak menerima kode? Kirim ulang',\n      subtitle: 'Masukkan kode verifikasi yang dikirim ke telepon Anda',\n      title: 'Verifikasi telepon Anda',\n    },\n    restrictedAccess: {\n      actionLink: 'Masuk',\n      actionText: 'Sudah punya akun?',\n      blockButton__emailSupport: 'Email dukungan',\n      blockButton__joinWaitlist: 'Gabung daftar tunggu',\n      subtitle:\n        'Pendaftaran saat ini dinonaktifkan. Jika Anda yakin seharusnya memiliki akses, silakan hubungi dukungan.',\n      subtitleWaitlist:\n        'Pendaftaran saat ini dinonaktifkan. Untuk menjadi yang pertama tahu saat kami meluncur, bergabunglah dengan daftar tunggu.',\n      title: 'Akses dibatasi',\n    },\n    start: {\n      actionLink: 'Masuk',\n      actionLink__use_email: 'Gunakan email sebagai gantinya',\n      actionLink__use_phone: 'Gunakan telepon sebagai gantinya',\n      actionText: 'Sudah punya akun?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Selamat datang! Silakan isi detail untuk memulai.',\n      subtitleCombined: 'Selamat datang! Silakan isi detail untuk memulai.',\n      title: 'Buat akun Anda',\n      titleCombined: 'Buat akun Anda',\n    },\n  },\n  socialButtonsBlockButton: 'Lanjutkan dengan {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: '{{email}} sudah menjadi anggota organisasi.',\n    captcha_invalid:\n      'Pendaftaran gagal karena validasi keamanan gagal. Silakan muat ulang halaman untuk mencoba lagi atau hubungi dukungan untuk bantuan lebih lanjut.',\n    captcha_unavailable:\n      'Pendaftaran gagal karena validasi bot gagal. Silakan muat ulang halaman untuk mencoba lagi atau hubungi dukungan untuk bantuan lebih lanjut.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: 'Alamat email ini sudah digunakan. Silakan coba yang lain.',\n    form_identifier_exists__phone_number: 'Nomor telepon ini sudah digunakan. Silakan coba yang lain.',\n    form_identifier_exists__username: 'Nama pengguna ini sudah digunakan. Silakan coba yang lain.',\n    form_identifier_not_found: 'Kami tidak dapat menemukan akun dengan detail tersebut.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'Alamat email harus berupa alamat email yang valid.',\n    form_param_format_invalid__phone_number: 'Nomor telepon harus dalam format internasional yang valid',\n    form_param_max_length_exceeded__first_name: 'Nama depan tidak boleh lebih dari 256 karakter.',\n    form_param_max_length_exceeded__last_name: 'Nama belakang tidak boleh lebih dari 256 karakter.',\n    form_param_max_length_exceeded__name: 'Nama tidak boleh lebih dari 256 karakter.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: 'Kata sandi Anda tidak cukup kuat.',\n    form_password_pwned:\n      'Kata sandi ini telah ditemukan sebagai bagian dari kebocoran data dan tidak dapat digunakan, silakan coba kata sandi lain.',\n    form_password_pwned__sign_in:\n      'Kata sandi ini telah ditemukan sebagai bagian dari kebocoran data dan tidak dapat digunakan, silakan reset kata sandi Anda.',\n    form_password_size_in_bytes_exceeded:\n      'Kata sandi Anda telah melebihi jumlah byte maksimum yang diizinkan, silakan persingkat atau hapus beberapa karakter khusus.',\n    form_password_validation_failed: 'Kata Sandi Salah',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: 'Anda tidak dapat menghapus identifikasi terakhir Anda.',\n    not_allowed_access:\n      \"Alamat email atau nomor telepon tidak diizinkan untuk mendaftar. Ini mungkin disebabkan oleh penggunaan '+', '=', '#' atau '.' dalam alamat email Anda, penggunaan domain yang terhubung dengan layanan email sementara, atau pengecualian eksplisit. Jika Anda menganggap ini sebagai kesalahan, silakan hubungi dukungan.\",\n    organization_domain_blocked: 'Ini adalah domain penyedia email yang diblokir. Silakan gunakan yang lain.',\n    organization_domain_common: 'Ini adalah domain penyedia email umum. Silakan gunakan yang lain.',\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded:\n      'Anda telah mencapai batas keanggotaan organisasi, termasuk undangan yang belum selesai.',\n    organization_minimum_permissions_needed:\n      'Harus ada setidaknya satu anggota organisasi dengan izin minimum yang diperlukan.',\n    passkey_already_exists: 'Passkey sudah terdaftar di perangkat ini.',\n    passkey_not_supported: 'Passkey tidak didukung di perangkat ini.',\n    passkey_pa_not_supported: 'Pendaftaran memerlukan platform autentikator tetapi perangkat tidak mendukungnya.',\n    passkey_registration_cancelled: 'Pendaftaran passkey dibatalkan atau waktu habis.',\n    passkey_retrieval_cancelled: 'Verifikasi passkey dibatalkan atau waktu habis.',\n    passwordComplexity: {\n      maximumLength: 'kurang dari {{length}} karakter',\n      minimumLength: '{{length}} karakter atau lebih',\n      requireLowercase: 'huruf kecil',\n      requireNumbers: 'angka',\n      requireSpecialCharacter: 'karakter khusus',\n      requireUppercase: 'huruf besar',\n      sentencePrefix: 'Kata sandi Anda harus mengandung',\n    },\n    phone_number_exists: 'Nomor telepon ini sudah digunakan. Silakan coba yang lain.',\n    session_exists: 'Anda sudah masuk.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Kata sandi Anda berfungsi, tapi bisa lebih kuat. Coba tambahkan lebih banyak karakter.',\n      goodPassword: 'Kata sandi Anda memenuhi semua persyaratan yang diperlukan.',\n      notEnough: 'Kata sandi Anda tidak cukup kuat.',\n      suggestions: {\n        allUppercase: 'Kapitalisasi beberapa, tapi tidak semua huruf.',\n        anotherWord: 'Tambahkan lebih banyak kata yang kurang umum.',\n        associatedYears: 'Hindari tahun yang terkait dengan Anda.',\n        capitalization: 'Kapitalisasi lebih dari huruf pertama.',\n        dates: 'Hindari tanggal dan tahun yang terkait dengan Anda.',\n        l33t: \"Hindari penggantian huruf yang dapat diprediksi seperti '@' untuk 'a'.\",\n        longerKeyboardPattern: 'Gunakan pola keyboard yang lebih panjang dan ubah arah pengetikan beberapa kali.',\n        noNeed: 'Anda dapat membuat kata sandi yang kuat tanpa menggunakan simbol, angka, atau huruf besar.',\n        pwned: 'Jika Anda menggunakan kata sandi ini di tempat lain, Anda harus mengubahnya.',\n        recentYears: 'Hindari tahun-tahun terakhir.',\n        repeated: 'Hindari kata dan karakter yang berulang.',\n        reverseWords: 'Hindari ejaan terbalik dari kata-kata umum.',\n        sequences: 'Hindari urutan karakter umum.',\n        useWords: 'Gunakan beberapa kata, tapi hindari frasa umum.',\n      },\n      warnings: {\n        common: 'Ini adalah kata sandi yang umum digunakan.',\n        commonNames: 'Nama dan nama keluarga yang umum mudah ditebak.',\n        dates: 'Tanggal mudah ditebak.',\n        extendedRepeat: 'Pola karakter berulang seperti \"abcabcabc\" mudah ditebak.',\n        keyPattern: 'Pola keyboard pendek mudah ditebak.',\n        namesByThemselves: 'Nama tunggal atau nama keluarga mudah ditebak.',\n        pwned: 'Kata sandi Anda telah terekspos oleh kebocoran data di Internet.',\n        recentYears: 'Tahun-tahun terakhir mudah ditebak.',\n        sequences: 'Urutan karakter umum seperti \"abc\" mudah ditebak.',\n        similarToCommon: 'Ini mirip dengan kata sandi yang umum digunakan.',\n        simpleRepeat: 'Karakter berulang seperti \"aaa\" mudah ditebak.',\n        straightRow: 'Baris lurus tombol di keyboard Anda mudah ditebak.',\n        topHundred: 'Ini adalah kata sandi yang sering digunakan.',\n        topTen: 'Ini adalah kata sandi yang sangat sering digunakan.',\n        userInputs: 'Seharusnya tidak ada data pribadi atau terkait halaman.',\n        wordByItself: 'Kata tunggal mudah ditebak.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Tambah akun',\n    action__manageAccount: 'Kelola akun',\n    action__signOut: 'Keluar',\n    action__signOutAll: 'Keluar dari semua akun',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Disalin!',\n      actionLabel__copy: 'Salin semua',\n      actionLabel__download: 'Unduh .txt',\n      actionLabel__print: 'Cetak',\n      infoText1: 'Kode cadangan akan diaktifkan untuk akun ini.',\n      infoText2:\n        'Jaga kerahasiaan kode cadangan dan simpan dengan aman. Anda dapat membuat ulang kode cadangan jika mencurigai telah disusupi.',\n      subtitle__codelist: 'Simpan dengan aman dan jaga kerahasiaannya.',\n      successMessage:\n        'Kode cadangan sekarang diaktifkan. Anda dapat menggunakan salah satu dari ini untuk masuk ke akun Anda, jika kehilangan akses ke perangkat autentikasi. Setiap kode hanya dapat digunakan sekali.',\n      successSubtitle:\n        'Anda dapat menggunakan salah satu dari ini untuk masuk ke akun Anda, jika kehilangan akses ke perangkat autentikasi.',\n      title: 'Tambah verifikasi kode cadangan',\n      title__codelist: 'Kode cadangan',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Pilih penyedia untuk menghubungkan akun Anda.',\n      formHint__noAccounts: 'Tidak ada penyedia akun eksternal yang tersedia.',\n      removeResource: {\n        messageLine1: '{{identifier}} akan dihapus dari akun ini.',\n        messageLine2:\n          'Anda tidak akan dapat menggunakan akun terhubung ini dan fitur terkait tidak akan berfungsi lagi.',\n        successMessage: '{{connectedAccount}} telah dihapus dari akun Anda.',\n        title: 'Hapus akun terhubung',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}',\n      successMessage: 'Penyedia telah ditambahkan ke akun Anda',\n      title: 'Tambah akun terhubung',\n    },\n    deletePage: {\n      actionDescription: 'Ketik \"Delete account\" di bawah untuk melanjutkan.',\n      confirm: 'Hapus akun',\n      messageLine1: 'Anda yakin ingin menghapus akun Anda?',\n      messageLine2: 'Tindakan ini permanen dan tidak dapat dibatalkan.',\n      title: 'Hapus akun',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Email berisi kode verifikasi akan dikirim ke alamat email ini.',\n        formSubtitle: 'Masukkan kode verifikasi yang dikirim ke {{identifier}}',\n        formTitle: 'Kode verifikasi',\n        resendButton: 'Tidak menerima kode? Kirim ulang',\n        successMessage: 'Email {{identifier}} telah ditambahkan ke akun Anda.',\n      },\n      emailLink: {\n        formHint: 'Email berisi tautan verifikasi akan dikirim ke alamat email ini.',\n        formSubtitle: 'Klik tautan verifikasi di email yang dikirim ke {{identifier}}',\n        formTitle: 'Tautan verifikasi',\n        resendButton: 'Tidak menerima tautan? Kirim ulang',\n        successMessage: 'Email {{identifier}} telah ditambahkan ke akun Anda.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} akan dihapus dari akun ini.',\n        messageLine2: 'Anda tidak akan dapat masuk menggunakan alamat email ini.',\n        successMessage: '{{emailAddress}} telah dihapus dari akun Anda.',\n        title: 'Hapus alamat email',\n      },\n      title: 'Tambah alamat email',\n      verifyTitle: 'Verifikasi alamat email',\n    },\n    formButtonPrimary__add: 'Tambah',\n    formButtonPrimary__continue: 'Lanjutkan',\n    formButtonPrimary__finish: 'Selesai',\n    formButtonPrimary__remove: 'Hapus',\n    formButtonPrimary__save: 'Simpan',\n    formButtonReset: 'Batal',\n    mfaPage: {\n      formHint: 'Pilih metode untuk ditambahkan.',\n      title: 'Tambah verifikasi dua langkah',\n    },\n    mfaPhoneCodePage: {\n      backButton: undefined,\n      primaryButton__addPhoneNumber: undefined,\n      removeResource: {\n        messageLine1: undefined,\n        messageLine2: undefined,\n        successMessage: undefined,\n        title: undefined,\n      },\n      subtitle__availablePhoneNumbers: undefined,\n      subtitle__unavailablePhoneNumbers: undefined,\n      successMessage1: undefined,\n      successMessage2: undefined,\n      successTitle: undefined,\n      title: undefined,\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: undefined,\n        buttonUnableToScan__nonPrimary: undefined,\n        infoText__ableToScan: undefined,\n        infoText__unableToScan: undefined,\n        inputLabel__unableToScan1: undefined,\n        inputLabel__unableToScan2: undefined,\n      },\n      removeResource: {\n        messageLine1: undefined,\n        messageLine2: undefined,\n        successMessage: undefined,\n        title: undefined,\n      },\n      successMessage: undefined,\n      title: undefined,\n      verifySubtitle: undefined,\n      verifyTitle: undefined,\n    },\n    mobileButton__menu: undefined,\n    navbar: {\n      account: 'Profil',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Kelola info akun Anda.',\n      security: 'Keamanan',\n      title: 'Akun',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: undefined,\n        title: undefined,\n      },\n      subtitle__rename: undefined,\n      title__rename: undefined,\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Disarankan untuk keluar dari semua perangkat lain yang mungkin menggunakan kata sandi lama Anda.',\n      readonly: 'Kata sandi Anda saat ini tidak dapat diedit karena Anda hanya dapat masuk melalui koneksi enterprise.',\n      successMessage__set: 'Kata sandi Anda telah diatur.',\n      successMessage__signOutOfOtherSessions: 'Semua perangkat lain telah dikeluarkan.',\n      successMessage__update: 'Kata sandi Anda telah diperbarui.',\n      title__set: 'Atur kata sandi',\n      title__update: 'Perbarui kata sandi',\n    },\n    phoneNumberPage: {\n      infoText:\n        'Pesan teks berisi kode verifikasi akan dikirim ke nomor telepon ini. Biaya pesan dan data mungkin berlaku.',\n      removeResource: {\n        messageLine1: '{{identifier}} akan dihapus dari akun ini.',\n        messageLine2: 'Anda tidak akan dapat masuk menggunakan nomor telepon ini.',\n        successMessage: '{{phoneNumber}} telah dihapus dari akun Anda.',\n        title: 'Hapus nomor telepon',\n      },\n      successMessage: '{{identifier}} telah ditambahkan ke akun Anda.',\n      title: 'Tambah nomor telepon',\n      verifySubtitle: 'Masukkan kode verifikasi yang dikirim ke {{identifier}}',\n      verifyTitle: 'Verifikasi nomor telepon',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Ukuran yang disarankan 1:1, hingga 10MB.',\n      imageFormDestructiveActionSubtitle: 'Hapus',\n      imageFormSubtitle: 'Unggah',\n      imageFormTitle: 'Gambar profil',\n      readonly: 'Informasi profil Anda telah disediakan oleh koneksi enterprise dan tidak dapat diedit.',\n      successMessage: 'Profil Anda telah diperbarui.',\n      title: 'Perbarui profil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: undefined,\n        title: undefined,\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: undefined,\n        actionLabel__reauthorize: undefined,\n        destructiveActionTitle: undefined,\n        primaryButton: undefined,\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize: undefined,\n        title: undefined,\n      },\n      dangerSection: {\n        deleteAccountButton: undefined,\n        title: undefined,\n      },\n      emailAddressesSection: {\n        destructiveAction: undefined,\n        detailsAction__nonPrimary: undefined,\n        detailsAction__primary: undefined,\n        detailsAction__unverified: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      enterpriseAccountsSection: {\n        title: undefined,\n      },\n      headerTitle__account: undefined,\n      headerTitle__security: undefined,\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: undefined,\n          headerTitle: undefined,\n          subtitle__regenerate: undefined,\n          title__regenerate: undefined,\n        },\n        phoneCode: {\n          actionLabel__setDefault: undefined,\n          destructiveActionLabel: undefined,\n        },\n        primaryButton: undefined,\n        title: undefined,\n        totp: {\n          destructiveActionTitle: undefined,\n          headerTitle: undefined,\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: undefined,\n        menuAction__rename: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      passwordSection: {\n        primaryButton__setPassword: undefined,\n        primaryButton__updatePassword: undefined,\n        title: undefined,\n      },\n      phoneNumbersSection: {\n        destructiveAction: undefined,\n        detailsAction__nonPrimary: undefined,\n        detailsAction__primary: undefined,\n        detailsAction__unverified: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      profileSection: {\n        primaryButton: undefined,\n        title: undefined,\n      },\n      usernameSection: {\n        primaryButton__setUsername: undefined,\n        primaryButton__updateUsername: undefined,\n        title: undefined,\n      },\n      web3WalletsSection: {\n        destructiveAction: undefined,\n        detailsAction__nonPrimary: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n    },\n    usernamePage: {\n      successMessage: undefined,\n      title__set: undefined,\n      title__update: undefined,\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: undefined,\n        messageLine2: undefined,\n        successMessage: undefined,\n        title: undefined,\n      },\n      subtitle__availableWallets: undefined,\n      subtitle__unavailableWallets: undefined,\n      successMessage: undefined,\n      title: undefined,\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: 'Masuk',\n      actionText: 'Sudah punya akses?',\n      formButton: 'Gabung daftar tunggu',\n      subtitle: 'Masukkan alamat email Anda dan kami akan memberi tahu ketika tempat Anda siap',\n      title: 'Gabung daftar tunggu',\n    },\n    success: {\n      message: 'Anda akan segera dialihkan...',\n      subtitle: 'Kami akan menghubungi ketika tempat Anda siap',\n      title: 'Terima kasih telah bergabung dengan daftar tunggu!',\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBACE;AAAA,EACF,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UACE;AAAA,MACF,kBACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BACE;AAAA,IACF,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCACE;AAAA,IACF,yCACE;AAAA,IACF,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCAAiC;AAAA,MACjC,mCAAmC;AAAA,MACnC,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,MAC7B;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UACE;AAAA,MACF,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBAAsB;AAAA,UACtB,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}