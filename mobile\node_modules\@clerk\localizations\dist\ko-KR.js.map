{"version": 3, "sources": ["../src/ko-KR.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const koKR: LocalizationResource = {\n  locale: 'ko-KR',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: '돌아가기',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: '기본값',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: '기타 사칭 장치',\n  badge__primary: '기본',\n  badge__renewsAt: undefined,\n  badge__requiresAction: '조치 필요',\n  badge__startsAt: undefined,\n  badge__thisDevice: '이 장치',\n  badge__unverified: '미확인',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: '사용자 장치',\n  badge__you: '당신',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: '조직 만들기',\n    invitePage: {\n      formButtonReset: '넘기기',\n    },\n    title: '조직 만들기',\n  },\n  dates: {\n    lastDay: \"어제 {{ date | timeString('ko-KR') }}\",\n    next6Days: \"다음 {{ date | weekday('ko-KR','long') }} {{ date | timeString('ko-KR') }}\",\n    nextDay: \"내일 {{ date | timeString('ko-KR') }}\",\n    numeric: \"{{ date | numeric('ko-KR') }}\",\n    previous6Days: \"지난 {{ date | weekday('ko-KR','long') }} {{ date | timeString('ko-KR') }}\",\n    sameDay: \"오늘 {{ date | timeString('ko-KR') }}\",\n  },\n  dividerText: '또는',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: '다른 방법 사용하기',\n  footerPageLink__help: '도움',\n  footerPageLink__privacy: '개인정보처리방침',\n  footerPageLink__terms: '약관',\n  formButtonPrimary: '계속',\n  formButtonPrimary__verify: 'Verify',\n  formFieldAction__forgotPassword: '비밀번호를 잊으셨나요?',\n  formFieldError__matchingPasswords: '비밀번호가 일치합니다.',\n  formFieldError__notMatchingPasswords: '비밀번호가 일치하지 않습니다.',\n  formFieldError__verificationLinkExpired: 'The verification link expired. Please request a new link.',\n  formFieldHintText__optional: '선택사항',\n  formFieldHintText__slug: 'A slug is a human-readable ID that must be unique. It’s often used in URLs.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: '계정 삭제',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses:\n    '하나 이상의 이메일 주소를 공백 또는 쉼표로 구분하여 입력하거나 붙여넣습니다',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: undefined,\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Enable automatic invitations for this domain',\n  formFieldLabel__backupCode: '백업 코드',\n  formFieldLabel__confirmDeletion: '확인',\n  formFieldLabel__confirmPassword: '비밀번호 확인',\n  formFieldLabel__currentPassword: '현재 비밀번호',\n  formFieldLabel__emailAddress: '이메일 주소',\n  formFieldLabel__emailAddress_username: '이메일 주소 혹은 사용자 이름',\n  formFieldLabel__emailAddresses: '이메일 주소',\n  formFieldLabel__firstName: '이름',\n  formFieldLabel__lastName: '성',\n  formFieldLabel__newPassword: '새 비밀번호',\n  formFieldLabel__organizationDomain: '도메인',\n  formFieldLabel__organizationDomainDeletePending: 'Delete pending invitations and suggestions',\n  formFieldLabel__organizationDomainEmailAddress: 'Verification email address',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Enter an email address under this domain to receive a code and verify this domain.',\n  formFieldLabel__organizationName: '이름',\n  formFieldLabel__organizationSlug: '슬러그',\n  formFieldLabel__passkeyName: undefined,\n  formFieldLabel__password: '비밀번호',\n  formFieldLabel__phoneNumber: '휴대폰 번호',\n  formFieldLabel__role: '역할',\n  formFieldLabel__signOutOfOtherSessions: '다른 모든 기기에서 로그아웃',\n  formFieldLabel__username: '사용자 이름',\n  impersonationFab: {\n    action__signOut: '로그아웃',\n    title: '{{identifier}}로 로그인했습니다',\n  },\n  maintenanceMode: undefined,\n  membershipRole__admin: '관리자',\n  membershipRole__basicMember: '멤버',\n  membershipRole__guestMember: '게스트',\n  organizationList: {\n    action__createOrganization: 'Create organization',\n    action__invitationAccept: 'Join',\n    action__suggestionsAccept: 'Request to join',\n    createOrganization: 'Create Organization',\n    invitationAcceptedLabel: 'Joined',\n    subtitle: 'to continue to {{applicationName}}',\n    suggestionsAcceptedLabel: 'Pending approval',\n    title: 'Choose an account',\n    titleWithoutPersonal: 'Choose an organization',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Automatic invitations',\n    badge__automaticSuggestion: 'Automatic suggestions',\n    badge__manualInvitation: 'No automatic enrollment',\n    badge__unverified: 'Unverified',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Add the domain to verify. Users with email addresses at this domain can join the organization automatically or request to join.',\n      title: 'Add domain',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed: '초대를 보낼 수 없습니다. 다음을 수정하고 다시 시도하세요:',\n      formButtonPrimary__continue: '초대 보내기',\n      selectDropdown__role: 'Select role',\n      subtitle: '이 조직에 새 멤버 초대',\n      successMessage: '초대가 성공적으로 전송되었습니다',\n      title: '회원 초대',\n    },\n    membersPage: {\n      action__invite: '초대',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: '회원 제거',\n        tableHeader__actions: undefined,\n        tableHeader__joined: '가입됨',\n        tableHeader__role: '역할',\n        tableHeader__user: '사용자',\n      },\n      detailsTitle__emptyRow: '표시할 멤버 없음',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Invite users by connecting an email domain with your organization. Anyone who signs up with a matching email domain will be able to join the organization anytime.',\n          headerTitle: 'Automatic invitations',\n          primaryButton: 'Manage verified domains',\n        },\n        table__emptyRow: 'No invitations to display',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: '초대 취소',\n        tableHeader__invited: '초대됨',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Users who sign up with a matching email domain, will be able to see a suggestion to request to join your organization.',\n          headerTitle: 'Automatic suggestions',\n          primaryButton: 'Manage verified domains',\n        },\n        menuAction__approve: 'Approve',\n        menuAction__reject: 'Reject',\n        tableHeader__requested: 'Requested access',\n        table__emptyRow: 'No requests to display',\n      },\n      start: {\n        headerTitle__invitations: 'Invitations',\n        headerTitle__members: 'Members',\n        headerTitle__requests: 'Requests',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Manage your organization.',\n      general: 'General',\n      members: 'Members',\n      title: 'Organization',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: '계속하려면 아래에 {{organizationName}}을(를) 입력하세요.',\n          messageLine1: '이 조직을 삭제하시겠습니까?',\n          messageLine2: '이 작업은 영구적이며 되돌릴 수 없습니다.',\n          successMessage: '조직을 삭제했습니다.',\n          title: '조직 삭제',\n        },\n        leaveOrganization: {\n          actionDescription: 'Type \"{{organizationName}}\" below to continue.',\n          messageLine1: '이 조직을 탈퇴하시겠습니까? 이 조직 및 해당 애플리케이션에 대한 액세스 권한을 잃게됩니다.',\n          messageLine2: '이 작업은 영구적이며 되돌릴 수 없습니다',\n          successMessage: '조직을 탈퇴했습니다.',\n          title: '조직 떠나기',\n        },\n        title: '위험',\n      },\n      domainSection: {\n        menuAction__manage: 'Manage',\n        menuAction__remove: 'Delete',\n        menuAction__verify: 'Verify',\n        primaryButton: '도메인 추가',\n        subtitle: '인증된 이메일 도메인을 기반으로 사용자가 조직에 자동으로 가입하거나 가입 요청을 할 수 있게 합니다.',\n        title: '인증된 도메인',\n      },\n      successMessage: '조직이 업데이트되었습니다.',\n      title: '조직 프로필',\n    },\n    removeDomainPage: {\n      messageLine1: 'The email domain {{domain}} will be removed.',\n      messageLine2: 'Users won’t be able to join the organization automatically after this.',\n      successMessage: '{{domain}} has been removed.',\n      title: 'Remove domain',\n    },\n    start: {\n      headerTitle__general: 'General',\n      headerTitle__members: '멤버',\n      profileSection: {\n        primaryButton: undefined,\n        title: 'Organization Profile',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Removing this domain will affect invited users.',\n        removeDomainActionLabel__remove: 'Remove domain',\n        removeDomainSubtitle: 'Remove this domain from your verified domains',\n        removeDomainTitle: 'Remove domain',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Users are automatically invited to join the organization when they sign-up and can join anytime.',\n        automaticInvitationOption__label: 'Automatic invitations',\n        automaticSuggestionOption__description:\n          'Users receive a suggestion to request to join, but must be approved by an admin before they are able to join the organization.',\n        automaticSuggestionOption__label: 'Automatic suggestions',\n        calloutInfoLabel: 'Changing the enrollment mode will only affect new users.',\n        calloutInvitationCountLabel: 'Pending invitations sent to users: {{count}}',\n        calloutSuggestionCountLabel: 'Pending suggestions sent to users: {{count}}',\n        manualInvitationOption__description: 'Users can only be invited manually to the organization.',\n        manualInvitationOption__label: 'No automatic enrollment',\n        subtitle: 'Choose how users from this domain can join the organization.',\n      },\n      start: {\n        headerTitle__danger: 'Danger',\n        headerTitle__enrollment: 'Enrollment options',\n      },\n      subtitle: 'The domain {{domain}} is now verified. Continue by selecting enrollment mode.',\n      title: 'Update {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Enter the verification code sent to your email address',\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'The domain {{domainName}} needs to be verified via email.',\n      subtitleVerificationCodeScreen: 'A verification code was sent to {{emailAddress}}. Enter the code to continue.',\n      title: 'Verify domain',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: '조직 만들기',\n    action__invitationAccept: 'Join',\n    action__manageOrganization: '조직 관리',\n    action__suggestionsAccept: 'Request to join',\n    notSelected: '선택한 조직 없음',\n    personalWorkspace: '개인 워크스페이스',\n    suggestionsAcceptedLabel: 'Pending approval',\n  },\n  paginationButton__next: '다음',\n  paginationButton__previous: '이전',\n  paginationRowText__displaying: '표시중',\n  paginationRowText__of: '의',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Add account',\n      action__signOutAll: 'Sign out of all accounts',\n      subtitle: 'Select the account with which you wish to continue.',\n      title: 'Choose an account',\n    },\n    alternativeMethods: {\n      actionLink: '도움 요청하기',\n      actionText: 'Don’t have any of these?',\n      blockButton__backupCode: '백업 코드 사용하기',\n      blockButton__emailCode: '{{identifier}}로 이메일 코드 보내기',\n      blockButton__emailLink: '{{identifier}}로 이메일 링크 보내기',\n      blockButton__passkey: undefined,\n      blockButton__password: '비밀번호로 로그인',\n      blockButton__phoneCode: '{{identifier}}로 SMS 코드 보내기',\n      blockButton__totp: '인증 앱 사용하기',\n      getHelp: {\n        blockButton__emailSupport: '이메일 지원',\n        content: '계정에 로그인하는 데 문제가 있는 경우 이메일을 보내주시면 최대한 빨리 액세스를 복구해 드리겠습니다.',\n        title: '도움 요청하기',\n      },\n      subtitle: '문제가 있나요? 다른 방법으로 로그인할 수 있습니다.',\n      title: '다른 방법 사용하기',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: '백업코드는 2단계 인증을 설정할 때 얻은 코드입니다',\n      title: '백업 코드 입력',\n    },\n    emailCode: {\n      formTitle: '인증 코드',\n      resendButton: '코드 재전송',\n      subtitle: '{{applicationName}}로 계속하려면',\n      title: '이메일을 확인하세요',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: '계속하려면 원래 탭으로 돌아가세요.',\n        title: '이 인증 링크는 만료되었습니다',\n      },\n      failed: {\n        subtitle: '계속하려면 원래 탭으로 돌아가세요.',\n        title: '이 인증 링크는 유효하지 않습니다',\n      },\n      formSubtitle: '이메일로 전송된 인증 링크를 사용하세요',\n      formTitle: '인증 링크',\n      loading: {\n        subtitle: '곧 리다이렉션 됩니다',\n        title: '로그인 중...',\n      },\n      resendButton: '링크 재전송',\n      subtitle: '{{applicationName}}로 계속하려면',\n      title: '이메일을 확인하세요',\n      unusedTab: {\n        title: '이 탭을 닫으셔도 됩니다',\n      },\n      verified: {\n        subtitle: '곧 리다이렉션 될 예정입니다',\n        title: '로그인에 성공했습니다',\n      },\n      verifiedSwitchTab: {\n        subtitle: '계속하려면 원래 탭으로 돌아가세요',\n        subtitleNewTab: '계속하려면 새로 연 탭으로 돌아가세요',\n        titleNewTab: '다른 탭에서 로그인',\n      },\n    },\n    forgotPassword: {\n      formTitle: '비밀번호 재설정 코드',\n      resendButton: '코드 재전송',\n      subtitle: 'to reset your password',\n      subtitle_email: '먼저 이메일로 전송된 코드를 입력하세요',\n      subtitle_phone: '먼저 휴대폰으로 전송된 코드를 입력하세요',\n      title: '비밀번호 재설정',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: '비밀번호 재설정',\n      label__alternativeMethods: '또는 다른 방법으로 로그인',\n      title: '비밀번호를 잊으셨나요?',\n    },\n    noAvailableMethods: {\n      message: '로그인을 계속할 수 없습니다. 사용 가능한 인증 방법이 없습니다.',\n      subtitle: '오류가 발생했습니다',\n      title: '로그인할 수 없습니다',\n    },\n    passkey: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: '다른 방법 사용하기',\n      subtitle: '계정에 등록된 비밀번호를 입력해 주세요',\n      title: '비밀번호를 입력하세요',\n    },\n    passwordPwned: {\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: '인증 코드',\n      resendButton: '코드 다시 보내기',\n      subtitle: '{{applicationName}}로 계속하려면',\n      title: '휴대폰 확인',\n    },\n    phoneCodeMfa: {\n      formTitle: '인증 코드',\n      resendButton: '코드 다시 보내기',\n      subtitle: '계속하려면 휴대폰으로 전송된 인증 코드를 입력하세요',\n      title: '휴대폰 확인',\n    },\n    resetPassword: {\n      formButtonPrimary: '비밀번호 재설정',\n      requiredMessage: 'For security reasons, it is required to reset your password.',\n      successMessage: '비밀번호가 성공적으로 변경되었습니다. 로그인하는 중입니다. 잠시만 기다려주세요.',\n      title: '비밀번호 재설정',\n    },\n    resetPasswordMfa: {\n      detailsLabel: '비밀번호를 재설정하기 전에 신원을 확인해야 합니다.',\n    },\n    start: {\n      actionLink: '회원가입',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: '이메일 사용하기',\n      actionLink__use_email_username: '이메일 또는 사용자 이름 사용하기',\n      actionLink__use_passkey: undefined,\n      actionLink__use_phone: '휴대폰 번호 사용하기',\n      actionLink__use_username: '사용자 이름 사용하기',\n      actionText: '계정이 없으신가요?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: '환영합니다! 계속하려면 로그인해 주세요',\n      subtitleCombined: undefined,\n      title: '{{applicationName}}에 로그인',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: '인증 코드',\n      subtitle: '계속하려면 인증 앱에서 생성된 인증 코드를 입력하세요',\n      title: '2단계 인증',\n    },\n  },\n  signInEnterPasswordTitle: '비밀번호를 입력하세요',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: '로그인',\n      actionText: '계정이 있으신가요?',\n      subtitle: '계속하려면 누락된 필드에 값을 입력하세요',\n      title: '누락된 필드를 입력하세요',\n    },\n    emailCode: {\n      formSubtitle: '이메일 주소로 전송된 인증 코드를 입력하세요',\n      formTitle: '인증 코드',\n      resendButton: '코드 재전송',\n      subtitle: '이메일로 전송된 인증 코드를 입력하세요',\n      title: '이메일 인증',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: '이메일 주소로 전송된 인증 링크를 사용합니다.',\n      formTitle: '인증 링크',\n      loading: {\n        title: '가입 중...',\n      },\n      resendButton: '링크 재전송',\n      subtitle: '{{applicationName}}로 계속하려면',\n      title: '이메일 인증하기',\n      verified: {\n        title: '성공적으로 가입에 성공했습니다',\n      },\n      verifiedSwitchTab: {\n        subtitle: '계속하려면 새로 연 탭으로 돌아가기',\n        subtitleNewTab: '계속하려면 이전 탭으로 돌아가기',\n        title: '이메일 인증 성공',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: '휴대폰 번호로 전송된 인증 코드를 입력하세요.',\n      formTitle: '인증 코드',\n      resendButton: '코드 재전송',\n      subtitle: '휴대폰으로 전송된 인증 코드를 입력하세요',\n      title: '휴대폰 번호 인증',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: '로그인하기',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: '계정이 있으신가요?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: '환영합니다! 아래 정보를 입력해주세요.',\n      subtitleCombined: '환영합니다! 아래 정보를 입력해주세요.',\n      title: '계정 만들기',\n      titleCombined: '계정 만들기',\n    },\n  },\n  socialButtonsBlockButton: '{{provider|titleize}}로 계속하기',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'Sign up unsuccessful due to failed security validations. Please refresh the page to try again or reach out to support for more assistance.',\n    captcha_unavailable:\n      'Sign up unsuccessful due to failed bot validation. Please refresh the page to try again or reach out to support for more assistance.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: '이 세부 정보와 일치하는 계정을 찾을 수 없습니다.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'Email address must be a valid email address.',\n    form_param_format_invalid__phone_number: 'Phone number must be in a valid international format',\n    form_param_max_length_exceeded__first_name: 'First name should not exceed 256 characters.',\n    form_param_max_length_exceeded__last_name: 'Last name should not exceed 256 characters.',\n    form_param_max_length_exceeded__name: 'Name should not exceed 256 characters.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: '비밀번호가 충분히 안전하지 않습니다.',\n    form_password_pwned: '이 비밀번호는 유출사항이 발견되어 사용할 수 없으므로 대신 다른 비밀번호를 사용해 보세요.',\n    form_password_pwned__sign_in: undefined,\n    form_password_size_in_bytes_exceeded:\n      '비밀번호가 허용되는 최대 바이트 수를 초과했습니다. 비밀번호를 줄이거나 일부 특수 문자를 제거해 주세요.',\n    form_password_validation_failed: '잘못된 비밀번호',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: 'You cannot delete your last identification.',\n    not_allowed_access:\n      \"이메일 주소 또는 전화번호는 가입에 사용할 수 없습니다. 이는 '+', '=', '#' 또는 '.'이 이메일 주소에 사용되었거나 임시 이메일 서비스에 연결된 도메인이 사용되었거나 명시적 제외가 있는 경우입니다. 이 오류가 발생한 경우 지원에 문의하세요.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: '{{length}} 보다 짧은 문자열',\n      minimumLength: '{{length}} 또는 그 이상의 문자열',\n      requireLowercase: '소문자',\n      requireNumbers: '숫자',\n      requireSpecialCharacter: '특수문자',\n      requireUppercase: '대문자',\n      sentencePrefix: '당신의 비밀번호는 반드시 포함해야합니다',\n    },\n    phone_number_exists: '이 전화번호는 이미 사용중입니다. 다른 번호를 시도해 주세요.',\n    session_exists: '이미 로그인 중입니다.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: '비밀번호는 작동하지만 더 강력할 수 있습니다. 문자를 더 추가해 보세요.',\n      goodPassword: '수고하셨습니다. 훌륭한 비밀번호입니다.',\n      notEnough: '비밀번호가 충분히 안전하지 않습니다.',\n      suggestions: {\n        allUppercase: '모든 문자가 아닌 일부 문자를 대문자로 표시합니다.',\n        anotherWord: '덜 일반적인 단어를 더 추가합니다.',\n        associatedYears: '귀하와 연관된 연도는 피하세요.',\n        capitalization: '한 글자 이상을 대문자로 표기하세요.',\n        dates: '자신과 관련된 날짜와 연도는 피하세요.',\n        l33t: \"'a'에서 '@'와 같이 예측 가능한 문자 대체를 피하세요.\",\n        longerKeyboardPattern: '더 긴 키보드 패턴을 사용하고 타이핑 방향을 여러 번 변경합니다.',\n        noNeed: '기호, 숫자 또는 대문자를 사용하지 않고도 강력한 비밀번호를 만들 수 있습니다.',\n        pwned: '이 비밀번호를 다른 곳에서 사용하는 경우 변경해야 합니다.',\n        recentYears: '최근 연도는 피하세요.',\n        repeated: '반복되는 단어와 문자를 피하세요.',\n        reverseWords: '일반적인 단어의 철자를 거꾸로 쓰지 마세요.',\n        sequences: '일반적인 문자 시퀀스를 피하세요.',\n        useWords: '여러 단어를 사용하되 일반적인 문구는 피하세요.',\n      },\n      warnings: {\n        common: '일반적으로 사용되는 비밀번호입니다.',\n        commonNames: '일반적인 이름과 성은 추측하기 쉽습니다.',\n        dates: '날짜는 쉽게 추측할 수 있습니다.',\n        extendedRepeat: '\"abcabcabc\"와 같이 반복되는 문자 패턴은 쉽게 추측할 수 있습니다.',\n        keyPattern: '짧은 패턴은 추측하기 쉽습니다.',\n        namesByThemselves: '단일 이름이나 성은 추측하기 쉽습니다.',\n        pwned: '인터넷에서 데이터 유출로 인해 비밀번호가 노출되었습니다.',\n        recentYears: '최근연도는 쉽게 추측할 수 있습니다.',\n        sequences: '\"abc\"와 같은 일반적인 문자 시퀀스는 쉽게 추측할 수 있습니다.',\n        similarToCommon: '일반적으로 사용되는 비밀번호와 유사합니다.',\n        simpleRepeat: '\"aaa\"와 같이 반복되는 문자는 쉽게 추측할 수 있습니다.',\n        straightRow: '같은 줄에 위치한 키보드를 사용하는 것은 추측하기 쉽습니다.',\n        topHundred: '자주 사용되는 비밀번호입니다.',\n        topTen: '아주 많이 사용되는 비밀번호입니다.',\n        userInputs: '개인 정보나 페이지 관련 데이터가 없어야 합니다.',\n        wordByItself: '한 단어는 쉽게 추측할 수 있습니다.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: '계정 추가',\n    action__manageAccount: '계정 관리',\n    action__signOut: '로그아웃',\n    action__signOutAll: '모든 계정에서 로그아웃',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: '복사 완료!',\n      actionLabel__copy: '전체 복사',\n      actionLabel__download: '.txt 다운로드',\n      actionLabel__print: '인쇄',\n      infoText1: '이 계정에 대해 백업 코드가 활성화됩니다.',\n      infoText2:\n        '백업 코드를 비밀로 유지하고 안전하게 보관하세요. 백업 코드가 손상된 것으로 의심되는 경우 백업 코드를 다시 생성할 수 있습니다.',\n      subtitle__codelist: '안전하게 저장하고 비밀로 유지하세요.',\n      successMessage:\n        '이제 백업 코드가 활성화되었습니다. 인증 장치에 액세스할 수 없는 경우 이 중 하나를 사용하여 계정에 로그인할 수 있습니다. 각 코드는 한 번만 사용할 수 있습니다.',\n      successSubtitle: '인증 장치에 액세스할 수 없는 경우 이 중 하나를 사용하여 계정에 로그인할 수 있습니다.',\n      title: '백업 코드 인증 추가',\n      title__codelist: '백업 코드',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: '계정을 연결할 제공자를 선택하세요',\n      formHint__noAccounts: '사용 가능한 외부 계정 제공자가 없습니다.',\n      removeResource: {\n        messageLine1: '{{identifier}}가 이 계정에서 제거 될 예정입니다.',\n        messageLine2: '이 연결된 계정을 더 이상 사용할 수 없으며 종속된 모든 기능이 더 이상 작동하지 않습니다.',\n        successMessage: '{{connectedAccount}}가 당신의 계정에서 제거되었습니다.',\n        title: '연결된 계정 제거',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}} 계정 연결',\n      successMessage: '이 제공자가 계정에 추가되었습니다.',\n      title: '연결된 계정 추가하기',\n    },\n    deletePage: {\n      actionDescription: '계속하려면 아래에 계정 삭제를 입력하세요.',\n      confirm: '계정 삭제',\n      messageLine1: '정말로 계정을 삭제하시겠습니까?',\n      messageLine2: '이 작업은 영구적이며 되돌릴 수 없습니다.',\n      title: '계정 삭제',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: '이 이메일 주소로 인증 코드가 포함된 이메일이 전송됩니다.',\n        formSubtitle: '{{identifier}}에게 전송된 인증 코드를 입력합니다',\n        formTitle: '인증 코드',\n        resendButton: '코드 재전송',\n        successMessage: '{{identifier}} 이메일이 당신의 계정에 추가되었습니다.',\n      },\n      emailLink: {\n        formHint: '인증 링크가 포함된 이메일이 이 이메일 주소로 전송됩니다.',\n        formSubtitle: '{{identifier}}에게 전송된 이메일의 링크를 클릭합니다.',\n        formTitle: '인증 링크',\n        resendButton: '링크 재전송',\n        successMessage: '{{identifier}} 이메일이 당신의 계정에 추가되었습니다.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} 이메일이 이 계정에서 제거 될 에정입니다.',\n        messageLine2: '더 이상 이 이메일 주소로 로그인할 수 없습니다.',\n        successMessage: '{{emailAddress}} 이메일이 당신의 계정에서 삭제되었습니다',\n        title: '이메일 제거',\n      },\n      title: '이메일 주소 추가',\n      verifyTitle: 'Verify email address',\n    },\n    formButtonPrimary__add: 'Add',\n    formButtonPrimary__continue: '계속',\n    formButtonPrimary__finish: '완료',\n    formButtonPrimary__remove: 'Remove',\n    formButtonPrimary__save: 'Save',\n    formButtonReset: '취소',\n    mfaPage: {\n      formHint: '추가할 방법을 선택합니다.',\n      title: '2단계 인증 추가',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Use existing number',\n      primaryButton__addPhoneNumber: '휴대폰 번호 추가',\n      removeResource: {\n        messageLine1: '{{identifier}}는 로그인할 때 더 이상 인증 코드를 받지 않습니다.',\n        messageLine2: '계정이 안전하지 않을 수 있습니다. 계속하시겠습니까?',\n        successMessage: '{{mfaPhoneCode}}에 대한 SMS 코드 2단계 인증이 제거되었습니다',\n        title: '2단계 인증 제거',\n      },\n      subtitle__availablePhoneNumbers: 'SMS 코드 2단계 인증을 위해 등록할 휴대폰 번호를 선택하세요.',\n      subtitle__unavailablePhoneNumbers: 'SMS 코드 2단계 인증에 등록할 수 있는 휴대폰 번호가 없습니다.',\n      successMessage1:\n        'When signing in, you will need to enter a verification code sent to this phone number as an additional step.',\n      successMessage2:\n        'Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.',\n      successTitle: 'SMS code verification enabled',\n      title: 'SMS 코드 인증 추가',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: '대신 QR 코드 스캔 하세요',\n        buttonUnableToScan__nonPrimary: 'QR 코드를 스캔할 수 없나요?',\n        infoText__ableToScan: '인증 앱에서 새 로그인 방법을 설정하고 다음 QR 코드를 스캔하여 계정에 연결합니다.',\n        infoText__unableToScan: '인증 장치에서 새 로그인 방법을 설정하고 아래에 제공된 키를 입력합니다.',\n        inputLabel__unableToScan1:\n          '시간 기반 또는 일회용 비밀번호가 사용 설정되어 있는지 확인한 다음 계정 연결을 완료합니다.',\n        inputLabel__unableToScan2: '또는 인증자가 TOTP URI를 지원하는 경우 전체 URI를 복사할 수도 있습니다.',\n      },\n      removeResource: {\n        messageLine1: '로그인할 때 더 이상 이 인증자의 인증 코드가 필요하지 않습니다.',\n        messageLine2: '계정이 안전하지 않을 수 있습니다. 계속 진행하시겠습니까?',\n        successMessage: '인증자 애플리케이션을 통한 2단계 인증이 제거되었습니다.',\n        title: '2단계 인증 제거',\n      },\n      successMessage:\n        '이제 2단계 인증이 활성화되었습니다. 로그인할 때 추가 단계로 이 인증자의 인증 코드를 입력해야 합니다.',\n      title: '인증 애플리케이션 추가',\n      verifySubtitle: '인증자가 생성한 인증 코드를 입력합니다',\n      verifyTitle: '인증 코드',\n    },\n    mobileButton__menu: '메뉴',\n    navbar: {\n      account: '프로필',\n      apiKeys: undefined,\n      billing: undefined,\n      description: '계정 정보를 관리하세요.',\n      security: '보안',\n      title: '계정',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: undefined,\n        title: undefined,\n      },\n      subtitle__rename: undefined,\n      title__rename: undefined,\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'It is recommended to sign out of all other devices which may have used your old password.',\n      readonly: 'Your password can currently not be edited because you can sign in only via the enterprise connection.',\n      successMessage__set: '비밀번호가 설정되었습니다.',\n      successMessage__signOutOfOtherSessions: '다른 모든 기기는 로그아웃되었습니다.',\n      successMessage__update: '비밀번호가 업데이트되었습니다.',\n      title__set: '비밀번호 설정',\n      title__update: '비밀번호 변경',\n    },\n    phoneNumberPage: {\n      infoText: '인증 링크가 포함된 문자 메시지가 이 휴대폰 번호로 전송됩니다.',\n      removeResource: {\n        messageLine1: '{{identifier}}가 당신의 계정에서 제거 될 예정입니다.',\n        messageLine2: '더 이상 이 휴대폰 번호로 로그인할 수 없습니다.',\n        successMessage: '{{phoneNumber}}가 당신의 계정에서 제거되었습니다.',\n        title: '휴대폰 번호 제거',\n      },\n      successMessage: '{{identifier}}가 당신의 계정에 추가되었습니다.',\n      title: '휴대폰 번호 추가',\n      verifySubtitle: 'Enter the verification code sent to {{identifier}}',\n      verifyTitle: 'Verify phone number',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: '10MB보다 작은 JPG, PNG, GIF 또는 WEBP 이미지를 업로드합니다',\n      imageFormDestructiveActionSubtitle: '이미지 제거',\n      imageFormSubtitle: '이미지 업로드',\n      imageFormTitle: '프로필 이미지',\n      readonly: 'Your profile information has been provided by the enterprise connection and cannot be edited.',\n      successMessage: '프로필이 업데이트되었습니다.',\n      title: '프로필 업데이트',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: '이 장치에서 로그아웃',\n        title: '활성화된 장치',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: '재시도',\n        actionLabel__reauthorize: '지금 인증하기',\n        destructiveActionTitle: '제거',\n        primaryButton: '계정 연결하기',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues',\n        title: '연결된 계정',\n      },\n      dangerSection: {\n        deleteAccountButton: '계정 삭제',\n        title: '위험',\n      },\n      emailAddressesSection: {\n        destructiveAction: '이메일 주소 제거',\n        detailsAction__nonPrimary: '기본으로 설정',\n        detailsAction__primary: '인증 완료',\n        detailsAction__unverified: '인증 완료',\n        primaryButton: '이메일 주소 추가',\n        title: '이메일 주소',\n      },\n      enterpriseAccountsSection: {\n        title: '기업 계정',\n      },\n      headerTitle__account: '프로필',\n      headerTitle__security: '보안',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: '코드 재생성',\n          headerTitle: '백업 코드',\n          subtitle__regenerate: '새로운 보안 백업 코드 세트를 받으세요. 이전 백업 코드는 삭제되며 사용할 수 없습니다.',\n          title__regenerate: '백업 코드 재생성',\n        },\n        phoneCode: {\n          actionLabel__setDefault: '기본으로 설정',\n          destructiveActionLabel: '휴대폰 번호 제거',\n        },\n        primaryButton: '2단계 인증 추가',\n        title: '2단계 인증',\n        totp: {\n          destructiveActionTitle: '제거',\n          headerTitle: '인증 애플리케이션',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: undefined,\n        menuAction__rename: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      passwordSection: {\n        primaryButton__setPassword: '비밀번호 설정',\n        primaryButton__updatePassword: '비밀번호 변경',\n        title: '비밀번호',\n      },\n      phoneNumbersSection: {\n        destructiveAction: '휴대폰 번호 제거',\n        detailsAction__nonPrimary: '기본으로 설정',\n        detailsAction__primary: '인증 완료',\n        detailsAction__unverified: '인증 완료',\n        primaryButton: '휴대폰 번호 추가',\n        title: '휴대폰 번호',\n      },\n      profileSection: {\n        primaryButton: '프로필 수정',\n        title: '프로필',\n      },\n      usernameSection: {\n        primaryButton__setUsername: '사용자 이름 설정',\n        primaryButton__updateUsername: '사용자 이름 변경',\n        title: '사용자 이름',\n      },\n      web3WalletsSection: {\n        destructiveAction: '지갑 제거',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 지갑',\n        title: 'Web3 지갑',\n      },\n    },\n    usernamePage: {\n      successMessage: '당신읭 사용자 이름이 업데이트되었습니다.',\n      title__set: '사용자 이름 업데이트',\n      title__update: '사용자 이름 업데이트',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}}가 이 계정에서 제거 될 예정입니다.',\n        messageLine2: '더 이상 이 web3 지갑을 사용하여 로그인할 수 없습니다.',\n        successMessage: '{{web3Wallet}}가 당신의 계정에서 제거되었습니다.',\n        title: 'web3 지갑 제거',\n      },\n      subtitle__availableWallets: '계정에 연결할 web3 지갑을 선택하세요.',\n      subtitle__unavailableWallets: '사용 가능한 web3 지갑이 없습니다.',\n      successMessage: '지갑이 계정에 추가되었습니다ß.',\n      title: 'web3 지갑 추가',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCAAiC;AAAA,MACjC,mCAAmC;AAAA,MACnC,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BACE;AAAA,QACF,2BAA2B;AAAA,MAC7B;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBAAsB;AAAA,UACtB,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}