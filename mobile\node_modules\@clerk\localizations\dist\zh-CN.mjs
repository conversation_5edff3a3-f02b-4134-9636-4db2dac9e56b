// src/zh-CN.ts
var zhCN = {
  locale: "zh-CN",
  apiKeys: {
    action__add: void 0,
    action__search: void 0,
    createdAndExpirationStatus__expiresOn: void 0,
    createdAndExpirationStatus__never: void 0,
    detailsTitle__emptyRow: void 0,
    formButtonPrimary__add: void 0,
    formFieldCaption__expiration__expiresOn: void 0,
    formFieldCaption__expiration__never: void 0,
    formFieldOption__expiration__180d: void 0,
    formFieldOption__expiration__1d: void 0,
    formFieldOption__expiration__1y: void 0,
    formFieldOption__expiration__30d: void 0,
    formFieldOption__expiration__60d: void 0,
    formFieldOption__expiration__7d: void 0,
    formFieldOption__expiration__90d: void 0,
    formFieldOption__expiration__never: void 0,
    formHint: void 0,
    formTitle: void 0,
    lastUsed__days: void 0,
    lastUsed__hours: void 0,
    lastUsed__minutes: void 0,
    lastUsed__months: void 0,
    lastUsed__seconds: void 0,
    lastUsed__years: void 0,
    menuAction__revoke: void 0,
    revokeConfirmation: {
      confirmationText: void 0,
      formButtonPrimary__revoke: void 0,
      formHint: void 0,
      formTitle: void 0
    }
  },
  backButton: "\u8FD4\u56DE",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "\u9ED8\u8BA4",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "\u5176\u4ED6\u6A21\u62DF\u5668\u8BBE\u5907",
  badge__primary: "\u4E3B\u8981",
  badge__renewsAt: void 0,
  badge__requiresAction: "\u9700\u8981\u64CD\u4F5C",
  badge__startsAt: void 0,
  badge__thisDevice: "\u6B64\u8BBE\u5907",
  badge__unverified: "\u672A\u9A8C\u8BC1",
  badge__upcomingPlan: void 0,
  badge__userDevice: "\u7528\u6237\u8BBE\u5907",
  badge__you: "\u60A8",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      pastDueNotice: void 0,
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    credit: void 0,
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDue: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "\u521B\u5EFA\u7EC4\u7EC7",
    invitePage: {
      formButtonReset: "\u8DF3\u8FC7"
    },
    title: "\u521B\u5EFA\u7EC4\u7EC7"
  },
  dates: {
    lastDay: "\u6628\u5929{{ date | timeString('zh-CN') }}",
    next6Days: "{{ date | weekday('zh-CN','long') }} {{ date | timeString('zh-CN') }}",
    nextDay: "\u660E\u5929{{ date | timeString('zh-CN') }}",
    numeric: "{{ date | numeric('zh-CN') }}",
    previous6Days: "\u4E0A\u5468{{ date | weekday('zh-CN','long') }} {{ date | timeString('zh-CN') }}",
    sameDay: "\u4ECA\u5929{{ date | timeString('zh-CN') }}"
  },
  dividerText: "\u6216\u8005",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "\u4F7F\u7528\u53E6\u4E00\u79CD\u65B9\u6CD5",
  footerPageLink__help: "\u5E2E\u52A9",
  footerPageLink__privacy: "\u9690\u79C1",
  footerPageLink__terms: "\u6761\u6B3E",
  formButtonPrimary: "\u7EE7\u7EED",
  formButtonPrimary__verify: "\u9A8C\u8BC1",
  formFieldAction__forgotPassword: "\u5FD8\u8BB0\u5BC6\u7801\uFF1F",
  formFieldError__matchingPasswords: "\u5BC6\u7801\u5339\u914D\u3002",
  formFieldError__notMatchingPasswords: "\u5BC6\u7801\u4E0D\u5339\u914D\u3002",
  formFieldError__verificationLinkExpired: "\u9A8C\u8BC1\u94FE\u63A5\u5DF2\u8FC7\u671F\u3002\u8BF7\u7533\u8BF7\u65B0\u7684\u94FE\u63A5\u3002",
  formFieldHintText__optional: "\u9009\u586B",
  formFieldHintText__slug: "Slug \u662F\u4E00\u4E2A\u4EBA\u7C7B\u53EF\u8BFB\u7684 ID\uFF0C\u5B83\u5FC5\u987B\u662F\u552F\u4E00\u7684\u3002\u5B83\u901A\u5E38\u7528\u4E8E URL \u4E2D\u3002",
  formFieldInputPlaceholder__apiKeyDescription: void 0,
  formFieldInputPlaceholder__apiKeyExpirationDate: void 0,
  formFieldInputPlaceholder__apiKeyName: void 0,
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "\u5220\u9664\u5E10\u6237",
  formFieldInputPlaceholder__emailAddress: void 0,
  formFieldInputPlaceholder__emailAddress_username: void 0,
  formFieldInputPlaceholder__emailAddresses: "\u8F93\u5165\u6216\u7C98\u8D34\u4E00\u4E2A\u6216\u591A\u4E2A\u7535\u5B50\u90AE\u4EF6\u5730\u5740\uFF0C\u7528\u7A7A\u683C\u6216\u9017\u53F7\u5206\u9694",
  formFieldInputPlaceholder__firstName: void 0,
  formFieldInputPlaceholder__lastName: void 0,
  formFieldInputPlaceholder__organizationDomain: void 0,
  formFieldInputPlaceholder__organizationDomainEmailAddress: void 0,
  formFieldInputPlaceholder__organizationName: void 0,
  formFieldInputPlaceholder__organizationSlug: "\u6211\u7684\u7EC4\u7EC7",
  formFieldInputPlaceholder__password: void 0,
  formFieldInputPlaceholder__phoneNumber: void 0,
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__apiKeyDescription: void 0,
  formFieldLabel__apiKeyExpiration: void 0,
  formFieldLabel__apiKeyName: void 0,
  formFieldLabel__automaticInvitations: "\u4E3A\u6B64\u57DF\u540D\u542F\u7528\u81EA\u52A8\u9080\u8BF7",
  formFieldLabel__backupCode: "\u5907\u7528\u4EE3\u7801",
  formFieldLabel__confirmDeletion: "\u786E\u8BA4",
  formFieldLabel__confirmPassword: "\u786E\u8BA4\u5BC6\u7801",
  formFieldLabel__currentPassword: "\u5F53\u524D\u5BC6\u7801",
  formFieldLabel__emailAddress: "\u7535\u5B50\u90AE\u4EF6\u5730\u5740",
  formFieldLabel__emailAddress_username: "\u7535\u5B50\u90AE\u4EF6\u5730\u5740\u6216\u7528\u6237\u540D",
  formFieldLabel__emailAddresses: "\u7535\u5B50\u90AE\u4EF6\u5730\u5740",
  formFieldLabel__firstName: "\u540D\u5B57",
  formFieldLabel__lastName: "\u59D3\u6C0F",
  formFieldLabel__newPassword: "\u65B0\u5BC6\u7801",
  formFieldLabel__organizationDomain: "\u57DF\u540D",
  formFieldLabel__organizationDomainDeletePending: "\u5220\u9664\u5F85\u5904\u7406\u7684\u9080\u8BF7\u548C\u5EFA\u8BAE",
  formFieldLabel__organizationDomainEmailAddress: "\u9A8C\u8BC1\u90AE\u7BB1\u5730\u5740",
  formFieldLabel__organizationDomainEmailAddressDescription: "\u8F93\u5165\u6B64\u57DF\u540D\u4E0B\u7684\u4E00\u4E2A\u90AE\u7BB1\u5730\u5740\u4EE5\u63A5\u6536\u9A8C\u8BC1\u7801\u5E76\u9A8C\u8BC1\u6B64\u57DF\u540D\u3002",
  formFieldLabel__organizationName: "\u7EC4\u7EC7\u540D\u79F0",
  formFieldLabel__organizationSlug: "URL \u7B80\u79F0",
  formFieldLabel__passkeyName: void 0,
  formFieldLabel__password: "\u5BC6\u7801",
  formFieldLabel__phoneNumber: "\u7535\u8BDD\u53F7\u7801",
  formFieldLabel__role: "\u89D2\u8272",
  formFieldLabel__signOutOfOtherSessions: "\u767B\u51FA\u6240\u6709\u5176\u4ED6\u8BBE\u5907",
  formFieldLabel__username: "\u7528\u6237\u540D",
  impersonationFab: {
    action__signOut: "\u9000\u51FA\u767B\u5F55",
    title: "\u4EE5 {{identifier}} \u767B\u5F55"
  },
  maintenanceMode: void 0,
  membershipRole__admin: "\u7BA1\u7406\u5458",
  membershipRole__basicMember: "\u6210\u5458",
  membershipRole__guestMember: "\u8BBF\u5BA2",
  organizationList: {
    action__createOrganization: "\u521B\u5EFA\u7EC4\u7EC7",
    action__invitationAccept: "\u52A0\u5165",
    action__suggestionsAccept: "\u8BF7\u6C42\u52A0\u5165",
    createOrganization: "\u521B\u5EFA\u7EC4\u7EC7",
    invitationAcceptedLabel: "\u5DF2\u52A0\u5165",
    subtitle: "\u4EE5\u7EE7\u7EED\u4F7F\u7528 {{applicationName}}",
    suggestionsAcceptedLabel: "\u7B49\u5F85\u6279\u51C6",
    title: "\u9009\u62E9\u4E00\u4E2A\u8D26\u6237",
    titleWithoutPersonal: "\u9009\u62E9\u4E00\u4E2A\u7EC4\u7EC7"
  },
  organizationProfile: {
    apiKeysPage: {
      title: void 0
    },
    badge__automaticInvitation: "\u81EA\u52A8\u9080\u8BF7",
    badge__automaticSuggestion: "\u81EA\u52A8\u5EFA\u8BAE",
    badge__manualInvitation: "\u65E0\u81EA\u52A8\u6CE8\u518C",
    badge__unverified: "\u672A\u9A8C\u8BC1",
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "\u6DFB\u52A0\u57DF\u540D\u4EE5\u8FDB\u884C\u9A8C\u8BC1\u3002\u5177\u6709\u6B64\u57DF\u540D\u7535\u5B50\u90AE\u4EF6\u5730\u5740\u7684\u7528\u6237\u53EF\u4EE5\u81EA\u52A8\u52A0\u5165\u7EC4\u7EC7\u6216\u8BF7\u6C42\u52A0\u5165\u3002",
      title: "\u6DFB\u52A0\u57DF\u540D"
    },
    invitePage: {
      detailsTitle__inviteFailed: "\u9080\u8BF7\u65E0\u6CD5\u53D1\u9001\u3002\u4FEE\u590D\u4EE5\u4E0B\u95EE\u9898\u7136\u540E\u91CD\u8BD5\uFF1A",
      formButtonPrimary__continue: "\u53D1\u9001\u9080\u8BF7",
      selectDropdown__role: "\u9009\u62E9\u89D2\u8272",
      subtitle: "\u9080\u8BF7\u65B0\u6210\u5458\u52A0\u5165\u6B64\u7EC4\u7EC7",
      successMessage: "\u9080\u8BF7\u6210\u529F\u53D1\u9001",
      title: "\u9080\u8BF7\u6210\u5458"
    },
    membersPage: {
      action__invite: "\u9080\u8BF7",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "\u79FB\u9664\u6210\u5458",
        tableHeader__actions: void 0,
        tableHeader__joined: "\u52A0\u5165",
        tableHeader__role: "\u89D2\u8272",
        tableHeader__user: "\u7528\u6237"
      },
      detailsTitle__emptyRow: "\u6CA1\u6709\u53EF\u663E\u793A\u7684\u6210\u5458",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "\u901A\u8FC7\u5C06\u7535\u5B50\u90AE\u4EF6\u57DF\u540D\u4E0E\u60A8\u7684\u7EC4\u7EC7\u8FDE\u63A5\u6765\u9080\u8BF7\u7528\u6237\u3002\u4EFB\u4F55\u4F7F\u7528\u5339\u914D\u7535\u5B50\u90AE\u4EF6\u57DF\u540D\u6CE8\u518C\u7684\u4EBA\u90FD\u53EF\u4EE5\u968F\u65F6\u52A0\u5165\u7EC4\u7EC7\u3002",
          headerTitle: "\u81EA\u52A8\u9080\u8BF7",
          primaryButton: "\u7BA1\u7406\u5DF2\u9A8C\u8BC1\u57DF\u540D"
        },
        table__emptyRow: "\u6CA1\u6709\u53EF\u663E\u793A\u7684\u9080\u8BF7"
      },
      invitedMembersTab: {
        menuAction__revoke: "\u64A4\u9500\u9080\u8BF7",
        tableHeader__invited: "\u5DF2\u9080\u8BF7"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "\u4F7F\u7528\u5339\u914D\u7535\u5B50\u90AE\u4EF6\u57DF\u540D\u6CE8\u518C\u7684\u7528\u6237\u5C06\u80FD\u591F\u770B\u5230\u8BF7\u6C42\u52A0\u5165\u60A8\u7EC4\u7EC7\u7684\u5EFA\u8BAE\u3002",
          headerTitle: "\u81EA\u52A8\u5EFA\u8BAE",
          primaryButton: "\u7BA1\u7406\u5DF2\u9A8C\u8BC1\u57DF\u540D"
        },
        menuAction__approve: "\u6279\u51C6",
        menuAction__reject: "\u62D2\u7EDD",
        tableHeader__requested: "\u5DF2\u8BF7\u6C42\u8BBF\u95EE",
        table__emptyRow: "\u6CA1\u6709\u53EF\u663E\u793A\u7684\u8BF7\u6C42"
      },
      start: {
        headerTitle__invitations: "\u9080\u8BF7",
        headerTitle__members: "\u6210\u5458",
        headerTitle__requests: "\u8BF7\u6C42"
      }
    },
    navbar: {
      apiKeys: void 0,
      billing: void 0,
      description: "\u7BA1\u7406\u60A8\u7684\u7EC4\u7EC7\u3002",
      general: "\u5E38\u89C4",
      members: "\u6210\u5458",
      title: "\u7EC4\u7EC7"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: '\u5728\u4E0B\u65B9\u8F93\u5165 "{{organizationName}}" \u4EE5\u7EE7\u7EED\u3002',
          messageLine1: "\u60A8\u786E\u5B9A\u8981\u5220\u9664\u6B64\u7EC4\u7EC7\u5417\uFF1F",
          messageLine2: "\u6B64\u64CD\u4F5C\u662F\u6C38\u4E45\u6027\u7684\u4E14\u65E0\u6CD5\u64A4\u9500\u3002",
          successMessage: "\u60A8\u5DF2\u5220\u9664\u8BE5\u7EC4\u7EC7\u3002",
          title: "\u5220\u9664\u7EC4\u7EC7"
        },
        leaveOrganization: {
          actionDescription: '\u5728\u4E0B\u65B9\u8F93\u5165 "{{organizationName}}" \u4EE5\u7EE7\u7EED\u3002',
          messageLine1: "\u60A8\u786E\u5B9A\u8981\u79BB\u5F00\u6B64\u7EC4\u7EC7\u5417\uFF1F\u60A8\u5C06\u5931\u53BB\u5BF9\u6B64\u7EC4\u7EC7\u53CA\u5176\u5E94\u7528\u7A0B\u5E8F\u7684\u8BBF\u95EE\u6743\u9650\u3002",
          messageLine2: "\u6B64\u64CD\u4F5C\u662F\u6C38\u4E45\u6027\u7684\u4E14\u65E0\u6CD5\u64A4\u9500\u3002",
          successMessage: "\u60A8\u5DF2\u79BB\u5F00\u4E86\u7EC4\u7EC7\u3002",
          title: "\u79BB\u5F00\u7EC4\u7EC7"
        },
        title: "\u5371\u9669"
      },
      domainSection: {
        menuAction__manage: "\u7BA1\u7406",
        menuAction__remove: "\u5220\u9664",
        menuAction__verify: "\u9A8C\u8BC1",
        primaryButton: "\u6DFB\u52A0\u57DF\u540D",
        subtitle: "\u5141\u8BB8\u7528\u6237\u6839\u636E\u5DF2\u9A8C\u8BC1\u7684\u7535\u5B50\u90AE\u4EF6\u57DF\u540D\u81EA\u52A8\u52A0\u5165\u7EC4\u7EC7\u6216\u8BF7\u6C42\u52A0\u5165\u3002",
        title: "\u5DF2\u9A8C\u8BC1\u57DF\u540D"
      },
      successMessage: "\u7EC4\u7EC7\u4FE1\u606F\u5DF2\u66F4\u65B0\u3002",
      title: "\u7EC4\u7EC7\u7B80\u4ECB"
    },
    removeDomainPage: {
      messageLine1: "\u7535\u5B50\u90AE\u4EF6\u57DF\u540D {{domain}} \u5C06\u88AB\u5220\u9664\u3002",
      messageLine2: "\u6B64\u540E\uFF0C\u7528\u6237\u5C06\u65E0\u6CD5\u81EA\u52A8\u52A0\u5165\u7EC4\u7EC7\u3002",
      successMessage: "{{domain}} \u5DF2\u88AB\u5220\u9664\u3002",
      title: "\u5220\u9664\u57DF\u540D"
    },
    start: {
      headerTitle__general: "\u5E38\u89C4",
      headerTitle__members: "\u6210\u5458",
      profileSection: {
        primaryButton: "\u66F4\u65B0\u7EC4\u7EC7\u7B80\u4ECB",
        title: "\u7EC4\u7EC7\u7B80\u4ECB",
        uploadAction__title: "\u6807\u5FD7"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "\u5220\u9664\u6B64\u57DF\u540D\u5C06\u5F71\u54CD\u53D7\u9080\u7528\u6237\u3002",
        removeDomainActionLabel__remove: "\u5220\u9664\u57DF\u540D",
        removeDomainSubtitle: "\u4ECE\u60A8\u7684\u5DF2\u9A8C\u8BC1\u57DF\u540D\u4E2D\u5220\u9664\u6B64\u57DF\u540D",
        removeDomainTitle: "\u5220\u9664\u57DF\u540D"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "\u7528\u6237\u5728\u6CE8\u518C\u65F6\u4F1A\u81EA\u52A8\u6536\u5230\u52A0\u5165\u7EC4\u7EC7\u7684\u9080\u8BF7\uFF0C\u5E76\u4E14\u53EF\u4EE5\u968F\u65F6\u52A0\u5165\u3002",
        automaticInvitationOption__label: "\u81EA\u52A8\u9080\u8BF7",
        automaticSuggestionOption__description: "\u7528\u6237\u4F1A\u6536\u5230\u8BF7\u6C42\u52A0\u5165\u7684\u5EFA\u8BAE\uFF0C\u4F46\u5728\u52A0\u5165\u7EC4\u7EC7\u4E4B\u524D\u5FC5\u987B\u5F97\u5230\u7BA1\u7406\u5458\u7684\u6279\u51C6\u3002",
        automaticSuggestionOption__label: "\u81EA\u52A8\u5EFA\u8BAE",
        calloutInfoLabel: "\u66F4\u6539\u6CE8\u518C\u6A21\u5F0F\u53EA\u4F1A\u5F71\u54CD\u65B0\u7528\u6237\u3002",
        calloutInvitationCountLabel: "\u5DF2\u53D1\u9001\u7ED9\u7528\u6237\u7684\u5F85\u5904\u7406\u9080\u8BF7\uFF1A{{count}}",
        calloutSuggestionCountLabel: "\u5DF2\u53D1\u9001\u7ED9\u7528\u6237\u7684\u5F85\u5904\u7406\u5EFA\u8BAE\uFF1A{{count}}",
        manualInvitationOption__description: "\u7528\u6237\u53EA\u80FD\u88AB\u624B\u52A8\u9080\u8BF7\u52A0\u5165\u7EC4\u7EC7\u3002",
        manualInvitationOption__label: "\u65E0\u81EA\u52A8\u6CE8\u518C",
        subtitle: "\u9009\u62E9\u6B64\u57DF\u540D\u7684\u7528\u6237\u5982\u4F55\u52A0\u5165\u7EC4\u7EC7\u3002"
      },
      start: {
        headerTitle__danger: "\u5371\u9669",
        headerTitle__enrollment: "\u6CE8\u518C\u9009\u9879"
      },
      subtitle: "\u57DF\u540D {{domain}} \u73B0\u5DF2\u9A8C\u8BC1\u3002\u7EE7\u7EED\u9009\u62E9\u6CE8\u518C\u6A21\u5F0F\u3002",
      title: "\u66F4\u65B0 {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "\u8F93\u5165\u53D1\u9001\u5230\u60A8\u90AE\u7BB1\u5730\u5740\u7684\u9A8C\u8BC1\u7801",
      formTitle: "\u9A8C\u8BC1\u7801",
      resendButton: "\u672A\u6536\u5230\u9A8C\u8BC1\u7801\uFF1F\u91CD\u65B0\u53D1\u9001",
      subtitle: "\u57DF\u540D {{domainName}} \u9700\u8981\u901A\u8FC7\u7535\u5B50\u90AE\u4EF6\u8FDB\u884C\u9A8C\u8BC1\u3002",
      subtitleVerificationCodeScreen: "\u9A8C\u8BC1\u7801\u5DF2\u53D1\u9001\u5230 {{emailAddress}}\u3002\u8F93\u5165\u9A8C\u8BC1\u7801\u4EE5\u7EE7\u7EED\u3002",
      title: "\u9A8C\u8BC1\u57DF\u540D"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "\u521B\u5EFA\u7EC4\u7EC7",
    action__invitationAccept: "\u52A0\u5165",
    action__manageOrganization: "\u7BA1\u7406\u7EC4\u7EC7",
    action__suggestionsAccept: "\u8BF7\u6C42\u52A0\u5165",
    notSelected: "\u672A\u9009\u62E9\u7EC4\u7EC7",
    personalWorkspace: "\u4E2A\u4EBA\u5DE5\u4F5C\u533A",
    suggestionsAcceptedLabel: "\u7B49\u5F85\u6279\u51C6"
  },
  paginationButton__next: "\u4E0B\u4E00\u9875",
  paginationButton__previous: "\u4E0A\u4E00\u9875",
  paginationRowText__displaying: "\u663E\u793A",
  paginationRowText__of: "\u7684",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "\u6DFB\u52A0\u8D26\u6237",
      action__signOutAll: "\u9000\u51FA\u6240\u6709\u8D26\u6237",
      subtitle: "\u9009\u62E9\u60A8\u8981\u7EE7\u7EED\u4F7F\u7528\u7684\u8D26\u6237\u3002",
      title: "\u9009\u62E9\u4E00\u4E2A\u8D26\u6237"
    },
    alternativeMethods: {
      actionLink: "\u83B7\u53D6\u5E2E\u52A9",
      actionText: "\u6CA1\u6709\u8FD9\u4E9B\uFF1F",
      blockButton__backupCode: "\u4F7F\u7528\u5907\u7528\u4EE3\u7801",
      blockButton__emailCode: "\u7535\u5B50\u90AE\u4EF6\u9A8C\u8BC1\u7801\u5230 {{identifier}}",
      blockButton__emailLink: "\u7535\u5B50\u90AE\u4EF6\u94FE\u63A5\u5230 {{identifier}}",
      blockButton__passkey: void 0,
      blockButton__password: "\u4F7F\u7528\u60A8\u7684\u5BC6\u7801\u767B\u5F55",
      blockButton__phoneCode: "\u53D1\u9001\u77ED\u4FE1\u4EE3\u7801\u5230 {{identifier}}",
      blockButton__totp: "\u4F7F\u7528\u60A8\u7684\u9A8C\u8BC1\u5E94\u7528\u7A0B\u5E8F",
      getHelp: {
        blockButton__emailSupport: "\u90AE\u4EF6\u652F\u6301",
        content: "\u5982\u679C\u60A8\u5728\u767B\u5F55\u8D26\u6237\u65F6\u9047\u5230\u56F0\u96BE\uFF0C\u8BF7\u7ED9\u6211\u4EEC\u53D1\u9001\u7535\u5B50\u90AE\u4EF6\uFF0C\u6211\u4EEC\u5C06\u5C3D\u5FEB\u8BA9\u60A8\u6062\u590D\u8BBF\u95EE\u3002",
        title: "\u83B7\u53D6\u5E2E\u52A9"
      },
      subtitle: "\u9047\u5230\u95EE\u9898\uFF1F\u60A8\u53EF\u4EE5\u4F7F\u7528\u4EE5\u4E0B\u4EFB\u4F55\u65B9\u6CD5\u767B\u5F55\u3002",
      title: "\u4F7F\u7528\u5176\u4ED6\u65B9\u6CD5"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "\u7EE7\u7EED\u4F7F\u7528 {{applicationName}}",
      title: "\u8F93\u5165\u5907\u7528\u4EE3\u7801"
    },
    emailCode: {
      formTitle: "\u9A8C\u8BC1\u7801",
      resendButton: "\u91CD\u65B0\u53D1\u9001\u9A8C\u8BC1\u7801",
      subtitle: "\u7EE7\u7EED\u4F7F\u7528 {{applicationName}}",
      title: "\u67E5\u770B\u60A8\u7684\u7535\u5B50\u90AE\u4EF6"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      expired: {
        subtitle: "\u8FD4\u56DE\u539F\u59CB\u6807\u7B7E\u9875\u7EE7\u7EED\u3002",
        title: "\u6B64\u9A8C\u8BC1\u94FE\u63A5\u5DF2\u8FC7\u671F"
      },
      failed: {
        subtitle: "\u8FD4\u56DE\u539F\u59CB\u6807\u7B7E\u9875\u7EE7\u7EED\u3002",
        title: "\u6B64\u9A8C\u8BC1\u94FE\u63A5\u65E0\u6548"
      },
      formSubtitle: "\u4F7F\u7528\u53D1\u9001\u5230\u60A8\u7684\u7535\u5B50\u90AE\u4EF6\u7684\u9A8C\u8BC1\u94FE\u63A5",
      formTitle: "\u9A8C\u8BC1\u94FE\u63A5",
      loading: {
        subtitle: "\u5373\u5C06\u4E3A\u60A8\u91CD\u5B9A\u5411",
        title: "\u6B63\u5728\u767B\u5F55..."
      },
      resendButton: "\u91CD\u65B0\u53D1\u9001\u94FE\u63A5",
      subtitle: "\u7EE7\u7EED\u4F7F\u7528 {{applicationName}}",
      title: "\u67E5\u770B\u60A8\u7684\u7535\u5B50\u90AE\u4EF6",
      unusedTab: {
        title: "\u60A8\u53EF\u4EE5\u5173\u95ED\u6B64\u6807\u7B7E\u9875"
      },
      verified: {
        subtitle: "\u5373\u5C06\u4E3A\u60A8\u91CD\u5B9A\u5411",
        title: "\u6210\u529F\u767B\u5F55"
      },
      verifiedSwitchTab: {
        subtitle: "\u8FD4\u56DE\u539F\u59CB\u6807\u7B7E\u9875\u7EE7\u7EED",
        subtitleNewTab: "\u8FD4\u56DE\u65B0\u6253\u5F00\u7684\u6807\u7B7E\u9875\u7EE7\u7EED",
        titleNewTab: "\u5728\u5176\u4ED6\u6807\u7B7E\u9875\u4E0A\u767B\u5F55"
      }
    },
    forgotPassword: {
      formTitle: "\u91CD\u7F6E\u5BC6\u7801\u4EE3\u7801",
      resendButton: "\u91CD\u65B0\u53D1\u9001\u4EE3\u7801",
      subtitle: "\u4EE5\u91CD\u7F6E\u60A8\u7684\u5BC6\u7801",
      subtitle_email: "\u9996\u5148\uFF0C\u8F93\u5165\u53D1\u9001\u5230\u60A8\u7684\u7535\u5B50\u90AE\u4EF6 ID \u7684\u4EE3\u7801",
      subtitle_phone: "\u9996\u5148\uFF0C\u8F93\u5165\u53D1\u9001\u5230\u60A8\u624B\u673A\u7684\u4EE3\u7801",
      title: "\u91CD\u7F6E\u5BC6\u7801"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "\u91CD\u7F6E\u5BC6\u7801",
      label__alternativeMethods: "\u6216\u8005\uFF0C\u4F7F\u7528\u5176\u4ED6\u65B9\u5F0F\u767B\u5F55\u3002",
      title: "\u5FD8\u8BB0\u5BC6\u7801\uFF1F"
    },
    noAvailableMethods: {
      message: "\u65E0\u6CD5\u7EE7\u7EED\u767B\u5F55\u3002\u6CA1\u6709\u53EF\u7528\u7684\u8EAB\u4EFD\u9A8C\u8BC1\u56E0\u7D20\u3002",
      subtitle: "\u51FA\u73B0\u9519\u8BEF",
      title: "\u65E0\u6CD5\u767B\u5F55"
    },
    passkey: {
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "\u4F7F\u7528\u5176\u4ED6\u65B9\u6CD5",
      subtitle: "\u7EE7\u7EED\u4F7F\u7528 {{applicationName}}",
      title: "\u8F93\u5165\u60A8\u7684\u5BC6\u7801"
    },
    passwordPwned: {
      title: void 0
    },
    phoneCode: {
      formTitle: "\u9A8C\u8BC1\u7801",
      resendButton: "\u91CD\u65B0\u53D1\u9001\u9A8C\u8BC1\u7801",
      subtitle: "\u7EE7\u7EED\u4F7F\u7528 {{applicationName}}",
      title: "\u68C0\u67E5\u624B\u673A\u77ED\u4FE1"
    },
    phoneCodeMfa: {
      formTitle: "\u9A8C\u8BC1\u7801",
      resendButton: "\u91CD\u65B0\u53D1\u9001\u9A8C\u8BC1\u7801",
      subtitle: "\u8BF7\u7EE7\u7EED\u8F93\u5165\u53D1\u9001\u5230\u60A8\u624B\u673A\u7684\u9A8C\u8BC1\u7801\u3002",
      title: "\u68C0\u67E5\u624B\u673A\u77ED\u4FE1"
    },
    resetPassword: {
      formButtonPrimary: "\u91CD\u7F6E\u5BC6\u7801",
      requiredMessage: "\u51FA\u4E8E\u5B89\u5168\u539F\u56E0\uFF0C\u9700\u8981\u91CD\u7F6E\u60A8\u7684\u5BC6\u7801\u3002",
      successMessage: "\u60A8\u7684\u5BC6\u7801\u5DF2\u6210\u529F\u66F4\u6539\u3002\u6B63\u5728\u4E3A\u60A8\u767B\u5F55\uFF0C\u8BF7\u7A0D\u7B49\u3002",
      title: "\u91CD\u7F6E\u5BC6\u7801"
    },
    resetPasswordMfa: {
      detailsLabel: "\u6211\u4EEC\u9700\u8981\u9A8C\u8BC1\u60A8\u7684\u8EAB\u4EFD\u624D\u80FD\u91CD\u7F6E\u60A8\u7684\u5BC6\u7801\u3002"
    },
    start: {
      actionLink: "\u6CE8\u518C",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "\u4F7F\u7528\u7535\u5B50\u90AE\u4EF6",
      actionLink__use_email_username: "\u4F7F\u7528\u7535\u5B50\u90AE\u4EF6\u6216\u7528\u6237\u540D",
      actionLink__use_passkey: void 0,
      actionLink__use_phone: "\u4F7F\u7528\u7535\u8BDD",
      actionLink__use_username: "\u4F7F\u7528\u7528\u6237\u540D",
      actionText: "\u8FD8\u6CA1\u6709\u8D26\u6237\uFF1F",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "\u7EE7\u7EED\u4F7F\u7528 {{applicationName}}",
      subtitleCombined: void 0,
      title: "\u767B\u5F55",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "\u9A8C\u8BC1\u7801",
      subtitle: "\u8BF7\u7EE7\u7EED\u8F93\u5165\u7531\u60A8\u7684\u8EAB\u4EFD\u9A8C\u8BC1\u5E94\u7528\u751F\u6210\u7684\u9A8C\u8BC1\u7801\u3002",
      title: "\u4E24\u6B65\u9A8C\u8BC1"
    }
  },
  signInEnterPasswordTitle: "\u8F93\u5165\u60A8\u7684\u5BC6\u7801",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "\u767B\u5F55",
      actionText: "\u5DF2\u7ECF\u6709\u8D26\u6237\u4E86\uFF1F",
      subtitle: "\u7EE7\u7EED\u4F7F\u7528 {{applicationName}}",
      title: "\u586B\u5199\u7F3A\u5C11\u7684\u5B57\u6BB5"
    },
    emailCode: {
      formSubtitle: "\u8F93\u5165\u53D1\u9001\u5230\u60A8\u7684\u7535\u5B50\u90AE\u4EF6\u5730\u5740\u7684\u9A8C\u8BC1\u7801",
      formTitle: "\u9A8C\u8BC1\u7801",
      resendButton: "\u91CD\u65B0\u53D1\u9001\u9A8C\u8BC1\u7801",
      subtitle: "\u7EE7\u7EED\u4F7F\u7528 {{applicationName}}",
      title: "\u9A8C\u8BC1\u60A8\u7684\u7535\u5B50\u90AE\u4EF6"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      formSubtitle: "\u4F7F\u7528\u53D1\u9001\u5230\u60A8\u7684\u7535\u5B50\u90AE\u4EF6\u5730\u5740\u7684\u9A8C\u8BC1\u94FE\u63A5",
      formTitle: "\u9A8C\u8BC1\u94FE\u63A5",
      loading: {
        title: "\u6B63\u5728\u6CE8\u518C..."
      },
      resendButton: "\u91CD\u65B0\u53D1\u9001\u94FE\u63A5",
      subtitle: "\u7EE7\u7EED\u4F7F\u7528 {{applicationName}}",
      title: "\u9A8C\u8BC1\u60A8\u7684\u7535\u5B50\u90AE\u4EF6",
      verified: {
        title: "\u6210\u529F\u6CE8\u518C"
      },
      verifiedSwitchTab: {
        subtitle: "\u8FD4\u56DE\u65B0\u6253\u5F00\u7684\u6807\u7B7E\u9875\u7EE7\u7EED",
        subtitleNewTab: "\u8FD4\u56DE\u4E0A\u4E00\u4E2A\u6807\u7B7E\u9875\u7EE7\u7EED",
        title: "\u6210\u529F\u9A8C\u8BC1\u7535\u5B50\u90AE\u4EF6"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: void 0,
        label__onlyTermsOfService: void 0,
        label__termsOfServiceAndPrivacyPolicy: void 0
      },
      continue: {
        subtitle: void 0,
        title: void 0
      }
    },
    phoneCode: {
      formSubtitle: "\u8F93\u5165\u53D1\u9001\u5230\u60A8\u7684\u7535\u8BDD\u53F7\u7801\u7684\u9A8C\u8BC1\u7801",
      formTitle: "\u9A8C\u8BC1\u7801",
      resendButton: "\u91CD\u65B0\u53D1\u9001\u9A8C\u8BC1\u7801",
      subtitle: "\u7EE7\u7EED\u4F7F\u7528 {{applicationName}}",
      title: "\u9A8C\u8BC1\u60A8\u7684\u7535\u8BDD"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "\u767B\u5F55",
      actionLink__use_email: void 0,
      actionLink__use_phone: void 0,
      actionText: "\u5DF2\u7ECF\u6709\u8D26\u6237\u4E86\uFF1F",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "\u7EE7\u7EED\u4F7F\u7528 {{applicationName}}",
      subtitleCombined: "\u7EE7\u7EED\u4F7F\u7528 {{applicationName}}",
      title: "\u521B\u5EFA\u60A8\u7684\u8D26\u6237",
      titleCombined: "\u521B\u5EFA\u60A8\u7684\u8D26\u6237"
    }
  },
  socialButtonsBlockButton: "\u4F7F\u7528 {{provider|titleize}} \u767B\u5F55",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "\u7531\u4E8E\u5B89\u5168\u9A8C\u8BC1\u5931\u8D25\uFF0C\u6CE8\u518C\u672A\u6210\u529F\u3002\u8BF7\u5237\u65B0\u9875\u9762\u91CD\u8BD5\u6216\u8054\u7CFB\u652F\u6301\u83B7\u53D6\u66F4\u591A\u5E2E\u52A9\u3002",
    captcha_unavailable: "\u6CE8\u518C\u5931\u8D25\uFF0C\u539F\u56E0\u662F\u672A\u901A\u8FC7\u673A\u5668\u4EBA\u9A8C\u8BC1\u3002\u8BF7\u5237\u65B0\u9875\u9762\u91CD\u8BD5\u6216\u8054\u7CFB\u652F\u6301\u56E2\u961F\u4EE5\u83B7\u53D6\u66F4\u591A\u5E2E\u52A9\u3002",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: void 0,
    form_identifier_exists__phone_number: void 0,
    form_identifier_exists__username: void 0,
    form_identifier_not_found: "\u6211\u4EEC\u65E0\u6CD5\u627E\u5230\u5177\u6709\u8FD9\u4E9B\u4FE1\u606F\u7684\u8D26\u6237\u3002",
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: "\u90AE\u7BB1\u5730\u5740\u5FC5\u987B\u662F\u6709\u6548\u7684\u90AE\u7BB1\u683C\u5F0F\u3002",
    form_param_format_invalid__phone_number: "\u7535\u8BDD\u53F7\u7801\u5FC5\u987B\u7B26\u5408\u6709\u6548\u7684\u56FD\u9645\u683C\u5F0F\u3002",
    form_param_max_length_exceeded__first_name: "\u540D\u5B57\u957F\u5EA6\u4E0D\u5F97\u8D85\u8FC7256\u4E2A\u5B57\u7B26\u3002",
    form_param_max_length_exceeded__last_name: "\u59D3\u6C0F\u957F\u5EA6\u4E0D\u5F97\u8D85\u8FC7256\u4E2A\u5B57\u7B26\u3002",
    form_param_max_length_exceeded__name: "\u59D3\u540D\u957F\u5EA6\u4E0D\u5F97\u8D85\u8FC7256\u4E2A\u5B57\u7B26\u3002",
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: void 0,
    form_password_not_strong_enough: "\u60A8\u7684\u5BC6\u7801\u5F3A\u5EA6\u4E0D\u591F\u3002",
    form_password_pwned: "\u8FD9\u4E2A\u5BC6\u7801\u5728\u6570\u636E\u6CC4\u9732\u4E2D\u88AB\u53D1\u73B0\uFF0C\u4E0D\u80FD\u4F7F\u7528\uFF0C\u8BF7\u6362\u4E00\u4E2A\u5BC6\u7801\u8BD5\u8BD5\u3002",
    form_password_pwned__sign_in: void 0,
    form_password_size_in_bytes_exceeded: "\u60A8\u7684\u5BC6\u7801\u8D85\u8FC7\u4E86\u5141\u8BB8\u7684\u6700\u5927\u5B57\u8282\u6570\uFF0C\u8BF7\u7F29\u77ED\u5B83\u6216\u53BB\u6389\u4E00\u4E9B\u7279\u6B8A\u5B57\u7B26\u3002",
    form_password_validation_failed: "\u5BC6\u7801\u9519\u8BEF",
    form_username_invalid_character: void 0,
    form_username_invalid_length: void 0,
    identification_deletion_failed: "\u60A8\u65E0\u6CD5\u5220\u9664\u6700\u540E\u4E00\u4E2A\u8EAB\u4EFD\u6807\u8BC6\u3002",
    not_allowed_access: "\u60A8\u4F7F\u7528\u7684\u7535\u5B50\u90AE\u4EF6\u5730\u5740\u6216\u7535\u8BDD\u53F7\u7801\u4E0D\u5141\u8BB8\u6CE8\u518C\u3002\u8FD9\u53EF\u80FD\u662F\u56E0\u4E3A\u60A8\u5728\u7535\u5B50\u90AE\u4EF6\u5730\u5740\u4E2D\u4F7F\u7528\u4E86 '+', '=', '#' \u6216 '.'\uFF0C\u4F7F\u7528\u4E86\u4E0E\u4E34\u65F6\u7535\u5B50\u90AE\u4EF6\u670D\u52A1\u5173\u8054\u7684\u57DF\u540D\uFF0C\u6216\u8005\u6709\u660E\u786E\u7684\u6392\u9664\u3002\u5982\u679C\u60A8\u8BA4\u4E3A\u8FD9\u662F\u9519\u8BEF\uFF0C\u8BF7\u8054\u7CFB\u652F\u6301\u3002",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: void 0,
    passkey_not_supported: void 0,
    passkey_pa_not_supported: void 0,
    passkey_registration_cancelled: void 0,
    passkey_retrieval_cancelled: void 0,
    passwordComplexity: {
      maximumLength: "\u5C11\u4E8E{{length}}\u4E2A\u5B57\u7B26",
      minimumLength: "{{length}}\u4E2A\u6216\u66F4\u591A\u5B57\u7B26",
      requireLowercase: "\u4E00\u4E2A\u5C0F\u5199\u5B57\u6BCD",
      requireNumbers: "\u4E00\u4E2A\u6570\u5B57",
      requireSpecialCharacter: "\u4E00\u4E2A\u7279\u6B8A\u5B57\u7B26",
      requireUppercase: "\u4E00\u4E2A\u5927\u5199\u5B57\u6BCD",
      sentencePrefix: "\u60A8\u7684\u5BC6\u7801\u5FC5\u987B\u5305\u542B"
    },
    phone_number_exists: "\u8BE5\u7535\u8BDD\u53F7\u7801\u5DF2\u88AB\u4F7F\u7528\uFF0C\u8BF7\u5C1D\u8BD5\u5176\u4ED6\u53F7\u7801\u3002",
    session_exists: "\u60A8\u5DF2\u767B\u5F55\u3002",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "\u60A8\u7684\u5BC6\u7801\u53EF\u4EE5\u7528\uFF0C\u4F46\u53EF\u4EE5\u66F4\u5F3A\u3002\u8BD5\u7740\u6DFB\u52A0\u66F4\u591A\u5B57\u7B26\u3002",
      goodPassword: "\u505A\u5F97\u597D\u3002\u8FD9\u662F\u4E00\u4E2A\u4F18\u79C0\u7684\u5BC6\u7801\u3002",
      notEnough: "\u60A8\u7684\u5BC6\u7801\u5F3A\u5EA6\u4E0D\u591F\u3002",
      suggestions: {
        allUppercase: "\u5927\u5199\u4E00\u4E9B\uFF0C\u4F46\u4E0D\u662F\u6240\u6709\u7684\u5B57\u6BCD\u3002",
        anotherWord: "\u6DFB\u52A0\u66F4\u4E0D\u5E38\u89C1\u7684\u66F4\u591A\u5355\u8BCD\u3002",
        associatedYears: "\u907F\u514D\u4E0E\u4F60\u6709\u5173\u7684\u5E74\u4EFD\u3002",
        capitalization: "\u5927\u5199\u4E0D\u4EC5\u4EC5\u662F\u7B2C\u4E00\u4E2A\u5B57\u6BCD\u3002",
        dates: "\u907F\u514D\u4E0E\u4F60\u6709\u5173\u7684\u65E5\u671F\u548C\u5E74\u4EFD\u3002",
        l33t: '\u907F\u514D\u9884\u6D4B\u7684\u5B57\u6BCD\u66FF\u6362\uFF0C\u5982"@"\u4EE3\u66FF"a"\u3002',
        longerKeyboardPattern: "\u4F7F\u7528\u66F4\u957F\u7684\u952E\u76D8\u6A21\u5F0F\uFF0C\u5E76\u591A\u6B21\u6539\u53D8\u6253\u5B57\u65B9\u5411\u3002",
        noNeed: "\u4F60\u53EF\u4EE5\u521B\u5EFA\u5F3A\u5BC6\u7801\uFF0C\u800C\u65E0\u9700\u4F7F\u7528\u7B26\u53F7\uFF0C\u6570\u5B57\u6216\u5927\u5199\u5B57\u6BCD\u3002",
        pwned: "\u5982\u679C\u60A8\u5728\u5176\u4ED6\u5730\u65B9\u4F7F\u7528\u6B64\u5BC6\u7801\uFF0C\u60A8\u5E94\u8BE5\u66F4\u6539\u5B83\u3002",
        recentYears: "\u907F\u514D\u8FD1\u5E74\u6765\u3002",
        repeated: "\u907F\u514D\u91CD\u590D\u7684\u5355\u8BCD\u548C\u5B57\u7B26\u3002",
        reverseWords: "\u907F\u514D\u5E38\u7528\u8BCD\u7684\u53CD\u5411\u62FC\u5199\u3002",
        sequences: "\u907F\u514D\u5E38\u89C1\u5B57\u7B26\u5E8F\u5217\u3002",
        useWords: "\u4F7F\u7528\u591A\u4E2A\u5355\u8BCD\uFF0C\u4F46\u907F\u514D\u5E38\u89C1\u77ED\u8BED\u3002"
      },
      warnings: {
        common: "\u8FD9\u662F\u4E00\u4E2A\u5E38\u7528\u7684\u5BC6\u7801\u3002",
        commonNames: "\u5E38\u89C1\u7684\u540D\u5B57\u548C\u59D3\u6C0F\u6613\u88AB\u731C\u5230\u3002",
        dates: "\u65E5\u671F\u6613\u88AB\u731C\u5230\u3002",
        extendedRepeat: '\u50CF"abcabcabc"\u8FD9\u6837\u7684\u91CD\u590D\u5B57\u7B26\u6A21\u5F0F\u6613\u88AB\u731C\u5230\u3002',
        keyPattern: "\u77ED\u952E\u76D8\u6A21\u5F0F\u6613\u88AB\u731C\u5230\u3002",
        namesByThemselves: "\u5355\u4E2A\u540D\u5B57\u6216\u59D3\u6C0F\u6613\u88AB\u731C\u5230\u3002",
        pwned: "\u60A8\u7684\u5BC6\u7801\u5728\u4E92\u8054\u7F51\u4E0A\u7684\u6570\u636E\u6CC4\u9732\u4E2D\u88AB\u66B4\u9732\u3002",
        recentYears: "\u8FD1\u5E74\u6765\u6613\u88AB\u731C\u5230\u3002",
        sequences: '\u50CF"abc"\u8FD9\u6837\u7684\u5E38\u89C1\u5B57\u7B26\u5E8F\u5217\u6613\u88AB\u731C\u5230\u3002',
        similarToCommon: "\u8FD9\u4E2A\u5BC6\u7801\u548C\u5E38\u7528\u5BC6\u7801\u76F8\u4F3C\u3002",
        simpleRepeat: '\u50CF"aaa"\u8FD9\u6837\u7684\u91CD\u590D\u5B57\u7B26\u6613\u88AB\u731C\u5230\u3002',
        straightRow: "\u952E\u76D8\u4E0A\u7684\u76F4\u884C\u952E\u6613\u88AB\u731C\u5230\u3002",
        topHundred: "\u8FD9\u662F\u4E00\u4E2A\u9891\u7E41\u4F7F\u7528\u7684\u5BC6\u7801\u3002",
        topTen: "\u8FD9\u662F\u4E00\u4E2A\u5927\u91CF\u4F7F\u7528\u7684\u5BC6\u7801\u3002",
        userInputs: "\u4E0D\u5E94\u8BE5\u6709\u4EFB\u4F55\u4E2A\u4EBA\u6216\u9875\u9762\u76F8\u5173\u7684\u6570\u636E\u3002",
        wordByItself: "\u5355\u4E2A\u5355\u8BCD\u6613\u88AB\u731C\u5230\u3002"
      }
    }
  },
  userButton: {
    action__addAccount: "\u6DFB\u52A0\u8D26\u6237",
    action__manageAccount: "\u7BA1\u7406\u8D26\u6237",
    action__signOut: "\u9000\u51FA\u767B\u5F55",
    action__signOutAll: "\u9000\u51FA\u6240\u6709\u8D26\u6237"
  },
  userProfile: {
    apiKeysPage: {
      title: void 0
    },
    backupCodePage: {
      actionLabel__copied: "\u5DF2\u590D\u5236\uFF01",
      actionLabel__copy: "\u590D\u5236\u5168\u90E8",
      actionLabel__download: "\u4E0B\u8F7D .txt",
      actionLabel__print: "\u6253\u5370",
      infoText1: "\u5C06\u4E3A\u6B64\u8D26\u6237\u542F\u7528\u5907\u4EFD\u4EE3\u7801\u3002",
      infoText2: "\u4FDD\u5BC6\u5E76\u5B89\u5168\u5B58\u50A8\u5907\u4EFD\u4EE3\u7801\u3002\u5982\u679C\u60A8\u6000\u7591\u5B83\u4EEC\u5DF2\u7ECF\u6CC4\u9732\uFF0C\u60A8\u53EF\u4EE5\u91CD\u65B0\u751F\u6210\u5907\u4EFD\u4EE3\u7801\u3002",
      subtitle__codelist: "\u5B89\u5168\u5B58\u50A8\u5E76\u4FDD\u5B88\u79D8\u5BC6\u3002",
      successMessage: "\u73B0\u5728\u5DF2\u542F\u7528\u5907\u4EFD\u4EE3\u7801\u3002\u5982\u679C\u60A8\u5931\u53BB\u4E86\u9A8C\u8BC1\u8BBE\u5907\u7684\u8BBF\u95EE\u6743\u9650\uFF0C\u60A8\u53EF\u4EE5\u4F7F\u7528\u5176\u4E2D\u4E4B\u4E00\u767B\u5F55\u60A8\u7684\u8D26\u6237\u3002\u6BCF\u4E2A\u4EE3\u7801\u53EA\u80FD\u4F7F\u7528\u4E00\u6B21\u3002",
      successSubtitle: "\u5982\u679C\u60A8\u5931\u53BB\u4E86\u9A8C\u8BC1\u8BBE\u5907\u7684\u8BBF\u95EE\u6743\u9650\uFF0C\u60A8\u53EF\u4EE5\u4F7F\u7528\u5176\u4E2D\u4E4B\u4E00\u767B\u5F55\u60A8\u7684\u8D26\u6237\u3002",
      title: "\u6DFB\u52A0\u5907\u4EFD\u4EE3\u7801\u9A8C\u8BC1",
      title__codelist: "\u5907\u4EFD\u4EE3\u7801"
    },
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "\u9009\u62E9\u4E00\u4E2A\u4F9B\u5E94\u5546\u6765\u8FDE\u63A5\u60A8\u7684\u8D26\u6237\u3002",
      formHint__noAccounts: "\u6CA1\u6709\u53EF\u7528\u7684\u5916\u90E8\u8D26\u6237\u4F9B\u5E94\u5546\u3002",
      removeResource: {
        messageLine1: "{{identifier}} \u5C06\u4ECE\u6B64\u8D26\u6237\u4E2D\u88AB\u79FB\u9664\u3002",
        messageLine2: "\u60A8\u5C06\u65E0\u6CD5\u518D\u4F7F\u7528\u8FD9\u4E2A\u5DF2\u8FDE\u63A5\u7684\u8D26\u6237\uFF0C\u4EFB\u4F55\u4F9D\u8D56\u7684\u529F\u80FD\u5C06\u4E0D\u518D\u5DE5\u4F5C\u3002",
        successMessage: "{{connectedAccount}} \u5DF2\u4ECE\u60A8\u7684\u8D26\u6237\u4E2D\u79FB\u9664\u3002",
        title: "\u79FB\u9664\u5DF2\u8FDE\u63A5\u7684\u8D26\u6237"
      },
      socialButtonsBlockButton: "\u8FDE\u63A5 {{provider|titleize}} \u8D26\u6237",
      successMessage: "\u4F9B\u5E94\u5546\u5DF2\u88AB\u6DFB\u52A0\u5230\u60A8\u7684\u8D26\u6237",
      title: "\u6DFB\u52A0\u5DF2\u8FDE\u63A5\u7684\u8D26\u6237"
    },
    deletePage: {
      actionDescription: '\u5728\u4E0B\u65B9\u8F93\u5165 "Delete account" \u4EE5\u7EE7\u7EED\u3002',
      confirm: "Delete account",
      messageLine1: "\u60A8\u786E\u5B9A\u8981\u5220\u9664\u60A8\u7684\u8D26\u6237\u5417\uFF1F",
      messageLine2: "\u6B64\u64CD\u4F5C\u662F\u6C38\u4E45\u4E14\u4E0D\u53EF\u9006\u7684\u3002",
      title: "\u5220\u9664\u8D26\u6237"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "\u4E00\u5C01\u542B\u6709\u9A8C\u8BC1\u7801\u7684\u90AE\u4EF6\u5C06\u4F1A\u88AB\u53D1\u9001\u5230\u8FD9\u4E2A\u7535\u5B50\u90AE\u4EF6\u5730\u5740\u3002",
        formSubtitle: "\u8F93\u5165\u53D1\u9001\u5230 {{identifier}} \u7684\u9A8C\u8BC1\u7801",
        formTitle: "\u9A8C\u8BC1\u7801",
        resendButton: "\u91CD\u53D1\u9A8C\u8BC1\u7801",
        successMessage: "\u7535\u5B50\u90AE\u4EF6 {{identifier}} \u5DF2\u88AB\u6DFB\u52A0\u5230\u60A8\u7684\u8D26\u6237\u3002"
      },
      emailLink: {
        formHint: "\u4E00\u5C01\u542B\u6709\u9A8C\u8BC1\u94FE\u63A5\u7684\u90AE\u4EF6\u5C06\u4F1A\u88AB\u53D1\u9001\u5230\u8FD9\u4E2A\u7535\u5B50\u90AE\u4EF6\u5730\u5740\u3002",
        formSubtitle: "\u70B9\u51FB\u53D1\u9001\u5230 {{identifier}} \u7684\u90AE\u4EF6\u4E2D\u7684\u9A8C\u8BC1\u94FE\u63A5",
        formTitle: "\u9A8C\u8BC1\u94FE\u63A5",
        resendButton: "\u91CD\u53D1\u94FE\u63A5",
        successMessage: "\u7535\u5B50\u90AE\u4EF6 {{identifier}} \u5DF2\u88AB\u6DFB\u52A0\u5230\u60A8\u7684\u8D26\u6237\u3002"
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} \u5C06\u4ECE\u6B64\u8D26\u6237\u4E2D\u88AB\u79FB\u9664\u3002",
        messageLine2: "\u60A8\u5C06\u65E0\u6CD5\u4F7F\u7528\u8FD9\u4E2A\u7535\u5B50\u90AE\u4EF6\u5730\u5740\u767B\u5F55\u3002",
        successMessage: "\u7535\u5B50\u90AE\u4EF6 {{emailAddress}} \u5DF2\u4ECE\u60A8\u7684\u8D26\u6237\u4E2D\u79FB\u9664\u3002",
        title: "\u79FB\u9664\u7535\u5B50\u90AE\u4EF6\u5730\u5740"
      },
      title: "\u6DFB\u52A0\u7535\u5B50\u90AE\u4EF6\u5730\u5740",
      verifyTitle: "Verify email address"
    },
    formButtonPrimary__add: "\u6DFB\u52A0",
    formButtonPrimary__continue: "\u7EE7\u7EED",
    formButtonPrimary__finish: "\u5B8C\u6210",
    formButtonPrimary__remove: "\u79FB\u9664",
    formButtonPrimary__save: "\u4FDD\u5B58",
    formButtonReset: "\u53D6\u6D88",
    mfaPage: {
      formHint: "\u9009\u62E9\u4E00\u4E2A\u6DFB\u52A0\u7684\u65B9\u6CD5\u3002",
      title: "\u6DFB\u52A0\u4E24\u6B65\u9A8C\u8BC1"
    },
    mfaPhoneCodePage: {
      backButton: "\u4F7F\u7528\u73B0\u6709\u53F7\u7801",
      primaryButton__addPhoneNumber: "\u6DFB\u52A0\u7535\u8BDD\u53F7\u7801",
      removeResource: {
        messageLine1: "{{identifier}} \u5C06\u4E0D\u518D\u5728\u767B\u5F55\u65F6\u63A5\u6536\u9A8C\u8BC1\u4EE3\u7801\u3002",
        messageLine2: "\u60A8\u7684\u8D26\u6237\u53EF\u80FD\u4E0D\u518D\u5B89\u5168\u3002\u60A8\u786E\u5B9A\u8981\u7EE7\u7EED\u5417\uFF1F",
        successMessage: "\u5DF2\u79FB\u9664{{mfaPhoneCode}}\u7684\u77ED\u4FE1\u9A8C\u8BC1\u7801\u4E24\u6B65\u9A8C\u8BC1",
        title: "\u79FB\u9664\u4E24\u6B65\u9A8C\u8BC1"
      },
      subtitle__availablePhoneNumbers: "\u9009\u62E9\u4E00\u4E2A\u7535\u8BDD\u53F7\u7801\u6765\u6CE8\u518C\u77ED\u4FE1\u9A8C\u8BC1\u7801\u4E24\u6B65\u9A8C\u8BC1\u3002",
      subtitle__unavailablePhoneNumbers: "\u6CA1\u6709\u53EF\u7528\u7684\u7535\u8BDD\u53F7\u7801\u6765\u6CE8\u518C\u77ED\u4FE1\u9A8C\u8BC1\u7801\u4E24\u6B65\u9A8C\u8BC1\u3002",
      successMessage1: "\u767B\u5F55\u65F6\uFF0C\u60A8\u9700\u8981\u8F93\u5165\u53D1\u9001\u5230\u6B64\u7535\u8BDD\u53F7\u7801\u7684\u9A8C\u8BC1\u7801\u4F5C\u4E3A\u989D\u5916\u7684\u6B65\u9AA4\u3002",
      successMessage2: "\u8BF7\u4FDD\u5B58\u8FD9\u4E9B\u5907\u4EFD\u4EE3\u7801\u5E76\u5C06\u5176\u59A5\u5584\u5B58\u653E\u3002\u5982\u679C\u60A8\u65E0\u6CD5\u8BBF\u95EE\u8EAB\u4EFD\u9A8C\u8BC1\u8BBE\u5907\uFF0C\u53EF\u4EE5\u4F7F\u7528\u5907\u4EFD\u4EE3\u7801\u8FDB\u884C\u767B\u5F55\u3002",
      successTitle: "\u77ED\u4FE1\u9A8C\u8BC1\u7801\u9A8C\u8BC1\u5DF2\u542F\u7528",
      title: "\u6DFB\u52A0\u77ED\u4FE1\u9A8C\u8BC1\u7801\u9A8C\u8BC1"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "\u626B\u63CF\u4E8C\u7EF4\u7801",
        buttonUnableToScan__nonPrimary: "\u4E0D\u80FD\u626B\u63CF\u4E8C\u7EF4\u7801\uFF1F",
        infoText__ableToScan: "\u5728\u60A8\u7684\u9A8C\u8BC1\u5668\u5E94\u7528\u4E2D\u8BBE\u7F6E\u4E00\u4E2A\u65B0\u7684\u767B\u5F55\u65B9\u6CD5\uFF0C\u5E76\u626B\u63CF\u4E0B\u9762\u7684\u4E8C\u7EF4\u7801\u5C06\u5176\u94FE\u63A5\u5230\u60A8\u7684\u8D26\u6237\u3002",
        infoText__unableToScan: "\u5728\u9A8C\u8BC1\u5668\u4E2D\u8BBE\u7F6E\u4E00\u4E2A\u65B0\u7684\u767B\u5F55\u65B9\u6CD5\uFF0C\u5E76\u8F93\u5165\u4E0B\u9762\u63D0\u4F9B\u7684 Key\u3002",
        inputLabel__unableToScan1: "\u786E\u4FDD\u542F\u7528\u4E86\u57FA\u4E8E\u65F6\u95F4\u6216\u4E00\u6B21\u6027\u5BC6\u7801\uFF0C\u7136\u540E\u5B8C\u6210\u94FE\u63A5\u60A8\u7684\u8D26\u6237\u3002",
        inputLabel__unableToScan2: "\u6216\u8005\uFF0C\u5982\u679C\u60A8\u7684\u9A8C\u8BC1\u5668\u652F\u6301 TOTP URIs\uFF0C\u60A8\u4E5F\u53EF\u4EE5\u590D\u5236\u5B8C\u6574\u7684 URI\u3002"
      },
      removeResource: {
        messageLine1: "\u767B\u5F55\u65F6\uFF0C\u5C06\u4E0D\u518D\u9700\u8981\u6765\u81EA\u6B64\u9A8C\u8BC1\u5668\u7684\u9A8C\u8BC1\u7801\u3002",
        messageLine2: "\u60A8\u7684\u8D26\u6237\u53EF\u80FD\u4E0D\u518D\u5B89\u5168\u3002\u60A8\u786E\u5B9A\u8981\u7EE7\u7EED\u5417\uFF1F",
        successMessage: "\u5DF2\u79FB\u9664\u901A\u8FC7\u9A8C\u8BC1\u5668\u5E94\u7528\u7A0B\u5E8F\u7684\u4E24\u6B65\u9A8C\u8BC1\u3002",
        title: "\u79FB\u9664\u4E24\u6B65\u9A8C\u8BC1"
      },
      successMessage: "\u73B0\u5728\u5DF2\u542F\u7528\u4E24\u6B65\u9A8C\u8BC1\u3002\u5728\u767B\u5F55\u65F6\uFF0C\u60A8\u9700\u8981\u8F93\u5165\u6765\u81EA\u6B64\u9A8C\u8BC1\u5668\u7684\u9A8C\u8BC1\u7801\u4F5C\u4E3A\u989D\u5916\u6B65\u9AA4\u3002",
      title: "\u6DFB\u52A0\u9A8C\u8BC1\u5668\u5E94\u7528\u7A0B\u5E8F",
      verifySubtitle: "\u8F93\u5165\u60A8\u7684\u9A8C\u8BC1\u5668\u751F\u6210\u7684\u9A8C\u8BC1\u7801",
      verifyTitle: "\u9A8C\u8BC1\u4EE3\u7801"
    },
    mobileButton__menu: "\u83DC\u5355",
    navbar: {
      account: "\u8D26\u6237",
      apiKeys: void 0,
      billing: void 0,
      description: "\u7BA1\u7406\u60A8\u7684\u8D26\u6237\u3002",
      security: "\u5B89\u5168",
      title: "\u8D26\u6237"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: void 0,
        title: void 0
      },
      subtitle__rename: void 0,
      title__rename: void 0
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "\u5EFA\u8BAE\u60A8\u4ECE\u6240\u6709\u53EF\u80FD\u4F7F\u7528\u8FC7\u65E7\u5BC6\u7801\u7684\u5176\u4ED6\u8BBE\u5907\u4E0A\u9000\u51FA\u767B\u5F55\u3002",
      readonly: "\u7531\u4E8E\u60A8\u53EA\u80FD\u901A\u8FC7\u4F01\u4E1A\u8FDE\u63A5\u767B\u5F55\uFF0C\u5F53\u524D\u65E0\u6CD5\u7F16\u8F91\u60A8\u7684\u5BC6\u7801\u3002",
      successMessage__set: "\u60A8\u7684\u5BC6\u7801\u5DF2\u8BBE\u7F6E\u3002",
      successMessage__signOutOfOtherSessions: "\u6240\u6709\u5176\u4ED6\u8BBE\u5907\u5DF2\u9000\u51FA\u3002",
      successMessage__update: "\u60A8\u7684\u5BC6\u7801\u5DF2\u66F4\u65B0\u3002",
      title__set: "\u8BBE\u7F6E\u5BC6\u7801",
      title__update: "\u66F4\u6539\u5BC6\u7801"
    },
    phoneNumberPage: {
      infoText: "\u4E00\u6761\u5305\u542B\u9A8C\u8BC1\u94FE\u63A5\u7684\u77ED\u4FE1\u5C06\u4F1A\u53D1\u9001\u5230\u8FD9\u4E2A\u7535\u8BDD\u53F7\u7801\u3002",
      removeResource: {
        messageLine1: "{{identifier}} \u5C06\u4ECE\u6B64\u8D26\u6237\u4E2D\u88AB\u79FB\u9664\u3002",
        messageLine2: "\u60A8\u5C06\u65E0\u6CD5\u4F7F\u7528\u8FD9\u4E2A\u7535\u8BDD\u53F7\u7801\u767B\u5F55\u3002",
        successMessage: "\u7535\u8BDD\u53F7\u7801 {{phoneNumber}} \u5DF2\u4ECE\u60A8\u7684\u8D26\u6237\u4E2D\u79FB\u9664\u3002",
        title: "\u79FB\u9664\u7535\u8BDD\u53F7\u7801"
      },
      successMessage: "{{identifier}} \u5DF2\u88AB\u6DFB\u52A0\u5230\u60A8\u7684\u8D26\u6237\u3002",
      title: "\u6DFB\u52A0\u7535\u8BDD\u53F7\u7801",
      verifySubtitle: "\u8BF7\u8F93\u5165\u53D1\u9001\u81F3 {{identifier}} \u7684\u9A8C\u8BC1\u7801",
      verifyTitle: "\u9A8C\u8BC1\u7535\u8BDD\u53F7\u7801"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "\u4E0A\u4F20\u5C0F\u4E8E10MB\u7684JPG, PNG, GIF, \u6216WEBP\u683C\u5F0F\u7684\u56FE\u7247",
      imageFormDestructiveActionSubtitle: "\u79FB\u9664\u56FE\u7247",
      imageFormSubtitle: "\u4E0A\u4F20\u56FE\u7247",
      imageFormTitle: "\u4E2A\u4EBA\u8D44\u6599\u56FE\u7247",
      readonly: "\u60A8\u7684\u4E2A\u4EBA\u4FE1\u606F\u7531\u4F01\u4E1A\u8FDE\u63A5\u63D0\u4F9B\uFF0C\u65E0\u6CD5\u8FDB\u884C\u7F16\u8F91\u3002",
      successMessage: "\u60A8\u7684\u4E2A\u4EBA\u8D44\u6599\u5DF2\u66F4\u65B0\u3002",
      title: "\u66F4\u65B0\u4E2A\u4EBA\u8D44\u6599"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "\u9000\u51FA\u8BBE\u5907",
        title: "\u6D3B\u52A8\u8BBE\u5907"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "\u518D\u8BD5\u4E00\u6B21",
        actionLabel__reauthorize: "\u7ACB\u5373\u6388\u6743",
        destructiveActionTitle: "\u79FB\u9664",
        primaryButton: "\u8FDE\u63A5\u8D26\u6237",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "\u201C\u6240\u9700\u7684\u6743\u9650\u8303\u56F4\u5DF2\u66F4\u65B0\uFF0C\u60A8\u53EF\u80FD\u4F1A\u9047\u5230\u529F\u80FD\u53D7\u9650\u7684\u95EE\u9898\u3002\u8BF7\u91CD\u65B0\u6388\u6743\u6B64\u5E94\u7528\u7A0B\u5E8F\uFF0C\u4EE5\u907F\u514D\u51FA\u73B0\u4EFB\u4F55\u95EE\u9898\u3002\u201D",
        title: "\u5DF2\u8FDE\u63A5\u7684\u8D26\u6237"
      },
      dangerSection: {
        deleteAccountButton: "\u5220\u9664\u8D26\u6237",
        title: "\u7EC8\u6B62\u8D26\u6237"
      },
      emailAddressesSection: {
        destructiveAction: "\u79FB\u9664\u7535\u5B50\u90AE\u4EF6\u5730\u5740",
        detailsAction__nonPrimary: "\u8BBE\u4E3A\u4E3B\u8981",
        detailsAction__primary: "\u5B8C\u6210\u9A8C\u8BC1",
        detailsAction__unverified: "\u5B8C\u6210\u9A8C\u8BC1",
        primaryButton: "\u6DFB\u52A0\u7535\u5B50\u90AE\u4EF6\u5730\u5740",
        title: "\u7535\u5B50\u90AE\u4EF6\u5730\u5740"
      },
      enterpriseAccountsSection: {
        title: "\u4F01\u4E1A\u8D26\u6237"
      },
      headerTitle__account: "\u8D26\u6237",
      headerTitle__security: "\u5B89\u5168",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "\u91CD\u65B0\u751F\u6210\u4EE3\u7801",
          headerTitle: "\u5907\u4EFD\u4EE3\u7801",
          subtitle__regenerate: "\u83B7\u53D6\u4E00\u5957\u65B0\u7684\u5B89\u5168\u5907\u4EFD\u4EE3\u7801\u3002\u4E4B\u524D\u7684\u5907\u4EFD\u4EE3\u7801\u5C06\u88AB\u5220\u9664\uFF0C\u65E0\u6CD5\u4F7F\u7528\u3002",
          title__regenerate: "\u91CD\u65B0\u751F\u6210\u5907\u4EFD\u4EE3\u7801"
        },
        phoneCode: {
          actionLabel__setDefault: "\u8BBE\u4E3A\u9ED8\u8BA4",
          destructiveActionLabel: "\u79FB\u9664\u7535\u8BDD\u53F7\u7801"
        },
        primaryButton: "\u6DFB\u52A0\u4E24\u6B65\u9A8C\u8BC1",
        title: "\u4E24\u6B65\u9A8C\u8BC1",
        totp: {
          destructiveActionTitle: "\u79FB\u9664",
          headerTitle: "\u9A8C\u8BC1\u5668\u5E94\u7528\u7A0B\u5E8F"
        }
      },
      passkeysSection: {
        menuAction__destructive: void 0,
        menuAction__rename: void 0,
        primaryButton: void 0,
        title: void 0
      },
      passwordSection: {
        primaryButton__setPassword: "\u8BBE\u7F6E\u5BC6\u7801",
        primaryButton__updatePassword: "\u66F4\u6539\u5BC6\u7801",
        title: "\u5BC6\u7801"
      },
      phoneNumbersSection: {
        destructiveAction: "\u79FB\u9664\u7535\u8BDD\u53F7\u7801",
        detailsAction__nonPrimary: "\u8BBE\u4E3A\u4E3B\u8981",
        detailsAction__primary: "\u5B8C\u6210\u9A8C\u8BC1",
        detailsAction__unverified: "\u5B8C\u6210\u9A8C\u8BC1",
        primaryButton: "\u6DFB\u52A0\u7535\u8BDD\u53F7\u7801",
        title: "\u7535\u8BDD\u53F7\u7801"
      },
      profileSection: {
        primaryButton: "\u66F4\u65B0\u4E2A\u4EBA\u8D44\u6599",
        title: "\u4E2A\u4EBA\u8D44\u6599"
      },
      usernameSection: {
        primaryButton__setUsername: "\u8BBE\u7F6E\u7528\u6237\u540D",
        primaryButton__updateUsername: "\u66F4\u6539\u7528\u6237\u540D",
        title: "\u7528\u6237\u540D"
      },
      web3WalletsSection: {
        destructiveAction: "\u79FB\u9664\u94B1\u5305",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3 \u94B1\u5305",
        title: "Web3 \u94B1\u5305"
      }
    },
    usernamePage: {
      successMessage: "\u60A8\u7684\u7528\u6237\u540D\u5DF2\u66F4\u65B0\u3002",
      title__set: "\u66F4\u65B0\u7528\u6237\u540D",
      title__update: "\u66F4\u65B0\u7528\u6237\u540D"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} \u5C06\u4ECE\u6B64\u8D26\u6237\u4E2D\u88AB\u79FB\u9664\u3002",
        messageLine2: "\u60A8\u5C06\u65E0\u6CD5\u4F7F\u7528\u8FD9\u4E2A web3 \u94B1\u5305\u767B\u5F55\u3002",
        successMessage: "{{web3Wallet}} \u5DF2\u4ECE\u60A8\u7684\u8D26\u6237\u4E2D\u79FB\u9664\u3002",
        title: "\u79FB\u9664 web3 \u94B1\u5305"
      },
      subtitle__availableWallets: "\u9009\u62E9\u4E00\u4E2A web3 \u94B1\u5305\u8FDE\u63A5\u5230\u60A8\u7684\u8D26\u6237\u3002",
      subtitle__unavailableWallets: "\u6CA1\u6709\u53EF\u7528\u7684 web3 \u94B1\u5305\u3002",
      successMessage: "\u94B1\u5305\u5DF2\u88AB\u6DFB\u52A0\u5230\u60A8\u7684\u8D26\u6237\u3002",
      title: "\u6DFB\u52A0web3\u94B1\u5305",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  zhCN
};
//# sourceMappingURL=zh-CN.mjs.map