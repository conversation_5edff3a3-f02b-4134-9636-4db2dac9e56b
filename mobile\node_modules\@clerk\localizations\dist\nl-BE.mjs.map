{"version": 3, "sources": ["../src/nl-BE.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const nlBE: LocalizationResource = {\n  locale: 'nl-NL',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Terug',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Standaard',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Ander impersonatie apparaat',\n  badge__primary: 'Primair',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Actie vereist',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Dit apparaat',\n  badge__unverified: 'Ongeverifieerd',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Gebruikersapparaat',\n  badge__you: 'Jij',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Creëer organisatie',\n    invitePage: {\n      formButtonReset: 'Overslaan',\n    },\n    title: 'Creëer organisatie',\n  },\n  dates: {\n    lastDay: \"Gisteren om {{ date | timeString('nl-NL') }}\",\n    next6Days: \"{{ date | weekday('nl-NL','long') }} om {{ date | timeString('nl-NL') }}\",\n    nextDay: \"Morgen om {{ date | timeString('nl-NL') }}\",\n    numeric: \"{{ date | numeric('nl-NL') }}\",\n    previous6Days: \"Vorige {{ date | weekday('nl-NL','long') }} om {{ date | timeString('nl-NL') }}\",\n    sameDay: \"Vandaag om {{ date | timeString('nl-NL') }}\",\n  },\n  dividerText: 'of',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Een andere methode gebruiken',\n  footerPageLink__help: 'Helppagina',\n  footerPageLink__privacy: 'Privacybeleid',\n  footerPageLink__terms: 'Algemene voorwaarden',\n  formButtonPrimary: 'Doorgaan',\n  formButtonPrimary__verify: 'Verifieer',\n  formFieldAction__forgotPassword: 'Wachtwoord vergeten?',\n  formFieldError__matchingPasswords: 'Wachtwoorden matchen.',\n  formFieldError__notMatchingPasswords: 'Wachtwoorden komen niet overeen.',\n  formFieldError__verificationLinkExpired: 'De verificatielink is verlopen. Vraag een nieuwe link aan.',\n  formFieldHintText__optional: 'Optioneel',\n  formFieldHintText__slug: 'Een slug is een leesbare ID die uniek moet zijn. Het wordt vaak gebruikt in URL’s.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: 'Voer een back-upcode in',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Verwijder account',\n  formFieldInputPlaceholder__emailAddress: '<EMAIL>',\n  formFieldInputPlaceholder__emailAddress_username: '<EMAIL>',\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: 'Voornaam',\n  formFieldInputPlaceholder__lastName: 'Achternaam',\n  formFieldInputPlaceholder__organizationDomain: 'voorbeeld.domein',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: '<EMAIL>',\n  formFieldInputPlaceholder__organizationName: 'Organisatienaam',\n  formFieldInputPlaceholder__organizationSlug: 'mijn-org',\n  formFieldInputPlaceholder__password: 'Wachtwoord',\n  formFieldInputPlaceholder__phoneNumber: 'Telefoonnummer',\n  formFieldInputPlaceholder__username: 'Gebruikersnaam',\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Automatische uitnodigingen inschakelen voor dit domein',\n  formFieldLabel__backupCode: 'Backupcode',\n  formFieldLabel__confirmDeletion: 'Bevestiging',\n  formFieldLabel__confirmPassword: 'Wachtwoord bevestigen',\n  formFieldLabel__currentPassword: 'Huidig wachtwoord',\n  formFieldLabel__emailAddress: 'E-mailadres',\n  formFieldLabel__emailAddress_username: 'E-mailadres of gebruikersnaam',\n  formFieldLabel__emailAddresses: 'E-mailadressen',\n  formFieldLabel__firstName: 'Voornaam',\n  formFieldLabel__lastName: 'Achternaam',\n  formFieldLabel__newPassword: 'Nieuw wachtwoord',\n  formFieldLabel__organizationDomain: 'Domain',\n  formFieldLabel__organizationDomainDeletePending: 'Verwijder uitnodigingen en suggesties',\n  formFieldLabel__organizationDomainEmailAddress: 'Verificatie-e-mailadres',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Voer een e-mailadres onder dit domein in om een code te ontvangen en dit domein te verifiëren.',\n  formFieldLabel__organizationName: 'Organisatienaam',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Naam',\n  formFieldLabel__password: 'Wachtwoord',\n  formFieldLabel__phoneNumber: 'Telefoonnummer',\n  formFieldLabel__role: 'Rol',\n  formFieldLabel__signOutOfOtherSessions: 'Alle andere apparaten uitloggen',\n  formFieldLabel__username: 'Gebruikersnaam',\n  impersonationFab: {\n    action__signOut: 'Uitloggen',\n    title: 'Ingelogd als {{identifier}}',\n  },\n  maintenanceMode: 'Onderhoudsmodus',\n  membershipRole__admin: 'Beheerder',\n  membershipRole__basicMember: 'Lid',\n  membershipRole__guestMember: 'Gast',\n  organizationList: {\n    action__createOrganization: 'Creëer organisatie',\n    action__invitationAccept: 'Toetreden',\n    action__suggestionsAccept: 'Verzoek om toetreden',\n    createOrganization: 'Creëer organisatie',\n    invitationAcceptedLabel: 'Toegetreden',\n    subtitle: 'om door te gaan naar {{applicationName}}',\n    suggestionsAcceptedLabel: 'In behandeling',\n    title: 'Kies een organisatie',\n    titleWithoutPersonal: 'Kies een organisatie',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Automatische Uitnodiging',\n    badge__automaticSuggestion: 'Automatische Suggesties',\n    badge__manualInvitation: 'Geen automatische inschrijving',\n    badge__unverified: 'Ongeverifieerd',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Voeg het domein toe om te verifiëren. Gebruikers met e-mailadressen in dit domein kunnen de organisatie automatisch toegang krijgen of een verzoek om toegang te maken.',\n      title: 'Domein toevoegen',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'De uitnodigingen konden niet verzonden worden. Los het volgende op en probeer het opnieuw:',\n      formButtonPrimary__continue: 'Uitnodigingen verzenden',\n      selectDropdown__role: 'Selecteer rol',\n      subtitle: 'Nodig nieuwe leden uit voor deze organisatie',\n      successMessage: 'Uitnodigingen succesvol verzonden',\n      title: 'Leden uitnodigen',\n    },\n    membersPage: {\n      action__invite: 'Uitnodigen',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Verwijder lid',\n        tableHeader__actions: 'Acties',\n        tableHeader__joined: 'Toegetreden',\n        tableHeader__role: 'Rol',\n        tableHeader__user: 'Gebruiker',\n      },\n      detailsTitle__emptyRow: 'Geen leden gevonden',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Uitnodig gebruikers door een domein toe te voegen aan je organisatie. Iedereen die zich aanmeldt met een e-mailadres in dit domein kan de organisatie automatisch toegang krijgen of een verzoek om toegang te maken.',\n          headerTitle: 'Automatische uitnodigingen',\n          primaryButton: 'Beheer geverifieerde domeinen',\n        },\n        table__emptyRow: 'Geen uitnodigingen gevonden',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Uitnodiging intrekken',\n        tableHeader__invited: 'Uitgenodigd',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Gebruikers die zich aanmelden met een e-mailadres in dit domein, kunnen een verzoek om toegang tot de organisatie zien.',\n          headerTitle: 'Automatische suggesties',\n          primaryButton: 'Beheer geverifieerde domeinen',\n        },\n        menuAction__approve: 'Goedkeuren',\n        menuAction__reject: 'Weigeren',\n        tableHeader__requested: 'Verzoek om toegang',\n        table__emptyRow: 'Geen verzoeken om toegang',\n      },\n      start: {\n        headerTitle__invitations: 'Uitnodigingen',\n        headerTitle__members: 'Leden',\n        headerTitle__requests: 'Verzoeken',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Beheer je organisatie.',\n      general: 'Algemeen',\n      members: 'Leden',\n      title: 'Organisatie',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Typ \"{{organizationName}}\" hieronder om door te gaan.',\n          messageLine1: 'Weet je zeker dat je deze organisatie wilt verwijderen?',\n          messageLine2: 'Deze actie is permanent en onomkeerbaar.',\n          successMessage: 'Je hebt deze organisatie verwijderd.',\n          title: 'Organisatie verwijderen',\n        },\n        leaveOrganization: {\n          actionDescription: 'Typ \"{{organizationName}}\" hieronder om door te gaan.',\n          messageLine1:\n            'Weet je zeker dat je deze organisatie wilt verlaten? Je zult toegang verliezen tot deze organisatie en haar applicaties.',\n          messageLine2: 'Deze actie is permanent en onomkeerbaar.',\n          successMessage: 'Je hebt deze organisatie verlaten.',\n          title: 'Verlaat organisatie',\n        },\n        title: 'Gevaar',\n      },\n      domainSection: {\n        menuAction__manage: 'Beheer',\n        menuAction__remove: 'Verwijder',\n        menuAction__verify: 'Verifieer',\n        primaryButton: 'Domein toevoegen',\n        subtitle:\n          'Laat gebruikers de organisatie automatisch toegang krijgen of een verzoek om toegang maken op basis van een geverifieerd e-maildomein.',\n        title: 'Geverifieerde domeinen',\n      },\n      successMessage: 'De organisatie is bijgewerkt.',\n      title: 'Organisatieprofiel',\n    },\n    removeDomainPage: {\n      messageLine1: 'Het e-maildomein {{domain}} wordt verwijderd.',\n      messageLine2:\n        'Gebruikers kunnen de organisatie niet meer automatisch toegang krijgen na dit domein te verwijderen.',\n      successMessage: '{{domain}} is verwijderd.',\n      title: 'Domein verwijderen',\n    },\n    start: {\n      headerTitle__general: 'Algemeen',\n      headerTitle__members: 'Leden',\n      profileSection: {\n        primaryButton: '',\n        title: 'Organisatieprofiel',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Verwijderen van dit domein zal uitnodigingen beïnvloeden.',\n        removeDomainActionLabel__remove: 'Domein verwijderen',\n        removeDomainSubtitle: 'Verwijder dit domein van je geverifieerde domeinen',\n        removeDomainTitle: 'Domein verwijderen',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Gebruikers worden automatisch uitgenodigd om lid te worden van de organisatie wanneer ze zich aanmelden en kunnen lid worden wanneer ze dat willen.',\n        automaticInvitationOption__label: 'Automatische uitnodigingen',\n        automaticSuggestionOption__description:\n          'Gebruikers ontvangen een aanbeveling om lid te worden, maar moeten worden goedgekeurd door een beheerder voordat ze toegang kunnen krijgen tot de organisatie.',\n        automaticSuggestionOption__label: 'Automatische suggesties',\n        calloutInfoLabel: 'Wijziging van de inschrijfmodus heeft alleen invloed op nieuwe gebruikers.',\n        calloutInvitationCountLabel: 'Uitnodigingen verzonden aan gebruikers: {{count}}',\n        calloutSuggestionCountLabel: 'Aanbevelingen verzonden aan gebruikers: {{count}}',\n        manualInvitationOption__description:\n          'Gebruikers kunnen alleen handmatig worden uitgenodigd voor de organisatie.',\n        manualInvitationOption__label: 'Geen automatische inschrijving',\n        subtitle: 'Kies hoe gebruikers van dit domein toegang kunnen krijgen tot de organisatie.',\n      },\n      start: {\n        headerTitle__danger: 'Danger',\n        headerTitle__enrollment: 'Inschrijfopties',\n      },\n      subtitle: 'Het domein {{domain}} is nu geverifieerd. Ga verder door de inschrijfmodus te selecteren.',\n      title: 'Update {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Voer de verificatiecode in die verzonden is naar je e-mailadres',\n      formTitle: 'Verificatiecode',\n      resendButton: 'Niet ontvangen? Verstuur opnieuw',\n      subtitle: 'Het domein {{domainName}} moet worden geverifieerd via e-mail.',\n      subtitleVerificationCodeScreen: 'A verification code was sent to {{emailAddress}}. Enter the code to continue.',\n      title: 'Verifieer domein',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Maak organisatie aan',\n    action__invitationAccept: 'Join',\n    action__manageOrganization: 'Beheer organisatie',\n    action__suggestionsAccept: 'Verzoek om lid te worden',\n    notSelected: 'Geen organisatie geselecteerd',\n    personalWorkspace: 'Persoonlijke werkruimte',\n    suggestionsAcceptedLabel: 'In behandeling',\n  },\n  paginationButton__next: 'Volgende',\n  paginationButton__previous: 'Vorige',\n  paginationRowText__displaying: 'Weergeven',\n  paginationRowText__of: 'van',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Krijg hulp',\n      actionText: 'Heb je geen van deze?',\n      blockButton__backupCode: 'Backupcode gebruiken',\n      blockButton__emailCode: 'Email code naar {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Doorgaan met je wachtwoord',\n      blockButton__phoneCode: 'Verzend SMS code naar {{identifier}}',\n      blockButton__totp: 'Use your authenticator app',\n      getHelp: {\n        blockButton__emailSupport: 'Email ondersteuning',\n        content:\n          'Als je moeite hebt om je account te verifiëren, email ons en we zullen met je werken om toegang te herstellen zo snel mogelijk.',\n        title: 'Krijg hulp',\n      },\n      subtitle: 'Problemen? Je kunt een van deze methoden gebruiken voor verificatie.',\n      title: 'Gebruik een andere methode',\n    },\n    backupCodeMfa: {\n      subtitle: 'Je backupcode is de code die je kreeg bij het installeren van tweestapsverificatie.',\n      title: 'Backupcode invoeren',\n    },\n    emailCode: {\n      formTitle: 'Verificatiecode',\n      resendButton: 'Niet ontvangen? Opnieuw verzenden',\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      title: 'Controleer je email',\n    },\n    noAvailableMethods: {\n      message: 'Kan niet verder gaan met verificatie. Er is geen beschikbare verificatiefactor.',\n      subtitle: 'Er is een fout opgetreden',\n      title: 'Kan je account niet verifiëren',\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Gebruik een andere methode',\n      subtitle: 'Voer het wachtwoord in dat bij je account hoort',\n      title: 'Voer je wachtwoord in',\n    },\n    phoneCode: {\n      formTitle: 'Verificatiecode',\n      resendButton: 'Niet ontvangen? Opnieuw verzenden',\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      title: 'Controleer je telefoon',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Verificatiecode',\n      resendButton: 'Niet ontvangen? Opnieuw verzenden',\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      title: 'Controleer je telefoon',\n    },\n    totpMfa: {\n      formTitle: 'Verificatiecode',\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      title: 'Tweestapsverificatie',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Account toevoegen',\n      action__signOutAll: 'Uitloggen van alle accounts',\n      subtitle: 'Selecteer het account met welk je door wilt gaan.',\n      title: 'Kies een account',\n    },\n    alternativeMethods: {\n      actionLink: 'Help',\n      actionText: 'Heb je geen van deze?',\n      blockButton__backupCode: 'Gebruik een backupcode',\n      blockButton__emailCode: 'Verzend code naar {{identifier}}',\n      blockButton__emailLink: 'Verzend link naar {{identifier}}',\n      blockButton__passkey: 'Gebruik toegangssleutel',\n      blockButton__password: 'Inloggen met je wachtwoord',\n      blockButton__phoneCode: 'Verzend code naar {{identifier}}',\n      blockButton__totp: 'Gebruik je authenticator app',\n      getHelp: {\n        blockButton__emailSupport: 'E-mail {{applicationName}}',\n        content: 'Als je geen toegang hebt neem dan contact op met {{applicationName}} en we helpen je verder.',\n        title: 'Help',\n      },\n      subtitle: 'Problemen? Je kan een van deze methoden gebruiken om in te loggen.',\n      title: 'Gebruik een andere methode',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      title: 'Voer een backupcode in',\n    },\n    emailCode: {\n      formTitle: 'Verificatiecode',\n      resendButton: 'Verstuur code opnieuw',\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      title: 'Check je e-mail',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'De client komt niet overeen met wat verwacht werd. Probeer het opnieuw.',\n        title: 'Clientfout',\n      },\n      expired: {\n        subtitle: 'Ga naar de oorspronkelijke tab om verder te gaan.',\n        title: 'Deze verificatielink is verlopen',\n      },\n      failed: {\n        subtitle: 'Ga naar de oorspronkelijke tab om verder te gaan.',\n        title: 'Deze verificatielink is niet geldig',\n      },\n      formSubtitle: 'Gebruik de verificatielink die verzonden is naar je e-mailadres',\n      formTitle: 'Verificatielink',\n      loading: {\n        subtitle: 'Je zal weldra doorgestuurd worden',\n        title: 'Inloggen ...',\n      },\n      resendButton: 'Verstuur link opnieuw',\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      title: 'Check je e-mail',\n      unusedTab: {\n        title: 'Je kan deze tab sluiten.',\n      },\n      verified: {\n        subtitle: 'Je zal weldra doorgestuurd worden',\n        title: 'Successvol ingelogd',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Ga naar de oorspronkelijke tab om verder te gaan',\n        subtitleNewTab: 'Ga naar de pasgeopende tab om verder te gaan',\n        titleNewTab: 'Ingelogd in andere tab',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Wachtwoord resetcode',\n      resendButton: 'Niet ontvangen? Verstuur opnieuw',\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      subtitle_email: 'Voer de code in die verzonden is naar je e-mailadres',\n      subtitle_phone: 'Voer de code in die verzonden is naar je telefoon',\n      title: 'Wachtwoord resetten',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Wachtwoord resetten',\n      label__alternativeMethods: 'Of log in met een andere methode',\n      title: 'Wachtwoord vergeten?',\n    },\n    noAvailableMethods: {\n      message: 'Het is niet mogelijk om door te gaan met inloggen. Er is geen beschikbare authenticatiefactor.',\n      subtitle: 'Er heeft zich een fout voorgedaan',\n      title: 'Inloggen niet mogelijk',\n    },\n    passkey: {\n      subtitle: 'Gebruik je toegangssleutel voor authenticatie.',\n      title: 'Authenticatie met toegangssleutel',\n    },\n    password: {\n      actionLink: 'Gebruik een andere methode',\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      title: 'Vul je wachtwoord in',\n    },\n    passwordPwned: {\n      title: 'Dit wachtwoord is gelekt bij een datalek. Kies een ander wachtwoord om veiligheidsredenen.',\n    },\n    phoneCode: {\n      formTitle: 'Verificatiecode',\n      resendButton: 'Verstuur code opnieuw',\n      subtitle: 'om verder te gaan naar {{applicationName}}',\n      title: 'Check je telefoon',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Verificatiecode',\n      resendButton: 'Verstuur code opnieuw',\n      subtitle: '',\n      title: 'Check je telefoon',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Wachtwoord resetten',\n      requiredMessage: 'Voor veiligheidsredenen is het vereist om je wachtwoord te resetten.',\n      successMessage: 'Je wachtwoord is succesvol gewijzigd. We sturen je door naar de inlogpagina.',\n      title: 'Wachtwoord resetten',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Voor veiligheidsredenen is het vereist om je wachtwoord te resetten.',\n    },\n    start: {\n      actionLink: 'Registreren',\n      actionLink__join_waitlist: 'Meld je aan voor de wachtlijst',\n      actionLink__use_email: 'Gebruik e-mail',\n      actionLink__use_email_username: 'Gebruik e-mail of gebruikersnaam',\n      actionLink__use_passkey: 'Gebruik toegangssleutel',\n      actionLink__use_phone: 'Gebruik telefoon',\n      actionLink__use_username: 'Gebruik gebruikersnaam',\n      actionText: 'Geen account?',\n      actionText__join_waitlist: 'Nog geen account?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Inloggen',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Verificatiecode',\n      subtitle: '',\n      title: 'Tweestapsverificatie',\n    },\n  },\n  signInEnterPasswordTitle: 'Vul je wachtwoord in',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Inloggen',\n      actionText: 'Heb je een account?',\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      title: 'Vul de ontbrekende velden in',\n    },\n    emailCode: {\n      formSubtitle: 'Voer de verificatiecode in die verzonden is naar je e-mailadres',\n      formTitle: 'Verificatiecode',\n      resendButton: 'Verstuur code opnieuw',\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      title: 'Bevestig je e-mailadres',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'De client komt niet overeen met het verwachte. Probeer het opnieuw.',\n        title: 'Clientfout',\n      },\n      formSubtitle: 'Gebruik de verificatielink die verzonden is naar je e-mailadres',\n      formTitle: 'Verificatielink',\n      loading: {\n        title: 'Registreren ...',\n      },\n      resendButton: 'Verstuur link opnieuw',\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      title: 'Bevestig je e-mailadres',\n      verified: {\n        title: 'Succesvol geregistreerd',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Ga naar de pas geopende tab om verder te gaan',\n        subtitleNewTab: 'Ga naar de vorige tab om verder te gaan',\n        title: 'E-mail bevestigd',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Ik accepteer het Privacybeleid',\n        label__onlyTermsOfService: 'Ik accepteer de Algemene Voorwaarden',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Ik accepteer de {{ termsOfServiceLink || link(\"Algemene Voorwaarden\") }} en het {{ privacyPolicyLink || link(\"Privacybeleid\") }}',\n      },\n      continue: {\n        subtitle: 'Door verder te gaan, ga je akkoord met de bovenstaande voorwaarden.',\n        title: 'Doorgaan',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Voer de verificatiecode in die verzonden is naar je telefoonnummer',\n      formTitle: 'Verificatiecode',\n      resendButton: 'Verstuur code opnieuw',\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      title: 'Bevestig je telefoonnummer',\n    },\n    restrictedAccess: {\n      actionLink: 'Neem contact op met de ondersteuning',\n      actionText: 'Hulp nodig?',\n      blockButton__emailSupport: 'Stuur e-mail naar ondersteuning',\n      blockButton__joinWaitlist: 'Meld je aan voor de wachtlijst',\n      subtitle: 'Je toegang is beperkt. Neem contact met ons op voor meer informatie.',\n      subtitleWaitlist: 'We wachten op je toegang tot de wachtlijst. Bedankt voor je geduld.',\n      title: 'Beperkte toegang',\n    },\n    start: {\n      actionLink: 'Inloggen',\n      actionLink__use_email: 'Gebruik e-mail',\n      actionLink__use_phone: 'Gebruik telefoon',\n      actionText: 'Heb je al een account?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'om door te gaan naar {{applicationName}}',\n      subtitleCombined: 'om door te gaan naar {{applicationName}}',\n      title: 'Maak je account aan',\n      titleCombined: 'Maak je account aan',\n    },\n  },\n  socialButtonsBlockButton: 'Ga verder met {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: 'Ga verder met {{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: 'Je bent al lid van de organisatie.',\n    captcha_invalid:\n      'Aanmelding mislukt vanwege mislukte beveiligingsvalidaties. Vernieuw de pagina om het opnieuw te proberen of neem contact op met de ondersteuning voor verdere hulp.',\n    captcha_unavailable:\n      'Aanmelding mislukt vanwege mislukte botvalidatie. Vernieuw de pagina om het opnieuw te proberen of neem contact op met de ondersteuning voor verdere hulp.',\n    form_code_incorrect: 'De ingevoerde code is incorrect.',\n    form_identifier_exists__email_address: 'Dit e-mailadres is al in gebruik.',\n    form_identifier_exists__phone_number: 'Dit telefoonnummer is al in gebruik.',\n    form_identifier_exists__username: 'Deze gebruikersnaam is al in gebruik.',\n    form_identifier_not_found: 'We konden geen account vinden met deze details.',\n    form_param_format_invalid: 'Het formaat van het ingevoerde gegeven is ongeldig.',\n    form_param_format_invalid__email_address: 'E-mailadres moet een geldig e-mailadres zijn.',\n    form_param_format_invalid__phone_number: 'Telefoonnummer moet een geldig internationaal nummer zijn.',\n    form_param_max_length_exceeded__first_name: 'Voornaam moet minder dan 256 tekens bevatten.',\n    form_param_max_length_exceeded__last_name: 'Achternaam moet minder dan 256 tekens bevatten.',\n    form_param_max_length_exceeded__name: 'Naam moet minder dan 256 tekens bevatten.',\n    form_param_nil: 'Dit veld mag niet leeg zijn.',\n    form_param_value_invalid: 'De waarde die je hebt ingevoerd is ongeldig.',\n    form_password_incorrect: 'Het wachtwoord is incorrect.',\n    form_password_length_too_short: 'Het wachtwoord is te kort.',\n    form_password_not_strong_enough: 'Je wachtwoord is niet sterk genoeg.',\n    form_password_pwned: 'Dit wachtwoord is in een datalek gevonden.',\n    form_password_pwned__sign_in: 'Als je dit wachtwoord elders gebruikt, moet je het wijzigen.',\n    form_password_size_in_bytes_exceeded:\n      'Je wachtwoord heeft het maximum aantal bytes overschreden, vermijd speciale tekens.',\n    form_password_validation_failed: 'Wachtwoord is incorrect.',\n    form_username_invalid_character: 'De gebruikersnaam bevat ongeldige tekens.',\n    form_username_invalid_length: 'De gebruikersnaam is te kort of te lang.',\n    identification_deletion_failed: 'Je kunt je laatste identificatie niet verwijderen.',\n    not_allowed_access:\n      \"Je e-mailadres of telefoonnummer is niet toegestaan voor registratie. Dit kan zijn omdat je '+', '=', '#' of '.' in je e-mailadres gebruikt, een domein dat is gekoppeld aan een tijdelijke e-mailservice gebruikt, of een expliciete uitsluiting heeft.\",\n    organization_domain_blocked: 'Het domein van de organisatie is geblokkeerd.',\n    organization_domain_common: 'Het domein van de organisatie is te algemeen.',\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: 'Het lidmaatschapsquotum van de organisatie is overschreden.',\n    organization_minimum_permissions_needed: 'Minimale machtigingen vereist voor de organisatie.',\n    passkey_already_exists: 'Deze passkey bestaat al.',\n    passkey_not_supported: 'Passkeys worden niet ondersteund door deze browser.',\n    passkey_pa_not_supported: 'Passkeys worden niet ondersteund door deze browser.',\n    passkey_registration_cancelled: 'Passkey registratie is geannuleerd.',\n    passkey_retrieval_cancelled: 'Passkey ophalen is geannuleerd.',\n    passwordComplexity: {\n      maximumLength: 'Wachtwoord moet minder dan 256 tekens bevatten.',\n      minimumLength: 'Wachtwoord moet minstens 8 tekens bevatten.',\n      requireLowercase: 'Wachtwoord moet minstens 1 kleine letter bevatten.',\n      requireNumbers: 'Wachtwoord moet minstens 1 cijfer bevatten.',\n      requireSpecialCharacter: 'Wachtwoord moet minstens 1 speciaal teken bevatten.',\n      requireUppercase: 'Wachtwoord moet minstens 1 hoofdletter bevatten.',\n      sentencePrefix: 'Wachtwoord moet minstens 1 speciaal teken bevatten.',\n    },\n    phone_number_exists: 'Dit telefoonnummer is al in gebruik. Probeer een ander nummer.',\n    session_exists: 'Je bent al ingelogd.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Je wachtwoord werkt, maar kan sterker zijn. Probeer meer tekens toe te voegen.',\n      goodPassword: 'Je wachtwoord voldoet aan alle vereisten.',\n      notEnough: 'Je wachtwoord is niet sterk genoeg.',\n      suggestions: {\n        allUppercase: 'Zet een deel in hoofdletters, maar niet alle letters.',\n        anotherWord: 'Voeg meer woorden toe die minder vaak voorkomen.',\n        associatedYears: 'Vermijd jaartallen die met jou geassocieerd zijn.',\n        capitalization: 'Zet meer dan de eerste letter in hoofdletter.',\n        dates: 'Vermijd data en jaartallen die met jou geassocieerd zijn.',\n        l33t: \"Vermijd voorspelbare vervangingen, zoals '@' voor 'a'.\",\n        longerKeyboardPattern: 'Gebruik langere toetsenbord patronen, en wissel meerdere keren van richting.',\n        noNeed: 'Je kan ook een sterk wachtwoord maken zonder speciale tekens, hoofdletters of nummers.',\n        pwned: 'Als u dit wachtwoord elders gebruikt, moet u het veranderen.',\n        recentYears: 'Vermijd recente jaartallen.',\n        repeated: 'Vermijd herhalende woorden en letters.',\n        reverseWords: 'Vermijd het omdraaien van veelvoorkomende woorden.',\n        sequences: 'Vermijd veelvoorkomende tekstreeksen.',\n        useWords: 'Gebruik meerdere woorden, maar vermijd veelvoorkomende zinnen.',\n      },\n      warnings: {\n        common: 'Dit wachtwoord wordt veel gebruikt.',\n        commonNames: 'Veelvoorkomende voor- en achternamen zijn makkelijk te raden.',\n        dates: 'Datums zijn makkelijk te raden.',\n        extendedRepeat: 'Herhalende patronen zoals \"abcabcabc\" zijn makkelijk te raden.',\n        keyPattern: 'Korte toetsenbord patronen zijn makkelijk te raden.',\n        namesByThemselves: 'Voor- en achternamen op zich zijn makkelijk te raden.',\n        pwned: 'Dit wachtwoord is in een datalek gevonden.',\n        recentYears: 'Recente jaartallen zijn makkelijk te raden.',\n        sequences: 'Veelvoorkomende tekstreeksen zoals \"abc\" zijn makkelijk te raden.',\n        similarToCommon: 'Dit lijkt op een veelvoorkomend wachtwoord.',\n        simpleRepeat: 'Herhalende letters zoals \"aaa\" zijn makkelijk te raden.',\n        straightRow: 'Opeenvolgende toetsen op jouw toetsenbord zijn makkelijk te raden.',\n        topHundred: 'Dit wachtwoord wordt erg veel gebruikt.',\n        topTen: 'Dit wachtwoord wordt heel erg veel gebruikt.',\n        userInputs: 'Vermijd persoonlijke of website gerelateerde woorden.',\n        wordByItself: 'Woorden op zich zijn makkelijk te raden.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Account toevoegen',\n    action__manageAccount: 'Account beheren',\n    action__signOut: 'Uitloggen',\n    action__signOutAll: 'Uitloggen uit alle accounts',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Gekopieerd!',\n      actionLabel__copy: 'Kopieer',\n      actionLabel__download: 'Download .txt',\n      actionLabel__print: 'Print',\n      infoText1: 'Backupcodes zullen voor dit account ingeschakeld zijn.',\n      infoText2:\n        'Houd de backupcodes geheim en bewaar ze veilig. U kunt backupcodes opnieuw genereren als u vermoedt dat ze zijn aangetast.',\n      subtitle__codelist: 'Sla ze veilig op en hou ze geheim.',\n      successMessage:\n        'Backupcodes zijn nu ingeschakeld. U kunt er een van gebruiken om in te loggen op uw account als u geen toegang meer heeft tot uw authenticatieapparaat. Elke code kan maar één keer gebruikt worden.',\n      successSubtitle:\n        'Je kunt één van deze gebruiken om in te loggen op je account als je geen toegang meer hebt tot je authenticatieapparaat.',\n      title: 'Voeg backup code verificatie toe',\n      title__codelist: 'Backup codes',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Kies een provider om je account te verbinden.',\n      formHint__noAccounts: 'Er zijn geen beschikbare externe accountproviders.',\n      removeResource: {\n        messageLine1: '{{identifier}} zal verwijderd worden uit dit account.',\n        messageLine2:\n          'Je kunt deze verbonden account niet meer gebruiken en afhankelijke functies zullen niet meer werken.',\n        successMessage: '{{connectedAccount}} is verwijderd uit je account.',\n        title: 'Verwijder externe account',\n      },\n      socialButtonsBlockButton: 'Verbind {{provider|titleize}} account',\n      successMessage: 'Deze provider is toegevoegd aan je account.',\n      title: 'Verbind externe account',\n    },\n    deletePage: {\n      actionDescription: 'Type \"Delete account\" below to continue.',\n      confirm: 'Delete account',\n      messageLine1: 'Are you sure you want to delete your account?',\n      messageLine2: 'This action is permanent and irreversible.',\n      title: 'Delete account',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Een mail met daarin een verificatiecode is verstuurd naar dit adres.',\n        formSubtitle: 'Voer de verificatiecode in die verstuurd is naar {{identifier}}',\n        formTitle: 'Verificatiecode',\n        resendButton: 'Verstuur code opnieuw',\n        successMessage: 'Het e-mailadres {{identifier}} is toegevoegd aan je account.',\n      },\n      emailLink: {\n        formHint: 'Een mail met daarin een verificatielink is verstuurd naar dit adres.',\n        formSubtitle: 'Klik op de verificatielink die verstuurd is naar {{identifier}}',\n        formTitle: 'Verificatielink',\n        resendButton: 'Verstuur link opnieuw',\n        successMessage: 'Het e-mailadres {{identifier}} is toegevoegd aan je account.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} zal verwijderd worden uit dit account.',\n        messageLine2: 'Je zal niet meer kunnen inloggen met dit e-mailadres.',\n        successMessage: '{{emailAddress}} is verwijderd uit je account.',\n        title: 'Verwijder e-mailadres',\n      },\n      title: 'E-mailadres toevoegen',\n      verifyTitle: 'E-mailadres bevestigen',\n    },\n    formButtonPrimary__add: 'Toevoegen',\n    formButtonPrimary__continue: 'Doorgaan',\n    formButtonPrimary__finish: 'Afronden',\n    formButtonPrimary__remove: 'Verwijderen',\n    formButtonPrimary__save: 'Opslaan',\n    formButtonReset: 'Annuleren',\n    mfaPage: {\n      formHint: 'Kies een methode om toe te voegen.',\n      title: 'Tweestapsverificatie toevoegen',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Gebruik bestaand nummer',\n      primaryButton__addPhoneNumber: 'Telefoonnummer toevoegen',\n      removeResource: {\n        messageLine1: '{{identifier}} zal niet langer verificatiecodes ontvangen bij het inloggen.',\n        messageLine2: 'Uw account is mogelijk niet zo veilig. Weet je zeker dat je door wilt gaan?',\n        successMessage: 'SMS-code tweestapsverificatie is verwijderd voor {{mfaPhoneCode}}',\n        title: 'Verwijder tweestapsverificatie',\n      },\n      subtitle__availablePhoneNumbers:\n        'Selecteer een telefoonnummer om je te registreren voor SMS-code twee-stapsverificatie.',\n      subtitle__unavailablePhoneNumbers:\n        'Er zijn geen beschikbare telefoonnummers om te registreren voor SMS-code tweestapsverificatie.',\n      successMessage1:\n        'When signing in, you will need to enter a verification code sent to this phone number as an additional step.',\n      successMessage2:\n        'Sla deze backup codes op en bewaar ze ergens veilig. Als je toegang kwijtraakt tot je authenticatieapparaat, kun je de backup codes gebruiken om in te loggen.',\n      successTitle: 'SMS-code verificatie ingeschakeld',\n      title: 'Voeg SMS-code verificatie toe',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Een tweede optie, scan de QR-code',\n        buttonUnableToScan__nonPrimary: 'Kan je de code niet scannen?',\n        infoText__ableToScan: 'Scan de QR-code met je authenticator app om de authenticator toe te voegen.',\n        infoText__unableToScan:\n          'Stel een nieuwe aanmeldmethode in op je authenticator en voer de onderstaande sleutel in.',\n        inputLabel__unableToScan1:\n          'Zorg ervoor dat tijdsgebaseerde of eenmalige wachtwoorden zijn ingeschakeld, en voltooi vervolgens het koppelen van uw account.',\n        inputLabel__unableToScan2: \"Als je authenticator TOTP-URI's ondersteunt, kun je ook de volledige URI kopiëren.\",\n      },\n      removeResource: {\n        messageLine1: 'Verificatiecodes van deze authenticator zullen niet langer vereist zijn bij het inloggen.',\n        messageLine2: 'Uw account is mogelijk niet zo veilig. Weet je zeker dat je door wilt gaan?',\n        successMessage: 'Tweestapsverificatie via authenticator-applicatie is verwijderd.',\n        title: 'Verwijder tweestapsverificatie',\n      },\n      successMessage:\n        'Tweestapsverificatie is nu ingesteld. Bij het inloggen zal je een verificatiecode van je authenticator app moeten invoeren.',\n      title: 'Voeg authenticator toe',\n      verifySubtitle: 'Voer de verificatiecode in die je authenticator app heeft gegenereerd.',\n      verifyTitle: 'Verificatiecode',\n    },\n    mobileButton__menu: 'Menu',\n    navbar: {\n      account: 'Profiel',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Beheer je account informatie.',\n      security: 'Beveiliging',\n      title: 'Account',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} zal verwijderd worden uit dit account.',\n        title: 'Verwijder passkey',\n      },\n      subtitle__rename: 'Je kunt de naam van de passkey wijzigen om deze gemakkelijker te vinden.',\n      title__rename: 'Passkey hernoemen',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Het is aanbevolen om uit te loggen van alle andere apparaten die mogelijk gebruik hebben gemaakt van je oude wachtwoord.',\n      readonly:\n        'Je wachtwoord kan momenteel niet worden gewijzigd omdat je alleen via de enterprise connectie kunt inloggen.',\n      successMessage__set: 'Je wachtwoord is ingesteld.',\n      successMessage__signOutOfOtherSessions: 'Alle andere apparaten zijn uitgelogd.',\n      successMessage__update: 'Je wachtwoord is bijgewerkt.',\n      title__set: 'Stel wachtwoord in',\n      title__update: 'Wachtwoord wijzigen',\n    },\n    phoneNumberPage: {\n      infoText: 'Een SMS met daarin een verificatiecode is verstuurd naar dit nummer.',\n      removeResource: {\n        messageLine1: '{{identifier}} zal van dit account verwijderd worden.',\n        messageLine2: 'Je zal niet meer kunnen inloggen met dit telefoonnummer.',\n        successMessage: '{{phoneNumber}} is verwijderd uit je account.',\n        title: 'Verwijder telefoonnummer',\n      },\n      successMessage: '{{phoneNumber}} is toegevoegd aan je account.',\n      title: 'Telefoonnummer toevoegen',\n      verifySubtitle: 'Voer de verificatiecode in die verstuurd is naar {{phoneNumber}}',\n      verifyTitle: 'Verifieer telefoonnummer',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Upload een JPG, PNG, GIF, of WEBP afbeelding kleiner dan 10 MB',\n      imageFormDestructiveActionSubtitle: 'Verwijder afbeelding',\n      imageFormSubtitle: 'Afbeelding uploaden',\n      imageFormTitle: 'Profielfoto',\n      readonly: 'Je profiel informatie is verstrekt door de enterprise connectie en kan niet worden bewerkt.',\n      successMessage: 'Je profiel is bijgewerkt.',\n      title: 'Profiel bijwerken',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Log uit op apparaat',\n        title: 'Actieve apparaten',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Probeer opnieuw',\n        actionLabel__reauthorize: 'Authoriseer nu',\n        destructiveActionTitle: 'Verwijderen',\n        primaryButton: 'Verbind een account',\n        subtitle__disconnected: 'Je account is losgekoppeld. Verbind het opnieuw om verder te gaan.',\n        subtitle__reauthorize:\n          'De vereiste scopes zijn bijgewerkt, en je kunt mogelijk beperkte functionaliteit ervaren. Autoriseer deze toepassing opnieuw om problemen te voorkomen.',\n        title: 'Aangesloten accounts',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Verwijder account',\n        title: 'Account beëindigen',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Verwijder e-mailadres',\n        detailsAction__nonPrimary: 'Stel in als hoofd',\n        detailsAction__primary: 'Rond verificatie af',\n        detailsAction__unverified: 'Rond verificatie af',\n        primaryButton: 'Voeg een e-mailadres toe',\n        title: 'E-mailadressen',\n      },\n      enterpriseAccountsSection: {\n        title: 'Bedrijfsaccounts',\n      },\n      headerTitle__account: 'Account',\n      headerTitle__security: 'Beveiliging',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Codes hergenereren',\n          headerTitle: 'Backupcodes',\n          subtitle__regenerate: 'Genereer een nieuwe set backupcodes. De vorige kunnen niet meer gebruikt worden.',\n          title__regenerate: 'Backupcodes hergenereren',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Stel in als standaard',\n          destructiveActionLabel: 'Verwijder tweestapsverificatie',\n        },\n        primaryButton: 'Tweestapsverificatie instellen',\n        title: 'Tweestapsverificatie',\n        totp: {\n          destructiveActionTitle: 'Verwijderen',\n          headerTitle: 'Authenticatorapplicatie',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Verwijderen',\n        menuAction__rename: 'Hernoemen',\n        primaryButton: undefined,\n        title: 'Passkeys',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Wachtwoord instellen',\n        primaryButton__updatePassword: 'Wachtwoord wijzigen',\n        title: 'Wachtwoord',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Verwijder telefoonnummer',\n        detailsAction__nonPrimary: 'Stel in als hoofd',\n        detailsAction__primary: 'Rond verificatie af',\n        detailsAction__unverified: 'Rond verificatie af',\n        primaryButton: 'Voeg een telefoonnummer toe',\n        title: 'Telefoonnummers',\n      },\n      profileSection: {\n        primaryButton: 'Profiel bijwerken',\n        title: 'Profiel',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Stel gebruikersnaam in',\n        primaryButton__updateUsername: 'Wijzig gebruikersnaam',\n        title: 'Gebruikersnaam',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Verwijder portefeuille',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 portefeuilles',\n        title: 'Web3 portefeuilles',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Je gebruikersnaam is bijgewerkt.',\n      title__set: 'Gebruikersnaam bijwerken',\n      title__update: 'Gebruikersnaam bijwerken',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} zal verwijderd worden uit dit account.',\n        messageLine2: 'Je zal niet meer kunnen inloggen met deze web3 portefeuille.',\n        successMessage: '{{web3Wallet}} is verwijderd uit je account.',\n        title: 'Verwijder web3 portefeuille',\n      },\n      subtitle__availableWallets: 'Selecteer een web3 portefeuille om toe te voegen.',\n      subtitle__unavailableWallets: 'Er zijn geen beschikbare web3 portefeuilles.',\n      successMessage: 'De portefeuille is toegevoegd aan dit account.',\n      title: 'Web3 portefeuille toevoegen.',\n      web3WalletButtonsBlockButton: 'Voeg een Web3-portefeuille toe',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: 'Neem deel aan de wachtlijst',\n      actionText: 'Nog geen account?',\n      formButton: 'Verstuur',\n      subtitle: 'Je wordt toegevoegd aan de wachtlijst en op de hoogte gehouden.',\n      title: 'Wachtlijst aanmelding',\n    },\n    success: {\n      message: 'Je bent succesvol toegevoegd aan de wachtlijst!',\n      subtitle: 'Je ontvangt een bericht zodra er ruimte beschikbaar is.',\n      title: 'Succes!',\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cACE;AAAA,MACF,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCACE;AAAA,QACF,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBAAsB;AAAA,QACtB,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BAA2B;AAAA,MAC7B;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBAAsB;AAAA,UACtB,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}