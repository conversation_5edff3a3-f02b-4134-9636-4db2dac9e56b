{"version": 3, "sources": ["../src/hr-HR.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const hrHR: LocalizationResource = {\n  locale: 'hr-HR',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Natrag',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Zadano',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Drugi uređaj za oponašanje',\n  badge__primary: 'Primarno',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Zahtijeva akciju',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Ovaj uređaj',\n  badge__unverified: 'Nepotvrđeno',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Korisnički uređaj',\n  badge__you: 'Vi',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'kreiraj organizaciju',\n    invitePage: {\n      formButtonReset: 'Preskoči',\n    },\n    title: 'Kreiraj organizaciju',\n  },\n  dates: {\n    lastDay: \"Jučer u {{ date | timeString('hr-HR') }}\",\n    next6Days: \"{{ date | weekday('hr-HR','long') }} u {{ date | timeString('hr-HR') }}\",\n    nextDay: \"Sutra u {{ date | timeString('hr-HR') }}\",\n    numeric: \"{{ date | numeric('hr-HR') }}\",\n    previous6Days: \"Prošli {{ date | weekday('hr-HR','long') }} u {{ date | timeString('hr-HR') }}\",\n    sameDay: \"Danas u {{ date | timeString('hr-HR') }}\",\n  },\n  dividerText: 'ili',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Koristite drugu metodu',\n  footerPageLink__help: 'Pomoć',\n  footerPageLink__privacy: 'Privatnost',\n  footerPageLink__terms: 'Uvjeti',\n  formButtonPrimary: 'Nastavi',\n  formButtonPrimary__verify: 'Potvrdi',\n  formFieldAction__forgotPassword: 'Zaboravili ste lozinku?',\n  formFieldError__matchingPasswords: 'Lozinke se podudaraju.',\n  formFieldError__notMatchingPasswords: 'Lozinke se ne podudaraju.',\n  formFieldError__verificationLinkExpired: 'Verifikacijska poveznica je istekla. Molimo zatražite novu poveznicu.',\n  formFieldHintText__optional: 'Neobavezno',\n  formFieldHintText__slug: 'Slug je čitljiv ID koji mora biti jedinstven. Često se koristi u URL-ovima.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Izbriši račun',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: 'moja-organizacija',\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Omogući automatske pozivnice za ovu domenu',\n  formFieldLabel__backupCode: 'Rezervni kod',\n  formFieldLabel__confirmDeletion: 'Potvrda',\n  formFieldLabel__confirmPassword: 'Potvrdi lozinku',\n  formFieldLabel__currentPassword: 'Trenutna lozinka',\n  formFieldLabel__emailAddress: 'E-mail adresa',\n  formFieldLabel__emailAddress_username: 'E-mail adresa ili korisničko ime',\n  formFieldLabel__emailAddresses: 'E-mail adrese',\n  formFieldLabel__firstName: 'Ime',\n  formFieldLabel__lastName: 'Prezime',\n  formFieldLabel__newPassword: 'Nova lozinka',\n  formFieldLabel__organizationDomain: 'Domena',\n  formFieldLabel__organizationDomainDeletePending: 'Izbriši prijedloge i pozivnice na čekanju',\n  formFieldLabel__organizationDomainEmailAddress: 'E-mail adresa za verifikaciju',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Unesite e-mail adresu pod ovom domenom kako biste primili kod i potvrdili ovu domenu.',\n  formFieldLabel__organizationName: 'Naziv',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Naziv pristupnog ključa',\n  formFieldLabel__password: 'Lozinka',\n  formFieldLabel__phoneNumber: 'Telefonski broj',\n  formFieldLabel__role: 'Uloga',\n  formFieldLabel__signOutOfOtherSessions: 'Odjavi se sa svih ostalih uređaja',\n  formFieldLabel__username: 'Korisničko ime',\n  impersonationFab: {\n    action__signOut: 'Odjava',\n    title: 'Prijavljeni ste kao {{identifier}}',\n  },\n  maintenanceMode: 'Trenutno provodimo održavanje, ali ne brinite, ne bi trebalo trajati duže od nekoliko minuta.',\n  membershipRole__admin: 'Administrator',\n  membershipRole__basicMember: 'Član',\n  membershipRole__guestMember: 'Gost',\n  organizationList: {\n    action__createOrganization: 'Kreiraj organizaciju',\n    action__invitationAccept: 'Pridruži se',\n    action__suggestionsAccept: 'Zatraži pridruživanje',\n    createOrganization: 'Kreiraj organizaciju',\n    invitationAcceptedLabel: 'Pridružen',\n    subtitle: 'za nastavak na {{applicationName}}',\n    suggestionsAcceptedLabel: 'Čeka odobrenje',\n    title: 'Odaberite račun',\n    titleWithoutPersonal: 'Odaberite organizaciju',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Automatske pozivnice',\n    badge__automaticSuggestion: 'Automatski prijedlozi',\n    badge__manualInvitation: 'Bez automatskog učlanjenja',\n    badge__unverified: 'Nepotvrđeno',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Dodajte domenu za provjeru. Korisnici s e-mail adresama na ovoj domeni mogu se automatski pridružiti organizaciji ili zatražiti pridruživanje.',\n      title: 'Dodaj domenu',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Pozivnice se nisu mogle poslati. Već postoje pozivnice na čekanju za sljedeće e-mail adrese: {{email_addresses}}.',\n      formButtonPrimary__continue: 'Pošalji pozivnice',\n      selectDropdown__role: 'Odaberi ulogu',\n      subtitle: 'Unesite ili zalijepite jednu ili više e-mail adresa, odvojenih razmakom ili zarezom.',\n      successMessage: 'Pozivnice uspješno poslane',\n      title: 'Pozovi nove članove',\n    },\n    membersPage: {\n      action__invite: 'Pozovi',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Ukloni člana',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'Pridružen',\n        tableHeader__role: 'Uloga',\n        tableHeader__user: 'Korisnik',\n      },\n      detailsTitle__emptyRow: 'Nema članova za prikaz',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Pozovite korisnike povezivanjem e-mail domene s vašom organizacijom. Svatko tko se prijavi s odgovarajućom e-mail domenom moći će se pridružiti organizaciji u bilo kojem trenutku.',\n          headerTitle: 'Automatske pozivnice',\n          primaryButton: 'Upravljaj potvrđenim domenama',\n        },\n        table__emptyRow: 'Nema pozivnica za prikaz',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Opozovi pozivnicu',\n        tableHeader__invited: 'Pozvan',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Korisnici koji se prijave s odgovarajućom e-mail domenom moći će vidjeti prijedlog za zahtjev za pridruživanje vašoj organizaciji.',\n          headerTitle: 'Automatski prijedlozi',\n          primaryButton: 'Upravljaj potvrđenim domenama',\n        },\n        menuAction__approve: 'Odobri',\n        menuAction__reject: 'Odbij',\n        tableHeader__requested: 'Zatražen pristup',\n        table__emptyRow: 'Nema zahtjeva za prikaz',\n      },\n      start: {\n        headerTitle__invitations: 'Pozivnice',\n        headerTitle__members: 'Članovi',\n        headerTitle__requests: 'Zahtjevi',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Upravljajte svojom organizacijom.',\n      general: 'Općenito',\n      members: 'Članovi',\n      title: 'Organizacija',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Upišite \"{{organizationName}}\" ispod za nastavak.',\n          messageLine1: 'Jeste li sigurni da želite izbrisati ovu organizaciju?',\n          messageLine2: 'Ova radnja je trajna i nepovratna.',\n          successMessage: 'Izbrisali ste organizaciju.',\n          title: 'Izbriši organizaciju',\n        },\n        leaveOrganization: {\n          actionDescription: 'Upišite \"{{organizationName}}\" ispod za nastavak.',\n          messageLine1:\n            'Jeste li sigurni da želite napustiti ovu organizaciju? Izgubit ćete pristup ovoj organizaciji i njezinim aplikacijama.',\n          messageLine2: 'Ova radnja je trajna i nepovratna.',\n          successMessage: 'Napustili ste organizaciju.',\n          title: 'Napusti organizaciju',\n        },\n        title: 'Opasnost',\n      },\n      domainSection: {\n        menuAction__manage: 'Upravljaj',\n        menuAction__remove: 'Izbriši',\n        menuAction__verify: 'Potvrdi',\n        primaryButton: 'Dodaj domenu',\n        subtitle:\n          'Dopustite korisnicima da se automatski pridruže organizaciji ili zatraže pridruživanje na temelju potvrđene e-mail domene.',\n        title: 'Potvrđene domene',\n      },\n      successMessage: 'Organizacija je ažurirana.',\n      title: 'Ažuriraj profil',\n    },\n    removeDomainPage: {\n      messageLine1: 'E-mail domena {{domain}} bit će uklonjena.',\n      messageLine2: 'Korisnici se nakon ovoga neće moći automatski pridružiti organizaciji.',\n      successMessage: '{{domain}} je uklonjena.',\n      title: 'Ukloni domenu',\n    },\n    start: {\n      headerTitle__general: 'Općenito',\n      headerTitle__members: 'Članovi',\n      profileSection: {\n        primaryButton: 'Ažuriraj profil',\n        title: 'Profil organizacije',\n        uploadAction__title: 'Logotip',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Uklanjanje ove domene utjecat će na pozvane korisnike.',\n        removeDomainActionLabel__remove: 'Ukloni domenu',\n        removeDomainSubtitle: 'Ukloni ovu domenu iz vaših potvrđenih domena',\n        removeDomainTitle: 'Ukloni domenu',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Korisnici su automatski pozvani da se pridruže organizaciji kada se prijave i mogu se pridružiti u bilo kojem trenutku.',\n        automaticInvitationOption__label: 'Automatske pozivnice',\n        automaticSuggestionOption__description:\n          'Korisnici primaju prijedlog za zahtjev za pridruživanje, ali moraju biti odobreni od strane administratora prije nego što se mogu pridružiti organizaciji.',\n        automaticSuggestionOption__label: 'Automatski prijedlozi',\n        calloutInfoLabel: 'Promjena načina upisa utjecat će samo na nove korisnike.',\n        calloutInvitationCountLabel: 'Pozivnice na čekanju poslane korisnicima: {{count}}',\n        calloutSuggestionCountLabel: 'Prijedlozi na čekanju poslani korisnicima: {{count}}',\n        manualInvitationOption__description: 'Korisnici mogu biti pozvani u organizaciju samo ručno.',\n        manualInvitationOption__label: 'Bez automatskog upisa',\n        subtitle: 'Odaberite kako se korisnici s ove domene mogu pridružiti organizaciji.',\n      },\n      start: {\n        headerTitle__danger: 'Opasnost',\n        headerTitle__enrollment: 'Opcije upisa',\n      },\n      subtitle: 'Domena {{domain}} je sada potvrđena. Nastavite odabirom načina upisa.',\n      title: 'Ažuriraj {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Unesite verifikacijski kod poslan na vašu e-mail adresu',\n      formTitle: 'Verifikacijski kod',\n      resendButton: 'Niste primili kod? Pošalji ponovno',\n      subtitle: 'Domena {{domainName}} mora biti potvrđena putem e-maila.',\n      subtitleVerificationCodeScreen: 'Verifikacijski kod poslan je na {{emailAddress}}. Unesite kod za nastavak.',\n      title: 'Potvrdi domenu',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Kreiraj organizaciju',\n    action__invitationAccept: 'Pridruži se',\n    action__manageOrganization: 'Upravljaj',\n    action__suggestionsAccept: 'Zatraži pridruživanje',\n    notSelected: 'Nije odabrana organizacija',\n    personalWorkspace: 'Osobni račun',\n    suggestionsAcceptedLabel: 'Čeka odobrenje',\n  },\n  paginationButton__next: 'Sljedeće',\n  paginationButton__previous: 'Prethodno',\n  paginationRowText__displaying: 'Prikazuje se',\n  paginationRowText__of: 'od',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Zatražite pomoć',\n      actionText: 'Nemate ništa od ovoga?',\n      blockButton__backupCode: 'Koristite rezervni kod',\n      blockButton__emailCode: 'Pošalji kod e-poštom na {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Nastavite s vašom lozinkom',\n      blockButton__phoneCode: 'Pošalji SMS kod na {{identifier}}',\n      blockButton__totp: 'Koristite vašu aplikaciju za autentifikaciju',\n      getHelp: {\n        blockButton__emailSupport: 'Pošalji e-poštu podršci',\n        content:\n          'Ako imate problema s verifikacijom vašeg računa, pošaljite nam e-poštu i radit ćemo s vama na vraćanju pristupa što je prije moguće.',\n        title: 'Zatražite pomoć',\n      },\n      subtitle: 'Imate problema? Možete koristiti bilo koju od ovih metoda za verifikaciju.',\n      title: 'Koristite drugu metodu',\n    },\n    backupCodeMfa: {\n      subtitle: 'Vaš rezervni kod je onaj koji ste dobili prilikom postavljanja dvostupanjske autentifikacije.',\n      title: 'Unesite rezervni kod',\n    },\n    emailCode: {\n      formTitle: 'Verifikacijski kod',\n      resendButton: 'Niste primili kod? Pošalji ponovno',\n      subtitle: 'za nastavak na {{applicationName}}',\n      title: 'Provjerite svoju e-poštu',\n    },\n    noAvailableMethods: {\n      message: 'Ne može se nastaviti s verifikacijom. Nema dostupnog faktora autentifikacije.',\n      subtitle: 'Došlo je do pogreške',\n      title: 'Ne može se verificirati vaš račun',\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Koristite drugu metodu',\n      subtitle: 'Unesite lozinku povezanu s vašim računom',\n      title: 'Unesite vašu lozinku',\n    },\n    phoneCode: {\n      formTitle: 'Verifikacijski kod',\n      resendButton: 'Niste primili kod? Pošalji ponovno',\n      subtitle: 'za nastavak na {{applicationName}}',\n      title: 'Provjerite svoj telefon',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Verifikacijski kod',\n      resendButton: 'Niste primili kod? Pošalji ponovno',\n      subtitle: 'Za nastavak, molimo unesite verifikacijski kod poslan na vaš telefon',\n      title: 'Provjerite svoj telefon',\n    },\n    totpMfa: {\n      formTitle: 'Verifikacijski kod',\n      subtitle: 'Za nastavak, molimo unesite verifikacijski kod generiran vašom aplikacijom za autentifikaciju',\n      title: 'Dvostupanjska verifikacija',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Dodaj račun',\n      action__signOutAll: 'Odjavi se sa svih računa',\n      subtitle: 'Odaberite račun s kojim želite nastaviti.',\n      title: 'Odaberite račun',\n    },\n    alternativeMethods: {\n      actionLink: 'Zatražite pomoć',\n      actionText: 'Nemate ništa od ovoga?',\n      blockButton__backupCode: 'Koristite rezervni kod',\n      blockButton__emailCode: 'Pošalji kod e-poštom na {{identifier}}',\n      blockButton__emailLink: 'Pošalji poveznicu e-poštom na {{identifier}}',\n      blockButton__passkey: 'Prijavite se svojim pristupnim ključem',\n      blockButton__password: 'Prijavite se svojom lozinkom',\n      blockButton__phoneCode: 'Pošalji SMS kod na {{identifier}}',\n      blockButton__totp: 'Koristite svoju aplikaciju za autentifikaciju',\n      getHelp: {\n        blockButton__emailSupport: 'Pošaljite e-poštu podršci',\n        content:\n          'Ako imate problema s prijavom na svoj račun, pošaljite nam e-poštu i radit ćemo s vama na vraćanju pristupa što je prije moguće.',\n        title: 'Zatražite pomoć',\n      },\n      subtitle: 'Imate problema? Možete koristiti bilo koju od ovih metoda za prijavu.',\n      title: 'Koristite drugu metodu',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'Vaš rezervni kod je onaj koji ste dobili prilikom postavljanja dvostupanjske autentifikacije.',\n      title: 'Unesite rezervni kod',\n    },\n    emailCode: {\n      formTitle: 'Verifikacijski kod',\n      resendButton: 'Niste primili kod? Pošalji ponovno',\n      subtitle: 'za nastavak na {{applicationName}}',\n      title: 'Provjerite svoju e-poštu',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Za nastavak, otvorite verifikacijsku poveznicu na uređaju i pregledniku s kojeg ste započeli prijavu',\n        title: 'Verifikacijska poveznica nije valjana za ovaj uređaj',\n      },\n      expired: {\n        subtitle: 'Vratite se na izvornu karticu za nastavak.',\n        title: 'Ova verifikacijska poveznica je istekla',\n      },\n      failed: {\n        subtitle: 'Vratite se na izvornu karticu za nastavak.',\n        title: 'Ova verifikacijska poveznica nije valjana',\n      },\n      formSubtitle: 'Koristite verifikacijsku poveznicu poslanu na vašu e-poštu',\n      formTitle: 'Verifikacijska poveznica',\n      loading: {\n        subtitle: 'Uskoro ćete biti preusmjereni',\n        title: 'Prijava...',\n      },\n      resendButton: 'Niste primili poveznicu? Pošalji ponovno',\n      subtitle: 'za nastavak na {{applicationName}}',\n      title: 'Provjerite svoju e-poštu',\n      unusedTab: {\n        title: 'Možete zatvoriti ovu karticu',\n      },\n      verified: {\n        subtitle: 'Uskoro ćete biti preusmjereni',\n        title: 'Uspješno prijavljeni',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Vratite se na izvornu karticu za nastavak',\n        subtitleNewTab: 'Vratite se na novootvorenu karticu za nastavak',\n        titleNewTab: 'Prijavljeni na drugoj kartici',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Kod za resetiranje lozinke',\n      resendButton: 'Niste primili kod? Pošalji ponovno',\n      subtitle: 'za resetiranje vaše lozinke',\n      subtitle_email: 'Prvo unesite kod poslan na vašu e-mail adresu',\n      subtitle_phone: 'Prvo unesite kod poslan na vaš telefon',\n      title: 'Resetiraj lozinku',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Resetirajte svoju lozinku',\n      label__alternativeMethods: 'Ili se prijavite na drugi način',\n      title: 'Zaboravili ste lozinku?',\n    },\n    noAvailableMethods: {\n      message: 'Nije moguće nastaviti s prijavom. Nema dostupnog faktora autentifikacije.',\n      subtitle: 'Došlo je do pogreške',\n      title: 'Nije moguće prijaviti se',\n    },\n    passkey: {\n      subtitle:\n        'Korištenje vašeg pristupnog ključa potvrđuje da ste to vi. Vaš uređaj može tražiti otisak prsta, prepoznavanje lica ili zaključavanje zaslona.',\n      title: 'Koristite svoj pristupni ključ',\n    },\n    password: {\n      actionLink: 'Koristite drugu metodu',\n      subtitle: 'Unesite lozinku povezanu s vašim računom',\n      title: 'Unesite svoju lozinku',\n    },\n    passwordPwned: {\n      title: 'Lozinka je kompromitirana',\n    },\n    phoneCode: {\n      formTitle: 'Verifikacijski kod',\n      resendButton: 'Niste primili kod? Pošalji ponovno',\n      subtitle: 'za nastavak na {{applicationName}}',\n      title: 'Provjerite svoj telefon',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Verifikacijski kod',\n      resendButton: 'Niste primili kod? Pošalji ponovno',\n      subtitle: 'Za nastavak, unesite verifikacijski kod poslan na vaš telefon',\n      title: 'Provjerite svoj telefon',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Resetiraj lozinku',\n      requiredMessage: 'Iz sigurnosnih razloga, potrebno je resetirati vašu lozinku.',\n      successMessage: 'Vaša lozinka je uspješno promijenjena. Prijavljujemo vas, molimo pričekajte trenutak.',\n      title: 'Postavite novu lozinku',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Moramo potvrditi vaš identitet prije resetiranja lozinke.',\n    },\n    start: {\n      actionLink: 'Registrirajte se',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Koristite e-poštu',\n      actionLink__use_email_username: 'Koristite e-poštu ili korisničko ime',\n      actionLink__use_passkey: 'Koristite pristupni ključ umjesto toga',\n      actionLink__use_phone: 'Koristite telefon',\n      actionLink__use_username: 'Koristite korisničko ime',\n      actionText: 'Nemate račun?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Dobrodošli natrag! Molimo prijavite se za nastavak',\n      subtitleCombined: undefined,\n      title: 'Prijavite se na {{applicationName}}',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Verifikacijski kod',\n      subtitle: 'Za nastavak, unesite verifikacijski kod generiran vašom aplikacijom za autentifikaciju',\n      title: 'Dvostupanjska verifikacija',\n    },\n  },\n  signInEnterPasswordTitle: 'Unesite svoju lozinku',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Prijavi se',\n      actionText: 'Već imate račun?',\n      subtitle: 'Molimo ispunite preostale detalje za nastavak.',\n      title: 'Ispunite preostala polja',\n    },\n    emailCode: {\n      formSubtitle: 'Unesite verifikacijski kod poslan na vašu e-mail adresu',\n      formTitle: 'Verifikacijski kod',\n      resendButton: 'Niste primili kod? Pošalji ponovno',\n      subtitle: 'Unesite verifikacijski kod poslan na vašu e-poštu',\n      title: 'Potvrdite svoju e-poštu',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Za nastavak, otvorite verifikacijsku poveznicu na uređaju i pregledniku s kojeg ste započeli registraciju',\n        title: 'Verifikacijska poveznica nije valjana za ovaj uređaj',\n      },\n      formSubtitle: 'Koristite verifikacijsku poveznicu poslanu na vašu e-mail adresu',\n      formTitle: 'Verifikacijska poveznica',\n      loading: {\n        title: 'Registracija...',\n      },\n      resendButton: 'Niste primili poveznicu? Pošalji ponovno',\n      subtitle: 'za nastavak na {{applicationName}}',\n      title: 'Potvrdite svoju e-poštu',\n      verified: {\n        title: 'Uspješno registrirani',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Vratite se na novootvorenu karticu za nastavak',\n        subtitleNewTab: 'Vratite se na prethodnu karticu za nastavak',\n        title: 'Uspješno potvrđena e-pošta',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Slažem se s {{ privacyPolicyLink || link(\"Politikom privatnosti\") }}',\n        label__onlyTermsOfService: 'Slažem se s {{ termsOfServiceLink || link(\"Uvjetima korištenja\") }}',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Slažem se s {{ termsOfServiceLink || link(\"Uvjetima korištenja\") }} i {{ privacyPolicyLink || link(\"Politikom privatnosti\") }}',\n      },\n      continue: {\n        subtitle: 'Molimo pročitajte i prihvatite uvjete kako biste nastavili',\n        title: 'Pravni pristanak',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Unesite verifikacijski kod poslan na vaš broj telefona',\n      formTitle: 'Verifikacijski kod',\n      resendButton: 'Niste primili kod? Pošalji ponovno',\n      subtitle: 'Unesite verifikacijski kod poslan na vaš telefon',\n      title: 'Potvrdite svoj telefon',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Prijavi se',\n      actionLink__use_email: 'Koristite e-poštu',\n      actionLink__use_phone: 'Koristite telefon',\n      actionText: 'Već imate račun?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Dobrodošli! Molimo ispunite detalje za početak.',\n      subtitleCombined: 'Dobrodošli! Molimo ispunite detalje za početak.',\n      title: 'Kreirajte svoj račun',\n      titleCombined: 'Kreirajte svoj račun',\n    },\n  },\n  socialButtonsBlockButton: 'Nastavite s {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: '{{email}} je već član organizacije.',\n    captcha_invalid:\n      'Registracija neuspješna zbog neuspjelih sigurnosnih provjera. Molimo osvježite stranicu i pokušajte ponovno ili se obratite podršci za dodatnu pomoć.',\n    captcha_unavailable:\n      'Registracija neuspješna zbog neuspjele provjere bota. Molimo osvježite stranicu i pokušajte ponovno ili se obratite podršci za dodatnu pomoć.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: 'Ova e-mail adresa je zauzeta. Molimo pokušajte s drugom.',\n    form_identifier_exists__phone_number: 'Ovaj telefonski broj je zauzet. Molimo pokušajte s drugim.',\n    form_identifier_exists__username: 'Ovo korisničko ime je zauzeto. Molimo pokušajte s drugim.',\n    form_identifier_not_found: 'Nismo pronašli račun s tim podacima.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'E-mail adresa mora biti valjana e-mail adresa.',\n    form_param_format_invalid__phone_number: 'Telefonski broj mora biti u valjanom međunarodnom formatu',\n    form_param_max_length_exceeded__first_name: 'Ime ne smije biti duže od 256 znakova.',\n    form_param_max_length_exceeded__last_name: 'Prezime ne smije biti duže od 256 znakova.',\n    form_param_max_length_exceeded__name: 'Ime ne smije biti duže od 256 znakova.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: 'Vaša lozinka nije dovoljno jaka.',\n    form_password_pwned:\n      'Ova lozinka je pronađena kao dio curenja podataka i ne može se koristiti, molimo pokušajte s drugom lozinkom.',\n    form_password_pwned__sign_in:\n      'Ova lozinka je pronađena kao dio curenja podataka i ne može se koristiti, molimo resetirajte svoju lozinku.',\n    form_password_size_in_bytes_exceeded:\n      'Vaša lozinka je premašila maksimalni dopušteni broj bajtova, molimo skratite je ili uklonite neke posebne znakove.',\n    form_password_validation_failed: 'Netočna lozinka',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: 'Ne možete izbrisati svoju posljednju identifikaciju.',\n    not_allowed_access:\n      \"E-mail adresa ili broj telefona nije dozvoljen za registraciju. Ovo može biti zbog korištenja '+', '=', '#' ili '.' u vašoj e-mail adresi, korištenja domene povezane s vremenskom e-mail uslugom ili eksplicitnog blokiranja. Ako smatrate da je ovo pogreška, obratite se podršci.\",\n    organization_domain_blocked: 'Ovo je blokirana domena pružatelja e-pošte. Molimo koristite drugu.',\n    organization_domain_common: 'Ovo je uobičajena domena pružatelja e-pošte. Molimo koristite drugu.',\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded:\n      'Dostigli ste ograničenje članstava u organizacijama, uključujući otvorene pozivnice.',\n    organization_minimum_permissions_needed:\n      'Mora postojati barem jedan član organizacije s minimalnim potrebnim dozvolama.',\n    passkey_already_exists: 'Pristupni ključ je već registriran na ovom uređaju.',\n    passkey_not_supported: 'Pristupni ključevi nisu podržani na ovom uređaju.',\n    passkey_pa_not_supported: 'Registracija zahtijeva platformski autentifikator, ali uređaj ga ne podržava.',\n    passkey_registration_cancelled: 'Registracija pristupnog ključa je otkazana ili je isteklo vrijeme.',\n    passkey_retrieval_cancelled: 'Provjera pristupnog ključa je otkazana ili je isteklo vrijeme.',\n    passwordComplexity: {\n      maximumLength: 'manje od {{length}} znakova',\n      minimumLength: '{{length}} ili više znakova',\n      requireLowercase: 'malo slovo',\n      requireNumbers: 'broj',\n      requireSpecialCharacter: 'poseban znak',\n      requireUppercase: 'veliko slovo',\n      sentencePrefix: 'Vaša lozinka mora sadržavati',\n    },\n    phone_number_exists: 'Ovaj telefonski broj je zauzet. Molimo pokušajte s drugim.',\n    session_exists: 'Već ste prijavljeni.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Vaša lozinka funkcionira, ali mogla bi biti jača. Pokušajte dodati više znakova.',\n      goodPassword: 'Vaša lozinka zadovoljava sve potrebne zahtjeve.',\n      notEnough: 'Vaša lozinka nije dovoljno jaka.',\n      suggestions: {\n        allUppercase: 'Napišite velika samo neka, a ne sva slova.',\n        anotherWord: 'Dodajte više riječi koje su manje uobičajene.',\n        associatedYears: 'Izbjegavajte godine koje su povezane s vama.',\n        capitalization: 'Napišite velikim slovima više od prvog slova.',\n        dates: 'Izbjegavajte datume i godine koje su povezane s vama.',\n        l33t: \"Izbjegavajte predvidljive zamjene slova poput '@' za 'a'.\",\n        longerKeyboardPattern: 'Koristite duže obrasce tipkovnice i više puta promijenite smjer tipkanja.',\n        noNeed: 'Možete stvoriti jake lozinke bez korištenja simbola, brojeva ili velikih slova.',\n        pwned: 'Ako ovu lozinku koristite negdje drugdje, trebali biste je promijeniti.',\n        recentYears: 'Izbjegavajte nedavne godine.',\n        repeated: 'Izbjegavajte ponavljanje riječi i znakova.',\n        reverseWords: 'Izbjegavajte obrnuta spelovanja uobičajenih riječi.',\n        sequences: 'Izbjegavajte uobičajene nizove znakova.',\n        useWords: 'Koristite više riječi, ali izbjegavajte uobičajene fraze.',\n      },\n      warnings: {\n        common: 'Ovo je često korištena lozinka.',\n        commonNames: 'Uobičajena imena i prezimena lako je pogoditi.',\n        dates: 'Datume je lako pogoditi.',\n        extendedRepeat: 'Ponavljajuće uzorke znakova poput \"abcabcabc\" lako je pogoditi.',\n        keyPattern: 'Kratke obrasce tipkovnice lako je pogoditi.',\n        namesByThemselves: 'Pojedinačna imena ili prezimena lako je pogoditi.',\n        pwned: 'Vaša lozinka je otkrivena u curenju podataka na internetu.',\n        recentYears: 'Nedavne godine lako je pogoditi.',\n        sequences: 'Uobičajene nizove znakova poput \"abc\" lako je pogoditi.',\n        similarToCommon: 'Ovo je slično često korištenoj lozinci.',\n        simpleRepeat: 'Ponavljajuće znakove poput \"aaa\" lako je pogoditi.',\n        straightRow: 'Ravne redove tipki na vašoj tipkovnici lako je pogoditi.',\n        topHundred: 'Ovo je često korištena lozinka.',\n        topTen: 'Ovo je vrlo često korištena lozinka.',\n        userInputs: 'Ne bi trebalo biti osobnih podataka ili podataka vezanih uz stranicu.',\n        wordByItself: 'Pojedinačne riječi lako je pogoditi.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Dodaj račun',\n    action__manageAccount: 'Upravljaj računom',\n    action__signOut: 'Odjavi se',\n    action__signOutAll: 'Odjavi se sa svih računa',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Kopirano!',\n      actionLabel__copy: 'Kopiraj sve',\n      actionLabel__download: 'Preuzmi .txt',\n      actionLabel__print: 'Ispiši',\n      infoText1: 'Rezervni kodovi bit će omogućeni za ovaj račun.',\n      infoText2:\n        'Čuvajte rezervne kodove u tajnosti i pohranite ih na sigurno. Možete regenerirati rezervne kodove ako sumnjate da su kompromitirani.',\n      subtitle__codelist: 'Pohranite ih na sigurno i čuvajte u tajnosti.',\n      successMessage:\n        'Rezervni kodovi su sada omogućeni. Možete koristiti jedan od njih za prijavu na svoj račun ako izgubite pristup svom uređaju za autentifikaciju. Svaki kod se može koristiti samo jednom.',\n      successSubtitle:\n        'Možete koristiti jedan od ovih za prijavu na svoj račun ako izgubite pristup svom uređaju za autentifikaciju.',\n      title: 'Dodaj verifikaciju rezervnim kodom',\n      title__codelist: 'Rezervni kodovi',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Odaberite pružatelja usluge za povezivanje vašeg računa.',\n      formHint__noAccounts: 'Nema dostupnih vanjskih pružatelja računa.',\n      removeResource: {\n        messageLine1: '{{identifier}} će biti uklonjen s ovog računa.',\n        messageLine2: 'Više nećete moći koristiti ovaj povezani račun i sve ovisne značajke više neće raditi.',\n        successMessage: '{{connectedAccount}} je uklonjen s vašeg računa.',\n        title: 'Ukloni povezani račun',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}',\n      successMessage: 'Pružatelj usluge je dodan vašem računu',\n      title: 'Dodaj povezani račun',\n    },\n    deletePage: {\n      actionDescription: 'Upišite \"Izbriši račun\" ispod za nastavak.',\n      confirm: 'Izbriši račun',\n      messageLine1: 'Jeste li sigurni da želite izbrisati svoj račun?',\n      messageLine2: 'Ova radnja je trajna i nepovratna.',\n      title: 'Izbriši račun',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Poruka s verifikacijskim kodom bit će poslana na ovu adresu e-pošte.',\n        formSubtitle: 'Unesite verifikacijski kod poslan na {{identifier}}',\n        formTitle: 'Verifikacijski kod',\n        resendButton: 'Niste primili kod? Pošalji ponovno',\n        successMessage: 'E-pošta {{identifier}} je dodana vašem računu.',\n      },\n      emailLink: {\n        formHint: 'Poruka s verifikacijskom poveznicom bit će poslana na ovu adresu e-pošte.',\n        formSubtitle: 'Kliknite na verifikacijsku poveznicu u e-pošti poslanoj na {{identifier}}',\n        formTitle: 'Verifikacijska poveznica',\n        resendButton: 'Niste primili poveznicu? Pošalji ponovno',\n        successMessage: 'E-pošta {{identifier}} je dodana vašem računu.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} će biti uklonjen s ovog računa.',\n        messageLine2: 'Više se nećete moći prijaviti koristeći ovu adresu e-pošte.',\n        successMessage: '{{emailAddress}} je uklonjena s vašeg računa.',\n        title: 'Ukloni adresu e-pošte',\n      },\n      title: 'Dodaj adresu e-pošte',\n      verifyTitle: 'Potvrdi adresu e-pošte',\n    },\n    formButtonPrimary__add: 'Dodaj',\n    formButtonPrimary__continue: 'Nastavi',\n    formButtonPrimary__finish: 'Završi',\n    formButtonPrimary__remove: 'Ukloni',\n    formButtonPrimary__save: 'Spremi',\n    formButtonReset: 'Odustani',\n    mfaPage: {\n      formHint: 'Odaberite metodu za dodavanje.',\n      title: 'Dodaj dvostupanjsku provjeru',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Koristi postojeći broj',\n      primaryButton__addPhoneNumber: 'Dodaj telefonski broj',\n      removeResource: {\n        messageLine1: '{{identifier}} više neće primati verifikacijske kodove prilikom prijave.',\n        messageLine2: 'Vaš račun možda neće biti toliko siguran. Jeste li sigurni da želite nastaviti?',\n        successMessage: 'SMS kod dvostupanjske provjere je uklonjen za {{mfaPhoneCode}}',\n        title: 'Ukloni dvostupanjsku provjeru',\n      },\n      subtitle__availablePhoneNumbers:\n        'Odaberite postojeći telefonski broj za registraciju SMS koda dvostupanjske provjere ili dodajte novi.',\n      subtitle__unavailablePhoneNumbers:\n        'Nema dostupnih telefonskih brojeva za registraciju SMS koda dvostupanjske provjere, molimo dodajte novi.',\n      successMessage1:\n        'Prilikom prijave, morat ćete unijeti verifikacijski kod poslan na ovaj telefonski broj kao dodatni korak.',\n      successMessage2:\n        'Spremite ove rezervne kodove i pohranite ih na sigurno mjesto. Ako izgubite pristup svom uređaju za autentifikaciju, možete koristiti rezervne kodove za prijavu.',\n      successTitle: 'SMS kod verifikacije omogućen',\n      title: 'Dodaj SMS kod verifikacije',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Umjesto toga, skeniraj QR kod',\n        buttonUnableToScan__nonPrimary: 'Ne možete skenirati QR kod?',\n        infoText__ableToScan:\n          'Postavite novu metodu prijave u vašoj aplikaciji za autentifikaciju i skenirajte sljedeći QR kod da biste je povezali s vašim računom.',\n        infoText__unableToScan: 'Postavite novu metodu prijave u vašem autentifikatoru i unesite Ključ naveden ispod.',\n        inputLabel__unableToScan1:\n          'Provjerite jesu li omogućene vremenski temeljene ili jednokratne lozinke, zatim završite povezivanje vašeg računa.',\n        inputLabel__unableToScan2:\n          'Alternativno, ako vaš autentifikator podržava TOTP URI-je, možete također kopirati cijeli URI.',\n      },\n      removeResource: {\n        messageLine1: 'Verifikacijski kodovi iz ovog autentifikatora više neće biti potrebni prilikom prijave.',\n        messageLine2: 'Vaš račun možda neće biti toliko siguran. Jeste li sigurni da želite nastaviti?',\n        successMessage: 'Dvostupanjska provjera putem aplikacije za autentifikaciju je uklonjena.',\n        title: 'Ukloni dvostupanjsku provjeru',\n      },\n      successMessage:\n        'Dvostupanjska provjera je sada omogućena. Prilikom prijave, morat ćete unijeti verifikacijski kod iz ovog autentifikatora kao dodatni korak.',\n      title: 'Dodaj aplikaciju za autentifikaciju',\n      verifySubtitle: 'Unesite verifikacijski kod generiran vašim autentifikatorom',\n      verifyTitle: 'Verifikacijski kod',\n    },\n    mobileButton__menu: 'Izbornik',\n    navbar: {\n      account: 'Profil',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Upravljajte informacijama vašeg računa.',\n      security: 'Sigurnost',\n      title: 'Račun',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} će biti uklonjen s ovog računa.',\n        title: 'Ukloni pristupni ključ',\n      },\n      subtitle__rename: 'Možete promijeniti naziv pristupnog ključa kako biste ga lakše pronašli.',\n      title__rename: 'Preimenuj pristupni ključ',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Preporučuje se odjava sa svih drugih uređaja koji su možda koristili vašu staru lozinku.',\n      readonly: 'Vaša lozinka trenutno se ne može uređivati jer se možete prijaviti samo putem poslovne veze.',\n      successMessage__set: 'Vaša lozinka je postavljena.',\n      successMessage__signOutOfOtherSessions: 'Svi ostali uređaji su odjavljeni.',\n      successMessage__update: 'Vaša lozinka je ažurirana.',\n      title__set: 'Postavi lozinku',\n      title__update: 'Ažuriraj lozinku',\n    },\n    phoneNumberPage: {\n      infoText:\n        'Tekstualna poruka s verifikacijskim kodom bit će poslana na ovaj telefonski broj. Mogu se primijeniti naknade za poruke i podatke.',\n      removeResource: {\n        messageLine1: '{{identifier}} će biti uklonjen s ovog računa.',\n        messageLine2: 'Više se nećete moći prijaviti koristeći ovaj telefonski broj.',\n        successMessage: '{{phoneNumber}} je uklonjen s vašeg računa.',\n        title: 'Ukloni telefonski broj',\n      },\n      successMessage: '{{identifier}} je dodan vašem računu.',\n      title: 'Dodaj telefonski broj',\n      verifySubtitle: 'Unesite verifikacijski kod poslan na {{identifier}}',\n      verifyTitle: 'Potvrdi telefonski broj',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Preporučena veličina 1:1, do 10MB.',\n      imageFormDestructiveActionSubtitle: 'Ukloni',\n      imageFormSubtitle: 'Učitaj',\n      imageFormTitle: 'Profilna slika',\n      readonly: 'Vaše profilne informacije su pružene putem poslovne veze i ne mogu se uređivati.',\n      successMessage: 'Vaš profil je ažuriran.',\n      title: 'Ažuriraj profil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Odjavi se s uređaja',\n        title: 'Aktivni uređaji',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Ponovno poveži',\n        actionLabel__reauthorize: 'Autoriziraj sada',\n        destructiveActionTitle: 'Ukloni',\n        primaryButton: 'Poveži račun',\n        subtitle__disconnected: 'Ovaj račun je isključen.',\n        subtitle__reauthorize:\n          'Potrebna ovlaštenja su ažurirana i možda doživljavate ograničenu funkcionalnost. Molimo vas da ponovno autorizirate ovu aplikaciju kako biste izbjegli probleme',\n        title: 'Povezani računi',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Izbriši račun',\n        title: 'Izbriši račun',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Ukloni e-poštu',\n        detailsAction__nonPrimary: 'Postavi kao primarnu',\n        detailsAction__primary: 'Dovrši verifikaciju',\n        detailsAction__unverified: 'Potvrdi',\n        primaryButton: 'Dodaj adresu e-pošte',\n        title: 'Adrese e-pošte',\n      },\n      enterpriseAccountsSection: {\n        title: 'Poslovni računi',\n      },\n      headerTitle__account: 'Detalji profila',\n      headerTitle__security: 'Sigurnost',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Regeneriraj',\n          headerTitle: 'Rezervni kodovi',\n          subtitle__regenerate:\n            'Dobijte novi set sigurnosnih rezervnih kodova. Prethodni rezervni kodovi bit će izbrisani i ne mogu se koristiti.',\n          title__regenerate: 'Regeneriraj rezervne kodove',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Postavi kao zadano',\n          destructiveActionLabel: 'Ukloni',\n        },\n        primaryButton: 'Dodaj dvostupanjsku provjeru',\n        title: 'Dvostupanjska provjera',\n        totp: {\n          destructiveActionTitle: 'Ukloni',\n          headerTitle: 'Aplikacija za autentifikaciju',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Ukloni',\n        menuAction__rename: 'Preimenuj',\n        primaryButton: undefined,\n        title: 'Pristupni ključevi',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Postavi lozinku',\n        primaryButton__updatePassword: 'Ažuriraj lozinku',\n        title: 'Lozinka',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Ukloni telefonski broj',\n        detailsAction__nonPrimary: 'Postavi kao primarni',\n        detailsAction__primary: 'Dovrši verifikaciju',\n        detailsAction__unverified: 'Potvrdi telefonski broj',\n        primaryButton: 'Dodaj telefonski broj',\n        title: 'Telefonski brojevi',\n      },\n      profileSection: {\n        primaryButton: 'Ažuriraj profil',\n        title: 'Profil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Postavi korisničko ime',\n        primaryButton__updateUsername: 'Ažuriraj korisničko ime',\n        title: 'Korisničko ime',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Ukloni novčanik',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Poveži novčanik',\n        title: 'Web3 novčanici',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Vaše korisničko ime je ažurirano.',\n      title__set: 'Postavi korisničko ime',\n      title__update: 'Ažuriraj korisničko ime',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} će biti uklonjen s ovog računa.',\n        messageLine2: 'Više se nećete moći prijaviti koristeći ovaj web3 novčanik.',\n        successMessage: '{{web3Wallet}} je uklonjen s vašeg računa.',\n        title: 'Ukloni web3 novčanik',\n      },\n      subtitle__availableWallets: 'Odaberite web3 novčanik za povezivanje s vašim računom.',\n      subtitle__unavailableWallets: 'Nema dostupnih web3 novčanika.',\n      successMessage: 'Novčanik je dodan vašem računu.',\n      title: 'Dodaj web3 novčanik',\n      web3WalletButtonsBlockButton: '{{provider|titleize}}',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BACE;AAAA,IACF,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCACE;AAAA,IACF,yCACE;AAAA,IACF,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBAAwB;AAAA,QACxB,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UACE;AAAA,MACF,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}