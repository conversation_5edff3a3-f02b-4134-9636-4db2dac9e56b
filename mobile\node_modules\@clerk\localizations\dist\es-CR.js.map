{"version": 3, "sources": ["../src/es-CR.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const esCR: LocalizationResource = {\n  locale: 'es-CR',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Atrás',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: 'Plan actual',\n  badge__default: 'Por defecto',\n  badge__endsAt: \"Termina {{ date | shortDate('en-ES') }}\",\n  badge__expired: 'Caducado',\n  badge__otherImpersonatorDevice: 'Otro dispositivo de imitación',\n  badge__primary: 'Primario',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Requiere acción',\n  badge__startsAt: \"Empieza {{ date | shortDate('en-ES') }}\",\n  badge__thisDevice: 'Este dispositivo',\n  badge__unverified: 'No confirmado',\n  badge__upcomingPlan: 'Próximo plan',\n  badge__userDevice: 'Dispositivo de usuario',\n  badge__you: 'Usted',\n  commerce: {\n    addPaymentMethod: 'Añadir método de pago',\n    alwaysFree: 'Siempre gratis',\n    annually: 'Anualmente',\n    availableFeatures: 'Características disponibles',\n    billedAnnually: 'Facturado anualmente',\n    billedMonthlyOnly: undefined,\n    cancelSubscription: 'Cancelar suscripción',\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: 'Tu nueva suscripción está lista.',\n      description__subscriptionSuccessful: 'Tu nueva suscripción está lista.',\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: 'Método de pago',\n        title__statementId: 'Número de factura',\n        title__subscriptionBegins: 'La suscripción empieza',\n        title__totalPaid: 'Total pagado',\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: '¡Pago exitoso!',\n      title__subscriptionSuccessful: '¡Éxito!',\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: 'Gratis',\n    getStarted: 'Empezar',\n    keepSubscription: 'Mantener suscripción',\n    manage: 'Gestionar',\n    manageSubscription: 'Gestionar suscripción',\n    month: 'Mes',\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: 'Volver a suscribirse',\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: 'Cambiar a este plan',\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Crear organización',\n    invitePage: {\n      formButtonReset: 'Saltar',\n    },\n    title: 'Crear organización',\n  },\n  dates: {\n    lastDay: \"Ayer a las {{ date | timeString('es-ES') }}\",\n    next6Days: \"{{ date | weekday('es-ES','long') }} a las {{ date | timeString('es-ES') }}\",\n    nextDay: \"Mañana a las {{ date | timeString('es-ES') }}\",\n    numeric: \"{{ date | numeric('es-ES') }}\",\n    previous6Days: \"Último {{ date | weekday('es-ES','long') }} en {{ date | timeString('es-ES') }}\",\n    sameDay: \"Hoy a las {{ date | timeString('es-ES') }}\",\n  },\n  dividerText: 'o',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Usar otro método',\n  footerPageLink__help: 'Ayuda',\n  footerPageLink__privacy: 'Privacidad',\n  footerPageLink__terms: 'Términos',\n  formButtonPrimary: 'Continuar',\n  formButtonPrimary__verify: 'Verificar',\n  formFieldAction__forgotPassword: 'Has olvidado tu contraseña?',\n  formFieldError__matchingPasswords: 'Las contraseñas coinciden.',\n  formFieldError__notMatchingPasswords: 'Las contraseñas no coinciden.',\n  formFieldError__verificationLinkExpired: 'El enlace de verificación expiró. Por favor, vuelve a solicitarlo.',\n  formFieldHintText__optional: 'Opcional',\n  formFieldHintText__slug:\n    'Un apodo es una identificación legible por humanos que debe ser única. Se utiliza a menudo en URL.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: 'Ingresa el código de respaldo',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Eliminar cuenta',\n  formFieldInputPlaceholder__emailAddress: 'Ingresa tu correo electrónico',\n  formFieldInputPlaceholder__emailAddress_username: 'Ingresa el correo electrónico o nombre de usuario',\n  formFieldInputPlaceholder__emailAddresses:\n    'Ingrese o pegue una o más direcciones de correo electrónico, separadas por espacios o comas',\n  formFieldInputPlaceholder__firstName: 'Nombre',\n  formFieldInputPlaceholder__lastName: 'Apellido',\n  formFieldInputPlaceholder__organizationDomain: 'ejemplo.com',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: '<EMAIL>',\n  formFieldInputPlaceholder__organizationName: 'Nombre de la organización',\n  formFieldInputPlaceholder__organizationSlug: 'mi-organización',\n  formFieldInputPlaceholder__password: 'Ingresa tu contraseña',\n  formFieldInputPlaceholder__phoneNumber: 'Ingresa tu número de teléfono',\n  formFieldInputPlaceholder__username: 'Ingresa tu nombre de usuario',\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Activar invitaciones automáticas para este dominio',\n  formFieldLabel__backupCode: 'Código de respaldo',\n  formFieldLabel__confirmDeletion: 'Confirmación',\n  formFieldLabel__confirmPassword: 'Confirme la contraseña',\n  formFieldLabel__currentPassword: 'Contraseña actual',\n  formFieldLabel__emailAddress: 'Correo electrónico',\n  formFieldLabel__emailAddress_username: 'Correo electrónico o nombre de usuario',\n  formFieldLabel__emailAddresses: 'Direcciones de correo',\n  formFieldLabel__firstName: 'Nombre',\n  formFieldLabel__lastName: 'Apellido',\n  formFieldLabel__newPassword: 'Nueva contraseña',\n  formFieldLabel__organizationDomain: 'Dominio',\n  formFieldLabel__organizationDomainDeletePending: 'Eliminar invitaciones y sugerencias pendientes',\n  formFieldLabel__organizationDomainEmailAddress: 'Verificación de correo',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Entrar una dirección de correo electrónico bajo este dominio para recibir un código y verificarlo.',\n  formFieldLabel__organizationName: 'Nombre de la Organización',\n  formFieldLabel__organizationSlug: 'Apodo',\n  formFieldLabel__passkeyName: 'Nombre de llave de acceso',\n  formFieldLabel__password: 'Contraseña',\n  formFieldLabel__phoneNumber: 'Número de teléfono',\n  formFieldLabel__role: 'Rol',\n  formFieldLabel__signOutOfOtherSessions: 'Cerrar sesión en todos los demás dispositivos',\n  formFieldLabel__username: 'Nombre de usuario',\n  impersonationFab: {\n    action__signOut: 'Cerrar',\n    title: 'Registrado como {{identifier}}',\n  },\n  maintenanceMode: 'Actualmente estamos en mantenimiento, pero no te preocupes, no debería llevar más de unos minutos.',\n  membershipRole__admin: 'Administrador',\n  membershipRole__basicMember: 'Miembro',\n  membershipRole__guestMember: 'Invitado',\n  organizationList: {\n    action__createOrganization: 'Crear organización',\n    action__invitationAccept: 'Unirse',\n    action__suggestionsAccept: 'Pedir unirse',\n    createOrganization: 'Crear Organización',\n    invitationAcceptedLabel: 'Unido',\n    subtitle: 'para continuar con {{applicationName}}',\n    suggestionsAcceptedLabel: 'Aprobación pendiente',\n    title: 'Elige una cuenta',\n    titleWithoutPersonal: 'Elige una organización',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Invitaciones automáticas',\n    badge__automaticSuggestion: 'Sugerencias automáticas',\n    badge__manualInvitation: 'Sin inscripciones automáticas',\n    badge__unverified: 'No verificado',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Añade el dominio para verificar. Los usuarios con direcciones de correo electrónico en este dominio pueden unirse a la organización aquí o pedir unirse.',\n      title: 'Anadir dominio',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'No se pudieron enviar las invitaciones. Solucione lo siguiente y vuelva a intentarlo:',\n      formButtonPrimary__continue: 'Enviar invitaciones',\n      selectDropdown__role: 'Seleccionar rol',\n      subtitle: 'Ingresa o pega una o más direcciones de correo electrónico, separadas por espacios o comas',\n      successMessage: 'Invitaciones enviadas con éxito',\n      title: 'Invitar miembros',\n    },\n    membersPage: {\n      action__invite: 'Invitar',\n      action__search: 'Buscar',\n      activeMembersTab: {\n        menuAction__remove: 'Eliminar miembro',\n        tableHeader__actions: 'Acciones',\n        tableHeader__joined: 'Se unió',\n        tableHeader__role: 'Rol',\n        tableHeader__user: 'Usuario',\n      },\n      detailsTitle__emptyRow: 'No hay miembros para mostrar',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Invita usuarios conectando un correo electrónico del dominio con su organización. Cualquiera que se registre con un correo electrónico del dominio existente podrá unirse a la organización en cualquier momento.',\n          headerTitle: 'Invitaciones automáticas',\n          primaryButton: 'Gestionar dominios verificados',\n        },\n        table__emptyRow: 'No hay invitaciones para mostrar',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Revocar invitación',\n        tableHeader__invited: 'Invitado',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Los usuarios que inicien sesión con un correo electrónico del dominio existente podrán ver una sugerencia para solicitar unirse a su organización.',\n          headerTitle: 'Sugerencias automáticas',\n          primaryButton: 'Gestionar dominios verificados',\n        },\n        menuAction__approve: 'Aprobado',\n        menuAction__reject: 'Rechazado',\n        tableHeader__requested: 'Acceso solicitado',\n        table__emptyRow: 'No hay solicitudes para mostrar',\n      },\n      start: {\n        headerTitle__invitations: 'Invitaciones',\n        headerTitle__members: 'Miembros',\n        headerTitle__requests: 'Solicitudes',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: 'Facturación',\n      description: 'Gestiona tu organización.',\n      general: 'General',\n      members: 'Miembros',\n      title: 'Organización',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Escribe \"{{organizationName}}\" a continuación para continuar.',\n          messageLine1: '¿Estás seguro que quieres eliminar esta organización?',\n          messageLine2: 'Esta acción es permanente e irreversible.',\n          successMessage: 'Haz eliminado la organización.',\n          title: 'Eliminar la organización',\n        },\n        leaveOrganization: {\n          actionDescription: 'Escribe \"{{organizationName}}\" a continuación para continuar.',\n          messageLine1:\n            '¿Estás seguro de que deseas abandonar esta organización? Perderás el acceso a esta organización y sus aplicaciones.',\n          messageLine2: 'Esta acción es permanente e irreversible.',\n          successMessage: 'Has dejado la organización.',\n          title: 'Abandonar la organización',\n        },\n        title: 'Peligro',\n      },\n      domainSection: {\n        menuAction__manage: 'Gestionar',\n        menuAction__remove: 'Eliminar',\n        menuAction__verify: 'Verificar',\n        primaryButton: 'Añadir dominio',\n        subtitle:\n          'Permite a los usuarios conectarse automáticamente o solicitar unirse a la organización basado en un correo electrónico del dominio verificado.',\n        title: 'Dominios verificados',\n      },\n      successMessage: 'La organización ha sido actualizada.',\n      title: 'Perfil de la organización',\n    },\n    removeDomainPage: {\n      messageLine1: 'Se eliminará el de correo electrónico del dominio {{domain}}.',\n      messageLine2:\n        'Los usuarios no podrán unirse a la organización de manera automática una vez que se haya eliminado.',\n      successMessage: '{{domain}} se ha eliminado.',\n      title: 'Eliminar dominio',\n    },\n    start: {\n      headerTitle__general: 'General',\n      headerTitle__members: 'Miembros',\n      profileSection: {\n        primaryButton: 'Actualizar perfil',\n        title: 'Perfil de la organización',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Eliminar este dominio afectará a los usuarios invitados.',\n        removeDomainActionLabel__remove: 'Eliminar dominio',\n        removeDomainSubtitle: 'Eliminar este dominio de los dominios verificados',\n        removeDomainTitle: 'Eliminar dominio',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Los usuarios son invitados automáticamente a la organización cuando se registran y pueden unirse en cualquier momento.',\n        automaticInvitationOption__label: 'Invitaciones automáticas',\n        automaticSuggestionOption__description:\n          'Los usuarios reciben una sugerencia para solicitar unirse, pero deben ser aprobados por un administrador antes de poder unirse a la organización.',\n        automaticSuggestionOption__label: 'Sugerencias automáticas',\n        calloutInfoLabel: 'Cambiar el modo de inscripción solo afectará a los nuevos usuarios.',\n        calloutInvitationCountLabel: 'Invitaciones pendientes enviadas a usuarios: {{count}}',\n        calloutSuggestionCountLabel: 'Sugerencias pendientes enviadas a usuarios: {{count}}',\n        manualInvitationOption__description: 'Los usuarios solo pueden ser invitados manualmente a la organización.',\n        manualInvitationOption__label: 'Sin inscripción automática',\n        subtitle: 'Seleccione cómo los usuarios de este dominio pueden unirse a la organización.',\n      },\n      start: {\n        headerTitle__danger: 'Peligro',\n        headerTitle__enrollment: 'Opciones de inscripción',\n      },\n      subtitle: 'El dominio {{domain}} ahora está verificado. Continúa seleccionando el modo de inscripción.',\n      title: 'Actualizar {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Introduce el código de verificación enviado a tu dirección de correo electrónico',\n      formTitle: 'Código de verificación',\n      resendButton: 'No recibiste un código? Reenviar',\n      subtitle: 'El dominio {{domainName}} necesita ser verificado vía correo electrónico.',\n      subtitleVerificationCodeScreen:\n        'Se envió un código de verificación a {{emailAddress}}. Introduce el código para continuar.',\n      title: 'Verificar dominio',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Crear Organización',\n    action__invitationAccept: 'Unirse',\n    action__manageOrganization: 'Administrar Organización',\n    action__suggestionsAccept: 'Solicitar unirse',\n    notSelected: 'Ninguna organización seleccionada',\n    personalWorkspace: 'Espacio personal',\n    suggestionsAcceptedLabel: 'Pendiente de aprobación',\n  },\n  paginationButton__next: 'Siguiente',\n  paginationButton__previous: 'Anterior',\n  paginationRowText__displaying: 'Mostrando',\n  paginationRowText__of: 'de',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Solicitar ayuda',\n      actionText: '¿No posees ninguno de estos?',\n      blockButton__backupCode: 'Utiliza un código de respaldo',\n      blockButton__emailCode: 'Envía el código a {{identifier}}',\n      blockButton__passkey: 'Utiliza tu llave de acceso',\n      blockButton__password: 'Continúa con tu contraseña',\n      blockButton__phoneCode: 'Enviar un mensaje de texto a {{identifier}}',\n      blockButton__totp: 'aplicación de autenticación',\n      getHelp: {\n        blockButton__emailSupport: 'Soporte por correo electrónico',\n        content: 'Si tienes problemas para iniciar sesión',\n        title: 'Obtener ayuda',\n      },\n      subtitle: '¿Tienes problemas? Puedes utilizar cualquiera de estos métodos para iniciar sesión',\n      title: 'Utiliza otro método',\n    },\n    backupCodeMfa: {\n      subtitle: 'Tu código de respaldo es el que obtuviste cuando configuraste la autenticación en dos pasos',\n      title: 'Ingresa tu código de respaldo',\n    },\n    emailCode: {\n      formTitle: 'Código de verificación',\n      resendButton: '¿No recibiste un código? Reenviar',\n      subtitle: 'para continuar con {{applicationName}}',\n      title: 'Revisa tu correo electrónico',\n    },\n    noAvailableMethods: {\n      message: 'No se puede proceder con el inicio de sesión. No hay ningún factor de autenticación disponible',\n      subtitle: 'Ocurrió un error',\n      title: 'No se puede iniciar sesión',\n    },\n    passkey: {\n      blockButton__passkey: 'Utiliza tu llave de acceso',\n      subtitle:\n        'Utilizar tu llave de acceso confirma que eres tú. Tu dispositivo puede solicitar tu huella dactilar, rostro o pantalla de bloqueo.',\n      title: 'Utiliza tu llave de acceso',\n    },\n    password: {\n      actionLink: 'Utiliza otro método',\n      subtitle: 'Ingresa tu contraseña actual para continuar',\n      title: 'Verificación requerida',\n    },\n    phoneCode: {\n      formTitle: 'Código de verificación',\n      resendButton: '¿No recibiste un código? Reenviar',\n      subtitle: 'Ingresa el código enviado a tu teléfono para continuar',\n      title: 'Verificación requerida',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Código de verificación',\n      resendButton: '¿No recibiste un código? Reenviar',\n      subtitle: 'Ingresa el código enviado a tu teléfono para continuar',\n      title: 'Verificación requerida',\n    },\n    totpMfa: {\n      formTitle: 'Código de verificación',\n      subtitle: 'Ingresa el código generado por la aplicación de autenticación para continuar',\n      title: 'Verificación requerida',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Agregar cuenta',\n      action__signOutAll: 'Cerrar sesión de todas las cuentas',\n      subtitle: 'Seleccione la cuenta con la que desea continuar.',\n      title: 'Elija una cuenta',\n    },\n    alternativeMethods: {\n      actionLink: 'Obtener ayuda',\n      actionText: '¿No tienes ninguno de estos?',\n      blockButton__backupCode: 'Usa un código de respaldo',\n      blockButton__emailCode: 'Enviar código a {{identifier}}',\n      blockButton__emailLink: 'Enviar enlace a {{identifier}}',\n      blockButton__passkey: 'Inicia sesión con tu llave de acceso',\n      blockButton__password: 'Inicia sesión con tu contraseña',\n      blockButton__phoneCode: 'Enviar código a {{identifier}}',\n      blockButton__totp: 'Usa tu aplicación de autenticación',\n      getHelp: {\n        blockButton__emailSupport: 'Soporte de correo electrónico',\n        content:\n          'Si tienes problemas para ingresar a tu cuenta, envíenos un correo electrónico y trabajaremos contigo para restablecer el acceso lo antes posible.',\n        title: 'Obtener ayuda',\n      },\n      subtitle: 'Si estás experimentando problemas, puedes utilizar uno de estos métodos para ingresar.',\n      title: 'Utiliza otro método',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Introduce un código de seguridad',\n    },\n    emailCode: {\n      formTitle: 'Código de verificación',\n      resendButton: 'Re-enviar código',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Revise su correo electrónico',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Para continuar, abre el enlace de verificación en tu dispositivo o navegador donde intentaste iniciar sesión',\n        title: 'El enlace de verificación es inválido para este dispositivo',\n      },\n      expired: {\n        subtitle: 'Regresa a la pestaña original para continuar.',\n        title: 'El enlace de verificación ha expirado',\n      },\n      failed: {\n        subtitle: 'Regresa a la pestaña original para continuar.',\n        title: 'El enlace de verificación es inválido',\n      },\n      formSubtitle: 'Utiliza el enlace de verificación enviado a tu correo electrónico',\n      formTitle: 'Enlace de verificación',\n      loading: {\n        subtitle: 'Serás redireccionado pronto',\n        title: 'Iniciando sesión...',\n      },\n      resendButton: '¿No recibiste un enlace? Reenviar',\n      subtitle: 'para continuar con {{applicationName}}',\n      title: 'Revisa tu correo electrónico',\n      unusedTab: {\n        title: 'Puedes cerrar esta pestaña',\n      },\n      verified: {\n        subtitle: 'Serás redireccionado pronto',\n        title: 'Sesión iniciada correctamente',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Regresa a la pestaña original para continuar',\n        subtitleNewTab: 'Regresa a la pestaña recién abierta para continuar',\n        titleNewTab: 'Sesión iniciada en otra pestaña',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Código para restablecer contraseña',\n      resendButton: '¿No recibiste un código? Reenviar',\n      subtitle: 'para restablecer tu contraseña',\n      subtitle_email: 'Primero, ingresa el código enviado a tu correo electrónico',\n      subtitle_phone: 'Primero, ingresa el código enviado a tu teléfono',\n      title: 'Restablecer contraseña',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Restablece tu contraseña',\n      label__alternativeMethods: 'O, inicia sesión con otro método',\n      title: '¿Olvidaste la contraseña?',\n    },\n    noAvailableMethods: {\n      message: 'No se puede continuar con el inicio de sesión. No hay ningún factor de autenticación disponible.',\n      subtitle: 'Ocurrió un error',\n      title: 'No puedo iniciar sesión',\n    },\n    passkey: {\n      subtitle:\n        'Usando tu llave de acceso confirmas que eres tú. Tu dispositivo puede pedirte la huella dactilar, el rostro o el bloqueo de pantalla.',\n      title: 'Usa tu llave de acceso',\n    },\n    password: {\n      actionLink: 'Utiliza otro método',\n      subtitle: 'para continuar con {{applicationName}}',\n      title: 'Introduzca su contraseña',\n    },\n    passwordPwned: {\n      title: 'Contraseña en peligro',\n    },\n    phoneCode: {\n      formTitle: 'Código de verificación',\n      resendButton: 'Reenviar código',\n      subtitle: 'para continuar con {{applicationName}}',\n      title: 'Revisa tu teléfono',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Código de verificación',\n      resendButton: 'Reenviar código',\n      subtitle: 'para continuar con {{applicationName}}',\n      title: 'Revisa tu teléfono',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Restablecer contraseña',\n      requiredMessage: 'Por razones de seguridad, es necesario restablecer su contraseña.',\n      successMessage:\n        'Tu contraseña se ha cambiado correctamente. Te estamos redirigiendo, por favor espera un momento.',\n      title: 'Establecer nueva contraseña',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Es necesario verificar su identidad para restablecer su contraseña.',\n    },\n    start: {\n      actionLink: 'Registrarse',\n      actionLink__join_waitlist: 'Unirse a la lista de espera',\n      actionLink__use_email: 'Utilizar correo electrónico',\n      actionLink__use_email_username: 'Utilizar correo electrónico o nombre de usuario',\n      actionLink__use_passkey: 'Utilizar llave de acceso',\n      actionLink__use_phone: 'Utilizar teléfono',\n      actionLink__use_username: 'Utilizar nombre de usuario',\n      actionText: '¿No tienes cuenta?',\n      actionText__join_waitlist: '¿Quieres acceso anticipado?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: '¡Bienvenido de vuelta! Por favor inicia sesión para continuar',\n      subtitleCombined: undefined,\n      title: 'Iniciar sesión',\n      titleCombined: 'Para continuar con {{applicationName}}',\n    },\n    totpMfa: {\n      formTitle: 'Código de verificación',\n      subtitle: 'Para continuar, por favor introduce el código generado por tu aplicación de autenticación',\n      title: 'Verificación de dos pasos',\n    },\n  },\n  signInEnterPasswordTitle: 'Ingresa tu contraseña',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Entrar',\n      actionText: '¿Tienes una cuenta?',\n      subtitle: 'Por favor completa los datos para continuar',\n      title: 'Completa los datos faltantes',\n    },\n    emailCode: {\n      formSubtitle: 'Ingresa el código de verificación enviado a tu correo electrónico',\n      formTitle: 'Código de verificación',\n      resendButton: 'Reenviar código',\n      subtitle: 'Ingresa el código de verificación enviado a tu correo electrónico',\n      title: 'Verifica tu correo electrónico',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Para continuar, abre el enlace de verificación en el dispositivo o navegador desde el que estás intentando registrarte',\n        title: 'El enlace de verificación es inválido para este dispositivo',\n      },\n      formSubtitle: 'Utiliza el enlace de verificación enviado a tu dirección de correo electrónico',\n      formTitle: 'Enlace de verificación',\n      loading: {\n        title: 'Registrando...',\n      },\n      resendButton: '¿No recibiste el enlace? Reenviar',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Verifica tu correo electrónico',\n      verified: {\n        title: 'Registrado con éxito',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Regresa a la pestaña recién abierta para continuar',\n        subtitleNewTab: 'Regresa a la pestaña anterior para continuar',\n        title: 'Correo electrónico verificado con éxito',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Introduzca el código de verificación enviado a su número de teléfono.',\n      formTitle: 'Código de verificación',\n      resendButton: 'Reenviar código',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Verifica tu teléfono',\n    },\n    restrictedAccess: {\n      actionLink: 'Iniciar sesión',\n      actionText: '¿Tienes cuenta?',\n      blockButton__emailSupport: 'Soporte por correo electrónico',\n      blockButton__joinWaitlist: 'Unirse a la lista de espera',\n      subtitle: 'Los registros están desactivados actualmente. Si crees que tienes acceso, contacta soporte',\n      subtitleWaitlist:\n        'Los registros están desactivados actualmente. Para ser el primero en saber cuando estemos listos, únete a la lista de espera',\n      title: 'Acceso restringido',\n    },\n    start: {\n      actionLink: 'Iniciar sesión',\n      actionLink__use_email: 'Utilizar el correo electrónico',\n      actionLink__use_phone: 'Utilizar el número telefónico',\n      actionText: '¿Tienes una cuenta?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: '¡Bienvenido! Por favor completa los datos para comenzar',\n      subtitleCombined: '¡Bienvenido! Por favor completa los datos para comenzar',\n      title: 'Crea tu cuenta',\n      titleCombined: 'Crea tu cuenta',\n    },\n  },\n  socialButtonsBlockButton: 'Continuar con {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: '{{email}} ya es miembro de la organización.',\n    captcha_invalid:\n      'El registro falló debido a fallos en la validación de seguridad. Por favor, recarga la página o contáctanos para obtener más asistencia.',\n    captcha_unavailable:\n      'El registro falló debido a fallos en la validación de robot. Por favor, recarga la página o contáctanos para obtener más asistencia.',\n    form_code_incorrect: 'Código incorrecto.',\n    form_identifier_exists__email_address: 'El correo electrónico ya existe',\n    form_identifier_exists__phone_number: 'El número telefónico ya existe.',\n    form_identifier_exists__username: 'El nombre de usuario ya existe.',\n    form_identifier_not_found: 'No se encontró una cuenta, intenta de nuevo.',\n    form_param_format_invalid: 'Formato inválido.',\n    form_param_format_invalid__email_address: 'El correo electrónico debe ser válido.',\n    form_param_format_invalid__phone_number: 'El número telefónico debe ser en un formato válido internacional.',\n    form_param_max_length_exceeded__first_name: 'El nombre debe tener menos de 256 caracteres.',\n    form_param_max_length_exceeded__last_name: 'El apellido debe tener menos de 256 caracteres.',\n    form_param_max_length_exceeded__name: 'El nombre debe tener menos de 256 caracteres.',\n    form_param_nil: 'Este campo es rquerido y no puede estar vacío.',\n    form_param_value_invalid: 'Valor inválido',\n    form_password_incorrect: 'Contraseña incorrecta.',\n    form_password_length_too_short: 'La contraseña es muy corta.',\n    form_password_not_strong_enough: 'La contraseña no es suficientemente segura.',\n    form_password_pwned:\n      'Esta contraseña se encontró como parte de una brecha y no se puede utilizar, intenta con otra contraseña.',\n    form_password_pwned__sign_in:\n      'Esta contraseña se encontró como parte de una brecha y no se puede utilizar, por favor restablece tu contraseña.',\n    form_password_size_in_bytes_exceeded:\n      'La contraseña excede el número máximo de bytes permitidos. Por favor, elimine algunos caracteres especiales o reduzca la longitud de la contraseña.',\n    form_password_validation_failed: 'Contraseña incorrecta',\n    form_username_invalid_character: 'El nombre de usuario contiene caracteres inválidos.',\n    form_username_invalid_length: 'La longitud del nombre de usuario es demasiado corta.',\n    identification_deletion_failed: 'No se puede eliminar la última identificación.',\n    not_allowed_access: 'No tienes permiso para acceder a esta página.',\n    organization_domain_blocked: 'Este correo electrónico del dominio está bloqueado, intenta con otro.',\n    organization_domain_common: 'Este correo electrónico del dominio es muy común, intenta con otro.',\n    organization_domain_exists_for_enterprise_connection: 'Este dominio ya esta en uso para tú organización',\n    organization_membership_quota_exceeded:\n      'Alcanzaste el limite de miembros en la organización, incluyendo las invitaciones enviadas.',\n    organization_minimum_permissions_needed: 'Debe existir al menos un miembro en la organización.',\n    passkey_already_exists: 'Ya se ha registrado una llave de acceso en este dispositivo.',\n    passkey_not_supported: 'Las llaves de acceso no son compatibles con este dispositivo.',\n    passkey_pa_not_supported:\n      'El registro requiere un autenticador de plataforma, pero el dispositivo no es compatible.',\n    passkey_registration_cancelled: 'El registro de la llave de acceso se ha cancelado o ha expirado.',\n    passkey_retrieval_cancelled: 'La verificación de la llave de acceso se ha cancelado o ha expirado.',\n    passwordComplexity: {\n      maximumLength: 'menos de {{length}} caracteres',\n      minimumLength: '{{length}} o más caracteres',\n      requireLowercase: 'al menos una letra minúscula',\n      requireNumbers: 'al menos un número',\n      requireSpecialCharacter: 'al menos un carácter especial',\n      requireUppercase: 'al menos una letra mayúscula',\n      sentencePrefix: 'Tu contraseña debe contener',\n    },\n    phone_number_exists: 'Este número de telefónico ya está en uso.',\n    session_exists: undefined,\n    web3_missing_identifier: 'No se puede encontrar la extension de la billetera Web3. Instala una para continuar',\n    zxcvbn: {\n      couldBeStronger: 'Tu contraseña funciona, pero puede ser más segura. Prueba añadiendo más caracteres.',\n      goodPassword: 'Tu contraseña cumple con todos los requisitos necesarios.',\n      notEnough: 'Tu contraseña no es lo suficientemente segura.',\n      suggestions: {\n        allUppercase: 'Escribe algunas letras en mayúsculas, pero no todas.',\n        anotherWord: 'Añade palabras menos comunes.',\n        associatedYears: 'Evita años asociados contigo.',\n        capitalization: 'Escribe algunas letras en mayúsculas además de la primera.',\n        dates: 'Evita fechas asociadas contigo.',\n        l33t: \"Evita sustituciones predecibles como '@' por 'a'\",\n        longerKeyboardPattern: 'Usa patrones de teclado más largos y cambia la dirección de escritura varias veces.',\n        noNeed: 'Puedes crear contraseñas seguras sin usar símbolos, números o mayúsculas.',\n        pwned: 'Si utilizas esta contraseña en otro lugar, deberías cambiarla.',\n        recentYears: 'Evita años recientes.',\n        repeated: 'Evita palabras y letras repetidas.',\n        reverseWords: 'Evita palabras comunes escritas al revés',\n        sequences: 'Evita secuencias de letras comunes.',\n        useWords: 'Utiliza varias palabras, pero evita frases comunes.',\n      },\n      warnings: {\n        common: 'Es una contraseña utilizada comúnmente.',\n        commonNames: 'Nombres y apellidos comunes son fáciles de adivinar.',\n        dates: 'Las fechas son fáciles de adivinar.',\n        extendedRepeat: 'Patrones repetidos como \"abcabcabc\" son fáciles de adivinar.',\n        keyPattern: 'Patrones cortos son fáciles de adivinar.',\n        namesByThemselves: 'Nombres y apellidos son fáciles de adivinar.',\n        pwned: 'Tu contraseña fue expuesta por una violación de datos en Internet.',\n        recentYears: 'Los años recientes son fáciles de adivinar.',\n        sequences: 'Patrones comunes como \"abc\" son fáciles de adivinar',\n        similarToCommon: 'Es similar a una contraseña utilizada habitualmente.',\n        simpleRepeat: 'Caracteres repetidos como \"aaa\" son fáciles de adivinar',\n        straightRow: 'Teclas consecutivas en tu teclado son fáciles de adivinar.',\n        topHundred: 'Es una contraseña utilizada con mucha frecuencia.',\n        topTen: 'Es de las contraseñas más utilizadas.',\n        userInputs: 'No debería haber datos personales o relacionados con esta página.',\n        wordByItself: 'Palabras únicas son fáciles de adivinar.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Añadir cuenta',\n    action__manageAccount: 'Administrar cuenta',\n    action__signOut: 'Cerrar sesión',\n    action__signOutAll: 'Salir de todas las cuentas',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Copiado!',\n      actionLabel__copy: 'Copiar todo',\n      actionLabel__download: 'Descargar .txt',\n      actionLabel__print: 'Imprimir',\n      infoText1: 'Se habilitarán códigos de respaldo para esta cuenta.',\n      infoText2:\n        'Mantén los códigos de respaldo en secreto y guárdalos de forma segura. Puedes regenerar códigos de respaldo si sospechas que se han visto comprometidos.',\n      subtitle__codelist: 'Guárdalos de forma segura y mantenlos en secreto.',\n      successMessage:\n        'Los códigos de respaldo ahora están habilitados. Puedes utilizar uno de estos para iniciar sesión en tu cuenta, si pierdes el acceso a tu dispositivo de autenticación. Cada código solo se puede utilizar una vez.',\n      successSubtitle:\n        'Puedes utilizar uno de estos para iniciar sesión en tu cuenta, si pierdes el acceso a tu dispositivo de autenticación.',\n      title: 'Agregar verificación de código de respaldo',\n      title__codelist: 'Códigos de respaldo',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: 'Predeterminado',\n        actionLabel__remove: 'Eliminar',\n        add: 'Agregar un nuevo método de pago',\n        addSubtitle: 'Agregar un nuevo método de pago a tu cuenta.',\n        cancelButton: 'Cancelar',\n        formButtonPrimary__add: 'Agregar Método de Pago',\n        formButtonPrimary__pay: 'Pagar {{amount}}',\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: '{{identifier}} será eliminado de esta cuenta.',\n          messageLine2:\n            'No podrás utilizar este método de pago para ninguna suscripción actual y estas ya no funcionarán.',\n          successMessage: '{{paymentSource}} ha sido eliminada de tu cuenta.',\n          title: 'Eliminar método de pago',\n        },\n        title: 'Opciones disponibles',\n      },\n      start: {\n        headerTitle__payments: 'Pagos',\n        headerTitle__plans: 'Planes',\n        headerTitle__statements: 'Declaraciones',\n        headerTitle__subscriptions: 'Suscripciones',\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: 'Facturación y Pagos',\n    },\n    connectedAccountPage: {\n      formHint: 'Selecciona un proveedor para conectar su cuenta.',\n      formHint__noAccounts: 'No hay proveedores de cuentas externas disponibles.',\n      removeResource: {\n        messageLine1: '{{identifier}} será eliminado de esta cuenta.',\n        messageLine2: 'Ya no podrás utilizar esta cuenta activa y las funciones dependientes ya no funcionarán.',\n        successMessage: '{{connectedAccount}} ha sido eliminado de tu cuenta.',\n        title: 'Eliminar cuenta conectada',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}',\n      successMessage: 'El proveedor ha sido agregado a tu cuenta',\n      title: 'Agregar cuenta conectada',\n    },\n    deletePage: {\n      actionDescription: 'Escribe \"Eliminar cuenta\" a continuación para continuar',\n      confirm: 'Eliminar cuenta',\n      messageLine1: '¿Estás seguro que quieres eliminar tu cuenta?',\n      messageLine2: 'Esta acción es permanente e irreversible.',\n      title: 'Eliminar cuenta',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint:\n          'A esta dirección de correo electrónico se le enviará un correo electrónico con un código de verificación.',\n        formSubtitle: 'Introduce el código de verificación enviado a {{identifier}}',\n        formTitle: 'Código de verificación',\n        resendButton: 'Reenviar código',\n        successMessage: 'El correo electrónico {{identifier}} se ha agregado a tu cuenta.',\n      },\n      emailLink: {\n        formHint:\n          'Se enviará un correo electrónico con un enlace de verificación a esta dirección de correo electrónico.',\n        formSubtitle: 'Haz clic en el enlace de verificación en el correo electrónico enviado a {{identifier}}',\n        formTitle: 'Enlace de verificación',\n        resendButton: 'Reenviar enlace',\n        successMessage: 'El correo electrónico {{identifier}} se ha agregado a su cuenta.',\n      },\n      enterpriseSSOLink: {\n        formButton: 'Click para iniciar sesión',\n        formSubtitle: 'Completar el inicio de sesión con {{identifier}}',\n      },\n      formHint: 'Deberás verificar el correo electrónico antes de ser añadido a tu cuenta',\n      removeResource: {\n        messageLine1: '{{identifier}} será eliminado de esta cuenta.',\n        messageLine2: 'Ya no podrás iniciar sesión con esta dirección de correo electrónico.',\n        successMessage: '{{emailAddress}} ha sido eliminado de tu cuenta.',\n        title: 'Eliminar dirección de correo electrónico',\n      },\n      title: 'Agregar dirección de correo electrónico',\n      verifyTitle: 'Verificar dirección de correo electrónico',\n    },\n    formButtonPrimary__add: 'Agregar',\n    formButtonPrimary__continue: 'Continuar',\n    formButtonPrimary__finish: 'Terminar',\n    formButtonPrimary__remove: 'Eliminar',\n    formButtonPrimary__save: 'Guardar',\n    formButtonReset: 'Cancelar',\n    mfaPage: {\n      formHint: 'Seleccione un método para agregar.',\n      title: 'Agregar verificación en dos pasos',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Usar número existente',\n      primaryButton__addPhoneNumber: 'Agregar número de teléfono',\n      removeResource: {\n        messageLine1: '{{identifier}} dejará de recibir códigos de verificación al iniciar sesión.',\n        messageLine2: 'Es posible que tu cuenta no sea tan segura. ¿Estás seguro de que quieres continuar?',\n        successMessage: 'Se eliminó la verificación de dos pasos por SMS para {{mfaPhoneCode}}',\n        title: 'Eliminar la verificación en dos pasos',\n      },\n      subtitle__availablePhoneNumbers:\n        'Selecciona un número de teléfono para registrar la verificación en dos pasos por SMS.',\n      subtitle__unavailablePhoneNumbers:\n        'No hay números de teléfono disponibles para registrar la verificación en dos pasos por SMS.',\n      successMessage1:\n        'Al iniciar sesión, se te solicitará un código de verificación enviado a este número de teléfono como un paso adicional.',\n      successMessage2:\n        'Guarda estos códigos de respaldo y almacénalos en un lugar seguro. Si pierdes el acceso a tu dispositivo de autenticación, puedes utilizar los códigos de respaldo para iniciar sesión.',\n      successTitle: 'Verificación por SMS habilitada',\n      title: 'Agregar verificación por SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Escanea el código QR en su lugar',\n        buttonUnableToScan__nonPrimary: '¿No puedes escanear el código QR?',\n        infoText__ableToScan:\n          'Configura un nuevo método de inicio de sesión en tu aplicación de autenticación y escanea el siguiente código QR para vincularlo a tu cuenta.',\n        infoText__unableToScan:\n          'Configura un nuevo método de inicio de sesión en tu autenticador e ingresa la clave que se proporciona a continuación.',\n        inputLabel__unableToScan1:\n          'Asegúrate de que las contraseñas de Un-Solo-Uso o Por-Tiempo estén habilitadas, luego finaliza la vinculación con tu cuenta.',\n        inputLabel__unableToScan2:\n          'Alternativamente, si tu autenticador admite URIs TOTP, también puedes copiar la URI completa.',\n      },\n      removeResource: {\n        messageLine1: 'El código de verificación de este autenticador ya no será necesario al iniciar sesión.',\n        messageLine2: 'Es posible que tu cuenta no sea tan segura. ¿Estás seguro de que quieres continuar?',\n        successMessage: 'Se eliminó la verificación en dos pasos a través de la aplicación de autenticación.',\n        title: 'Eliminar la verificación en dos pasos',\n      },\n      successMessage:\n        'La verificación en dos pasos ahora está habilitada. Al iniciar sesión, deberás ingresar un código de verificación de este autenticador como un paso adicional.',\n      title: 'Agregar aplicación de autenticación',\n      verifySubtitle: 'Ingresa el código de verificación generado por tu autenticador',\n      verifyTitle: 'Código de verificación',\n    },\n    mobileButton__menu: 'Menú',\n    navbar: {\n      account: 'Perfil',\n      apiKeys: undefined,\n      billing: 'Facturación',\n      description: 'Administra la información de tu cuenta.',\n      security: 'Seguridad',\n      title: 'Cuenta',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} será eliminada de esta cuenta.',\n        title: 'Eliminar llave de acceso',\n      },\n      subtitle__rename: 'Puedes cambiar el nombre de la llave de acceso para que sea más fácil de encontrar.',\n      title__rename: 'Renombrar llave de acceso',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Se recomienda cerrar la sesión de todos los otros dispositivos que hayan utilizado su antigua contraseña.',\n      readonly:\n        'Tu contraseña no se puede editar actualmente porque solo se puede acceder a través de la conexión de empresa.',\n      successMessage__set: 'Se ha establecido tu contraseña.',\n      successMessage__signOutOfOtherSessions: 'Se cerró la sesión de todos los demás dispositivos.',\n      successMessage__update: 'Tu contraseña ha sido actualizada.',\n      title__set: 'Configurar contraseña',\n      title__update: 'Cambiar contraseña',\n    },\n    phoneNumberPage: {\n      infoText: 'Se enviará un mensaje de texto con un enlace de verificación a este número de teléfono.',\n      removeResource: {\n        messageLine1: '{{identifier}} será eliminado de esta cuenta.',\n        messageLine2: 'Ya no podrás iniciar sesión con este número de teléfono.',\n        successMessage: '{{phoneNumber}} ha sido eliminado de tu cuenta.',\n        title: 'Eliminar número de teléfono',\n      },\n      successMessage: '{{identifier}} ha sido añadido a tu cuenta.',\n      title: 'Agregar el número de teléfono',\n      verifySubtitle: 'Ingresa el código de verificación enviado a {{identifier}}',\n      verifyTitle: 'Verificar número de teléfono',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Carga una imagen JPG, PNG, GIF o WEBP de menos de 10 MB',\n      imageFormDestructiveActionSubtitle: 'Eliminar la imagen',\n      imageFormSubtitle: 'Cargar imagen',\n      imageFormTitle: 'Imagen de perfil',\n      readonly: 'Tu información de perfil ha sido proporcionada por la conexión de empresa y no se puede editar.',\n      successMessage: 'Tu perfil ha sido actualizado.',\n      title: 'Actualizar perfil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Cerrar sesión en el dispositivo',\n        title: 'Dispositivos activos',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Inténtelo nuevamente',\n        actionLabel__reauthorize: 'Autorizar ahora',\n        destructiveActionTitle: 'Quitar',\n        primaryButton: 'Conectar cuenta',\n        subtitle__disconnected: 'Esta cuenta ha sido desconectada.',\n        subtitle__reauthorize:\n          'Los permisos requeridos han sido actualizados, y podrías experimentar limitaciones. Por favor, autoriza de nuevo esta aplicación para evitar cualquier problema',\n        title: 'Cuentas conectadas',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Eliminar cuenta',\n        title: 'Eliminar cuenta',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Eliminar correo electrónico',\n        detailsAction__nonPrimary: 'Establecer como primario',\n        detailsAction__primary: 'Completar verificación',\n        detailsAction__unverified: 'Completar verificación',\n        primaryButton: 'Agregar correo electrónico',\n        title: 'Correos electrónicos',\n      },\n      enterpriseAccountsSection: {\n        title: 'Cuentas de empresa',\n      },\n      headerTitle__account: 'Detalles de la cuenta',\n      headerTitle__security: 'Seguridad',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Regenerar',\n          headerTitle: 'Códigos de respaldo',\n          subtitle__regenerate:\n            'Obtén un nuevo conjunto de códigos de respaldo seguros. Los códigos de respaldo anteriores se eliminarán y no podrán ser utilizados.',\n          title__regenerate: 'Regenerar códigos de respaldo',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Establecer como predeterminado',\n          destructiveActionLabel: 'Eliminar número telefónico',\n        },\n        primaryButton: 'Añadir verificación de dos pasos',\n        title: 'Verificación de dos pasos',\n        totp: {\n          destructiveActionTitle: 'Eliminar',\n          headerTitle: 'Aplicación de autenticación',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Eliminar',\n        menuAction__rename: 'Renombrar',\n        primaryButton: 'Añadir llave de acceso',\n        title: 'Llaves de acceso',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Establecer contraseña ',\n        primaryButton__updatePassword: 'Cambiar contraseña',\n        title: 'Contraseña',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Eliminar número telefónico',\n        detailsAction__nonPrimary: 'Establecer como primario',\n        detailsAction__primary: 'Completar la verificación',\n        detailsAction__unverified: 'Completar la verificación',\n        primaryButton: 'Agregar un número telefónico',\n        title: 'Números telefónicos',\n      },\n      profileSection: {\n        primaryButton: 'Actualizar perfil',\n        title: 'Perfil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Definir nombre de usuario',\n        primaryButton__updateUsername: 'Cambiar nombre de usuario',\n        title: 'Nombre de usuario',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Quitar billetera',\n        detailsAction__nonPrimary: 'Establecer como primaria',\n        primaryButton: 'Conectar billetera',\n        title: 'Billeteras Web3',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Tu nombre de usuario ha sido actualizado.',\n      title__set: 'Actualizar nombre de usuario',\n      title__update: 'Actualizar nombre de usuario',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} será eliminado de esta cuenta.',\n        messageLine2: 'Ya no podrás iniciar sesión con esta billetera web3.',\n        successMessage: '{{web3Wallet}} ha sido eliminado de tu cuenta.',\n        title: 'Eliminar la billetera web3',\n      },\n      subtitle__availableWallets: 'Selecciona una billetera web3 para conectarse a tu cuenta.',\n      subtitle__unavailableWallets: 'No hay billeteras web3 disponibles.',\n      successMessage: 'La billetera ha sido agregada a tu cuenta.',\n      title: 'Añadir billetera web3',\n      web3WalletButtonsBlockButton: '{{provider|titleize}}',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: 'Iniciar sesión',\n      actionText: '¿Tienes una cuenta?',\n      formButton: 'Únete a la lista de espera',\n      subtitle: 'Ingresa tu correo electrónico y te avisaremos cuando tú lugar esté listo',\n      title: 'Únete a la lista de espera',\n    },\n    success: {\n      message: 'Serás redirigido pronto...',\n      subtitle: 'Nos pondremos en contacto contigo cuando tu lugar esté listo',\n      title: '¡Gracias por unirte a la lista de espera!',\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBACE;AAAA,EACF,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cACE;AAAA,MACF,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BACE;AAAA,IACF,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBAAoB;AAAA,IACpB,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCACE;AAAA,IACF,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BACE;AAAA,IACF,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cACE;AAAA,UACF,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UACE;AAAA,QACF,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UACE;AAAA,QACF,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}