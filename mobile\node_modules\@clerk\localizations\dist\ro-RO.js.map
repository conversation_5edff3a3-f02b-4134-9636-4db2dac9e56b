{"version": 3, "sources": ["../src/ro-RO.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const roRO: LocalizationResource = {\n  locale: 'ro-RO',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Înapoi',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Implicit',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Alt dispozitiv de imitație',\n  badge__primary: 'Principală',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Necesită acțiune',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Acest dispozitiv',\n  badge__unverified: 'Nedeclarat',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Dispozitiv de utilizator',\n  badge__you: 'Tu',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Creați o organizație',\n    invitePage: {\n      formButtonReset: 'Skip',\n    },\n    title: 'Creați o organizație',\n  },\n  dates: {\n    lastDay: \"Ieri la {{ date | timeString('en-US') }}\",\n    next6Days: \"{{ date | weekday('en-US','long') }} la {{ date | timeString('en-US') }}\",\n    nextDay: \"Mâine la {{ date | timeString('en-US') }}\",\n    numeric: \"{{ date | numeric('en-US') }}}\",\n    previous6Days: \"Ultimul {{ date | weekday('en-US','long') }} la {{ date | timeString('en-US') }}\",\n    sameDay: \"Astăzi la {{ date | timeString('en-US') }}\",\n  },\n  dividerText: 'sau',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Utilizați o altă metodă',\n  footerPageLink__help: 'Ajutor',\n  footerPageLink__privacy: 'Confidențialitate',\n  footerPageLink__terms: 'Termeni',\n  formButtonPrimary: 'Continuați',\n  formButtonPrimary__verify: 'Verify',\n  formFieldAction__forgotPassword: 'Ați uitat parola?',\n  formFieldError__matchingPasswords: 'Parolele se potrivesc.',\n  formFieldError__notMatchingPasswords: 'Parolele nu se potrivesc.',\n  formFieldError__verificationLinkExpired: 'Linkul de verificare a expirat. Vă rugăm să solicitați un nou link.',\n  formFieldHintText__optional: 'Opțional',\n  formFieldHintText__slug:\n    'Un slug este un ID lizibil de către om, care trebuie să fie unic. Este adesea utilizat în URL-uri.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Delete account',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses:\n    'Introduceți sau lipiți una sau mai multe adrese de e-mail, separate prin spații sau virgule',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: undefined,\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Activați invitațiile automate pentru acest domeniu',\n  formFieldLabel__backupCode: 'Cod de rezervă',\n  formFieldLabel__confirmDeletion: 'Confirmare',\n  formFieldLabel__confirmPassword: 'Confirmați parola',\n  formFieldLabel__currentPassword: 'Parola curentă',\n  formFieldLabel__emailAddress: 'Adresa de e-mail',\n  formFieldLabel__emailAddress_username: 'Adresa de e-mail sau numele de utilizator',\n  formFieldLabel__emailAddresses: 'Adrese de e-mail',\n  formFieldLabel__firstName: 'Prenume',\n  formFieldLabel__lastName: 'Nume',\n  formFieldLabel__newPassword: 'Parolă nouă',\n  formFieldLabel__organizationDomain: 'Domeniu',\n  formFieldLabel__organizationDomainDeletePending: 'Ștergeți invitațiile și sugestiile în așteptare',\n  formFieldLabel__organizationDomainEmailAddress: 'Adresa de e-mail de verificare',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Introduceți o adresă de e-mail sub acest domeniu pentru a primi un cod și pentru a verifica acest domeniu.',\n  formFieldLabel__organizationName: 'Numele organizației',\n  formFieldLabel__organizationSlug: 'Slug URL',\n  formFieldLabel__passkeyName: undefined,\n  formFieldLabel__password: 'Parola',\n  formFieldLabel__phoneNumber: 'Număr de telefon',\n  formFieldLabel__role: 'Rol',\n  formFieldLabel__signOutOfOtherSessions: 'Deconectați-vă de pe toate celelalte dispozitive',\n  formFieldLabel__username: 'Nume utilizator',\n  impersonationFab: {\n    action__signOut: 'Deconectați-vă',\n    title: 'Conectat ca {{identifier}}',\n  },\n  maintenanceMode: undefined,\n  membershipRole__admin: 'Admin',\n  membershipRole__basicMember: 'Membru',\n  membershipRole__guestMember: 'Invitat',\n  organizationList: {\n    action__createOrganization: 'Creați o organizație',\n    action__invitationAccept: 'Alăturați-vă',\n    action__suggestionsAccept: 'Cerere de aderare',\n    createOrganization: 'Creați o organizație',\n    invitationAcceptedLabel: 'S-a alăturat',\n    subtitle: 'pentru a continua la {{applicationName}}',\n    suggestionsAcceptedLabel: 'În curs de aprobare',\n    title: 'Selectați un cont',\n    titleWithoutPersonal: 'Selectați o organizație',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Invitații automate',\n    badge__automaticSuggestion: 'Sugestii automate',\n    badge__manualInvitation: 'Fără înscriere automată',\n    badge__unverified: 'Nedeclarat',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Adăugați domeniul de verificat. Utilizatorii cu adrese de e-mail la acest domeniu se pot alătura organizației în mod automat sau pot solicita să se alăture.',\n      title: 'Adăugați domeniul',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Invitațiile nu au putut fi trimise. Există deja invitații în așteptare pentru următoarele adrese de e-mail: {{email_addresses}}.',\n      formButtonPrimary__continue: 'Trimiteți invitații',\n      selectDropdown__role: 'Select role',\n      subtitle: 'Invitați noi membri în această organizație',\n      successMessage: 'Invitații trimise cu succes',\n      title: 'Invitați membri',\n    },\n    membersPage: {\n      action__invite: 'Invitați',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Îndepărtați membrul',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'S-a alăturat',\n        tableHeader__role: 'Rol',\n        tableHeader__user: 'Utilizator',\n      },\n      detailsTitle__emptyRow: 'Nu există membri de afișat',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Invitați utilizatorii prin conectarea unui domeniu de e-mail cu organizația dvs. Oricine se înscrie cu un domeniu de e-mail corespunzător va putea să se alăture organizației oricând.',\n          headerTitle: 'Invitații automate',\n          primaryButton: 'Gestionați domeniile verificate',\n        },\n        table__emptyRow: 'Nu există invitații de afișare',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Revocarea invitației',\n        tableHeader__invited: 'Invitat',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Utilizatorii care se înscriu cu un domeniu de e-mail corespunzător vor putea vedea o sugestie de a solicita să se alăture organizației dvs.',\n          headerTitle: 'Sugestii automate',\n          primaryButton: 'Gestionați domeniile verificate',\n        },\n        menuAction__approve: 'Aprobarea',\n        menuAction__reject: 'Respingeți',\n        tableHeader__requested: 'Accesul solicitat',\n        table__emptyRow: 'Nu există cereri de afișare',\n      },\n      start: {\n        headerTitle__invitations: 'Invitații',\n        headerTitle__members: 'Membri',\n        headerTitle__requests: 'Cereri',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Manage your organization.',\n      general: 'General',\n      members: 'Members',\n      title: 'Organization',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Introduceți {{organizationName}} mai jos pentru a continua.',\n          messageLine1: 'Sunteți sigur că doriți să ștergeți această organizație?',\n          messageLine2: 'Această acțiune este permanentă și ireversibilă.',\n          successMessage: 'Ați șters organizația.',\n          title: 'Ștergeți organizația',\n        },\n        leaveOrganization: {\n          actionDescription: 'Introduceți {{organizationName}} mai jos pentru a continua.',\n          messageLine1:\n            'Ești sigur că vrei să părăsești această organizație? Veți pierde accesul la această organizație și la aplicațiile sale.',\n          messageLine2: 'Această acțiune este permanentă și ireversibilă.',\n          successMessage: 'Ați părăsit organizația.',\n          title: 'Organizarea concediului',\n        },\n        title: 'Pericol',\n      },\n      domainSection: {\n        menuAction__manage: 'Manage',\n        menuAction__remove: 'Delete',\n        menuAction__verify: 'Verify',\n        primaryButton: 'Adăugați domeniul',\n        subtitle:\n          'Permiteți utilizatorilor să se alăture automat organizației sau să solicite aderarea pe baza unui domeniu de e-mail verificat.',\n        title: 'Domenii verificate',\n      },\n      successMessage: 'Organizația a fost actualizată.',\n      title: 'Profilul organizației',\n    },\n    removeDomainPage: {\n      messageLine1: 'Domeniul de e-mail {{domain}} va fi eliminat.',\n      messageLine2: 'După aceasta, utilizatorii nu vor mai putea să se alăture automat organizației.',\n      successMessage: '{{domain}} a fost eliminat.',\n      title: 'Înlăturați domeniul',\n    },\n    start: {\n      headerTitle__general: 'General',\n      headerTitle__members: 'Membri',\n      profileSection: {\n        primaryButton: undefined,\n        title: 'Organization Profile',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Eliminarea acestui domeniu va afecta utilizatorii invitați.',\n        removeDomainActionLabel__remove: 'Înlăturați domeniul',\n        removeDomainSubtitle: 'Eliminați acest domeniu din domeniile verificate',\n        removeDomainTitle: 'Înlăturați domeniul',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Utilizatorii sunt invitați automat să se alăture organizației atunci când se înscriu și se pot alătura oricând.',\n        automaticInvitationOption__label: 'Invitații automate',\n        automaticSuggestionOption__description:\n          'Utilizatorii primesc o sugestie de a solicita aderarea, dar trebuie să fie aprobați de un administrator înainte de a se putea alătura organizației.',\n        automaticSuggestionOption__label: 'Sugestii automate',\n        calloutInfoLabel: 'Schimbarea modului de înscriere va afecta doar utilizatorii noi.',\n        calloutInvitationCountLabel: 'Invitații în așteptare trimise utilizatorilor: {{count}}',\n        calloutSuggestionCountLabel: 'Sugestii în așteptare trimise utilizatorilor: {{count}}',\n        manualInvitationOption__description: 'Utilizatorii pot fi invitați doar manual în organizație.',\n        manualInvitationOption__label: 'Fără înscriere automată',\n        subtitle: 'Alegeți modul în care utilizatorii din acest domeniu se pot alătura organizației.',\n      },\n      start: {\n        headerTitle__danger: 'Pericol',\n        headerTitle__enrollment: 'Opțiuni de înscriere',\n      },\n      subtitle: 'Domeniul {{domain}} este acum verificat. Continuați prin selectarea modului de înscriere.',\n      title: 'Update {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Introduceți codul de verificare trimis la adresa dvs. de e-mail',\n      formTitle: 'Cod de verificare',\n      resendButton: 'Nu ați primit un cod? Trimiteți din nou',\n      subtitle: 'Domeniul {{domainName}} trebuie să fie verificat prin e-mail.',\n      subtitleVerificationCodeScreen:\n        'Un cod de verificare a fost trimis la {{emailAddress}}. Introduceți codul pentru a continua.',\n      title: 'Verifică domeniul',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Creați o organizație',\n    action__invitationAccept: 'Alăturați-vă',\n    action__manageOrganization: 'Gestionați organizația',\n    action__suggestionsAccept: 'Cerere de aderare',\n    notSelected: 'Nici o organizație selectată',\n    personalWorkspace: 'Cont personal',\n    suggestionsAcceptedLabel: 'În curs de aprobare',\n  },\n  paginationButton__next: 'Următorul',\n  paginationButton__previous: 'Anterior',\n  paginationRowText__displaying: 'Afișarea',\n  paginationRowText__of: 'de',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Add account',\n      action__signOutAll: 'Sign out of all accounts',\n      subtitle: 'Select the account with which you wish to continue.',\n      title: 'Choose an account',\n    },\n    alternativeMethods: {\n      actionLink: 'Obțineți ajutor',\n      actionText: 'Don’t have any of these?',\n      blockButton__backupCode: 'Utilizați un cod de rezervă',\n      blockButton__emailCode: 'Codul de e-mail către {{identifier}}',\n      blockButton__emailLink: 'Trimiteți un link prin e-mail către {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Conectați-vă cu parola dvs',\n      blockButton__phoneCode: 'Trimiteți codul SMS la {{identifier}}',\n      blockButton__totp: 'Utilizați aplicația de autentificare',\n      getHelp: {\n        blockButton__emailSupport: 'Suport prin e-mail',\n        content:\n          'Dacă întâmpinați dificultăți la conectarea în contul dvs., trimiteți-ne un e-mail și vom lucra cu dvs. pentru a restabili accesul cât mai curând posibil.',\n        title: 'Obțineți ajutor',\n      },\n      subtitle: 'Facing issues? You can use any of these methods to sign in.',\n      title: 'Utilizați o altă metodă',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'pentru a continua la {{applicationName}}',\n      title: 'Introduceți un cod de rezervă',\n    },\n    emailCode: {\n      formTitle: 'Cod de verificare',\n      resendButton: 'Nu ați primit un cod? Trimiteți din nou',\n      subtitle: 'pentru a continua la {{applicationName}}',\n      title: 'Verifică-ți e-mailul',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: 'Reveniți la tab-ul inițial pentru a continua.',\n        title: 'Acest link de verificare a expirat',\n      },\n      failed: {\n        subtitle: 'Reveniți la tab-ul inițial pentru a continua.',\n        title: 'Acest link de verificare nu este valid',\n      },\n      formSubtitle: 'Folosiți link-ul de verificare trimis pe e-mailul dvs',\n      formTitle: 'Link de verificare',\n      loading: {\n        subtitle: 'Veți fi redirecționat în curând',\n        title: 'Conectarea...',\n      },\n      resendButton: 'Nu ați primit un link? Trimiteți din nou',\n      subtitle: 'pentru a continua la {{applicationName}}',\n      title: 'Verifică-ți e-mailul',\n      unusedTab: {\n        title: 'Puteți închide această filă',\n      },\n      verified: {\n        subtitle: 'Veți fi redirecționat în curând',\n        title: 'Conectat cu succes',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Reveniți la tab-ul inițial pentru a continua',\n        subtitleNewTab: 'Reveniți la tab-ul nou deschis pentru a continua',\n        titleNewTab: 'Conectat pe altă filă',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Resetarea codului de parolă',\n      resendButton: 'Nu ați primit un cod? Trimiteți din nou',\n      subtitle: 'to reset your password',\n      subtitle_email: 'First, enter the code sent to your email ID',\n      subtitle_phone: 'First, enter the code sent to your phone',\n      title: 'Reset password',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Resetați-vă parola',\n      label__alternativeMethods: 'Sau, conectați-vă cu o altă metodă.',\n      title: 'Ați uitat parola?',\n    },\n    noAvailableMethods: {\n      message: 'Nu se poate continua autentificarea. Nu există niciun factor de autentificare disponibil.',\n      subtitle: 'S-a produs o eroare',\n      title: 'Nu se poate autentifica',\n    },\n    passkey: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Utilizați o altă metodă',\n      subtitle: 'pentru a continua la {{applicationName}}',\n      title: 'Introduceți parola dvs',\n    },\n    passwordPwned: {\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: 'Cod de verificare',\n      resendButton: 'Nu ați primit un cod? Trimiteți din nou',\n      subtitle: 'pentru a continua la {{applicationName}}',\n      title: 'Verificarea telefonului dvs',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Cod de verificare',\n      resendButton: 'Nu ați primit un cod? Trimiteți din nou',\n      subtitle: undefined,\n      title: 'Verificarea telefonului dvs',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Resetare parolă',\n      requiredMessage:\n        'Există deja un cont cu o adresă de e-mail neverificată. Vă rugăm să vă resetați parola pentru securitate.',\n      successMessage: 'Parola dvs. a fost schimbată cu succes. Vă rugăm să așteptați un moment.',\n      title: 'Resetare parolă',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Trebuie să vă verificăm identitatea înainte de a vă reseta parola.',\n    },\n    start: {\n      actionLink: 'Înscrieți-vă',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Utilizați e-mailul',\n      actionLink__use_email_username: 'Utilizați e-mail sau nume de utilizator',\n      actionLink__use_passkey: undefined,\n      actionLink__use_phone: 'Utilizați telefonul',\n      actionLink__use_username: 'Utilizați numele de utilizator',\n      actionText: 'Nu aveți cont?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'pentru a continua la {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Conectați-vă',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Cod de verificare',\n      subtitle: undefined,\n      title: 'Verificare în două etape',\n    },\n  },\n  signInEnterPasswordTitle: 'Introduceți parola dvs',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Conectați-vă',\n      actionText: 'Aveți un cont?',\n      subtitle: 'pentru a continua la {{applicationName}}',\n      title: 'Completați câmpurile lipsă',\n    },\n    emailCode: {\n      formSubtitle: 'Introduceți codul de verificare trimis la adresa dvs. de e-mail',\n      formTitle: 'Cod de verificare',\n      resendButton: 'Nu ați primit un cod? Trimiteți din nou',\n      subtitle: 'pentru a continua la {{applicationName}}',\n      title: 'Verificați-vă e-mailul',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: 'Utilizați link-ul de verificare trimis la adresa dvs. de e-mail',\n      formTitle: 'Link de verificare',\n      loading: {\n        title: 'Se crează contul...',\n      },\n      resendButton: 'Nu ați primit un link? Trimiteți din nou',\n      subtitle: 'pentru a continua la {{applicationName}}',\n      title: 'Verificați-vă e-mailul',\n      verified: {\n        title: 'Înregistrat cu succes',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Reveniți la tab-ul nou deschis pentru a continua',\n        subtitleNewTab: 'Reveniți la tab-ul anterior pentru a continua',\n        title: 'E-mail verificat cu succes',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Introduceți codul de verificare trimis la numărul dvs. de telefon',\n      formTitle: 'Cod de verificare',\n      resendButton: 'Nu ați primit un cod? Trimiteți din nou',\n      subtitle: 'pentru a continua la {{applicationName}}',\n      title: 'Verificarea telefonului dvs',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Conectați-vă',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: 'Aveți un cont?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'pentru a continua la {{applicationName}}',\n      subtitleCombined: 'pentru a continua la {{applicationName}}',\n      title: 'Creați-vă un cont',\n      titleCombined: 'Creați-vă un cont',\n    },\n  },\n  socialButtonsBlockButton: 'Continuați cu {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'Înscrierea a eșuat din cauza unor validări de securitate nereușite. Vă rugăm să reîmprospătați pagina pentru a încerca din nou sau contactați serviciul de asistență pentru mai multă asistență.',\n    captcha_unavailable:\n      'Înscrierea a eșuat din cauza unei validări nereușite a robotului. Vă rugăm să reîmprospătați pagina pentru a încerca din nou sau contactați serviciul de asistență pentru mai multă asistență.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: 'Nu am putut găsi un cont cu aceste detalii.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'Adresa de e-mail trebuie să fie o adresă de e-mail validă.',\n    form_param_format_invalid__phone_number: 'Phone number must be in a valid international format',\n    form_param_max_length_exceeded__first_name: 'Prenumele nu trebuie să depășească 256 de caractere.',\n    form_param_max_length_exceeded__last_name: 'Numele de familie nu trebuie să depășească 256 de caractere.',\n    form_param_max_length_exceeded__name: 'Numele nu trebuie să depășească 256 de caractere.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: 'Parola dvs. nu este suficient de puternică.',\n    form_password_pwned:\n      'Această parolă a fost descoperită ca parte a unei încălcări și nu poate fi utilizată, vă rugăm să încercați o altă parolă.',\n    form_password_pwned__sign_in: undefined,\n    form_password_size_in_bytes_exceeded:\n      'Parola dvs. a depășit numărul maxim de octeți permis, vă rugăm să o scurtați sau să eliminați unele caractere speciale.',\n    form_password_validation_failed: 'Parolă incorectă',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: 'Nu vă puteți șterge ultima identificare.',\n    not_allowed_access:\n      \"Adresa de e-mail sau numărul de telefon nu este permis pentru înregistrare. Acest lucru poate fi datorat utilizării '+', '=', '#' sau '.' în adresa dvs. de e-mail, utilizării unui domeniu asociat cu un serviciu de e-mail temporar sau unei excluziuni explicite.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: 'mai puțin de {{length}} caractere',\n      minimumLength: '{{length}} sau mai multe caractere',\n      requireLowercase: 'o literă minusculă',\n      requireNumbers: 'un număr',\n      requireSpecialCharacter: 'un caracter special',\n      requireUppercase: 'o literă majusculă',\n      sentencePrefix: 'Parola dvs. trebuie să conțină',\n    },\n    phone_number_exists: 'Acest număr de telefon este ocupat. Vă rugăm să încercați altul.',\n    session_exists: 'Sunteți deja conectat.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger:\n        'Parola dvs. funcționează, dar ar putea fi mai puternică. Încercați să adăugați mai multe caractere.',\n      goodPassword: 'Parola dvs. îndeplinește toate cerințele necesare.',\n      notEnough: 'Parola dvs. nu este suficient de puternică.',\n      suggestions: {\n        allUppercase: 'Scrieți cu majuscule unele litere, dar nu toate literele.',\n        anotherWord: 'Adăugați mai multe cuvinte care sunt mai puțin frecvente.',\n        associatedYears: 'Evitați anii care vă sunt asociați.',\n        capitalization: 'Scrieți cu majusculă mai mult decât prima literă.',\n        dates: 'Evitați datele și anii care vă sunt asociate.',\n        l33t: 'Evitați înlocuirile previzibile de litere, cum ar fi \"@\" pentru \"a\".',\n        longerKeyboardPattern:\n          'Utilizați modele de tastatură mai lungi și schimbați direcția de tastare de mai multe ori.',\n        noNeed: 'Puteți crea parole puternice fără a folosi simboluri, numere sau litere majuscule.',\n        pwned: 'Dacă folosiți această parolă în altă parte, ar trebui să o schimbați.',\n        recentYears: 'Evitați ultimii ani.',\n        repeated: 'Evitați cuvintele și caracterele repetate.',\n        reverseWords: 'Evitați ortografia inversă a cuvintelor uzuale.',\n        sequences: 'Evitați secvențele de caractere comune.',\n        useWords: 'Folosiți mai multe cuvinte, dar evitați frazele comune.',\n      },\n      warnings: {\n        common: 'Aceasta este o parolă folosită în mod obișnuit.',\n        commonNames: 'Numele comune și numele de familie sunt ușor de ghicit.',\n        dates: 'Datele sunt ușor de ghicit.',\n        extendedRepeat: 'Modelele de caractere repetate, cum ar fi \"abcabcabc\", sunt ușor de ghicit.',\n        keyPattern: 'Modelele scurte de tastatură sunt ușor de ghicit.',\n        namesByThemselves: 'Numele sau prenumele simple sunt ușor de ghicit.',\n        pwned: 'Parola dvs. a fost expusă în urma unei încălcări a securității datelor pe internet.',\n        recentYears: 'Ultimii ani sunt ușor de ghicit.',\n        sequences: 'Secvențele de caractere comune, cum ar fi \"abc\", sunt ușor de ghicit.',\n        similarToCommon: 'Acest lucru este similar cu o parolă folosită în mod obișnuit.',\n        simpleRepeat: 'Caracterele repetate, cum ar fi \"aaa\", sunt ușor de ghicit.',\n        straightRow: 'Rândurile drepte de taste de pe tastatură sunt ușor de ghicit.',\n        topHundred: 'Aceasta este o parolă utilizată frecvent.',\n        topTen: 'Aceasta este o parolă foarte utilizată.',\n        userInputs: 'Nu trebuie să existe date personale sau legate de pagini.',\n        wordByItself: 'Cuvintele simple sunt ușor de ghicit.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Adăugați un cont',\n    action__manageAccount: 'Gestionați contul',\n    action__signOut: 'Deconectați-vă',\n    action__signOutAll: 'Deconectați-vă din toate conturile',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Copiat!',\n      actionLabel__copy: 'Copiați toate',\n      actionLabel__download: 'Descarcă .txt',\n      actionLabel__print: 'Imprimare',\n      infoText1: 'Codurile de rezervă vor fi activate pentru acest cont.',\n      infoText2:\n        'Păstrați codurile de rezervă în secret și păstrați-le în siguranță. Puteți regenera codurile de rezervă dacă bănuiți că acestea au fost compromise.',\n      subtitle__codelist: 'Păstrați-le în siguranță și păstrați-le în secret.',\n      successMessage:\n        'Codurile de rezervă sunt acum activate. Puteți utiliza unul dintre acestea pentru a vă conecta la contul dvs., dacă pierdeți accesul la dispozitivul de autentificare. Fiecare cod poate fi utilizat o singură dată.',\n      successSubtitle:\n        'Puteți utiliza unul dintre acestea pentru a vă conecta la contul dvs., dacă pierdeți accesul la dispozitivul de autentificare.',\n      title: 'Adăugați verificarea codului de rezervă',\n      title__codelist: 'Coduri de rezervă',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Selectați un furnizor pentru a vă conecta contul.',\n      formHint__noAccounts: 'Nu există furnizori de conturi externe disponibili.',\n      removeResource: {\n        messageLine1: '{{identifier}} va fi eliminat din acest cont.',\n        messageLine2:\n          'Nu veți mai putea utiliza acest cont conectat, iar toate funcțiile dependente nu vor mai funcționa.',\n        successMessage: '{{connectedAccount}} a fost eliminat din contul dumneavoastră.',\n        title: 'Înlăturați contul conectat',\n      },\n      socialButtonsBlockButton: 'Conectează contul {{provider|titleize}}',\n      successMessage: 'Furnizorul a fost adăugat în contul dvs',\n      title: 'Adăugați un cont conectat',\n    },\n    deletePage: {\n      actionDescription: 'Introduceți Delete account (Ștergeți contul) mai jos pentru a continua.',\n      confirm: 'Ștergeți contul',\n      messageLine1: 'Sunteți sigur că doriți să vă ștergeți contul?',\n      messageLine2: 'Această acțiune este permanentă și ireversibilă.',\n      title: 'Ștergeți contul',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Un e-mail conținând un cod de verificare va fi trimis la această adresă de e-mail.',\n        formSubtitle: 'Introduceți codul de verificare trimis la {{identifier}}',\n        formTitle: 'Cod de verificare',\n        resendButton: 'Nu ați primit un cod? Trimiteți din nou',\n        successMessage: 'E-mailul {{identifier}} a fost adăugat în contul dvs.',\n      },\n      emailLink: {\n        formHint: 'La această adresă de e-mail va fi trimis un e-mail conținând un link de verificare.',\n        formSubtitle: 'Faceți clic pe link-ul de verificare din e-mailul trimis către {{identifier}}',\n        formTitle: 'Link de verificare',\n        resendButton: 'Nu ați primit un link? Trimiteți din nou',\n        successMessage: 'E-mailul {{identifier}} a fost adăugat în contul dvs.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} va fi eliminat din acest cont.',\n        messageLine2: 'Nu veți mai putea să vă conectați folosind această adresă de e-mail.',\n        successMessage: '{{emailAddress}} a fost eliminat din contul dvs.',\n        title: 'Eliminați adresa de e-mail',\n      },\n      title: 'Adăugați adresa de e-mail',\n      verifyTitle: 'Verify email address',\n    },\n    formButtonPrimary__add: 'Add',\n    formButtonPrimary__continue: 'Continuați',\n    formButtonPrimary__finish: 'Finalizare',\n    formButtonPrimary__remove: 'Remove',\n    formButtonPrimary__save: 'Save',\n    formButtonReset: 'Anulează',\n    mfaPage: {\n      formHint: 'Selectați o metodă de adăugat.',\n      title: 'Adăugați verificarea în doi pași',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Use existing number',\n      primaryButton__addPhoneNumber: 'Adăugați un număr de telefon',\n      removeResource: {\n        messageLine1: '{{identifier}} nu va mai primi coduri de verificare atunci când se conectează.',\n        messageLine2:\n          'Este posibil ca contul dumneavoastră să nu fie la fel de sigur. Sunteți sigur că doriți să continuați?',\n        successMessage: 'Verificarea în doi pași a codului SMS a fost eliminată pentru {{mfaPhoneCode}}',\n        title: 'Eliminarea verificării în două etape',\n      },\n      subtitle__availablePhoneNumbers:\n        'Selectați un număr de telefon pentru a vă înregistra pentru verificarea în doi pași a codului SMS.',\n      subtitle__unavailablePhoneNumbers:\n        'Nu există numere de telefon disponibile pentru a vă înregistra pentru verificarea în doi pași prin cod SMS.',\n      successMessage1:\n        'When signing in, you will need to enter a verification code sent to this phone number as an additional step.',\n      successMessage2:\n        'Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.',\n      successTitle: 'SMS code verification enabled',\n      title: 'Adăugați verificarea codului SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Scanați în schimb codul QR',\n        buttonUnableToScan__nonPrimary: 'Nu puteți scana codul QR?',\n        infoText__ableToScan:\n          'Configurați o nouă metodă de conectare în aplicația de autentificare și scanați următorul cod QR pentru a o lega de contul dvs.',\n        infoText__unableToScan:\n          'Configurați o nouă metodă de conectare în autentificatorul dvs. și introduceți cheia furnizată mai jos.',\n        inputLabel__unableToScan1:\n          'Asigurați-vă că este activată opțiunea Parole pe bază de timp sau Parole unice, apoi finalizați conectarea contului dvs.',\n        inputLabel__unableToScan2:\n          'Alternativ, dacă autentificatorul dvs. acceptă URI-urile TOTP, puteți copia și URI-ul complet.',\n      },\n      removeResource: {\n        messageLine1: 'Codurile de verificare de la acest autentificator nu vor mai fi necesare la autentificare.',\n        messageLine2:\n          'Este posibil ca contul dumneavoastră să nu fie la fel de sigur. Sunteți sigur că doriți să continuați?',\n        successMessage: 'Verificarea în doi pași prin intermediul aplicației Authenticator a fost eliminată.',\n        title: 'Eliminarea verificării în două etape',\n      },\n      successMessage:\n        'Verificarea în doi pași este acum activată. Când vă conectați, va trebui să introduceți un cod de verificare de la acest autentificator ca pas suplimentar.',\n      title: 'Adăugați aplicația de autentificare',\n      verifySubtitle: 'Introduceți codul de verificare generat de autentificatorul dvs',\n      verifyTitle: 'Cod de verificare',\n    },\n    mobileButton__menu: 'Meniu',\n    navbar: {\n      account: 'Profile',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Manage your account info.',\n      security: 'Security',\n      title: 'Account',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: undefined,\n        title: undefined,\n      },\n      subtitle__rename: undefined,\n      title__rename: undefined,\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'It is recommended to sign out of all other devices which may have used your old password.',\n      readonly:\n        'În prezent, parola dvs. nu poate fi modificată, deoarece vă puteți conecta numai prin intermediul conexiunii întreprinderii.',\n      successMessage__set: 'Parola dvs. a fost setată.',\n      successMessage__signOutOfOtherSessions: 'Toate celelalte dispozitive au fost deconectate.',\n      successMessage__update: 'Parola dvs. a fost actualizată.',\n      title__set: 'Setați parola',\n      title__update: 'Modificați parola',\n    },\n    phoneNumberPage: {\n      infoText: 'La acest număr de telefon va fi trimis un mesaj text conținând un link de verificare.',\n      removeResource: {\n        messageLine1: '{{identifier}} va fi eliminat din acest cont.',\n        messageLine2: 'Nu veți mai putea să vă conectați folosind acest număr de telefon.',\n        successMessage: '{{phoneNumber}} a fost eliminat din contul dvs.',\n        title: 'Eliminați numărul de telefon',\n      },\n      successMessage: '{{identifier}} a fost adăugat în contul dumneavoastră.',\n      title: 'Adăugați numărul de telefon',\n      verifySubtitle: 'Enter the verification code sent to {{identifier}}',\n      verifyTitle: 'Verify phone number',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Încărcați o imagine JPG, PNG, GIF sau WEBP mai mică de 10 MB',\n      imageFormDestructiveActionSubtitle: 'Eliminați imaginea',\n      imageFormSubtitle: 'Încărcați imaginea',\n      imageFormTitle: 'Imagine de profil',\n      readonly:\n        'Informațiile din profilul dvs. au fost furnizate de către conexiunea cu compania și nu pot fi modificate.',\n      successMessage: 'Profilul dvs. a fost actualizat.',\n      title: 'Actualizarea profilului',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Deconectați-vă de la dispozitiv',\n        title: 'Dispozitive active',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Încearcă din nou',\n        actionLabel__reauthorize: 'Autorizați acum',\n        destructiveActionTitle: 'Eliminați',\n        primaryButton: 'Conectați-vă contul',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues',\n        title: 'Conturi conectate',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Ștergeți contul',\n        title: 'Pericol',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Eliminați adresa de e-mail',\n        detailsAction__nonPrimary: 'Setați ca principală',\n        detailsAction__primary: 'Verificare completă',\n        detailsAction__unverified: 'Verifică adresa de e-mail',\n        primaryButton: 'Adăugați o adresă de e-mail',\n        title: 'Adrese de e-mail',\n      },\n      enterpriseAccountsSection: {\n        title: 'Conturi de companie',\n      },\n      headerTitle__account: 'Cont',\n      headerTitle__security: 'Securitate',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Regenerarea codurilor',\n          headerTitle: 'Coduri de rezervă',\n          subtitle__regenerate:\n            'Obțineți un set nou de coduri de rezervă securizate. Codurile de rezervă anterioare vor fi șterse și nu vor mai putea fi utilizate.',\n          title__regenerate: 'Regenerarea codurilor de rezervă',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Setat ca implicit',\n          destructiveActionLabel: 'Eliminați numărul de telefon',\n        },\n        primaryButton: 'Adăugați verificarea în doi pași',\n        title: 'Verificare în două etape',\n        totp: {\n          destructiveActionTitle: 'Eliminați',\n          headerTitle: 'Aplicația Authenticator',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: undefined,\n        menuAction__rename: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Setați parola',\n        primaryButton__updatePassword: 'Modificați parola',\n        title: 'Parola',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Eliminați numărul de telefon',\n        detailsAction__nonPrimary: 'Setați ca principal',\n        detailsAction__primary: 'Verificare completă',\n        detailsAction__unverified: 'Verificați numărul de telefon',\n        primaryButton: 'Adăugați un număr de telefon',\n        title: 'Numere de telefon',\n      },\n      profileSection: {\n        primaryButton: undefined,\n        title: 'Profil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Setați numele de utilizator',\n        primaryButton__updateUsername: 'Schimbă numele de utilizator',\n        title: 'Nume utilizator',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Scoateți portofelul',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Portofele Web3',\n        title: 'Portofele Web3',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Numele dvs. de utilizator a fost actualizat.',\n      title__set: 'Actualizați numele de utilizator',\n      title__update: 'Actualizați numele de utilizator',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} va fi eliminat din acest cont.',\n        messageLine2: 'Nu veți mai putea să vă conectați folosind acest portofel web3.',\n        successMessage: '{{web3Wallet}} a fost eliminat din contul dumneavoastră.',\n        title: 'Îndepărtați portofelul web3',\n      },\n      subtitle__availableWallets: 'Selectați un portofel web3 pentru a vă conecta la cont.',\n      subtitle__unavailableWallets: 'Nu există portofele web3 disponibile.',\n      successMessage: 'Portofelul a fost adăugat în contul dumneavoastră.',\n      title: 'Adăugați portofelul web3',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBACE;AAAA,EACF,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBACE;AAAA,MACF,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBACE;AAAA,MACF,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBACE;AAAA,QACF,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UACE;AAAA,MACF,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}