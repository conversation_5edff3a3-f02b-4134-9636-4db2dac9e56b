{"version": 3, "sources": ["../src/sv-SE.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const svSE: LocalizationResource = {\n  locale: 'sv-SE',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Tillbaka',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Standard',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Annans imitatörenhet',\n  badge__primary: 'Primär',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Kräver åtgärd',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Den här enheten',\n  badge__unverified: 'Overifierad',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Användarens enhet',\n  badge__you: 'Du',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Skapa organisation',\n    invitePage: {\n      formButtonReset: 'Hoppa över',\n    },\n    title: 'Skapa organisation',\n  },\n  dates: {\n    lastDay: \"Igår klockan {{ date | timeString('sv-SE') }}\",\n    next6Days: \"{{ date | weekday('sv-SE','long') }} klockan {{ date | timeString('sv-SE') }}\",\n    nextDay: \"Imorgon klockan {{ date | timeString('sv-SE') }}\",\n    numeric: \"{{ date | numeric('sv-SE') }}\",\n    previous6Days: \"Senaste {{ date | weekday('sv-SE','long') }} klockan {{ date | timeString('sv-SE') }}\",\n    sameDay: \"Idag klockan {{ date | timeString('sv-SE') }}\",\n  },\n  dividerText: 'eller',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Använd en annan metod',\n  footerPageLink__help: 'Hjälp',\n  footerPageLink__privacy: 'Integritet',\n  footerPageLink__terms: 'Villkor',\n  formButtonPrimary: 'Fortsätt',\n  formButtonPrimary__verify: 'Verifiera',\n  formFieldAction__forgotPassword: 'Glömt lösenord?',\n  formFieldError__matchingPasswords: 'Lösenorden matchar.',\n  formFieldError__notMatchingPasswords: 'Lösenorden matchar inte.',\n  formFieldError__verificationLinkExpired: 'Verifieringslänken har löpt ut. Vänligen begär en ny länk.',\n  formFieldHintText__optional: 'Valfritt',\n  formFieldHintText__slug: 'En slug är ett läsbart ID som måste vara unikt. Det används ofta i URL:er.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: 'Ange din reservkod',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Radera konto',\n  formFieldInputPlaceholder__emailAddress: 'Ange din e-postadress',\n  formFieldInputPlaceholder__emailAddress_username: 'Ange din e-postadress eller ditt användarnamn',\n  formFieldInputPlaceholder__emailAddresses:\n    'Ange eller klistra in en eller flera e-postadresser, separerade med mellanslag eller kommatecken',\n  formFieldInputPlaceholder__firstName: 'Ange ditt förnamn',\n  formFieldInputPlaceholder__lastName: 'Ange ditt efternamn',\n  formFieldInputPlaceholder__organizationDomain: 'Ange organisationsdomän',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: 'Ange e-postadress för verifiering',\n  formFieldInputPlaceholder__organizationName: 'Ange organisationsnamn',\n  formFieldInputPlaceholder__organizationSlug: 'min-organisation',\n  formFieldInputPlaceholder__password: 'Ange ditt lösenord',\n  formFieldInputPlaceholder__phoneNumber: 'Ange ditt telefonnummer',\n  formFieldInputPlaceholder__username: 'Ange ditt användarnamn',\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Aktivera automatiska inbjudningar för denna domän',\n  formFieldLabel__backupCode: 'Reserv-kod',\n  formFieldLabel__confirmDeletion: 'Radera konto',\n  formFieldLabel__confirmPassword: 'Bekräfta lösenord',\n  formFieldLabel__currentPassword: 'Nuvarande lösenord',\n  formFieldLabel__emailAddress: 'E-postadress',\n  formFieldLabel__emailAddress_username: 'E-postadress eller användarnamn',\n  formFieldLabel__emailAddresses: 'E-postadresser',\n  formFieldLabel__firstName: 'Förnamn',\n  formFieldLabel__lastName: 'Efternamn',\n  formFieldLabel__newPassword: 'Nytt lösenord',\n  formFieldLabel__organizationDomain: 'Domän',\n  formFieldLabel__organizationDomainDeletePending: 'Ta bort väntande inbjudningar och förslag',\n  formFieldLabel__organizationDomainEmailAddress: 'Verifierings-e-postadress',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Ange en e-postadress under denna domän för att få en kod och verifiera denna domän.',\n  formFieldLabel__organizationName: 'Organisationsnamn',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Namn på passkey',\n  formFieldLabel__password: 'Lösenord',\n  formFieldLabel__phoneNumber: 'Telefonnummer',\n  formFieldLabel__role: 'Roll',\n  formFieldLabel__signOutOfOtherSessions: 'Logga ut från alla andra enheter',\n  formFieldLabel__username: 'Användarnamn',\n  impersonationFab: {\n    action__signOut: 'Logga ut',\n    title: 'Inloggad som {{identifier}}',\n  },\n  maintenanceMode: 'Vi genomför för närvarande underhåll, men oroa dig inte, det bör inte ta mer än några minuter.',\n  membershipRole__admin: 'Admin',\n  membershipRole__basicMember: 'Medlem',\n  membershipRole__guestMember: 'Gäst',\n  organizationList: {\n    action__createOrganization: 'Skapa organisation',\n    action__invitationAccept: 'Gå med',\n    action__suggestionsAccept: 'Be om att gå med',\n    createOrganization: 'Skapa Organisation',\n    invitationAcceptedLabel: 'Gått med',\n    subtitle: 'för att fortsätta till {{applicationName}}',\n    suggestionsAcceptedLabel: 'Väntar godkännande',\n    title: 'Välj ett konto',\n    titleWithoutPersonal: 'Välj en organisation',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Automatiska inbjudningar',\n    badge__automaticSuggestion: 'Automatiska förslag',\n    badge__manualInvitation: 'Ingen automatisk registrering',\n    badge__unverified: 'Overifierad',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Lägg till domänen för att verifiera. Användare med e-postadresser i denna domän kan gå med i organisationen automatiskt eller begära att få gå med.',\n      title: 'Lägg till domän',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed: 'Inbjudningarna kunde inte skickas. Åtgärda följande och försök igen:',\n      formButtonPrimary__continue: 'Skicka inbjudningar',\n      selectDropdown__role: 'Välj roll',\n      subtitle: 'Bjud in nya medlemmar till denna organisation',\n      successMessage: 'Inbjudningar skickade',\n      title: 'Bjud in medlemmar',\n    },\n    membersPage: {\n      action__invite: 'Bjud in',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Ta bort medlem',\n        tableHeader__actions: 'Åtgärder',\n        tableHeader__joined: 'Gick med',\n        tableHeader__role: 'Roll',\n        tableHeader__user: 'Användare',\n      },\n      detailsTitle__emptyRow: 'Inga medlemmar att visa',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Bjud in användare genom att koppla en e-postdomän till din organisation. Alla som registrerar sig med en matchande e-postdomän kommer att kunna gå med i organisationen när som helst.',\n          headerTitle: 'Automatiska inbjudningar',\n          primaryButton: 'Hantera verifierade domäner',\n        },\n        table__emptyRow: 'Inga inbjudningar att visa',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Återkalla inbjudan',\n        tableHeader__invited: 'Inbjudna',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Användare som registrerar sig med en matchande e-postdomän kommer att kunna se ett förslag om att begära att gå med i din organisation.',\n          headerTitle: 'Automatiska förslag',\n          primaryButton: 'Hantera verifierade domäner',\n        },\n        menuAction__approve: 'Godkänn',\n        menuAction__reject: 'Avvisa',\n        tableHeader__requested: 'Begärd åtkomst',\n        table__emptyRow: 'Inga förfrågningar att visa',\n      },\n      start: {\n        headerTitle__invitations: 'Inbjudningar',\n        headerTitle__members: 'Medlemmar',\n        headerTitle__requests: 'Förfrågningar',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Hantera din organisation.',\n      general: 'Allmänna inställningar',\n      members: 'Medlemar',\n      title: 'Organisation',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Skriv \"{{organizationName}}\" nedan för att fortsätta.',\n          messageLine1: 'Är du säker på att du vill radera denna organisation?',\n          messageLine2: 'Denna åtgärd är permanent och kan inte ångras.',\n          successMessage: 'Du har raderat organisationen.',\n          title: 'Radera organisation',\n        },\n        leaveOrganization: {\n          actionDescription: 'Skriv \"{{organizationName}}\" nedan för att fortsätta.',\n          messageLine1:\n            'Är du säker på att du vill lämna denna organisation? Du kommer att förlora åtkomst till organisationen och dess applikationer.',\n          messageLine2: 'Denna åtgärd är permanent och oåterkallelig.',\n          successMessage: 'Du har lämnat organisationen.',\n          title: 'Lämna organisation',\n        },\n        title: 'Farligt',\n      },\n      domainSection: {\n        menuAction__manage: 'Hantera',\n        menuAction__remove: 'Radera',\n        menuAction__verify: 'Verifiera',\n        primaryButton: 'Lägg till domän',\n        subtitle:\n          'Tillåt användare att gå med i organisationen automatiskt eller begära att gå med baserat på en verifierad e-postdomän.',\n        title: 'Verifierade domäner',\n      },\n      successMessage: 'Organisationen har uppdaterats.',\n      title: 'Organisationsprofil',\n    },\n    removeDomainPage: {\n      messageLine1: 'E-postdomänen {{domain}} kommer att tas bort.',\n      messageLine2: 'Användare kommer inte att kunna gå med i organisationen automatiskt efter detta.',\n      successMessage: '{{domain}} har tagits bort.',\n      title: 'Ta bort domän',\n    },\n    start: {\n      headerTitle__general: 'Allmänna inställningar',\n      headerTitle__members: 'Medlemmar',\n      profileSection: {\n        primaryButton: 'Uppdatera profil',\n        title: 'Organisationsprofil',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Att ta bort denna domän kommer att påverka inbjudna användare.',\n        removeDomainActionLabel__remove: 'Ta bort domän',\n        removeDomainSubtitle: 'Ta bort denna domän från dina verifierade domäner',\n        removeDomainTitle: 'Ta bort domän',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Användare bjuds automatiskt in att gå med i organisationen när de registrerar sig och kan gå med när som helst.',\n        automaticInvitationOption__label: 'Automatiska inbjudningar',\n        automaticSuggestionOption__description:\n          'Användare får ett förslag om att begära att få gå med, men måste godkännas av en administratör innan de kan gå med i organisationen.',\n        automaticSuggestionOption__label: 'Automatiska förslag',\n        calloutInfoLabel: 'Att ändra registreringsläget kommer endast att påverka nya användare.',\n        calloutInvitationCountLabel: 'Väntande inbjudningar skickade till användare: {{count}}',\n        calloutSuggestionCountLabel: 'Väntande förslag skickade till användare: {{count}}',\n        manualInvitationOption__description: 'Användare kan endast bjudas in manuellt till organisationen.',\n        manualInvitationOption__label: 'Ingen automatisk registrering',\n        subtitle: 'Välj hur användare från denna domän kan gå med i organisationen.',\n      },\n      start: {\n        headerTitle__danger: 'Fara',\n        headerTitle__enrollment: 'Registreringsalternativ',\n      },\n      subtitle: 'Domänen {{domain}} är nu verifierad. Fortsätt genom att välja registreringsläge.',\n      title: 'Uppdatera {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Ange verifieringskoden som skickats till din e-postadress',\n      formTitle: 'Verifieringskod',\n      resendButton: 'Fick du inte koden? Skicka igen',\n      subtitle: 'Domänen {{domainName}} behöver verifieras via e-post.',\n      subtitleVerificationCodeScreen:\n        'En verifieringskod skickades till {{emailAddress}}. Ange koden för att fortsätta.',\n      title: 'Verifiera domän',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Skapa organisation',\n    action__invitationAccept: 'Join',\n    action__manageOrganization: 'Hantera organisation',\n    action__suggestionsAccept: 'Request to join',\n    notSelected: 'Ingen organisation vald',\n    personalWorkspace: 'Personligt Arbetsområde',\n    suggestionsAcceptedLabel: 'Pending approval',\n  },\n  paginationButton__next: 'Nästa',\n  paginationButton__previous: 'Föregående',\n  paginationRowText__displaying: 'Visar',\n  paginationRowText__of: 'av',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Få hjälp',\n      actionText: 'Har du inget av dessa?',\n      blockButton__backupCode: 'Använd en reservkod',\n      blockButton__emailCode: 'Skicka kod via e-post till {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Fortsätt med ditt lösenord',\n      blockButton__phoneCode: 'Skicka SMS-kod till {{identifier}}',\n      blockButton__totp: 'Använd din autentiseringsapp',\n      getHelp: {\n        blockButton__emailSupport: 'E-posta support',\n        content:\n          'Om du har problem med att verifiera ditt konto, maila oss så hjälper vi dig att återställa åtkomsten så snart som möjligt.',\n        title: 'Få hjälp',\n      },\n      subtitle: 'Har du problem? Du kan använda någon av dessa metoder för verifiering.',\n      title: 'Använd en annan metod',\n    },\n    backupCodeMfa: {\n      subtitle: 'Din reservkod är den du fick när du ställde in tvåstegsverifiering.',\n      title: 'Ange en reservkod',\n    },\n    emailCode: {\n      formTitle: 'Verifieringskod',\n      resendButton: 'Fick du ingen kod? Skicka igen',\n      subtitle: 'för att fortsätta till {{applicationName}}',\n      title: 'Kontrollera din e-post',\n    },\n    noAvailableMethods: {\n      message: 'Kan inte fortsätta med verifieringen. Det finns ingen tillgänglig autentiseringsfaktor.',\n      subtitle: 'Ett fel inträffade',\n      title: 'Kan inte verifiera ditt konto',\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Använd en annan metod',\n      subtitle: 'Ange lösenordet som är kopplat till ditt konto',\n      title: 'Ange ditt lösenord',\n    },\n    phoneCode: {\n      formTitle: 'Verifieringskod',\n      resendButton: 'Fick du ingen kod? Skicka igen',\n      subtitle: 'för att fortsätta till {{applicationName}}',\n      title: 'Kontrollera din telefon',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Verifieringskod',\n      resendButton: 'Fick du ingen kod? Skicka igen',\n      subtitle: 'För att fortsätta, vänligen ange verifieringskoden som skickats till din telefon',\n      title: 'Kontrollera din telefon',\n    },\n    totpMfa: {\n      formTitle: 'Verifieringskod',\n      subtitle: 'För att fortsätta, vänligen ange verifieringskoden som genererats av din autentiseringsapp',\n      title: 'Tvåstegsverifiering',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Lägg till konto',\n      action__signOutAll: 'Logga ut från alla konton',\n      subtitle: 'Välj det konto du vill fortsätta med',\n      title: 'Välj ett konto',\n    },\n    alternativeMethods: {\n      actionLink: 'Få hjälp',\n      actionText: 'Saknar du någon av dessa?',\n      blockButton__backupCode: 'Använd en reservkod',\n      blockButton__emailCode: 'Skicka kod till {{identifier}}',\n      blockButton__emailLink: 'Skicka länk till {{identifier}}',\n      blockButton__passkey: 'Använd din passkey',\n      blockButton__password: 'Logga in med ditt lösenord',\n      blockButton__phoneCode: 'Skicka kod till {{identifier}}',\n      blockButton__totp: 'Använd din autentiseringsapp',\n      getHelp: {\n        blockButton__emailSupport: 'E-post support',\n        content:\n          'Om du har problem med att logga in på ditt konto, kontakta oss via e-post så hjälper vi dig att återställa åtkomsten så snabbt som möjligt.',\n        title: 'Få hjälp',\n      },\n      subtitle: 'Har du problem? Du kan använda någon av dessa metoder för att logga in.',\n      title: 'Använd en annan metod',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'för att fortsätta till {{applicationName}}',\n      title: 'Ange en reservkod',\n    },\n    emailCode: {\n      formTitle: 'Verifieringskod',\n      resendButton: 'Skicka koden igen',\n      subtitle: 'för att fortsätta till {{applicationName}}',\n      title: 'Kontrollera din e-post',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'För att fortsätta, öppna verifieringslänken på enhet och webbläsare från vilken du startade inloggningen',\n        title: 'Verifieringslänken är ogiltig för denna enhet',\n      },\n      expired: {\n        subtitle: 'Återgå till ursprungliga fliken för att fortsätta.',\n        title: 'Denna verifieringslänk har upphört att gälla',\n      },\n      failed: {\n        subtitle: 'Återgå till ursprungliga fliken för att fortsätta.',\n        title: 'Denna verifieringslänk är ogiltig',\n      },\n      formSubtitle: 'Använd verifieringslänken som skickades till din e-postadress',\n      formTitle: 'Verifieringslänk',\n      loading: {\n        subtitle: 'Du kommer att omdirigeras snart',\n        title: 'Loggar in...',\n      },\n      resendButton: 'Skicka länken igen',\n      subtitle: 'för att fortsätta till {{applicationName}}',\n      title: 'Kontrollera din e-post',\n      unusedTab: {\n        title: 'Du kan stänga den här fliken',\n      },\n      verified: {\n        subtitle: 'Du kommer att omdirigeras snart',\n        title: 'Inloggningen lyckades',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Återgå till ursprungliga fliken för att fortsätta',\n        subtitleNewTab: 'Återgå till den nyligen öppnade fliken för att fortsätta',\n        titleNewTab: 'Loggade in på annan flik',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Återställ lösenordskod',\n      resendButton: 'Fick du inte en kod? Skicka igen',\n      subtitle: 'för att återställa ditt lösenord',\n      subtitle_email: 'Först, ange koden som skickats till din e-postadress',\n      subtitle_phone: 'Först, ange koden som skickats till din telefon',\n      title: 'Återställ lösenord',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Återställ ditt lösenord',\n      label__alternativeMethods: 'Eller, logga in med en annan metod',\n      title: 'Glömt lösenord?',\n    },\n    noAvailableMethods: {\n      message: 'Kan inte fortsätta med inloggning. Det finns ingen tillgänglig autentiseringsfaktor.',\n      subtitle: 'Ett fel inträffade',\n      title: 'Kan inte logga in',\n    },\n    passkey: {\n      subtitle:\n        'Att använda din passkey bekräftar att det är du. Din enhet kan be om ditt fingeravtryck, ansikte eller skärmlås.',\n      title: 'Använd din passkey',\n    },\n    password: {\n      actionLink: 'Använd en annan metod',\n      subtitle: 'för att fortsätta till {{applicationName}}',\n      title: 'Ange ditt lösenord',\n    },\n    passwordPwned: {\n      title: 'Lösenord är för osäkert',\n    },\n    phoneCode: {\n      formTitle: 'Verifieringskod',\n      resendButton: 'Skicka koden igen',\n      subtitle: 'för att fortsätta till {{applicationName}}',\n      title: 'Kolla din telefon',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Verifieringskod',\n      resendButton: 'Skicka koden igen',\n      subtitle: 'För att fortsätta, vänligen ange verifieringskoden som skickats till din telefon',\n      title: 'Kolla din telefon',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Återställ lösenord',\n      requiredMessage: 'Av säkerhetsskäl är det nödvändigt att återställa ditt lösenord.',\n      successMessage: 'Ditt lösenord har ändrats framgångsrikt. Loggar in dig, var god vänta en stund.',\n      title: 'Ange nytt lösenord',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Vi behöver verifiera din identitet innan vi återställer ditt lösenord.',\n    },\n    start: {\n      actionLink: 'Skapa konto',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Use email',\n      actionLink__use_email_username: 'Use email or username',\n      actionLink__use_passkey: 'Använd passkey istället',\n      actionLink__use_phone: 'Använd telefon',\n      actionLink__use_username: 'Använd användarnamn',\n      actionText: 'Har du inget konto?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'för att fortsätta till {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Logga in',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Verifieringskod',\n      subtitle: 'För att fortsätta, vänligen ange verifieringskoden som genereras av din autentiseringsapp',\n      title: 'Tvåstegsverifiering',\n    },\n  },\n  signInEnterPasswordTitle: 'Ange ditt lösenord',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Logga in',\n      actionText: 'Har du redan ett konto?',\n      subtitle: 'för att fortsätta till {{applicationName}}',\n      title: 'Fyll i nödvändiga fält',\n    },\n    emailCode: {\n      formSubtitle: 'Ange verifieringskoden som skickades till din e-postadress',\n      formTitle: 'Verifieringskod',\n      resendButton: 'Skicka koden igen',\n      subtitle: 'för att fortsätta till {{applicationName}}',\n      title: 'Verifiera din e-post',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'För att fortsätta, öppna verifieringslänken på enhet och webbläsare från vilken du startade inloggningen',\n        title: 'Verifieringslänken är ogiltig för denna enhet',\n      },\n      formSubtitle: 'Använd verifieringslänken som skickades till din e-postadress',\n      formTitle: 'Verifieringslänk',\n      loading: {\n        title: 'Registrerar...',\n      },\n      resendButton: 'Skicka länken igen',\n      subtitle: 'för att fortsätta till {{applicationName}}',\n      title: 'Verifiera din e-post',\n      verified: {\n        title: 'Registreringen lyckades',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Återgå till den nyligen öppnade fliken för att fortsätta',\n        subtitleNewTab: 'Återgå till föregående flik för att fortsätta',\n        title: 'E-posten har verifierats',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Jag godkänner {{ privacyPolicyLink || link(\"Integritetspolicyn\") }}',\n        label__onlyTermsOfService: 'Jag godkänner de {{ termsOfServiceLink || link(\"Allmänna villkoren\") }}',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Jag godkänner de {{ termsOfServiceLink || link(\"Allmänna villkoren\") }} och {{ privacyPolicyLink || link(\"Integritetspolicyn\") }}',\n      },\n      continue: {\n        subtitle: 'Vänligen läs och godkänn villkoren för att fortsätta',\n        title: 'Juridiskt samtycke',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Ange verifieringskoden som skickades till ditt telefonnummer',\n      formTitle: 'Verifieringskod',\n      resendButton: 'Skicka koden igen',\n      subtitle: 'för att fortsätta till {{applicationName}}',\n      title: 'Verifiera din telefon',\n    },\n    restrictedAccess: {\n      actionLink: 'Tillbaka till inloggning',\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: 'Åtkomst till denna app är begränsad och en inbjudan krävs för att registrera sig.',\n      subtitleWaitlist: undefined,\n      title: 'Begränsad åtkomst',\n    },\n    start: {\n      actionLink: 'Logga in',\n      actionLink__use_email: 'Använd e-post istället',\n      actionLink__use_phone: 'Använd telefon istället',\n      actionText: 'Har du redan ett konto?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'för att fortsätta till {{applicationName}}',\n      subtitleCombined: 'för att fortsätta till {{applicationName}}',\n      title: 'Skapa ditt konto',\n      titleCombined: 'Skapa ditt konto',\n    },\n  },\n  socialButtonsBlockButton: 'Fortsätt med {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: '{{email}} är redan medlem i organisationen.',\n    captcha_invalid:\n      'Registrering misslyckades på grund av säkerhetskontroller. Vänligen uppdatera sidan och försök igen eller kontakta supporten för mer hjälp.',\n    captcha_unavailable:\n      'Registrering misslyckades på grund av misslyckad bot-validering. Vänligen uppdatera sidan och försök igen eller kontakta supporten för mer hjälp.',\n    form_code_incorrect: 'Koden är felaktig',\n    form_identifier_exists__email_address: 'Denna e-postadress är taget. Vänligen prova ett annat.',\n    form_identifier_exists__phone_number: 'Detta telefonnummer är taget. Vänligen prova ett annat.',\n    form_identifier_exists__username: 'Detta användarnamn är taget. Vänligen prova ett annat.',\n    form_identifier_not_found: 'Vi kunde inte hitta ett konto med dessa uppgifter.',\n    form_param_format_invalid: 'Formatet är ogiltigt.',\n    form_param_format_invalid__email_address: 'E-postadressen måste vara en giltig e-postadress.',\n    form_param_format_invalid__phone_number: 'Telefonnumret måste vara i ett giltigt internationellt format.',\n    form_param_max_length_exceeded__first_name: 'Förnamnet får inte överskrida 256 tecken.',\n    form_param_max_length_exceeded__last_name: 'Efternamnet får inte överskrida 256 tecken.',\n    form_param_max_length_exceeded__name: 'Namnet får inte överskrida 256 tecken.',\n    form_param_nil: 'Parametern får inte vara tom.',\n    form_param_value_invalid: undefined,\n    form_password_incorrect: 'Lösenordet är felaktigt.',\n    form_password_length_too_short: 'Lösenordet är för kort.',\n    form_password_not_strong_enough: 'Ditt lösenord är inte tillräckligt starkt.',\n    form_password_pwned: 'Lösenordet har läckt i tidigare dataintrång.',\n    form_password_pwned__sign_in: 'Lösenordet har läckt, vänligen logga in för att ändra det.',\n    form_password_size_in_bytes_exceeded:\n      'Ditt lösenord har överskridit det maximala antalet tillåtna bytes, vänligen förkorta det eller ta bort några specialtecken.',\n    form_password_validation_failed: 'Felaktigt lösenord',\n    form_username_invalid_character: 'Användarnamnet innehåller ogiltiga tecken.',\n    form_username_invalid_length: 'Användarnamnets längd är ogiltig.',\n    identification_deletion_failed: 'Du kan inte ta bort din sista identifiering.',\n    not_allowed_access:\n      \"Adressen eller telefonnumret du använder för registrering är inte tillåtet. Detta kan bero på att du använder '+', '=', '#' eller '.' i din e-postadress, använder en domän som är kopplad till en tidsbegränsad e-posttjänst eller har ett explicit blockerat.\",\n    organization_domain_blocked: 'Domänen är blockerad.',\n    organization_domain_common: 'Domänen är vanlig.',\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: 'Medlemskapet är fullt.',\n    organization_minimum_permissions_needed: 'Du måste ha tillräckligt med behörigheter.',\n    passkey_already_exists: 'Passnyckeln finns redan.',\n    passkey_not_supported: 'Passnyckel stöds inte.',\n    passkey_pa_not_supported: 'Passnyckel PA stöds inte.',\n    passkey_registration_cancelled: 'Registrering av passnyckel avbruten.',\n    passkey_retrieval_cancelled: 'Hämtning av passnyckel avbruten.',\n    passwordComplexity: {\n      maximumLength: 'Maximal längd',\n      minimumLength: 'Minimal längd',\n      requireLowercase: 'Kräver små bokstäver',\n      requireNumbers: 'Kräver siffror',\n      requireSpecialCharacter: 'Kräver specialtecken',\n      requireUppercase: 'Kräver stora bokstäver',\n      sentencePrefix: 'Lösenordet måste innehålla',\n    },\n    phone_number_exists: 'Detta telefonnummer är taget. Vänligen prova ett annat.',\n    session_exists: 'Du är redan inloggad.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Ditt lösenord fungerar, men kunde vara starkare. Försök lägga till fler tecken.',\n      goodPassword: 'Ditt lösenord uppfyller alla nödvändiga krav.',\n      notEnough: 'Ditt lösenord är inte tillräckligt starkt.',\n      suggestions: {\n        allUppercase: 'Använd stora bokstäver, men inte för alla tecken.',\n        anotherWord: 'Lägg till fler ord som är mindre vanliga.',\n        associatedYears: 'Undvik år som är associerade med dig.',\n        capitalization: 'Använd stor bokstav för mer än det första tecknet.',\n        dates: 'Undvik datum och år som är associerade med dig.',\n        l33t: \"Undvik förutsägbara bokstavsersättningar som '@' för 'a'.\",\n        longerKeyboardPattern: 'Använd längre tangentbordsmönster och ändra skrivrättning flera gånger.',\n        noNeed: 'Du kan skapa starka lösenord utan att använda symboler, siffror eller stora bokstäver.',\n        pwned: 'Om du använder detta lösenord någon annanstans bör du ändra det.',\n        recentYears: 'Undvik de senaste åren.',\n        repeated: 'Undvik upprepade ord och tecken.',\n        reverseWords: 'Undvik omvänd stavning av vanliga ord.',\n        sequences: 'Undvik vanliga teckenföljder.',\n        useWords: 'Använd flera ord, men undvik vanliga fraser.',\n      },\n      warnings: {\n        common: 'Detta är ett vanligt använt lösenord.',\n        commonNames: 'Vanliga namn och efternamn är lätta att gissa.',\n        dates: 'Datum är lätta att gissa.',\n        extendedRepeat: 'Upprepade teckenmönster som \"abcabcabc\" är lätta att gissa.',\n        keyPattern: 'Korta tangentbordsmönster är lätta att gissa.',\n        namesByThemselves: 'Enskilda namn eller efternamn är lätta att gissa.',\n        pwned: 'Ditt lösenord har exponerats genom ett dataintrång på internet.',\n        recentYears: 'Senaste åren är lätta att gissa.',\n        sequences: 'Vanliga teckenföljder som \"abc\" är lätta att gissa.',\n        similarToCommon: 'Detta liknar ett vanligt använt lösenord.',\n        simpleRepeat: 'Upprepade tecken som \"aaa\" är lätta att gissa.',\n        straightRow: 'Raka rader av tangenter på ditt tangentbord är lätta att gissa.',\n        topHundred: 'Detta är ett ofta använt lösenord.',\n        topTen: 'Detta är ett mycket använt lösenord.',\n        userInputs: 'Det bör inte finnas några personliga eller sidrelaterade data.',\n        wordByItself: 'Enskilda ord är lätta att gissa.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Lägg till konto',\n    action__manageAccount: 'Hantera konto',\n    action__signOut: 'Logga ut',\n    action__signOutAll: 'Logga ut från alla konton',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Kopierat!',\n      actionLabel__copy: 'Kopiera alla',\n      actionLabel__download: 'Ladda ner .txt',\n      actionLabel__print: 'Skriv ut',\n      infoText1: 'Backupkoder kommer att aktiveras för detta konto.',\n      infoText2:\n        'Håll backupkoderna hemliga och förvara dem säkert. Du kan generera nya backupkoder om du misstänker att de har komprometterats.',\n      subtitle__codelist: 'Förvara dem säkert och håll dem hemliga.',\n      successMessage:\n        'Backupkoder är nu aktiverade. Du kan använda en av dessa för att logga in på ditt konto om du förlorar åtkomsten till din autentiseringsenhet. Varje kod kan endast användas en gång.',\n      successSubtitle:\n        'Du kan använda en av dessa för att logga in på ditt konto om du förlorar åtkomsten till din autentiseringsenhet.',\n      title: 'Lägg till backupkodverifiering',\n      title__codelist: 'Backupkoder',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Välj en leverantör för att ansluta ditt konto.',\n      formHint__noAccounts: 'Det finns inga tillgängliga externa kontoleverantörer.',\n      removeResource: {\n        messageLine1: '{{identifier}} kommer att tas bort från detta konto.',\n        messageLine2:\n          'Du kommer inte längre att kunna använda detta anslutna konto och alla beroende funktioner kommer att sluta fungera.',\n        successMessage: '{{connectedAccount}} har tagits bort från ditt konto.',\n        title: 'Ta bort anslutet konto',\n      },\n      socialButtonsBlockButton: 'Anslut {{provider|titleize}} konto',\n      successMessage: 'Leverantören har lagts till i ditt konto.',\n      title: 'Lägg till anslutet konto',\n    },\n    deletePage: {\n      actionDescription: 'Skriv \"Radera konto\" nedan för att fortsätta.',\n      confirm: 'Radera konto',\n      messageLine1: 'Är du säker på att du vill radera ditt konto?',\n      messageLine2: 'Denna åtgärd är permanent och kan inte ångras.',\n      title: 'Radera konto',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Ett e-postmeddelande med en verifieringskod kommer att skickas till denna e-postadress.',\n        formSubtitle: 'Ange verifieringskoden som skickats till {{identifier}}',\n        formTitle: 'Verifieringskod',\n        resendButton: 'Skicka kod igen',\n        successMessage: 'E-postadressen {{identifier}} har lagts till i ditt konto.',\n      },\n      emailLink: {\n        formHint: 'Ett e-postmeddelande med en verifieringslänk kommer att skickas till denna e-postadress.',\n        formSubtitle: 'Klicka på verifieringslänken i e-postmeddelandet som skickats till {{identifier}}',\n        formTitle: 'Verifieringslänk',\n        resendButton: 'Skicka länken igen',\n        successMessage: 'E-postadressen {{identifier}} har lagts till i ditt konto.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} kommer att tas bort från detta konto.',\n        messageLine2: 'Du kommer inte längre att kunna logga in med denna e-postadress.',\n        successMessage: '{{emailAddress}} har tagits bort från ditt konto.',\n        title: 'Ta bort e-postadress',\n      },\n      title: 'Lägg till e-postadress',\n      verifyTitle: 'Verifiera e-postadress',\n    },\n    formButtonPrimary__add: 'Lägg till',\n    formButtonPrimary__continue: 'Fortsätt',\n    formButtonPrimary__finish: 'Slutför',\n    formButtonPrimary__remove: 'Ta bort',\n    formButtonPrimary__save: 'Spara',\n    formButtonReset: 'Avbryt',\n    mfaPage: {\n      formHint: 'Välj en metod att lägga till.',\n      title: 'Lägg till tvåstegsverifiering',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Använd befintligt telefonnummer',\n      primaryButton__addPhoneNumber: 'Lägg till ett telefonnummer',\n      removeResource: {\n        messageLine1: '{{identifier}} kommer inte längre att ta emot verifieringskoder vid inloggning.',\n        messageLine2: 'Ditt konto kan vara mindre säkert. Är du säker på att du vill fortsätta?',\n        successMessage: 'SMS-kod tvåstegsverifiering har tagits bort för {{mfaPhoneCode}}',\n        title: 'Ta bort tvåstegsverifiering',\n      },\n      subtitle__availablePhoneNumbers: 'Välj ett telefonnummer att registrera för SMS-kod tvåstegsverifiering.',\n      subtitle__unavailablePhoneNumbers:\n        'Det finns inga tillgängliga telefonnummer att registrera för SMS-kod tvåstegsverifiering.',\n      successMessage1:\n        'När du loggar in kommer du att behöva ange en verifieringskod som skickats till detta telefonnummer som ett ytterligare steg.',\n      successMessage2:\n        'Spara dessa säkerhetskoder och förvara dem på ett säkert ställe. Om du förlorar tillgång till din autentiseringsenhet kan du använda säkerhetskoder för att logga in.',\n      successTitle: 'SMS-kodsverifiering aktiverad',\n      title: 'Lägg till SMS-kodverifiering',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Skanna QR-kod istället',\n        buttonUnableToScan__nonPrimary: 'Kan inte skanna QR-kod?',\n        infoText__ableToScan:\n          'Konfigurera en ny inloggningsmetod i din autentiseringsapp och skanna följande QR-kod för att länka den till ditt konto.',\n        infoText__unableToScan: 'Konfigurera en ny inloggningsmetod i din autentiseringsapp och ange nyckeln nedan.',\n        inputLabel__unableToScan1:\n          'Se till att tidsbaserade eller engångslösenord är aktiverade och slutför sedan länkningen till ditt konto.',\n        inputLabel__unableToScan2:\n          'Alternativt, om din autentiseringsapp stödjer TOTP URI kan du också kopiera hela URI.',\n      },\n      removeResource: {\n        messageLine1: 'Verifieringskoder från denna autentiseringsapp kommer inte längre att krävas vid inloggning.',\n        messageLine2: 'Ditt konto kan vara mindre säkert. Är du säker på att du vill fortsätta?',\n        successMessage: 'Tvåstegsverifiering via autentiseringsapp har tagits bort.',\n        title: 'Ta bort tvåstegsverifiering',\n      },\n      successMessage:\n        'Tvåstegsverifiering är nu aktiverat. Vid inloggning behöver du ange en verifieringskod från denna autentiseringsapp som ett extra steg.',\n      title: 'Lägg till autentiseringsapp',\n      verifySubtitle: 'Ange verifieringskoden genererad av din autentiseringsapp',\n      verifyTitle: 'Verifieringskod',\n    },\n    mobileButton__menu: 'Meny',\n    navbar: {\n      account: 'Profil',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Hantera din kontoinformation.',\n      security: 'Säkerhet',\n      title: 'Konto',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} kommer att tas bort från detta konto.',\n        title: 'Ta bort passkey',\n      },\n      subtitle__rename: 'Du kan ändra passkey-namnet för att göra det lättare att hitta.',\n      title__rename: 'Byt namn på Passkey',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Det rekommenderas att logga ut från alla andra enheter som kan ha använt ditt gamla lösenord.',\n      readonly:\n        'Ditt lösenord kan för närvarande inte redigeras eftersom du endast kan logga in via företagsanslutningen.',\n      successMessage__set: 'Ditt lösenord har angetts.',\n      successMessage__signOutOfOtherSessions: 'Alla andra enheter har loggats ut.',\n      successMessage__update: 'Ditt lösenord har uppdaterats.',\n      title__set: 'Ange lösenord',\n      title__update: 'Byt lösenord',\n    },\n    phoneNumberPage: {\n      infoText: 'Ett textmeddelande med en verifieringslänk kommer att skickas till detta telefonnummer.',\n      removeResource: {\n        messageLine1: '{{identifier}} kommer att tas bort från detta konto.',\n        messageLine2: 'Du kommer inte längre att kunna logga in med detta telefonnummer.',\n        successMessage: '{{phoneNumber}} har tagits bort från ditt konto.',\n        title: 'Ta bort telefonnummer',\n      },\n      successMessage: '{{identifier}} har lagts till i ditt konto.',\n      title: 'Lägg till telefonnummer',\n      verifySubtitle: 'Ange verifieringskoden som skickats till {{identifier}}',\n      verifyTitle: 'Verifiera telefonnummer',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Ladda upp en JPG, PNG, GIF, eller WEBP bild som är mindre än 10 MB',\n      imageFormDestructiveActionSubtitle: 'Ta bort bild',\n      imageFormSubtitle: 'Ladda upp bild',\n      imageFormTitle: 'Profilbild',\n      readonly: 'Din profilinformation har tillhandahållits av företagsanslutningen och kan inte redigeras.',\n      successMessage: 'Din profil har uppdaterats.',\n      title: 'Uppdatera profil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Logga ut från enhet',\n        title: 'Aktiva enheter',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Försök igen',\n        actionLabel__reauthorize: 'Autentisera nu',\n        destructiveActionTitle: 'Ta bort',\n        primaryButton: 'Anslut konto',\n        subtitle__disconnected: 'Detta konto har kopplats bort.',\n        subtitle__reauthorize:\n          'De nödvändiga behörigheterna har uppdaterats, och du kan uppleva begränsad funktionalitet. Vänligen ge ny auktorisering till denna applikation för att undvika problem',\n        title: 'Anslutna konton',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Radera konto',\n        title: 'Radera konto',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Ta bort e-postadress',\n        detailsAction__nonPrimary: 'Sätt som primär',\n        detailsAction__primary: 'Fullborda verifiering',\n        detailsAction__unverified: 'Fullborda verifiering',\n        primaryButton: 'Lägg till en e-postadress',\n        title: 'E-postadresser',\n      },\n      enterpriseAccountsSection: {\n        title: 'Enterprise accounts',\n      },\n      headerTitle__account: 'Konto',\n      headerTitle__security: 'Säkerhet',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Återgenerera koder',\n          headerTitle: 'Säkerhetskopieringskoder',\n          subtitle__regenerate:\n            'Få en ny uppsättning säkra säkerhetskopieringskoder. Tidigare koder kommer att raderas och kan inte användas.',\n          title__regenerate: 'Återgenerera säkerhetskopieringskoder',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Ange som standard',\n          destructiveActionLabel: 'Ta bort telefonnummer',\n        },\n        primaryButton: 'Lägg till tvåstegsverifiering',\n        title: 'Tvåstegsverifiering',\n        totp: {\n          destructiveActionTitle: 'Ta bort',\n          headerTitle: 'Autentiseringsapp',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Ta bort',\n        menuAction__rename: 'Byt namn',\n        primaryButton: undefined,\n        title: 'Passkeys',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Ställ in lösenord',\n        primaryButton__updatePassword: 'Byt lösenord',\n        title: 'Lösenord',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Ta bort telefonnummer',\n        detailsAction__nonPrimary: 'Sätt som primär',\n        detailsAction__primary: 'Fullborda verifiering',\n        detailsAction__unverified: 'Fullborda verifiering',\n        primaryButton: 'Lägg till ett telefonnummer',\n        title: 'Telefonnummer',\n      },\n      profileSection: {\n        primaryButton: 'Uppdatera profil',\n        title: 'Profil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Sätt användarnamn',\n        primaryButton__updateUsername: 'Ändra användarnamn',\n        title: 'Användarnamn',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Ta bort plånbok',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 plånböcker',\n        title: 'Web3 plånböcker',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Ditt användarnamn har uppdaterats.',\n      title__set: 'Uppdatera användarnamn',\n      title__update: 'Uppdatera användarnamn',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} kommer att tas bort från detta konto.',\n        messageLine2: 'Du kommer inte längre att kunna logga in med denna web3-plånbok.',\n        successMessage: '{{web3Wallet}} har tagits bort från ditt konto.',\n        title: 'Ta bort web3-plånbok',\n      },\n      subtitle__availableWallets: 'Välj en web3-plånbok att ansluta till ditt konto.',\n      subtitle__unavailableWallets: 'Det finns inga tillgängliga web3-plånböcker.',\n      successMessage: 'Plånboken har lagts till i ditt konto.',\n      title: 'Lägg till web3-plånbok',\n      web3WalletButtonsBlockButton: '{{provider|titleize}}',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCAAiC;AAAA,MACjC,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBAAwB;AAAA,QACxB,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}