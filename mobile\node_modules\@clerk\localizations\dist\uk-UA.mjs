// src/uk-UA.ts
var ukUA = {
  locale: "uk-UA",
  apiKeys: {
    action__add: void 0,
    action__search: void 0,
    createdAndExpirationStatus__expiresOn: void 0,
    createdAndExpirationStatus__never: void 0,
    detailsTitle__emptyRow: void 0,
    formButtonPrimary__add: void 0,
    formFieldCaption__expiration__expiresOn: void 0,
    formFieldCaption__expiration__never: void 0,
    formFieldOption__expiration__180d: void 0,
    formFieldOption__expiration__1d: void 0,
    formFieldOption__expiration__1y: void 0,
    formFieldOption__expiration__30d: void 0,
    formFieldOption__expiration__60d: void 0,
    formFieldOption__expiration__7d: void 0,
    formFieldOption__expiration__90d: void 0,
    formFieldOption__expiration__never: void 0,
    formHint: void 0,
    formTitle: void 0,
    lastUsed__days: void 0,
    lastUsed__hours: void 0,
    lastUsed__minutes: void 0,
    lastUsed__months: void 0,
    lastUsed__seconds: void 0,
    lastUsed__years: void 0,
    menuAction__revoke: void 0,
    revokeConfirmation: {
      confirmationText: void 0,
      formButtonPrimary__revoke: void 0,
      formHint: void 0,
      formTitle: void 0
    }
  },
  backButton: "\u041D\u0430\u0437\u0430\u0434",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "\u0417\u0430 \u0437\u0430\u043C\u043E\u0432\u0447\u0443\u0432\u0430\u043D\u043D\u044F\u043C",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "\u0406\u043D\u0448\u0438\u0439 \u043F\u0440\u0438\u0441\u0442\u0440\u0456\u0439-\u0434\u0432\u0456\u0439\u043D\u0438\u043A",
  badge__primary: "\u041E\u0441\u043D\u043E\u0432\u043D\u0438\u0439",
  badge__renewsAt: void 0,
  badge__requiresAction: "\u041F\u043E\u0442\u0440\u0435\u0431\u0443\u0454 \u0434\u0456\u0457",
  badge__startsAt: void 0,
  badge__thisDevice: "\u0426\u0435\u0439 \u043F\u0440\u0438\u0441\u0442\u0440\u0456\u0439",
  badge__unverified: "\u041D\u0435\u043F\u0435\u0440\u0435\u0432\u0456\u0440\u0435\u043D\u0438\u0439",
  badge__upcomingPlan: void 0,
  badge__userDevice: "\u041F\u0440\u0438\u0441\u0442\u0440\u0456\u0439 \u043A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0430",
  badge__you: "\u0412\u0438",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      pastDueNotice: void 0,
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    credit: void 0,
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDue: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "\u0421\u0442\u0432\u043E\u0440\u0438\u0442\u0438 \u043E\u0440\u0433\u0430\u043D\u0456\u0437\u0430\u0446\u0456\u044E",
    invitePage: {
      formButtonReset: "\u041F\u0440\u043E\u043F\u0443\u0441\u0442\u0438\u0442\u0438"
    },
    title: "\u0421\u0442\u0432\u043E\u0440\u0438\u0442\u0438 \u043E\u0440\u0433\u0430\u043D\u0456\u0437\u0430\u0446\u0456\u044E"
  },
  dates: {
    lastDay: "\u0412\u0447\u043E\u0440\u0430 \u0432 {{ date | timeString('uk-UA') }}",
    next6Days: "{{ date | weekday('uk-UA','long') }} \u0432 {{ date | timeString('uk-UA') }}",
    nextDay: "\u0417\u0430\u0432\u0442\u0440\u0430 \u0432 {{ date | timeString('uk-UA') }}",
    numeric: "{{ date | numeric('uk-UA') }}",
    previous6Days: "\u041E\u0441\u0442\u0430\u043D\u043D\u0456\u0439 {{ date | weekday('uk-UA','long') }} \u0432 {{ date | timeString('uk-UA') }}",
    sameDay: "\u0421\u044C\u043E\u0433\u043E\u0434\u043D\u0456 \u0432 {{ date | timeString('uk-UA') }}"
  },
  dividerText: "\u0430\u0431\u043E",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u0456\u043D\u0448\u0438\u0439 \u043C\u0435\u0442\u043E\u0434",
  footerPageLink__help: "\u0414\u043E\u043F\u043E\u043C\u043E\u0433\u0430",
  footerPageLink__privacy: "\u041F\u0440\u0438\u0432\u0430\u0442\u043D\u0456\u0441\u0442\u044C",
  footerPageLink__terms: "\u0423\u043C\u043E\u0432\u0438",
  formButtonPrimary: "\u041F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438",
  formButtonPrimary__verify: "Verify",
  formFieldAction__forgotPassword: "\u0417\u0430\u0431\u0443\u043B\u0438 \u043F\u0430\u0440\u043E\u043B\u044C?",
  formFieldError__matchingPasswords: "\u041F\u0430\u0440\u043E\u043B\u0456 \u0437\u0431\u0456\u0433\u0430\u044E\u0442\u044C\u0441\u044F.",
  formFieldError__notMatchingPasswords: "\u041F\u0430\u0440\u043E\u043B\u0456 \u043D\u0435 \u0437\u0431\u0456\u0433\u0430\u044E\u0442\u044C\u0441\u044F.",
  formFieldError__verificationLinkExpired: "The verification link expired. Please request a new link.",
  formFieldHintText__optional: "\u041D\u0435\u043E\u0431\u043E\u0432'\u044F\u0437\u043A\u043E\u0432\u043E",
  formFieldHintText__slug: "A slug is a human-readable ID that must be unique. It\u2019s often used in URLs.",
  formFieldInputPlaceholder__apiKeyDescription: void 0,
  formFieldInputPlaceholder__apiKeyExpirationDate: void 0,
  formFieldInputPlaceholder__apiKeyName: void 0,
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Delete account",
  formFieldInputPlaceholder__emailAddress: void 0,
  formFieldInputPlaceholder__emailAddress_username: void 0,
  formFieldInputPlaceholder__emailAddresses: "\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u0430\u0431\u043E \u0432\u0441\u0442\u0430\u0432\u0442\u0435 \u043E\u0434\u043D\u0443 \u0430\u0431\u043E \u0431\u0456\u043B\u044C\u0448\u0435 \u0430\u0434\u0440\u0435\u0441 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u043E\u0457 \u043F\u043E\u0448\u0442\u0438, \u0440\u043E\u0437\u0434\u0456\u043B\u0435\u043D\u0438\u0445 \u043F\u0440\u043E\u0431\u0456\u043B\u0430\u043C\u0438 \u0430\u0431\u043E \u043A\u043E\u043C\u0430\u043C\u0438",
  formFieldInputPlaceholder__firstName: void 0,
  formFieldInputPlaceholder__lastName: void 0,
  formFieldInputPlaceholder__organizationDomain: void 0,
  formFieldInputPlaceholder__organizationDomainEmailAddress: void 0,
  formFieldInputPlaceholder__organizationName: void 0,
  formFieldInputPlaceholder__organizationSlug: void 0,
  formFieldInputPlaceholder__password: void 0,
  formFieldInputPlaceholder__phoneNumber: void 0,
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__apiKeyDescription: void 0,
  formFieldLabel__apiKeyExpiration: void 0,
  formFieldLabel__apiKeyName: void 0,
  formFieldLabel__automaticInvitations: "Enable automatic invitations for this domain",
  formFieldLabel__backupCode: "\u041A\u043E\u0434 \u0432\u0456\u0434\u043D\u043E\u0432\u043B\u0435\u043D\u043D\u044F",
  formFieldLabel__confirmDeletion: "\u041F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F",
  formFieldLabel__confirmPassword: "\u041F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F \u043F\u0430\u0440\u043E\u043B\u044F",
  formFieldLabel__currentPassword: "\u041F\u043E\u0442\u043E\u0447\u043D\u0438\u0439 \u043F\u0430\u0440\u043E\u043B\u044C",
  formFieldLabel__emailAddress: "\u041F\u043E\u0448\u0442\u0430",
  formFieldLabel__emailAddress_username: "\u041F\u043E\u0448\u0442\u0430 \u0430\u0431\u043E \u0456\u043C'\u044F \u043A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0430",
  formFieldLabel__emailAddresses: "\u041F\u043E\u0448\u0442\u043E\u0432\u0456 \u0430\u0434\u0440\u0435\u0441\u0438",
  formFieldLabel__firstName: "\u0406\u043C'\u044F",
  formFieldLabel__lastName: "\u041F\u0440\u0456\u0437\u0432\u0438\u0449\u0435",
  formFieldLabel__newPassword: "\u041D\u043E\u0432\u0438\u0439 \u043F\u0430\u0440\u043E\u043B\u044C",
  formFieldLabel__organizationDomain: "Domain",
  formFieldLabel__organizationDomainDeletePending: "Delete pending invitations and suggestions",
  formFieldLabel__organizationDomainEmailAddress: "Verification email address",
  formFieldLabel__organizationDomainEmailAddressDescription: "Enter an email address under this domain to receive a code and verify this domain.",
  formFieldLabel__organizationName: "\u041D\u0430\u0437\u0432\u0430 \u043E\u0440\u0433\u0430\u043D\u0456\u0437\u0430\u0446\u0456\u0457",
  formFieldLabel__organizationSlug: "URL \u0430\u0434\u0440\u0435\u0441\u0430",
  formFieldLabel__passkeyName: void 0,
  formFieldLabel__password: "\u041F\u0430\u0440\u043E\u043B\u044C",
  formFieldLabel__phoneNumber: "\u041D\u043E\u043C\u0435\u0440 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443",
  formFieldLabel__role: "\u0420\u043E\u043B\u044C",
  formFieldLabel__signOutOfOtherSessions: "\u0412\u0438\u0439\u0442\u0438 \u0437 \u0443\u0441\u0456\u0445 \u0456\u043D\u0448\u0438\u0445 \u043F\u0440\u0438\u0441\u0442\u0440\u043E\u0457\u0432",
  formFieldLabel__username: "\u0406\u043C'\u044F \u043A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0430",
  impersonationFab: {
    action__signOut: "\u0412\u0438\u0439\u0442\u0438",
    title: "\u0412\u0438 \u0443\u0432\u0456\u0439\u0448\u043B\u0438 \u044F\u043A {{identifier}}"
  },
  maintenanceMode: void 0,
  membershipRole__admin: "\u0410\u0434\u043C\u0456\u043D\u0456\u0441\u0442\u0440\u0430\u0442\u043E\u0440",
  membershipRole__basicMember: "\u0427\u043B\u0435\u043D",
  membershipRole__guestMember: "\u0413\u0456\u0441\u0442\u044C",
  organizationList: {
    action__createOrganization: "Create organization",
    action__invitationAccept: "Join",
    action__suggestionsAccept: "Request to join",
    createOrganization: "Create Organization",
    invitationAcceptedLabel: "Joined",
    subtitle: "to continue to {{applicationName}}",
    suggestionsAcceptedLabel: "Pending approval",
    title: "Choose an account",
    titleWithoutPersonal: "Choose an organization"
  },
  organizationProfile: {
    apiKeysPage: {
      title: void 0
    },
    badge__automaticInvitation: "Automatic invitations",
    badge__automaticSuggestion: "Automatic suggestions",
    badge__manualInvitation: "No automatic enrollment",
    badge__unverified: "Unverified",
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Add the domain to verify. Users with email addresses at this domain can join the organization automatically or request to join.",
      title: "Add domain"
    },
    invitePage: {
      detailsTitle__inviteFailed: "\u0417\u0430\u043F\u0440\u043E\u0448\u0435\u043D\u043D\u044F \u043D\u0435 \u0432\u0434\u0430\u043B\u043E\u0441\u044F \u043D\u0430\u0434\u0456\u0441\u043B\u0430\u0442\u0438. \u0412\u0438\u043F\u0440\u0430\u0432\u0442\u0435 \u043D\u0430\u0441\u0442\u0443\u043F\u043D\u0435 \u0456 \u043F\u043E\u0432\u0442\u043E\u0440\u0456\u0442\u044C \u0441\u043F\u0440\u043E\u0431\u0443:",
      formButtonPrimary__continue: "\u041D\u0430\u0434\u0456\u0441\u043B\u0430\u0442\u0438 \u0437\u0430\u043F\u0440\u043E\u0448\u0435\u043D\u043D\u044F",
      selectDropdown__role: "Select role",
      subtitle: "\u0417\u0430\u043F\u0440\u043E\u0441\u0456\u0442\u044C \u043D\u043E\u0432\u0438\u0445 \u0443\u0447\u0430\u0441\u043D\u0438\u043A\u0456\u0432 \u0434\u043E \u0446\u0456\u0454\u0457 \u043E\u0440\u0433\u0430\u043D\u0456\u0437\u0430\u0446\u0456\u0457",
      successMessage: "\u0417\u0430\u043F\u0440\u043E\u0448\u0435\u043D\u043D\u044F \u0443\u0441\u043F\u0456\u0448\u043D\u043E \u043D\u0430\u0434\u0456\u0441\u043B\u0430\u043D\u043E",
      title: "\u0417\u0430\u043F\u0440\u043E\u0441\u0438\u0442\u0438 \u0443\u0447\u0430\u0441\u043D\u0438\u043A\u0456\u0432"
    },
    membersPage: {
      action__invite: "\u0417\u0430\u043F\u0440\u043E\u0441\u0438\u0442\u0438",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0443\u0447\u0430\u0441\u043D\u0438\u043A\u0430",
        tableHeader__actions: void 0,
        tableHeader__joined: "\u041F\u0440\u0438\u0454\u0434\u043D\u0430\u0432\u0441\u044F",
        tableHeader__role: "\u0420\u043E\u043B\u044C",
        tableHeader__user: "\u041A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0447"
      },
      detailsTitle__emptyRow: "\u041D\u0435\u043C\u0430\u0454 \u0443\u0447\u0430\u0441\u043D\u0438\u043A\u0456\u0432 \u0434\u043B\u044F \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Invite users by connecting an email domain with your organization. Anyone who signs up with a matching email domain will be able to join the organization anytime.",
          headerTitle: "Automatic invitations",
          primaryButton: "Manage verified domains"
        },
        table__emptyRow: "No invitations to display"
      },
      invitedMembersTab: {
        menuAction__revoke: "\u0412\u0456\u0434\u043A\u043B\u0438\u043A\u0430\u0442\u0438 \u0437\u0430\u043F\u0440\u043E\u0448\u0435\u043D\u043D\u044F",
        tableHeader__invited: "\u0417\u0430\u043F\u0440\u043E\u0448\u0435\u043D\u0456"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Users who sign up with a matching email domain, will be able to see a suggestion to request to join your organization.",
          headerTitle: "Automatic suggestions",
          primaryButton: "Manage verified domains"
        },
        menuAction__approve: "Approve",
        menuAction__reject: "Reject",
        tableHeader__requested: "Requested access",
        table__emptyRow: "No requests to display"
      },
      start: {
        headerTitle__invitations: "Invitations",
        headerTitle__members: "Members",
        headerTitle__requests: "Requests"
      }
    },
    navbar: {
      apiKeys: void 0,
      billing: void 0,
      description: "Manage your organization.",
      general: "General",
      members: "Members",
      title: "Organization"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Type "{{organizationName}}" below to continue.',
          messageLine1: "Are you sure you want to delete this organization?",
          messageLine2: "This action is permanent and irreversible.",
          successMessage: "You have deleted the organization.",
          title: "Delete organization"
        },
        leaveOrganization: {
          actionDescription: 'Type "{{organizationName}}" below to continue.',
          messageLine1: "\u0412\u0438 \u0432\u043F\u0435\u0432\u043D\u0435\u043D\u0456, \u0449\u043E \u0445\u043E\u0447\u0435\u0442\u0435 \u043F\u043E\u043A\u0438\u043D\u0443\u0442\u0438 \u0446\u044E \u043E\u0440\u0433\u0430\u043D\u0456\u0437\u0430\u0446\u0456\u044E? \u0412\u0438 \u0432\u0442\u0440\u0430\u0442\u0438\u0442\u0435 \u0434\u043E\u0441\u0442\u0443\u043F \u0434\u043E \u0446\u0456\u0454\u0457 \u043E\u0440\u0433\u0430\u043D\u0456\u0437\u0430\u0446\u0456\u0457 \u0442\u0430 \u0457\u0457 \u0434\u043E\u0434\u0430\u0442\u043A\u0456\u0432.",
          messageLine2: "\u0426\u044F \u0434\u0456\u044F \u0454 \u043F\u043E\u0441\u0442\u0456\u0439\u043D\u043E\u044E \u0456 \u043D\u0435\u0437\u0432\u043E\u0440\u043E\u0442\u043D\u043E\u044E.",
          successMessage: "\u0412\u0438 \u043F\u043E\u043A\u0438\u043D\u0443\u043B\u0438 \u043E\u0440\u0433\u0430\u043D\u0456\u0437\u0430\u0446\u0456\u044E.",
          title: "\u041F\u043E\u043A\u0438\u043D\u0443\u0442\u0438 \u043E\u0440\u0433\u0430\u043D\u0456\u0437\u0430\u0446\u0456\u044E"
        },
        title: "\u041D\u0435\u0431\u0435\u0437\u043F\u0435\u043A\u0430"
      },
      domainSection: {
        menuAction__manage: "Manage",
        menuAction__remove: "Delete",
        menuAction__verify: "Verify",
        primaryButton: "Add domain",
        subtitle: "Allow users to join the organization automatically or request to join based on a verified email domain.",
        title: "Verified domains"
      },
      successMessage: "\u041E\u0440\u0433\u0430\u043D\u0456\u0437\u0430\u0446\u0456\u044E \u0431\u0443\u043B\u043E \u043E\u043D\u043E\u0432\u043B\u0435\u043D\u043E.",
      title: "\u041F\u0440\u043E\u0444\u0456\u043B\u044C \u043E\u0440\u0433\u0430\u043D\u0456\u0437\u0430\u0446\u0456\u0457"
    },
    removeDomainPage: {
      messageLine1: "The email domain {{domain}} will be removed.",
      messageLine2: "Users won\u2019t be able to join the organization automatically after this.",
      successMessage: "{{domain}} has been removed.",
      title: "Remove domain"
    },
    start: {
      headerTitle__general: "General",
      headerTitle__members: "\u0423\u0447\u0430\u0441\u043D\u0438\u043A\u0438",
      profileSection: {
        primaryButton: void 0,
        title: "Organization Profile",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Removing this domain will affect invited users.",
        removeDomainActionLabel__remove: "Remove domain",
        removeDomainSubtitle: "Remove this domain from your verified domains",
        removeDomainTitle: "Remove domain"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Users are automatically invited to join the organization when they sign-up and can join anytime.",
        automaticInvitationOption__label: "Automatic invitations",
        automaticSuggestionOption__description: "Users receive a suggestion to request to join, but must be approved by an admin before they are able to join the organization.",
        automaticSuggestionOption__label: "Automatic suggestions",
        calloutInfoLabel: "Changing the enrollment mode will only affect new users.",
        calloutInvitationCountLabel: "Pending invitations sent to users: {{count}}",
        calloutSuggestionCountLabel: "Pending suggestions sent to users: {{count}}",
        manualInvitationOption__description: "Users can only be invited manually to the organization.",
        manualInvitationOption__label: "No automatic enrollment",
        subtitle: "Choose how users from this domain can join the organization."
      },
      start: {
        headerTitle__danger: "Danger",
        headerTitle__enrollment: "Enrollment options"
      },
      subtitle: "The domain {{domain}} is now verified. Continue by selecting enrollment mode.",
      title: "Update {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Enter the verification code sent to your email address",
      formTitle: "Verification code",
      resendButton: "Didn't receive a code? Resend",
      subtitle: "The domain {{domainName}} needs to be verified via email.",
      subtitleVerificationCodeScreen: "A verification code was sent to {{emailAddress}}. Enter the code to continue.",
      title: "Verify domain"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "\u0421\u0442\u0432\u043E\u0440\u0438\u0442\u0438 \u043E\u0440\u0433\u0430\u043D\u0456\u0437\u0430\u0446\u0456\u044E",
    action__invitationAccept: "Join",
    action__manageOrganization: "\u0423\u043F\u0440\u0430\u0432\u043B\u0456\u043D\u043D\u044F \u043E\u0440\u0433\u0430\u043D\u0456\u0437\u0430\u0446\u0456\u0454\u044E",
    action__suggestionsAccept: "Request to join",
    notSelected: "\u041E\u0440\u0433\u0430\u043D\u0456\u0437\u0430\u0446\u0456\u044F \u043D\u0435 \u043E\u0431\u0440\u0430\u043D\u0430",
    personalWorkspace: "\u041E\u0441\u043E\u0431\u0438\u0441\u0442\u0438\u0439 \u0440\u043E\u0431\u043E\u0447\u0438\u0439 \u043F\u0440\u043E\u0441\u0442\u0456\u0440",
    suggestionsAcceptedLabel: "Pending approval"
  },
  paginationButton__next: "\u0412\u043F\u0435\u0440\u0435\u0434",
  paginationButton__previous: "\u041D\u0430\u0437\u0430\u0434",
  paginationRowText__displaying: "\u0412\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F",
  paginationRowText__of: "\u0437",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Add account",
      action__signOutAll: "Sign out of all accounts",
      subtitle: "Select the account with which you wish to continue.",
      title: "Choose an account"
    },
    alternativeMethods: {
      actionLink: "\u0414\u043E\u043F\u043E\u043C\u043E\u0433\u0430",
      actionText: "Don\u2019t have any of these?",
      blockButton__backupCode: "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0439\u0442\u0435 \u043A\u043E\u0434 \u0432\u0456\u0434\u043D\u043E\u0432\u043B\u0435\u043D\u043D\u044F",
      blockButton__emailCode: "\u041D\u0430\u0434\u0456\u0441\u043B\u0430\u0442\u0438 \u043A\u043E\u0434 \u043D\u0430 {{identifier}}",
      blockButton__emailLink: "\u041D\u0430\u0434\u0456\u0441\u043B\u0430\u0442\u0438 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F \u043D\u0430 {{identifier}}",
      blockButton__passkey: void 0,
      blockButton__password: "\u0423\u0432\u0456\u0439\u0442\u0438 \u0437 \u043F\u0430\u0440\u043E\u043B\u0435\u043C",
      blockButton__phoneCode: "\u041D\u0430\u0434\u0456\u0441\u043B\u0430\u0442\u0438 \u043A\u043E\u0434 \u043D\u0430 {{identifier}}",
      blockButton__totp: "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0439\u0442\u0435 \u0430\u0443\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0442\u043E\u0440",
      getHelp: {
        blockButton__emailSupport: "\u041D\u0430\u043F\u0438\u0441\u0430\u0442\u0438 \u0432 \u043F\u0456\u0434\u0442\u0440\u0438\u043C\u043A\u0443",
        content: "\u042F\u043A\u0449\u043E \u0443 \u0432\u0430\u0441 \u0432\u0438\u043D\u0438\u043A\u043B\u0438 \u0442\u0440\u0443\u0434\u043D\u043E\u0449\u0456 \u0437 \u0432\u0445\u043E\u0434\u043E\u043C \u0443 \u0412\u0430\u0448 \u0430\u043A\u0430\u0443\u043D\u0442, \u043D\u0430\u043F\u0438\u0448\u0456\u0442\u044C \u043D\u0430\u043C, \u0456 \u043C\u0438 \u043F\u043E\u043F\u0440\u0430\u0446\u044E\u0454\u043C\u043E \u0437 \u0412\u0430\u043C\u0438, \u0449\u043E\u0431 \u0432\u0456\u0434\u043D\u043E\u0432\u0438\u0442\u0438 \u0434\u043E\u0441\u0442\u0443\u043F \u044F\u043A\u043D\u0430\u0439\u0448\u0432\u0438\u0434\u0448\u0435.",
        title: "\u0414\u043E\u043F\u043E\u043C\u043E\u0433\u0430"
      },
      subtitle: "Facing issues? You can use any of these methods to sign in.",
      title: "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u0456\u043D\u0448\u0438\u0439 \u043C\u0435\u0442\u043E\u0434"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: '\u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u0440\u043E\u0431\u043E\u0442\u0443 \u0432 "{{applicationName}}"',
      title: "\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u043A\u043E\u0434 \u0432\u0456\u0434\u043D\u043E\u0432\u043B\u0435\u043D\u043D\u044F"
    },
    emailCode: {
      formTitle: "\u041A\u043E\u0434 \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F",
      resendButton: "\u041D\u0435 \u043E\u0442\u0440\u0438\u043C\u0430\u043B\u0438 \u043A\u043E\u0434? \u041F\u043E\u0432\u0442\u043E\u0440\u043D\u043E \u0432\u0456\u0434\u043F\u0440\u0430\u0432\u0438\u0442\u0438",
      subtitle: "\u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u0434\u043E {{applicationName}}",
      title: "\u041F\u0435\u0440\u0435\u0432\u0456\u0440\u0442\u0435 \u0441\u0432\u043E\u044E \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u0443 \u043F\u043E\u0448\u0442\u0443"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      expired: {
        subtitle: "\u041F\u043E\u0432\u0435\u0440\u043D\u0456\u0442\u044C\u0441\u044F \u043D\u0430 \u043F\u043E\u0447\u0430\u0442\u043A\u043E\u0432\u0443 \u0432\u043A\u043B\u0430\u0434\u043A\u0443, \u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438.",
        title: "\u0422\u0435\u0440\u043C\u0456\u043D \u0434\u0456\u0457 \u0446\u044C\u043E\u0433\u043E \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F \u0434\u043B\u044F \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F \u0437\u0430\u043A\u0456\u043D\u0447\u0438\u0432\u0441\u044F"
      },
      failed: {
        subtitle: "\u041F\u043E\u0432\u0435\u0440\u043D\u0456\u0442\u044C\u0441\u044F \u043D\u0430 \u043F\u043E\u0447\u0430\u0442\u043A\u043E\u0432\u0443 \u0432\u043A\u043B\u0430\u0434\u043A\u0443, \u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438",
        title: "\u0426\u0435 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F \u0434\u043B\u044F \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F \u0454 \u043D\u0435\u0434\u0456\u0439\u0441\u043D\u0438\u043C"
      },
      formSubtitle: "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0439\u0442\u0435 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F \u0434\u043B\u044F \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F, \u043D\u0430\u0434\u0456\u0441\u043B\u0430\u043D\u0435 \u043D\u0430 \u0412\u0430\u0448\u0443 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u0443 \u043F\u043E\u0448\u0442\u0443",
      formTitle: "\u041F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F \u0434\u043B\u044F \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F",
      loading: {
        subtitle: "\u0412\u0430\u0441 \u0431\u0443\u0434\u0435 \u043F\u0435\u0440\u0435\u043D\u0430\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u043E \u043D\u0430\u0439\u0431\u043B\u0438\u0436\u0447\u0438\u043C \u0447\u0430\u0441\u043E\u043C",
        title: "\u0412\u0445\u0456\u0434 \u0432 \u0441\u0438\u0441\u0442\u0435\u043C\u0443..."
      },
      resendButton: "\u041F\u0435\u0440\u0435\u0432\u0456\u0434\u043F\u0440\u0430\u0432\u0438\u0442\u0438 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F",
      subtitle: '\u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u0440\u043E\u0431\u043E\u0442\u0443 \u0432 "{{applicationName}}"',
      title: "\u041F\u0435\u0440\u0435\u0432\u0456\u0440\u0442\u0435 \u0412\u0430\u0448\u0443 \u043F\u043E\u0448\u0442\u0443",
      unusedTab: {
        title: "\u0412\u043A\u043B\u0430\u0434\u043A\u0443 \u043C\u043E\u0436\u043D\u0430 \u0437\u0430\u043A\u0440\u0438\u0442\u0438"
      },
      verified: {
        subtitle: "\u0412\u0438 \u0441\u043A\u043E\u0440\u043E \u0431\u0443\u0434\u0435\u0442\u0435 \u043F\u0435\u0440\u0435\u043D\u0430\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0456",
        title: "\u0423\u0441\u043F\u0456\u0448\u043D\u0438\u0439 \u0432\u0445\u0456\u0434"
      },
      verifiedSwitchTab: {
        subtitle: "\u041F\u043E\u0432\u0435\u0440\u043D\u0456\u0442\u044C\u0441\u044F \u043D\u0430 \u043F\u043E\u043F\u0435\u0440\u0435\u0434\u043D\u044E \u0432\u043A\u043B\u0430\u0434\u043A\u0443, \u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438",
        subtitleNewTab: "\u041F\u043E\u0432\u0435\u0440\u043D\u0456\u0442\u044C\u0441\u044F \u0434\u043E \u0449\u043E\u0439\u043D\u043E \u0432\u0456\u0434\u043A\u0440\u0438\u0442\u043E\u0457 \u0432\u043A\u043B\u0430\u0434\u043A\u0438, \u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438",
        titleNewTab: "\u0412\u0438 \u0432\u0432\u0456\u0439\u0448\u043B\u0438 \u043D\u0430 \u0456\u043D\u0448\u0456\u0439 \u0432\u043A\u043B\u0430\u0434\u0446\u0456"
      }
    },
    forgotPassword: {
      formTitle: "\u041A\u043E\u0434 \u0432\u0456\u0434\u043D\u043E\u0432\u043B\u0435\u043D\u043D\u044F \u043F\u0430\u0440\u043E\u043B\u044F",
      resendButton: "\u041D\u0430\u0434\u0456\u0441\u043B\u0430\u0442\u0438 \u043A\u043E\u0434 \u0449\u0435 \u0440\u0430\u0437",
      subtitle: "to reset your password",
      subtitle_email: "First, enter the code sent to your email ID",
      subtitle_phone: "First, enter the code sent to your phone",
      title: "Reset password"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "\u0412\u0456\u0434\u043D\u043E\u0432\u0438\u0442\u0438 \u043F\u0430\u0440\u043E\u043B\u044C",
      label__alternativeMethods: "\u0410\u0431\u043E, \u0443\u0432\u0456\u0439\u0442\u0438 \u0456\u043D\u0448\u0438\u043C \u0441\u043F\u043E\u0441\u043E\u0431\u043E\u043C",
      title: "\u0417\u0430\u0431\u0443\u043B\u0438 \u043F\u0430\u0440\u043E\u043B\u044C?"
    },
    noAvailableMethods: {
      message: "\u041D\u0435 \u0432\u0434\u0430\u0454\u0442\u044C\u0441\u044F \u0432\u0438\u043A\u043E\u043D\u0430\u0442\u0438 \u0432\u0445\u0456\u0434. \u041D\u0435\u043C\u0430\u0454 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u043E\u0433\u043E \u0444\u0430\u043A\u0442\u043E\u0440\u0443 \u0430\u0432\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0457.",
      subtitle: "\u0412\u0438\u043D\u0438\u043A\u043B\u0430 \u043F\u043E\u043C\u0438\u043B\u043A\u0430",
      title: "\u041D\u0435 \u0432\u0434\u0430\u043B\u043E\u0441\u044F \u0443\u0432\u0456\u0439\u0442\u0438"
    },
    passkey: {
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u0442\u0438 \u0456\u043D\u0448\u0438\u0439 \u043C\u0435\u0442\u043E\u0434",
      subtitle: '\u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u0440\u043E\u0431\u043E\u0442\u0443 \u0432 "{{applicationName}}"',
      title: "\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u043F\u0430\u0440\u043E\u043B\u044C"
    },
    passwordPwned: {
      title: void 0
    },
    phoneCode: {
      formTitle: "\u041A\u043E\u0434 \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F",
      resendButton: "\u041D\u0435 \u043E\u0442\u0440\u0438\u043C\u0430\u043B\u0438 \u043A\u043E\u0434? \u043F\u043E\u0432\u0442\u043E\u0440\u043D\u043E \u0432\u0456\u0434\u043F\u0440\u0430\u0432\u0438\u0442\u0438",
      subtitle: "\u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u0432 {{applicationName}}",
      title: "\u041F\u0435\u0440\u0435\u0432\u0456\u0440\u0442\u0435 \u0441\u0432\u0456\u0439 \u0442\u0435\u043B\u0435\u0444\u043E\u043D"
    },
    phoneCodeMfa: {
      formTitle: "\u041A\u043E\u0434 \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F",
      resendButton: "\u041D\u0435 \u043E\u0442\u0440\u0438\u043C\u0430\u043B\u0438 \u043A\u043E\u0434? \u043F\u043E\u0432\u0442\u043E\u0440\u043D\u043E \u0432\u0456\u0434\u043F\u0440\u0430\u0432\u0438\u0442\u0438",
      subtitle: void 0,
      title: "\u041F\u0435\u0440\u0435\u0432\u0456\u0440\u0442\u0435 \u0441\u0432\u0456\u0439 \u0442\u0435\u043B\u0435\u0444\u043E\u043D"
    },
    resetPassword: {
      formButtonPrimary: "\u0421\u043A\u0438\u043D\u0443\u0442\u0438 \u043F\u0430\u0440\u043E\u043B\u044C",
      requiredMessage: "For security reasons, it is required to reset your password.",
      successMessage: "\u0412\u0430\u0448 \u043F\u0430\u0440\u043E\u043B\u044C \u0443\u0441\u043F\u0456\u0448\u043D\u043E \u0437\u043C\u0456\u043D\u0435\u043D\u043E. \u0412\u0438\u043A\u043E\u043D\u0443\u0454\u0442\u044C\u0441\u044F \u0432\u0445\u0456\u0434, \u0437\u0430\u0447\u0435\u043A\u0430\u0439\u0442\u0435.",
      title: "\u0421\u043A\u0438\u043D\u0443\u0442\u0438 \u043F\u0430\u0440\u043E\u043B\u044C"
    },
    resetPasswordMfa: {
      detailsLabel: "\u041D\u0435\u043E\u0431\u0445\u0456\u0434\u043D\u043E \u0432\u0435\u0440\u0438\u0444\u0456\u043A\u0443\u0432\u0430\u0442\u0438 \u0432\u0430\u0448\u0443 \u043E\u0441\u043E\u0431\u0443 \u043F\u0435\u0440\u0435\u0434 \u0432\u0456\u0434\u043D\u043E\u0432\u043B\u0435\u043D\u043D\u044F\u043C \u043F\u0430\u0440\u043E\u043B\u044F"
    },
    start: {
      actionLink: "\u0417\u0430\u0440\u0435\u0454\u0441\u0442\u0440\u0443\u0432\u0430\u0442\u0438\u0441\u044F",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u043F\u043E\u0448\u0442\u0443",
      actionLink__use_email_username: "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u043F\u043E\u0448\u0442\u0443 \u0430\u0431\u043E \u0456\u043C'\u044F \u043A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0430",
      actionLink__use_passkey: void 0,
      actionLink__use_phone: "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u043D\u043E\u043C\u0435\u0440 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443",
      actionLink__use_username: "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u0456\u043C'\u044F \u043A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0430",
      actionText: "\u041D\u0435\u043C\u0430\u0454 \u0430\u043A\u0430\u0443\u043D\u0442\u0430?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: '\u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u0440\u043E\u0431\u043E\u0442\u0443 \u0432 "{{applicationName}}"',
      subtitleCombined: void 0,
      title: "\u0423\u0432\u0456\u0439\u0442\u0438",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "\u0412\u0435\u0440\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0439\u043D\u0438\u0439 \u043A\u043E\u0434",
      subtitle: void 0,
      title: "\u0414\u0432\u043E\u0435\u0442\u0430\u043F\u043D\u0430 \u043F\u0435\u0440\u0435\u0432\u0456\u0440\u043A\u0430"
    }
  },
  signInEnterPasswordTitle: "\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u0412\u0430\u0448 \u043F\u0430\u0440\u043E\u043B\u044C",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "\u0423\u0432\u0456\u0439\u0442\u0438",
      actionText: "\u0423\u0436\u0435 \u0454 \u0430\u043A\u0430\u0443\u043D\u0442?",
      subtitle: '\u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u0440\u043E\u0431\u043E\u0442\u0443 \u0432 "{{applicationName}}"',
      title: "\u0417\u0430\u043F\u043E\u0432\u043D\u0456\u0442\u044C \u0443\u0441\u0456 \u043F\u043E\u043B\u044F"
    },
    emailCode: {
      formSubtitle: "\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u043A\u043E\u0434 \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F, \u043D\u0430\u0434\u0456\u0441\u043B\u0430\u043D\u0438\u0439 \u043D\u0430 \u0432\u0430\u0448\u0443 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u0443 \u0430\u0434\u0440\u0435\u0441\u0443",
      formTitle: "\u041A\u043E\u0434 \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F",
      resendButton: "\u041D\u0435 \u043E\u0442\u0440\u0438\u043C\u0430\u043B\u0438 \u043A\u043E\u0434? \u041F\u043E\u0432\u0442\u043E\u0440\u043D\u043E \u0432\u0456\u0434\u043F\u0440\u0430\u0432\u0438\u0442\u0438",
      subtitle: "\u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u0434\u043E {{applicationName}}",
      title: "\u041F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0456\u0442\u044C \u0441\u0432\u043E\u044E \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u0443 \u043F\u043E\u0448\u0442\u0443"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      formSubtitle: "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0439\u0442\u0435 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F \u0434\u043B\u044F \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F, \u043D\u0430\u0434\u0456\u0441\u043B\u0430\u043D\u0435 \u043D\u0430 \u0432\u0430\u0448\u0443 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u0443 \u0430\u0434\u0440\u0435\u0441\u0443",
      formTitle: "\u041F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F \u0434\u043B\u044F \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F",
      loading: {
        title: "\u0420\u0435\u0454\u0441\u0442\u0440\u0430\u0446\u0456\u044F..."
      },
      resendButton: "\u041D\u0435 \u043E\u0442\u0440\u0438\u043C\u0430\u043B\u0438 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F? \u041F\u043E\u0432\u0442\u043E\u0440\u043D\u043E \u0432\u0456\u0434\u043F\u0440\u0430\u0432\u0438\u0442\u0438",
      subtitle: "\u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u0434\u043E {{applicationName}}",
      title: "\u041F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0456\u0442\u044C \u0441\u0432\u043E\u044E \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u0443 \u043F\u043E\u0448\u0442\u0443",
      verified: {
        title: "\u0423\u0441\u043F\u0456\u0448\u043D\u043E \u0437\u0430\u0440\u0435\u0454\u0441\u0442\u0440\u043E\u0432\u0430\u043D\u043E"
      },
      verifiedSwitchTab: {
        subtitle: "\u041F\u043E\u0432\u0435\u0440\u043D\u0456\u0442\u044C\u0441\u044F \u043D\u0430 \u043D\u043E\u0432\u0443 \u0432\u043A\u043B\u0430\u0434\u043A\u0443, \u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438",
        subtitleNewTab: "\u041F\u043E\u0432\u0435\u0440\u043D\u0443\u0442\u0438\u0441\u044F \u0434\u043E \u043F\u043E\u043F\u0435\u0440\u0435\u0434\u043D\u044C\u043E\u0457 \u0432\u043A\u043B\u0430\u0434\u043A\u0438 \u0434\u043B\u044F \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0435\u043D\u043D\u044F",
        title: "\u0423\u0441\u043F\u0456\u0448\u043D\u043E \u043F\u0435\u0440\u0435\u0432\u0456\u0440\u0435\u043D\u043E email"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: void 0,
        label__onlyTermsOfService: void 0,
        label__termsOfServiceAndPrivacyPolicy: void 0
      },
      continue: {
        subtitle: void 0,
        title: void 0
      }
    },
    phoneCode: {
      formSubtitle: "\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u043A\u043E\u0434 \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F, \u043D\u0430\u0434\u0456\u0441\u043B\u0430\u043D\u0438\u0439 \u043D\u0430 \u0432\u0430\u0448 \u043D\u043E\u043C\u0435\u0440 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443",
      formTitle: "\u041A\u043E\u0434 \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F",
      resendButton: "\u041D\u0435 \u043E\u0442\u0440\u0438\u043C\u0430\u043B\u0438 \u043A\u043E\u0434? \u041F\u043E\u0432\u0442\u043E\u0440\u043D\u043E \u0432\u0456\u0434\u043F\u0440\u0430\u0432\u0438\u0442\u0438",
      subtitle: "\u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u0437 {{applicationName}}",
      title: "\u041F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0456\u0442\u044C \u0441\u0432\u0456\u0439 \u0442\u0435\u043B\u0435\u0444\u043E\u043D"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "\u0423\u0432\u0456\u0439\u0442\u0438",
      actionLink__use_email: void 0,
      actionLink__use_phone: void 0,
      actionText: "\u0423\u0436\u0435 \u0454 \u0430\u043A\u0430\u0443\u043D\u0442?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: '\u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u0440\u043E\u0431\u043E\u0442\u0443 \u0432 "{{applicationName}}"',
      subtitleCombined: '\u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u0440\u043E\u0431\u043E\u0442\u0443 \u0432 "{{applicationName}}"',
      title: "\u0421\u0442\u0432\u043E\u0440\u0456\u0442\u044C \u0412\u0430\u0448 \u0430\u043A\u0430\u0443\u043D\u0442",
      titleCombined: "\u0421\u0442\u0432\u043E\u0440\u0456\u0442\u044C \u0412\u0430\u0448 \u0430\u043A\u0430\u0443\u043D\u0442"
    }
  },
  socialButtonsBlockButton: "\u041F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u0437\u0430 \u0434\u043E\u043F\u043E\u043C\u043E\u0433\u043E\u044E {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "Sign up unsuccessful due to failed security validations. Please refresh the page to try again or reach out to support for more assistance.",
    captcha_unavailable: "Sign up unsuccessful due to failed bot validation. Please refresh the page to try again or reach out to support for more assistance.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: void 0,
    form_identifier_exists__phone_number: void 0,
    form_identifier_exists__username: void 0,
    form_identifier_not_found: "\u041D\u0435 \u0432\u0434\u0430\u043B\u043E\u0441\u044F \u0437\u043D\u0430\u0439\u0442\u0438 \u0430\u043A\u0430\u0443\u043D\u0442 \u0437 \u0446\u0438\u043C\u0438 \u0434\u0430\u043D\u0438\u043C\u0438.",
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: "Email address must be a valid email address.",
    form_param_format_invalid__phone_number: "Phone number must be in a valid international format",
    form_param_max_length_exceeded__first_name: "First name should not exceed 256 characters.",
    form_param_max_length_exceeded__last_name: "Last name should not exceed 256 characters.",
    form_param_max_length_exceeded__name: "Name should not exceed 256 characters.",
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: void 0,
    form_password_not_strong_enough: "\u0412\u0430\u0448 \u043F\u0430\u0440\u043E\u043B\u044C \u043D\u0435\u0434\u043E\u0441\u0442\u0430\u0442\u043D\u044C\u043E \u043D\u0430\u0434\u0456\u0439\u043D\u0438\u0439.",
    form_password_pwned: "\u0426\u0435\u0439 \u043F\u0430\u0440\u043E\u043B\u044C \u0431\u0443\u043B\u043E \u0437\u043B\u0430\u043C\u0430\u043D\u043E \u0456 \u0439\u043E\u0433\u043E \u043D\u0435 \u043C\u043E\u0436\u043D\u0430 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438, \u0441\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u0456\u043D\u0448\u0438\u0439 \u043F\u0430\u0440\u043E\u043B\u044C.",
    form_password_pwned__sign_in: void 0,
    form_password_size_in_bytes_exceeded: "\u0412\u0430\u0448 \u043F\u0430\u0440\u043E\u043B\u044C \u043F\u0435\u0440\u0435\u0432\u0438\u0449\u0443\u0454 \u043C\u0430\u043A\u0441\u0438\u043C\u0430\u043B\u044C\u043D\u043E \u0434\u043E\u043F\u0443\u0441\u0442\u0438\u043C\u0443 \u043A\u0456\u043B\u044C\u043A\u0456\u0441\u0442\u044C \u0431\u0430\u0439\u0442\u0456\u0432, \u0441\u043A\u043E\u0440\u043E\u0442\u0456\u0442\u044C \u0439\u043E\u0433\u043E \u0430\u0431\u043E \u0432\u0438\u0434\u0430\u043B\u0456\u0442\u044C \u0434\u0435\u044F\u043A\u0456 \u0441\u043F\u0435\u0446\u0456\u0430\u043B\u044C\u043D\u0456 \u0441\u0438\u043C\u0432\u043E\u043B\u0438.",
    form_password_validation_failed: "\u041D\u0435\u0432\u0456\u0440\u043D\u0438\u0439 \u043F\u0430\u0440\u043E\u043B\u044C",
    form_username_invalid_character: void 0,
    form_username_invalid_length: void 0,
    identification_deletion_failed: "You cannot delete your last identification.",
    not_allowed_access: "\u0410\u0434\u0440\u0435\u0441\u0430 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u043E\u0457 \u043F\u043E\u0448\u0442\u0438 \u0430\u0431\u043E \u043D\u043E\u043C\u0435\u0440 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443 \u043D\u0435 \u0434\u043E\u0437\u0432\u043E\u043B\u0435\u043D\u043E \u0434\u043B\u044F \u0440\u0435\u0454\u0441\u0442\u0440\u0430\u0446\u0456\u0457. \u0426\u0435 \u043C\u043E\u0436\u0435 \u0431\u0443\u0442\u0438 \u043F\u043E\u0432'\u044F\u0437\u0430\u043D\u043E \u0437 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u043D\u044F\u043C '+', '=', '#' \u0430\u0431\u043E '.' \u0432 \u0430\u0434\u0440\u0435\u0441\u0456 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u043E\u0457 \u043F\u043E\u0448\u0442\u0438, \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u043D\u044F\u043C \u0434\u043E\u043C\u0435\u043D\u0443, \u043F\u043E\u0432'\u044F\u0437\u0430\u043D\u043E\u0433\u043E \u0437 \u0442\u0438\u043C\u0447\u0430\u0441\u043E\u0432\u043E\u044E \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u043E\u044E \u043F\u043E\u0448\u0442\u043E\u044E, \u0430\u0431\u043E \u044F\u0432\u043D\u043E\u0433\u043E \u0432\u0438\u043A\u043B\u044E\u0447\u0435\u043D\u043D\u044F.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: void 0,
    passkey_not_supported: void 0,
    passkey_pa_not_supported: void 0,
    passkey_registration_cancelled: void 0,
    passkey_retrieval_cancelled: void 0,
    passwordComplexity: {
      maximumLength: "\u043C\u0435\u043D\u0448\u0435 {{length}} \u0441\u0438\u043C\u0432\u043E\u043B\u0456\u0432",
      minimumLength: "{{length}} \u0430\u0431\u043E \u0431\u0456\u043B\u044C\u0448\u0435 \u0441\u0438\u043C\u0432\u043E\u043B\u0456\u0432",
      requireLowercase: "\u0431\u0443\u043A\u0432\u0443 \u0432 \u043D\u0438\u0436\u043D\u044C\u043E\u043C\u0443 \u0440\u0435\u0433\u0456\u0441\u0442\u0440\u0456",
      requireNumbers: "\u0446\u0438\u0444\u0440\u0443",
      requireSpecialCharacter: "\u0441\u043F\u0435\u0446\u0456\u0430\u043B\u044C\u043D\u0438\u0439 \u0441\u0438\u043C\u0432\u043E\u043B",
      requireUppercase: "\u0431\u0443\u043A\u0432\u0443 \u0443 \u0432\u0435\u0440\u0445\u043D\u044C\u043E\u043C\u0443 \u0440\u0435\u0433\u0456\u0441\u0442\u0440\u0456",
      sentencePrefix: "\u0412\u0430\u0448 \u043F\u0430\u0440\u043E\u043B\u044C \u043F\u043E\u0432\u0438\u043D\u0435\u043D \u043C\u0456\u0441\u0442\u0438\u0442\u0438"
    },
    phone_number_exists: "\u0426\u0435\u0439 \u043D\u043E\u043C\u0435\u0440 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443 \u0432\u0436\u0435 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0454\u0442\u044C\u0441\u044F. \u0421\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u0456\u043D\u0448\u0438\u0439.",
    session_exists: "\u0412\u0438 \u0432\u0436\u0435 \u0443\u0432\u0456\u0439\u0448\u043B\u0438 \u0432 \u0441\u0438\u0441\u0442\u0435\u043C\u0443.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "\u0412\u0430\u0448 \u043F\u0430\u0440\u043E\u043B\u044C \u043F\u0456\u0434\u0445\u043E\u0434\u0438\u0442\u044C, \u0430\u043B\u0435 \u043C\u0456\u0433 \u0431\u0438 \u0431\u0443\u0442\u0438 \u043D\u0430\u0434\u0456\u0439\u043D\u0456\u0448\u0438\u043C. \u0421\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u0434\u043E\u0434\u0430\u0442\u0438 \u0431\u0456\u043B\u044C\u0448\u0435 \u0441\u0438\u043C\u0432\u043E\u043B\u0456\u0432.",
      goodPassword: "\u0425\u043E\u0440\u043E\u0448\u0430 \u0440\u043E\u0431\u043E\u0442\u0430. \u0426\u0435 \u0432\u0456\u0434\u043C\u0456\u043D\u043D\u0438\u0439 \u043F\u0430\u0440\u043E\u043B\u044C.",
      notEnough: "\u0412\u0430\u0448 \u043F\u0430\u0440\u043E\u043B\u044C \u043D\u0435\u0434\u043E\u0441\u0442\u0430\u0442\u043D\u044C\u043E \u043D\u0430\u0434\u0456\u0439\u043D\u0438\u0439.",
      suggestions: {
        allUppercase: "\u0420\u043E\u0431\u0456\u0442\u044C \u0432\u0435\u043B\u0438\u043A\u0438\u043C\u0438 \u0434\u0435\u044F\u043A\u0456, \u0430\u043B\u0435 \u043D\u0435 \u0432\u0441\u0456 \u0431\u0443\u043A\u0432\u0438.",
        anotherWord: "\u0414\u043E\u0434\u0430\u0439\u0442\u0435 \u0431\u0456\u043B\u044C\u0448\u0435 \u0441\u043B\u0456\u0432, \u044F\u043A\u0456 \u043C\u0435\u043D\u0448 \u043F\u043E\u0448\u0438\u0440\u0435\u043D\u0456.",
        associatedYears: "\u0423\u043D\u0438\u043A\u0430\u0439\u0442\u0435 \u0440\u043E\u043A\u0456\u0432, \u044F\u043A\u0456 \u043F\u043E\u0432'\u044F\u0437\u0430\u043D\u0456 \u0437 \u0432\u0430\u043C\u0438.",
        capitalization: "\u0420\u043E\u0431\u0456\u0442\u044C \u0432\u0435\u043B\u0438\u043A\u0438\u043C\u0438 \u043D\u0435 \u0442\u0456\u043B\u044C\u043A\u0438 \u043F\u0435\u0440\u0448\u0443 \u0431\u0443\u043A\u0432\u0443",
        dates: "\u0423\u043D\u0438\u043A\u0430\u0439\u0442\u0435 \u0434\u0430\u0442 \u0456 \u0440\u043E\u043A\u0456\u0432, \u044F\u043A\u0456 \u043F\u043E\u0432'\u044F\u0437\u0430\u043D\u0456 \u0437 \u0432\u0430\u043C\u0438.",
        l33t: '\u0423\u043D\u0438\u043A\u0430\u0439\u0442\u0435 \u043F\u0435\u0440\u0435\u0434\u0431\u0430\u0447\u0443\u0432\u0430\u043D\u0438\u0445 \u0437\u0430\u043C\u0456\u043D \u0431\u0443\u043A\u0432, \u0442\u0430\u043A\u0438\u0445 \u044F\u043A "@" \u0437\u0430\u043C\u0456\u0441\u0442\u044C "a".',
        longerKeyboardPattern: "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0439\u0442\u0435 \u0434\u043E\u0432\u0448\u0456 \u043F\u043E\u0454\u0434\u043D\u0430\u043D\u043D\u044F \u043A\u043B\u0430\u0432\u0456\u0448 \u0456 \u043A\u0456\u043B\u044C\u043A\u0430 \u0440\u0430\u0437\u0456\u0432 \u0437\u043C\u0456\u043D\u044E\u0439\u0442\u0435 \u043D\u0430\u043F\u0440\u044F\u043C\u043E\u043A \u0432\u0432\u0435\u0434\u0435\u043D\u043D\u044F.",
        noNeed: "\u0412\u0438 \u043C\u043E\u0436\u0435\u0442\u0435 \u0441\u0442\u0432\u043E\u0440\u044E\u0432\u0430\u0442\u0438 \u043D\u0430\u0434\u0456\u0439\u043D\u0456 \u043F\u0430\u0440\u043E\u043B\u0456 \u0431\u0435\u0437 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u043D\u044F \u0441\u0438\u043C\u0432\u043E\u043B\u0456\u0432, \u0446\u0438\u0444\u0440 \u0430\u0431\u043E \u0432\u0435\u043B\u0438\u043A\u0438\u0445 \u043B\u0456\u0442\u0435\u0440.",
        pwned: "\u042F\u043A\u0449\u043E \u0432\u0438 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0454\u0442\u0435 \u0446\u0435\u0439 \u043F\u0430\u0440\u043E\u043B\u044C \u0432 \u0456\u043D\u0448\u043E\u043C\u0443 \u043C\u0456\u0441\u0446\u0456, \u0432\u0430\u043C \u0441\u043B\u0456\u0434 \u0437\u043C\u0456\u043D\u0438\u0442\u0438 \u0439\u043E\u0433\u043E.",
        recentYears: "\u0423\u043D\u0438\u043A\u0430\u0439\u0442\u0435 \u043E\u0441\u0442\u0430\u043D\u043D\u0456\u0445 \u0440\u043E\u043A\u0456\u0432.",
        repeated: "\u0423\u043D\u0438\u043A\u0430\u0439\u0442\u0435 \u043F\u043E\u0432\u0442\u043E\u0440\u044E\u0432\u0430\u043D\u0438\u0445 \u0441\u043B\u0456\u0432 \u0456 \u0441\u0438\u043C\u0432\u043E\u043B\u0456\u0432.",
        reverseWords: "\u0423\u043D\u0438\u043A\u0430\u0439\u0442\u0435 \u0437\u0432\u043E\u0440\u043E\u0442\u043D\u043E\u0433\u043E \u043D\u0430\u043F\u0438\u0441\u0430\u043D\u043D\u044F \u0447\u0430\u0441\u0442\u043E \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u043D\u0438\u0445 \u0441\u043B\u0456\u0432.",
        sequences: "\u0423\u043D\u0438\u043A\u0430\u0439\u0442\u0435 \u0447\u0430\u0441\u0442\u0438\u0445 \u043F\u043E\u0441\u043B\u0456\u0434\u043E\u0432\u043D\u043E\u0441\u0442\u0435\u0439 \u0441\u0438\u043C\u0432\u043E\u043B\u0456\u0432.",
        useWords: "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0439\u0442\u0435 \u043A\u0456\u043B\u044C\u043A\u0430 \u0441\u043B\u0456\u0432, \u0430\u043B\u0435 \u0443\u043D\u0438\u043A\u0430\u0439\u0442\u0435 \u043F\u043E\u0448\u0438\u0440\u0435\u043D\u0438\u0445 \u0444\u0440\u0430\u0437."
      },
      warnings: {
        common: "\u0426\u0435 \u043F\u043E\u0448\u0438\u0440\u0435\u043D\u0438\u0439 \u043F\u0430\u0440\u043E\u043B\u044C.",
        commonNames: "\u041F\u043E\u0448\u0438\u0440\u0435\u043D\u0456 \u0456\u043C\u0435\u043D\u0430 \u0442\u0430 \u043F\u0440\u0456\u0437\u0432\u0438\u0449\u0430 \u043B\u0435\u0433\u043A\u043E \u0432\u0433\u0430\u0434\u0430\u0442\u0438.",
        dates: "\u0414\u0430\u0442\u0438 \u043B\u0435\u0433\u043A\u043E \u0432\u0433\u0430\u0434\u0430\u0442\u0438.",
        extendedRepeat: '\u0428\u0430\u0431\u043B\u043E\u043D\u0438 \u0441\u0438\u043C\u0432\u043E\u043B\u0456\u0432, \u0449\u043E \u043F\u043E\u0432\u0442\u043E\u0440\u044E\u044E\u0442\u044C\u0441\u044F, \u0442\u0430\u043A\u0456 \u044F\u043A "abcabcabcabc", \u043B\u0435\u0433\u043A\u043E \u0432\u0433\u0430\u0434\u0430\u0442\u0438.',
        keyPattern: "\u041A\u043E\u0440\u043E\u0442\u043A\u0456 \u043F\u043E\u0454\u0434\u043D\u0430\u043D\u043D\u044F \u043A\u043B\u0430\u0432\u0456\u0448 \u043B\u0435\u0433\u043A\u043E \u0432\u0433\u0430\u0434\u0430\u0442\u0438.",
        namesByThemselves: "\u041E\u0434\u043D\u0456 \u0456\u043C\u0435\u043D\u0430 \u0430\u0431\u043E \u043F\u0440\u0456\u0437\u0432\u0438\u0449\u0430 \u043B\u0435\u0433\u043A\u043E \u0432\u0433\u0430\u0434\u0430\u0442\u0438.",
        pwned: "\u0412\u0430\u0448 \u043F\u0430\u0440\u043E\u043B\u044C \u0431\u0443\u043B\u043E \u0440\u043E\u0437\u043A\u0440\u0438\u0442\u043E \u0432\u043D\u0430\u0441\u043B\u0456\u0434\u043E\u043A \u0432\u0438\u0442\u043E\u043A\u0443 \u0434\u0430\u043D\u0438\u0445 \u0432 \u0406\u043D\u0442\u0435\u0440\u043D\u0435\u0442\u0456.",
        recentYears: "\u041E\u0441\u0442\u0430\u043D\u043D\u0456 \u0440\u043E\u043A\u0438 \u043B\u0435\u0433\u043A\u043E \u0432\u0433\u0430\u0434\u0430\u0442\u0438.",
        sequences: '\u0427\u0430\u0441\u0442\u0456 \u043F\u043E\u0441\u043B\u0456\u0434\u043E\u0432\u043D\u043E\u0441\u0442\u0456 \u0441\u0438\u043C\u0432\u043E\u043B\u0456\u0432, \u0442\u0430\u043A\u0456 \u044F\u043A "abc", \u043B\u0435\u0433\u043A\u043E \u0432\u0433\u0430\u0434\u0430\u0442\u0438.',
        similarToCommon: "\u0426\u0435\u0439 \u043F\u0430\u0440\u043E\u043B\u044C \u0441\u0445\u043E\u0436\u0438\u0439 \u043D\u0430 \u0447\u0430\u0441\u0442\u043E \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u043D\u0438\u0439 \u043F\u0430\u0440\u043E\u043B\u044C.",
        simpleRepeat: '\u0421\u0438\u043C\u0432\u043E\u043B\u0438, \u0449\u043E \u043F\u043E\u0432\u0442\u043E\u0440\u044E\u044E\u0442\u044C\u0441\u044F, \u0442\u0430\u043A\u0456 \u044F\u043A "aaa", \u043B\u0435\u0433\u043A\u043E \u0432\u0433\u0430\u0434\u0430\u0442\u0438.',
        straightRow: "\u041F\u0440\u044F\u043C\u0456 \u0440\u044F\u0434\u0438 \u043A\u043B\u0430\u0432\u0456\u0448 \u043D\u0430 \u043A\u043B\u0430\u0432\u0456\u0430\u0442\u0443\u0440\u0456 \u043B\u0435\u0433\u043A\u043E \u0432\u0433\u0430\u0434\u0430\u0442\u0438.",
        topHundred: "\u0426\u0435 \u0447\u0430\u0441\u0442\u043E \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u043D\u0438\u0439 \u043F\u0430\u0440\u043E\u043B\u044C.",
        topTen: "\u0426\u0435 \u0434\u0443\u0436\u0435 \u0447\u0430\u0441\u0442\u043E \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u043D\u0438\u0439 \u043F\u0430\u0440\u043E\u043B\u044C.",
        userInputs: "\u041D\u0435 \u043F\u043E\u0432\u0438\u043D\u043D\u043E \u0431\u0443\u0442\u0438 \u043D\u0456\u044F\u043A\u0438\u0445 \u043E\u0441\u043E\u0431\u0438\u0441\u0442\u0438\u0445 \u0434\u0430\u043D\u0438\u0445 \u0430\u0431\u043E \u0434\u0430\u043D\u0438\u0445, \u043F\u043E\u0432'\u044F\u0437\u0430\u043D\u0438\u0445 \u0437\u0456 \u0441\u0442\u043E\u0440\u0456\u043D\u043A\u043E\u044E.",
        wordByItself: "\u041E\u043A\u0440\u0435\u043C\u0456 \u0441\u043B\u043E\u0432\u0430 \u043B\u0435\u0433\u043A\u043E \u0432\u0433\u0430\u0434\u0430\u0442\u0438."
      }
    }
  },
  userButton: {
    action__addAccount: "\u0414\u043E\u0434\u0430\u0442\u0438 \u0430\u043A\u0430\u0443\u043D\u0442",
    action__manageAccount: "\u0423\u043F\u0440\u0430\u0432\u043B\u0456\u043D\u043D\u044F \u0430\u043A\u0430\u0443\u043D\u0442\u043E\u043C",
    action__signOut: "\u0412\u0438\u0439\u0442\u0438",
    action__signOutAll: "\u0412\u0438\u0439\u0442\u0438 \u0437 \u0443\u0441\u0456\u0445 \u0430\u043A\u0430\u0443\u043D\u0442\u0456\u0432"
  },
  userProfile: {
    apiKeysPage: {
      title: void 0
    },
    backupCodePage: {
      actionLabel__copied: "\u0421\u043A\u043E\u043F\u0456\u0439\u043E\u0432\u0430\u043D\u043E!",
      actionLabel__copy: "\u041A\u043E\u043F\u0456\u044E\u0432\u0430\u0442\u0438 \u0432\u0441\u0435",
      actionLabel__download: "\u0417\u0430\u0432\u0430\u043D\u0442\u0430\u0436\u0438\u0442\u0438 .txt",
      actionLabel__print: "\u0414\u0440\u0443\u043A",
      infoText1: "\u0420\u0435\u0437\u0435\u0440\u0432\u043D\u0456 \u043A\u043E\u0434\u0438 \u0431\u0443\u0434\u0443\u0442\u044C \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0456 \u0434\u043B\u044F \u0446\u044C\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443.",
      infoText2: "\u0417\u0431\u0435\u0440\u0456\u0433\u0430\u0439\u0442\u0435 \u0440\u0435\u0437\u0435\u0440\u0432\u043D\u0456 \u043A\u043E\u0434\u0438 \u0432 \u0442\u0430\u0454\u043C\u043D\u0438\u0446\u0456 \u0442\u0430 \u0437\u0431\u0435\u0440\u0456\u0433\u0430\u0439\u0442\u0435 \u0457\u0445 \u0443 \u0431\u0435\u0437\u043F\u0435\u0446\u0456. \u0412\u0438 \u043C\u043E\u0436\u0435\u0442\u0435 \u0441\u0442\u0432\u043E\u0440\u0438\u0442\u0438 \u043D\u043E\u0432\u0456 \u0440\u0435\u0437\u0435\u0440\u0432\u043D\u0456 \u043A\u043E\u0434\u0438, \u044F\u043A\u0449\u043E \u043F\u0456\u0434\u043E\u0437\u0440\u044E\u0454\u0442\u0435, \u0449\u043E \u0432\u043E\u043D\u0438 \u0431\u0443\u043B\u0438 \u0441\u043A\u043E\u043C\u043F\u0440\u043E\u043C\u0435\u0442\u043E\u0432\u0430\u043D\u0456.",
      subtitle__codelist: "\u0417\u0431\u0435\u0440\u0456\u0433\u0430\u0439\u0442\u0435 \u0457\u0445 \u0443 \u0431\u0435\u0437\u043F\u0435\u0446\u0456 \u0442\u0430 \u043D\u0435 \u043F\u043E\u0432\u0456\u0434\u043E\u043C\u043B\u044F\u0439\u0442\u0435 \u043D\u0456\u043A\u043E\u043C\u0443.",
      successMessage: "\u0420\u0435\u0437\u0435\u0440\u0432\u043D\u0456 \u043A\u043E\u0434\u0438 \u0432\u0432\u0456\u043C\u043A\u043D\u0435\u043D\u043E. \u0412\u0438 \u043C\u043E\u0436\u0435\u0442\u0435 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u043E\u0434\u0438\u043D \u0456\u0437 \u0446\u0438\u0445 \u043A\u043E\u0434\u0456\u0432 \u0434\u043B\u044F \u0432\u0445\u043E\u0434\u0443 \u0434\u043E \u0441\u0432\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443, \u044F\u043A\u0449\u043E \u0432\u0438 \u0432\u0442\u0440\u0430\u0442\u0438\u0442\u0435 \u0434\u043E\u0441\u0442\u0443\u043F \u0434\u043E \u0441\u0432\u043E\u0433\u043E \u0430\u0443\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0439\u043D\u043E\u0433\u043E \u043F\u0440\u0438\u0441\u0442\u0440\u043E\u044E. \u041A\u043E\u0436\u0435\u043D \u043A\u043E\u0434 \u043C\u043E\u0436\u0435 \u0431\u0443\u0442\u0438 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u0438\u0439 \u0442\u0456\u043B\u044C\u043A\u0438 \u043E\u0434\u0438\u043D \u0440\u0430\u0437.",
      successSubtitle: "\u0412\u0438 \u043C\u043E\u0436\u0435\u0442\u0435 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u043E\u0434\u0438\u043D \u0456\u0437 \u0446\u0438\u0445 \u043A\u043E\u0434\u0456\u0432 \u0434\u043B\u044F \u0432\u0445\u043E\u0434\u0443 \u0443 \u0441\u0432\u0456\u0439 \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u0438\u0439 \u0437\u0430\u043F\u0438\u0441, \u044F\u043A\u0449\u043E \u0432\u0438 \u0432\u0442\u0440\u0430\u0442\u0438\u0442\u0435 \u0434\u043E\u0441\u0442\u0443\u043F \u0434\u043E \u0441\u0432\u043E\u0433\u043E \u0430\u0443\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0439\u043D\u043E\u0433\u043E \u043F\u0440\u0438\u0441\u0442\u0440\u043E\u044E.",
      title: "\u0414\u043E\u0434\u0430\u0442\u0438 \u0440\u0435\u0437\u0435\u0440\u0432\u043D\u0438\u0439 \u043A\u043E\u0434 \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F",
      title__codelist: "\u0420\u0435\u0437\u0435\u0440\u0432\u043D\u0456 \u043A\u043E\u0434\u0438"
    },
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "\u0412\u0438\u0431\u0435\u0440\u0456\u0442\u044C \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430 \u0434\u043B\u044F \u043F\u0456\u0434\u043A\u043B\u044E\u0447\u0435\u043D\u043D\u044F \u0432\u0430\u0448\u043E\u0433\u043E \u0430\u043A\u0430\u0443\u043D\u0442\u0430.",
      formHint__noAccounts: "\u041D\u0435\u043C\u0430\u0454 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u0438\u0445 \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0456\u0432 \u0437\u043E\u0432\u043D\u0456\u0448\u043D\u0456\u0445 \u0430\u043A\u0430\u0443\u043D\u0442\u0456\u0432.",
      removeResource: {
        messageLine1: "{{identifier}} \u0431\u0443\u0434\u0435 \u0432\u0438\u0434\u0430\u043B\u0435\u043D\u043E \u0437 \u0432\u0430\u0448\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443.",
        messageLine2: "\u0412\u0438 \u0431\u0456\u043B\u044C\u0448\u0435 \u043D\u0435 \u0437\u043C\u043E\u0436\u0435\u0442\u0435 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u0446\u0435\u0439 \u043F\u0456\u0434\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0439 \u0430\u043A\u0430\u0443\u043D\u0442, \u0456 \u0431\u0443\u0434\u044C-\u044F\u043A\u0456 \u0437\u0430\u043B\u0435\u0436\u043D\u0456 \u0444\u0443\u043D\u043A\u0446\u0456\u0457 \u0431\u0456\u043B\u044C\u0448\u0435 \u043D\u0435 \u043F\u0440\u0430\u0446\u044E\u0432\u0430\u0442\u0438\u043C\u0443\u0442\u044C.",
        successMessage: "{{connectedAccount}} \u0431\u0443\u043B\u043E \u0432\u0438\u0434\u0430\u043B\u0435\u043D\u043E \u0437 \u0432\u0430\u0448\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443.",
        title: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u043F\u0456\u0434\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0439 \u0430\u043A\u0430\u0443\u043D\u0442"
      },
      socialButtonsBlockButton: "\u041F\u0456\u0434\u043A\u043B\u044E\u0447\u0438\u0442\u0438 \u0430\u043A\u0430\u0443\u043D\u0442 {{provider|titleize}}",
      successMessage: "\u041F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430 \u0431\u0443\u043B\u043E \u0434\u043E\u0434\u0430\u043D\u043E \u0434\u043E \u0432\u0430\u0448\u043E\u0433\u043E \u0430\u043A\u0430\u0443\u043D\u0442\u0430",
      title: "\u0414\u043E\u0434\u0430\u0442\u0438 \u043F\u0456\u0434\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0439 \u0430\u043A\u0430\u0443\u043D\u0442"
    },
    deletePage: {
      actionDescription: '\u0412\u0432\u0435\u0434\u0456\u0442\u044C "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0430\u043A\u0430\u0443\u043D\u0442" \u043D\u0438\u0436\u0447\u0435, \u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438.',
      confirm: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0430\u043A\u0430\u0443\u043D\u0442",
      messageLine1: "\u0412\u0438 \u0432\u043F\u0435\u0432\u043D\u0435\u043D\u0456, \u0449\u043E \u0445\u043E\u0447\u0435\u0442\u0435 \u0432\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0441\u0432\u0456\u0439 \u0430\u043A\u0430\u0443\u043D\u0442?",
      messageLine2: "\u0426\u044F \u0434\u0456\u044F \u0454 \u043E\u0441\u0442\u0430\u0442\u043E\u0447\u043D\u043E\u044E \u0442\u0430 \u043D\u0435\u0437\u0432\u043E\u0440\u043E\u0442\u043D\u043E\u044E.",
      title: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0430\u043A\u0430\u0443\u043D\u0442"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "\u041D\u0430 \u0446\u044E \u0430\u0434\u0440\u0435\u0441\u0443 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u043E\u0457 \u043F\u043E\u0448\u0442\u0438 \u0431\u0443\u0434\u0435 \u043D\u0430\u0434\u0456\u0441\u043B\u0430\u043D\u043E \u043B\u0438\u0441\u0442 \u0456\u0437 \u0432\u0435\u0440\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0439\u043D\u0438\u043C \u043A\u043E\u0434\u043E\u043C.",
        formSubtitle: "\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u0432\u0435\u0440\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0439\u043D\u0438\u0439 \u043A\u043E\u0434, \u0432\u0456\u0434\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u0439 \u043D\u0430 {{identifier}}",
        formTitle: "\u0412\u0435\u0440\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0439\u043D\u0438\u0439 \u043A\u043E\u0434",
        resendButton: "\u041D\u0430\u0434\u0456\u0441\u043B\u0430\u0442\u0438 \u043A\u043E\u0434 \u043F\u043E\u0432\u0442\u043E\u0440\u043D\u043E",
        successMessage: "\u0410\u0434\u0440\u0435\u0441\u0443 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u043E\u0457 \u043F\u043E\u0448\u0442\u0438 {{identifier}} \u0431\u0443\u043B\u043E \u0434\u043E\u0434\u0430\u043D\u043E \u0434\u043E \u0432\u0430\u0448\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443."
      },
      emailLink: {
        formHint: "\u041D\u0430 \u0446\u044E \u0430\u0434\u0440\u0435\u0441\u0443 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u043E\u0457 \u043F\u043E\u0448\u0442\u0438 \u0431\u0443\u0434\u0435 \u043D\u0430\u0434\u0456\u0441\u043B\u0430\u043D\u043E \u0432\u0435\u0440\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0439\u043D\u0435 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F.",
        formSubtitle: "\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C \u043D\u0430 \u0432\u0435\u0440\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0439\u043D\u0435 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F \u0432 \u043B\u0438\u0441\u0442\u0456, \u0432\u0456\u0434\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u043E\u043C\u0443 \u043D\u0430 {{identifier}}",
        formTitle: "\u0412\u0435\u0440\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0439\u043D\u0435 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F",
        resendButton: "\u041D\u0430\u0434\u0456\u0441\u043B\u0430\u0442\u0438 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F \u043F\u043E\u0432\u0442\u043E\u0440\u043D\u043E",
        successMessage: "\u0410\u0434\u0440\u0435\u0441\u0443 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u043E\u0457 \u043F\u043E\u0448\u0442\u0438 {{identifier}} \u0431\u0443\u043B\u043E \u0434\u043E\u0434\u0430\u043D\u043E \u0434\u043E \u0432\u0430\u0448\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} \u0431\u0443\u0434\u0435 \u0432\u0438\u0434\u0430\u043B\u0435\u043D\u043E \u0437 \u0446\u044C\u043E\u0433\u043E \u0430\u043A\u0430\u0443\u043D\u0442\u0430.",
        messageLine2: "\u0412\u0438 \u0431\u0456\u043B\u044C\u0448\u0435 \u043D\u0435 \u0437\u043C\u043E\u0436\u0435\u0442\u0435 \u0443\u0432\u0456\u0439\u0442\u0438 \u0437 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u043D\u044F\u043C \u0446\u0456\u0454\u0457 \u0430\u0434\u0440\u0435\u0441\u0438 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u043E\u0457 \u043F\u043E\u0448\u0442\u0438.",
        successMessage: "{{emailAddress}} \u0431\u0443\u043B\u043E \u0432\u0438\u0434\u0430\u043B\u0435\u043D\u043E \u0437 \u0432\u0430\u0448\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443.",
        title: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0430\u0434\u0440\u0435\u0441\u0443 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u043E\u0457 \u043F\u043E\u0448\u0442\u0438"
      },
      title: "\u0414\u043E\u0434\u0430\u0442\u0438 \u0430\u0434\u0440\u0435\u0441\u0443 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u043E\u0457 \u043F\u043E\u0448\u0442\u0438",
      verifyTitle: "Verify email address"
    },
    formButtonPrimary__add: "Add",
    formButtonPrimary__continue: "\u041F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438",
    formButtonPrimary__finish: "\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438",
    formButtonPrimary__remove: "Remove",
    formButtonPrimary__save: "Save",
    formButtonReset: "\u0421\u043A\u0430\u0441\u0443\u0432\u0430\u0442\u0438",
    mfaPage: {
      formHint: "\u0412\u0438\u0431\u0435\u0440\u0456\u0442\u044C \u043C\u0435\u0442\u043E\u0434 \u0434\u043B\u044F \u0434\u043E\u0434\u0430\u0432\u0430\u043D\u043D\u044F.",
      title: "\u0414\u043E\u0434\u0430\u0442\u0438 \u0434\u0432\u043E\u0444\u0430\u043A\u0442\u043E\u0440\u043D\u0443 \u0430\u0443\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u044E"
    },
    mfaPhoneCodePage: {
      backButton: "Use existing number",
      primaryButton__addPhoneNumber: "\u0414\u043E\u0434\u0430\u0442\u0438 \u043D\u043E\u043C\u0435\u0440 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443",
      removeResource: {
        messageLine1: "{{identifier}} \u0431\u0456\u043B\u044C\u0448\u0435 \u043D\u0435 \u0431\u0443\u0434\u0435 \u043E\u0442\u0440\u0438\u043C\u0443\u0432\u0430\u0442\u0438 \u043A\u043E\u0434\u0438 \u043F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043D\u043D\u044F \u043F\u0440\u0438 \u0432\u0445\u043E\u0434\u0456 \u0432 \u0441\u0438\u0441\u0442\u0435\u043C\u0443.",
        messageLine2: "\u0412\u0430\u0448 \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u0438\u0439 \u0437\u0430\u043F\u0438\u0441 \u0431\u0443\u0434\u0435 \u043C\u0435\u043D\u0448 \u0437\u0430\u0445\u0438\u0449\u0435\u043D\u0438\u043C. \u0412\u0438 \u0432\u043F\u0435\u0432\u043D\u0435\u043D\u0456, \u0449\u043E \u0445\u043E\u0447\u0435\u0442\u0435 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438?",
        successMessage: "\u0414\u0432\u043E\u0435\u0442\u0430\u043F\u043D\u0430 \u043F\u0435\u0440\u0435\u0432\u0456\u0440\u043A\u0430 \u0437 \u043A\u043E\u0434\u043E\u043C \u0437 SMS \u0431\u0443\u043B\u0430 \u0432\u0438\u0434\u0430\u043B\u0435\u043D\u0430 \u0434\u043B\u044F {{mfaPhoneCode}}",
        title: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0434\u0432\u043E\u0435\u0442\u0430\u043F\u043D\u0443 \u043F\u0435\u0440\u0435\u0432\u0456\u0440\u043A\u0443"
      },
      subtitle__availablePhoneNumbers: "\u0412\u0438\u0431\u0435\u0440\u0456\u0442\u044C \u043D\u043E\u043C\u0435\u0440 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443 \u0434\u043B\u044F \u0440\u0435\u0454\u0441\u0442\u0440\u0430\u0446\u0456\u0457 \u0443 \u0434\u0432\u043E\u0435\u0442\u0430\u043F\u043D\u0456\u0439 \u043F\u0435\u0440\u0435\u0432\u0456\u0440\u0446\u0456 \u0437 \u043A\u043E\u0434\u043E\u043C \u0437 SMS.",
      subtitle__unavailablePhoneNumbers: "\u041D\u0435\u043C\u0430\u0454 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u0438\u0445 \u043D\u043E\u043C\u0435\u0440\u0456\u0432 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443 \u0434\u043B\u044F \u0440\u0435\u0454\u0441\u0442\u0440\u0430\u0446\u0456\u0457 \u0432 \u0434\u0432\u043E\u0435\u0442\u0430\u043F\u043D\u0456\u0439 \u043F\u0435\u0440\u0435\u0432\u0456\u0440\u0446\u0456 \u0437 \u043A\u043E\u0434\u043E\u043C \u0437 SMS.",
      successMessage1: "When signing in, you will need to enter a verification code sent to this phone number as an additional step.",
      successMessage2: "Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.",
      successTitle: "SMS code verification enabled",
      title: "\u0414\u043E\u0434\u0430\u0442\u0438 \u043F\u0435\u0440\u0435\u0432\u0456\u0440\u043A\u0443 \u043A\u043E\u0434\u043E\u043C \u0437 SMS"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "\u0417\u0430\u043C\u0456\u0441\u0442\u044C \u0446\u044C\u043E\u0433\u043E \u0432\u0456\u0434\u0441\u043A\u0430\u043D\u0443\u0439\u0442\u0435 QR-\u043A\u043E\u0434",
        buttonUnableToScan__nonPrimary: "\u041D\u0435 \u0432\u0434\u0430\u0454\u0442\u044C\u0441\u044F \u0432\u0456\u0434\u0441\u043A\u0430\u043D\u0443\u0432\u0430\u0442\u0438 QR-\u043A\u043E\u0434?",
        infoText__ableToScan: "\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0439\u0442\u0435 \u043D\u043E\u0432\u0438\u0439 \u043C\u0435\u0442\u043E\u0434 \u0432\u0445\u043E\u0434\u0443 \u0443 \u0432\u0430\u0448\u043E\u043C\u0443 \u0437\u0430\u0441\u0442\u043E\u0441\u0443\u043D\u043A\u0443 \u0430\u0443\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0457 \u0442\u0430 \u0432\u0456\u0434\u0441\u043A\u0430\u043D\u0443\u0439\u0442\u0435 \u043D\u0430\u0441\u0442\u0443\u043F\u043D\u0438\u0439 QR-\u043A\u043E\u0434, \u0449\u043E\u0431 \u043F\u043E\u0432'\u044F\u0437\u0430\u0442\u0438 \u0439\u043E\u0433\u043E \u0437 \u0432\u0430\u0448\u0438\u043C \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u0438\u043C \u0437\u0430\u043F\u0438\u0441\u043E\u043C.",
        infoText__unableToScan: "\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0439\u0442\u0435 \u043D\u043E\u0432\u0438\u0439 \u043C\u0435\u0442\u043E\u0434 \u0432\u0445\u043E\u0434\u0443 \u0443 \u0432\u0430\u0448\u043E\u043C\u0443 \u0437\u0430\u0441\u0442\u043E\u0441\u0443\u043D\u043A\u0443 \u0430\u0432\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0457 \u0442\u0430 \u0432\u0432\u0435\u0434\u0456\u0442\u044C \u043D\u0438\u0436\u0447\u0435 \u043D\u0430\u0434\u0430\u043D\u0438\u0439 \u043A\u043B\u044E\u0447.",
        inputLabel__unableToScan1: "\u041F\u0435\u0440\u0435\u043A\u043E\u043D\u0430\u0439\u0442\u0435\u0441\u044F, \u0449\u043E \u0432\u0432\u0456\u043C\u043A\u043D\u0435\u043D\u043E \u043E\u0434\u043D\u043E\u0440\u0430\u0437\u043E\u0432\u0456 \u043F\u0430\u0440\u043E\u043B\u0456 \u043D\u0430 \u043E\u0441\u043D\u043E\u0432\u0456 \u0447\u0430\u0441\u0443, \u043F\u043E\u0442\u0456\u043C \u0437\u0430\u0432\u0435\u0440\u0448\u0456\u0442\u044C \u0437\u0432'\u044F\u0437\u0443\u0432\u0430\u043D\u043D\u044F \u0441\u0432\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443.",
        inputLabel__unableToScan2: "\u041A\u0440\u0456\u043C \u0442\u043E\u0433\u043E, \u044F\u043A\u0449\u043E \u0432\u0430\u0448 \u0434\u043E\u0434\u0430\u0442\u043E\u043A \u0430\u0443\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0457 \u043F\u0456\u0434\u0442\u0440\u0438\u043C\u0443\u0454 URI TOTP, \u0432\u0438 \u0442\u0430\u043A\u043E\u0436 \u043C\u043E\u0436\u0435\u0442\u0435 \u0441\u043A\u043E\u043F\u0456\u044E\u0432\u0430\u0442\u0438 \u043F\u043E\u0432\u043D\u0438\u0439 URI."
      },
      removeResource: {
        messageLine1: "\u0412\u0435\u0440\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0439\u043D\u0438\u0439 \u043A\u043E\u0434 \u0456\u0437 \u0446\u044C\u043E\u0433\u043E \u0434\u043E\u0434\u0430\u0442\u043A\u0430 \u0430\u0432\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0457 \u0431\u0456\u043B\u044C\u0448\u0435 \u043D\u0435 \u0431\u0443\u0434\u0435 \u043F\u043E\u0442\u0440\u0456\u0431\u0435\u043D \u043F\u0456\u0434 \u0447\u0430\u0441 \u0432\u0445\u043E\u0434\u0443 \u0432 \u0441\u0438\u0441\u0442\u0435\u043C\u0443.",
        messageLine2: "\u0412\u0430\u0448 \u0430\u043A\u0430\u0443\u043D\u0442 \u0431\u0443\u0434\u0435 \u043C\u0435\u043D\u0448 \u0437\u0430\u0445\u0438\u0449\u0435\u043D\u0438\u043C. \u0412\u0438 \u0432\u043F\u0435\u0432\u043D\u0435\u043D\u0456, \u0449\u043E \u0445\u043E\u0447\u0435\u0442\u0435 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438?",
        successMessage: "\u0414\u0432\u043E\u0435\u0442\u0430\u043F\u043D\u0443 \u0430\u0432\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u044E \u0447\u0435\u0440\u0435\u0437 \u0437\u0430\u0441\u0442\u043E\u0441\u0443\u043D\u043E\u043A \u0430\u0432\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0457 \u0431\u0443\u043B\u043E \u0432\u0438\u0434\u0430\u043B\u0435\u043D\u043E.",
        title: "\u0412\u0438\u0434\u0430\u043B\u0435\u043D\u043D\u044F \u0434\u0432\u043E\u0435\u0442\u0430\u043F\u043D\u043E\u0457 \u0430\u0443\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0457"
      },
      successMessage: "\u0414\u0432\u043E\u0435\u0442\u0430\u043F\u043D\u0430 \u043F\u0435\u0440\u0435\u0432\u0456\u0440\u043A\u0430 \u0432\u0432\u0456\u043C\u043A\u043D\u0435\u043D\u0430. \u041F\u0456\u0434 \u0447\u0430\u0441 \u0432\u0445\u043E\u0434\u0443 \u0432 \u0441\u0438\u0441\u0442\u0435\u043C\u0443 \u0432\u0430\u043C \u043F\u043E\u0442\u0440\u0456\u0431\u043D\u043E \u0431\u0443\u0434\u0435 \u0432\u0432\u0435\u0441\u0442\u0438 \u0432\u0435\u0440\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0439\u043D\u0438\u0439 \u043A\u043E\u0434 \u0456\u0437 \u0446\u044C\u043E\u0433\u043E \u0434\u043E\u0434\u0430\u0442\u043A\u0430 \u044F\u043A \u0434\u043E\u0434\u0430\u0442\u043A\u043E\u0432\u0438\u0439 \u043A\u0440\u043E\u043A.",
      title: "\u0414\u043E\u0434\u0430\u0442\u0438 \u0434\u043E\u0434\u0430\u0442\u043E\u043A \u0430\u0443\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0457",
      verifySubtitle: "\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u0432\u0435\u0440\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0439\u043D\u0438\u0439 \u043A\u043E\u0434, \u0441\u0442\u0432\u043E\u0440\u0435\u043D\u0438\u0439 \u0432\u0430\u0448\u0438\u043C \u0434\u043E\u0434\u0430\u0442\u043A\u043E\u043C \u0430\u0443\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0457",
      verifyTitle: "\u0412\u0435\u0440\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0439\u043D\u0438\u0439 \u043A\u043E\u0434"
    },
    mobileButton__menu: "\u041C\u0435\u043D\u044E",
    navbar: {
      account: "Profile",
      apiKeys: void 0,
      billing: void 0,
      description: "Manage your account info.",
      security: "Security",
      title: "Account"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: void 0,
        title: void 0
      },
      subtitle__rename: void 0,
      title__rename: void 0
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "It is recommended to sign out of all other devices which may have used your old password.",
      readonly: "Your password can currently not be edited because you can sign in only via the enterprise connection.",
      successMessage__set: "\u0412\u0430\u0448 \u043F\u0430\u0440\u043E\u043B\u044C \u0432\u0441\u0442\u0430\u043D\u043E\u0432\u043B\u0435\u043D\u043E.",
      successMessage__signOutOfOtherSessions: "\u0423\u0441\u0456 \u0456\u043D\u0448\u0456 \u043F\u0440\u0438\u0441\u0442\u0440\u043E\u0457 \u0431\u0443\u043B\u0438 \u0432\u0438\u0432\u0435\u0434\u0435\u043D\u0456 \u0456\u0437 \u0441\u0438\u0441\u0442\u0435\u043C\u0438.",
      successMessage__update: "\u0412\u0430\u0448 \u043F\u0430\u0440\u043E\u043B\u044C \u0431\u0443\u043B\u043E \u043E\u043D\u043E\u0432\u043B\u0435\u043D\u043E.",
      title__set: "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0438 \u043F\u0430\u0440\u043E\u043B\u044C",
      title__update: "\u0417\u043C\u0456\u043D\u0438\u0442\u0438 \u043F\u0430\u0440\u043E\u043B\u044C"
    },
    phoneNumberPage: {
      infoText: "\u041D\u0430 \u0446\u0435\u0439 \u043D\u043E\u043C\u0435\u0440 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443 \u0431\u0443\u0434\u0435 \u043D\u0430\u0434\u0456\u0441\u043B\u0430\u043D\u043E \u0442\u0435\u043A\u0441\u0442\u043E\u0432\u0435 \u043F\u043E\u0432\u0456\u0434\u043E\u043C\u043B\u0435\u043D\u043D\u044F \u0437 \u0432\u0435\u0440\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0439\u043D\u0438\u043C \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F\u043C.",
      removeResource: {
        messageLine1: "{{identifier}} \u0431\u0443\u0434\u0435 \u0432\u0438\u0434\u0430\u043B\u0435\u043D\u043E \u0437 \u0446\u044C\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443.",
        messageLine2: "\u0412\u0438 \u0431\u0456\u043B\u044C\u0448\u0435 \u043D\u0435 \u0437\u043C\u043E\u0436\u0435\u0442\u0435 \u0443\u0432\u0456\u0439\u0442\u0438, \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u044E\u0447\u0438 \u0446\u0435\u0439 \u043D\u043E\u043C\u0435\u0440 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443.",
        successMessage: "{{phoneNumber}} \u0431\u0443\u043B\u043E \u0432\u0438\u0434\u0430\u043B\u0435\u043D\u043E \u0437 \u0432\u0430\u0448\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443.",
        title: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u043D\u043E\u043C\u0435\u0440 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443"
      },
      successMessage: "{{identifier}} \u0431\u0443\u043B\u043E \u0434\u043E\u0434\u0430\u043D\u043E \u0434\u043E \u0432\u0430\u0448\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443.",
      title: "\u0414\u043E\u0434\u0430\u0442\u0438 \u043D\u043E\u043C\u0435\u0440 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443",
      verifySubtitle: "Enter the verification code sent to {{identifier}}",
      verifyTitle: "Verify phone number"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "\u0417\u0430\u0432\u0430\u043D\u0442\u0430\u0436\u0442\u0435 \u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u0443 \u0444\u043E\u0440\u043C\u0430\u0442\u0430\u0445 JPG, PNG, GIF \u0430\u0431\u043E WEBP \u0440\u043E\u0437\u043C\u0456\u0440\u043E\u043C \u043C\u0435\u043D\u0448\u0435 10 \u041C\u0411",
      imageFormDestructiveActionSubtitle: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F",
      imageFormSubtitle: "\u0417\u0430\u0432\u0430\u043D\u0442\u0430\u0436\u0438\u0442\u0438 \u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F",
      imageFormTitle: "\u0417\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u043F\u0440\u043E\u0444\u0456\u043B\u044E",
      readonly: "Your profile information has been provided by the enterprise connection and cannot be edited.",
      successMessage: "\u0412\u0430\u0448 \u043F\u0440\u043E\u0444\u0456\u043B\u044C \u0431\u0443\u043B\u043E \u043E\u043D\u043E\u0432\u043B\u0435\u043D\u043E.",
      title: "\u041E\u043D\u043E\u0432\u0438\u0442\u0438 \u043F\u0440\u043E\u0444\u0456\u043B\u044C"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "\u0412\u0438\u0439\u0442\u0438 \u0437 \u043F\u0440\u0438\u0441\u0442\u0440\u043E\u044E",
        title: "\u0410\u043A\u0442\u0438\u0432\u043D\u0456 \u043F\u0440\u0438\u0441\u0442\u0440\u043E\u0457"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "\u0421\u043F\u0440\u043E\u0431\u0443\u0432\u0430\u0442\u0438 \u0437\u043D\u043E\u0432\u0443",
        actionLabel__reauthorize: "\u0410\u0432\u0442\u043E\u0440\u0438\u0437\u0443\u0432\u0430\u0442\u0438 \u0437\u0430\u0440\u0430\u0437",
        destructiveActionTitle: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438",
        primaryButton: "\u041F\u0456\u0434\u043A\u043B\u044E\u0447\u0438\u0442\u0438 \u0430\u043A\u0430\u0443\u043D\u0442",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues",
        title: "\u041F\u0456\u0434\u043A\u043B\u044E\u0447\u0435\u043D\u0456 \u0430\u043A\u0430\u0443\u043D\u0442\u0438"
      },
      dangerSection: {
        deleteAccountButton: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0430\u043A\u0430\u0443\u043D\u0442",
        title: "\u041D\u0435\u0431\u0435\u0437\u043F\u0435\u043A\u0430"
      },
      emailAddressesSection: {
        destructiveAction: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0430\u0434\u0440\u0435\u0441\u0443 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u043E\u0457 \u043F\u043E\u0448\u0442\u0438",
        detailsAction__nonPrimary: "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0438 \u044F\u043A \u043E\u0441\u043D\u043E\u0432\u043D\u0443",
        detailsAction__primary: "\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438 \u043F\u0435\u0440\u0435\u0432\u0456\u0440\u043A\u0443",
        detailsAction__unverified: "\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438 \u043F\u0435\u0440\u0435\u0432\u0456\u0440\u043A\u0443",
        primaryButton: "\u0414\u043E\u0434\u0430\u0442\u0438 \u0430\u0434\u0440\u0435\u0441\u0443 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u043E\u0457 \u043F\u043E\u0448\u0442\u0438",
        title: "\u0410\u0434\u0440\u0435\u0441\u0438 \u0435\u043B\u0435\u043A\u0442\u0440\u043E\u043D\u043D\u043E\u0457 \u043F\u043E\u0448\u0442\u0438"
      },
      enterpriseAccountsSection: {
        title: "Enterprise accounts"
      },
      headerTitle__account: "\u041E\u0431\u043B\u0456\u043A\u043E\u0432\u0438\u0439 \u0437\u0430\u043F\u0438\u0441",
      headerTitle__security: "\u0411\u0435\u0437\u043F\u0435\u043A\u0430",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "\u0417\u0433\u0435\u043D\u0435\u0440\u0443\u0432\u0430\u0442\u0438 \u043A\u043E\u0434\u0438",
          headerTitle: "\u0420\u0435\u0437\u0435\u0440\u0432\u043D\u0456 \u043A\u043E\u0434\u0438",
          subtitle__regenerate: "\u041E\u0442\u0440\u0438\u043C\u0430\u0439\u0442\u0435 \u043D\u043E\u0432\u0438\u0439 \u043D\u0430\u0431\u0456\u0440 \u0431\u0435\u0437\u043F\u0435\u0447\u043D\u0438\u0445 \u0440\u0435\u0437\u0435\u0440\u0432\u043D\u0438\u0445 \u043A\u043E\u0434\u0456\u0432. \u041F\u043E\u043F\u0435\u0440\u0435\u0434\u043D\u0456 \u0440\u0435\u0437\u0435\u0440\u0432\u043D\u0456 \u043A\u043E\u0434\u0438 \u0431\u0443\u0434\u0443\u0442\u044C \u0432\u0438\u0434\u0430\u043B\u0435\u043D\u0456 \u0456 \u043D\u0435 \u043C\u043E\u0436\u0443\u0442\u044C \u0431\u0443\u0442\u0438 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u0456.",
          title__regenerate: "\u0417\u0433\u0435\u043D\u0435\u0440\u0443\u0432\u0430\u0442\u0438 \u043D\u043E\u0432\u0456 \u0440\u0435\u0437\u0435\u0440\u0432\u043D\u0456 \u043A\u043E\u0434\u0438"
        },
        phoneCode: {
          actionLabel__setDefault: "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0438 \u0437\u0430 \u0437\u0430\u043C\u043E\u0432\u0447\u0443\u0432\u0430\u043D\u043D\u044F\u043C",
          destructiveActionLabel: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u043D\u043E\u043C\u0435\u0440 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443"
        },
        primaryButton: "\u0414\u043E\u0434\u0430\u0442\u0438 \u0434\u0432\u043E\u0444\u0430\u043A\u0442\u043E\u0440\u043D\u0443 \u0430\u0443\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u044E",
        title: "\u0414\u0432\u043E\u0444\u0430\u043A\u0442\u043E\u0440\u043D\u0430 \u0430\u0443\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u044F",
        totp: {
          destructiveActionTitle: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438",
          headerTitle: "\u0414\u043E\u0434\u0430\u0442\u043E\u043A \u0430\u0443\u0442\u0435\u043D\u0442\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u0457"
        }
      },
      passkeysSection: {
        menuAction__destructive: void 0,
        menuAction__rename: void 0,
        primaryButton: void 0,
        title: void 0
      },
      passwordSection: {
        primaryButton__setPassword: "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0438 \u043F\u0430\u0440\u043E\u043B\u044C",
        primaryButton__updatePassword: "\u0417\u043C\u0456\u043D\u0438\u0442\u0438 \u043F\u0430\u0440\u043E\u043B\u044C",
        title: "\u041F\u0430\u0440\u043E\u043B\u044C"
      },
      phoneNumbersSection: {
        destructiveAction: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u043D\u043E\u043C\u0435\u0440 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443",
        detailsAction__nonPrimary: "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0438 \u044F\u043A \u043E\u0441\u043D\u043E\u0432\u043D\u0438\u0439",
        detailsAction__primary: "\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438 \u0432\u0435\u0440\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u044E",
        detailsAction__unverified: "\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438 \u0432\u0435\u0440\u0438\u0444\u0456\u043A\u0430\u0446\u0456\u044E",
        primaryButton: "\u0414\u043E\u0434\u0430\u0442\u0438 \u043D\u043E\u043C\u0435\u0440 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0443",
        title: "\u041D\u043E\u043C\u0435\u0440\u0438 \u0442\u0435\u043B\u0435\u0444\u043E\u043D\u0456\u0432"
      },
      profileSection: {
        primaryButton: void 0,
        title: "\u041F\u0440\u043E\u0444\u0456\u043B\u044C"
      },
      usernameSection: {
        primaryButton__setUsername: "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0438 \u0456\u043C'\u044F \u043A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0430",
        primaryButton__updateUsername: "\u0417\u043C\u0456\u043D\u0438\u0442\u0438 \u0456\u043C'\u044F \u043A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0430",
        title: "\u0406\u043C'\u044F \u043A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0430"
      },
      web3WalletsSection: {
        destructiveAction: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0433\u0430\u043C\u0430\u043D\u0435\u0446\u044C",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3 \u0433\u0430\u043C\u0430\u043D\u0446\u0456",
        title: "Web3 \u0433\u0430\u043C\u0430\u043D\u0446\u0456"
      }
    },
    usernamePage: {
      successMessage: "\u0406\u043C'\u044F \u043A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0430 \u0431\u0443\u043B\u043E \u043E\u043D\u043E\u0432\u043B\u0435\u043D\u043E.",
      title__set: "\u041E\u043D\u043E\u0432\u0438\u0442\u0438 \u0456\u043C'\u044F \u043A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0430",
      title__update: "\u041E\u043D\u043E\u0432\u0438\u0442\u0438 \u0456\u043C'\u044F \u043A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0430"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} \u0431\u0443\u0434\u0435 \u0432\u0438\u0434\u0430\u043B\u0435\u043D\u043E \u0437 \u0446\u044C\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443.",
        messageLine2: "\u0412\u0438 \u0431\u0456\u043B\u044C\u0448\u0435 \u043D\u0435 \u0437\u043C\u043E\u0436\u0435\u0442\u0435 \u0423\u0432\u0456\u0439\u0442\u0438 \u0437 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u043D\u044F\u043C \u0446\u044C\u043E\u0433\u043E web3 \u0433\u0430\u043C\u0430\u043D\u0446\u044F.",
        successMessage: "{{web3Wallet}} \u0431\u0443\u043B\u043E \u0432\u0438\u0434\u0430\u043B\u0435\u043D\u043E \u0437 \u0432\u0430\u0448\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443.",
        title: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 web3 \u0433\u0430\u043C\u0430\u043D\u0435\u0446\u044C"
      },
      subtitle__availableWallets: "\u0412\u0438\u0431\u0435\u0440\u0456\u0442\u044C web3 \u0433\u0430\u043C\u0430\u043D\u0435\u0446\u044C \u0434\u043B\u044F \u043F\u0456\u0434\u043A\u043B\u044E\u0447\u0435\u043D\u043D\u044F \u0434\u043E \u0432\u0430\u0448\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443.",
      subtitle__unavailableWallets: "\u041D\u0435\u043C\u0430\u0454 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u0438\u0445 web3 \u0433\u0430\u043C\u0430\u043D\u0446\u0456\u0432.",
      successMessage: "\u0413\u0430\u043C\u0430\u043D\u0435\u0446\u044C \u0431\u0443\u043B\u043E \u0434\u043E\u0434\u0430\u043D\u043E \u0434\u043E \u0432\u0430\u0448\u043E\u0433\u043E \u043E\u0431\u043B\u0456\u043A\u043E\u0432\u043E\u0433\u043E \u0437\u0430\u043F\u0438\u0441\u0443.",
      title: "\u0414\u043E\u0434\u0430\u0442\u0438 web3 \u0433\u0430\u043C\u0430\u043D\u0435\u0446\u044C",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  ukUA
};
//# sourceMappingURL=uk-UA.mjs.map