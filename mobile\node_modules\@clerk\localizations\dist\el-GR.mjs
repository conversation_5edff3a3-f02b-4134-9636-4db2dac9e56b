// src/el-GR.ts
var elGR = {
  locale: "el-GR",
  apiKeys: {
    action__add: void 0,
    action__search: void 0,
    createdAndExpirationStatus__expiresOn: void 0,
    createdAndExpirationStatus__never: void 0,
    detailsTitle__emptyRow: void 0,
    formButtonPrimary__add: void 0,
    formFieldCaption__expiration__expiresOn: void 0,
    formFieldCaption__expiration__never: void 0,
    formFieldOption__expiration__180d: void 0,
    formFieldOption__expiration__1d: void 0,
    formFieldOption__expiration__1y: void 0,
    formFieldOption__expiration__30d: void 0,
    formFieldOption__expiration__60d: void 0,
    formFieldOption__expiration__7d: void 0,
    formFieldOption__expiration__90d: void 0,
    formFieldOption__expiration__never: void 0,
    formHint: void 0,
    formTitle: void 0,
    lastUsed__days: void 0,
    lastUsed__hours: void 0,
    lastUsed__minutes: void 0,
    lastUsed__months: void 0,
    lastUsed__seconds: void 0,
    lastUsed__years: void 0,
    menuAction__revoke: void 0,
    revokeConfirmation: {
      confirmationText: void 0,
      formButtonPrimary__revoke: void 0,
      formHint: void 0,
      formTitle: void 0
    }
  },
  backButton: "\u03A0\u03AF\u03C3\u03C9",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "\u03A0\u03C1\u03BF\u03B5\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "\u0386\u03BB\u03BB\u03B7 \u03C3\u03C5\u03C3\u03BA\u03B5\u03C5\u03AE \u03C5\u03C0\u03BF\u03B4\u03C5\u03CC\u03BC\u03B5\u03BD\u03BF\u03C5",
  badge__primary: "\u039A\u03CD\u03C1\u03B9\u03BF",
  badge__renewsAt: void 0,
  badge__requiresAction: "\u0391\u03C0\u03B1\u03B9\u03C4\u03B5\u03AF \u03B5\u03BD\u03AD\u03C1\u03B3\u03B5\u03B9\u03B1",
  badge__startsAt: void 0,
  badge__thisDevice: "\u0391\u03C5\u03C4\u03AE \u03B7 \u03C3\u03C5\u03C3\u03BA\u03B5\u03C5\u03AE",
  badge__unverified: "\u039C\u03B7 \u03B5\u03C0\u03B1\u03BB\u03B7\u03B8\u03B5\u03C5\u03BC\u03AD\u03BD\u03BF",
  badge__upcomingPlan: void 0,
  badge__userDevice: "\u03A3\u03C5\u03C3\u03BA\u03B5\u03C5\u03AE \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7",
  badge__you: "\u0395\u03C3\u03B5\u03AF\u03C2",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      pastDueNotice: void 0,
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    credit: void 0,
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDue: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "\u0394\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03BF\u03CD",
    invitePage: {
      formButtonReset: "\u03A0\u03B1\u03C1\u03AC\u03BB\u03B5\u03B9\u03C8\u03B7"
    },
    title: "\u0394\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u039F\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03BF\u03CD"
  },
  dates: {
    lastDay: "\u03A7\u03B8\u03B5\u03C2 \u03C3\u03C4\u03B9\u03C2 {{ date | timeString('el') }}",
    next6Days: "{{ date | weekday('el','long') }} \u03C3\u03C4\u03B9\u03C2 {{ date | timeString('el') }}",
    nextDay: "\u0391\u03CD\u03C1\u03B9\u03BF \u03C3\u03C4\u03B9\u03C2 {{ date | timeString('el') }}",
    numeric: "{{ date | numeric('el') }}",
    previous6Days: "\u03A4\u03B5\u03BB\u03B5\u03C5\u03C4\u03B1\u03AF\u03B1 {{ date | weekday('el','long') }} \u03C3\u03C4\u03B9\u03C2 {{ date | timeString('el') }}",
    sameDay: "\u03A3\u03AE\u03BC\u03B5\u03C1\u03B1 \u03C3\u03C4\u03B9\u03C2 {{ date | timeString('el') }}"
  },
  dividerText: "\u03AE",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "\u03A7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03AE\u03C3\u03C4\u03B5 \u03AC\u03BB\u03BB\u03B7 \u03BC\u03AD\u03B8\u03BF\u03B4\u03BF",
  footerPageLink__help: "\u0392\u03BF\u03AE\u03B8\u03B5\u03B9\u03B1",
  footerPageLink__privacy: "\u03A0\u03C1\u03BF\u03C3\u03C4\u03B1\u03C3\u03AF\u03B1 \u03C0\u03C1\u03BF\u03C3\u03C9\u03C0\u03B9\u03BA\u03CE\u03BD \u03B4\u03B5\u03B4\u03BF\u03BC\u03AD\u03BD\u03C9\u03BD",
  footerPageLink__terms: "\u038C\u03C1\u03BF\u03B9",
  formButtonPrimary: "\u03A3\u03C5\u03BD\u03AD\u03C7\u03B5\u03B9\u03B1",
  formButtonPrimary__verify: "Verify",
  formFieldAction__forgotPassword: "\u039E\u03B5\u03C7\u03AC\u03C3\u03B1\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC;",
  formFieldError__matchingPasswords: "\u039F\u03B9 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03AF \u03C4\u03B1\u03B9\u03C1\u03B9\u03AC\u03B6\u03BF\u03C5\u03BD.",
  formFieldError__notMatchingPasswords: "\u039F\u03B9 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03AF \u03B4\u03B5\u03BD \u03C4\u03B1\u03B9\u03C1\u03B9\u03AC\u03B6\u03BF\u03C5\u03BD.",
  formFieldError__verificationLinkExpired: "The verification link expired. Please request a new link.",
  formFieldHintText__optional: "\u03A0\u03C1\u03BF\u03B1\u03B9\u03C1\u03B5\u03C4\u03B9\u03BA\u03CC",
  formFieldHintText__slug: "A slug is a human-readable ID that must be unique. It\u2019s often used in URLs.",
  formFieldInputPlaceholder__apiKeyDescription: void 0,
  formFieldInputPlaceholder__apiKeyExpirationDate: void 0,
  formFieldInputPlaceholder__apiKeyName: void 0,
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Delete account",
  formFieldInputPlaceholder__emailAddress: "\u0395\u03B9\u03C3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03C4\u03B7 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email \u03C3\u03B1\u03C2",
  formFieldInputPlaceholder__emailAddress_username: "\u0395\u03B9\u03C3\u03AC\u03B3\u03B5\u03C4\u03B5 email \u03AE \u03CC\u03BD\u03BF\u03BC\u03B1 \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7",
  formFieldInputPlaceholder__emailAddresses: "\u0395\u03B9\u03C3\u03B1\u03B3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03AE \u03B5\u03C0\u03B9\u03BA\u03BF\u03BB\u03BB\u03AE\u03C3\u03C4\u03B5 \u03BC\u03AF\u03B1 \u03AE \u03C0\u03B5\u03C1\u03B9\u03C3\u03C3\u03CC\u03C4\u03B5\u03C1\u03B5\u03C2 \u03B4\u03B9\u03B5\u03C5\u03B8\u03CD\u03BD\u03C3\u03B5\u03B9\u03C2 email, \u03C7\u03C9\u03C1\u03B9\u03C3\u03BC\u03AD\u03BD\u03B5\u03C2 \u03BC\u03B5 \u03BA\u03B5\u03BD\u03AC \u03AE \u03BA\u03CC\u03BC\u03BC\u03B1\u03C4\u03B1",
  formFieldInputPlaceholder__firstName: "\u038C\u03BD\u03BF\u03BC\u03B1",
  formFieldInputPlaceholder__lastName: "\u0395\u03C0\u03CE\u03BD\u03C5\u03BC\u03BF",
  formFieldInputPlaceholder__organizationDomain: "example.com",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "<EMAIL>",
  formFieldInputPlaceholder__organizationName: "\u038C\u03BD\u03BF\u03BC\u03B1 \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03BF\u03CD",
  formFieldInputPlaceholder__organizationSlug: "my-org",
  formFieldInputPlaceholder__password: "\u0395\u03B9\u03C3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03C3\u03B1\u03C2",
  formFieldInputPlaceholder__phoneNumber: "\u0395\u03B9\u03C3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03B1\u03C1\u03B9\u03B8\u03BC\u03CC \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5 \u03C3\u03B1\u03C2",
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__apiKeyDescription: void 0,
  formFieldLabel__apiKeyExpiration: void 0,
  formFieldLabel__apiKeyName: void 0,
  formFieldLabel__automaticInvitations: "\u0395\u03BD\u03B5\u03C1\u03B3\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7 \u03B1\u03C5\u03C4\u03CC\u03BC\u03B1\u03C4\u03C9\u03BD \u03C0\u03C1\u03BF\u03C3\u03BA\u03BB\u03AE\u03C3\u03B5\u03C9\u03BD \u03B3\u03B9\u03B1 \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03C4\u03BF\u03BC\u03AD\u03B1",
  formFieldLabel__backupCode: "\u0391\u03BD\u03C4\u03AF\u03B3\u03C1\u03B1\u03C6\u03BF \u03B1\u03C3\u03C6\u03B1\u03BB\u03B5\u03AF\u03B1\u03C2 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD",
  formFieldLabel__confirmDeletion: "\u0395\u03C0\u03B9\u03B2\u03B5\u03B2\u03B1\u03AF\u03C9\u03C3\u03B7",
  formFieldLabel__confirmPassword: "\u0395\u03C0\u03B9\u03B2\u03B5\u03B2\u03B1\u03AF\u03C9\u03C3\u03B7 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2",
  formFieldLabel__currentPassword: "\u03A4\u03C1\u03AD\u03C7\u03C9\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2",
  formFieldLabel__emailAddress: "\u0394\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email",
  formFieldLabel__emailAddress_username: "\u0394\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email \u03AE \u03CC\u03BD\u03BF\u03BC\u03B1 \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7",
  formFieldLabel__emailAddresses: "\u0394\u03B9\u03B5\u03C5\u03B8\u03CD\u03BD\u03C3\u03B5\u03B9\u03C2 email",
  formFieldLabel__firstName: "\u038C\u03BD\u03BF\u03BC\u03B1",
  formFieldLabel__lastName: "\u0395\u03C0\u03CE\u03BD\u03C5\u03BC\u03BF",
  formFieldLabel__newPassword: "\u039D\u03AD\u03BF\u03C2 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2",
  formFieldLabel__organizationDomain: "Domain",
  formFieldLabel__organizationDomainDeletePending: "\u0394\u03B9\u03B1\u03B3\u03C1\u03B1\u03C6\u03AE \u03B5\u03BA\u03BA\u03C1\u03B5\u03BC\u03CE\u03BD \u03C0\u03C1\u03BF\u03C3\u03BA\u03BB\u03AE\u03C3\u03B5\u03C9\u03BD \u03BA\u03B1\u03B9 \u03C0\u03C1\u03BF\u03C4\u03AC\u03C3\u03B5\u03C9\u03BD",
  formFieldLabel__organizationDomainEmailAddress: "\u0394\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
  formFieldLabel__organizationDomainEmailAddressDescription: "\u0395\u03B9\u03C3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03BC\u03B9\u03B1 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email \u03C3\u03B5 \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03C4\u03BF\u03BC\u03AD\u03B1 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03BB\u03AC\u03B2\u03B5\u03C4\u03B5 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03BA\u03B1\u03B9 \u03BD\u03B1 \u03B5\u03C0\u03B1\u03BB\u03B7\u03B8\u03B5\u03CD\u03C3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03C4\u03BF\u03BC\u03AD\u03B1.",
  formFieldLabel__organizationName: "\u038C\u03BD\u03BF\u03BC\u03B1 \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03BF\u03CD",
  formFieldLabel__organizationSlug: "\u03A3\u03C5\u03BD\u03C4\u03CC\u03BC\u03B5\u03C5\u03C3\u03B7 URL",
  formFieldLabel__passkeyName: void 0,
  formFieldLabel__password: "\u039A\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2",
  formFieldLabel__phoneNumber: "\u0391\u03C1\u03B9\u03B8\u03BC\u03CC\u03C2 \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5",
  formFieldLabel__role: "\u03A1\u03CC\u03BB\u03BF\u03C2",
  formFieldLabel__signOutOfOtherSessions: "\u0391\u03C0\u03BF\u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7 \u03B1\u03C0\u03CC \u03CC\u03BB\u03B5\u03C2 \u03C4\u03B9\u03C2 \u03AC\u03BB\u03BB\u03B5\u03C2 \u03C3\u03C5\u03C3\u03BA\u03B5\u03C5\u03AD\u03C2",
  formFieldLabel__username: "\u038C\u03BD\u03BF\u03BC\u03B1 \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7",
  impersonationFab: {
    action__signOut: "\u0391\u03C0\u03BF\u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7",
    title: "\u0395\u03AF\u03C3\u03C4\u03B5 \u03C3\u03C5\u03BD\u03B4\u03B5\u03B4\u03B5\u03BC\u03AD\u03BD\u03BF\u03C2 \u03C9\u03C2 {{identifier}}"
  },
  maintenanceMode: void 0,
  membershipRole__admin: "\u0394\u03B9\u03B1\u03C7\u03B5\u03B9\u03C1\u03B9\u03C3\u03C4\u03AE\u03C2",
  membershipRole__basicMember: "\u039C\u03AD\u03BB\u03BF\u03C2",
  membershipRole__guestMember: "\u0395\u03C0\u03B9\u03C3\u03BA\u03AD\u03C0\u03C4\u03B7\u03C2",
  organizationList: {
    action__createOrganization: "\u0394\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03BF\u03CD",
    action__invitationAccept: "\u03A3\u03C5\u03BC\u03BC\u03B5\u03C4\u03BF\u03C7\u03AE",
    action__suggestionsAccept: "\u0391\u03AF\u03C4\u03B7\u03C3\u03B7 \u03C3\u03C5\u03BC\u03BC\u03B5\u03C4\u03BF\u03C7\u03AE\u03C2",
    createOrganization: "\u0394\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u039F\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03BF\u03CD",
    invitationAcceptedLabel: "\u03A3\u03C5\u03BC\u03BC\u03B5\u03C4\u03AD\u03C7\u03B5\u03C4\u03B5",
    subtitle: "\u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF {{applicationName}}",
    suggestionsAcceptedLabel: "\u0395\u03BA\u03BA\u03C1\u03B5\u03BC\u03B5\u03AF \u03AD\u03B3\u03BA\u03C1\u03B9\u03C3\u03B7",
    title: "\u0395\u03C0\u03B9\u03BB\u03AD\u03BE\u03C4\u03B5 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC",
    titleWithoutPersonal: "\u0395\u03C0\u03B9\u03BB\u03AD\u03BE\u03C4\u03B5 \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03CC"
  },
  organizationProfile: {
    apiKeysPage: {
      title: void 0
    },
    badge__automaticInvitation: "\u0391\u03C5\u03C4\u03CC\u03BC\u03B1\u03C4\u03B5\u03C2 \u03C0\u03C1\u03BF\u03C3\u03BA\u03BB\u03AE\u03C3\u03B5\u03B9\u03C2",
    badge__automaticSuggestion: "\u0391\u03C5\u03C4\u03CC\u03BC\u03B1\u03C4\u03B5\u03C2 \u03C0\u03C1\u03BF\u03C4\u03AC\u03C3\u03B5\u03B9\u03C2",
    badge__manualInvitation: "\u03A7\u03C9\u03C1\u03AF\u03C2 \u03B1\u03C5\u03C4\u03CC\u03BC\u03B1\u03C4\u03B7 \u03B5\u03B3\u03B3\u03C1\u03B1\u03C6\u03AE",
    badge__unverified: "\u039C\u03B7 \u03B5\u03C0\u03B1\u03BB\u03B7\u03B8\u03B5\u03C5\u03BC\u03AD\u03BD\u03BF",
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AD\u03C3\u03C4\u03B5 \u03C4\u03BF\u03BD \u03C4\u03BF\u03BC\u03AD\u03B1 \u03B3\u03B9\u03B1 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7. \u03A7\u03C1\u03AE\u03C3\u03C4\u03B5\u03C2 \u03BC\u03B5 \u03B4\u03B9\u03B5\u03C5\u03B8\u03CD\u03BD\u03C3\u03B5\u03B9\u03C2 email \u03C3\u03B5 \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03C4\u03BF\u03BC\u03AD\u03B1 \u03BC\u03C0\u03BF\u03C1\u03BF\u03CD\u03BD \u03BD\u03B1 \u03C3\u03C5\u03BC\u03BC\u03B5\u03C4\u03AC\u03C3\u03C7\u03BF\u03C5\u03BD \u03C3\u03C4\u03BF\u03BD \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03CC \u03B1\u03C5\u03C4\u03CC\u03BC\u03B1\u03C4\u03B1 \u03AE \u03BD\u03B1 \u03B1\u03B9\u03C4\u03B7\u03B8\u03BF\u03CD\u03BD \u03C3\u03C5\u03BC\u03BC\u03B5\u03C4\u03BF\u03C7\u03AE.",
      title: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03C4\u03BF\u03BC\u03AD\u03B1"
    },
    invitePage: {
      detailsTitle__inviteFailed: "\u039F\u03B9 \u03C0\u03C1\u03BF\u03C3\u03BA\u03BB\u03AE\u03C3\u03B5\u03B9\u03C2 \u03B4\u03B5\u03BD \u03BC\u03C0\u03BF\u03C1\u03BF\u03CD\u03C3\u03B1\u03BD \u03BD\u03B1 \u03C3\u03C4\u03B1\u03BB\u03BF\u03CD\u03BD. \u0394\u03B9\u03BF\u03C1\u03B8\u03CE\u03C3\u03C4\u03B5 \u03C4\u03B1 \u03C0\u03B1\u03C1\u03B1\u03BA\u03AC\u03C4\u03C9 \u03C3\u03C4\u03BF\u03B9\u03C7\u03B5\u03AF\u03B1 \u03BA\u03B1\u03B9 \u03B4\u03BF\u03BA\u03B9\u03BC\u03AC\u03C3\u03C4\u03B5 \u03BE\u03B1\u03BD\u03AC:",
      formButtonPrimary__continue: "\u0391\u03C0\u03BF\u03C3\u03C4\u03BF\u03BB\u03AE \u03C0\u03C1\u03BF\u03C3\u03BA\u03BB\u03AE\u03C3\u03B5\u03C9\u03BD",
      selectDropdown__role: "\u0395\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE \u03C1\u03CC\u03BB\u03BF\u03C5",
      subtitle: "\u03A0\u03C1\u03BF\u03C3\u03BA\u03B1\u03BB\u03AD\u03C3\u03C4\u03B5 \u03BD\u03AD\u03B1 \u03BC\u03AD\u03BB\u03B7 \u03C3\u03B5 \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03CC",
      successMessage: "\u039F\u03B9 \u03C0\u03C1\u03BF\u03C3\u03BA\u03BB\u03AE\u03C3\u03B5\u03B9\u03C2 \u03B5\u03C3\u03C4\u03AC\u03BB\u03B7\u03C3\u03B1\u03BD \u03BC\u03B5 \u03B5\u03C0\u03B9\u03C4\u03C5\u03C7\u03AF\u03B1",
      title: "\u03A0\u03C1\u03CC\u03C3\u03BA\u03BB\u03B7\u03C3\u03B7 \u03BC\u03B5\u03BB\u03CE\u03BD"
    },
    membersPage: {
      action__invite: "\u03A0\u03C1\u03CC\u03C3\u03BA\u03BB\u03B7\u03C3\u03B7",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 \u03BC\u03AD\u03BB\u03BF\u03C5\u03C2",
        tableHeader__actions: "\u0395\u03BD\u03AD\u03C1\u03B3\u03B5\u03B9\u03B5\u03C2",
        tableHeader__joined: "\u0395\u03B3\u03B3\u03C1\u03B1\u03C6\u03AE\u03BA\u03B1\u03C4\u03B5",
        tableHeader__role: "\u03A1\u03CC\u03BB\u03BF\u03C2",
        tableHeader__user: "\u03A7\u03C1\u03AE\u03C3\u03C4\u03B7\u03C2"
      },
      detailsTitle__emptyRow: "\u0394\u03B5\u03BD \u03C5\u03C0\u03AC\u03C1\u03C7\u03BF\u03C5\u03BD \u03BC\u03AD\u03BB\u03B7 \u03B3\u03B9\u03B1 \u03B5\u03BC\u03C6\u03AC\u03BD\u03B9\u03C3\u03B7",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Invite users by connecting an email domain with your organization. Anyone who signs up with a matching email domain will be able to join the organization anytime.",
          headerTitle: "Automatic invitations",
          primaryButton: "Manage verified domains"
        },
        table__emptyRow: "No invitations to display"
      },
      invitedMembersTab: {
        menuAction__revoke: "\u0391\u03BD\u03AC\u03BA\u03BB\u03B7\u03C3\u03B7 \u03C0\u03C1\u03CC\u03C3\u03BA\u03BB\u03B7\u03C3\u03B7\u03C2",
        tableHeader__invited: "\u03A0\u03C1\u03BF\u03C3\u03BA\u03B5\u03BA\u03BB\u03B7\u03BC\u03AD\u03BD\u03BF"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Users who sign up with a matching email domain, will be able to see a suggestion to request to join your organization.",
          headerTitle: "Automatic suggestions",
          primaryButton: "Manage verified domains"
        },
        menuAction__approve: "Approve",
        menuAction__reject: "Reject",
        tableHeader__requested: "Requested access",
        table__emptyRow: "No requests to display"
      },
      start: {
        headerTitle__invitations: "Invitations",
        headerTitle__members: "Members",
        headerTitle__requests: "Requests"
      }
    },
    navbar: {
      apiKeys: void 0,
      billing: void 0,
      description: "Manage your organization.",
      general: "General",
      members: "Members",
      title: "Organization"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: "\u03A0\u03BB\u03B7\u03BA\u03C4\u03C1\u03BF\u03BB\u03BF\u03B3\u03AE\u03C3\u03C4\u03B5 \u03C4\u03BF {{organizationName}} \u03C0\u03B1\u03C1\u03B1\u03BA\u03AC\u03C4\u03C9 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5.",
          messageLine1: "\u0395\u03AF\u03C3\u03C4\u03B5 \u03C3\u03AF\u03B3\u03BF\u03C5\u03C1\u03BF\u03C2 \u03CC\u03C4\u03B9 \u03B8\u03AD\u03BB\u03B5\u03C4\u03B5 \u03BD\u03B1 \u03B4\u03B9\u03B1\u03B3\u03C1\u03AC\u03C8\u03B5\u03C4\u03B5 \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03CC;",
          messageLine2: "\u0391\u03C5\u03C4\u03AE \u03B7 \u03B5\u03BD\u03AD\u03C1\u03B3\u03B5\u03B9\u03B1 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03BC\u03CC\u03BD\u03B9\u03BC\u03B7 \u03BA\u03B1\u03B9 \u03BA\u03B1\u03B9 \u03BC\u03B7 \u03B1\u03BD\u03B1\u03C3\u03C4\u03C1\u03AD\u03C8\u03B9\u03BC\u03B7.",
          successMessage: "\u0388\u03C7\u03B5\u03C4\u03B5 \u03B4\u03B9\u03B1\u03B3\u03C1\u03AC\u03C8\u03B5\u03B9 \u03C4\u03BF\u03BD \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03CC.",
          title: "\u0394\u03B9\u03B1\u03B3\u03C1\u03B1\u03C6\u03AE \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03BF\u03CD"
        },
        leaveOrganization: {
          actionDescription: "\u03A0\u03BB\u03B7\u03BA\u03C4\u03C1\u03BF\u03BB\u03BF\u03B3\u03AE\u03C3\u03C4\u03B5 \u03C4\u03BF {{organizationName}} \u03C0\u03B1\u03C1\u03B1\u03BA\u03AC\u03C4\u03C9 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5.",
          messageLine1: "\u0395\u03AF\u03C3\u03C4\u03B5 \u03C3\u03AF\u03B3\u03BF\u03C5\u03C1\u03BF\u03C2 \u03CC\u03C4\u03B9 \u03B8\u03AD\u03BB\u03B5\u03C4\u03B5 \u03BD\u03B1 \u03B1\u03C0\u03BF\u03C7\u03C9\u03C1\u03AE\u03C3\u03B5\u03C4\u03B5 \u03B1\u03C0\u03CC \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03CC; \u0398\u03B1 \u03C7\u03AC\u03C3\u03B5\u03C4\u03B5 \u03C4\u03B7\u03BD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7 \u03C3\u03B5 \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03CC \u03BA\u03B1\u03B9 \u03C4\u03B9\u03C2 \u03B5\u03C6\u03B1\u03C1\u03BC\u03BF\u03B3\u03AD\u03C2 \u03C4\u03BF\u03C5.",
          messageLine2: "\u0391\u03C5\u03C4\u03AE \u03B7 \u03B5\u03BD\u03AD\u03C1\u03B3\u03B5\u03B9\u03B1 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03BC\u03CC\u03BD\u03B9\u03BC\u03B7 \u03BA\u03B1\u03B9 \u03BA\u03B1\u03B9 \u03BC\u03B7 \u03B1\u03BD\u03B1\u03C3\u03C4\u03C1\u03AD\u03C8\u03B9\u03BC\u03B7.",
          successMessage: "\u0388\u03C7\u03B5\u03C4\u03B5 \u03B1\u03C0\u03BF\u03C7\u03C9\u03C1\u03AE\u03C3\u03B5\u03B9 \u03B1\u03C0\u03CC \u03C4\u03BF\u03BD \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03CC.",
          title: "\u0391\u03C0\u03BF\u03C7\u03CE\u03C1\u03B7\u03C3\u03B7 \u03B1\u03C0\u03CC \u03C4\u03BF\u03BD \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03CC"
        },
        title: "\u039A\u03AF\u03BD\u03B4\u03C5\u03BD\u03BF\u03C2"
      },
      domainSection: {
        menuAction__manage: "Manage",
        menuAction__remove: "Delete",
        menuAction__verify: "Verify",
        primaryButton: "Add domain",
        subtitle: "Allow users to join the organization automatically or request to join based on a verified email domain.",
        title: "Verified domains"
      },
      successMessage: "\u039F \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03CC\u03C2 \u03AD\u03C7\u03B5\u03B9 \u03B5\u03BD\u03B7\u03BC\u03B5\u03C1\u03C9\u03B8\u03B5\u03AF.",
      title: "\u03A0\u03C1\u03BF\u03C6\u03AF\u03BB \u039F\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03BF\u03CD"
    },
    removeDomainPage: {
      messageLine1: "The email domain {{domain}} will be removed.",
      messageLine2: "Users won\u2019t be able to join the organization automatically after this.",
      successMessage: "{{domain}} has been removed.",
      title: "Remove domain"
    },
    start: {
      headerTitle__general: "General",
      headerTitle__members: "\u039C\u03AD\u03BB\u03B7",
      profileSection: {
        primaryButton: "\u0395\u03BD\u03B7\u03BC\u03AD\u03C1\u03C9\u03C3\u03B7 \u03C0\u03C1\u03BF\u03C6\u03AF\u03BB",
        title: "Organization Profile",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Removing this domain will affect invited users.",
        removeDomainActionLabel__remove: "Remove domain",
        removeDomainSubtitle: "Remove this domain from your verified domains",
        removeDomainTitle: "Remove domain"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Users are automatically invited to join the organization when they sign-up and can join anytime.",
        automaticInvitationOption__label: "Automatic invitations",
        automaticSuggestionOption__description: "Users receive a suggestion to request to join, but must be approved by an admin before they are able to join the organization.",
        automaticSuggestionOption__label: "Automatic suggestions",
        calloutInfoLabel: "Changing the enrollment mode will only affect new users.",
        calloutInvitationCountLabel: "Pending invitations sent to users: {{count}}",
        calloutSuggestionCountLabel: "Pending suggestions sent to users: {{count}}",
        manualInvitationOption__description: "Users can only be invited manually to the organization.",
        manualInvitationOption__label: "No automatic enrollment",
        subtitle: "Choose how users from this domain can join the organization."
      },
      start: {
        headerTitle__danger: "Danger",
        headerTitle__enrollment: "Enrollment options"
      },
      subtitle: "The domain {{domain}} is now verified. Continue by selecting enrollment mode.",
      title: "Update {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Enter the verification code sent to your email address",
      formTitle: "Verification code",
      resendButton: "Didn't receive a code? Resend",
      subtitle: "The domain {{domainName}} needs to be verified via email.",
      subtitleVerificationCodeScreen: "A verification code was sent to {{emailAddress}}. Enter the code to continue.",
      title: "Verify domain"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "\u0394\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u039F\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03BF\u03CD",
    action__invitationAccept: "Join",
    action__manageOrganization: "\u0394\u03B9\u03B1\u03C7\u03B5\u03AF\u03C1\u03B9\u03C3\u03B7 \u039F\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03BF\u03CD",
    action__suggestionsAccept: "Request to join",
    notSelected: "\u0394\u03B5\u03BD \u03AD\u03C7\u03B5\u03B9 \u03B5\u03C0\u03B9\u03BB\u03B5\u03B3\u03B5\u03AF \u03BF\u03C1\u03B3\u03B1\u03BD\u03B9\u03C3\u03BC\u03CC\u03C2",
    personalWorkspace: "\u03A0\u03C1\u03BF\u03C3\u03C9\u03C0\u03B9\u03BA\u03CC\u03C2 \u03A7\u03CE\u03C1\u03BF\u03C2 \u0395\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1\u03C2",
    suggestionsAcceptedLabel: "Pending approval"
  },
  paginationButton__next: "\u0395\u03C0\u03CC\u03BC\u03B5\u03BD\u03BF",
  paginationButton__previous: "\u03A0\u03C1\u03BF\u03B7\u03B3\u03BF\u03CD\u03BC\u03B5\u03BD\u03BF",
  paginationRowText__displaying: "\u0395\u03BC\u03C6\u03AC\u03BD\u03B9\u03C3\u03B7",
  paginationRowText__of: "\u03B1\u03C0\u03CC",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Add account",
      action__signOutAll: "Sign out of all accounts",
      subtitle: "Select the account with which you wish to continue.",
      title: "Choose an account"
    },
    alternativeMethods: {
      actionLink: "\u039B\u03AE\u03C8\u03B7 \u03B2\u03BF\u03AE\u03B8\u03B5\u03B9\u03B1\u03C2",
      actionText: "\u0394\u03B5\u03BD \u03AD\u03C7\u03B5\u03C4\u03B5 \u03BA\u03B1\u03BD\u03AD\u03BD\u03B1 \u03B1\u03C0\u03CC \u03B1\u03C5\u03C4\u03AC;",
      blockButton__backupCode: "\u03A7\u03C1\u03AE\u03C3\u03B7 \u03B5\u03BD\u03CC\u03C2 \u03B5\u03C6\u03B5\u03B4\u03C1\u03B9\u03BA\u03BF\u03CD \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD",
      blockButton__emailCode: "\u0391\u03C0\u03BF\u03C3\u03C4\u03BF\u03BB\u03AE \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD \u03BC\u03B5 email \u03C3\u03C4\u03BF {{identifier}}",
      blockButton__emailLink: "\u0391\u03C0\u03BF\u03C3\u03C4\u03BF\u03BB\u03AE \u03C3\u03C5\u03BD\u03B4\u03AD\u03C3\u03BC\u03BF\u03C5 \u03C3\u03C4\u03BF {{identifier}}",
      blockButton__passkey: void 0,
      blockButton__password: "\u03A3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7 \u03BC\u03B5 \u03C4\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2",
      blockButton__phoneCode: "\u0391\u03C0\u03BF\u03C3\u03C4\u03BF\u03BB\u03AE \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD SMS \u03C3\u03C4\u03BF {{identifier}}",
      blockButton__totp: "\u03A7\u03C1\u03AE\u03C3\u03B7 \u03C4\u03B7\u03C2 \u03B5\u03C6\u03B1\u03C1\u03BC\u03BF\u03B3\u03AE\u03C2 \u03B1\u03C5\u03B8\u03B5\u03BD\u03C4\u03B9\u03BA\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7\u03C2",
      getHelp: {
        blockButton__emailSupport: "\u03A5\u03C0\u03BF\u03C3\u03C4\u03AE\u03C1\u03B9\u03BE\u03B7 \u03BC\u03AD\u03C3\u03C9 email",
        content: "\u0395\u03AC\u03BD \u03B1\u03BD\u03C4\u03B9\u03BC\u03B5\u03C4\u03C9\u03C0\u03AF\u03B6\u03B5\u03C4\u03B5 \u03B4\u03C5\u03C3\u03BA\u03BF\u03BB\u03AF\u03B1 \u03C3\u03C4\u03B7 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7 \u03C3\u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2, \u03C3\u03C4\u03B5\u03AF\u03BB\u03C4\u03B5 \u03BC\u03B1\u03C2 email \u03BA\u03B1\u03B9 \u03B8\u03B1 \u03B5\u03C0\u03B9\u03BA\u03BF\u03B9\u03BD\u03C9\u03BD\u03AE\u03C3\u03BF\u03C5\u03BC\u03B5 \u03BC\u03B1\u03B6\u03AF \u03C3\u03B1\u03C2 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03B1\u03C0\u03BF\u03BA\u03B1\u03C4\u03B1\u03C3\u03C4\u03AE\u03C3\u03BF\u03C5\u03BC\u03B5 \u03C4\u03B7\u03BD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7 \u03C4\u03BF \u03C3\u03C5\u03BD\u03C4\u03BF\u03BC\u03CC\u03C4\u03B5\u03C1\u03BF \u03B4\u03C5\u03BD\u03B1\u03C4\u03CC\u03BD.",
        title: "\u039B\u03AE\u03C8\u03B7 \u03B2\u03BF\u03AE\u03B8\u03B5\u03B9\u03B1\u03C2"
      },
      subtitle: "\u0391\u03BD\u03C4\u03B9\u03BC\u03B5\u03C4\u03C9\u03C0\u03AF\u03B6\u03B5\u03C4\u03B5 \u03B4\u03C5\u03C3\u03BA\u03BF\u03BB\u03AF\u03B5\u03C2; \u039C\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03AE\u03C3\u03B5\u03C4\u03B5 \u03BF\u03C0\u03BF\u03B9\u03B1\u03B4\u03AE\u03C0\u03BF\u03C4\u03B5 \u03B1\u03C0\u03CC \u03B1\u03C5\u03C4\u03AD\u03C2 \u03C4\u03B9\u03C2 \u03BC\u03B5\u03B8\u03CC\u03B4\u03BF\u03C5\u03C2 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B4\u03B5\u03B8\u03B5\u03AF\u03C4\u03B5.",
      title: "\u03A7\u03C1\u03AE\u03C3\u03B7 \u03BC\u03B9\u03B1\u03C2 \u03AC\u03BB\u03BB\u03B7\u03C2 \u03BC\u03B5\u03B8\u03CC\u03B4\u03BF\u03C5"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "\u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF {{applicationName}}",
      title: "\u0395\u03B9\u03C3\u03B1\u03B3\u03C9\u03B3\u03AE \u03B5\u03BD\u03CC\u03C2 \u03B5\u03C6\u03B5\u03B4\u03C1\u03B9\u03BA\u03BF\u03CD \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD"
    },
    emailCode: {
      formTitle: "\u039A\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
      resendButton: "\u0394\u03B5\u03BD \u03BB\u03AC\u03B2\u03B1\u03C4\u03B5 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC; \u0391\u03C0\u03BF\u03C3\u03C4\u03BF\u03BB\u03AE \u03BE\u03B1\u03BD\u03AC",
      subtitle: "\u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF {{applicationName}}",
      title: "\u0395\u03BB\u03AD\u03B3\u03BE\u03C4\u03B5 \u03C4\u03BF email \u03C3\u03B1\u03C2"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      expired: {
        subtitle: "\u0395\u03C0\u03B9\u03C3\u03C4\u03C1\u03BF\u03C6\u03AE \u03C3\u03C4\u03B7\u03BD \u03B1\u03C1\u03C7\u03B9\u03BA\u03AE \u03BA\u03B1\u03C1\u03C4\u03AD\u03BB\u03B1 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5.",
        title: "\u0391\u03C5\u03C4\u03CC\u03C2 \u03BF \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03AD\u03C7\u03B5\u03B9 \u03BB\u03AE\u03BE\u03B5\u03B9"
      },
      failed: {
        subtitle: "\u0395\u03C0\u03B9\u03C3\u03C4\u03C1\u03BF\u03C6\u03AE \u03C3\u03C4\u03B7\u03BD \u03B1\u03C1\u03C7\u03B9\u03BA\u03AE \u03BA\u03B1\u03C1\u03C4\u03AD\u03BB\u03B1 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5.",
        title: "\u0391\u03C5\u03C4\u03CC\u03C2 \u03BF \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03B4\u03B5\u03BD \u03B5\u03AF\u03BD\u03B1\u03B9 \u03AD\u03B3\u03BA\u03C5\u03C1\u03BF\u03C2"
      },
      formSubtitle: "\u03A7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03AE\u03C3\u03C4\u03B5 \u03C4\u03BF\u03BD \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03C0\u03BF\u03C5 \u03B1\u03C0\u03B5\u03C3\u03C4\u03AC\u03BB\u03B7 \u03C3\u03C4\u03BF email \u03C3\u03B1\u03C2",
      formTitle: "\u03A3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
      loading: {
        subtitle: "\u0398\u03B1 \u03B1\u03BD\u03B1\u03BA\u03B1\u03C4\u03B5\u03C5\u03B8\u03C5\u03BD\u03B8\u03B5\u03AF\u03C4\u03B5 \u03C3\u03CD\u03BD\u03C4\u03BF\u03BC\u03B1",
        title: "\u03A3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7 \u03C3\u03B5 \u03B5\u03BE\u03AD\u03BB\u03B9\u03BE\u03B7..."
      },
      resendButton: "\u0394\u03B5\u03BD \u03BB\u03AC\u03B2\u03B1\u03C4\u03B5 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF; \u0391\u03C0\u03BF\u03C3\u03C4\u03BF\u03BB\u03AE \u03BE\u03B1\u03BD\u03AC",
      subtitle: "\u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF {{applicationName}}",
      title: "\u0395\u03BB\u03AD\u03B3\u03BE\u03C4\u03B5 \u03C4\u03BF email \u03C3\u03B1\u03C2",
      unusedTab: {
        title: "\u039C\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03BA\u03BB\u03B5\u03AF\u03C3\u03B5\u03C4\u03B5 \u03B1\u03C5\u03C4\u03AE\u03BD \u03C4\u03B7\u03BD \u03BA\u03B1\u03C1\u03C4\u03AD\u03BB\u03B1"
      },
      verified: {
        subtitle: "\u0398\u03B1 \u03B1\u03BD\u03B1\u03BA\u03B1\u03C4\u03B5\u03C5\u03B8\u03C5\u03BD\u03B8\u03B5\u03AF\u03C4\u03B5 \u03C3\u03CD\u03BD\u03C4\u03BF\u03BC\u03B1",
        title: "\u0395\u03C0\u03B9\u03C4\u03C5\u03C7\u03AE\u03C2 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7"
      },
      verifiedSwitchTab: {
        subtitle: "\u0395\u03C0\u03B9\u03C3\u03C4\u03C1\u03BF\u03C6\u03AE \u03C3\u03C4\u03B7\u03BD \u03B1\u03C1\u03C7\u03B9\u03BA\u03AE \u03BA\u03B1\u03C1\u03C4\u03AD\u03BB\u03B1 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5",
        subtitleNewTab: "\u0395\u03C0\u03B9\u03C3\u03C4\u03C1\u03BF\u03C6\u03AE \u03C3\u03C4\u03B7 \u03BD\u03AD\u03B1 \u03BA\u03B1\u03C1\u03C4\u03AD\u03BB\u03B1 \u03C0\u03BF\u03C5 \u03AC\u03BD\u03BF\u03B9\u03BE\u03B5 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5",
        titleNewTab: "\u0388\u03C7\u03B5\u03C4\u03B5 \u03C3\u03C5\u03BD\u03B4\u03B5\u03B8\u03B5\u03AF \u03C3\u03B5 \u03AC\u03BB\u03BB\u03B7 \u03BA\u03B1\u03C1\u03C4\u03AD\u03BB\u03B1"
      }
    },
    forgotPassword: {
      formTitle: "\u0395\u03C0\u03B1\u03BD\u03B1\u03C6\u03BF\u03C1\u03AC \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2",
      resendButton: "\u0394\u03B5\u03BD \u03BB\u03AC\u03B2\u03B1\u03C4\u03B5 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC; \u0391\u03C0\u03BF\u03C3\u03C4\u03BF\u03BB\u03AE \u03BE\u03B1\u03BD\u03AC",
      subtitle: "\u03B3\u03B9\u03B1 \u03BD\u03B1 \u03B5\u03C0\u03B1\u03BD\u03B1\u03C6\u03AD\u03C1\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03C3\u03B1\u03C2",
      subtitle_email: "\u03A0\u03C1\u03CE\u03C4\u03B1, \u03B5\u03B9\u03C3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03C0\u03BF\u03C5 \u03C3\u03C4\u03AC\u03BB\u03B8\u03B7\u03BA\u03B5 \u03C3\u03C4\u03B7 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email \u03C3\u03B1\u03C2",
      subtitle_phone: "\u03A0\u03C1\u03CE\u03C4\u03B1, \u03B5\u03B9\u03C3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03C0\u03BF\u03C5 \u03C3\u03C4\u03AC\u03BB\u03B8\u03B7\u03BA\u03B5 \u03C3\u03C4\u03BF \u03C4\u03B7\u03BB\u03AD\u03C6\u03C9\u03BD\u03CC \u03C3\u03B1\u03C2",
      title: "\u0395\u03C0\u03B1\u03BD\u03B1\u03C6\u03BF\u03C1\u03AC \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "\u0395\u03C0\u03B1\u03BD\u03B1\u03C6\u03BF\u03C1\u03AC \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2",
      label__alternativeMethods: "\u0389, \u03C3\u03C5\u03BD\u03B4\u03B5\u03B8\u03B5\u03AF\u03C4\u03B5 \u03BC\u03B5 \u03BC\u03B9\u03B1 \u03AC\u03BB\u03BB\u03B7 \u03BC\u03AD\u03B8\u03BF\u03B4\u03BF.",
      title: "\u039E\u03B5\u03C7\u03AC\u03C3\u03B1\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2;"
    },
    noAvailableMethods: {
      message: "\u0394\u03B5\u03BD \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B4\u03C5\u03BD\u03B1\u03C4\u03AE \u03B7 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7. \u0394\u03B5\u03BD \u03C5\u03C0\u03AC\u03C1\u03C7\u03B5\u03B9 \u03B4\u03B9\u03B1\u03B8\u03AD\u03C3\u03B9\u03BC\u03BF\u03C2 \u03C0\u03B1\u03C1\u03AC\u03B3\u03BF\u03BD\u03C4\u03B1\u03C2 \u03B1\u03C5\u03B8\u03B5\u03BD\u03C4\u03B9\u03BA\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7\u03C2.",
      subtitle: "\u03A0\u03C1\u03BF\u03AD\u03BA\u03C5\u03C8\u03B5 \u03C3\u03C6\u03AC\u03BB\u03BC\u03B1",
      title: "\u0394\u03B5\u03BD \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B4\u03C5\u03BD\u03B1\u03C4\u03AE \u03B7 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7"
    },
    passkey: {
      subtitle: "\u0397 \u03C7\u03C1\u03AE\u03C3\u03B7 \u03C4\u03BF\u03C5 passkey \u03C3\u03B1\u03C2 \u03B5\u03C0\u03B9\u03B2\u03B5\u03B2\u03B1\u03B9\u03CE\u03BD\u03B5\u03B9 \u03C4\u03B7\u03BD \u03C4\u03B1\u03C5\u03C4\u03CC\u03C4\u03B7\u03C4\u03AC \u03C3\u03B1\u03C2. \u0397 \u03C3\u03C5\u03C3\u03BA\u03B5\u03C5\u03AE \u03C3\u03B1\u03C2 \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF \u03BD\u03B1 \u03B6\u03B7\u03C4\u03AE\u03C3\u03B5\u03B9 \u03B4\u03B1\u03BA\u03C4\u03C5\u03BB\u03B9\u03BA\u03CC \u03B1\u03C0\u03BF\u03C4\u03CD\u03C0\u03C9\u03BC\u03B1, \u03B1\u03BD\u03B1\u03B3\u03BD\u03CE\u03C1\u03B9\u03C3\u03B7 \u03C0\u03C1\u03BF\u03C3\u03CE\u03C0\u03BF\u03C5 \u03AE \u03BA\u03BB\u03B5\u03AF\u03B4\u03C9\u03BC\u03B1 \u03BF\u03B8\u03CC\u03BD\u03B7\u03C2.",
      title: "\u03A7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03AE\u03C3\u03C4\u03B5 \u03C4\u03BF passkey \u03C3\u03B1\u03C2"
    },
    password: {
      actionLink: "\u03A7\u03C1\u03AE\u03C3\u03B7 \u03AC\u03BB\u03BB\u03B7\u03C2 \u03BC\u03B5\u03B8\u03CC\u03B4\u03BF\u03C5",
      subtitle: "\u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF {{applicationName}}",
      title: "\u0395\u03B9\u03C3\u03B1\u03B3\u03C9\u03B3\u03AE \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2"
    },
    passwordPwned: {
      title: "\u03A0\u03B1\u03C1\u03B1\u03B2\u03B9\u03B1\u03C3\u03BC\u03AD\u03BD\u03BF\u03C2 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2"
    },
    phoneCode: {
      formTitle: "\u039A\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
      resendButton: "\u0394\u03B5\u03BD \u03BB\u03AC\u03B2\u03B1\u03C4\u03B5 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC; \u0391\u03C0\u03BF\u03C3\u03C4\u03BF\u03BB\u03AE \u03BE\u03B1\u03BD\u03AC",
      subtitle: "\u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF {{applicationName}}",
      title: "\u0395\u03BB\u03AD\u03B3\u03BE\u03C4\u03B5 \u03C4\u03BF \u03C4\u03B7\u03BB\u03AD\u03C6\u03C9\u03BD\u03CC \u03C3\u03B1\u03C2"
    },
    phoneCodeMfa: {
      formTitle: "\u039A\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
      resendButton: "\u0394\u03B5\u03BD \u03BB\u03AC\u03B2\u03B1\u03C4\u03B5 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC; \u0391\u03C0\u03BF\u03C3\u03C4\u03BF\u03BB\u03AE \u03BE\u03B1\u03BD\u03AC",
      subtitle: void 0,
      title: "\u0395\u03BB\u03AD\u03B3\u03BE\u03C4\u03B5 \u03C4\u03BF \u03C4\u03B7\u03BB\u03AD\u03C6\u03C9\u03BD\u03CC \u03C3\u03B1\u03C2"
    },
    resetPassword: {
      formButtonPrimary: "\u0395\u03C0\u03B1\u03BD\u03B1\u03C6\u03BF\u03C1\u03AC \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2",
      requiredMessage: "\u03A5\u03C0\u03AC\u03C1\u03C7\u03B5\u03B9 \u03AE\u03B4\u03B7 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC\u03C2 \u03BC\u03B5 \u03BC\u03B7 \u03B5\u03C0\u03B1\u03BB\u03B7\u03B8\u03B5\u03C5\u03BC\u03AD\u03BD\u03B7 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email. \u03A0\u03B1\u03C1\u03B1\u03BA\u03B1\u03BB\u03BF\u03CD\u03BC\u03B5 \u03B5\u03C0\u03B1\u03BD\u03B1\u03C6\u03AD\u03C1\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03C3\u03B1\u03C2 \u03B3\u03B9\u03B1 \u03BB\u03CC\u03B3\u03BF\u03C5\u03C2 \u03B1\u03C3\u03C6\u03B1\u03BB\u03B5\u03AF\u03B1\u03C2.",
      successMessage: "\u039F \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2 \u03AD\u03C7\u03B5\u03B9 \u03B1\u03BB\u03BB\u03AC\u03BE\u03B5\u03B9 \u03BC\u03B5 \u03B5\u03C0\u03B9\u03C4\u03C5\u03C7\u03AF\u03B1. \u03A3\u03B1\u03C2 \u03C3\u03C5\u03BD\u03B4\u03AD\u03BF\u03C5\u03BC\u03B5, \u03C0\u03B1\u03C1\u03B1\u03BA\u03B1\u03BB\u03CE \u03C0\u03B5\u03C1\u03B9\u03BC\u03AD\u03BD\u03B5\u03C4\u03B5.",
      title: "\u0395\u03C0\u03B1\u03BD\u03B1\u03C6\u03BF\u03C1\u03AC \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2"
    },
    resetPasswordMfa: {
      detailsLabel: "\u03A0\u03C1\u03AD\u03C0\u03B5\u03B9 \u03BD\u03B1 \u03B5\u03C0\u03B1\u03BB\u03B7\u03B8\u03B5\u03CD\u03C3\u03BF\u03C5\u03BC\u03B5 \u03C4\u03B7\u03BD \u03C4\u03B1\u03C5\u03C4\u03CC\u03C4\u03B7\u03C4\u03AC \u03C3\u03B1\u03C2 \u03C0\u03C1\u03B9\u03BD \u03B5\u03C0\u03B1\u03BD\u03B1\u03C6\u03AD\u03C1\u03BF\u03C5\u03BC\u03B5 \u03C4\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2."
    },
    start: {
      actionLink: "\u0395\u03B3\u03B3\u03C1\u03B1\u03C6\u03AE",
      actionLink__join_waitlist: "\u0395\u03B3\u03B3\u03C1\u03B1\u03C6\u03AE \u03C3\u03C4\u03B7 \u03BB\u03AF\u03C3\u03C4\u03B1 \u03B1\u03BD\u03B1\u03BC\u03BF\u03BD\u03AE\u03C2",
      actionLink__use_email: "\u03A7\u03C1\u03AE\u03C3\u03B7 email",
      actionLink__use_email_username: "\u03A7\u03C1\u03AE\u03C3\u03B7 email \u03AE \u03BF\u03BD\u03CC\u03BC\u03B1\u03C4\u03BF\u03C2 \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7",
      actionLink__use_passkey: "\u03A7\u03C1\u03AE\u03C3\u03B7 passkey",
      actionLink__use_phone: "\u03A7\u03C1\u03AE\u03C3\u03B7 \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5",
      actionLink__use_username: "\u03A7\u03C1\u03AE\u03C3\u03B7 \u03BF\u03BD\u03CC\u03BC\u03B1\u03C4\u03BF\u03C2 \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7",
      actionText: "\u0394\u03B5\u03BD \u03AD\u03C7\u03B5\u03C4\u03B5 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC;",
      actionText__join_waitlist: "\u0398\u03AD\u03BB\u03B5\u03C4\u03B5 \u03C0\u03C1\u03CE\u03B9\u03BC\u03B7 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7;",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "\u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF {{applicationName}}",
      subtitleCombined: void 0,
      title: "\u03A3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7",
      titleCombined: "\u03A3\u03C5\u03BD\u03AD\u03C7\u03B5\u03B9\u03B1 \u03C3\u03C4\u03BF {{applicationName}}"
    },
    totpMfa: {
      formTitle: "\u039A\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
      subtitle: void 0,
      title: "A\u03C5\u03B8\u03B5\u03BD\u03C4\u03B9\u03BA\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7 \u03B4\u03CD\u03BF \u03B2\u03B7\u03BC\u03AC\u03C4\u03C9\u03BD"
    }
  },
  signInEnterPasswordTitle: "\u0395\u03B9\u03C3\u03B1\u03B3\u03C9\u03B3\u03AE \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "\u03A3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7",
      actionText: "\u0388\u03C7\u03B5\u03C4\u03B5 \u03AE\u03B4\u03B7 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC;",
      subtitle: "\u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF {{applicationName}}",
      title: "\u03A3\u03C5\u03BC\u03C0\u03BB\u03B7\u03C1\u03CE\u03C3\u03C4\u03B5 \u03C4\u03B1 \u03B1\u03C0\u03B1\u03C1\u03B1\u03AF\u03C4\u03B7\u03C4\u03B1 \u03C0\u03B5\u03B4\u03AF\u03B1"
    },
    emailCode: {
      formSubtitle: "\u0395\u03B9\u03C3\u03B1\u03B3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03C0\u03BF\u03C5 \u03B1\u03C0\u03B5\u03C3\u03C4\u03AC\u03BB\u03B7 \u03C3\u03C4\u03BF email \u03C3\u03B1\u03C2",
      formTitle: "\u039A\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
      resendButton: "\u0394\u03B5\u03BD \u03BB\u03AC\u03B2\u03B1\u03C4\u03B5 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC; \u0391\u03C0\u03BF\u03C3\u03C4\u03BF\u03BB\u03AE \u03BE\u03B1\u03BD\u03AC",
      subtitle: "\u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF {{applicationName}}",
      title: "\u0395\u03C0\u03B1\u03BB\u03B7\u03B8\u03B5\u03CD\u03C3\u03C4\u03B5 \u03C4\u03BF email \u03C3\u03B1\u03C2"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      formSubtitle: "\u03A7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03AE\u03C3\u03C4\u03B5 \u03C4\u03BF\u03BD \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03C0\u03BF\u03C5 \u03B1\u03C0\u03B5\u03C3\u03C4\u03AC\u03BB\u03B7 \u03C3\u03C4\u03B7 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email \u03C3\u03B1\u03C2",
      formTitle: "\u03A3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
      loading: {
        title: "\u0395\u03B3\u03B3\u03C1\u03B1\u03C6\u03AE \u03C3\u03B5 \u03B5\u03BE\u03AD\u03BB\u03B9\u03BE\u03B7..."
      },
      resendButton: "\u0394\u03B5\u03BD \u03BB\u03AC\u03B2\u03B1\u03C4\u03B5 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF; \u0391\u03C0\u03BF\u03C3\u03C4\u03BF\u03BB\u03AE \u03BE\u03B1\u03BD\u03AC",
      subtitle: "\u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF {{applicationName}}",
      title: "\u0395\u03C0\u03B1\u03BB\u03B7\u03B8\u03B5\u03CD\u03C3\u03C4\u03B5 \u03C4\u03BF email \u03C3\u03B1\u03C2",
      verified: {
        title: "\u0395\u03C0\u03B9\u03C4\u03C5\u03C7\u03AE\u03C2 \u03B5\u03B3\u03B3\u03C1\u03B1\u03C6\u03AE"
      },
      verifiedSwitchTab: {
        subtitle: "\u0395\u03C0\u03B9\u03C3\u03C4\u03C1\u03BF\u03C6\u03AE \u03C3\u03C4\u03B7 \u03BD\u03AD\u03B1 \u03BA\u03B1\u03C1\u03C4\u03AD\u03BB\u03B1 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5",
        subtitleNewTab: "\u0395\u03C0\u03B9\u03C3\u03C4\u03C1\u03BF\u03C6\u03AE \u03C3\u03C4\u03B7\u03BD \u03C0\u03C1\u03BF\u03B7\u03B3\u03BF\u03CD\u03BC\u03B5\u03BD\u03B7 \u03BA\u03B1\u03C1\u03C4\u03AD\u03BB\u03B1 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5",
        title: "\u0395\u03C0\u03B9\u03C4\u03C5\u03C7\u03AE\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7 email"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: '\u03A3\u03C5\u03BC\u03C6\u03C9\u03BD\u03CE \u03BC\u03B5 \u03C4\u03B7\u03BD {{ privacyPolicyLink || link("\u03A0\u03BF\u03BB\u03B9\u03C4\u03B9\u03BA\u03AE \u0391\u03C0\u03BF\u03C1\u03C1\u03AE\u03C4\u03BF\u03C5") }}',
        label__onlyTermsOfService: '\u03A3\u03C5\u03BC\u03C6\u03C9\u03BD\u03CE \u03BC\u03B5 \u03C4\u03BF\u03C5\u03C2 {{ termsOfServiceLink || link("\u038C\u03C1\u03BF\u03C5\u03C2 \u03A7\u03C1\u03AE\u03C3\u03B7\u03C2") }}',
        label__termsOfServiceAndPrivacyPolicy: '\u03A3\u03C5\u03BC\u03C6\u03C9\u03BD\u03CE \u03BC\u03B5 \u03C4\u03BF\u03C5\u03C2 {{ termsOfServiceLink || link("\u038C\u03C1\u03BF\u03C5\u03C2 \u03A7\u03C1\u03AE\u03C3\u03B7\u03C2") }} \u03BA\u03B1\u03B9 \u03C4\u03B7\u03BD {{ privacyPolicyLink || link("\u03A0\u03BF\u03BB\u03B9\u03C4\u03B9\u03BA\u03AE \u0391\u03C0\u03BF\u03C1\u03C1\u03AE\u03C4\u03BF\u03C5") }}'
      },
      continue: {
        subtitle: "\u03A0\u03B1\u03C1\u03B1\u03BA\u03B1\u03BB\u03CE \u03B4\u03B9\u03B1\u03B2\u03AC\u03C3\u03C4\u03B5 \u03BA\u03B1\u03B9 \u03B1\u03C0\u03BF\u03B4\u03B5\u03C7\u03C4\u03B5\u03AF\u03C4\u03B5 \u03C4\u03BF\u03C5\u03C2 \u03CC\u03C1\u03BF\u03C5\u03C2 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5",
        title: "\u039D\u03BF\u03BC\u03B9\u03BA\u03AE \u03C3\u03C5\u03BD\u03B1\u03AF\u03BD\u03B5\u03C3\u03B7"
      }
    },
    phoneCode: {
      formSubtitle: "\u0395\u03B9\u03C3\u03B1\u03B3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03C0\u03BF\u03C5 \u03B1\u03C0\u03B5\u03C3\u03C4\u03AC\u03BB\u03B7 \u03C3\u03C4\u03BF\u03BD \u03B1\u03C1\u03B9\u03B8\u03BC\u03CC \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5 \u03C3\u03B1\u03C2",
      formTitle: "\u039A\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
      resendButton: "\u0394\u03B5\u03BD \u03BB\u03AC\u03B2\u03B1\u03C4\u03B5 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC; \u0391\u03C0\u03BF\u03C3\u03C4\u03BF\u03BB\u03AE \u03BE\u03B1\u03BD\u03AC",
      subtitle: "\u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF {{applicationName}}",
      title: "\u0395\u03C0\u03B1\u03BB\u03B7\u03B8\u03B5\u03CD\u03C3\u03C4\u03B5 \u03C4\u03BF \u03C4\u03B7\u03BB\u03AD\u03C6\u03C9\u03BD\u03CC \u03C3\u03B1\u03C2"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "\u03A3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7",
      actionLink__use_email: void 0,
      actionLink__use_phone: void 0,
      actionText: "\u0388\u03C7\u03B5\u03C4\u03B5 \u03AE\u03B4\u03B7 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC;",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "\u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF {{applicationName}}",
      subtitleCombined: "\u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF {{applicationName}}",
      title: "\u0394\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AE\u03C3\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2",
      titleCombined: "\u0394\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AE\u03C3\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2"
    }
  },
  socialButtonsBlockButton: "\u03A3\u03C5\u03BD\u03AD\u03C7\u03B5\u03B9\u03B1 \u03BC\u03B5 {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "\u0397 \u03B5\u03B3\u03B3\u03C1\u03B1\u03C6\u03AE \u03B1\u03C0\u03AD\u03C4\u03C5\u03C7\u03B5 \u03BB\u03CC\u03B3\u03C9 \u03B1\u03C0\u03BF\u03C4\u03C5\u03C7\u03B7\u03BC\u03AD\u03BD\u03C9\u03BD \u03B5\u03BB\u03AD\u03B3\u03C7\u03C9\u03BD \u03B1\u03C3\u03C6\u03B1\u03BB\u03B5\u03AF\u03B1\u03C2. \u0391\u03BD\u03B1\u03BD\u03B5\u03CE\u03C3\u03C4\u03B5 \u03C4\u03B7 \u03C3\u03B5\u03BB\u03AF\u03B4\u03B1 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03B4\u03BF\u03BA\u03B9\u03BC\u03AC\u03C3\u03B5\u03C4\u03B5 \u03BE\u03B1\u03BD\u03AC \u03AE \u03B5\u03C0\u03B9\u03BA\u03BF\u03B9\u03BD\u03C9\u03BD\u03AE\u03C3\u03C4\u03B5 \u03BC\u03B5 \u03C4\u03BF \u03BA\u03AD\u03BD\u03C4\u03C1\u03BF \u03C5\u03C0\u03BF\u03C3\u03C4\u03AE\u03C1\u03B9\u03BE\u03B7\u03C2 \u03B3\u03B9\u03B1 \u03C0\u03B5\u03C1\u03B9\u03C3\u03C3\u03CC\u03C4\u03B5\u03C1\u03B7 \u03B2\u03BF\u03AE\u03B8\u03B5\u03B9\u03B1.",
    captcha_unavailable: "Sign up unsuccessful due to failed bot validation. Please refresh the page to try again or reach out to support for more assistance.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: void 0,
    form_identifier_exists__phone_number: void 0,
    form_identifier_exists__username: void 0,
    form_identifier_not_found: "\u0394\u03B5\u03BD \u03B2\u03C1\u03AD\u03B8\u03B7\u03BA\u03B5 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC\u03C2 \u03BC\u03B5 \u03B1\u03C5\u03C4\u03AD\u03C2 \u03C4\u03B9\u03C2 \u03BB\u03B5\u03C0\u03C4\u03BF\u03BC\u03AD\u03C1\u03B5\u03B9\u03B5\u03C2.",
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: "\u0397 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email \u03C0\u03C1\u03AD\u03C0\u03B5\u03B9 \u03BD\u03B1 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03BC\u03B9\u03B1 \u03AD\u03B3\u03BA\u03C5\u03C1\u03B7 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email.",
    form_param_format_invalid__phone_number: "Phone number must be in a valid international format",
    form_param_max_length_exceeded__first_name: "First name should not exceed 256 characters.",
    form_param_max_length_exceeded__last_name: "Last name should not exceed 256 characters.",
    form_param_max_length_exceeded__name: "Name should not exceed 256 characters.",
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: void 0,
    form_password_not_strong_enough: "\u039F \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2 \u03B4\u03B5\u03BD \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B1\u03C1\u03BA\u03B5\u03C4\u03AC \u03B9\u03C3\u03C7\u03C5\u03C1\u03CC\u03C2.",
    form_password_pwned: "\u0391\u03C5\u03C4\u03CC\u03C2 \u03BF \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2 \u03AD\u03C7\u03B5\u03B9 \u03B4\u03B9\u03B1\u03C1\u03C1\u03B5\u03CD\u03C3\u03B5\u03B9 online \u03C3\u03C4\u03BF \u03C0\u03B1\u03C1\u03B5\u03BB\u03B8\u03CC\u03BD \u03BA\u03B1\u03B9 \u03B4\u03B5\u03BD \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF \u03BD\u03B1 \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03B7\u03B8\u03B5\u03AF. \u0394\u03BF\u03BA\u03B9\u03BC\u03AC\u03C3\u03C4\u03B5 \u03AD\u03BD\u03B1\u03BD \u03AC\u03BB\u03BB\u03BF \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2 \u03B1\u03BD\u03C4\u03AF \u03B3\u03B9\u03B1 \u03B1\u03C5\u03C4\u03CC\u03BD.",
    form_password_pwned__sign_in: void 0,
    form_password_size_in_bytes_exceeded: "\u039F \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2 \u03AD\u03C7\u03B5\u03B9 \u03C5\u03C0\u03B5\u03C1\u03B2\u03B5\u03AF \u03C4\u03BF \u03BC\u03AD\u03B3\u03B9\u03C3\u03C4\u03BF \u03B1\u03C1\u03B9\u03B8\u03BC\u03CC bytes \u03C0\u03BF\u03C5 \u03B5\u03C0\u03B9\u03C4\u03C1\u03AD\u03C0\u03B5\u03C4\u03B1\u03B9. \u03A0\u03B1\u03C1\u03B1\u03BA\u03B1\u03BB\u03BF\u03CD\u03BC\u03B5, \u03C3\u03C5\u03BD\u03C4\u03BF\u03BC\u03B5\u03CD\u03C3\u03C4\u03B5 \u03C4\u03BF\u03BD \u03AE \u03B1\u03C6\u03B1\u03B9\u03C1\u03AD\u03C3\u03C4\u03B5 \u03BC\u03B5\u03C1\u03B9\u03BA\u03BF\u03CD\u03C2 \u03B5\u03B9\u03B4\u03B9\u03BA\u03BF\u03CD\u03C2 \u03C7\u03B1\u03C1\u03B1\u03BA\u03C4\u03AE\u03C1\u03B5\u03C2.",
    form_password_validation_failed: "\u039B\u03B1\u03BD\u03B8\u03B1\u03C3\u03BC\u03AD\u03BD\u03BF\u03C2 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2",
    form_username_invalid_character: void 0,
    form_username_invalid_length: void 0,
    identification_deletion_failed: "\u0394\u03B5\u03BD \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03B4\u03B9\u03B1\u03B3\u03C1\u03AC\u03C8\u03B5\u03C4\u03B5 \u03C4\u03BF \u03C4\u03B5\u03BB\u03B5\u03C5\u03C4\u03B1\u03AF\u03BF \u03C3\u03C4\u03BF\u03B9\u03C7\u03B5\u03AF\u03BF \u03C4\u03B1\u03C5\u03C4\u03BF\u03C0\u03BF\u03B9\u03B7\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2.",
    not_allowed_access: "\u0397 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email \u03AE \u03C4\u03BF \u03C4\u03B7\u03BB\u03AD\u03C6\u03C9\u03BD\u03BF \u03B4\u03B5\u03BD \u03B5\u03C0\u03B9\u03C4\u03C1\u03AD\u03C0\u03B5\u03C4\u03B1\u03B9 \u03B3\u03B9\u03B1 \u03C4\u03B7\u03BD \u03B5\u03B3\u03B3\u03C1\u03B1\u03C6\u03AE. \u0391\u03C5\u03C4\u03CC \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF \u03BD\u03B1 \u03BF\u03C6\u03B5\u03AF\u03BB\u03B5\u03C4\u03B1\u03B9 \u03C3\u03C4\u03B7 \u03C7\u03C1\u03AE\u03C3\u03B7 '+', '=', '#' \u03AE '.' \u03C3\u03C4\u03B7\u03BD \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email \u03C3\u03B1\u03C2, \u03C7\u03C1\u03AE\u03C3\u03B7 \u03C0\u03B5\u03B4\u03AF\u03BF\u03C5 \u03C0\u03BF\u03C5 \u03C3\u03C5\u03BD\u03B4\u03AD\u03B5\u03C4\u03B1\u03B9 \u03BC\u03B5 \u03C5\u03C0\u03B7\u03C1\u03B5\u03C3\u03AF\u03B1 email, \u03AE \u03B5\u03BC\u03C6\u03B1\u03BD\u03AE\u03C2 \u03B1\u03C0\u03BF\u03BA\u03BB\u03B5\u03B9\u03C3\u03BC\u03CC\u03C2. \u0391\u03BD \u03C0\u03B9\u03C3\u03C4\u03B5\u03CD\u03B5\u03C4\u03B5 \u03CC\u03C4\u03B9 \u03B1\u03C5\u03C4\u03CC \u03B5\u03AF\u03BD\u03B1\u03B9 \u03AD\u03BD\u03B1 \u03C3\u03C6\u03AC\u03BB\u03BC\u03B1, \u03C0\u03B1\u03C1\u03B1\u03BA\u03B1\u03BB\u03BF\u03CD\u03BC\u03B5 \u03B5\u03C0\u03B9\u03BA\u03BF\u03B9\u03BD\u03C9\u03BD\u03AE\u03C3\u03C4\u03B5 \u03BC\u03B5 \u03C4\u03B7\u03BD \u03C5\u03C0\u03BF\u03C3\u03C4\u03AE\u03C1\u03B9\u03BE\u03B7.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: void 0,
    passkey_not_supported: void 0,
    passkey_pa_not_supported: void 0,
    passkey_registration_cancelled: void 0,
    passkey_retrieval_cancelled: void 0,
    passwordComplexity: {
      maximumLength: "\u03BB\u03B9\u03B3\u03CC\u03C4\u03B5\u03C1\u03BF\u03C5\u03C2 \u03B1\u03C0\u03CC {{length}} \u03C7\u03B1\u03C1\u03B1\u03BA\u03C4\u03AE\u03C1\u03B5\u03C2",
      minimumLength: "{{length}} \u03AE \u03C0\u03B5\u03C1\u03B9\u03C3\u03C3\u03CC\u03C4\u03B5\u03C1\u03BF\u03C5\u03C2 \u03C7\u03B1\u03C1\u03B1\u03BA\u03C4\u03AE\u03C1\u03B5\u03C2",
      requireLowercase: "\u03AD\u03BD\u03B1 \u03C0\u03B5\u03B6\u03CC \u03B3\u03C1\u03AC\u03BC\u03BC\u03B1",
      requireNumbers: "\u03AD\u03BD\u03B1\u03BD \u03B1\u03C1\u03B9\u03B8\u03BC\u03CC",
      requireSpecialCharacter: "\u03AD\u03BD\u03B1 \u03B5\u03B9\u03B4\u03B9\u03BA\u03CC \u03C7\u03B1\u03C1\u03B1\u03BA\u03C4\u03AE\u03C1\u03B1",
      requireUppercase: "\u03AD\u03BD\u03B1 \u03BA\u03B5\u03C6\u03B1\u03BB\u03B1\u03AF\u03BF \u03B3\u03C1\u03AC\u03BC\u03BC\u03B1",
      sentencePrefix: "\u039F \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2 \u03C0\u03C1\u03AD\u03C0\u03B5\u03B9 \u03BD\u03B1 \u03C0\u03B5\u03C1\u03B9\u03AD\u03C7\u03B5\u03B9"
    },
    phone_number_exists: "\u0391\u03C5\u03C4\u03CC\u03C2 \u03BF \u03B1\u03C1\u03B9\u03B8\u03BC\u03CC\u03C2 \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5 \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03B5\u03AF\u03C4\u03B1\u03B9 \u03AE\u03B4\u03B7. \u0394\u03BF\u03BA\u03B9\u03BC\u03AC\u03C3\u03C4\u03B5 \u03AD\u03BD\u03B1\u03BD \u03AC\u03BB\u03BB\u03BF.",
    session_exists: "\u0388\u03C7\u03B5\u03C4\u03B5 \u03AE\u03B4\u03B7 \u03C3\u03C5\u03BD\u03B4\u03B5\u03B8\u03B5\u03AF.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "\u039F \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B1\u03C1\u03BA\u03B5\u03C4\u03CC\u03C2, \u03B1\u03BB\u03BB\u03AC \u03B8\u03B1 \u03BC\u03C0\u03BF\u03C1\u03BF\u03CD\u03C3\u03B5 \u03BD\u03B1 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03C0\u03B9\u03BF \u03B9\u03C3\u03C7\u03C5\u03C1\u03CC\u03C2. \u0394\u03BF\u03BA\u03B9\u03BC\u03AC\u03C3\u03C4\u03B5 \u03BD\u03B1 \u03C0\u03C1\u03BF\u03C3\u03B8\u03AD\u03C3\u03B5\u03C4\u03B5 \u03C0\u03B5\u03C1\u03B9\u03C3\u03C3\u03CC\u03C4\u03B5\u03C1\u03BF\u03C5\u03C2 \u03C7\u03B1\u03C1\u03B1\u03BA\u03C4\u03AE\u03C1\u03B5\u03C2.",
      goodPassword: "\u039F \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2 \u03C0\u03BB\u03B7\u03C1\u03BF\u03AF \u03CC\u03BB\u03B5\u03C2 \u03C4\u03B9\u03C2 \u03B1\u03C0\u03B1\u03B9\u03C4\u03BF\u03CD\u03BC\u03B5\u03BD\u03B5\u03C2 \u03C0\u03C1\u03BF\u03B4\u03B9\u03B1\u03B3\u03C1\u03B1\u03C6\u03AD\u03C2.",
      notEnough: "\u039F \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2 \u03B4\u03B5\u03BD \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B1\u03C1\u03BA\u03B5\u03C4\u03AC \u03B9\u03C3\u03C7\u03C5\u03C1\u03CC\u03C2.",
      suggestions: {
        allUppercase: "\u0388\u03C7\u03B5\u03C4\u03B5 \u03BC\u03CC\u03BD\u03BF \u03BC\u03B5\u03C1\u03B9\u03BA\u03AC \u03BA\u03B5\u03C6\u03B1\u03BB\u03B1\u03AF\u03B1 \u03B3\u03C1\u03AC\u03BC\u03BC\u03B1\u03C4\u03B1.",
        anotherWord: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AD\u03C3\u03C4\u03B5 \u03C0\u03B5\u03C1\u03B9\u03C3\u03C3\u03CC\u03C4\u03B5\u03C1\u03B5\u03C2 \u03BB\u03AD\u03BE\u03B5\u03B9\u03C2 \u03C0\u03BF\u03C5 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03BB\u03B9\u03B3\u03CC\u03C4\u03B5\u03C1\u03BF \u03C3\u03C5\u03BD\u03B7\u03B8\u03B9\u03C3\u03BC\u03AD\u03BD\u03B5\u03C2.",
        associatedYears: "\u0391\u03C0\u03BF\u03C6\u03CD\u03B3\u03B5\u03C4\u03B5 \u03AD\u03C4\u03B7 \u03C0\u03BF\u03C5 \u03C3\u03B1\u03C2 \u03B1\u03C6\u03BF\u03C1\u03BF\u03CD\u03BD.",
        capitalization: "\u039C\u03B7\u03BD \u03AD\u03C7\u03B5\u03C4\u03B5 \u03BA\u03B5\u03C6\u03B1\u03BB\u03B1\u03AF\u03BF \u03BC\u03CC\u03BD\u03BF \u03C4\u03BF \u03C0\u03C1\u03CE\u03C4\u03BF \u03B3\u03C1\u03AC\u03BC\u03BC\u03B1.",
        dates: "\u0391\u03C0\u03BF\u03C6\u03CD\u03B3\u03B5\u03C4\u03B5 \u03B7\u03BC\u03B5\u03C1\u03BF\u03BC\u03B7\u03BD\u03AF\u03B5\u03C2 \u03BA\u03B1\u03B9 \u03AD\u03C4\u03B7 \u03C0\u03BF\u03C5 \u03C3\u03B1\u03C2 \u03B1\u03C6\u03BF\u03C1\u03BF\u03CD\u03BD.",
        l33t: "\u0391\u03C0\u03BF\u03C6\u03CD\u03B3\u03B5\u03C4\u03B5 \u03C0\u03C1\u03BF\u03B2\u03BB\u03AD\u03C8\u03B9\u03BC\u03B5\u03C2 \u03B1\u03BD\u03C4\u03B9\u03BA\u03B1\u03C4\u03B1\u03C3\u03C4\u03AC\u03C3\u03B5\u03B9\u03C2 \u03B3\u03C1\u03B1\u03BC\u03BC\u03AC\u03C4\u03C9\u03BD \u03CC\u03C0\u03C9\u03C2 '@' \u03B3\u03B9\u03B1 'a'.",
        longerKeyboardPattern: "\u03A7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03AE\u03C3\u03C4\u03B5 \u03BC\u03B5\u03B3\u03B1\u03BB\u03CD\u03C4\u03B5\u03C1\u03B1 \u03BC\u03BF\u03C4\u03AF\u03B2\u03B1 \u03C0\u03BB\u03B7\u03BA\u03C4\u03C1\u03BF\u03BB\u03BF\u03B3\u03AF\u03BF\u03C5 \u03BA\u03B1\u03B9 \u03B1\u03BB\u03BB\u03AC\u03BE\u03C4\u03B5 \u03C0\u03BF\u03BB\u03BB\u03AD\u03C2 \u03C6\u03BF\u03C1\u03AD\u03C2 \u03C4\u03B7\u03BD \u03BA\u03B1\u03C4\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 \u03C0\u03BB\u03B7\u03BA\u03C4\u03C1\u03BF\u03BB\u03CC\u03B3\u03B7\u03C3\u03B7\u03C2.",
        noNeed: "\u039C\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03B4\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AE\u03C3\u03B5\u03C4\u03B5 \u03B9\u03C3\u03C7\u03C5\u03C1\u03BF\u03CD\u03C2 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2 \u03C7\u03C9\u03C1\u03AF\u03C2 \u03C4\u03B7 \u03C7\u03C1\u03AE\u03C3\u03B7 \u03C3\u03C5\u03BC\u03B2\u03CC\u03BB\u03C9\u03BD, \u03B1\u03C1\u03B9\u03B8\u03BC\u03CE\u03BD \u03AE \u03BA\u03B5\u03C6\u03B1\u03BB\u03B1\u03AF\u03C9\u03BD \u03B3\u03C1\u03B1\u03BC\u03BC\u03AC\u03C4\u03C9\u03BD.",
        pwned: "\u0391\u03BD \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03B5\u03AF\u03C4\u03B5 \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2 \u03BA\u03B1\u03B9 \u03B1\u03BB\u03BB\u03BF\u03CD, \u03B8\u03B1 \u03C0\u03C1\u03AD\u03C0\u03B5\u03B9 \u03BD\u03B1 \u03C4\u03BF\u03BD \u03B1\u03BB\u03BB\u03AC\u03BE\u03B5\u03C4\u03B5.",
        recentYears: "\u0391\u03C0\u03BF\u03C6\u03CD\u03B3\u03B5\u03C4\u03B5 \u03C0\u03C1\u03CC\u03C3\u03C6\u03B1\u03C4\u03B1 \u03AD\u03C4\u03B7.",
        repeated: "\u0391\u03C0\u03BF\u03C6\u03CD\u03B3\u03B5\u03C4\u03B5 \u03B5\u03C0\u03B1\u03BD\u03B1\u03BB\u03B1\u03BC\u03B2\u03B1\u03BD\u03CC\u03BC\u03B5\u03BD\u03B5\u03C2 \u03BB\u03AD\u03BE\u03B5\u03B9\u03C2 \u03BA\u03B1\u03B9 \u03C7\u03B1\u03C1\u03B1\u03BA\u03C4\u03AE\u03C1\u03B5\u03C2.",
        reverseWords: "\u0391\u03C0\u03BF\u03C6\u03CD\u03B3\u03B5\u03C4\u03B5 \u03B1\u03BD\u03C4\u03B9\u03C3\u03C4\u03C1\u03BF\u03C6\u03AD\u03C2 \u03C3\u03C5\u03BD\u03B7\u03B8\u03B9\u03C3\u03BC\u03AD\u03BD\u03C9\u03BD \u03BB\u03AD\u03BE\u03B5\u03C9\u03BD.",
        sequences: "\u0391\u03C0\u03BF\u03C6\u03CD\u03B3\u03B5\u03C4\u03B5 \u03BA\u03BF\u03B9\u03BD\u03AD\u03C2 \u03B1\u03BA\u03BF\u03BB\u03BF\u03C5\u03B8\u03AF\u03B5\u03C2 \u03C7\u03B1\u03C1\u03B1\u03BA\u03C4\u03AE\u03C1\u03C9\u03BD.",
        useWords: "\u03A7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03AE\u03C3\u03C4\u03B5 \u03C0\u03BF\u03BB\u03BB\u03AD\u03C2 \u03BB\u03AD\u03BE\u03B5\u03B9\u03C2, \u03B1\u03BB\u03BB\u03AC \u03B1\u03C0\u03BF\u03C6\u03CD\u03B3\u03B5\u03C4\u03B5 \u03BA\u03BF\u03B9\u03BD\u03AD\u03C2 \u03C6\u03C1\u03AC\u03C3\u03B5\u03B9\u03C2."
      },
      warnings: {
        common: "\u0391\u03C5\u03C4\u03CC\u03C2 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03AD\u03BD\u03B1\u03C2 \u03BA\u03BF\u03B9\u03BD\u03CC\u03C2 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2.",
        commonNames: "\u03A3\u03C5\u03BD\u03B7\u03B8\u03B9\u03C3\u03BC\u03AD\u03BD\u03B1 \u03BF\u03BD\u03CC\u03BC\u03B1\u03C4\u03B1 \u03BA\u03B1\u03B9 \u03B5\u03C0\u03CE\u03BD\u03C5\u03BC\u03B1 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B5\u03CD\u03BA\u03BF\u03BB\u03B1 \u03BD\u03B1 \u03BC\u03B1\u03BD\u03C4\u03B5\u03C5\u03C4\u03BF\u03CD\u03BD.",
        dates: "\u0397\u03BC\u03B5\u03C1\u03BF\u03BC\u03B7\u03BD\u03AF\u03B5\u03C2 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B5\u03CD\u03BA\u03BF\u03BB\u03B5\u03C2 \u03BD\u03B1 \u03BC\u03B1\u03BD\u03C4\u03B5\u03C5\u03C4\u03BF\u03CD\u03BD.",
        extendedRepeat: '\u0395\u03C0\u03B1\u03BD\u03B1\u03BB\u03B1\u03BC\u03B2\u03B1\u03BD\u03CC\u03BC\u03B5\u03BD\u03B1 \u03BC\u03BF\u03C4\u03AF\u03B2\u03B1 \u03C7\u03B1\u03C1\u03B1\u03BA\u03C4\u03AE\u03C1\u03C9\u03BD \u03CC\u03C0\u03C9\u03C2 "abcabcabc" \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B5\u03CD\u03BA\u03BF\u03BB\u03B1 \u03BD\u03B1 \u03BC\u03B1\u03BD\u03C4\u03B5\u03C5\u03C4\u03BF\u03CD\u03BD.',
        keyPattern: "\u03A3\u03CD\u03BD\u03C4\u03BF\u03BC\u03B1 \u03BC\u03BF\u03C4\u03AF\u03B2\u03B1 \u03C0\u03BB\u03B7\u03BA\u03C4\u03C1\u03BF\u03BB\u03BF\u03B3\u03AF\u03BF\u03C5 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B5\u03CD\u03BA\u03BF\u03BB\u03B1 \u03BD\u03B1 \u03BC\u03B1\u03BD\u03C4\u03B5\u03C5\u03C4\u03BF\u03CD\u03BD.",
        namesByThemselves: "\u039C\u03B5\u03BC\u03BF\u03BD\u03C9\u03BC\u03AD\u03BD\u03B1 \u03BF\u03BD\u03CC\u03BC\u03B1\u03C4\u03B1 \u03AE \u03B5\u03C0\u03CE\u03BD\u03C5\u03BC\u03B1 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B5\u03CD\u03BA\u03BF\u03BB\u03B1 \u03BD\u03B1 \u03BC\u03B1\u03BD\u03C4\u03B5\u03C5\u03C4\u03BF\u03CD\u03BD.",
        pwned: "\u039F \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2 \u03B1\u03C0\u03BF\u03BA\u03B1\u03BB\u03CD\u03C6\u03B8\u03B7\u03BA\u03B5 \u03B1\u03C0\u03CC \u03C0\u03B1\u03C1\u03B1\u03B2\u03AF\u03B1\u03C3\u03B7 \u03B4\u03B5\u03B4\u03BF\u03BC\u03AD\u03BD\u03C9\u03BD \u03C3\u03C4\u03BF \u03B4\u03B9\u03B1\u03B4\u03AF\u03BA\u03C4\u03C5\u03BF.",
        recentYears: "\u03A0\u03C1\u03CC\u03C3\u03C6\u03B1\u03C4\u03B1 \u03AD\u03C4\u03B7 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B5\u03CD\u03BA\u03BF\u03BB\u03B1 \u03BD\u03B1 \u03BC\u03B1\u03BD\u03C4\u03B5\u03C5\u03C4\u03BF\u03CD\u03BD.",
        sequences: '\u03A3\u03C5\u03BD\u03B7\u03B8\u03B9\u03C3\u03BC\u03AD\u03BD\u03B5\u03C2 \u03B1\u03BA\u03BF\u03BB\u03BF\u03C5\u03B8\u03AF\u03B5\u03C2 \u03C7\u03B1\u03C1\u03B1\u03BA\u03C4\u03AE\u03C1\u03C9\u03BD \u03CC\u03C0\u03C9\u03C2 "abc" \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B5\u03CD\u03BA\u03BF\u03BB\u03B1 \u03BD\u03B1 \u03BC\u03B1\u03BD\u03C4\u03B5\u03C5\u03C4\u03BF\u03CD\u03BD.',
        similarToCommon: "\u0391\u03C5\u03C4\u03CC\u03C2 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03C0\u03B1\u03C1\u03CC\u03BC\u03BF\u03B9\u03BF\u03C2 \u03BC\u03B5 \u03AD\u03BD\u03B1\u03BD \u03BA\u03BF\u03B9\u03BD\u03CC \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2.",
        simpleRepeat: '\u0395\u03C0\u03B1\u03BD\u03B1\u03BB\u03B1\u03BC\u03B2\u03B1\u03BD\u03CC\u03BC\u03B5\u03BD\u03BF\u03B9 \u03C7\u03B1\u03C1\u03B1\u03BA\u03C4\u03AE\u03C1\u03B5\u03C2 \u03CC\u03C0\u03C9\u03C2 "aaa" \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B5\u03CD\u03BA\u03BF\u03BB\u03BF \u03BD\u03B1 \u03BC\u03B1\u03BD\u03C4\u03B5\u03C5\u03C4\u03BF\u03CD\u03BD.',
        straightRow: "\u03A3\u03B5\u03B9\u03C1\u03AD\u03C2 \u03B3\u03C1\u03B1\u03BC\u03BC\u03AC\u03C4\u03C9\u03BD \u03C3\u03C4\u03BF \u03C0\u03BB\u03B7\u03BA\u03C4\u03C1\u03BF\u03BB\u03CC\u03B3\u03B9\u03BF \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B5\u03CD\u03BA\u03BF\u03BB\u03B1 \u03BD\u03B1 \u03BC\u03B1\u03BD\u03C4\u03B5\u03C5\u03C4\u03BF\u03CD\u03BD.",
        topHundred: "\u0391\u03C5\u03C4\u03CC\u03C2 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03AD\u03BD\u03B1\u03C2 \u03C3\u03C5\u03C7\u03BD\u03AC \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03BF\u03CD\u03BC\u03B5\u03BD\u03BF\u03C2 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2.",
        topTen: "\u0391\u03C5\u03C4\u03CC\u03C2 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03AD\u03BD\u03B1\u03C2 \u03C0\u03BF\u03BB\u03CD \u03B4\u03B9\u03B1\u03B4\u03B5\u03B4\u03BF\u03BC\u03AD\u03BD\u03BF\u03C2 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2.",
        userInputs: "\u0394\u03B5\u03BD \u03C0\u03C1\u03AD\u03C0\u03B5\u03B9 \u03BD\u03B1 \u03C5\u03C0\u03AC\u03C1\u03C7\u03BF\u03C5\u03BD \u03C0\u03C1\u03BF\u03C3\u03C9\u03C0\u03B9\u03BA\u03AC \u03AE \u03C3\u03C7\u03B5\u03C4\u03B9\u03BA\u03AC \u03BC\u03B5 \u03C4\u03B7 \u03C3\u03B5\u03BB\u03AF\u03B4\u03B1 \u03B4\u03B5\u03B4\u03BF\u03BC\u03AD\u03BD\u03B1.",
        wordByItself: "\u039F\u03B9 \u03BC\u03B5\u03BC\u03BF\u03BD\u03C9\u03BC\u03AD\u03BD\u03B5\u03C2 \u03BB\u03AD\u03BE\u03B5\u03B9\u03C2 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B5\u03CD\u03BA\u03BF\u03BB\u03B5\u03C2 \u03BD\u03B1 \u03BC\u03B1\u03BD\u03C4\u03B5\u03C5\u03C4\u03BF\u03CD\u03BD."
      }
    }
  },
  userButton: {
    action__addAccount: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03BF\u03CD",
    action__manageAccount: "\u0394\u03B9\u03B1\u03C7\u03B5\u03AF\u03C1\u03B9\u03C3\u03B7 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03BF\u03CD",
    action__signOut: "\u0391\u03C0\u03BF\u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7",
    action__signOutAll: "\u0391\u03C0\u03BF\u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7 \u03B1\u03C0\u03CC \u03CC\u03BB\u03BF\u03C5\u03C2 \u03C4\u03BF\u03C5\u03C2 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03BF\u03CD\u03C2"
  },
  userProfile: {
    apiKeysPage: {
      title: void 0
    },
    backupCodePage: {
      actionLabel__copied: "\u0391\u03BD\u03C4\u03B9\u03B3\u03C1\u03AC\u03C6\u03B7\u03BA\u03B1\u03BD!",
      actionLabel__copy: "\u0391\u03BD\u03C4\u03B9\u03B3\u03C1\u03B1\u03C6\u03AE \u03CC\u03BB\u03C9\u03BD",
      actionLabel__download: "\u039B\u03AE\u03C8\u03B7 .txt",
      actionLabel__print: "\u0395\u03BA\u03C4\u03CD\u03C0\u03C9\u03C3\u03B7",
      infoText1: "\u039F\u03B9 \u03B5\u03C6\u03B5\u03B4\u03C1\u03B9\u03BA\u03BF\u03AF \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03AF \u03B8\u03B1 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B5\u03BD\u03B5\u03C1\u03B3\u03BF\u03C0\u03BF\u03B9\u03B7\u03BC\u03AD\u03BD\u03BF\u03B9 \u03B3\u03B9\u03B1 \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC.",
      infoText2: "\u03A6\u03C5\u03BB\u03AC\u03BE\u03C4\u03B5 \u03C4\u03BF\u03C5\u03C2 \u03B5\u03C6\u03B5\u03B4\u03C1\u03B9\u03BA\u03BF\u03CD\u03C2 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD\u03C2 \u03BC\u03C5\u03C3\u03C4\u03B9\u03BA\u03BF\u03CD\u03C2 \u03BA\u03B1\u03B9 \u03B1\u03C0\u03BF\u03B8\u03B7\u03BA\u03B5\u03CD\u03C3\u03C4\u03B5 \u03C4\u03BF\u03C5\u03C2 \u03BC\u03B5 \u03B1\u03C3\u03C6\u03AC\u03BB\u03B5\u03B9\u03B1. \u039C\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03B4\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AE\u03C3\u03B5\u03C4\u03B5 \u03BD\u03AD\u03BF\u03C5\u03C2 \u03B5\u03C6\u03B5\u03B4\u03C1\u03B9\u03BA\u03BF\u03CD\u03C2 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD\u03C2 \u03B5\u03AC\u03BD \u03C5\u03C0\u03BF\u03C8\u03B9\u03AC\u03B6\u03B5\u03C3\u03C4\u03B5 \u03CC\u03C4\u03B9 \u03AD\u03C7\u03BF\u03C5\u03BD \u03B4\u03B9\u03B1\u03C1\u03C1\u03B5\u03CD\u03C3\u03B5\u03B9.",
      subtitle__codelist: "\u03A6\u03C5\u03BB\u03AC\u03BE\u03C4\u03B5 \u03C4\u03BF\u03C5\u03C2 \u03BC\u03B5 \u03B1\u03C3\u03C6\u03AC\u03BB\u03B5\u03B9\u03B1 \u03BA\u03B1\u03B9 \u03BA\u03C1\u03B1\u03C4\u03AE\u03C3\u03C4\u03B5 \u03C4\u03BF\u03C5\u03C2 \u03BC\u03C5\u03C3\u03C4\u03B9\u03BA\u03BF\u03CD\u03C2.",
      successMessage: "\u039F\u03B9 \u03B5\u03C6\u03B5\u03B4\u03C1\u03B9\u03BA\u03BF\u03AF \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03AF \u03B5\u03AF\u03BD\u03B1\u03B9 \u03C0\u03BB\u03AD\u03BF\u03BD \u03B5\u03BD\u03B5\u03C1\u03B3\u03BF\u03C0\u03BF\u03B9\u03B7\u03BC\u03AD\u03BD\u03BF\u03B9. \u039C\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03AE\u03C3\u03B5\u03C4\u03B5 \u03AD\u03BD\u03B1\u03BD \u03B1\u03C0\u03CC \u03B1\u03C5\u03C4\u03BF\u03CD\u03C2 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B4\u03B5\u03B8\u03B5\u03AF\u03C4\u03B5 \u03C3\u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2, \u03B5\u03AC\u03BD \u03C7\u03AC\u03C3\u03B5\u03C4\u03B5 \u03C4\u03B7\u03BD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7 \u03C3\u03C4\u03B7 \u03C3\u03C5\u03C3\u03BA\u03B5\u03C5\u03AE \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2. \u039A\u03AC\u03B8\u03B5 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF \u03BD\u03B1 \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03B7\u03B8\u03B5\u03AF \u03BC\u03CC\u03BD\u03BF \u03BC\u03AF\u03B1 \u03C6\u03BF\u03C1\u03AC.",
      successSubtitle: "\u039C\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03AE\u03C3\u03B5\u03C4\u03B5 \u03AD\u03BD\u03B1\u03BD \u03B1\u03C0\u03CC \u03B1\u03C5\u03C4\u03BF\u03CD\u03C2 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B4\u03B5\u03B8\u03B5\u03AF\u03C4\u03B5 \u03C3\u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2, \u03B5\u03AC\u03BD \u03C7\u03AC\u03C3\u03B5\u03C4\u03B5 \u03C4\u03B7\u03BD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7 \u03C3\u03C4\u03B7 \u03C3\u03C5\u03C3\u03BA\u03B5\u03C5\u03AE \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2.",
      title: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03BC\u03B5 \u03B5\u03C6\u03B5\u03B4\u03C1\u03B9\u03BA\u03BF\u03CD\u03C2 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD\u03C2",
      title__codelist: "\u0395\u03C6\u03B5\u03B4\u03C1\u03B9\u03BA\u03BF\u03AF \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03AF"
    },
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "\u0395\u03C0\u03B9\u03BB\u03AD\u03BE\u03C4\u03B5 \u03AD\u03BD\u03B1\u03BD \u03C0\u03AC\u03C1\u03BF\u03C7\u03BF \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B4\u03AD\u03C3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2.",
      formHint__noAccounts: "\u0394\u03B5\u03BD \u03C5\u03C0\u03AC\u03C1\u03C7\u03BF\u03C5\u03BD \u03B4\u03B9\u03B1\u03B8\u03AD\u03C3\u03B9\u03BC\u03BF\u03B9 \u03C0\u03AC\u03C1\u03BF\u03C7\u03BF\u03B9 \u03B5\u03BE\u03C9\u03C4\u03B5\u03C1\u03B9\u03BA\u03CE\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CE\u03BD.",
      removeResource: {
        messageLine1: "\u039F {{identifier}} \u03B8\u03B1 \u03B1\u03C6\u03B1\u03B9\u03C1\u03B5\u03B8\u03B5\u03AF \u03B1\u03C0\u03CC \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC.",
        messageLine2: "\u0394\u03B5\u03BD \u03B8\u03B1 \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03C0\u03BB\u03AD\u03BF\u03BD \u03BD\u03B1 \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03AE\u03C3\u03B5\u03C4\u03B5 \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03C3\u03C5\u03BD\u03B4\u03B5\u03B4\u03B5\u03BC\u03AD\u03BD\u03BF \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03BA\u03B1\u03B9 \u03BF\u03C0\u03BF\u03B9\u03B5\u03C3\u03B4\u03AE\u03C0\u03BF\u03C4\u03B5 \u03B5\u03BE\u03B1\u03C1\u03C4\u03B7\u03BC\u03AD\u03BD\u03B5\u03C2 \u03BB\u03B5\u03B9\u03C4\u03BF\u03C5\u03C1\u03B3\u03AF\u03B5\u03C2 \u03B4\u03B5\u03BD \u03B8\u03B1 \u03BB\u03B5\u03B9\u03C4\u03BF\u03C5\u03C1\u03B3\u03BF\u03CD\u03BD \u03C0\u03BB\u03AD\u03BF\u03BD.",
        successMessage: "\u039F {{connectedAccount}} \u03AD\u03C7\u03B5\u03B9 \u03B1\u03C6\u03B1\u03B9\u03C1\u03B5\u03B8\u03B5\u03AF \u03B1\u03C0\u03CC \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2.",
        title: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 \u03C3\u03C5\u03BD\u03B4\u03B5\u03B4\u03B5\u03BC\u03AD\u03BD\u03BF\u03C5 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03BF\u03CD"
      },
      socialButtonsBlockButton: "\u03A3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7 \u03BC\u03B5 \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC {{provider|titleize}}",
      successMessage: "\u039F \u03C0\u03AC\u03C1\u03BF\u03C7\u03BF\u03C2 \u03AD\u03C7\u03B5\u03B9 \u03C0\u03C1\u03BF\u03C3\u03C4\u03B5\u03B8\u03B5\u03AF \u03C3\u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2",
      title: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03C3\u03C5\u03BD\u03B4\u03B5\u03B4\u03B5\u03BC\u03AD\u03BD\u03BF\u03C5 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03BF\u03CD"
    },
    deletePage: {
      actionDescription: '\u03A0\u03BB\u03B7\u03BA\u03C4\u03C1\u03BF\u03BB\u03BF\u03B3\u03AE\u03C3\u03C4\u03B5 "\u0394\u03B9\u03B1\u03B3\u03C1\u03B1\u03C6\u03AE \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03BF\u03CD" \u03C0\u03B1\u03C1\u03B1\u03BA\u03AC\u03C4\u03C9 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5.',
      confirm: "\u0394\u03B9\u03B1\u03B3\u03C1\u03B1\u03C6\u03AE \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03BF\u03CD",
      messageLine1: "\u0395\u03AF\u03C3\u03C4\u03B5 \u03B2\u03AD\u03B2\u03B1\u03B9\u03BF\u03C2 \u03CC\u03C4\u03B9 \u03B8\u03AD\u03BB\u03B5\u03C4\u03B5 \u03BD\u03B1 \u03B4\u03B9\u03B1\u03B3\u03C1\u03AC\u03C8\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2;",
      messageLine2: "\u0391\u03C5\u03C4\u03AE \u03B7 \u03B5\u03BD\u03AD\u03C1\u03B3\u03B5\u03B9\u03B1 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03BC\u03CC\u03BD\u03B9\u03BC\u03B7 \u03BA\u03B1\u03B9 \u03BC\u03B7 \u03B1\u03BD\u03B1\u03C3\u03C4\u03C1\u03AD\u03C8\u03B9\u03BC\u03B7.",
      title: "\u0394\u03B9\u03B1\u03B3\u03C1\u03B1\u03C6\u03AE \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03BF\u03CD"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "\u0398\u03B1 \u03C3\u03C4\u03B1\u03BB\u03B5\u03AF \u03AD\u03BD\u03B1 email \u03C0\u03BF\u03C5 \u03C0\u03B5\u03C1\u03B9\u03AD\u03C7\u03B5\u03B9 \u03AD\u03BD\u03B1\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03C3\u03B5 \u03B1\u03C5\u03C4\u03AE\u03BD \u03C4\u03B7 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email.",
        formSubtitle: "\u0395\u03B9\u03C3\u03B1\u03B3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03C0\u03BF\u03C5 \u03B5\u03C3\u03C4\u03AC\u03BB\u03B7 \u03C3\u03C4\u03B7\u03BD {{identifier}}",
        formTitle: "\u039A\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
        resendButton: "\u0394\u03B5\u03BD \u03BB\u03AC\u03B2\u03B1\u03C4\u03B5 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC; \u0395\u03C0\u03B1\u03BD\u03AC\u03BB\u03B7\u03C8\u03B7 \u03B1\u03C0\u03BF\u03C3\u03C4\u03BF\u03BB\u03AE\u03C2",
        successMessage: "\u03A4\u03BF email {{identifier}} \u03AD\u03C7\u03B5\u03B9 \u03C0\u03C1\u03BF\u03C3\u03C4\u03B5\u03B8\u03B5\u03AF \u03C3\u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2."
      },
      emailLink: {
        formHint: "\u0398\u03B1 \u03C3\u03C4\u03B1\u03BB\u03B5\u03AF \u03AD\u03BD\u03B1 email \u03C0\u03BF\u03C5 \u03C0\u03B5\u03C1\u03B9\u03AD\u03C7\u03B5\u03B9 \u03AD\u03BD\u03B1\u03BD \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03C3\u03B5 \u03B1\u03C5\u03C4\u03AE\u03BD \u03C4\u03B7 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email.",
        formSubtitle: "\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03C3\u03C4\u03BF\u03BD \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03C3\u03C4\u03BF email \u03C0\u03BF\u03C5 \u03B5\u03C3\u03C4\u03AC\u03BB\u03B7 \u03C3\u03C4\u03B7\u03BD {{identifier}}",
        formTitle: "\u03A3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
        resendButton: "\u0394\u03B5\u03BD \u03BB\u03AC\u03B2\u03B1\u03C4\u03B5 \u03BA\u03AC\u03C0\u03BF\u03B9\u03BF\u03BD \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF; \u0395\u03C0\u03B1\u03BD\u03AC\u03BB\u03B7\u03C8\u03B7 \u03B1\u03C0\u03BF\u03C3\u03C4\u03BF\u03BB\u03AE\u03C2",
        successMessage: "\u03A4\u03BF email {{identifier}} \u03AD\u03C7\u03B5\u03B9 \u03C0\u03C1\u03BF\u03C3\u03C4\u03B5\u03B8\u03B5\u03AF \u03C3\u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "\u0397 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 {{identifier}} \u03B8\u03B1 \u03B1\u03C6\u03B1\u03B9\u03C1\u03B5\u03B8\u03B5\u03AF \u03B1\u03C0\u03CC \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC.",
        messageLine2: "\u0394\u03B5\u03BD \u03B8\u03B1 \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03C0\u03BB\u03AD\u03BF\u03BD \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B4\u03B5\u03B8\u03B5\u03AF\u03C4\u03B5 \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03CE\u03BD\u03C4\u03B1\u03C2 \u03B1\u03C5\u03C4\u03AE\u03BD \u03C4\u03B7 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 email.",
        successMessage: "\u0397 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 {{emailAddress}} \u03AD\u03C7\u03B5\u03B9 \u03B1\u03C6\u03B1\u03B9\u03C1\u03B5\u03B8\u03B5\u03AF \u03B1\u03C0\u03CC \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2.",
        title: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7\u03C2 email"
      },
      title: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7\u03C2 email",
      verifyTitle: "Verify email address"
    },
    formButtonPrimary__add: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7",
    formButtonPrimary__continue: "\u03A3\u03C5\u03BD\u03AD\u03C7\u03B5\u03B9\u03B1",
    formButtonPrimary__finish: "\u039F\u03BB\u03BF\u03BA\u03BB\u03AE\u03C1\u03C9\u03C3\u03B7",
    formButtonPrimary__remove: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7",
    formButtonPrimary__save: "\u0391\u03C0\u03BF\u03B8\u03AE\u03BA\u03B5\u03C5\u03C3\u03B7",
    formButtonReset: "\u0391\u03BA\u03CD\u03C1\u03C9\u03C3\u03B7",
    mfaPage: {
      formHint: "\u0395\u03C0\u03B9\u03BB\u03AD\u03BE\u03C4\u03B5 \u03BC\u03B9\u03B1 \u03BC\u03AD\u03B8\u03BF\u03B4\u03BF \u03B3\u03B9\u03B1 \u03C0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7.",
      title: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03B4\u03B9\u03C0\u03BB\u03AE\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2"
    },
    mfaPhoneCodePage: {
      backButton: "Use existing number",
      primaryButton__addPhoneNumber: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03B1\u03C1\u03B9\u03B8\u03BC\u03BF\u03CD \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5",
      removeResource: {
        messageLine1: "\u039F {{identifier}} \u03B4\u03B5\u03BD \u03B8\u03B1 \u03BB\u03B1\u03BC\u03B2\u03AC\u03BD\u03B5\u03B9 \u03C0\u03BB\u03AD\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03BA\u03B1\u03C4\u03AC \u03C4\u03B7 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7.",
        messageLine2: "\u039F \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC\u03C2 \u03C3\u03B1\u03C2 \u03B5\u03BD\u03B4\u03AD\u03C7\u03B5\u03C4\u03B1\u03B9 \u03BD\u03B1 \u03BC\u03B7\u03BD \u03B5\u03AF\u03BD\u03B1\u03B9 \u03C4\u03CC\u03C3\u03BF \u03B1\u03C3\u03C6\u03B1\u03BB\u03AE\u03C2. \u0395\u03AF\u03C3\u03C4\u03B5 \u03C3\u03AF\u03B3\u03BF\u03C5\u03C1\u03BF\u03B9 \u03CC\u03C4\u03B9 \u03B8\u03AD\u03BB\u03B5\u03C4\u03B5 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5;",
        successMessage: "\u0397 \u03B4\u03B9\u03C0\u03BB\u03AE \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7 \u03BC\u03B5 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD\u03C2 SMS \u03AD\u03C7\u03B5\u03B9 \u03B1\u03C6\u03B1\u03B9\u03C1\u03B5\u03B8\u03B5\u03AF \u03B3\u03B9\u03B1 \u03C4\u03BF\u03BD \u03B1\u03C1\u03B9\u03B8\u03BC\u03CC \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5 {{mfaPhoneCode}}",
        title: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 \u03B4\u03B9\u03C0\u03BB\u03AE\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2"
      },
      subtitle__availablePhoneNumbers: "\u0395\u03C0\u03B9\u03BB\u03AD\u03BE\u03C4\u03B5 \u03AD\u03BD\u03B1\u03BD \u03B1\u03C1\u03B9\u03B8\u03BC\u03CC \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03B5\u03B3\u03B3\u03C1\u03B1\u03C6\u03B5\u03AF\u03C4\u03B5 \u03B3\u03B9\u03B1 \u03C4\u03B7 \u03B4\u03B9\u03C0\u03BB\u03AE \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7 \u03BC\u03B5 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD\u03C2 SMS.",
      subtitle__unavailablePhoneNumbers: "\u0394\u03B5\u03BD \u03C5\u03C0\u03AC\u03C1\u03C7\u03BF\u03C5\u03BD \u03B4\u03B9\u03B1\u03B8\u03AD\u03C3\u03B9\u03BC\u03BF\u03B9 \u03B1\u03C1\u03B9\u03B8\u03BC\u03BF\u03AF \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5 \u03B3\u03B9\u03B1 \u03B5\u03B3\u03B3\u03C1\u03B1\u03C6\u03AE \u03C3\u03C4\u03B7\u03BD \u03B4\u03B9\u03C0\u03BB\u03AE \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7 \u03BC\u03B5 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD\u03C2 SMS.",
      successMessage1: "When signing in, you will need to enter a verification code sent to this phone number as an additional step.",
      successMessage2: "Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.",
      successTitle: "SMS code verification enabled",
      title: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD SMS"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "\u03A3\u03AC\u03C1\u03C9\u03C3\u03B7 QR \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD \u03B1\u03BD\u03C4\u03AF \u03B1\u03C5\u03C4\u03BF\u03CD",
        buttonUnableToScan__nonPrimary: "\u0394\u03B5\u03BD \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03C3\u03B1\u03C1\u03CE\u03C3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD QR \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC;",
        infoText__ableToScan: "\u03A1\u03C5\u03B8\u03BC\u03AF\u03C3\u03C4\u03B5 \u03BC\u03B9\u03B1 \u03BD\u03AD\u03B1 \u03BC\u03AD\u03B8\u03BF\u03B4\u03BF \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7\u03C2 \u03C3\u03C4\u03B7\u03BD \u03B5\u03C6\u03B1\u03C1\u03BC\u03BF\u03B3\u03AE \u03B1\u03C5\u03B8\u03B5\u03BD\u03C4\u03B9\u03BA\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7\u03C2 \u03C3\u03B1\u03C2 \u03BA\u03B1\u03B9 \u03C3\u03B1\u03C1\u03CE\u03C3\u03C4\u03B5 \u03C4\u03BF\u03BD \u03C0\u03B1\u03C1\u03B1\u03BA\u03AC\u03C4\u03C9 QR \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03BF\u03BD \u03C3\u03C5\u03BD\u03B4\u03AD\u03C3\u03B5\u03C4\u03B5 \u03BC\u03B5 \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2.",
        infoText__unableToScan: "\u03A1\u03C5\u03B8\u03BC\u03AF\u03C3\u03C4\u03B5 \u03BC\u03B9\u03B1 \u03BD\u03AD\u03B1 \u03BC\u03AD\u03B8\u03BF\u03B4\u03BF \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7\u03C2 \u03C3\u03C4\u03B7\u03BD \u03B1\u03C5\u03B8\u03B5\u03BD\u03C4\u03B9\u03BA\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7 \u03BA\u03B1\u03B9 \u03B5\u03B9\u03C3\u03B1\u03B3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BA\u03BB\u03B5\u03B9\u03B4\u03AF \u03C0\u03BF\u03C5 \u03C0\u03B1\u03C1\u03AD\u03C7\u03B5\u03C4\u03B1\u03B9 \u03C0\u03B1\u03C1\u03B1\u03BA\u03AC\u03C4\u03C9.",
        inputLabel__unableToScan1: "\u0392\u03B5\u03B2\u03B1\u03B9\u03C9\u03B8\u03B5\u03AF\u03C4\u03B5 \u03CC\u03C4\u03B9 \u03BF\u03B9 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03AF \u03C0\u03BF\u03C5 \u03B2\u03B1\u03C3\u03AF\u03B6\u03BF\u03BD\u03C4\u03B1\u03B9 \u03C3\u03C4\u03BF\u03BD \u03C7\u03C1\u03CC\u03BD\u03BF \u03AE \u03BC\u03AF\u03B1\u03C2 \u03C7\u03C1\u03AE\u03C3\u03B7\u03C2 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03B5\u03BD\u03B5\u03C1\u03B3\u03BF\u03C0\u03BF\u03B9\u03B7\u03BC\u03AD\u03BD\u03BF\u03B9 \u03BA\u03B1\u03B9 \u03BF\u03BB\u03BF\u03BA\u03BB\u03B7\u03C1\u03CE\u03C3\u03C4\u03B5 \u03C4\u03B7 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7 \u03C4\u03BF\u03C5 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03BF\u03CD \u03C3\u03B1\u03C2.",
        inputLabel__unableToScan2: "\u0395\u03BD\u03B1\u03BB\u03BB\u03B1\u03BA\u03C4\u03B9\u03BA\u03AC, \u03B5\u03AC\u03BD \u03B7 \u03B5\u03C6\u03B1\u03C1\u03BC\u03BF\u03B3\u03AE \u03B1\u03C5\u03B8\u03B5\u03BD\u03C4\u03B9\u03BA\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2 \u03C5\u03C0\u03BF\u03C3\u03C4\u03B7\u03C1\u03AF\u03B6\u03B5\u03B9 TOTP URIs, \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03B5\u03C0\u03AF\u03C3\u03B7\u03C2 \u03BD\u03B1 \u03B1\u03BD\u03C4\u03B9\u03B3\u03C1\u03AC\u03C8\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03C0\u03BB\u03AE\u03C1\u03B7 URI."
      },
      removeResource: {
        messageLine1: "\u0394\u03B5\u03BD \u03B8\u03B1 \u03B1\u03C0\u03B1\u03B9\u03C4\u03BF\u03CD\u03BD\u03C4\u03B1\u03B9 \u03C0\u03BB\u03AD\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03AF \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03B1\u03C0\u03CC \u03B1\u03C5\u03C4\u03AE\u03BD \u03C4\u03B7\u03BD \u03B5\u03C6\u03B1\u03C1\u03BC\u03BF\u03B3\u03AE \u03B1\u03C5\u03B8\u03B5\u03BD\u03C4\u03B9\u03BA\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7\u03C2 \u03BA\u03B1\u03C4\u03AC \u03C4\u03B7 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7.",
        messageLine2: "\u039F \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC\u03C2 \u03C3\u03B1\u03C2 \u03B5\u03BD\u03B4\u03AD\u03C7\u03B5\u03C4\u03B1\u03B9 \u03BD\u03B1 \u03BC\u03B7\u03BD \u03B5\u03AF\u03BD\u03B1\u03B9 \u03C4\u03CC\u03C3\u03BF \u03B1\u03C3\u03C6\u03B1\u03BB\u03AE\u03C2. \u0395\u03AF\u03C3\u03C4\u03B5 \u03C3\u03AF\u03B3\u03BF\u03C5\u03C1\u03BF\u03B9 \u03CC\u03C4\u03B9 \u03B8\u03AD\u03BB\u03B5\u03C4\u03B5 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5;",
        successMessage: "\u0397 \u03B4\u03B9\u03C0\u03BB\u03AE \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7 \u03BC\u03AD\u03C3\u03C9 \u03B5\u03C6\u03B1\u03C1\u03BC\u03BF\u03B3\u03AE\u03C2 \u03B1\u03C5\u03B8\u03B5\u03BD\u03C4\u03B9\u03BA\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7\u03C2 \u03AD\u03C7\u03B5\u03B9 \u03B1\u03C6\u03B1\u03B9\u03C1\u03B5\u03B8\u03B5\u03AF.",
        title: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 \u03B4\u03B9\u03C0\u03BB\u03AE\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2"
      },
      successMessage: "\u0397 \u03B4\u03B9\u03C0\u03BB\u03AE \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03C0\u03BB\u03AD\u03BF\u03BD \u03B5\u03BD\u03B5\u03C1\u03B3\u03BF\u03C0\u03BF\u03B9\u03B7\u03BC\u03AD\u03BD\u03B7. \u039A\u03B1\u03C4\u03AC \u03C4\u03B7 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7, \u03B8\u03B1 \u03C0\u03C1\u03AD\u03C0\u03B5\u03B9 \u03BD\u03B1 \u03B5\u03B9\u03C3\u03B1\u03B3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03AD\u03BD\u03B1\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03B1\u03C0\u03CC \u03B1\u03C5\u03C4\u03AE\u03BD \u03C4\u03B7\u03BD \u03B5\u03C6\u03B1\u03C1\u03BC\u03BF\u03B3\u03AE \u03B1\u03C5\u03B8\u03B5\u03BD\u03C4\u03B9\u03BA\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7\u03C2 \u03C9\u03C2 \u03B5\u03C0\u03B9\u03C0\u03BB\u03AD\u03BF\u03BD \u03B2\u03AE\u03BC\u03B1.",
      title: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03B5\u03C6\u03B1\u03C1\u03BC\u03BF\u03B3\u03AE\u03C2 \u03B1\u03C5\u03B8\u03B5\u03BD\u03C4\u03B9\u03BA\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7\u03C2",
      verifySubtitle: "\u0395\u03B9\u03C3\u03B1\u03B3\u03AC\u03B3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03C0\u03BF\u03C5 \u03B4\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AE\u03B8\u03B7\u03BA\u03B5 \u03B1\u03C0\u03CC \u03C4\u03B7\u03BD \u03B5\u03C6\u03B1\u03C1\u03BC\u03BF\u03B3\u03AE \u03B1\u03C5\u03B8\u03B5\u03BD\u03C4\u03B9\u03BA\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7\u03C2",
      verifyTitle: "\u039A\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2"
    },
    mobileButton__menu: "\u039C\u03B5\u03BD\u03BF\u03CD",
    navbar: {
      account: "\u03A0\u03C1\u03BF\u03C6\u03AF\u03BB",
      apiKeys: void 0,
      billing: void 0,
      description: "\u0394\u03B9\u03B1\u03C7\u03B5\u03B9\u03C1\u03B9\u03C3\u03C4\u03B5\u03AF\u03C4\u03B5 \u03C4\u03B9\u03C2 \u03C0\u03BB\u03B7\u03C1\u03BF\u03C6\u03BF\u03C1\u03AF\u03B5\u03C2 \u03C4\u03BF\u03C5 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03BF\u03CD \u03C3\u03B1\u03C2.",
      security: "\u0391\u03C3\u03C6\u03AC\u03BB\u03B5\u03B9\u03B1",
      title: "\u039B\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC\u03C2"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: void 0,
        title: void 0
      },
      subtitle__rename: void 0,
      title__rename: void 0
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "It is recommended to sign out of all other devices which may have used your old password.",
      readonly: "\u039F \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2 \u03B4\u03B5\u03BD \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF \u03BD\u03B1 \u03B5\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03C4\u03B5\u03AF \u03B1\u03C5\u03C4\u03AE\u03BD \u03C4\u03B7 \u03C3\u03C4\u03B9\u03B3\u03BC\u03AE \u03B5\u03C0\u03B5\u03B9\u03B4\u03AE \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B4\u03B5\u03B8\u03B5\u03AF\u03C4\u03B5 \u03BC\u03CC\u03BD\u03BF \u03BC\u03AD\u03C3\u03C9 \u03C4\u03B7\u03C2 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7\u03C2 \u03BC\u03B5 \u03C4\u03B7\u03BD \u03B5\u03C0\u03B9\u03C7\u03B5\u03AF\u03C1\u03B7\u03C3\u03B7.",
      successMessage__set: "\u039F \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2 \u03AD\u03C7\u03B5\u03B9 \u03BF\u03C1\u03B9\u03C3\u03C4\u03B5\u03AF.",
      successMessage__signOutOfOtherSessions: "\u038C\u03BB\u03B5\u03C2 \u03BF\u03B9 \u03AC\u03BB\u03BB\u03B5\u03C2 \u03C3\u03C5\u03BD\u03B5\u03B4\u03C1\u03AF\u03B5\u03C2 \u03AD\u03C7\u03BF\u03C5\u03BD \u03B1\u03C0\u03BF\u03C3\u03C5\u03BD\u03B4\u03B5\u03B8\u03B5\u03AF.",
      successMessage__update: "\u039F \u03BA\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03AE\u03C2 \u03C3\u03B1\u03C2 \u03AD\u03C7\u03B5\u03B9 \u03B5\u03BD\u03B7\u03BC\u03B5\u03C1\u03C9\u03B8\u03B5\u03AF.",
      title__set: "\u039F\u03C1\u03B9\u03C3\u03BC\u03CC\u03C2 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2",
      title__update: "\u0391\u03BB\u03BB\u03B1\u03B3\u03AE \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2"
    },
    phoneNumberPage: {
      infoText: "\u0398\u03B1 \u03C3\u03C4\u03B1\u03BB\u03B5\u03AF \u03AD\u03BD\u03B1 \u03BC\u03AE\u03BD\u03C5\u03BC\u03B1 \u03BA\u03B5\u03B9\u03BC\u03AD\u03BD\u03BF\u03C5 \u03C0\u03BF\u03C5 \u03C0\u03B5\u03C1\u03B9\u03AD\u03C7\u03B5\u03B9 \u03AD\u03BD\u03B1 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03BC\u03BF \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2 \u03C3\u03B5 \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03B1\u03C1\u03B9\u03B8\u03BC\u03CC \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5.",
      removeResource: {
        messageLine1: "\u039F \u03B1\u03C1\u03B9\u03B8\u03BC\u03CC\u03C2 {{identifier}} \u03B8\u03B1 \u03B1\u03C6\u03B1\u03B9\u03C1\u03B5\u03B8\u03B5\u03AF \u03B1\u03C0\u03CC \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC.",
        messageLine2: "\u0394\u03B5\u03BD \u03B8\u03B1 \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03C0\u03BB\u03AD\u03BF\u03BD \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B4\u03B5\u03B8\u03B5\u03AF\u03C4\u03B5 \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03CE\u03BD\u03C4\u03B1\u03C2 \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03B1\u03C1\u03B9\u03B8\u03BC\u03CC \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5.",
        successMessage: "\u039F \u03B1\u03C1\u03B9\u03B8\u03BC\u03CC\u03C2 \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5 {{phoneNumber}} \u03AD\u03C7\u03B5\u03B9 \u03B1\u03C6\u03B1\u03B9\u03C1\u03B5\u03B8\u03B5\u03AF \u03B1\u03C0\u03CC \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2.",
        title: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 \u03B1\u03C1\u03B9\u03B8\u03BC\u03BF\u03CD \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5"
      },
      successMessage: "\u039F \u03B1\u03C1\u03B9\u03B8\u03BC\u03CC\u03C2 \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5 {{identifier}} \u03AD\u03C7\u03B5\u03B9 \u03C0\u03C1\u03BF\u03C3\u03C4\u03B5\u03B8\u03B5\u03AF \u03C3\u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2.",
      title: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03B1\u03C1\u03B9\u03B8\u03BC\u03BF\u03CD \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5",
      verifySubtitle: "Enter the verification code sent to {{identifier}}",
      verifyTitle: "Verify phone number"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "\u0391\u03BD\u03B5\u03B2\u03AC\u03C3\u03C4\u03B5 \u03BC\u03B9\u03B1 \u03B5\u03B9\u03BA\u03CC\u03BD\u03B1 \u03C3\u03B5 \u03BC\u03BF\u03C1\u03C6\u03AE JPG, PNG, GIF \u03AE WEBP \u03BC\u03B9\u03BA\u03C1\u03CC\u03C4\u03B5\u03C1\u03B7 \u03C4\u03C9\u03BD 10 MB",
      imageFormDestructiveActionSubtitle: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 \u03B5\u03B9\u03BA\u03CC\u03BD\u03B1\u03C2",
      imageFormSubtitle: "\u0391\u03BD\u03AD\u03B2\u03B1\u03C3\u03BC\u03B1 \u03B5\u03B9\u03BA\u03CC\u03BD\u03B1\u03C2",
      imageFormTitle: "\u0395\u03B9\u03BA\u03CC\u03BD\u03B1 \u03C0\u03C1\u03BF\u03C6\u03AF\u03BB",
      readonly: "\u039F\u03B9 \u03C0\u03BB\u03B7\u03C1\u03BF\u03C6\u03BF\u03C1\u03AF\u03B5\u03C2 \u03C4\u03BF\u03C5 \u03C0\u03C1\u03BF\u03C6\u03AF\u03BB \u03C3\u03B1\u03C2 \u03AD\u03C7\u03BF\u03C5\u03BD \u03C0\u03B1\u03C1\u03B1\u03C3\u03C7\u03B5\u03B8\u03B5\u03AF \u03B1\u03C0\u03CC \u03C4\u03B7 \u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7 \u03BC\u03B5 \u03C4\u03B7\u03BD \u03B5\u03C0\u03B9\u03C7\u03B5\u03AF\u03C1\u03B7\u03C3\u03B7 \u03BA\u03B1\u03B9 \u03B4\u03B5\u03BD \u03BC\u03C0\u03BF\u03C1\u03BF\u03CD\u03BD \u03BD\u03B1 \u03B5\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03C4\u03BF\u03CD\u03BD.",
      successMessage: "\u03A4\u03BF \u03C0\u03C1\u03BF\u03C6\u03AF\u03BB \u03C3\u03B1\u03C2 \u03AD\u03C7\u03B5\u03B9 \u03B5\u03BD\u03B7\u03BC\u03B5\u03C1\u03C9\u03B8\u03B5\u03AF.",
      title: "\u0395\u03BD\u03B7\u03BC\u03AD\u03C1\u03C9\u03C3\u03B7 \u03C0\u03C1\u03BF\u03C6\u03AF\u03BB"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "\u0391\u03C0\u03BF\u03C3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7 \u03B1\u03C0\u03CC \u03C4\u03B7 \u03C3\u03C5\u03C3\u03BA\u03B5\u03C5\u03AE",
        title: "\u0395\u03BD\u03B5\u03C1\u03B3\u03AD\u03C2 \u03C3\u03C5\u03C3\u03BA\u03B5\u03C5\u03AD\u03C2"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "\u03A0\u03C1\u03BF\u03C3\u03C0\u03AC\u03B8\u03B5\u03B9\u03B1 \u03BE\u03B1\u03BD\u03AC",
        actionLabel__reauthorize: "\u0395\u03BE\u03BF\u03C5\u03C3\u03B9\u03BF\u03B4\u03BF\u03C4\u03AE\u03C3\u03C4\u03B5 \u03C4\u03CE\u03C1\u03B1",
        destructiveActionTitle: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7",
        primaryButton: "\u03A3\u03CD\u03BD\u03B4\u03B5\u03C3\u03B7 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03BF\u03CD",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues",
        title: "\u03A3\u03C5\u03BD\u03B4\u03B5\u03B4\u03B5\u03BC\u03AD\u03BD\u03BF\u03B9 \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03BF\u03AF"
      },
      dangerSection: {
        deleteAccountButton: "\u0394\u03B9\u03B1\u03B3\u03C1\u03B1\u03C6\u03AE \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03BF\u03CD",
        title: "\u039A\u03AF\u03BD\u03B4\u03C5\u03BD\u03BF\u03C2"
      },
      emailAddressesSection: {
        destructiveAction: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7\u03C2 email",
        detailsAction__nonPrimary: "\u039F\u03C1\u03B9\u03C3\u03BC\u03CC\u03C2 \u03C9\u03C2 \u03BA\u03CD\u03C1\u03B9\u03B1",
        detailsAction__primary: "\u039F\u03BB\u03BF\u03BA\u03BB\u03AE\u03C1\u03C9\u03C3\u03B7 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
        detailsAction__unverified: "\u039F\u03BB\u03BF\u03BA\u03BB\u03AE\u03C1\u03C9\u03C3\u03B7 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
        primaryButton: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7\u03C2 email",
        title: "\u0394\u03B9\u03B5\u03C5\u03B8\u03CD\u03BD\u03C3\u03B5\u03B9\u03C2 email"
      },
      enterpriseAccountsSection: {
        title: "\u0395\u03C0\u03B9\u03C7\u03B5\u03B9\u03C1\u03B7\u03C3\u03B9\u03B1\u03BA\u03BF\u03AF \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03BF\u03AF"
      },
      headerTitle__account: "\u039B\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC\u03C2",
      headerTitle__security: "\u0391\u03C3\u03C6\u03AC\u03BB\u03B5\u03B9\u03B1",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "\u0395\u03C0\u03B1\u03BD\u03B1\u03B4\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u03BA\u03C9\u03B4\u03B9\u03BA\u03CE\u03BD",
          headerTitle: "\u0395\u03C6\u03B5\u03B4\u03C1\u03B9\u03BA\u03BF\u03AF \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03AF",
          subtitle__regenerate: "\u039B\u03AC\u03B2\u03B5\u03C4\u03B5 \u03AD\u03BD\u03B1 \u03BD\u03AD\u03BF \u03C3\u03B5\u03C4 \u03B1\u03C3\u03C6\u03B1\u03BB\u03CE\u03BD \u03B5\u03C6\u03B5\u03B4\u03C1\u03B9\u03BA\u03CE\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CE\u03BD. \u039F\u03B9 \u03C0\u03C1\u03BF\u03B7\u03B3\u03BF\u03CD\u03BC\u03B5\u03BD\u03BF\u03B9 \u03B5\u03C6\u03B5\u03B4\u03C1\u03B9\u03BA\u03BF\u03AF \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03AF \u03B8\u03B1 \u03B4\u03B9\u03B1\u03B3\u03C1\u03B1\u03C6\u03BF\u03CD\u03BD \u03BA\u03B1\u03B9 \u03B4\u03B5\u03BD \u03BC\u03C0\u03BF\u03C1\u03BF\u03CD\u03BD \u03BD\u03B1 \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03B7\u03B8\u03BF\u03CD\u03BD.",
          title__regenerate: "\u0395\u03C0\u03B1\u03BD\u03B1\u03B4\u03B7\u03BC\u03B9\u03BF\u03C5\u03C1\u03B3\u03AF\u03B1 \u03B5\u03C6\u03B5\u03B4\u03C1\u03B9\u03BA\u03CE\u03BD \u03BA\u03C9\u03B4\u03B9\u03BA\u03CE\u03BD"
        },
        phoneCode: {
          actionLabel__setDefault: "\u039F\u03C1\u03B9\u03C3\u03BC\u03CC\u03C2 \u03C9\u03C2 \u03C0\u03C1\u03BF\u03B5\u03C0\u03B9\u03BB\u03B5\u03B3\u03BC\u03AD\u03BD\u03BF\u03C2",
          destructiveActionLabel: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 \u03B1\u03C1\u03B9\u03B8\u03BC\u03BF\u03CD \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5"
        },
        primaryButton: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03B1\u03C5\u03B8\u03B5\u03BD\u03C4\u03B9\u03BA\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7\u03C2 \u03B4\u03CD\u03BF \u03B2\u03B7\u03BC\u03AC\u03C4\u03C9\u03BD",
        title: "\u0391\u03C5\u03B8\u03B5\u03BD\u03C4\u03B9\u03BA\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7 \u03B4\u03CD\u03BF \u03B2\u03B7\u03BC\u03AC\u03C4\u03C9\u03BD",
        totp: {
          destructiveActionTitle: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7",
          headerTitle: "\u0395\u03C6\u03B1\u03C1\u03BC\u03BF\u03B3\u03AE \u03B1\u03C5\u03B8\u03B5\u03BD\u03C4\u03B9\u03BA\u03BF\u03C0\u03BF\u03AF\u03B7\u03C3\u03B7\u03C2"
        }
      },
      passkeysSection: {
        menuAction__destructive: void 0,
        menuAction__rename: void 0,
        primaryButton: void 0,
        title: void 0
      },
      passwordSection: {
        primaryButton__setPassword: "\u039F\u03C1\u03B9\u03C3\u03BC\u03CC\u03C2 \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2",
        primaryButton__updatePassword: "\u0391\u03BB\u03BB\u03B1\u03B3\u03AE \u03BA\u03C9\u03B4\u03B9\u03BA\u03BF\u03CD \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2",
        title: "\u039A\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03C0\u03C1\u03CC\u03C3\u03B2\u03B1\u03C3\u03B7\u03C2"
      },
      phoneNumbersSection: {
        destructiveAction: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 \u03B1\u03C1\u03B9\u03B8\u03BC\u03BF\u03CD \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5",
        detailsAction__nonPrimary: "\u039F\u03C1\u03B9\u03C3\u03BC\u03CC\u03C2 \u03C9\u03C2 \u03BA\u03CD\u03C1\u03B9\u03BF\u03C2",
        detailsAction__primary: "\u039F\u03BB\u03BF\u03BA\u03BB\u03AE\u03C1\u03C9\u03C3\u03B7 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
        detailsAction__unverified: "\u039F\u03BB\u03BF\u03BA\u03BB\u03AE\u03C1\u03C9\u03C3\u03B7 \u03B5\u03C0\u03B1\u03BB\u03AE\u03B8\u03B5\u03C5\u03C3\u03B7\u03C2",
        primaryButton: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03B1\u03C1\u03B9\u03B8\u03BC\u03BF\u03CD \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5",
        title: "\u0391\u03C1\u03B9\u03B8\u03BC\u03BF\u03AF \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5"
      },
      profileSection: {
        primaryButton: "\u0395\u03BD\u03B7\u03BC\u03AD\u03C1\u03C9\u03C3\u03B7 \u03C0\u03C1\u03BF\u03C6\u03AF\u03BB",
        title: "\u03A0\u03C1\u03BF\u03C6\u03AF\u03BB"
      },
      usernameSection: {
        primaryButton__setUsername: "\u039F\u03C1\u03B9\u03C3\u03BC\u03CC\u03C2 \u03BF\u03BD\u03CC\u03BC\u03B1\u03C4\u03BF\u03C2 \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7",
        primaryButton__updateUsername: "\u0391\u03BB\u03BB\u03B1\u03B3\u03AE \u03BF\u03BD\u03CC\u03BC\u03B1\u03C4\u03BF\u03C2 \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7",
        title: "\u038C\u03BD\u03BF\u03BC\u03B1 \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7"
      },
      web3WalletsSection: {
        destructiveAction: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 \u03C0\u03BF\u03C1\u03C4\u03BF\u03C6\u03BF\u03BB\u03B9\u03BF\u03CD",
        detailsAction__nonPrimary: void 0,
        primaryButton: "\u03A0\u03BF\u03C1\u03C4\u03BF\u03C6\u03CC\u03BB\u03B9\u03B1 Web3",
        title: "\u03A0\u03BF\u03C1\u03C4\u03BF\u03C6\u03CC\u03BB\u03B9\u03B1 Web3"
      }
    },
    usernamePage: {
      successMessage: "\u03A4\u03BF \u03CC\u03BD\u03BF\u03BC\u03B1 \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7 \u03C3\u03B1\u03C2 \u03AD\u03C7\u03B5\u03B9 \u03B5\u03BD\u03B7\u03BC\u03B5\u03C1\u03C9\u03B8\u03B5\u03AF.",
      title__set: "\u0395\u03BD\u03B7\u03BC\u03AD\u03C1\u03C9\u03C3\u03B7 \u03BF\u03BD\u03CC\u03BC\u03B1\u03C4\u03BF\u03C2 \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7",
      title__update: "\u0395\u03BD\u03B7\u03BC\u03AD\u03C1\u03C9\u03C3\u03B7 \u03BF\u03BD\u03CC\u03BC\u03B1\u03C4\u03BF\u03C2 \u03C7\u03C1\u03AE\u03C3\u03C4\u03B7"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "\u03A4\u03BF {{identifier}} \u03B8\u03B1 \u03B1\u03C6\u03B1\u03B9\u03C1\u03B5\u03B8\u03B5\u03AF \u03B1\u03C0\u03CC \u03B1\u03C5\u03C4\u03CC\u03BD \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC.",
        messageLine2: "\u0394\u03B5\u03BD \u03B8\u03B1 \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF\u03C4\u03B5 \u03C0\u03BB\u03AD\u03BF\u03BD \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B4\u03B5\u03B8\u03B5\u03AF\u03C4\u03B5 \u03C7\u03C1\u03B7\u03C3\u03B9\u03BC\u03BF\u03C0\u03BF\u03B9\u03CE\u03BD\u03C4\u03B1\u03C2 \u03B1\u03C5\u03C4\u03CC \u03C4\u03BF web3 \u03C0\u03BF\u03C1\u03C4\u03BF\u03C6\u03CC\u03BB\u03B9.",
        successMessage: "\u03A4\u03BF {{web3Wallet}} \u03AD\u03C7\u03B5\u03B9 \u03B1\u03C6\u03B1\u03B9\u03C1\u03B5\u03B8\u03B5\u03AF \u03B1\u03C0\u03CC \u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2.",
        title: "\u0391\u03C6\u03B1\u03AF\u03C1\u03B5\u03C3\u03B7 web3 \u03C0\u03BF\u03C1\u03C4\u03BF\u03C6\u03BF\u03BB\u03B9\u03BF\u03CD"
      },
      subtitle__availableWallets: "\u0395\u03C0\u03B9\u03BB\u03AD\u03BE\u03C4\u03B5 \u03AD\u03BD\u03B1 web3 \u03C0\u03BF\u03C1\u03C4\u03BF\u03C6\u03CC\u03BB\u03B9 \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03BF \u03C3\u03C5\u03BD\u03B4\u03AD\u03C3\u03B5\u03C4\u03B5 \u03C3\u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2.",
      subtitle__unavailableWallets: "\u0394\u03B5\u03BD \u03C5\u03C0\u03AC\u03C1\u03C7\u03BF\u03C5\u03BD \u03B4\u03B9\u03B1\u03B8\u03AD\u03C3\u03B9\u03BC\u03B1 web3 \u03C0\u03BF\u03C1\u03C4\u03BF\u03C6\u03CC\u03BB\u03B9\u03B1.",
      successMessage: "\u03A4\u03BF \u03C0\u03BF\u03C1\u03C4\u03BF\u03C6\u03CC\u03BB\u03B9 \u03AD\u03C7\u03B5\u03B9 \u03C0\u03C1\u03BF\u03C3\u03C4\u03B5\u03B8\u03B5\u03AF \u03C3\u03C4\u03BF\u03BD \u03BB\u03BF\u03B3\u03B1\u03C1\u03B9\u03B1\u03C3\u03BC\u03CC \u03C3\u03B1\u03C2.",
      title: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 web3 \u03C0\u03BF\u03C1\u03C4\u03BF\u03C6\u03BF\u03BB\u03B9\u03BF\u03CD",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  elGR
};
//# sourceMappingURL=el-GR.mjs.map