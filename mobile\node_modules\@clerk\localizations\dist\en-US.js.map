{"version": 3, "sources": ["../src/en-US.ts"], "sourcesContent": ["import type { LocalizationResource } from '@clerk/types';\n\nexport const enUS: LocalizationResource = {\n  locale: 'en-US',\n  apiKeys: {\n    action__add: 'Add new key',\n    action__search: 'Search keys',\n    createdAndExpirationStatus__expiresOn:\n      \"Created {{ createdDate | shortDate('en-US') }} • Expires {{ expiresDate | longDate('en-US') }}\",\n    createdAndExpirationStatus__never: \"Created {{ createdDate | shortDate('en-US') }} • Never expires\",\n    detailsTitle__emptyRow: 'No API keys found',\n    formButtonPrimary__add: 'Create key',\n    formFieldCaption__expiration__expiresOn: 'Expiring {{ date }}',\n    formFieldCaption__expiration__never: 'This key will never expire',\n    formFieldOption__expiration__180d: '180 Days',\n    formFieldOption__expiration__1d: '1 Day',\n    formFieldOption__expiration__1y: '1 Year',\n    formFieldOption__expiration__30d: '30 Days',\n    formFieldOption__expiration__60d: '60 Days',\n    formFieldOption__expiration__7d: '7 Days',\n    formFieldOption__expiration__90d: '90 Days',\n    formFieldOption__expiration__never: 'Never',\n    formHint: 'Provide a name to generate a new key. You’ll be able to revoke it anytime.',\n    formTitle: 'Add new API key',\n    lastUsed__days: '{{days}}d ago',\n    lastUsed__hours: '{{hours}}h ago',\n    lastUsed__minutes: '{{minutes}}m ago',\n    lastUsed__months: '{{months}}mo ago',\n    lastUsed__seconds: '{{seconds}}s ago',\n    lastUsed__years: '{{years}}y ago',\n    menuAction__revoke: 'Revoke key',\n    revokeConfirmation: {\n      confirmationText: 'Revoke',\n      formButtonPrimary__revoke: 'Revoke key',\n      formHint: 'Are you sure you want to delete this Secret key?',\n      formTitle: 'Revoke \"{{apiKeyName}}\" secret key?',\n    },\n  },\n  backButton: 'Back',\n  badge__activePlan: 'Active',\n  badge__canceledEndsAt: \"Canceled • Ends {{ date | shortDate('en-US') }}\",\n  badge__currentPlan: 'Current plan',\n  badge__default: 'Default',\n  badge__endsAt: \"Ends {{ date | shortDate('en-US') }}\",\n  badge__expired: 'Expired',\n  badge__otherImpersonatorDevice: 'Other impersonator device',\n  badge__primary: 'Primary',\n  badge__renewsAt: \"Renews {{ date | shortDate('en-US') }}\",\n  badge__requiresAction: 'Requires action',\n  badge__startsAt: \"Starts {{ date | shortDate('en-US') }}\",\n  badge__pastDueAt: \"Past due {{ date | shortDate('en-US') }}\",\n  badge__thisDevice: 'This device',\n  badge__unverified: 'Unverified',\n  badge__upcomingPlan: 'Upcoming',\n  badge__pastDuePlan: 'Past due',\n  badge__userDevice: 'User device',\n  badge__you: 'You',\n  commerce: {\n    addPaymentMethod: 'Add payment method',\n    alwaysFree: 'Always free',\n    annually: 'Annually',\n    availableFeatures: 'Available features',\n    billedAnnually: 'Billed annually',\n    billedMonthlyOnly: 'Only billed monthly',\n    cancelSubscription: 'Cancel subscription',\n    cancelSubscriptionAccessUntil:\n      \"You can keep using '{{plan}}' features until {{ date | longDate('en-US') }}, after which you will no longer have access.\",\n    cancelSubscriptionNoCharge: 'You will not be charged for this subscription.',\n    cancelSubscriptionTitle: 'Cancel {{plan}} Subscription?',\n    cannotSubscribeMonthly:\n      'You cannot subscribe to this plan by paying monthly. To subscribe to this plan, you need to choose to pay annually.',\n    cannotSubscribeUnrecoverable:\n      'You cannot subscribe to this plan. Your existing subscription is more expensive than this plan.',\n    checkout: {\n      description__paymentSuccessful: 'Your payment was successful.',\n      description__subscriptionSuccessful: 'Your new subscription is all set.',\n      downgradeNotice:\n        'You will keep your current subscription and its features until the end of the billing cycle, then you will be switched to this subscription.',\n      emailForm: {\n        subtitle: 'Before you can complete your purchase you must add an email address where receipts will be sent.',\n        title: 'Add an email address',\n      },\n      lineItems: {\n        title__paymentMethod: 'Payment method',\n        title__statementId: 'Statement ID',\n        title__subscriptionBegins: 'Subscription begins',\n        title__totalPaid: 'Total paid',\n      },\n      pastDueNotice: 'Your previous subscription was past due, with no payment.',\n      perMonth: 'per month',\n      title: 'Checkout',\n      title__paymentSuccessful: 'Payment was successful!',\n      title__subscriptionSuccessful: 'Success!',\n    },\n    credit: 'Credit',\n    creditRemainder: 'Credit for the remainder of your current subscription.',\n    defaultFreePlanActive: \"You're currently on the Free plan\",\n    free: 'Free',\n    getStarted: 'Get started',\n    keepSubscription: 'Keep subscription',\n    manage: 'Manage',\n    manageSubscription: 'Manage subscription',\n    month: 'Month',\n    monthly: 'Monthly',\n    pastDue: 'Past due',\n    pay: 'Pay {{amount}}',\n    paymentMethods: 'Payment Methods',\n    paymentSource: {\n      applePayDescription: {\n        annual: 'Annual payment',\n        monthly: 'Monthly payment',\n      },\n      dev: {\n        anyNumbers: 'Any numbers',\n        cardNumber: 'Card number',\n        cvcZip: 'CVC, ZIP',\n        developmentMode: 'Development mode',\n        expirationDate: 'Expiration date',\n        testCardInfo: 'Test card information',\n      },\n    },\n    popular: 'Popular',\n    pricingTable: {\n      billingCycle: 'Billing cycle',\n      included: 'Included',\n    },\n    subscriptionDetails: {\n      title: 'Subscription',\n      currentBillingCycle: 'Current billing cycle',\n      nextPaymentOn: 'Next payment on',\n      nextPaymentAmount: 'Next payment amount',\n      subscribedOn: 'Subscribed on',\n      endsOn: 'Ends on',\n      renewsAt: 'Renews at',\n      beginsOn: 'Begins on',\n      pastDueAt: 'Past due on',\n    },\n    reSubscribe: 'Resubscribe',\n    seeAllFeatures: 'See all features',\n    subscribe: 'Subscribe',\n    subtotal: 'Subtotal',\n    switchPlan: 'Switch to this plan',\n    switchToAnnual: 'Switch to annual',\n    switchToMonthly: 'Switch to monthly',\n    switchToMonthlyWithPrice: 'Switch to monthly {{currency}}{{price}} / month',\n    switchToAnnualWithAnnualPrice: 'Switch to annual {{currency}}{{price}} / year',\n    totalDue: 'Total due',\n    totalDueToday: 'Total Due Today',\n    viewFeatures: 'View features',\n    year: 'Year',\n  },\n  createOrganization: {\n    formButtonSubmit: 'Create organization',\n    invitePage: {\n      formButtonReset: 'Skip',\n    },\n    title: 'Create organization',\n  },\n  dates: {\n    lastDay: \"Yesterday at {{ date | timeString('en-US') }}\",\n    next6Days: \"{{ date | weekday('en-US','long') }} at {{ date | timeString('en-US') }}\",\n    nextDay: \"Tomorrow at {{ date | timeString('en-US') }}\",\n    numeric: \"{{ date | numeric('en-US') }}\",\n    previous6Days: \"Last {{ date | weekday('en-US','long') }} at {{ date | timeString('en-US') }}\",\n    sameDay: \"Today at {{ date | timeString('en-US') }}\",\n  },\n  dividerText: 'or',\n  footerActionLink__alternativePhoneCodeProvider: 'Send code via SMS instead',\n  footerActionLink__useAnotherMethod: 'Use another method',\n  footerPageLink__help: 'Help',\n  footerPageLink__privacy: 'Privacy',\n  footerPageLink__terms: 'Terms',\n  formButtonPrimary: 'Continue',\n  formButtonPrimary__verify: 'Verify',\n  formFieldAction__forgotPassword: 'Forgot password?',\n  formFieldError__matchingPasswords: 'Passwords match.',\n  formFieldError__notMatchingPasswords: \"Passwords don't match.\",\n  formFieldError__verificationLinkExpired: 'The verification link expired. Please request a new link.',\n  formFieldHintText__optional: 'Optional',\n  formFieldHintText__slug: 'A slug is a human-readable ID that must be unique. It’s often used in URLs.',\n  formFieldInputPlaceholder__apiKeyDescription: 'Explain why you’re generating this key',\n  formFieldInputPlaceholder__apiKeyExpirationDate: 'Select date',\n  formFieldInputPlaceholder__apiKeyName: 'Enter your secret key name',\n  formFieldInputPlaceholder__backupCode: 'Enter backup code',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Delete account',\n  formFieldInputPlaceholder__emailAddress: 'Enter your email address',\n  formFieldInputPlaceholder__emailAddress_username: 'Enter email or username',\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: 'First name',\n  formFieldInputPlaceholder__lastName: 'Last name',\n  formFieldInputPlaceholder__organizationDomain: 'example.com',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: '<EMAIL>',\n  formFieldInputPlaceholder__organizationName: 'Organization name',\n  formFieldInputPlaceholder__organizationSlug: 'my-org',\n  formFieldInputPlaceholder__password: 'Enter your password',\n  formFieldInputPlaceholder__phoneNumber: 'Enter your phone number',\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: 'Description',\n  formFieldLabel__apiKeyExpiration: 'Expiration',\n  formFieldLabel__apiKeyName: 'Secret key name',\n  formFieldLabel__automaticInvitations: 'Enable automatic invitations for this domain',\n  formFieldLabel__backupCode: 'Backup code',\n  formFieldLabel__confirmDeletion: 'Confirmation',\n  formFieldLabel__confirmPassword: 'Confirm password',\n  formFieldLabel__currentPassword: 'Current password',\n  formFieldLabel__emailAddress: 'Email address',\n  formFieldLabel__emailAddress_username: 'Email address or username',\n  formFieldLabel__emailAddresses: 'Email addresses',\n  formFieldLabel__firstName: 'First name',\n  formFieldLabel__lastName: 'Last name',\n  formFieldLabel__newPassword: 'New password',\n  formFieldLabel__organizationDomain: 'Domain',\n  formFieldLabel__organizationDomainDeletePending: 'Delete pending invitations and suggestions',\n  formFieldLabel__organizationDomainEmailAddress: 'Verification email address',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Enter an email address under this domain to receive a code and verify this domain.',\n  formFieldLabel__organizationName: 'Name',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Name of passkey',\n  formFieldLabel__password: 'Password',\n  formFieldLabel__phoneNumber: 'Phone number',\n  formFieldLabel__role: 'Role',\n  formFieldLabel__signOutOfOtherSessions: 'Sign out of all other devices',\n  formFieldLabel__username: 'Username',\n  impersonationFab: {\n    action__signOut: 'Sign out',\n    title: 'Signed in as {{identifier}}',\n  },\n  maintenanceMode:\n    \"We are currently undergoing maintenance, but don't worry, it shouldn't take more than a few minutes.\",\n  membershipRole__admin: 'Admin',\n  membershipRole__basicMember: 'Member',\n  membershipRole__guestMember: 'Guest',\n  organizationList: {\n    action__createOrganization: 'Create organization',\n    action__invitationAccept: 'Join',\n    action__suggestionsAccept: 'Request to join',\n    createOrganization: 'Create Organization',\n    invitationAcceptedLabel: 'Joined',\n    subtitle: 'to continue to {{applicationName}}',\n    suggestionsAcceptedLabel: 'Pending approval',\n    title: 'Choose an account',\n    titleWithoutPersonal: 'Choose an organization',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: 'API keys',\n    },\n    badge__automaticInvitation: 'Automatic invitations',\n    badge__automaticSuggestion: 'Automatic suggestions',\n    badge__manualInvitation: 'No automatic enrollment',\n    badge__unverified: 'Unverified',\n    billingPage: {\n      paymentHistorySection: {\n        empty: 'No payment history',\n        notFound: 'Payment attempt not found',\n        tableHeader__amount: 'Amount',\n        tableHeader__date: 'Date',\n        tableHeader__status: 'Status',\n      },\n      paymentSourcesSection: {\n        actionLabel__default: 'Make default',\n        actionLabel__remove: 'Remove',\n        add: 'Add new payment method',\n        addSubtitle: 'Add a new payment method to your account.',\n        cancelButton: 'Cancel',\n        formButtonPrimary__add: 'Add Payment Method',\n        formButtonPrimary__pay: 'Pay {{amount}}',\n        payWithTestCardButton: 'Pay with test card',\n        removeResource: {\n          messageLine1: '{{identifier}} will be removed from this account.',\n          messageLine2:\n            'You will no longer be able to use this payment source and any recurring subscriptions dependent on it will no longer work.',\n          successMessage: '{{paymentSource}} has been removed from your account.',\n          title: 'Remove payment method',\n        },\n        title: 'Payment methods',\n      },\n      start: {\n        headerTitle__payments: 'Payments',\n        headerTitle__plans: 'Plans',\n        headerTitle__statements: 'Statements',\n        headerTitle__subscriptions: 'Subscription',\n      },\n      statementsSection: {\n        empty: 'No statements to display',\n        itemCaption__paidForPlan: 'Paid for {{plan}} {{period}} plan',\n        itemCaption__proratedCredit: 'Prorated credit for partial usage of previous subscription',\n        itemCaption__subscribedAndPaidForPlan: 'Subscribed and paid for {{plan}} {{period}} plan',\n        notFound: 'Statement not found',\n        tableHeader__amount: 'Amount',\n        tableHeader__date: 'Date',\n        title: 'Statements',\n        totalPaid: 'Total paid',\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: 'Subscribe to a plan',\n        actionLabel__switchPlan: 'Switch plans',\n        tableHeader__edit: 'Edit',\n        tableHeader__plan: 'Plan',\n        tableHeader__startDate: 'Start date',\n        title: 'Subscription',\n      },\n      subscriptionsSection: {\n        actionLabel__default: 'Manage',\n      },\n      switchPlansSection: {\n        title: 'Switch plans',\n      },\n      title: 'Billing',\n    },\n    createDomainPage: {\n      subtitle:\n        'Add the domain to verify. Users with email addresses at this domain can join the organization automatically or request to join.',\n      title: 'Add domain',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'The invitations could not be sent. There are already pending invitations for the following email addresses: {{email_addresses}}.',\n      formButtonPrimary__continue: 'Send invitations',\n      selectDropdown__role: 'Select role',\n      subtitle: 'Enter or paste one or more email addresses, separated by spaces or commas.',\n      successMessage: 'Invitations successfully sent',\n      title: 'Invite new members',\n    },\n    membersPage: {\n      action__invite: 'Invite',\n      action__search: 'Search',\n      activeMembersTab: {\n        menuAction__remove: 'Remove member',\n        tableHeader__actions: 'Actions',\n        tableHeader__joined: 'Joined',\n        tableHeader__role: 'Role',\n        tableHeader__user: 'User',\n      },\n      detailsTitle__emptyRow: 'No members to display',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Invite users by connecting an email domain with your organization. Anyone who signs up with a matching email domain will be able to join the organization anytime.',\n          headerTitle: 'Automatic invitations',\n          primaryButton: 'Manage verified domains',\n        },\n        table__emptyRow: 'No invitations to display',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Revoke invitation',\n        tableHeader__invited: 'Invited',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Users who sign up with a matching email domain, will be able to see a suggestion to request to join your organization.',\n          headerTitle: 'Automatic suggestions',\n          primaryButton: 'Manage verified domains',\n        },\n        menuAction__approve: 'Approve',\n        menuAction__reject: 'Reject',\n        tableHeader__requested: 'Requested access',\n        table__emptyRow: 'No requests to display',\n      },\n      start: {\n        headerTitle__invitations: 'Invitations',\n        headerTitle__members: 'Members',\n        headerTitle__requests: 'Requests',\n      },\n    },\n    navbar: {\n      apiKeys: 'API keys',\n      billing: 'Billing',\n      description: 'Manage your organization.',\n      general: 'General',\n      members: 'Members',\n      title: 'Organization',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: 'You do not have permissions to manage billing for this organization.',\n      },\n      title: 'Plans',\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Type \"{{organizationName}}\" below to continue.',\n          messageLine1: 'Are you sure you want to delete this organization?',\n          messageLine2: 'This action is permanent and irreversible.',\n          successMessage: 'You have deleted the organization.',\n          title: 'Delete organization',\n        },\n        leaveOrganization: {\n          actionDescription: 'Type \"{{organizationName}}\" below to continue.',\n          messageLine1:\n            'Are you sure you want to leave this organization? You will lose access to this organization and its applications.',\n          messageLine2: 'This action is permanent and irreversible.',\n          successMessage: 'You have left the organization.',\n          title: 'Leave organization',\n        },\n        title: 'Danger',\n      },\n      domainSection: {\n        menuAction__manage: 'Manage',\n        menuAction__remove: 'Delete',\n        menuAction__verify: 'Verify',\n        primaryButton: 'Add domain',\n        subtitle:\n          'Allow users to join the organization automatically or request to join based on a verified email domain.',\n        title: 'Verified domains',\n      },\n      successMessage: 'The organization has been updated.',\n      title: 'Update profile',\n    },\n    removeDomainPage: {\n      messageLine1: 'The email domain {{domain}} will be removed.',\n      messageLine2: 'Users won’t be able to join the organization automatically after this.',\n      successMessage: '{{domain}} has been removed.',\n      title: 'Remove domain',\n    },\n    start: {\n      headerTitle__general: 'General',\n      headerTitle__members: 'Members',\n      profileSection: {\n        primaryButton: 'Update profile',\n        title: 'Organization Profile',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Removing this domain will affect invited users.',\n        removeDomainActionLabel__remove: 'Remove domain',\n        removeDomainSubtitle: 'Remove this domain from your verified domains',\n        removeDomainTitle: 'Remove domain',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Users are automatically invited to join the organization when they sign-up and can join anytime.',\n        automaticInvitationOption__label: 'Automatic invitations',\n        automaticSuggestionOption__description:\n          'Users receive a suggestion to request to join, but must be approved by an admin before they are able to join the organization.',\n        automaticSuggestionOption__label: 'Automatic suggestions',\n        calloutInfoLabel: 'Changing the enrollment mode will only affect new users.',\n        calloutInvitationCountLabel: 'Pending invitations sent to users: {{count}}',\n        calloutSuggestionCountLabel: 'Pending suggestions sent to users: {{count}}',\n        manualInvitationOption__description: 'Users can only be invited manually to the organization.',\n        manualInvitationOption__label: 'No automatic enrollment',\n        subtitle: 'Choose how users from this domain can join the organization.',\n      },\n      start: {\n        headerTitle__danger: 'Danger',\n        headerTitle__enrollment: 'Enrollment options',\n      },\n      subtitle: 'The domain {{domain}} is now verified. Continue by selecting enrollment mode.',\n      title: 'Update {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Enter the verification code sent to your email address',\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'The domain {{domainName}} needs to be verified via email.',\n      subtitleVerificationCodeScreen: 'A verification code was sent to {{emailAddress}}. Enter the code to continue.',\n      title: 'Verify domain',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Create organization',\n    action__invitationAccept: 'Join',\n    action__manageOrganization: 'Manage',\n    action__suggestionsAccept: 'Request to join',\n    notSelected: 'No organization selected',\n    personalWorkspace: 'Personal account',\n    suggestionsAcceptedLabel: 'Pending approval',\n  },\n  paginationButton__next: 'Next',\n  paginationButton__previous: 'Previous',\n  paginationRowText__displaying: 'Displaying',\n  paginationRowText__of: 'of',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Get help',\n      actionText: 'Don’t have any of these?',\n      blockButton__backupCode: 'Use a backup code',\n      blockButton__emailCode: 'Email code to {{identifier}}',\n      blockButton__passkey: 'Use your passkey',\n      blockButton__password: 'Continue with your password',\n      blockButton__phoneCode: 'Send SMS code to {{identifier}}',\n      blockButton__totp: 'Use your authenticator app',\n      getHelp: {\n        blockButton__emailSupport: 'Email support',\n        content:\n          'If you have trouble verifying your account, email us and we will work with you to restore access as soon as possible.',\n        title: 'Get help',\n      },\n      subtitle: 'Facing issues? You can use any of these methods for verification.',\n      title: 'Use another method',\n    },\n    backupCodeMfa: {\n      subtitle: 'Enter the backup code you received when setting up two-step authentication',\n      title: 'Enter a backup code',\n    },\n    emailCode: {\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'Enter the code sent to your email to continue',\n      title: 'Verification required',\n    },\n    noAvailableMethods: {\n      message: 'Cannot proceed with verification. No suitable authentication factor is configured',\n      subtitle: 'An error occurred',\n      title: 'Cannot verify your account',\n    },\n    passkey: {\n      blockButton__passkey: 'Use your passkey',\n      subtitle:\n        'Using your passkey confirms your identity. Your device may ask for your fingerprint, face, or screen lock.',\n      title: 'Use your passkey',\n    },\n    password: {\n      actionLink: 'Use another method',\n      subtitle: 'Enter your current password to continue',\n      title: 'Verification required',\n    },\n    phoneCode: {\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'Enter the code sent to your phone to continue',\n      title: 'Verification required',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'Enter the code sent to your phone to continue',\n      title: 'Verification required',\n    },\n    totpMfa: {\n      formTitle: 'Verification code',\n      subtitle: 'Enter the code generated by your authenticator app to continue',\n      title: 'Verification required',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Add account',\n      action__signOutAll: 'Sign out of all accounts',\n      subtitle: 'Select the account with which you wish to continue.',\n      title: 'Choose an account',\n    },\n    alternativeMethods: {\n      actionLink: 'Get help',\n      actionText: 'Don’t have any of these?',\n      blockButton__backupCode: 'Use a backup code',\n      blockButton__emailCode: 'Email code to {{identifier}}',\n      blockButton__emailLink: 'Email link to {{identifier}}',\n      blockButton__passkey: 'Sign in with your passkey',\n      blockButton__password: 'Sign in with your password',\n      blockButton__phoneCode: 'Send SMS code to {{identifier}}',\n      blockButton__totp: 'Use your authenticator app',\n      getHelp: {\n        blockButton__emailSupport: 'Email support',\n        content:\n          'If you have trouble signing into your account, email us and we will work with you to restore access as soon as possible.',\n        title: 'Get help',\n      },\n      subtitle: 'Facing issues? You can use any of these methods to sign in.',\n      title: 'Use another method',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'to continue to {{applicationName}}',\n      title: 'Check your {{provider}}',\n    },\n    backupCodeMfa: {\n      subtitle: 'Your backup code is the one you got when setting up two-step authentication.',\n      title: 'Enter a backup code',\n    },\n    emailCode: {\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'to continue to {{applicationName}}',\n      title: 'Check your email',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'To continue, open the verification link on the device and browser from which you initiated the sign-in',\n        title: 'Verification link is invalid for this device',\n      },\n      expired: {\n        subtitle: 'Return to the original tab to continue.',\n        title: 'This verification link has expired',\n      },\n      failed: {\n        subtitle: 'Return to the original tab to continue.',\n        title: 'This verification link is invalid',\n      },\n      formSubtitle: 'Use the verification link sent to your email',\n      formTitle: 'Verification link',\n      loading: {\n        subtitle: 'You will be redirected soon',\n        title: 'Signing in...',\n      },\n      resendButton: \"Didn't receive a link? Resend\",\n      subtitle: 'to continue to {{applicationName}}',\n      title: 'Check your email',\n      unusedTab: {\n        title: 'You may close this tab',\n      },\n      verified: {\n        subtitle: 'You will be redirected soon',\n        title: 'Successfully signed in',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Return to original tab to continue',\n        subtitleNewTab: 'Return to the newly opened tab to continue',\n        titleNewTab: 'Signed in on other tab',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Reset password code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'to reset your password',\n      subtitle_email: 'First, enter the code sent to your email address',\n      subtitle_phone: 'First, enter the code sent to your phone',\n      title: 'Reset password',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Reset your password',\n      label__alternativeMethods: 'Or, sign in with another method',\n      title: 'Forgot Password?',\n    },\n    noAvailableMethods: {\n      message: \"Cannot proceed with sign in. There's no available authentication factor.\",\n      subtitle: 'An error occurred',\n      title: 'Cannot sign in',\n    },\n    passkey: {\n      subtitle: \"Using your passkey confirms it's you. Your device may ask for your fingerprint, face or screen lock.\",\n      title: 'Use your passkey',\n    },\n    password: {\n      actionLink: 'Use another method',\n      subtitle: 'Enter the password associated with your account',\n      title: 'Enter your password',\n    },\n    passwordPwned: {\n      title: 'Password compromised',\n    },\n    phoneCode: {\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'to continue to {{applicationName}}',\n      title: 'Check your phone',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'To continue, please enter the verification code sent to your phone',\n      title: 'Check your phone',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Reset Password',\n      requiredMessage: 'For security reasons, it is required to reset your password.',\n      successMessage: 'Your password was successfully changed. Signing you in, please wait a moment.',\n      title: 'Set new password',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'We need to verify your identity before resetting your password.',\n    },\n    start: {\n      actionLink: 'Sign up',\n      actionLink__join_waitlist: 'Join waitlist',\n      actionLink__use_email: 'Use email',\n      actionLink__use_email_username: 'Use email or username',\n      actionLink__use_passkey: 'Use passkey instead',\n      actionLink__use_phone: 'Use phone',\n      actionLink__use_username: 'Use username',\n      actionText: 'Don’t have an account?',\n      actionText__join_waitlist: 'Want early access?',\n      alternativePhoneCodeProvider: {\n        actionLink: 'Use another method',\n        label: '{{provider}} phone number',\n        subtitle: 'Enter your phone number to get a verification code on {{provider}}.',\n        title: 'Sign in to {{applicationName}} with {{provider}}',\n      },\n      subtitle: 'Welcome back! Please sign in to continue',\n      subtitleCombined: undefined,\n      title: 'Sign in to {{applicationName}}',\n      titleCombined: 'Continue to {{applicationName}}',\n    },\n    totpMfa: {\n      formTitle: 'Verification code',\n      subtitle: 'To continue, please enter the verification code generated by your authenticator app',\n      title: 'Two-step verification',\n    },\n  },\n  signInEnterPasswordTitle: 'Enter your password',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'Enter the verification code sent to your {{provider}}',\n      title: 'Verify your {{provider}}',\n    },\n    continue: {\n      actionLink: 'Sign in',\n      actionText: 'Already have an account?',\n      subtitle: 'Please fill in the remaining details to continue.',\n      title: 'Fill in missing fields',\n    },\n    emailCode: {\n      formSubtitle: 'Enter the verification code sent to your email address',\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'Enter the verification code sent to your email',\n      title: 'Verify your email',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'To continue, open the verification link on the device and browser from which you initiated the sign-up',\n        title: 'Verification link is invalid for this device',\n      },\n      formSubtitle: 'Use the verification link sent to your email address',\n      formTitle: 'Verification link',\n      loading: {\n        title: 'Signing up...',\n      },\n      resendButton: \"Didn't receive a link? Resend\",\n      subtitle: 'to continue to {{applicationName}}',\n      title: 'Verify your email',\n      verified: {\n        title: 'Successfully signed up',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Return to the newly opened tab to continue',\n        subtitleNewTab: 'Return to previous tab to continue',\n        title: 'Successfully verified email',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'I agree to the {{ privacyPolicyLink || link(\"Privacy Policy\") }}',\n        label__onlyTermsOfService: 'I agree to the {{ termsOfServiceLink || link(\"Terms of Service\") }}',\n        label__termsOfServiceAndPrivacyPolicy:\n          'I agree to the {{ termsOfServiceLink || link(\"Terms of Service\") }} and {{ privacyPolicyLink || link(\"Privacy Policy\") }}',\n      },\n      continue: {\n        subtitle: 'Please read and accept the terms to continue',\n        title: 'Legal consent',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Enter the verification code sent to your phone number',\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'Enter the verification code sent to your phone',\n      title: 'Verify your phone',\n    },\n    restrictedAccess: {\n      actionLink: 'Sign in',\n      actionText: 'Already have an account?',\n      blockButton__emailSupport: 'Email support',\n      blockButton__joinWaitlist: 'Join waitlist',\n      subtitle: 'Sign ups are currently disabled. If you believe you should have access, please contact support.',\n      subtitleWaitlist: 'Sign ups are currently disabled. To be the first to know when we launch, join the waitlist.',\n      title: 'Access restricted',\n    },\n    start: {\n      actionLink: 'Sign in',\n      actionLink__use_email: 'Use email instead',\n      actionLink__use_phone: 'Use phone instead',\n      actionText: 'Already have an account?',\n      alternativePhoneCodeProvider: {\n        actionLink: 'Use another method',\n        label: '{{provider}} phone number',\n        subtitle: 'Enter your phone number to get a verification code on {{provider}}.',\n        title: 'Sign up to {{applicationName}} with {{provider}}',\n      },\n      subtitle: 'Welcome! Please fill in the details to get started.',\n      subtitleCombined: 'Welcome! Please fill in the details to get started.',\n      title: 'Create your account',\n      titleCombined: 'Create your account',\n    },\n  },\n  socialButtonsBlockButton: 'Continue with {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: '{{email}} is already a member of the organization.',\n    captcha_invalid: undefined,\n    captcha_unavailable:\n      'Sign up unsuccessful due to failed bot validation. Please refresh the page to try again or reach out to support for more assistance.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: undefined,\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: undefined,\n    form_param_format_invalid__phone_number: undefined,\n    form_param_max_length_exceeded__first_name: undefined,\n    form_param_max_length_exceeded__last_name: undefined,\n    form_param_max_length_exceeded__name: undefined,\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: 'Your password is too short. It must be at least 8 characters long.',\n    form_password_not_strong_enough: 'Your password is not strong enough.',\n    form_password_pwned:\n      'This password has been found as part of a breach and can not be used, please try another password instead.',\n    form_password_pwned__sign_in:\n      'This password has been found as part of a breach and can not be used, please reset your password.',\n    form_password_size_in_bytes_exceeded: undefined,\n    form_password_validation_failed: undefined,\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: 'Your username must be between {{min_length}} and {{max_length}} characters long.',\n    identification_deletion_failed: undefined,\n    not_allowed_access: undefined,\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: 'A passkey is already registered with this device.',\n    passkey_not_supported: 'Passkeys are not supported on this device.',\n    passkey_pa_not_supported: 'Registration requires a platform authenticator but the device does not support it.',\n    passkey_registration_cancelled: 'Passkey registration was cancelled or timed out.',\n    passkey_retrieval_cancelled: 'Passkey verification was cancelled or timed out.',\n    passwordComplexity: {\n      maximumLength: 'less than {{length}} characters',\n      minimumLength: '{{length}} or more characters',\n      requireLowercase: 'a lowercase letter',\n      requireNumbers: 'a number',\n      requireSpecialCharacter: 'a special character',\n      requireUppercase: 'an uppercase letter',\n      sentencePrefix: 'Your password must contain',\n    },\n    phone_number_exists: undefined,\n    session_exists: undefined,\n    web3_missing_identifier: 'A Web3 Wallet extension cannot be found. Please install one to continue.',\n    zxcvbn: {\n      couldBeStronger: 'Your password works, but could be stronger. Try adding more characters.',\n      goodPassword: 'Your password meets all the necessary requirements.',\n      notEnough: 'Your password is not strong enough.',\n      suggestions: {\n        allUppercase: 'Capitalize some, but not all letters.',\n        anotherWord: 'Add more words that are less common.',\n        associatedYears: 'Avoid years that are associated with you.',\n        capitalization: 'Capitalize more than the first letter.',\n        dates: 'Avoid dates and years that are associated with you.',\n        l33t: \"Avoid predictable letter substitutions like '@' for 'a'.\",\n        longerKeyboardPattern: 'Use longer keyboard patterns and change typing direction multiple times.',\n        noNeed: 'You can create strong passwords without using symbols, numbers, or uppercase letters.',\n        pwned: 'If you use this password elsewhere, you should change it.',\n        recentYears: 'Avoid recent years.',\n        repeated: 'Avoid repeated words and characters.',\n        reverseWords: 'Avoid reversed spellings of common words.',\n        sequences: 'Avoid common character sequences.',\n        useWords: 'Use multiple words, but avoid common phrases.',\n      },\n      warnings: {\n        common: 'This is a commonly used password.',\n        commonNames: 'Common names and surnames are easy to guess.',\n        dates: 'Dates are easy to guess.',\n        extendedRepeat: 'Repeated character patterns like \"abcabcabc\" are easy to guess.',\n        keyPattern: 'Short keyboard patterns are easy to guess.',\n        namesByThemselves: 'Single names or surnames are easy to guess.',\n        pwned: 'Your password was exposed by a data breach on the Internet.',\n        recentYears: 'Recent years are easy to guess.',\n        sequences: 'Common character sequences like \"abc\" are easy to guess.',\n        similarToCommon: 'This is similar to a commonly used password.',\n        simpleRepeat: 'Repeated characters like \"aaa\" are easy to guess.',\n        straightRow: 'Straight rows of keys on your keyboard are easy to guess.',\n        topHundred: 'This is a frequently used password.',\n        topTen: 'This is a heavily used password.',\n        userInputs: 'There should not be any personal or page related data.',\n        wordByItself: 'Single words are easy to guess.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Add account',\n    action__manageAccount: 'Manage account',\n    action__signOut: 'Sign out',\n    action__signOutAll: 'Sign out of all accounts',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: 'API keys',\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Copied!',\n      actionLabel__copy: 'Copy all',\n      actionLabel__download: 'Download .txt',\n      actionLabel__print: 'Print',\n      infoText1: 'Backup codes will be enabled for this account.',\n      infoText2:\n        'Keep the backup codes secret and store them securely. You may regenerate backup codes if you suspect they have been compromised.',\n      subtitle__codelist: 'Store them securely and keep them secret.',\n      successMessage:\n        'Backup codes are now enabled. You can use one of these to sign in to your account, if you lose access to your authentication device. Each code can only be used once.',\n      successSubtitle:\n        'You can use one of these to sign in to your account, if you lose access to your authentication device.',\n      title: 'Add backup code verification',\n      title__codelist: 'Backup codes',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: 'No payment history',\n        notFound: 'Payment attempt not found',\n        tableHeader__amount: 'Amount',\n        tableHeader__date: 'Date',\n        tableHeader__status: 'Status',\n      },\n      paymentSourcesSection: {\n        actionLabel__default: 'Make default',\n        actionLabel__remove: 'Remove',\n        add: 'Add new payment method',\n        addSubtitle: 'Add a new payment method to your account.',\n        cancelButton: 'Cancel',\n        formButtonPrimary__add: 'Add Payment Method',\n        formButtonPrimary__pay: 'Pay {{amount}}',\n        payWithTestCardButton: 'Pay with test card',\n        removeResource: {\n          messageLine1: '{{identifier}} will be removed from this account.',\n          messageLine2:\n            'You will no longer be able to use this payment source and any recurring subscriptions dependent on it will no longer work.',\n          successMessage: '{{paymentSource}} has been removed from your account.',\n          title: 'Remove payment method',\n        },\n        title: 'Payment methods',\n      },\n      start: {\n        headerTitle__payments: 'Payments',\n        headerTitle__plans: 'Plans',\n        headerTitle__statements: 'Statements',\n        headerTitle__subscriptions: 'Subscription',\n      },\n      statementsSection: {\n        empty: 'No statements to display',\n        itemCaption__paidForPlan: 'Paid for {{plan}} {{period}} plan',\n        itemCaption__proratedCredit: 'Prorated credit for partial usage of previous subscription',\n        itemCaption__subscribedAndPaidForPlan: 'Subscribed and paid for {{plan}} {{period}} plan',\n        notFound: 'Statement not found',\n        tableHeader__amount: 'Amount',\n        tableHeader__date: 'Date',\n        title: 'Statements',\n        totalPaid: 'Total paid',\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: 'Subscribe to a plan',\n        actionLabel__switchPlan: 'Switch plans',\n        tableHeader__edit: 'Edit',\n        tableHeader__plan: 'Plan',\n        tableHeader__startDate: 'Start date',\n        title: 'Subscription',\n      },\n      subscriptionsSection: {\n        actionLabel__default: 'Manage',\n      },\n      switchPlansSection: {\n        title: 'Switch plans',\n      },\n      title: 'Billing',\n    },\n    connectedAccountPage: {\n      formHint: 'Select a provider to connect your account.',\n      formHint__noAccounts: 'There are no available external account providers.',\n      removeResource: {\n        messageLine1: '{{identifier}} will be removed from this account.',\n        messageLine2:\n          'You will no longer be able to use this connected account and any dependent features will no longer work.',\n        successMessage: '{{connectedAccount}} has been removed from your account.',\n        title: 'Remove connected account',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}',\n      successMessage: 'The provider has been added to your account',\n      title: 'Add connected account',\n    },\n    deletePage: {\n      actionDescription: 'Type \"Delete account\" below to continue.',\n      confirm: 'Delete account',\n      messageLine1: 'Are you sure you want to delete your account?',\n      messageLine2: 'This action is permanent and irreversible.',\n      title: 'Delete account',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'An email containing a verification code will be sent to this email address.',\n        formSubtitle: 'Enter the verification code sent to {{identifier}}',\n        formTitle: 'Verification code',\n        resendButton: \"Didn't receive a code? Resend\",\n        successMessage: 'The email {{identifier}} has been added to your account.',\n      },\n      emailLink: {\n        formHint: 'An email containing a verification link will be sent to this email address.',\n        formSubtitle: 'Click on the verification link in the email sent to {{identifier}}',\n        formTitle: 'Verification link',\n        resendButton: \"Didn't receive a link? Resend\",\n        successMessage: 'The email {{identifier}} has been added to your account.',\n      },\n      enterpriseSSOLink: {\n        formButton: 'Click to sign-in',\n        formSubtitle: 'Complete the sign-in with {{identifier}}',\n      },\n      formHint: \"You'll need to verify this email address before it can be added to your account.\",\n      removeResource: {\n        messageLine1: '{{identifier}} will be removed from this account.',\n        messageLine2: 'You will no longer be able to sign in using this email address.',\n        successMessage: '{{emailAddress}} has been removed from your account.',\n        title: 'Remove email address',\n      },\n      title: 'Add email address',\n      verifyTitle: 'Verify email address',\n    },\n    formButtonPrimary__add: 'Add',\n    formButtonPrimary__continue: 'Continue',\n    formButtonPrimary__finish: 'Finish',\n    formButtonPrimary__remove: 'Remove',\n    formButtonPrimary__save: 'Save',\n    formButtonReset: 'Cancel',\n    mfaPage: {\n      formHint: 'Select a method to add.',\n      title: 'Add two-step verification',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Use existing number',\n      primaryButton__addPhoneNumber: 'Add phone number',\n      removeResource: {\n        messageLine1: '{{identifier}} will be no longer receiving verification codes when signing in.',\n        messageLine2: 'Your account may not be as secure. Are you sure you want to continue?',\n        successMessage: 'SMS code two-step verification has been removed for {{mfaPhoneCode}}',\n        title: 'Remove two-step verification',\n      },\n      subtitle__availablePhoneNumbers:\n        'Select an existing phone number to register for SMS code two-step verification or add a new one.',\n      subtitle__unavailablePhoneNumbers:\n        'There are no available phone numbers to register for SMS code two-step verification, please add a new one.',\n      successMessage1:\n        'When signing in, you will need to enter a verification code sent to this phone number as an additional step.',\n      successMessage2:\n        'Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.',\n      successTitle: 'SMS code verification enabled',\n      title: 'Add SMS code verification',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Scan QR code instead',\n        buttonUnableToScan__nonPrimary: 'Can’t scan QR code?',\n        infoText__ableToScan:\n          'Set up a new sign-in method in your authenticator app and scan the following QR code to link it to your account.',\n        infoText__unableToScan: 'Set up a new sign-in method in your authenticator and enter the Key provided below.',\n        inputLabel__unableToScan1:\n          'Make sure Time-based or One-time passwords is enabled, then finish linking your account.',\n        inputLabel__unableToScan2:\n          'Alternatively, if your authenticator supports TOTP URIs, you can also copy the full URI.',\n      },\n      removeResource: {\n        messageLine1: 'Verification codes from this authenticator will no longer be required when signing in.',\n        messageLine2: 'Your account may not be as secure. Are you sure you want to continue?',\n        successMessage: 'Two-step verification via authenticator application has been removed.',\n        title: 'Remove two-step verification',\n      },\n      successMessage:\n        'Two-step verification is now enabled. When signing in, you will need to enter a verification code from this authenticator as an additional step.',\n      title: 'Add authenticator application',\n      verifySubtitle: 'Enter verification code generated by your authenticator',\n      verifyTitle: 'Verification code',\n    },\n    mobileButton__menu: 'Menu',\n    navbar: {\n      account: 'Profile',\n      apiKeys: 'API keys',\n      billing: 'Billing',\n      description: 'Manage your account info.',\n      security: 'Security',\n      title: 'Account',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} will be removed from this account.',\n        title: 'Remove passkey',\n      },\n      subtitle__rename: 'You can change the passkey name to make it easier to find.',\n      title__rename: 'Rename Passkey',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'It is recommended to sign out of all other devices which may have used your old password.',\n      readonly: 'Your password can currently not be edited because you can sign in only via the enterprise connection.',\n      successMessage__set: 'Your password has been set.',\n      successMessage__signOutOfOtherSessions: 'All other devices have been signed out.',\n      successMessage__update: 'Your password has been updated.',\n      title__set: 'Set password',\n      title__update: 'Update password',\n    },\n    phoneNumberPage: {\n      infoText:\n        'A text message containing a verification code will be sent to this phone number. Message and data rates may apply.',\n      removeResource: {\n        messageLine1: '{{identifier}} will be removed from this account.',\n        messageLine2: 'You will no longer be able to sign in using this phone number.',\n        successMessage: '{{phoneNumber}} has been removed from your account.',\n        title: 'Remove phone number',\n      },\n      successMessage: '{{identifier}} has been added to your account.',\n      title: 'Add phone number',\n      verifySubtitle: 'Enter the verification code sent to {{identifier}}',\n      verifyTitle: 'Verify phone number',\n    },\n    plansPage: {\n      title: 'Plans',\n    },\n    profilePage: {\n      fileDropAreaHint: 'Recommended size 1:1, up to 10MB.',\n      imageFormDestructiveActionSubtitle: 'Remove',\n      imageFormSubtitle: 'Upload',\n      imageFormTitle: 'Profile image',\n      readonly: 'Your profile information has been provided by the enterprise connection and cannot be edited.',\n      successMessage: 'Your profile has been updated.',\n      title: 'Update profile',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Sign out of device',\n        title: 'Active devices',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Reconnect',\n        actionLabel__reauthorize: 'Authorize now',\n        destructiveActionTitle: 'Remove',\n        primaryButton: 'Connect account',\n        subtitle__disconnected: 'This account has been disconnected.',\n        subtitle__reauthorize:\n          'The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues',\n        title: 'Connected accounts',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Delete account',\n        title: 'Delete account',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Remove email',\n        detailsAction__nonPrimary: 'Set as primary',\n        detailsAction__primary: 'Complete verification',\n        detailsAction__unverified: 'Verify',\n        primaryButton: 'Add email address',\n        title: 'Email addresses',\n      },\n      enterpriseAccountsSection: {\n        title: 'Enterprise accounts',\n      },\n      headerTitle__account: 'Profile details',\n      headerTitle__security: 'Security',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Regenerate',\n          headerTitle: 'Backup codes',\n          subtitle__regenerate:\n            'Get a fresh set of secure backup codes. Prior backup codes will be deleted and cannot be used.',\n          title__regenerate: 'Regenerate backup codes',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Set as default',\n          destructiveActionLabel: 'Remove',\n        },\n        primaryButton: 'Add two-step verification',\n        title: 'Two-step verification',\n        totp: {\n          destructiveActionTitle: 'Remove',\n          headerTitle: 'Authenticator application',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Remove',\n        menuAction__rename: 'Rename',\n        primaryButton: 'Add a passkey',\n        title: 'Passkeys',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Set password',\n        primaryButton__updatePassword: 'Update password',\n        title: 'Password',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Remove phone number',\n        detailsAction__nonPrimary: 'Set as primary',\n        detailsAction__primary: 'Complete verification',\n        detailsAction__unverified: 'Verify phone number',\n        primaryButton: 'Add phone number',\n        title: 'Phone numbers',\n      },\n      profileSection: {\n        primaryButton: 'Update profile',\n        title: 'Profile',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Set username',\n        primaryButton__updateUsername: 'Update username',\n        title: 'Username',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Remove wallet',\n        detailsAction__nonPrimary: 'Set as primary',\n        primaryButton: 'Connect wallet',\n        title: 'Web3 wallets',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Your username has been updated.',\n      title__set: 'Set username',\n      title__update: 'Update username',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} will be removed from this account.',\n        messageLine2: 'You will no longer be able to sign in using this web3 wallet.',\n        successMessage: '{{web3Wallet}} has been removed from your account.',\n        title: 'Remove web3 wallet',\n      },\n      subtitle__availableWallets: 'Select a web3 wallet to connect to your account.',\n      subtitle__unavailableWallets: 'There are no available web3 wallets.',\n      successMessage: 'The wallet has been added to your account.',\n      title: 'Add web3 wallet',\n      web3WalletButtonsBlockButton: '{{provider|titleize}}',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: 'Sign in',\n      actionText: 'Already have access?',\n      formButton: 'Join the waitlist',\n      subtitle: 'Enter your email address and we’ll let you know when your spot is ready',\n      title: 'Join the waitlist',\n    },\n    success: {\n      message: 'You will be redirected soon...',\n      subtitle: 'We’ll be in touch when your spot is ready',\n      title: 'Thanks for joining the waitlist!',\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCACE;AAAA,IACF,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BACE;AAAA,IACF,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBACE;AAAA,IACF,8BACE;AAAA,IACF,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBACE;AAAA,MACF,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,qBAAqB;AAAA,MACnB,OAAO;AAAA,MACP,qBAAqB;AAAA,MACrB,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,0BAA0B;AAAA,IAC1B,+BAA+B;AAAA,IAC/B,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBACE;AAAA,EACF,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cACE;AAAA,UACF,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBAAiB;AAAA,IACjB,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BACE;AAAA,IACF,sCAAsC;AAAA,IACtC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBAAoB;AAAA,IACpB,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cACE;AAAA,UACF,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBAAwB;AAAA,QACxB,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UACE;AAAA,MACF,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}