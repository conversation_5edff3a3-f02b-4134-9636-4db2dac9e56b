{"version": 3, "sources": ["../src/mn-MN.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const mnMN: LocalizationResource = {\n  locale: 'mn-MN',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Буцах',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Анхдагч',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Бусад дуурайгч төхөөрөмж',\n  badge__primary: 'Үндсэн',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Үйлдэл шаардлагтай',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Энэ төхөөрөмж',\n  badge__unverified: 'Баталгаажаагүй',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Хэрэглэгчийн төхөөрөмж',\n  badge__you: 'Чи',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Байгуулга үүсгэх',\n    invitePage: {\n      formButtonReset: 'Алгасах',\n    },\n    title: 'Байгуулга үүсгэх',\n  },\n  dates: {\n    lastDay: \"Өчигдөр {{ date | timeString('mn-MN') }} цагт\",\n    next6Days: \"{{ date | weekday('mn-MN','long') }} -с {{ date | timeString('mn-MN') }} хүртэл\",\n    nextDay: \"Маргааш {{ date | timeString('mn-MN') }} цагт\",\n    numeric: \"{{ date | numeric('mn-MN') }}\",\n    previous6Days: \"{{ date | weekday('mn-MN','long') }} -c {{ date | timeString('mn-MN') }} хүртэл\",\n    sameDay: \"Өнөөдөр {{ date | timeString('mn-MN') }} цагт\",\n  },\n  dividerText: 'эсвэл',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Өөр арга ашиглах',\n  footerPageLink__help: 'Тусламж',\n  footerPageLink__privacy: 'Нууцлал',\n  footerPageLink__terms: 'Нөхцөл',\n  formButtonPrimary: 'Үргэлжлүүлэх',\n  formButtonPrimary__verify: 'Баталгаажуулах',\n  formFieldAction__forgotPassword: 'Нууц үгээ мартсан?',\n  formFieldError__matchingPasswords: 'Нууц үг таарч байна.',\n  formFieldError__notMatchingPasswords: 'Нууц үг таарахгүй байна.',\n  formFieldError__verificationLinkExpired: 'Баталгаажуулах холбоосын хугацаа дууссан. Шинэ холбоос хүсэлт гаргана уу.',\n  formFieldHintText__optional: 'Сонголтоор',\n  formFieldHintText__slug:\n    'Slug нь хүн унших боломжтой ID бөгөөд өвөрмөц байх ёстой. Энэ нь ихэвчлэн URL-д ашиглагддаг.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: 'Нөөц код',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Бүртгэл устгах',\n  formFieldInputPlaceholder__emailAddress: 'Имэйл хаяг',\n  formFieldInputPlaceholder__emailAddress_username: 'Имэйл хаяг эсвэл хэрэглэгчийн нэр',\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: 'Нэр',\n  formFieldInputPlaceholder__lastName: 'Овог',\n  formFieldInputPlaceholder__organizationDomain: 'Байгууллагын домэйн',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: 'Байгууллагын домэйн имэйл хаяг',\n  formFieldInputPlaceholder__organizationName: 'Байгууллагын нэр',\n  formFieldInputPlaceholder__organizationSlug: 'my-org',\n  formFieldInputPlaceholder__password: 'Нууц үг',\n  formFieldInputPlaceholder__phoneNumber: 'Утасны дугаар',\n  formFieldInputPlaceholder__username: 'Хэрэглэгчийн нэр',\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Энэ домэйны автомат урилгыг идэвхжүүлэх',\n  formFieldLabel__backupCode: 'Нөөц код',\n  formFieldLabel__confirmDeletion: 'Баталгаажуулалт',\n  formFieldLabel__confirmPassword: 'Нууц үгээ баталгаажуулна уу',\n  formFieldLabel__currentPassword: 'Одоогын нууц үг',\n  formFieldLabel__emailAddress: 'Имэйл хаяг',\n  formFieldLabel__emailAddress_username: 'Имэйл хаяг эсвэл хэрэглэгчийн нэр',\n  formFieldLabel__emailAddresses: 'Имэйл хаяг',\n  formFieldLabel__firstName: 'Нэр',\n  formFieldLabel__lastName: 'Овог',\n  formFieldLabel__newPassword: 'Шинэ нууц үг',\n  formFieldLabel__organizationDomain: 'Домэйн',\n  formFieldLabel__organizationDomainDeletePending: 'Хүлээгдэж буй урилга болон саналуудыг устгах',\n  formFieldLabel__organizationDomainEmailAddress: 'Баталгаажуулах имэйл хаяг',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Код хүлээн авч, энэ домэйныг баталгаажуулахын тулд энэ домайн доор имэйл хаягаа оруулна уу.',\n  formFieldLabel__organizationName: 'Байгууллагын нэр',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: undefined,\n  formFieldLabel__password: 'Нууц үг',\n  formFieldLabel__phoneNumber: 'Утасны дугаар',\n  formFieldLabel__role: 'Үүрэг',\n  formFieldLabel__signOutOfOtherSessions: 'Бусад бүх төхөөрөмжөөс гарах',\n  formFieldLabel__username: 'Хэрэглэгчийн нэр',\n  impersonationFab: {\n    action__signOut: 'Гарах',\n    title: '{{identifier}}-р нэвтэрсэн',\n  },\n  maintenanceMode: undefined,\n  membershipRole__admin: 'Админ',\n  membershipRole__basicMember: 'Гишүүн',\n  membershipRole__guestMember: 'Зочин',\n  organizationList: {\n    action__createOrganization: 'Байгууллага үүсгэх',\n    action__invitationAccept: 'Нэгдэх',\n    action__suggestionsAccept: 'Нэгдэх хүсэлт',\n    createOrganization: 'Байгууллага үүсгэх',\n    invitationAcceptedLabel: 'Нэгдсэн',\n    subtitle: '{{applicationName}} руу үргэлжлүүлэх',\n    suggestionsAcceptedLabel: 'Зөвшөөрөл хүлээгдэж байна',\n    title: 'Бүртгэл сонгоно уу',\n    titleWithoutPersonal: 'Байгууллага сонгоно уу',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Автомат урилга',\n    badge__automaticSuggestion: 'Автомат саналууд',\n    badge__manualInvitation: 'Автомат бүртгэл байхгүй',\n    badge__unverified: 'Баталгаажаагүй',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Баталгаажуулахын тулд домэйн нэмнэ үү. Энэ домэйны имэйл хаягтай хэрэглэгчид байгууллагад автоматаар нэгдэх эсвэл элсэх хүсэлт гаргах боломжтой.',\n      title: 'Домэйн нэмэх',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Урилгыг илгээх боломжгүй байна. Дараах и-мэйл хаягуудад аль хэдийн хүлээгдэж буй урилгууд байна: {{email_addresses}}.',\n      formButtonPrimary__continue: 'Урилга илгээх',\n      selectDropdown__role: 'Үүрэг сонгох',\n      subtitle: 'Нэг буюу хэд хэдэн имэйл хаягийг хоосон зай эсвэл таслалаар тусгаарлан оруулна уу.',\n      successMessage: 'Урилгыг амжилттай илгээсэн',\n      title: 'Шинэ гишүүдийг урих',\n    },\n    membersPage: {\n      action__invite: 'Урих',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Гишүүнийг хасах',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'Нэгдсэн',\n        tableHeader__role: 'Үүрэг',\n        tableHeader__user: 'Хэрэглэгч',\n      },\n      detailsTitle__emptyRow: 'Харуулах гишүүн алга',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Байгууллагатайгаа имэйлийн домайн холбож хэрэглэгчдийг урина уу. Тохирох цахим шуудангийн домайнаар бүртгүүлсэн хүн хүссэн үедээ байгууллагад элсэх боломжтой.',\n          headerTitle: 'Автомат урилга',\n          primaryButton: 'Баталгаажсан домайнуудыг удирдах',\n        },\n        table__emptyRow: 'Урилга байхгүй',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Урилгыг хүчингүй болгох',\n        tableHeader__invited: 'Урьсан',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Тохирох имэйл домэйнээр бүртгүүлсэн хэрэглэгчид танай байгууллагад элсэх хүсэлт гаргах саналыг харах боломжтой болно.',\n          headerTitle: 'Автомат саналууд',\n          primaryButton: 'Баталгаажсан домайнуудыг удирдах',\n        },\n        menuAction__approve: 'Зөвшөөрөх',\n        menuAction__reject: 'Татгалзах',\n        tableHeader__requested: 'Хүссэн хандалт',\n        table__emptyRow: 'Хүсэлт алга',\n      },\n      start: {\n        headerTitle__invitations: 'Урилга',\n        headerTitle__members: 'Гишүүд',\n        headerTitle__requests: 'Хүсэлтүүд',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Байгууллагаа удирдих',\n      general: 'Ерөнхий',\n      members: 'Гишүүд',\n      title: 'Байгууллага',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Үргэлжлүүлэхийн тулд доор \"{{organizationName}}\" гэж бичнэ үү.',\n          messageLine1: 'Та энэ байгууллагыг устгахдаа итгэлтэй байна уу?',\n          messageLine2: 'Энэ үйлдэл нь бүр мөсөн устгах бөгөөд сэргээх боломжгүй юм.',\n          successMessage: 'Та байгууллагыг устгасан байна.',\n          title: 'Байгууллага устгах',\n        },\n        leaveOrganization: {\n          actionDescription: 'Үргэлжлүүлэхийн тулд доор \"{{organizationName}}\" гэж бичнэ үү.',\n          messageLine1:\n            'Та энэ байгууллагаас гарахдаа итгэлтэй байна уу? Та энэ байгууллага болон түүний програмуудад хандах эрхээ алдах болно.',\n          messageLine2: 'Энэ үйлдэл нь бүр мөсөн устгах бөгөөд сэргээх боломжгүй юм.',\n          successMessage: 'Та байгууллагаас гарсан байна.',\n          title: 'Байгууллагаас гарах',\n        },\n        title: 'Аюултай',\n      },\n      domainSection: {\n        menuAction__manage: 'Удирдах',\n        menuAction__remove: 'Устгах',\n        menuAction__verify: 'Баталгаажуулах',\n        primaryButton: 'Домэйн нэмэх',\n        subtitle:\n          'Хэрэглэгчид байгууллагад автоматаар нэгдэх эсвэл баталгаажуулсан имэйл домэйн дээр үндэслэн элсэх хүсэлт гаргахыг зөвшөөрнө үү.',\n        title: 'Баталгаажсан домэйнууд',\n      },\n      successMessage: 'Байгууллага шинэчлэгдсэн.',\n      title: 'Профайлыг шинэчлэх',\n    },\n    removeDomainPage: {\n      messageLine1: '{{domain}} имэйл домэйныг устгах болно.',\n      messageLine2: 'Үүний дараа хэрэглэгчид байгууллагад автоматаар нэгдэх боломжгүй болно.',\n      successMessage: '{{domain}} устгагдсан.',\n      title: 'Домэйн устгах',\n    },\n    start: {\n      headerTitle__general: 'Ерөнхий',\n      headerTitle__members: 'Гишүүд',\n      profileSection: {\n        primaryButton: 'Профайлыг шинэчлэх',\n        title: 'Байгуулгын профайл',\n        uploadAction__title: 'Лого',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Энэ домэйныг устгаснаар уригдсан хэрэглэгчдэд нөлөөлнө.',\n        removeDomainActionLabel__remove: 'Домэйн устгах',\n        removeDomainSubtitle: 'Энэ домэйныг баталгаажуулсан домайнуудаас устгах',\n        removeDomainTitle: 'Домэйн устгах',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Хэрэглэгчид бүртгүүлэхдээ байгууллагад автоматаар нэгдэхийг урьж, хүссэн үедээ элсэх боломжтой.',\n        automaticInvitationOption__label: 'Автомат урилга',\n        automaticSuggestionOption__description:\n          'Хэрэглэгчид элсэх хүсэлт гаргах саналыг хүлээн авдаг боловч тухайн байгууллагад элсэхээс өмнө админаас зөвшөөрөл авсан байх ёстой.',\n        automaticSuggestionOption__label: 'Автомат санал',\n        calloutInfoLabel: 'Бүртгэлийн горимыг өөрчлөх нь зөвхөн шинэ хэрэглэгчдэд нөлөөлнө.',\n        calloutInvitationCountLabel: 'Хэрэглэгчдэд илгээсэн хүлээгдэж буй урилгууд: {{count}}',\n        calloutSuggestionCountLabel: 'Хэрэглэгчдэд илгээсэн хүлээгдэж буй саналууд: {{count}}',\n        manualInvitationOption__description: 'Хэрэглэгчийг зөвхөн гар аргаар байгууллагад урих боломжтой.',\n        manualInvitationOption__label: 'Автомат бүртгэл байхгүй',\n        subtitle: 'Энэ домэйны хэрэглэгчид байгууллагад хэрхэн элсэхийг сонгоно уу.',\n      },\n      start: {\n        headerTitle__danger: 'Аюултай',\n        headerTitle__enrollment: 'Элсэлтийн сонголтууд',\n      },\n      subtitle: '{{domain}} домэйн одоо баталгаажсан. Бүртгэлийн горимыг сонгон үргэлжлүүлнэ үү.',\n      title: '{{домайн}}-г шинэчлэх',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Таны имэйл хаяг руу илгээсэн баталгаажуулах кодыг оруулна уу',\n      formTitle: 'Баталгаажуулах код',\n      resendButton: 'Код хүлээж аваагүй юу? Дахин илгээх',\n      subtitle: '{{domainName}} домайныг имэйлээр баталгаажуулах шаардлагатай.',\n      subtitleVerificationCodeScreen:\n        'Баталгаажуулах кодыг {{emailAddress}} руу илгээсэн. Үргэлжлүүлэхийн тулд кодыг оруулна уу.',\n      title: 'Домэйн баталгаажуулах',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Байгууллага үүсгэх',\n    action__invitationAccept: 'Нэгдэх',\n    action__manageOrganization: 'Удирдах',\n    action__suggestionsAccept: 'Нэгдэх хүсэлт',\n    notSelected: 'Байгууллага сонгогдоогүй байна',\n    personalWorkspace: 'Хувийн бүртгэл',\n    suggestionsAcceptedLabel: 'Зөвшөөрөл хүлээгдэж байна',\n  },\n  paginationButton__next: 'Дараах',\n  paginationButton__previous: 'Өмнөх',\n  paginationRowText__displaying: 'Харуулж байна',\n  paginationRowText__of: 'аас',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Бүртгэл нэмэх',\n      action__signOutAll: 'Бүх бүртгэлээс гарна уу',\n      subtitle: 'Үргэлжлүүлэхийг хүссэн бүртгэлээ сонгоно уу.',\n      title: 'Бүртгэл сонгоно уу',\n    },\n    alternativeMethods: {\n      actionLink: 'Тусламж',\n      actionText: 'Эдгээрийн аль нь ч байхгүй юу?',\n      blockButton__backupCode: 'Нөөц код ашиглана уу',\n      blockButton__emailCode: '{{identifier}} имэйлруу код илгээх',\n      blockButton__emailLink: '{{identifier}} имэйлруу холбоос силгээх ',\n      blockButton__passkey: 'Passkey-р нэвтэрнэ үү',\n      blockButton__password: 'Нууц үгээрээ нэвтэрнэ үү',\n      blockButton__phoneCode: '{{identifier}} руу SMS илгээх',\n      blockButton__totp: 'Authenticator програмаа ашиглана уу',\n      getHelp: {\n        blockButton__emailSupport: 'Имэйлийн дэмжлэг',\n        content:\n          'Хэрэв та бүртгэлдээ нэвтрэхэд хүндрэлтэй байгаа бол бидэн рүү имэйл илгээгээрэй, бид тантай хамтран ажиллах болно.',\n        title: 'Тусламж',\n      },\n      subtitle: 'Асуудалтай тулгарч байна уу? Та нэвтрэхийн тулд эдгээр аргуудын аль нэгийг ашиглаж болно.',\n      title: 'Өөр аргыг ашигла',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'Таны нөөц код нь хоёр шатлалт баталгаажуулалтыг тохируулах үед авсан код юм.',\n      title: 'Нөөц код оруулна уу',\n    },\n    emailCode: {\n      formTitle: 'Баталгаажуулах код',\n      resendButton: 'Код хүлээж аваагүй юу? Дахин илгээх',\n      subtitle: '{{applicationName}} руу үргэлжлүүлэхийн тулд',\n      title: 'Имэйлээ шалгана уу',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: 'Үргэлжлүүлэхийн тулд анхны таб руу буцна уу.',\n        title: 'Энэ баталгаажуулах холбоосын хугацаа дууссан',\n      },\n      failed: {\n        subtitle: 'Үргэлжлүүлэхийн тулд анхны таб руу буцна уу.',\n        title: 'Энэ баталгаажуулах холбоос хүчингүй байна',\n      },\n      formSubtitle: 'Таны имэйл рүү илгээсэн баталгаажуулах холбоосыг ашиглана уу',\n      formTitle: 'Баталгаажуулах холбоос',\n      loading: {\n        subtitle: 'Таныг удахгүй дахин чиглүүлэх болно',\n        title: 'Нэвтэрч байна...',\n      },\n      resendButton: 'Холбоос хүлээж аваагүй юу? Дахин илгээх',\n      subtitle: '{{applicationName}} руу үргэлжлүүлэхийн тулд',\n      title: 'Имэйлээ шалгана уу',\n      unusedTab: {\n        title: 'Та энэ табыг хааж болно',\n      },\n      verified: {\n        subtitle: 'Таныг удахгүй дахин чиглүүлэх болно',\n        title: 'Амжилттай нэвтэрлээ',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Үргэлжлүүлэхийн тулд эх таб руу буцна уу',\n        subtitleNewTab: 'Үргэлжлүүлэхийн тулд шинээр нээгдсэн таб руу буцна уу',\n        titleNewTab: 'Өөр таб дээр нэвтэрсэн',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Нууц үг шинэчлэх код',\n      resendButton: 'Код хүлээж аваагүй юу? Дахин илгээх',\n      subtitle: 'нууц үгээ шинэчлэхийн тулд',\n      subtitle_email: 'Эхлээд таны имэйл ID руу илгээсэн кодыг оруулна уу',\n      subtitle_phone: 'Эхлээд утсандаа илгээсэн кодыг оруулна уу',\n      title: 'Нууц үг шинэчлэх',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Нууц үг шинэчлэх',\n      label__alternativeMethods: 'Эсвэл өөр аргаар нэвтэрнэ үү',\n      title: 'Нууц үгээ мартсан?',\n    },\n    noAvailableMethods: {\n      message: 'Нэвтрэхийг үргэлжлүүлэх боломжгүй. Баталгаажуулах хүчин зүйл алга.',\n      subtitle: 'Алдаа гарлаа',\n      title: 'Нэвтрэх боломжгүй',\n    },\n    passkey: {\n      subtitle:\n        'Passkey-ээ ашигласнаар таныг мөн болохыг баталгаажуулна. Таны төхөөрөмж хурууны хээ, нүүр эсвэл дэлгэцийн түгжээг асууж магадгүй.',\n      title: 'Passkey ашиглана уу',\n    },\n    password: {\n      actionLink: 'Өөр аргыг ашигла',\n      subtitle: 'Бүртгэлтэй холбоотой нууц үгээ оруулна уу',\n      title: 'Нууц үгээ оруулна уу',\n    },\n    passwordPwned: {\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: 'Баталгаажуулах код',\n      resendButton: 'Код хүлээж аваагүй юу? Дахин илгээх',\n      subtitle: '{{applicationName}} руу үргэлжлүүлэхийн тулд',\n      title: 'Утсаа шалгана уу',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Баталгаажуулах код',\n      resendButton: 'Код хүлээж аваагүй юу? Дахин илгээх',\n      subtitle: 'Үргэлжлүүлэхийн тулд утсандаа илгээсэн баталгаажуулах кодыг оруулна уу',\n      title: 'Утсаа шалгана уу',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Нууц үг сэргээх',\n      requiredMessage: 'Аюулгүй байдлын үүднээс нууц үгээ шинэчлэх шаардлагатай.',\n      successMessage: 'Таны нууц үг амжилттай өөрчлөгдсөн байна. Таныг нэвтрүүлж байна, түр хүлээнэ үү.',\n      title: 'Шинэ нууц үг тохируулна уу',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Нууц үгээ шинэчлэхээс өмнө бид таны хувийн мэдээллийг баталгаажуулах шаардлагатай.',\n    },\n    start: {\n      actionLink: 'Бүртгүүлэх',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Имэйл ашиглах',\n      actionLink__use_email_username: 'Имэйл эсвэл хэрэглэгчийн нэр ашиглах',\n      actionLink__use_passkey: 'Passkey ашиглах',\n      actionLink__use_phone: 'Утсаа ашиглах',\n      actionLink__use_username: 'Хэрэглэгчийн нэрийг ашиглах',\n      actionText: 'Бүртгэлгүй юу?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Тавтай морил! Үргэлжлүүлэхийн тулд нэвтэрнэ үү',\n      subtitleCombined: undefined,\n      title: '{{applicationName}} руу нэвтрэх',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Баталгаажуулах код',\n      subtitle: 'Үргэлжлүүлэхийн тулд authenticator апп-аар үүсгэсэн баталгаажуулах кодыг оруулна уу',\n      title: 'Two-step баталгаажуулалт',\n    },\n  },\n  signInEnterPasswordTitle: 'Нууц үгээ оруулна уу',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Нэвтрэх',\n      actionText: 'Бүртгэлтэй юу?',\n      subtitle: 'Үргэлжлүүлэхийн тулд үлдсэн дэлгэрэнгүй мэдээллийг бөглөнө үү.',\n      title: 'Дутуу талбаруудыг бөглөнө үү',\n    },\n    emailCode: {\n      formSubtitle: 'Таны имэйл хаяг руу илгээсэн баталгаажуулах кодыг оруулна уу',\n      formTitle: 'Баталгаажуулах код',\n      resendButton: 'Код хүлээж аваагүй юу? Дахин илгээх',\n      subtitle: 'Таны имэйл рүү илгээсэн баталгаажуулах кодыг оруулна уу',\n      title: 'Имэйлээ баталгаажуулна уу',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: 'Таны имэйл хаяг руу илгээсэн баталгаажуулах холбоосыг ашиглана уу',\n      formTitle: 'Баталгаажуулах холбоос',\n      loading: {\n        title: 'Бүртгүүлж байна...',\n      },\n      resendButton: 'Холбоос хүлээж аваагүй юу? Дахин илгээх',\n      subtitle: '{{applicationName}} руу үргэлжлүүлэхийн тулд',\n      title: 'Имэйлээ баталгаажуулна уу',\n      verified: {\n        title: 'Амжилттай бүртгүүллээ',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Үргэлжлүүлэхийн тулд шинээр нээгдсэн таб руу буцна уу',\n        subtitleNewTab: 'Үргэлжлүүлэхийн тулд өмнөх таб руу буцна уу',\n        title: 'Имэйлийг амжилттай баталгаажууллаа',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Таны утасны дугаар руу илгээсэн баталгаажуулах кодыг оруулна уу',\n      formTitle: 'Баталгаажуулах код',\n      resendButton: 'Код хүлээж аваагүй юу? Дахин илгээх',\n      subtitle: 'Таны утсанд илгээсэн баталгаажуулах кодыг оруулна уу',\n      title: 'Утсаар баталгаажуулах',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Нэвтрэх',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: 'Бүртгэлтэй юу?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Тавтай морил! Эхлэхийн тулд дэлгэрэнгүй мэдээллийг бөглөнө үү.',\n      subtitleCombined: 'Тавтай морил! Эхлэхийн тулд дэлгэрэнгүй мэдээллийг бөглөнө үү.',\n      title: 'Бүртгэл үүсгэх',\n      titleCombined: 'Бүртгэл үүсгэх',\n    },\n  },\n  socialButtonsBlockButton: '{{provider|titleize}}-р үргэлжлүүлэх',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'Аюулгүй байдлын баталгаажуулалт амжилтгүй болсны улмаас бүртгүүлж чадсангүй. Дахин оролдохын тулд хуудсыг сэргээнэ үү эсвэл нэмэлт тусламж авахын тулд тусламж авахаар холбогдоно уу.',\n    captcha_unavailable:\n      'Ботын баталгаажуулалт амжилтгүй болсны улмаас бүртгүүлж чадсангүй. Дахин оролдохын тулд хуудсыг сэргээнэ үү эсвэл нэмэлт тусламж авахын тулд тусламж авахаар холбогдоно уу.',\n    form_code_incorrect: 'Маягтын код буруу байна',\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: 'Тодорхойлогч олдсонгүй.',\n    form_param_format_invalid: 'Параметрийн формат буруу.',\n    form_param_format_invalid__email_address: 'Имэйл хаяг нь хүчинтэй имэйл хаяг байх ёстой.',\n    form_param_format_invalid__phone_number: 'Утасны дугаар нь олон улсын хүчинтэй форматтай байх ёстой',\n    form_param_max_length_exceeded__first_name: 'Нэр нь 256 тэмдэгтээс хэтрэхгүй байх ёстой.',\n    form_param_max_length_exceeded__last_name: 'Овог 256 тэмдэгтээс хэтрэхгүй байх ёстой.',\n    form_param_max_length_exceeded__name: 'Нэр 256 тэмдэгтээс хэтрэхгүй байх ёстой.',\n    form_param_nil: 'Параметр байхгүй байна.',\n    form_param_value_invalid: undefined,\n    form_password_incorrect: 'Нууц үг буруу байна.',\n    form_password_length_too_short: 'Нууц үгийн урт хэт богино байна.',\n    form_password_not_strong_enough: 'Таны нууц үг хангалттай хүчтэй биш байна.',\n    form_password_pwned:\n      'Энэ нууц үгийг зөрчлийн нэг хэсэг гэж олсон тул ашиглах боломжгүй, оронд нь өөр нууц үг оролдоно уу.',\n    form_password_pwned__sign_in: undefined,\n    form_password_size_in_bytes_exceeded:\n      'Энэ нууц үгийг зөрчлийн нэг хэсэг гэж олсон тул ашиглах боломжгүй. Өөр нууц үг оруулж үзнэ үү.',\n    form_password_validation_failed: 'Нууц үг буруу',\n    form_username_invalid_character: 'Хэрэглэгчийн нэр буруу тэмдэгт агуулж байна.',\n    form_username_invalid_length: 'Хэрэглэгчийн нэр буруу байна.',\n    identification_deletion_failed: 'Та өөрийн сүүлчийн таниулбараа устгах боломжгүй.',\n    not_allowed_access:\n      \"Имэйл хаяг эсвэл утасны дугаарыг бүртгүүлэхийг хориглоно. Энэ нь '+', '=', '#' эсвэл '.'-г ашигласантай холбоотой байж болно. түр зуурын цахим шуудангийн үйлчилгээтэй холбогдсон домэйн ашиглах, эсвэл шууд хаагдсан байх. Хэрэв та үүнийг алдаа гэж үзэж байгаа бол дэмжлэгтэй холбогдоно уу.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: '{{length}} тэмдэгтээс бага',\n      minimumLength: '{{length}} буюу түүнээс олон тэмдэгт',\n      requireLowercase: 'жижиг үсэг',\n      requireNumbers: 'тоо',\n      requireSpecialCharacter: 'тусгай тэмдэгт',\n      requireUppercase: 'том үсэг',\n      sentencePrefix: 'таны нууц үг агуулсан байх ёстой',\n    },\n    phone_number_exists: 'Энэ утасны дугаарыг авсан. Өөр оролдоно уу.',\n    session_exists: 'Та аль хэдийн нэвтэрсэн байна.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Таны нууц үг ажилладаг, гэхдээ илүү хүчтэй байж болно. Илүү олон тэмдэгт нэмж үзээрэй.',\n      goodPassword: 'Таны нууц үг шаардлагатай бүх шаардлагыг хангаж байна.',\n      notEnough: 'Таны нууц үг хангалттай хүчтэй биш байна.',\n      suggestions: {\n        allUppercase: 'Зарим үсгийг томоор бичнэ үү, гэхдээ бүх үсгийг биш.',\n        anotherWord: 'Түгээмэл үг бага нэмээрэй.',\n        associatedYears: 'Тантай холбоотой жилүүдээс зайлсхий.',\n        capitalization: 'Эхнийхээс бусад үсгийг томоор бичнэ үү.',\n        dates: 'Тантай холбоотой огноо, жилээс зайлсхий.',\n        l33t: \"'a'-д '@' гэх мэт урьдчилан таамаглах боломжтой үсгийг орлуулахаас зайлсхий.\",\n        longerKeyboardPattern: 'Илүү урт гарын хээ ашиглаж, бичих чиглэлээ олон удаа өөрчил.',\n        noNeed: 'Та тэмдэг, тоо, том үсэг ашиглахгүйгээр хүчтэй нууц үг үүсгэж болно.',\n        pwned: 'Хэрэв та энэ нууц үгийг өөр газар ашигласан бол өөрчлөх хэрэгтэй.',\n        recentYears: 'Сүүлийн жилүүдээс зайлсхий.',\n        repeated: 'Дахин давтагдах үг, тэмдэгтээс зайлсхий.',\n        reverseWords: 'Нийтлэг үгсийг урвуу бичихээс зайлсхий.',\n        sequences: 'Нийтлэг тэмдэгтүүдийн дарааллаас зайлсхий.',\n        useWords: 'Олон үг хэрэглээрэй, гэхдээ нийтлэг хэллэгээс зайлсхий.',\n      },\n      warnings: {\n        common: 'Энэ бол түгээмэл хэрэглэгддэг нууц үг юм.',\n        commonNames: 'Нийтлэг нэр, овгийг таахад хялбар байдаг.',\n        dates: 'Огноог таахад хялбар байдаг.',\n        extendedRepeat: '\"abcabcabc\" гэх мэт давтагдах дүрийн хэв маягийг таахад хялбар байдаг.',\n        keyPattern: 'Богино нууц үгийг таахад хялбар байдаг.',\n        namesByThemselves: 'Ганц нэр эсвэл овог нэрийг таахад хялбар байдаг.',\n        pwned: 'Таны нууц үг интернет дэх мэдээллийн зөрчлийн улмаас илэрсэн.',\n        recentYears: 'Сүүлийн жилүүдийг таахад амархан.',\n        sequences: '\"ABC\" шиг нийтлэг тэмдэгтүүд нь таахад хялбар байдаг.',\n        similarToCommon: 'Энэ нь түгээмэл хэрэглэгддэг нууц үгтэй төстэй юм.',\n        simpleRepeat: '\"aаа\" гэх мэт давтагдсан тэмдэгтүүдийг таахад хялбар байдаг.',\n        straightRow: 'Таны гар дээрх зэрэгцээ товчлууруудыг таахад хялбар байдаг.',\n        topHundred: 'Энэ бол байнга хэрэглэгддэг нууц үг юм.',\n        topTen: 'Энэ бол маш их ашиглагддаг нууц үг юм.',\n        userInputs: 'Хувийн болон хуудастай холбоотой мэдээлэл байх ёсгүй.',\n        wordByItself: 'Ганц үгийг таахад хялбар байдаг.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Бүртгэл нэмэх',\n    action__manageAccount: 'Бүртгэлийг удирдах',\n    action__signOut: 'Гарах',\n    action__signOutAll: 'Бүх бүртгэлээс гарна уу',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Хуулсан!',\n      actionLabel__copy: 'Бүгдийг хуулах',\n      actionLabel__download: 'Татах .txt',\n      actionLabel__print: 'Хэвлэх',\n      infoText1: 'Энэ бүртгэлд нөөц кодуудыг идэвхжүүлэх',\n      infoText2:\n        'Нөөц кодыг нууцалж, найдвартай хадгална уу. Хэрэв та нөөц кодыг эвдэрсэн гэж сэжиглэж байгаа бол тэдгээрийг дахин үүсгэж болно.',\n      subtitle__codelist: 'Тэдгээрийг найдвартай хадгалж, нууцаар хадгал.',\n      successMessage:\n        'Нөөц кодуудыг одоо идэвхжүүлсэн. Хэрэв та баталгаажуулах төхөөрөмждөө хандах эрхээ алдсан тохиолдолд эдгээрийн аль нэгийг ашиглан бүртгэлдээ нэвтэрч болно. Код бүрийг зөвхөн нэг удаа ашиглах боломжтой.',\n      successSubtitle:\n        'Хэрэв та баталгаажуулах төхөөрөмждөө хандах эрхээ алдсан тохиолдолд эдгээрийн аль нэгийг ашиглан бүртгэлдээ нэвтэрч болно.',\n      title: 'Нөөц кодын баталгаажуулалтыг нэмэх',\n      title__codelist: 'Нөөц кодууд',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Бүртгэлээ холбох үйлчилгээ үзүүлэгчээ сонгоно уу.',\n      formHint__noAccounts: 'Боломжтой гадны бүртгэлийн үйлчилгээ үзүүлэгч байхгүй байна.',\n      removeResource: {\n        messageLine1: '{{identifier}} энэ бүртгэлээс хасагдана.',\n        messageLine2:\n          'Та цаашид энэ холбогдсон акаунтыг ашиглах боломжгүй бөгөөд хамааралтай функцүүд ажиллахаа болино.',\n        successMessage: '{{connectedAccount}} таны бүртгэлээс хасагдсан.',\n        title: 'Холбогдсон бүртгэлийг устгана уу',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}',\n      successMessage: 'Үйлчилгээ үзүүлэгч таны дансанд нэмэгдсэн байна',\n      title: 'Холбогдсон бүртгэл нэмэх',\n    },\n    deletePage: {\n      actionDescription: 'Үргэлжлүүлэхийн тулд \"Delete account\" гэж бичнэ үү.',\n      confirm: 'Бүртгэл устгах',\n      messageLine1: 'Та бүртгэлээ устгахдаа итгэлтэй байна уу?',\n      messageLine2: 'Энэ үйлдэл нь байнгын бөгөөд эргэлт буцалтгүй юм.',\n      title: 'Бүртгэл устгах',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Баталгаажуулах код агуулсан имэйлийг энэ имэйл хаяг руу илгээх болно.',\n        formSubtitle: '{{identifier}} руу илгээсэн баталгаажуулах кодыг илгээх',\n        formTitle: 'Verification code',\n        resendButton: 'Код хүлээж аваагүй юу? Дахин илгээх',\n        successMessage: 'Таны бүртгэлд {{identifier}} имэйл нэмэгдлээ.',\n      },\n      emailLink: {\n        formHint: 'Баталгаажуулах холбоос бүхий имэйлийг энэ имэйл хаяг руу илгээх болно.',\n        formSubtitle: '{{identifier}}руу илгээсэн имэйл дэх баталгаажуулах холбоос дээр дарна уу.',\n        formTitle: 'Баталгаажуулах холбоос',\n        resendButton: 'Холбоос хүлээж аваагүй юу? Дахин илгээх',\n        successMessage: 'Таны бүртгэлд {{identifier}} имэйл нэмэгдлээ.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} энэ бүртгэлээс хасагдана.',\n        messageLine2: 'Та цаашид энэ имэйл хаягийг ашиглан нэвтрэх боломжгүй болно.',\n        successMessage: '{{emailAddress}} таны бүртгэлээс хасагдсан.',\n        title: 'Имэйл хаягийг устгана уу',\n      },\n      title: 'Имэйл хаяг нэмэх',\n      verifyTitle: 'Имэйл хаягийг баталгаажуулах',\n    },\n    formButtonPrimary__add: 'Нэмэх',\n    formButtonPrimary__continue: 'Үргэлжлүүлэх',\n    formButtonPrimary__finish: 'Дуусгах',\n    formButtonPrimary__remove: 'Устгах',\n    formButtonPrimary__save: 'Хадгалах',\n    formButtonReset: 'Цуцлах',\n    mfaPage: {\n      formHint: 'Нэмэх аргыг сонгоно уу.',\n      title: 'Two-step баталгаажуулалт нэмэх',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Одоо байгаа дугаарыг ашиглах',\n      primaryButton__addPhoneNumber: 'Утасны дугаар нэмэх',\n      removeResource: {\n        messageLine1: '{{identifier}} нэвтрэх үед баталгаажуулах код хүлээн авахгүй.',\n        messageLine2: 'Таны бүртгэл тийм ч аюулгүй биш байж магадгүй. Та үргэлжлүүлэхдээ итгэлтэй байна уу?',\n        successMessage: 'SMS код Two-step баталгаажуулалтыг {{mfaPhoneCode}}-д устгасан.',\n        title: 'Two-step баталгаажуулалтыг устгах',\n      },\n      subtitle__availablePhoneNumbers:\n        'SMS код хоёр шаттай баталгаажуулалтад бүртгүүлэх эсвэл шинээр нэмэхийн тулд одоо байгаа утасны дугаараа сонгоно уу.',\n      subtitle__unavailablePhoneNumbers:\n        'SMS код хоёр шаттай баталгаажуулалтад бүртгүүлэх утасны дугаар байхгүй тул шинээр нэмнэ үү.',\n      successMessage1:\n        'Нэвтрэхдээ нэмэлт алхам болгон энэ утасны дугаар руу илгээсэн баталгаажуулах кодыг оруулах шаардлагатай.',\n      successMessage2:\n        'Эдгээр нөөц кодыг хадгалж, аюулгүй газар хадгална уу. Хэрэв та баталгаажуулах төхөөрөмждөө хандах эрхээ алдвал нөөц код ашиглан нэвтрэх боломжтой.',\n      successTitle: 'SMS код баталгаажуулалтыг идэвхжүүлсэн',\n      title: 'SMS код баталгаажуулалтыг нэмэх',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'QR кодыг уншина уу',\n        buttonUnableToScan__nonPrimary: 'QR кодыг скан хийж чадахгүй байна уу?',\n        infoText__ableToScan:\n          'Authenticator програмдаа нэвтрэх шинэ аргыг тохируулаад дараах QR кодыг скан хийж өөрийн бүртгэлтэй холбоно уу.',\n        infoText__unableToScan: 'Authenticator програмдаа нэвтрэх шинэ аргыг тохируулаад доор өгсөн Key оруулна уу',\n        inputLabel__unableToScan1:\n          'Цаг дээр суурилсан эсвэл Нэг удаагийн нууц үг идэвхжсэн эсэхийг шалгаад бүртгэлээ холбож дуусгана уу.',\n        inputLabel__unableToScan2:\n          'Эсвэл, хэрэв таны баталгаажуулагч TOTP URI-г дэмждэг бол та бүрэн URI-г хуулж болно.',\n      },\n      removeResource: {\n        messageLine1: 'Нэвтрэх үед энэ баталгаажуулагчийн баталгаажуулах код шаардлагагүй болно.',\n        messageLine2: 'Таны бүртгэл тийм ч аюулгүй биш байж магадгүй. Та үргэлжлүүлэхдээ итгэлтэй байна уу?',\n        successMessage: 'Authenticator програмаар дамжуулан хоёр шаттай баталгаажуулалтыг устгасан.',\n        title: 'Two-step баталгаажуулалтыг устгах',\n      },\n      successMessage:\n        'Two-step баталгаажуулалтыг одоо идэвхжүүлсэн. Нэвтрэх үед та нэмэлт алхам болгон энэ баталгаажуулагчаас баталгаажуулах код оруулах шаардлагатай болно.',\n      title: 'Authenticator програм нэмэх',\n      verifySubtitle: 'Authenticator-н үүсгэсэн баталгаажуулах кодыг оруулах',\n      verifyTitle: 'Баталгаажуулах код',\n    },\n    mobileButton__menu: 'Цэс',\n    navbar: {\n      account: 'Профайл',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Бүртгэлийнхээ мэдээллийг удирдана уу.',\n      security: 'Аюулгүй байдал',\n      title: 'Бүртгэл',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: undefined,\n        title: undefined,\n      },\n      subtitle__rename: undefined,\n      title__rename: undefined,\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Таны хуучин нууц үгийг ашигласан бусад бүх төхөөрөмжөөс гарахыг зөвлөж байна.',\n      readonly: 'Та зөвхөн байгууллагын холболтоор нэвтрэх боломжтой тул таны нууц үгийг одоогоор засах боломжгүй.',\n      successMessage__set: 'Таны нууц үгийг тохирууллаа.',\n      successMessage__signOutOfOtherSessions: 'Бусад бүх төхөөрөмжөөс гарсан.',\n      successMessage__update: 'Таны нууц үг шинэчлэгдсэн.',\n      title__set: 'Нууц үг тохируулах',\n      title__update: 'Нууц үгийг шинэчлэх',\n    },\n    phoneNumberPage: {\n      infoText:\n        'Баталгаажуулах код агуулсан мессежийг энэ утасны дугаар руу илгээх болно. Мессеж болон дата төлбөр гарч болзошгүй.',\n      removeResource: {\n        messageLine1: '{{identifier}} энэ бүртгэлээс хасагдана.',\n        messageLine2: 'Та цаашид энэ утасны дугаарыг ашиглан нэвтрэх боломжгүй болно.',\n        successMessage: '{{phoneNumber}} таны бүртгэлээс хасагдсан.',\n        title: 'Утасны дугаарыг устгах',\n      },\n      successMessage: '{{identifier}} таны бүртгэлд нэмэгдсэн.',\n      title: 'Утасны дугаар нэмэх',\n      verifySubtitle: '{{identifier}} руу илгээсэн баталгаажуулах кодыг оруулна уу',\n      verifyTitle: 'Утасны дугаарыг баталгаажуулах',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Санал болгож буй хэмжээ 1:1, 10MB хүртэл.',\n      imageFormDestructiveActionSubtitle: 'Устгах',\n      imageFormSubtitle: 'Upload',\n      imageFormTitle: 'Профайлын зураг',\n      readonly: 'Таны профайлын мэдээллийг байгууллагын холболтоор өгсөн тул засварлах боломжгүй.',\n      successMessage: 'Таны профайл шинэчлэгдсэн.',\n      title: 'Профайлыг шинэчлэх',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Төхөөрөмжөөс гарах',\n        title: 'Идэвхтэй төхөөрөмжүүд',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Дахин оролд',\n        actionLabel__reauthorize: 'Зөвшөөрөх',\n        destructiveActionTitle: 'Устгах',\n        primaryButton: 'Бүртгэлийг холбоно уу',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'Шаардлагатай хамрах хүрээг шинэчилсэн бөгөөд танд хязгаарлагдмал ажиллагаатай байж магадгүй. Асуудлаас зайлсхийхийн тулд энэ аппликешныг дахин зөвшөөрнө үү',\n        title: 'Холбогдсон бүртгэлүүд',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Бүртгэл устгах',\n        title: 'Бүртгэл устгах',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Имэйлийг устгах',\n        detailsAction__nonPrimary: 'Үндсэн болгож тохируулах',\n        detailsAction__primary: 'Бүрэн баталгаажуулалт',\n        detailsAction__unverified: 'Баталгаажуулах',\n        primaryButton: 'Имэйл хаяг нэмэх',\n        title: 'Имэйл хаягууд',\n      },\n      enterpriseAccountsSection: {\n        title: 'Байгууллагын бүртгэлүүд',\n      },\n      headerTitle__account: 'Профайлын дэлгэрэнгүй',\n      headerTitle__security: 'Аюулгүй байдал',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Дахин үүсгэх',\n          headerTitle: 'Нөөц кодууд',\n          subtitle__regenerate:\n            'Аюулгүй нөөц кодуудын шинэ багц аваарай. Өмнөх нөөц кодыг устгах бөгөөд ашиглах боломжгүй.',\n          title__regenerate: 'Нөөц кодуудыг дахин үүсгэх',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Өгөгдмөл болгож тохируулах',\n          destructiveActionLabel: 'Устгах',\n        },\n        primaryButton: 'Two-step баталгаажуулалт нэмэх',\n        title: 'Two-step баталгаажуулалт',\n        totp: {\n          destructiveActionTitle: 'Устгах',\n          headerTitle: 'Authenticator програм',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: undefined,\n        menuAction__rename: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Нууц үг тохируулах',\n        primaryButton__updatePassword: 'Нууц үг шинэчлэх',\n        title: 'Нууц үг',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Утасны дугаар устгах',\n        detailsAction__nonPrimary: 'Үндсэн болгох',\n        detailsAction__primary: 'Бүрэн баталгаажуулалт',\n        detailsAction__unverified: 'Утасны дугаар баталгаажуулах',\n        primaryButton: 'Утасны дугаар нэмэх',\n        title: 'Утасны дугаарууд',\n      },\n      profileSection: {\n        primaryButton: 'Профайлыг шинэчлэх',\n        title: 'Профайл',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Хэрэглэгчийн нэрийг тохируулах',\n        primaryButton__updateUsername: 'Хэрэглэгчийн нэрийг шинэчлэх',\n        title: 'Хэрэглэгчийн нэр',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Wallet устгэх',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 wallets',\n        title: 'Web3 wallets',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Таны хэрэглэгчийн нэр шинэчлэгдсэн.',\n      title__set: 'Хэрэглэгчийн нэрийг тохируулах',\n      title__update: 'эрэглэгчийн нэрийг шинэчлэх',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} энэ бүртгэлээс хасагдана.',\n        messageLine2: 'Та цаашид энэ web3 түрийвчийг ашиглан нэвтрэх боломжгүй болно.',\n        successMessage: '{{web3Wallet}} таны бүртгэлээс хасагдсан.',\n        title: 'Web3 wallet устгах',\n      },\n      subtitle__availableWallets: 'Өөрийн бүртгэлтэй холбогдохын тулд web3 wallet-аа сонго.',\n      subtitle__unavailableWallets: 'Боломжтой web3 wallets алга.',\n      successMessage: 'Таны бүртгэлд web3 wallet нэмэгдлээ.',\n      title: 'Web3 wallet нэмэх',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBACE;AAAA,EACF,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBAAwB;AAAA,QACxB,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UACE;AAAA,MACF,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}