{"version": 3, "sources": ["../src/cs-CZ.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const csCZ: LocalizationResource = {\n  locale: 'cs-CZ',\n  apiKeys: {\n    action__add: 'Přidat nový klíč',\n    action__search: 'Vyhledat klíče',\n    createdAndExpirationStatus__expiresOn:\n      \"Vytvořeno {{ createdDate | shortDate('cs-CZ') }} • Platí do {{ expiresDate | longDate('cs-CZ') }}\",\n    createdAndExpirationStatus__never: \"Vytvořeno {{ createdDate | shortDate('cs-CZ') }} • Nikdy nevyprší\",\n    detailsTitle__emptyRow: 'Nenalezeny žádné API klíče',\n    formButtonPrimary__add: 'Vytvořit klíč',\n    formFieldCaption__expiration__expiresOn: 'Vyprší {{ date }}',\n    formFieldCaption__expiration__never: 'Tento klíč nikdy nevyprší',\n    formFieldOption__expiration__180d: '180 dní',\n    formFieldOption__expiration__1d: '1 den',\n    formFieldOption__expiration__1y: '1 rok',\n    formFieldOption__expiration__30d: '30 dní',\n    formFieldOption__expiration__60d: '60 dní',\n    formFieldOption__expiration__7d: '7 dní',\n    formFieldOption__expiration__90d: '90 dní',\n    formFieldOption__expiration__never: 'Nikdy',\n    formHint: 'Zadejte název pro vygenerování nového klíče. Budete ho moci kdykoli zrušit.',\n    formTitle: 'Přidat nový API klíč',\n    lastUsed__days: 'Před {{days}} dny',\n    lastUsed__hours: 'Před {{hours}} hodinami',\n    lastUsed__minutes: 'Před {{minutes}} minutami',\n    lastUsed__months: 'Před {{months}} měsíci',\n    lastUsed__seconds: 'Před {{seconds}} sekundami',\n    lastUsed__years: 'Před {{years}} lety',\n    menuAction__revoke: 'Zrušit klíč',\n    revokeConfirmation: {\n      confirmationText: 'Zrušit',\n      formButtonPrimary__revoke: 'Zrušit klíč',\n      formHint: 'Jste si jisti, že chcete smazat tento tajný klíč?',\n      formTitle: 'Zrušit tajný klíč \"{{apiKeyName}}\"?',\n    },\n  },\n  backButton: 'Zpět',\n  badge__activePlan: 'Aktivní',\n  badge__canceledEndsAt: \"Zrušeno • Končí {{ date | shortDate('cs-CZ') }}\",\n  badge__currentPlan: 'Aktuální plán',\n  badge__default: 'Výchozí',\n  badge__endsAt: \"Končí {{ date | shortDate('cs-CZ') }}\",\n  badge__expired: 'Vypršelo',\n  badge__otherImpersonatorDevice: 'Jiné zařízení pro simulaci',\n  badge__primary: 'Hlavní',\n  badge__renewsAt: \"Obnovuje se {{ date | shortDate('cs-CZ') }}\",\n  badge__requiresAction: 'Vyžaduje akci',\n  badge__startsAt: \"Začíná {{ date | shortDate('cs-CZ') }}\",\n  badge__pastDueAt: \"Po splatnosti {{ date | shortDate('cs-CZ') }}\",\n  badge__thisDevice: 'Toto zařízení',\n  badge__unverified: 'Nepotvrzené',\n  badge__upcomingPlan: 'Nadcházející',\n  badge__pastDuePlan: 'Po splatnosti',\n  badge__userDevice: 'Zařízení uživatele',\n  badge__you: 'Vy',\n  commerce: {\n    addPaymentMethod: 'Přidat platební metodu',\n    alwaysFree: 'Vždy zdarma',\n    annually: 'Ročně',\n    availableFeatures: 'Dostupné funkce',\n    billedAnnually: 'Fakturováno ročně',\n    billedMonthlyOnly: 'Fakturováno pouze měsíčně',\n    cancelSubscription: 'Zrušit předplatné',\n    cancelSubscriptionAccessUntil:\n      \"Funkce '{{plan}}' můžete používat do {{ date | longDate('cs-CZ') }}, poté k nim ztratíte přístup.\",\n    cancelSubscriptionNoCharge: 'Za toto předplatné vám nebudou účtovány žádné poplatky.',\n    cancelSubscriptionTitle: 'Zrušit předplatné {{plan}}?',\n    cannotSubscribeMonthly:\n      'Nelze se přihlásit k tomuto plánu s měsíční platbou. Abyste se k němu přihlásili, musíte zvolit roční platbu.',\n    checkout: {\n      description__paymentSuccessful: 'Vaše platba byla úspěšná.',\n      description__subscriptionSuccessful: 'Vaše nové předplatné je nastaveno.',\n      downgradeNotice:\n        'Současné předplatné a jeho funkce si ponecháte do konce fakturačního cyklu, poté budete převedeni na toto předplatné.',\n      emailForm: {\n        subtitle: 'Než budete moci dokončit nákup, musíte přidat e-mailovou adresu, na kterou budou zasílány účtenky.',\n        title: 'Přidat e-mailovou adresu',\n      },\n      lineItems: {\n        title__paymentMethod: 'Platební metoda',\n        title__statementId: 'ID výpisu',\n        title__subscriptionBegins: 'Předplatné začíná',\n        title__totalPaid: 'Celkem zaplaceno',\n      },\n      pastDueNotice: 'Vaše předchozí předplatné bylo po splatnosti, bez platby.',\n      perMonth: 'měsíčně',\n      title: 'Pokladna',\n      title__paymentSuccessful: 'Platba byla úspěšná!',\n      title__subscriptionSuccessful: 'Úspěch!',\n    },\n    credit: 'Kredit',\n    creditRemainder: 'Kredit za zbytek vašeho současného předplatného.',\n    defaultFreePlanActive: 'Aktuálně používáte bezplatný plán',\n    free: 'Zdarma',\n    getStarted: 'Začít',\n    keepSubscription: 'Ponechat předplatné',\n    manage: 'Spravovat',\n    manageSubscription: 'Spravovat předplatné',\n    month: 'Měsíc',\n    monthly: 'Měsíčně',\n    pastDue: 'Po splatnosti',\n    pay: 'Zaplatit {{amount}}',\n    paymentMethods: 'Platební metody',\n    paymentSource: {\n      applePayDescription: {\n        annual: 'Roční platba',\n        monthly: 'Měsíční platba',\n      },\n      dev: {\n        anyNumbers: 'Jakákoli čísla',\n        cardNumber: 'Číslo karty',\n        cvcZip: 'CVC, PSČ',\n        developmentMode: 'Vývojový režim',\n        expirationDate: 'Datum platnosti',\n        testCardInfo: 'Informace o testovací kartě',\n      },\n    },\n    popular: 'Populární',\n    pricingTable: {\n      billingCycle: 'Fakturační cyklus',\n      included: 'Zahrnuto',\n    },\n    subscriptionDetails: {\n      title: 'Předplatné',\n      currentBillingCycle: 'Aktuální fakturační cyklus',\n      nextPaymentOn: 'Další platba dne',\n      nextPaymentAmount: 'Výše další platby',\n      subscribedOn: 'Předplaceno dne',\n      endsOn: 'Končí dne',\n      renewsAt: 'Obnovuje se dne',\n      beginsOn: 'Začíná dne',\n      pastDueAt: 'Po splatnosti dne',\n    },\n    reSubscribe: 'Znovu se přihlásit',\n    seeAllFeatures: 'Zobrazit všechny funkce',\n    subscribe: 'Přihlásit se',\n    subtotal: 'Mezisoučet',\n    switchPlan: 'Přepnout na tento plán',\n    switchToAnnual: 'Přepnout na roční',\n    switchToMonthly: 'Přepnout na měsíční',\n    switchToMonthlyWithPrice: 'Přepnout na měsíční {{currency}}{{price}} / měsíc',\n    switchToAnnualWithAnnualPrice: 'Přepnout na roční {{currency}}{{price}} / rok',\n    totalDue: 'Celkem k zaplacení',\n    totalDueToday: 'Celkem k zaplacení dnes',\n    viewFeatures: 'Zobrazit funkce',\n    year: 'Rok',\n  },\n  createOrganization: {\n    formButtonSubmit: 'Vytvořit organizaci',\n    invitePage: {\n      formButtonReset: 'Přeskočit',\n    },\n    title: 'Vytvořit organizaci',\n  },\n  dates: {\n    lastDay: \"Včera v {{ date | timeString('cs-CZ') }}\",\n    next6Days: \"Příští {{ date | weekday('cs-CZ','long') }} v {{ date | timeString('cs-CZ') }}\",\n    nextDay: \"Zítra v {{ date | timeString('cs-CZ') }}\",\n    numeric: \"{{ date | numeric('cs-CZ') }}\",\n    previous6Days: \"Minulý {{ date | weekday('cs-CZ','long') }} v {{ date | timeString('cs-CZ') }}\",\n    sameDay: \"Dnes v {{ date | timeString('cs-CZ') }}\",\n  },\n  dividerText: 'nebo',\n  footerActionLink__alternativePhoneCodeProvider: 'Místo toho poslat kód přes SMS',\n  footerActionLink__useAnotherMethod: 'Použít jinou metodu',\n  footerPageLink__help: 'Nápověda',\n  footerPageLink__privacy: 'Ochrana soukromí',\n  footerPageLink__terms: 'Podmínky',\n  formButtonPrimary: 'Pokračovat',\n  formButtonPrimary__verify: 'Ověřit',\n  formFieldAction__forgotPassword: 'Zapomněli jste heslo?',\n  formFieldError__matchingPasswords: 'Hesla se shodují.',\n  formFieldError__notMatchingPasswords: 'Hesla se neshodují.',\n  formFieldError__verificationLinkExpired: 'Ověřovací odkaz vypršel. Prosím, požádejte o nový odkaz.',\n  formFieldHintText__optional: 'Volitelné',\n  formFieldHintText__slug:\n    'Slug je člověkem čitelný identifikátor, který musí být unikátní. Často se používá v URL adresách.',\n  formFieldInputPlaceholder__apiKeyDescription: 'Vysvětlete, proč generujete tento klíč',\n  formFieldInputPlaceholder__apiKeyExpirationDate: 'Vyberte datum',\n  formFieldInputPlaceholder__apiKeyName: 'Zadejte název tajného klíče',\n  formFieldInputPlaceholder__backupCode: 'Zadejte záložní kód',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Smazat účet',\n  formFieldInputPlaceholder__emailAddress: 'Zadejte svou e-mailovou adresu',\n  formFieldInputPlaceholder__emailAddress_username: 'Zadejte e-mail nebo uživatelské jméno',\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: 'Křestní jméno',\n  formFieldInputPlaceholder__lastName: 'Příjmení',\n  formFieldInputPlaceholder__organizationDomain: 'example.com',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: '<EMAIL>',\n  formFieldInputPlaceholder__organizationName: 'Název organizace',\n  formFieldInputPlaceholder__organizationSlug: 'moje-org',\n  formFieldInputPlaceholder__password: 'Zadejte své heslo',\n  formFieldInputPlaceholder__phoneNumber: 'Zadejte své telefonní číslo',\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: 'Popis',\n  formFieldLabel__apiKeyExpiration: 'Platnost',\n  formFieldLabel__apiKeyName: 'Název tajného klíče',\n  formFieldLabel__automaticInvitations: 'Povolit automatické pozvánky pro tuto doménu',\n  formFieldLabel__backupCode: 'Záložní kód',\n  formFieldLabel__confirmDeletion: 'Potvrzení',\n  formFieldLabel__confirmPassword: 'Potvrdit heslo',\n  formFieldLabel__currentPassword: 'Aktuální heslo',\n  formFieldLabel__emailAddress: 'E-mailová adresa',\n  formFieldLabel__emailAddress_username: 'E-mailová adresa nebo uživatelské jméno',\n  formFieldLabel__emailAddresses: 'E-mailové adresy',\n  formFieldLabel__firstName: 'Křestní jméno',\n  formFieldLabel__lastName: 'Příjmení',\n  formFieldLabel__newPassword: 'Nové heslo',\n  formFieldLabel__organizationDomain: 'Doména',\n  formFieldLabel__organizationDomainDeletePending: 'Smazat čekající pozvánky a návrhy',\n  formFieldLabel__organizationDomainEmailAddress: 'Ověřovací e-mailová adresa',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Zadejte e-mailovou adresu z této domény k obdržení kódu a ověření domény.',\n  formFieldLabel__organizationName: 'Název',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Název přístupového klíče',\n  formFieldLabel__password: 'Heslo',\n  formFieldLabel__phoneNumber: 'Telefonní číslo',\n  formFieldLabel__role: 'Role',\n  formFieldLabel__signOutOfOtherSessions: 'Odhlásit se ze všech ostatních zařízení',\n  formFieldLabel__username: 'Uživatelské jméno',\n  impersonationFab: {\n    action__signOut: 'Odhlásit se',\n    title: 'Přihlášen jako {{identifier}}',\n  },\n  maintenanceMode: 'Momentálně provádíme údržbu, ale nebojte se, nemělo by to trvat déle než pár minut.',\n  membershipRole__admin: 'Správce',\n  membershipRole__basicMember: 'Člen',\n  membershipRole__guestMember: 'Host',\n  organizationList: {\n    action__createOrganization: 'Vytvořit organizaci',\n    action__invitationAccept: 'Připojit se',\n    action__suggestionsAccept: 'Požádat o připojení',\n    createOrganization: 'Vytvořit organizaci',\n    invitationAcceptedLabel: 'Připojeno',\n    subtitle: 'pro pokračování do {{applicationName}}',\n    suggestionsAcceptedLabel: 'Čeká na schválení',\n    title: 'Vyberte účet',\n    titleWithoutPersonal: 'Vyberte organizaci',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: 'API klíče',\n    },\n    badge__automaticInvitation: 'Automatické pozvánky',\n    badge__automaticSuggestion: 'Automatické návrhy',\n    badge__manualInvitation: 'Žádné automatické přihlášení',\n    badge__unverified: 'Nepotvrzené',\n    billingPage: {\n      paymentHistorySection: {\n        empty: 'Žádná historie plateb',\n        notFound: 'Pokus o platbu nenalezen',\n        tableHeader__amount: 'Částka',\n        tableHeader__date: 'Datum',\n        tableHeader__status: 'Stav',\n      },\n      paymentSourcesSection: {\n        actionLabel__default: 'Nastavit jako výchozí',\n        actionLabel__remove: 'Odebrat',\n        add: 'Přidat novou platební metodu',\n        addSubtitle: 'Přidejte novou platební metodu k vašemu účtu.',\n        cancelButton: 'Zrušit',\n        formButtonPrimary__add: 'Přidat platební metodu',\n        formButtonPrimary__pay: 'Zaplatit {{amount}}',\n        payWithTestCardButton: 'Zaplatit testovací kartou',\n        removeResource: {\n          messageLine1: '{{identifier}} bude odstraněn z tohoto účtu.',\n          messageLine2:\n            'Tento platební zdroj již nebudete moci používat a veškerá opakující se předplatná, která na něm závisí, přestanou fungovat.',\n          successMessage: '{{paymentSource}} byl odstraněn z vašeho účtu.',\n          title: 'Odebrat platební metodu',\n        },\n        title: 'Platební metody',\n      },\n      start: {\n        headerTitle__payments: 'Platby',\n        headerTitle__plans: 'Plány',\n        headerTitle__statements: 'Výpisy',\n        headerTitle__subscriptions: 'Předplatné',\n      },\n      statementsSection: {\n        empty: 'Žádné výpisy k zobrazení',\n        itemCaption__paidForPlan: 'Zaplaceno za plán {{plan}} {{period}}',\n        itemCaption__proratedCredit: 'Poměrný kredit za částečné využití předchozího předplatného',\n        itemCaption__subscribedAndPaidForPlan: 'Předplaceno a zaplaceno za plán {{plan}} {{period}}',\n        notFound: 'Výpis nenalezen',\n        tableHeader__amount: 'Částka',\n        tableHeader__date: 'Datum',\n        title: 'Výpisy',\n        totalPaid: 'Celkem zaplaceno',\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: 'Přihlásit se k plánu',\n        actionLabel__switchPlan: 'Změnit plány',\n        tableHeader__edit: 'Upravit',\n        tableHeader__plan: 'Plán',\n        tableHeader__startDate: 'Datum zahájení',\n        title: 'Předplatné',\n      },\n      subscriptionsSection: {\n        actionLabel__default: 'Spravovat',\n      },\n      switchPlansSection: {\n        title: 'Změnit plány',\n      },\n      title: 'Fakturace',\n    },\n    createDomainPage: {\n      subtitle:\n        'Přidejte doménu k ověření. Uživatelé s e-mailovými adresami z této domény se mohou připojit k organizaci automaticky nebo požádat o připojení.',\n      title: 'Přidat doménu',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Pozvánky nebylo možné odeslat. Již existují čekající pozvánky pro následující e-mailové adresy: {{email_addresses}}.',\n      formButtonPrimary__continue: 'Odeslat pozvánky',\n      selectDropdown__role: 'Vyberte roli',\n      subtitle: 'Zadejte nebo vložte jednu nebo více e-mailových adres, oddělených mezerami nebo čárkami.',\n      successMessage: 'Pozvánky úspěšně odeslány',\n      title: 'Pozvat nové členy',\n    },\n    membersPage: {\n      action__invite: 'Pozvat',\n      action__search: 'Vyhledat',\n      activeMembersTab: {\n        menuAction__remove: 'Odebrat člena',\n        tableHeader__actions: 'Akce',\n        tableHeader__joined: 'Připojeno',\n        tableHeader__role: 'Role',\n        tableHeader__user: 'Uživatel',\n      },\n      detailsTitle__emptyRow: 'Žádní členové k zobrazení',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Pozvěte uživatele propojením e-mailové domény s vaší organizací. Kdokoli, kdo se zaregistruje s odpovídající e-mailovou doménou, se bude moci kdykoli připojit k organizaci.',\n          headerTitle: 'Automatické pozvánky',\n          primaryButton: 'Spravovat ověřené domény',\n        },\n        table__emptyRow: 'Žádné pozvánky k zobrazení',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Zrušit pozvánku',\n        tableHeader__invited: 'Pozváni',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Uživatelé, kteří se zaregistrují s odpovídající e-mailovou doménou, uvidí návrh na připojení k vaší organizaci.',\n          headerTitle: 'Automatické návrhy',\n          primaryButton: 'Spravovat ověřené domény',\n        },\n        menuAction__approve: 'Schválit',\n        menuAction__reject: 'Odmítnout',\n        tableHeader__requested: 'Požádán přístup',\n        table__emptyRow: 'Žádné požadavky k zobrazení',\n      },\n      start: {\n        headerTitle__invitations: 'Pozvánky',\n        headerTitle__members: 'Členové',\n        headerTitle__requests: 'Žádosti',\n      },\n    },\n    navbar: {\n      apiKeys: 'API klíče',\n      billing: 'Fakturace',\n      description: 'Spravujte svou organizaci.',\n      general: 'Obecné',\n      members: 'Členové',\n      title: 'Organizace',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: 'Nemáte oprávnění spravovat fakturaci pro tuto organizaci.',\n      },\n      title: 'Plány',\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Napište \"{{organizationName}}\" níže pro pokračování.',\n          messageLine1: 'Jste si jisti, že chcete smazat tuto organizaci?',\n          messageLine2: 'Tato akce je trvalá a nevratná.',\n          successMessage: 'Organizací jste smazali.',\n          title: 'Smazat organizaci',\n        },\n        leaveOrganization: {\n          actionDescription: 'Napište \"{{organizationName}}\" níže pro pokračování.',\n          messageLine1:\n            'Jste si jisti, že chcete opustit tuto organizaci? Ztratíte přístup k této organizaci a jejím aplikacím.',\n          messageLine2: 'Tato akce je trvalá a nevratná.',\n          successMessage: 'Opustili jste organizaci.',\n          title: 'Opustit organizaci',\n        },\n        title: 'Nebezpečí',\n      },\n      domainSection: {\n        menuAction__manage: 'Spravovat',\n        menuAction__remove: 'Smazat',\n        menuAction__verify: 'Ověřit',\n        primaryButton: 'Přidat doménu',\n        subtitle:\n          'Umožněte uživatelům připojit se k organizaci automaticky nebo požádat o připojení na základě ověřené e-mailové domény.',\n        title: 'Ověřené domény',\n      },\n      successMessage: 'Organizace byla aktualizována.',\n      title: 'Aktualizovat profil',\n    },\n    removeDomainPage: {\n      messageLine1: 'E-mailová doména {{domain}} bude odstraněna.',\n      messageLine2: 'Uživatelé se již nebudou moci automaticky připojit k organizaci po tomto.',\n      successMessage: '{{domain}} byl odstraněn.',\n      title: 'Odebrat doménu',\n    },\n    start: {\n      headerTitle__general: 'Obecné',\n      headerTitle__members: 'Členové',\n      profileSection: {\n        primaryButton: 'Aktualizovat profil',\n        title: 'Profil organizace',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Odstranění této domény ovlivní pozvané uživatele.',\n        removeDomainActionLabel__remove: 'Odebrat doménu',\n        removeDomainSubtitle: 'Odebrat tuto doménu z vašich ověřených domén',\n        removeDomainTitle: 'Odebrat doménu',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Uživatelé jsou automaticky zváni k připojení k organizaci při registraci a mohou se připojit kdykoli.',\n        automaticInvitationOption__label: 'Automatické pozvánky',\n        automaticSuggestionOption__description:\n          'Uživatelé obdrží návrh na připojení, ale musí být schváleni administrátorem, než se budou moci připojit k organizaci.',\n        automaticSuggestionOption__label: 'Automatické návrhy',\n        calloutInfoLabel: 'Změna režimu registrace ovlivní pouze nové uživatele.',\n        calloutInvitationCountLabel: 'Čekající pozvánky odeslané uživatelům: {{count}}',\n        calloutSuggestionCountLabel: 'Čekající návrhy odeslané uživatelům: {{count}}',\n        manualInvitationOption__description: 'Uživatelé mohou být do organizace pozváni pouze ručně.',\n        manualInvitationOption__label: 'Žádné automatické přihlášení',\n        subtitle: 'Vyberte, jak se uživatelé z této domény mohou připojit k organizaci.',\n      },\n      start: {\n        headerTitle__danger: 'Nebezpečí',\n        headerTitle__enrollment: 'Možnosti registrace',\n      },\n      subtitle: 'Doména {{domain}} je nyní ověřena. Pokračujte výběrem režimu registrace.',\n      title: 'Aktualizovat {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Zadejte ověřovací kód odeslaný na vaši e-mailovou adresu',\n      formTitle: 'Ověřovací kód',\n      resendButton: 'Neobdrželi jste kód? Znovu poslat',\n      subtitle: 'Doména {{domainName}} musí být ověřena e-mailem.',\n      subtitleVerificationCodeScreen: 'Ověřovací kód byl odeslán na {{emailAddress}}. Zadejte kód pro pokračování.',\n      title: 'Ověřit doménu',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Vytvořit organizaci',\n    action__invitationAccept: 'Připojit se',\n    action__manageOrganization: 'Spravovat organizaci',\n    action__suggestionsAccept: 'Požádat o připojení',\n    notSelected: 'Není vybrána žádná organizace',\n    personalWorkspace: 'Osobní účet',\n    suggestionsAcceptedLabel: 'Čeká na schválení',\n  },\n  paginationButton__next: 'Další',\n  paginationButton__previous: 'Předchozí',\n  paginationRowText__displaying: 'Zobrazuji',\n  paginationRowText__of: 'z',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Získat nápovědu',\n      actionText: 'Nemáte žádnou z těchto možností?',\n      blockButton__backupCode: 'Použít záložní kód',\n      blockButton__emailCode: 'Odeslat kód na e-mail {{identifier}}',\n      blockButton__passkey: 'Použít váš přístupový klíč',\n      blockButton__password: 'Pokračovat s vaším heslem',\n      blockButton__phoneCode: 'Odeslat SMS kód na {{identifier}}',\n      blockButton__totp: 'Použít vaši aplikaci pro ověřování',\n      getHelp: {\n        blockButton__emailSupport: 'Podpora přes e-mail',\n        content:\n          'Pokud máte potíže s ověřením vašeho účtu, napište nám e-mail a my s vámi budeme spolupracovat na co nejrychlejším obnovení přístupu.',\n        title: 'Získat nápovědu',\n      },\n      subtitle: 'Máte potíže? Můžete použít kteroukoli z těchto metod pro ověření.',\n      title: 'Použít jinou metodu',\n    },\n    backupCodeMfa: {\n      subtitle: 'Zadejte záložní kód, který jste obdrželi při nastavení dvoufázové autentizace',\n      title: 'Zadejte záložní kód',\n    },\n    emailCode: {\n      formTitle: 'Ověřovací kód',\n      resendButton: 'Neobdrželi jste kód? Znovu poslat',\n      subtitle: 'Zadejte kód odeslaný na váš e-mail pro pokračování',\n      title: 'Vyžadováno ověření',\n    },\n    noAvailableMethods: {\n      message: 'Nelze pokračovat s ověřením. Není nakonfigurován žádný vhodný autentizační faktor',\n      subtitle: 'Došlo k chybě',\n      title: 'Nelze ověřit váš účet',\n    },\n    passkey: {\n      blockButton__passkey: 'Použít váš přístupový klíč',\n      subtitle:\n        'Použití vašeho přístupového klíče potvrzuje vaši identitu. Vaše zařízení může požádat o otisk prstu, obličej nebo zámek obrazovky.',\n      title: 'Použít váš přístupový klíč',\n    },\n    password: {\n      actionLink: 'Použít jinou metodu',\n      subtitle: 'Zadejte své aktuální heslo pro pokračování',\n      title: 'Vyžadováno ověření',\n    },\n    phoneCode: {\n      formTitle: 'Ověřovací kód',\n      resendButton: 'Neobdrželi jste kód? Znovu poslat',\n      subtitle: 'Zadejte kód odeslaný na váš telefon pro pokračování',\n      title: 'Vyžadováno ověření',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Ověřovací kód',\n      resendButton: 'Neobdrželi jste kód? Znovu poslat',\n      subtitle: 'Zadejte kód odeslaný na váš telefon pro pokračování',\n      title: 'Vyžadováno ověření',\n    },\n    totpMfa: {\n      formTitle: 'Ověřovací kód',\n      subtitle: 'Zadejte kód vygenerovaný vaší aplikací pro ověřování pro pokračování',\n      title: 'Vyžadováno ověření',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Přidat účet',\n      action__signOutAll: 'Odhlásit se ze všech účtů',\n      subtitle: 'Vyberte účet, se kterým chcete pokračovat.',\n      title: 'Vyberte účet',\n    },\n    alternativeMethods: {\n      actionLink: 'Získat nápovědu',\n      actionText: 'Nemáte žádnou z těchto možností?',\n      blockButton__backupCode: 'Použít záložní kód',\n      blockButton__emailCode: 'Odeslat e-mailový kód na {{identifier}}',\n      blockButton__emailLink: 'Odeslat e-mailový odkaz na {{identifier}}',\n      blockButton__passkey: 'Přihlásit se pomocí vašeho přístupového klíče',\n      blockButton__password: 'Přihlásit se pomocí vašeho hesla',\n      blockButton__phoneCode: 'Odeslat SMS kód na {{identifier}}',\n      blockButton__totp: 'Použít vaši aplikaci pro ověřování',\n      getHelp: {\n        blockButton__emailSupport: 'Podpora přes e-mail',\n        content:\n          'Pokud máte potíže s přihlášením do vašeho účtu, napište nám e-mail a my s vámi budeme spolupracovat na co nejrychlejším obnovení přístupu.',\n        title: 'Získat nápovědu',\n      },\n      subtitle: 'Máte potíže? Můžete použít kteroukoli z těchto metod k přihlášení.',\n      title: 'Použít jinou metodu',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: 'Ověřovací kód',\n      resendButton: 'Neobdrželi jste kód? Znovu poslat',\n      subtitle: 'pro pokračování do {{applicationName}}',\n      title: 'Zkontrolujte svůj {{provider}}',\n    },\n    backupCodeMfa: {\n      subtitle: 'Váš záložní kód je ten, který jste získali při nastavení dvoufázové autentizace.',\n      title: 'Zadejte záložní kód',\n    },\n    emailCode: {\n      formTitle: 'Ověřovací kód',\n      resendButton: 'Neobdrželi jste kód? Znovu poslat',\n      subtitle: 'pro pokračování do {{applicationName}}',\n      title: 'Zkontrolujte svůj e-mail',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Pro pokračování otevřete ověřovací odkaz na zařízení a prohlížeči, ze kterého jste zahájili přihlášení',\n        title: 'Ověřovací odkaz je pro toto zařízení neplatný',\n      },\n      expired: {\n        subtitle: 'Vraťte se do původní karty pro pokračování.',\n        title: 'Tento ověřovací odkaz vypršel',\n      },\n      failed: {\n        subtitle: 'Vraťte se do původní karty pro pokračování.',\n        title: 'Tento ověřovací odkaz je neplatný',\n      },\n      formSubtitle: 'Použijte ověřovací odkaz odeslaný na váš e-mail',\n      formTitle: 'Ověřovací odkaz',\n      loading: {\n        subtitle: 'Brzy budete přesměrováni',\n        title: 'Přihlašování...',\n      },\n      resendButton: 'Neobdrželi jste odkaz? Znovu poslat',\n      subtitle: 'pro pokračování do {{applicationName}}',\n      title: 'Zkontrolujte svůj e-mail',\n      unusedTab: {\n        title: 'Můžete zavřít tuto kartu',\n      },\n      verified: {\n        subtitle: 'Brzy budete přesměrováni',\n        title: 'Úspěšně přihlášeno',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Vraťte se na původní kartu pro pokračování',\n        subtitleNewTab: 'Vraťte se na nově otevřenou kartu pro pokračování',\n        titleNewTab: 'Přihlášeno na jiné kartě',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Kód pro resetování hesla',\n      resendButton: 'Neobdrželi jste kód? Znovu poslat',\n      subtitle: 'pro resetování hesla',\n      subtitle_email: 'Nejprve zadejte kód odeslaný na váš e-mail',\n      subtitle_phone: 'Nejprve zadejte kód odeslaný na váš telefon',\n      title: 'Resetovat heslo',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Resetovat heslo',\n      label__alternativeMethods: 'Nebo se přihlaste jinou metodou',\n      title: 'Zapomněli jste heslo?',\n    },\n    noAvailableMethods: {\n      message: 'Nelze pokračovat v přihlášení. Není k dispozici žádný autentizační faktor.',\n      subtitle: 'Došlo k chybě',\n      title: 'Nelze se přihlásit',\n    },\n    passkey: {\n      subtitle:\n        'Použití vašeho přístupového klíče potvrzuje, že jste to vy. Vaše zařízení může požádat o otisk prstu, obličej nebo zámek obrazovky.',\n      title: 'Použít váš přístupový klíč',\n    },\n    password: {\n      actionLink: 'Použít jinou metodu',\n      subtitle: 'Zadejte heslo spojené s vaším účtem',\n      title: 'Zadejte své heslo',\n    },\n    passwordPwned: {\n      title: 'Heslo kompromitováno',\n    },\n    phoneCode: {\n      formTitle: 'Ověřovací kód',\n      resendButton: 'Neobdrželi jste kód? Znovu poslat',\n      subtitle: 'pro pokračování do {{applicationName}}',\n      title: 'Zkontrolujte svůj telefon',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Ověřovací kód',\n      resendButton: 'Neobdrželi jste kód? Znovu poslat',\n      subtitle: 'Pro pokračování zadejte ověřovací kód odeslaný na váš telefon',\n      title: 'Zkontrolujte svůj telefon',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Resetovat heslo',\n      requiredMessage: 'Z bezpečnostních důvodů je nutné resetovat vaše heslo.',\n      successMessage: 'Vaše heslo bylo úspěšně změněno. Přihlašuji vás, prosím počkejte okamžik.',\n      title: 'Nastavit nové heslo',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Před resetováním hesla musíme ověřit vaši identitu.',\n    },\n    start: {\n      actionLink: 'Registrovat se',\n      actionLink__join_waitlist: 'Připojit se k čekací listině',\n      actionLink__use_email: 'Použít e-mail',\n      actionLink__use_email_username: 'Použít e-mail nebo uživatelské jméno',\n      actionLink__use_passkey: 'Použít přístupový klíč místo toho',\n      actionLink__use_phone: 'Použít telefon',\n      actionLink__use_username: 'Použít uživatelské jméno',\n      actionText: 'Nemáte účet?',\n      actionText__join_waitlist: 'Chcete dřívější přístup?',\n      alternativePhoneCodeProvider: {\n        actionLink: 'Použít jinou metodu',\n        label: 'Telefonní číslo {{provider}}',\n        subtitle: 'Zadejte své telefonní číslo, abyste získali ověřovací kód na {{provider}}.',\n        title: 'Přihlásit se do {{applicationName}} pomocí {{provider}}',\n      },\n      subtitle: 'Vítejte zpět! Prosím přihlaste se pro pokračování',\n      subtitleCombined: undefined,\n      title: 'Přihlásit se do {{applicationName}}',\n      titleCombined: 'Pokračovat do {{applicationName}}',\n    },\n    totpMfa: {\n      formTitle: 'Ověřovací kód',\n      subtitle: 'Pro pokračování zadejte ověřovací kód vygenerovaný vaší aplikací pro ověřování',\n      title: 'Dvoufázové ověření',\n    },\n  },\n  signInEnterPasswordTitle: 'Zadejte své heslo',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: 'Neobdrželi jste kód? Znovu poslat',\n      subtitle: 'Zadejte ověřovací kód odeslaný na váš {{provider}}',\n      title: 'Ověřte svůj {{provider}}',\n    },\n    continue: {\n      actionLink: 'Přihlásit se',\n      actionText: 'Už máte účet?',\n      subtitle: 'Prosím vyplňte zbývající údaje pro pokračování.',\n      title: 'Vyplňte chybějící pole',\n    },\n    emailCode: {\n      formSubtitle: 'Zadejte ověřovací kód odeslaný na vaši e-mailovou adresu',\n      formTitle: 'Ověřovací kód',\n      resendButton: 'Neobdrželi jste kód? Znovu poslat',\n      subtitle: 'Zadejte ověřovací kód odeslaný na váš e-mail',\n      title: 'Ověřte svůj e-mail',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Pro pokračování otevřete ověřovací odkaz na zařízení a prohlížeči, ze kterého jste zahájili registraci',\n        title: 'Ověřovací odkaz je pro toto zařízení neplatný',\n      },\n      formSubtitle: 'Použijte ověřovací odkaz odeslaný na vaši e-mailovou adresu',\n      formTitle: 'Ověřovací odkaz',\n      loading: {\n        title: 'Registruji se...',\n      },\n      resendButton: 'Neobdrželi jste odkaz? Znovu poslat',\n      subtitle: 'pro pokračování do {{applicationName}}',\n      title: 'Ověřte svůj e-mail',\n      verified: {\n        title: 'Úspěšně zaregistrováno',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Vraťte se na nově otevřenou kartu pro pokračování',\n        subtitleNewTab: 'Vraťte se na předchozí kartu pro pokračování',\n        title: 'E-mail úspěšně ověřen',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Souhlasím s {{ privacyPolicyLink || link(\"Zásadami ochrany osobních údajů\") }}',\n        label__onlyTermsOfService: 'Souhlasím s {{ termsOfServiceLink || link(\"Podmínkami služby\") }}',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Souhlasím s {{ termsOfServiceLink || link(\"Podmínkami služby\") }} a {{ privacyPolicyLink || link(\"Zásadami ochrany osobních údajů\") }}',\n      },\n      continue: {\n        subtitle: 'Prosím přečtěte si a přijměte podmínky pro pokračování',\n        title: 'Právní souhlas',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Zadejte ověřovací kód odeslaný na vaše telefonní číslo',\n      formTitle: 'Ověřovací kód',\n      resendButton: 'Neobdrželi jste kód? Znovu poslat',\n      subtitle: 'Zadejte ověřovací kód odeslaný na váš telefon',\n      title: 'Ověřte svůj telefon',\n    },\n    restrictedAccess: {\n      actionLink: 'Přihlásit se',\n      actionText: 'Už máte účet?',\n      blockButton__emailSupport: 'Podpora přes e-mail',\n      blockButton__joinWaitlist: 'Připojit se k čekací listině',\n      subtitle:\n        'Registrace jsou momentálně zakázány. Pokud se domníváte, že byste měli mít přístup, kontaktujte prosím podporu.',\n      subtitleWaitlist:\n        'Registrace jsou momentálně zakázány. Chcete-li být první, kdo se dozví, kdy spustíme, připojte se k čekací listině.',\n      title: 'Omezený přístup',\n    },\n    start: {\n      actionLink: 'Přihlásit se',\n      actionLink__use_email: 'Použít e-mail místo toho',\n      actionLink__use_phone: 'Použít telefon místo toho',\n      actionText: 'Už máte účet?',\n      alternativePhoneCodeProvider: {\n        actionLink: 'Použít jinou metodu',\n        label: 'Telefonní číslo {{provider}}',\n        subtitle: 'Zadejte své telefonní číslo, abyste získali ověřovací kód na {{provider}}.',\n        title: 'Zaregistrovat se do {{applicationName}} pomocí {{provider}}',\n      },\n      subtitle: 'Vítejte! Prosím vyplňte údaje pro začátek.',\n      subtitleCombined: 'Vítejte! Prosím vyplňte údaje pro začátek.',\n      title: 'Vytvořte si účet',\n      titleCombined: 'Vytvořte si účet',\n    },\n  },\n  socialButtonsBlockButton: 'Pokračovat s {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: '{{email}} je již členem organizace.',\n    captcha_invalid: undefined,\n    captcha_unavailable:\n      'Registrace nebyla úspěšná kvůli neúspěšné validaci bota. Prosím obnovte stránku a zkuste to znovu, nebo se obraťte na podporu pro další pomoc.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: undefined,\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: undefined,\n    form_param_format_invalid__phone_number: undefined,\n    form_param_max_length_exceeded__first_name: undefined,\n    form_param_max_length_exceeded__last_name: undefined,\n    form_param_max_length_exceeded__name: undefined,\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: 'Vaše heslo je příliš krátké. Musí mít alespoň 8 znaků.',\n    form_password_not_strong_enough: 'Vaše heslo není dostatečně silné.',\n    form_password_pwned: 'Toto heslo bylo nalezeno jako součást prolomení a nelze ho použít, zkuste prosím jiné heslo.',\n    form_password_pwned__sign_in:\n      'Toto heslo bylo nalezeno jako součást prolomení a nelze ho použít, prosím resetujte si heslo.',\n    form_password_size_in_bytes_exceeded: undefined,\n    form_password_validation_failed: undefined,\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: 'Vaše uživatelské jméno musí mít mezi {{min_length}} a {{max_length}} znaky.',\n    identification_deletion_failed: undefined,\n    not_allowed_access: undefined,\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: 'Přístupový klíč je již registrován na tomto zařízení.',\n    passkey_not_supported: 'Přístupové klíče nejsou podporovány na tomto zařízení.',\n    passkey_pa_not_supported: 'Registrace vyžaduje autentizační metodu platformy, ale zařízení ji nepodporuje.',\n    passkey_registration_cancelled: 'Registrace přístupového klíče byla zrušena nebo vypršel časový limit.',\n    passkey_retrieval_cancelled: 'Ověření přístupového klíče bylo zrušeno nebo vypršel časový limit.',\n    passwordComplexity: {\n      maximumLength: 'méně než {{length}} znaků',\n      minimumLength: '{{length}} nebo více znaků',\n      requireLowercase: 'malé písmeno',\n      requireNumbers: 'číslici',\n      requireSpecialCharacter: 'speciální znak',\n      requireUppercase: 'velké písmeno',\n      sentencePrefix: 'Vaše heslo musí obsahovat',\n    },\n    phone_number_exists: undefined,\n    session_exists: undefined,\n    web3_missing_identifier: 'Rozšíření peněženky Web3 nebylo nalezeno. Pro pokračování prosím nainstalujte jednu.',\n    zxcvbn: {\n      couldBeStronger: 'Vaše heslo funguje, ale mohlo by být silnější. Zkuste přidat více znaků.',\n      goodPassword: 'Vaše heslo splňuje všechny potřebné požadavky.',\n      notEnough: 'Vaše heslo není dostatečně silné.',\n      suggestions: {\n        allUppercase: 'Použijte velká písmena u některých, ale ne všech písmen.',\n        anotherWord: 'Přidejte více slov, která jsou méně běžná.',\n        associatedYears: 'Vyhněte se rokům, které jsou s vámi spojeny.',\n        capitalization: 'Použijte velká písmena více než jen u prvního písmene.',\n        dates: 'Vyhněte se datům a rokům, které jsou s vámi spojeny.',\n        l33t: \"Vyhněte se předvídatelným náhradám písmen jako '@' za 'a'.\",\n        longerKeyboardPattern: 'Použijte delší klávesnicové vzory a několikrát změňte směr psaní.',\n        noNeed: 'Můžete vytvářet silná hesla bez použití symbolů, čísel nebo velkých písmen.',\n        pwned: 'Pokud používáte toto heslo jinde, měli byste ho změnit.',\n        recentYears: 'Vyhněte se nedávným rokům.',\n        repeated: 'Vyhněte se opakujícím se slovům a znakům.',\n        reverseWords: 'Vyhněte se obráceným pravopisům běžných slov.',\n        sequences: 'Vyhněte se běžným sekvencím znaků.',\n        useWords: 'Použijte více slov, ale vyhněte se běžným frázím.',\n      },\n      warnings: {\n        common: 'Toto je běžně používané heslo.',\n        commonNames: 'Běžná jména a příjmení jsou snadno uhádnutelná.',\n        dates: 'Data jsou snadno uhádnutelná.',\n        extendedRepeat: 'Opakované vzory znaků jako \"abcabcabc\" jsou snadno uhádnutelné.',\n        keyPattern: 'Krátké klávesnicové vzory jsou snadno uhádnutelné.',\n        namesByThemselves: 'Samotná jména nebo příjmení jsou snadno uhádnutelná.',\n        pwned: 'Vaše heslo bylo odhaleno datovým únikem na internetu.',\n        recentYears: 'Nedávné roky jsou snadno uhádnutelné.',\n        sequences: 'Běžné sekvence znaků jako \"abc\" jsou snadno uhádnutelné.',\n        similarToCommon: 'Toto je podobné běžně používanému heslu.',\n        simpleRepeat: 'Opakované znaky jako \"aaa\" jsou snadno uhádnutelné.',\n        straightRow: 'Přímé řady kláves na klávesnici jsou snadno uhádnutelné.',\n        topHundred: 'Toto je často používané heslo.',\n        topTen: 'Toto je velmi často používané heslo.',\n        userInputs: 'Neměly by zde být žádné osobní nebo se stránkou související údaje.',\n        wordByItself: 'Jednotlivá slova jsou snadno uhádnutelná.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Přidat účet',\n    action__manageAccount: 'Spravovat účet',\n    action__signOut: 'Odhlásit se',\n    action__signOutAll: 'Odhlásit se ze všech účtů',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: 'API klíče',\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Zkopírováno!',\n      actionLabel__copy: 'Zkopírovat vše',\n      actionLabel__download: 'Stáhnout .txt',\n      actionLabel__print: 'Vytisknout',\n      infoText1: 'Záložní kódy budou pro tento účet povoleny.',\n      infoText2:\n        'Uchovávejte záložní kódy v tajnosti a bezpečně. Můžete vygenerovat nové záložní kódy, pokud máte podezření, že byly kompromitovány.',\n      subtitle__codelist: 'Uchovávejte je bezpečně a tajně.',\n      successMessage:\n        'Záložní kódy jsou nyní povoleny. Můžete použít jeden z těchto kódů k přihlášení do svého účtu, pokud ztratíte přístup k vašemu ověřovacímu zařízení. Každý kód lze použít pouze jednou.',\n      successSubtitle:\n        'Můžete použít jeden z těchto kódů k přihlášení do svého účtu, pokud ztratíte přístup k vašemu ověřovacímu zařízení.',\n      title: 'Přidat ověřování pomocí záložních kódů',\n      title__codelist: 'Záložní kódy',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: 'Žádná historie plateb',\n        notFound: 'Pokus o platbu nenalezen',\n        tableHeader__amount: 'Částka',\n        tableHeader__date: 'Datum',\n        tableHeader__status: 'Stav',\n      },\n      paymentSourcesSection: {\n        actionLabel__default: 'Nastavit jako výchozí',\n        actionLabel__remove: 'Odebrat',\n        add: 'Přidat novou platební metodu',\n        addSubtitle: 'Přidejte novou platební metodu k vašemu účtu.',\n        cancelButton: 'Zrušit',\n        formButtonPrimary__add: 'Přidat platební metodu',\n        formButtonPrimary__pay: 'Zaplatit {{amount}}',\n        payWithTestCardButton: 'Zaplatit testovací kartou',\n        removeResource: {\n          messageLine1: '{{identifier}} bude odstraněn z tohoto účtu.',\n          messageLine2:\n            'Tento platební zdroj již nebudete moci používat a veškerá opakující se předplatná, která na něm závisí, přestanou fungovat.',\n          successMessage: '{{paymentSource}} byl odstraněn z vašeho účtu.',\n          title: 'Odebrat platební metodu',\n        },\n        title: 'Platební metody',\n      },\n      start: {\n        headerTitle__payments: 'Platby',\n        headerTitle__plans: 'Plány',\n        headerTitle__statements: 'Výpisy',\n        headerTitle__subscriptions: 'Předplatné',\n      },\n      statementsSection: {\n        empty: 'Žádné výpisy k zobrazení',\n        itemCaption__paidForPlan: 'Zaplaceno za plán {{plan}} {{period}}',\n        itemCaption__proratedCredit: 'Poměrný kredit za částečné využití předchozího předplatného',\n        itemCaption__subscribedAndPaidForPlan: 'Předplaceno a zaplaceno za plán {{plan}} {{period}}',\n        notFound: 'Výpis nenalezen',\n        tableHeader__amount: 'Částka',\n        tableHeader__date: 'Datum',\n        title: 'Výpisy',\n        totalPaid: 'Celkem zaplaceno',\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: 'Přihlásit se k plánu',\n        actionLabel__switchPlan: 'Změnit plány',\n        tableHeader__edit: 'Upravit',\n        tableHeader__plan: 'Plán',\n        tableHeader__startDate: 'Datum zahájení',\n        title: 'Předplatné',\n      },\n      subscriptionsSection: {\n        actionLabel__default: 'Spravovat',\n      },\n      switchPlansSection: {\n        title: 'Změnit plány',\n      },\n      title: 'Fakturace',\n    },\n    connectedAccountPage: {\n      formHint: 'Vyberte poskytovatele pro připojení vašeho účtu.',\n      formHint__noAccounts: 'Nejsou k dispozici žádní externí poskytovatelé účtů.',\n      removeResource: {\n        messageLine1: '{{identifier}} bude odstraněn z tohoto účtu.',\n        messageLine2: 'Již nebudete moci používat tento připojený účet a veškeré závislé funkce přestanou fungovat.',\n        successMessage: '{{connectedAccount}} byl odstraněn z vašeho účtu.',\n        title: 'Odebrat připojený účet',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}',\n      successMessage: 'Poskytovatel byl přidán k vašemu účtu',\n      title: 'Přidat připojený účet',\n    },\n    deletePage: {\n      actionDescription: 'Napište \"Smazat účet\" níže pro pokračování.',\n      confirm: 'Smazat účet',\n      messageLine1: 'Jste si jisti, že chcete smazat svůj účet?',\n      messageLine2: 'Tato akce je trvalá a nevratná.',\n      title: 'Smazat účet',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Na tuto e-mailovou adresu bude odeslán ověřovací kód.',\n        formSubtitle: 'Zadejte ověřovací kód odeslaný na {{identifier}}',\n        formTitle: 'Ověřovací kód',\n        resendButton: 'Neobdrželi jste kód? Znovu poslat',\n        successMessage: 'E-mail {{identifier}} byl přidán k vašemu účtu.',\n      },\n      emailLink: {\n        formHint: 'Na tuto e-mailovou adresu bude odeslán ověřovací odkaz.',\n        formSubtitle: 'Klikněte na ověřovací odkaz v e-mailu odeslaném na {{identifier}}',\n        formTitle: 'Ověřovací odkaz',\n        resendButton: 'Neobdrželi jste odkaz? Znovu poslat',\n        successMessage: 'E-mail {{identifier}} byl přidán k vašemu účtu.',\n      },\n      enterpriseSSOLink: {\n        formButton: 'Klikněte pro přihlášení',\n        formSubtitle: 'Dokončete přihlášení pomocí {{identifier}}',\n      },\n      formHint: 'Tuto e-mailovou adresu budete muset ověřit, než ji bude možné přidat k vašemu účtu.',\n      removeResource: {\n        messageLine1: '{{identifier}} bude odstraněn z tohoto účtu.',\n        messageLine2: 'Již nebudete moci přihlásit se pomocí této e-mailové adresy.',\n        successMessage: '{{emailAddress}} byl odstraněn z vašeho účtu.',\n        title: 'Odebrat e-mailovou adresu',\n      },\n      title: 'Přidat e-mailovou adresu',\n      verifyTitle: 'Ověřit e-mailovou adresu',\n    },\n    formButtonPrimary__add: 'Přidat',\n    formButtonPrimary__continue: 'Pokračovat',\n    formButtonPrimary__finish: 'Dokončit',\n    formButtonPrimary__remove: 'Odebrat',\n    formButtonPrimary__save: 'Uložit',\n    formButtonReset: 'Zrušit',\n    mfaPage: {\n      formHint: 'Vyberte metodu k přidání.',\n      title: 'Přidat dvoufázové ověření',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Použít stávající číslo',\n      primaryButton__addPhoneNumber: 'Přidat telefonní číslo',\n      removeResource: {\n        messageLine1: '{{identifier}} již nebude dostávat ověřovací kódy při přihlašování.',\n        messageLine2: 'Váš účet nemusí být tak bezpečný. Jste si jisti, že chcete pokračovat?',\n        successMessage: 'Dvoufázové ověření SMS kódem bylo odstraněno pro {{mfaPhoneCode}}',\n        title: 'Odebrat dvoufázové ověření',\n      },\n      subtitle__availablePhoneNumbers:\n        'Vyberte stávající telefonní číslo pro registraci dvoufázového ověření SMS kódem nebo přidejte nové.',\n      subtitle__unavailablePhoneNumbers:\n        'Nejsou k dispozici žádná telefonní čísla pro registraci dvoufázového ověření SMS kódem, prosím přidejte nové.',\n      successMessage1:\n        'Při přihlášení budete muset jako další krok zadat ověřovací kód odeslaný na toto telefonní číslo.',\n      successMessage2:\n        'Uložte si tyto záložní kódy a uložte je na bezpečné místo. Pokud ztratíte přístup ke svému ověřovacímu zařízení, můžete k přihlášení použít záložní kódy.',\n      successTitle: 'SMS kódové ověření povoleno',\n      title: 'Přidat ověření SMS kódem',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Místo toho naskenovat QR kód',\n        buttonUnableToScan__nonPrimary: 'Nemůžete naskenovat QR kód?',\n        infoText__ableToScan:\n          'Nastavte novou metodu přihlášení ve vaší aplikaci pro ověřování a naskenujte následující QR kód, abyste ji propojili se svým účtem.',\n        infoText__unableToScan:\n          'Nastavte novou metodu přihlášení ve své aplikaci pro ověřování a zadejte níže uvedený klíč.',\n        inputLabel__unableToScan1:\n          'Ujistěte se, že je povoleno časové nebo jednorázové heslo, a dokončete propojení svého účtu.',\n        inputLabel__unableToScan2:\n          'Alternativně, pokud vaše aplikace pro ověřování podporuje TOTP URI, můžete také zkopírovat celý URI.',\n      },\n      removeResource: {\n        messageLine1: 'Ověřovací kódy z této aplikace pro ověřování již nebudou vyžadovány při přihlašování.',\n        messageLine2: 'Váš účet nemusí být tak bezpečný. Jste si jisti, že chcete pokračovat?',\n        successMessage: 'Dvoufázové ověření pomocí aplikace pro ověřování bylo odstraněno.',\n        title: 'Odebrat dvoufázové ověření',\n      },\n      successMessage:\n        'Dvoufázové ověření je nyní povoleno. Při přihlášení budete muset zadat ověřovací kód z této aplikace pro ověřování jako další krok.',\n      title: 'Přidat aplikaci pro ověřování',\n      verifySubtitle: 'Zadejte ověřovací kód vygenerovaný vaší aplikací pro ověřování',\n      verifyTitle: 'Ověřovací kód',\n    },\n    mobileButton__menu: 'Menu',\n    navbar: {\n      account: 'Profil',\n      apiKeys: 'API klíče',\n      billing: 'Fakturace',\n      description: 'Spravujte informace o svém účtu.',\n      security: 'Zabezpečení',\n      title: 'Účet',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} bude odstraněn z tohoto účtu.',\n        title: 'Odebrat přístupový klíč',\n      },\n      subtitle__rename: 'Můžete změnit název přístupového klíče, aby se snáze našel.',\n      title__rename: 'Přejmenovat přístupový klíč',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Doporučuje se odhlásit se ze všech ostatních zařízení, která mohla použít vaše staré heslo.',\n      readonly:\n        'Vaše heslo nelze aktuálně upravit, protože se můžete přihlásit pouze prostřednictvím podnikového připojení.',\n      successMessage__set: 'Vaše heslo bylo nastaveno.',\n      successMessage__signOutOfOtherSessions: 'Všechna ostatní zařízení byla odhlášena.',\n      successMessage__update: 'Vaše heslo bylo aktualizováno.',\n      title__set: 'Nastavit heslo',\n      title__update: 'Aktualizovat heslo',\n    },\n    phoneNumberPage: {\n      infoText:\n        'Na toto telefonní číslo bude odeslána textová zpráva obsahující ověřovací kód. Mohou být účtovány poplatky za zprávy a data.',\n      removeResource: {\n        messageLine1: '{{identifier}} bude odstraněn z tohoto účtu.',\n        messageLine2: 'Již nebudete moci přihlásit se pomocí tohoto telefonního čísla.',\n        successMessage: '{{phoneNumber}} byl odstraněn z vašeho účtu.',\n        title: 'Odebrat telefonní číslo',\n      },\n      successMessage: '{{identifier}} byl přidán k vašemu účtu.',\n      title: 'Přidat telefonní číslo',\n      verifySubtitle: 'Zadejte ověřovací kód odeslaný na {{identifier}}',\n      verifyTitle: 'Ověřit telefonní číslo',\n    },\n    plansPage: {\n      title: 'Plány',\n    },\n    profilePage: {\n      fileDropAreaHint: 'Doporučená velikost 1:1, až 10MB.',\n      imageFormDestructiveActionSubtitle: 'Odebrat',\n      imageFormSubtitle: 'Nahrát',\n      imageFormTitle: 'Profilový obrázek',\n      readonly: 'Informace o vašem profilu byly poskytnuty podnikovým připojením a nelze je upravovat.',\n      successMessage: 'Váš profil byl aktualizován.',\n      title: 'Aktualizovat profil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Odhlásit se ze zařízení',\n        title: 'Aktivní zařízení',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Znovu připojit',\n        actionLabel__reauthorize: 'Autorizovat nyní',\n        destructiveActionTitle: 'Odebrat',\n        primaryButton: 'Připojit účet',\n        subtitle__disconnected: 'Tento účet byl odpojen.',\n        subtitle__reauthorize:\n          'Požadované rozsahy byly aktualizovány a můžete zaznamenat omezenou funkcionalitu. Prosím znovu autorizujte tuto aplikaci, abyste se vyhnuli problémům',\n        title: 'Připojené účty',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Smazat účet',\n        title: 'Smazat účet',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Odebrat e-mail',\n        detailsAction__nonPrimary: 'Nastavit jako primární',\n        detailsAction__primary: 'Dokončit ověření',\n        detailsAction__unverified: 'Ověřit',\n        primaryButton: 'Přidat e-mailovou adresu',\n        title: 'E-mailové adresy',\n      },\n      enterpriseAccountsSection: {\n        title: 'Podnikové účty',\n      },\n      headerTitle__account: 'Podrobnosti profilu',\n      headerTitle__security: 'Zabezpečení',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Znovu vygenerovat',\n          headerTitle: 'Záložní kódy',\n          subtitle__regenerate:\n            'Získejte novou sadu zabezpečených záložních kódů. Předchozí záložní kódy budou smazány a nelze je použít.',\n          title__regenerate: 'Znovu vygenerovat záložní kódy',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Nastavit jako výchozí',\n          destructiveActionLabel: 'Odebrat',\n        },\n        primaryButton: 'Přidat dvoufázové ověření',\n        title: 'Dvoufázové ověření',\n        totp: {\n          destructiveActionTitle: 'Odebrat',\n          headerTitle: 'Aplikace pro ověřování',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Odebrat',\n        menuAction__rename: 'Přejmenovat',\n        primaryButton: 'Přidat přístupový klíč',\n        title: 'Přístupové klíče',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Nastavit heslo',\n        primaryButton__updatePassword: 'Aktualizovat heslo',\n        title: 'Heslo',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Odebrat telefonní číslo',\n        detailsAction__nonPrimary: 'Nastavit jako primární',\n        detailsAction__primary: 'Dokončit ověření',\n        detailsAction__unverified: 'Ověřit telefonní číslo',\n        primaryButton: 'Přidat telefonní číslo',\n        title: 'Telefonní čísla',\n      },\n      profileSection: {\n        primaryButton: 'Aktualizovat profil',\n        title: 'Profil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Nastavit uživatelské jméno',\n        primaryButton__updateUsername: 'Aktualizovat uživatelské jméno',\n        title: 'Uživatelské jméno',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Odebrat peněženku',\n        detailsAction__nonPrimary: 'Nastavit jako primární',\n        primaryButton: 'Připojit peněženku',\n        title: 'Web3 peněženky',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Vaše uživatelské jméno bylo aktualizováno.',\n      title__set: 'Nastavit uživatelské jméno',\n      title__update: 'Aktualizovat uživatelské jméno',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} bude odstraněn z tohoto účtu.',\n        messageLine2: 'Již se nebudete moci přihlásit pomocí této web3 peněženky.',\n        successMessage: '{{web3Wallet}} byl odstraněn z vašeho účtu.',\n        title: 'Odebrat web3 peněženku',\n      },\n      subtitle__availableWallets: 'Vyberte web3 peněženku k připojení k vašemu účtu.',\n      subtitle__unavailableWallets: 'Nejsou k dispozici žádné web3 peněženky.',\n      successMessage: 'Peněženka byla přidána k vašemu účtu.',\n      title: 'Přidat web3 peněženku',\n      web3WalletButtonsBlockButton: '{{provider|titleize}}',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: 'Přihlásit se',\n      actionText: 'Už máte přístup?',\n      formButton: 'Připojit se k čekací listině',\n      subtitle: 'Zadejte svou e-mailovou adresu a dáme vám vědět, až bude vaše místo připraveno',\n      title: 'Připojit se k čekací listině',\n    },\n    success: {\n      message: 'Brzy budete přesměrováni...',\n      subtitle: 'Ozveme se vám, až bude vaše místo připraveno',\n      title: 'Děkujeme za připojení k čekací listině!',\n    },\n  },\n};\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCACE;AAAA,IACF,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BACE;AAAA,IACF,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBACE;AAAA,IACF,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBACE;AAAA,MACF,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,qBAAqB;AAAA,MACnB,OAAO;AAAA,MACP,qBAAqB;AAAA,MACrB,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,0BAA0B;AAAA,IAC1B,+BAA+B;AAAA,IAC/B,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBACE;AAAA,EACF,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cACE;AAAA,UACF,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UACE;AAAA,MACF,kBACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBAAiB;AAAA,IACjB,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BACE;AAAA,IACF,sCAAsC;AAAA,IACtC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBAAoB;AAAA,IACpB,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cACE;AAAA,UACF,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UACE;AAAA,MACF,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}