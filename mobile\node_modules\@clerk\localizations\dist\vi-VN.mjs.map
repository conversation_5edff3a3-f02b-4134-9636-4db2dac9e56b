{"version": 3, "sources": ["../src/vi-VN.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const viVN: LocalizationResource = {\n  locale: 'vi-VN',\n  apiKeys: {\n    action__add: 'Thêm khoá mới',\n    action__search: 'Tìm kiếm khoá',\n    createdAndExpirationStatus__expiresOn:\n      \"Tạo {{ createdDate | shortDate('vi-VN') }} • Hết hạn {{ expiresDate | longDate('vi-VN') }}\",\n    createdAndExpirationStatus__never: \"Tạo {{ createdDate | shortDate('vi-VN') }} • Không hết hạn\",\n    detailsTitle__emptyRow: 'Không tìm thấy khoá API',\n    formButtonPrimary__add: 'Tạo khoá',\n    formFieldCaption__expiration__expiresOn: 'Hết hạn {{ date }}',\n    formFieldCaption__expiration__never: 'Khoá này sẽ không hết hạn',\n    formFieldOption__expiration__180d: '180 Ngày',\n    formFieldOption__expiration__1d: '1 Ngày',\n    formFieldOption__expiration__1y: '1 Năm',\n    formFieldOption__expiration__30d: '30 Ngày',\n    formFieldOption__expiration__60d: '60 Ngày',\n    formFieldOption__expiration__7d: '7 Ngày',\n    formFieldOption__expiration__90d: '90 Ngày',\n    formFieldOption__expiration__never: 'Không hết hạn',\n    formHint: 'Nhập tên để tạo khoá mới. Bạn sẽ có thể hủy bỏ bất kỳ lúc nào.',\n    formTitle: 'Thêm khoá API mới',\n\n    lastUsed__days: '{{days}} ngày trước',\n    lastUsed__hours: '{{hours}} giờ trước',\n    lastUsed__minutes: '{{minutes}} phút trước',\n    lastUsed__months: '{{months}} tháng trước',\n    lastUsed__seconds: '{{seconds}} giây trước',\n    lastUsed__years: '{{years}} năm trước',\n    menuAction__revoke: 'Hủy khoá',\n    revokeConfirmation: {\n      confirmationText: 'Hủy',\n      formButtonPrimary__revoke: 'Hủy khoá',\n      formHint: 'Bạn có chắc chắn muốn xóa khoá này không?',\n      formTitle: 'Hủy khoá \"{{apiKeyName}}\" không?',\n    },\n  },\n  backButton: 'Quay lại',\n  badge__activePlan: 'Đang hoạt động',\n  badge__canceledEndsAt: \"Đã hủy • Kết thúc {{ date | shortDate('vi-VN') }}\",\n  badge__currentPlan: 'Gói hiện tại',\n  badge__default: 'Mặc định',\n  badge__endsAt: \"Kết thúc {{ date | shortDate('vi-VN') }}\",\n  badge__expired: 'Đã hết hạn',\n  badge__otherImpersonatorDevice: 'Thiết bị giả mạo khác',\n  badge__primary: 'Chính',\n  badge__renewsAt: \"Gia hạn {{ date | shortDate('vi-VN') }}\",\n  badge__requiresAction: 'Yêu cầu hành động',\n  badge__startsAt: \"Bắt đầu {{ date | shortDate('vi-VN') }}\",\n  badge__thisDevice: 'Thiết bị này',\n  badge__unverified: 'Chưa xác minh',\n  badge__upcomingPlan: 'Sắp tới',\n  badge__userDevice: 'Thiết bị người dùng',\n  badge__you: 'Bạn',\n  commerce: {\n    addPaymentMethod: 'Thêm phương thức thanh toán',\n    alwaysFree: 'Miễn phí mãi mãi',\n    annually: 'Hàng năm',\n    availableFeatures: 'Tính năng có sẵn',\n    billedAnnually: 'Tính phí hàng năm',\n    billedMonthlyOnly: 'Chỉ tính phí hàng tháng',\n    cancelSubscription: 'Hủy đăng ký',\n    cancelSubscriptionAccessUntil:\n      \"Bạn có thể tiếp tục sử dụng tính năng '{{plan}}' cho đến {{ date | longDate('vi-VN') }}, sau đó bạn sẽ không còn quyền truy cập.\",\n    cancelSubscriptionNoCharge: 'Bạn sẽ không bị tính phí cho đăng ký này.',\n    cancelSubscriptionTitle: 'Hủy đăng ký {{plan}}?',\n    cannotSubscribeMonthly:\n      'Bạn không thể đăng ký gói này bằng cách thanh toán hàng tháng. Để đăng ký gói này, bạn cần chọn thanh toán hàng năm.',\n    checkout: {\n      description__paymentSuccessful: 'Thanh toán của bạn đã thành công.',\n      description__subscriptionSuccessful: 'Đăng ký mới của bạn đã được thiết lập.',\n      downgradeNotice:\n        'Bạn sẽ giữ đăng ký hiện tại và các tính năng của nó cho đến cuối chu kỳ thanh toán, sau đó bạn sẽ được chuyển sang đăng ký này.',\n      emailForm: {\n        subtitle: 'Trước khi bạn có thể hoàn thành việc mua hàng, bạn phải thêm địa chỉ email nơi gửi hóa đơn.',\n        title: 'Thêm địa chỉ email',\n      },\n      lineItems: {\n        title__paymentMethod: 'Phương thức thanh toán',\n        title__statementId: 'ID hóa đơn',\n        title__subscriptionBegins: 'Đăng ký bắt đầu',\n        title__totalPaid: 'Tổng thanh toán',\n      },\n      pastDueNotice: 'Đăng ký trước của bạn đã quá hạn và chưa thanh toán.',\n      perMonth: 'hàng tháng',\n      title: 'Thanh toán',\n      title__paymentSuccessful: 'Thanh toán thành công!',\n      title__subscriptionSuccessful: 'Thành công!',\n    },\n    credit: 'Tín dụng',\n    creditRemainder: 'Tín dụng cho phần còn lại của đăng ký hiện tại.',\n    defaultFreePlanActive: 'Bạn hiện đang trên gói Miễn phí',\n    free: 'Miễn phí',\n    getStarted: 'Bắt đầu',\n    keepSubscription: 'Giữ đăng ký',\n    manage: 'Quản lý',\n    manageSubscription: 'Quản lý đăng ký',\n    month: 'Tháng',\n    monthly: 'Hàng tháng',\n    pastDue: 'Quá hạn',\n    pay: 'Thanh toán {{amount}}',\n    paymentMethods: 'Phương thức thanh toán',\n    paymentSource: {\n      applePayDescription: {\n        annual: 'Thanh toán hàng năm',\n        monthly: 'Thanh toán hàng tháng',\n      },\n      dev: {\n        anyNumbers: 'Bất kỳ số nào',\n        cardNumber: 'Số thẻ',\n        cvcZip: 'CVC, ZIP',\n        developmentMode: 'Chế độ phát triển',\n        expirationDate: 'Ngày hết hạn',\n        testCardInfo: 'Thông tin thử nghiệm',\n      },\n    },\n    popular: 'Phổ biến',\n    pricingTable: {\n      billingCycle: 'Chu kỳ thanh toán',\n      included: 'Bao gồm',\n    },\n    reSubscribe: 'Đăng ký lại',\n    seeAllFeatures: 'Xem tất cả tính năng',\n    subscribe: 'Đăng ký',\n    subtotal: 'Tổng cộng',\n    switchPlan: 'Chuyển sang gói này',\n    switchToAnnual: 'Chuyển sang hàng năm',\n    switchToMonthly: 'Chuyển sang hàng tháng',\n    totalDue: 'Tổng cần thanh toán',\n    totalDueToday: 'Tổng cần thanh toán hôm nay',\n    viewFeatures: 'Xem tính năng',\n    year: 'Năm',\n  },\n  createOrganization: {\n    formButtonSubmit: 'Tạo tổ chức',\n    invitePage: {\n      formButtonReset: 'Bỏ qua',\n    },\n    title: 'Tạo tổ chức',\n  },\n  dates: {\n    lastDay: \"Hôm qua lúc {{ date | timeString('vi-VN') }}\",\n    next6Days: \"{{ date | weekday('vi-VN','long') }} lúc {{ date | timeString('vi-VN') }}\",\n    nextDay: \"Ngày mai lúc {{ date | timeString('vi-VN') }}\",\n    numeric: \"{{ date | numeric('vi-VN') }}\",\n    previous6Days: \"{{ date | weekday('vi-VN','long') }} lúc {{ date | timeString('vi-VN') }}\",\n    sameDay: \"Hôm nay lúc {{ date | timeString('vi-VN') }}\",\n  },\n  dividerText: 'hoặc',\n  footerActionLink__alternativePhoneCodeProvider: 'Gửi mã qua SMS thay vì email',\n  footerActionLink__useAnotherMethod: 'Sử dụng phương thức khác',\n  footerPageLink__help: 'Trợ giúp',\n  footerPageLink__privacy: 'Quyền riêng tư',\n  footerPageLink__terms: 'Điều khoản',\n  formButtonPrimary: 'Tiếp tục',\n  formButtonPrimary__verify: 'Xác minh',\n  formFieldAction__forgotPassword: 'Quên mật khẩu?',\n  formFieldError__matchingPasswords: 'Mật khẩu trùng khớp.',\n  formFieldError__notMatchingPasswords: 'Mật khẩu không trùng khớp.',\n  formFieldError__verificationLinkExpired: 'Liên kết xác minh đã hết hạn. Vui lòng yêu cầu liên kết mới.',\n  formFieldHintText__optional: 'Tùy chọn',\n  formFieldHintText__slug:\n    'Một slug là một ID dễ đọc bởi con người mà phải độc nhất. Nó thường được sử dụng trong URL.',\n  formFieldInputPlaceholder__apiKeyDescription: 'Giải thích tại sao bạn đang tạo khoá này',\n  formFieldInputPlaceholder__apiKeyExpirationDate: 'Chọn ngày',\n  formFieldInputPlaceholder__apiKeyName: 'Nhập tên khoá bí mật',\n  formFieldInputPlaceholder__backupCode: 'Nhập mã dự phòng',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Xóa tài khoản',\n  formFieldInputPlaceholder__emailAddress: 'Nhập địa chỉ email của bạn',\n  formFieldInputPlaceholder__emailAddress_username: 'Nhập email hoặc tên người dùng',\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: 'Tên',\n  formFieldInputPlaceholder__lastName: 'Họ',\n  formFieldInputPlaceholder__organizationDomain: 'example.com',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: '<EMAIL>',\n  formFieldInputPlaceholder__organizationName: 'Tên tổ chức',\n  formFieldInputPlaceholder__organizationSlug: 'my-org',\n  formFieldInputPlaceholder__password: 'Nhập mật khẩu của bạn',\n  formFieldInputPlaceholder__phoneNumber: 'Nhập số điện thoại của bạn',\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: 'Mô tả',\n  formFieldLabel__apiKeyExpiration: 'Hết hạn',\n  formFieldLabel__apiKeyName: 'Tên khoá bí mật',\n  formFieldLabel__automaticInvitations: 'Cho phép tự động mời cho tên miền này',\n  formFieldLabel__backupCode: 'Mã dự phòng',\n  formFieldLabel__confirmDeletion: 'Xác nhận',\n  formFieldLabel__confirmPassword: 'Xác nhận mật khẩu',\n  formFieldLabel__currentPassword: 'Mật khẩu hiện tại',\n  formFieldLabel__emailAddress: 'Địa chỉ email',\n  formFieldLabel__emailAddress_username: 'Email hoặc tên người dùng',\n  formFieldLabel__emailAddresses: 'Địa chỉ email',\n  formFieldLabel__firstName: 'Tên',\n  formFieldLabel__lastName: 'Họ',\n  formFieldLabel__newPassword: 'Mật khẩu mới',\n  formFieldLabel__organizationDomain: 'Tên miền',\n  formFieldLabel__organizationDomainDeletePending: 'Xóa mời và gợi ý đang chờ',\n  formFieldLabel__organizationDomainEmailAddress: 'Địa chỉ email xác minh',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Nhập địa chỉ email dưới tên miền này để nhận mã và xác minh tên miền này.',\n  formFieldLabel__organizationName: 'Tên',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Tên của passkey',\n  formFieldLabel__password: 'Mật khẩu',\n  formFieldLabel__phoneNumber: 'Số điện thoại',\n  formFieldLabel__role: 'Vai trò',\n  formFieldLabel__signOutOfOtherSessions: 'Đăng xuất khỏi tất cả thiết bị khác',\n  formFieldLabel__username: 'Tên người dùng',\n  impersonationFab: {\n    action__signOut: 'Đăng xuất',\n    title: 'Đăng nhập với {{identifier}}',\n  },\n  maintenanceMode: 'Chúng tôi đang trong quá trình bảo trì, nhưng đừng lo lắng, nó không nên mất quá nhiều thời gian.',\n  membershipRole__admin: 'Quản trị viên',\n  membershipRole__basicMember: 'Thành viên',\n  membershipRole__guestMember: 'Khách',\n  organizationList: {\n    action__createOrganization: 'Tạo tổ chức',\n    action__invitationAccept: 'Tham gia',\n    action__suggestionsAccept: 'Yêu cầu tham gia',\n    createOrganization: 'Tạo tổ chức',\n    invitationAcceptedLabel: 'Tham gia',\n    subtitle: 'để tiếp tục đến {{applicationName}}',\n    suggestionsAcceptedLabel: 'Đang chờ phê duyệt',\n    title: 'Chọn tài khoản',\n    titleWithoutPersonal: 'Chọn tổ chức',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: 'Khoá API',\n    },\n    badge__automaticInvitation: 'Tự động mời',\n    badge__automaticSuggestion: 'Tự động gợi ý',\n    badge__manualInvitation: 'Không tự động đăng ký',\n    badge__unverified: 'Chưa xác minh',\n    billingPage: {\n      paymentHistorySection: {\n        empty: 'Không có lịch sử thanh toán',\n        notFound: 'Không tìm thấy lịch sử thanh toán',\n        tableHeader__amount: 'Số tiền',\n        tableHeader__date: 'Ngày',\n        tableHeader__status: 'Trạng thái',\n      },\n      paymentSourcesSection: {\n        actionLabel__default: 'Làm mặc định',\n        actionLabel__remove: 'Xóa',\n        add: 'Thêm phương thức thanh toán mới',\n        addSubtitle: 'Thêm phương thức thanh toán mới vào tài khoản của bạn.',\n        cancelButton: 'Hủy',\n        formButtonPrimary__add: 'Thêm phương thức thanh toán',\n        formButtonPrimary__pay: 'Thanh toán {{amount}}',\n        payWithTestCardButton: 'Thanh toán với thẻ thử nghiệm',\n        removeResource: {\n          messageLine1: '{{identifier}} sẽ bị xóa khỏi tài khoản này.',\n          messageLine2:\n            'Bạn sẽ không còn thể sử dụng nguồn thanh toán này và bất kỳ đăng ký lặp lại nào phụ thuộc vào nó sẽ không còn hoạt động.',\n          successMessage: '{{paymentSource}} đã bị xóa khỏi tài khoản của bạn.',\n          title: 'Xóa phương thức thanh toán',\n        },\n        title: 'Phương thức thanh toán',\n      },\n      start: {\n        headerTitle__payments: 'Thanh toán',\n        headerTitle__plans: 'Gói',\n        headerTitle__statements: 'Hóa đơn',\n        headerTitle__subscriptions: 'Đăng ký',\n      },\n      statementsSection: {\n        empty: 'Không có hóa đơn để hiển thị',\n        itemCaption__paidForPlan: 'Thanh toán cho gói {{plan}} {{period}}',\n        itemCaption__proratedCredit: 'Tín dụng phân chia cho sử dụng một phần của đăng ký trước',\n        itemCaption__subscribedAndPaidForPlan: 'Đăng ký và thanh toán cho gói {{plan}} {{period}}',\n        notFound: 'Không tìm thấy hóa đơn',\n        tableHeader__amount: 'Số tiền',\n        tableHeader__date: 'Ngày',\n        title: 'Hóa đơn',\n        totalPaid: 'Tổng thanh toán',\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: 'Đăng ký gói',\n        actionLabel__switchPlan: 'Chuyển gói',\n        tableHeader__edit: 'Sửa',\n        tableHeader__plan: 'Gói',\n        tableHeader__startDate: 'Ngày bắt đầu',\n        title: 'Đăng ký',\n      },\n      subscriptionsSection: {\n        actionLabel__default: 'Quản lý',\n      },\n      switchPlansSection: {\n        title: 'Chuyển gói',\n      },\n      title: 'Thanh toán',\n    },\n    createDomainPage: {\n      subtitle:\n        'Thêm tên miền để xác minh. Người dùng với địa chỉ email ở tên miền này có thể tham gia tổ chức tự động hoặc yêu cầu tham gia.',\n      title: 'Thêm tên miền',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Mời không thể được gửi. Đã có mời đang chờ cho các địa chỉ email sau: {{email_addresses}}.',\n      formButtonPrimary__continue: 'Gửi mời',\n      selectDropdown__role: 'Chọn vai trò',\n      subtitle: 'Nhập hoặc dán một hoặc nhiều địa chỉ email, cách nhau bằng dấu cách hoặc dấu phẩy.',\n      successMessage: 'Mời đã được gửi thành công',\n      title: 'Mời thành viên mới',\n    },\n    membersPage: {\n      action__invite: 'Mời',\n      action__search: 'Tìm kiếm',\n      activeMembersTab: {\n        menuAction__remove: 'Xóa thành viên',\n        tableHeader__actions: 'Hành động',\n        tableHeader__joined: 'Tham gia',\n        tableHeader__role: 'Vai trò',\n        tableHeader__user: 'Người dùng',\n      },\n      detailsTitle__emptyRow: 'Không có thành viên để hiển thị',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Mời người dùng bằng cách kết nối tên miền email với tổ chức của bạn. Bất kỳ ai đăng ký với tên miền email khớp sẽ có thể tham gia tổ chức bất cứ lúc nào.',\n          headerTitle: 'Tự động mời',\n          primaryButton: 'Quản lý tên miền đã xác minh',\n        },\n        table__emptyRow: 'Không có mời để hiển thị',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Hủy mời',\n        tableHeader__invited: 'Đã mời',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Người dùng đăng ký với tên miền email khớp sẽ có thể thấy gợi ý để yêu cầu tham gia tổ chức của bạn.',\n          headerTitle: 'Tự động gợi ý',\n          primaryButton: 'Quản lý tên miền đã xác minh',\n        },\n        menuAction__approve: 'Phê duyệt',\n        menuAction__reject: 'Từ chối',\n        tableHeader__requested: 'Yêu cầu truy cập',\n        table__emptyRow: 'Không có yêu cầu để hiển thị',\n      },\n      start: {\n        headerTitle__invitations: 'Mời',\n        headerTitle__members: 'Thành viên',\n        headerTitle__requests: 'Yêu cầu',\n      },\n    },\n    navbar: {\n      apiKeys: 'Khoá API',\n      billing: 'Thanh toán',\n      description: 'Quản lý tổ chức của bạn.',\n      general: 'Tổng quan',\n      members: 'Thành viên',\n      title: 'Tổ chức',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: 'Bạn không có quyền quản lý thanh toán cho tổ chức này.',\n      },\n      title: 'Gói',\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Nhập \"{{organizationName}}\" dưới để tiếp tục.',\n          messageLine1: 'Bạn có chắc chắn muốn xóa tổ chức này?',\n          messageLine2: 'Hành động này là vĩnh viễn và không thể hoàn tác.',\n          successMessage: 'Bạn đã xóa tổ chức.',\n          title: 'Xóa tổ chức',\n        },\n        leaveOrganization: {\n          actionDescription: 'Nhập \"{{organizationName}}\" dưới để tiếp tục.',\n          messageLine1:\n            'Bạn có chắc chắn muốn rời khỏi tổ chức này? Bạn sẽ mất quyền truy cập vào tổ chức này và ứng dụng của nó.',\n          messageLine2: 'Hành động này là vĩnh viễn và không thể hoàn tác.',\n          successMessage: 'Bạn đã rời khỏi tổ chức.',\n          title: 'Rời khỏi tổ chức',\n        },\n        title: 'Nguy hiểm',\n      },\n      domainSection: {\n        menuAction__manage: 'Quản lý',\n        menuAction__remove: 'Xóa',\n        menuAction__verify: 'Xác minh',\n        primaryButton: 'Thêm tên miền',\n        subtitle:\n          'Cho phép người dùng tham gia tổ chức tự động hoặc yêu cầu tham gia dựa trên tên miền email đã xác minh.',\n        title: 'Tên miền đã xác minh',\n      },\n      successMessage: 'Tổ chức đã được cập nhật.',\n      title: 'Cập nhật hồ sơ',\n    },\n    removeDomainPage: {\n      messageLine1: 'Tên miền email {{domain}} sẽ bị xóa.',\n      messageLine2: 'Người dùng sẽ không thể tham gia tổ chức tự động sau đây.',\n      successMessage: '{{domain}} đã bị xóa.',\n      title: 'Xóa tên miền',\n    },\n    start: {\n      headerTitle__general: 'Tổng quan',\n      headerTitle__members: 'Thành viên',\n      profileSection: {\n        primaryButton: 'Cập nhật hồ sơ',\n        title: 'Hồ sơ tổ chức',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Xóa tên miền này sẽ ảnh hưởng đến người dùng đã mời.',\n        removeDomainActionLabel__remove: 'Xóa tên miền',\n        removeDomainSubtitle: 'Xóa tên miền này khỏi tên miền đã xác minh',\n        removeDomainTitle: 'Xóa tên miền',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Người dùng sẽ tự động được mời tham gia tổ chức khi đăng ký và có thể tham gia bất cứ lúc nào.',\n        automaticInvitationOption__label: 'Tự động mời',\n        automaticSuggestionOption__description:\n          'Người dùng sẽ nhận được gợi ý để yêu cầu tham gia, nhưng phải được phê duyệt bởi quản trị viên trước khi họ có thể tham gia tổ chức.',\n        automaticSuggestionOption__label: 'Tự động gợi ý',\n        calloutInfoLabel: 'Thay đổi chế độ đăng ký sẽ chỉ ảnh hưởng đến người dùng mới.',\n        calloutInvitationCountLabel: 'Mời đang chờ gửi đến người dùng: {{count}}',\n        calloutSuggestionCountLabel: 'Gợi ý đang chờ gửi đến người dùng: {{count}}',\n        manualInvitationOption__description: 'Người dùng chỉ có thể được mời thủ công đến tổ chức.',\n        manualInvitationOption__label: 'Không tự động đăng ký',\n        subtitle: 'Chọn cách người dùng từ tên miền này có thể tham gia tổ chức.',\n      },\n      start: {\n        headerTitle__danger: 'Nguy hiểm',\n        headerTitle__enrollment: 'Chế độ đăng ký',\n      },\n      subtitle: 'Tên miền {{domain}} đã được xác minh. Tiếp tục bằng cách chọn chế độ đăng ký.',\n      title: 'Cập nhật {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Nhập mã xác minh đã gửi đến địa chỉ email của bạn',\n      formTitle: 'Mã xác minh',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'Tên miền {{domainName}} cần được xác minh qua email.',\n      subtitleVerificationCodeScreen: 'Một mã xác minh đã được gửi đến {{emailAddress}}. Nhập mã để tiếp tục.',\n      title: 'Xác minh tên miền',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Tạo tổ chức',\n    action__invitationAccept: 'Tham gia',\n    action__manageOrganization: 'Quản lý',\n    action__suggestionsAccept: 'Yêu cầu tham gia',\n    notSelected: 'Không có tổ chức được chọn',\n    personalWorkspace: 'Tài khoản cá nhân',\n    suggestionsAcceptedLabel: 'Đang chờ phê duyệt',\n  },\n  paginationButton__next: 'Tiếp',\n  paginationButton__previous: 'Trước',\n  paginationRowText__displaying: 'Hiển thị',\n  paginationRowText__of: 'của',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Liên hệ hỗ trợ',\n      actionText: 'Không có bất kỳ phương thức nào?',\n      blockButton__backupCode: 'Sử dụng mã dự phòng',\n      blockButton__emailCode: 'Email mã đến {{identifier}}',\n      blockButton__passkey: 'Đăng nhập với mã passkey',\n      blockButton__password: 'Tiếp tục với mật khẩu của bạn',\n      blockButton__phoneCode: 'Gửi mã SMS đến {{identifier}}',\n      blockButton__totp: 'Sử dụng ứng dụng xác thực',\n      getHelp: {\n        blockButton__emailSupport: 'Email hỗ trợ',\n        content:\n          'Nếu bạn gặp vấn đề khi xác minh tài khoản của mình, email chúng tôi và chúng tôi sẽ hỗ trợ bạn khôi phục quyền truy cập sớm nhất có thể.',\n        title: 'Liên hệ hỗ trợ',\n      },\n      subtitle: 'Gặp vấn đề? Bạn có thể sử dụng bất kỳ phương thức nào để xác minh.',\n      title: 'Sử dụng phương thức khác',\n    },\n    backupCodeMfa: {\n      subtitle: 'Nhập mã dự phòng bạn nhận được khi thiết lập xác thực hai bước',\n      title: 'Nhập mã dự phòng',\n    },\n    emailCode: {\n      formTitle: 'Mã xác minh',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'Nhập mã đã gửi đến email của bạn để tiếp tục',\n      title: 'Xác minh yêu cầu',\n    },\n    noAvailableMethods: {\n      message: 'Không thể tiếp tục với xác minh. Không có yếu tố xác thực phù hợp được cấu hình',\n      subtitle: 'Đã xảy ra lỗi',\n      title: 'Không thể xác minh tài khoản của bạn',\n    },\n    passkey: {\n      blockButton__passkey: 'Sử dụng mã passkey',\n      subtitle:\n        'Sử dụng mã passkey xác minh danh tính của bạn. Thiết bị của bạn có thể yêu cầu vân tay, khuôn mặt hoặc khóa màn hình.',\n      title: 'Sử dụng mã passkey',\n    },\n    password: {\n      actionLink: 'Sử dụng phương thức khác',\n      subtitle: 'Nhập mật khẩu hiện tại để tiếp tục',\n      title: 'Xác minh yêu cầu',\n    },\n    phoneCode: {\n      formTitle: 'Mã xác minh',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'Nhập mã đã gửi đến điện thoại của bạn để tiếp tục',\n      title: 'Xác minh yêu cầu',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Mã xác minh',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'Nhập mã đã gửi đến điện thoại của bạn để tiếp tục',\n      title: 'Xác minh yêu cầu',\n    },\n    totpMfa: {\n      formTitle: 'Mã xác minh',\n      subtitle: 'Nhập mã đã được tạo bởi ứng dụng xác thực của bạn để tiếp tục',\n      title: 'Xác minh yêu cầu',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Thêm tài khoản',\n      action__signOutAll: 'Đăng xuất khỏi tất cả tài khoản',\n      subtitle: 'Chọn tài khoản với đó bạn muốn tiếp tục.',\n      title: 'Chọn tài khoản',\n    },\n    alternativeMethods: {\n      actionLink: 'Liên hệ hỗ trợ',\n      actionText: 'Không có bất kỳ phương thức nào?',\n      blockButton__backupCode: 'Sử dụng mã dự phòng',\n      blockButton__emailCode: 'Email mã đến {{identifier}}',\n      blockButton__emailLink: 'Email liên kết đến {{identifier}}',\n      blockButton__passkey: 'Đăng nhập với mã passkey',\n      blockButton__password: 'Đăng nhập với mật khẩu',\n      blockButton__phoneCode: 'Gửi mã SMS đến {{identifier}}',\n      blockButton__totp: 'Sử dụng ứng dụng xác thực',\n      getHelp: {\n        blockButton__emailSupport: 'Email hỗ trợ',\n        content:\n          'Nếu bạn gặp vấn đề khi đăng nhập vào tài khoản của mình, email chúng tôi và chúng tôi sẽ hỗ trợ bạn khôi phục quyền truy cập sớm nhất có thể.',\n        title: 'Liên hệ hỗ trợ',\n      },\n      subtitle: 'Gặp vấn đề? Bạn có thể sử dụng bất kỳ phương thức nào để đăng nhập.',\n      title: 'Sử dụng phương thức khác',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: 'Mã xác minh',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'để tiếp tục đến {{applicationName}}',\n      title: 'Kiểm tra {{provider}}',\n    },\n    backupCodeMfa: {\n      subtitle: 'Mã dự phòng của bạn là mã bạn nhận được khi thiết lập xác thực hai bước.',\n      title: 'Nhập mã dự phòng',\n    },\n    emailCode: {\n      formTitle: 'Mã xác minh',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'để tiếp tục đến {{applicationName}}',\n      title: 'Kiểm tra email',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Để tiếp tục, mở liên kết xác minh trên thiết bị và trình duyệt từ đó bạn đã khởi động đăng nhập',\n        title: 'Liên kết xác minh không hợp lệ cho thiết bị này',\n      },\n      expired: {\n        subtitle: 'Quay lại tab gốc để tiếp tục.',\n        title: 'Liên kết xác minh đã hết hạn',\n      },\n      failed: {\n        subtitle: 'Quay lại tab gốc để tiếp tục.',\n        title: 'Liên kết xác minh không hợp lệ',\n      },\n      formSubtitle: 'Sử dụng liên kết xác minh đã gửi đến email của bạn',\n      formTitle: 'Liên kết xác minh',\n      loading: {\n        subtitle: 'Bạn sẽ được chuyển hướng sớm',\n        title: 'Đang đăng nhập...',\n      },\n      resendButton: 'Không nhận được liên kết? Gửi lại',\n      subtitle: 'để tiếp tục đến {{applicationName}}',\n      title: 'Kiểm tra email',\n      unusedTab: {\n        title: 'Bạn có thể đóng tab này',\n      },\n      verified: {\n        subtitle: 'Bạn sẽ được chuyển hướng sớm',\n        title: 'Đăng nhập thành công',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Quay lại tab gốc để tiếp tục',\n        subtitleNewTab: 'Quay lại tab mới được mở để tiếp tục',\n        titleNewTab: 'Đăng nhập trên tab khác',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Mã xác minh mật khẩu',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'để đặt lại mật khẩu của bạn',\n      subtitle_email: 'Đầu tiên, nhập mã đã gửi đến email của bạn',\n      subtitle_phone: 'Đầu tiên, nhập mã đã gửi đến điện thoại của bạn',\n      title: 'Đặt lại mật khẩu',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Đặt lại mật khẩu',\n      label__alternativeMethods: 'Hoặc, đăng nhập với phương thức khác',\n      title: 'Quên mật khẩu?',\n    },\n    noAvailableMethods: {\n      message: 'Không thể tiếp tục đăng nhập. Không có yếu tố xác thực phù hợp được cấu hình',\n      subtitle: 'Đã xảy ra lỗi',\n      title: 'Không thể đăng nhập',\n    },\n    passkey: {\n      subtitle:\n        'Sử dụng mã passkey xác minh danh tính của bạn. Thiết bị của bạn có thể yêu cầu vân tay, khuôn mặt hoặc khóa màn hình.',\n      title: 'Sử dụng mã passkey',\n    },\n    password: {\n      actionLink: 'Sử dụng phương thức khác',\n      subtitle: 'Nhập mật khẩu được liên kết với tài khoản của bạn',\n      title: 'Nhập mật khẩu',\n    },\n    passwordPwned: {\n      title: 'Mật khẩu bị rò rỉ',\n    },\n    phoneCode: {\n      formTitle: 'Mã xác minh',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'để tiếp tục đến {{applicationName}}',\n      title: 'Kiểm tra điện thoại',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Mã xác minh',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'để tiếp tục đến {{applicationName}}',\n      title: 'Kiểm tra điện thoại',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Đặt lại mật khẩu',\n      requiredMessage: 'Vì lý do bảo mật, việc đặt lại mật khẩu là bắt buộc.',\n      successMessage: 'Mật khẩu của bạn đã được thay đổi thành công. Đang đăng nhập, vui lòng chờ một lát.',\n      title: 'Đặt mật khẩu mới',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Chúng tôi cần xác minh danh tính của bạn trước khi đặt lại mật khẩu.',\n    },\n    start: {\n      actionLink: 'Đăng ký',\n      actionLink__join_waitlist: 'Tham gia danh sách chờ',\n      actionLink__use_email: 'Sử dụng email',\n      actionLink__use_email_username: 'Sử dụng email hoặc tên người dùng',\n      actionLink__use_passkey: 'Sử dụng mã passkey thay vì',\n      actionLink__use_phone: 'Sử dụng điện thoại',\n      actionLink__use_username: 'Sử dụng tên người dùng',\n      actionText: 'Không có tài khoản?',\n      actionText__join_waitlist: 'Muốn trải nghiệm sớm?',\n      alternativePhoneCodeProvider: {\n        actionLink: 'Sử dụng phương thức khác',\n        label: '{{provider}} số điện thoại',\n        subtitle: 'Nhập số điện thoại của bạn để nhận mã xác minh trên {{provider}}.',\n        title: 'Đăng nhập vào {{applicationName}} với {{provider}}',\n      },\n      subtitle: 'Chào mừng trở lại! Vui lòng đăng nhập để tiếp tục',\n      subtitleCombined: undefined,\n      title: 'Đăng nhập vào {{applicationName}}',\n      titleCombined: 'Tiếp tục đến {{applicationName}}',\n    },\n    totpMfa: {\n      formTitle: 'Mã xác minh',\n      subtitle: 'Để tiếp tục, vui lòng nhập mã xác minh được tạo bởi ứng dụng xác thực của bạn',\n      title: 'Xác thực hai bước',\n    },\n  },\n  signInEnterPasswordTitle: 'Nhập mật khẩu',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'Nhập mã xác minh đã gửi đến {{provider}}',\n      title: 'Xác minh {{provider}}',\n    },\n    continue: {\n      actionLink: 'Đăng nhập',\n      actionText: 'Đã có tài khoản?',\n      subtitle: 'Vui lòng điền các chi tiết còn lại để tiếp tục.',\n      title: 'Điền các trường còn thiếu',\n    },\n    emailCode: {\n      formSubtitle: 'Nhập mã xác minh đã gửi đến email của bạn',\n      formTitle: 'Mã xác minh',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'Nhập mã xác minh đã gửi đến email của bạn',\n      title: 'Xác minh email',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Để tiếp tục, mở liên kết xác minh trên thiết bị và trình duyệt từ đó bạn đã khởi động đăng ký',\n        title: 'Liên kết xác minh không hợp lệ cho thiết bị này',\n      },\n      formSubtitle: 'Sử dụng liên kết xác minh đã gửi đến email của bạn',\n      formTitle: 'Liên kết xác minh',\n      loading: {\n        title: 'Đang đăng ký...',\n      },\n      resendButton: 'Không nhận được liên kết? Gửi lại',\n      subtitle: 'để tiếp tục đến {{applicationName}}',\n      title: 'Xác minh email',\n      verified: {\n        title: 'Đăng ký thành công',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Quay lại tab mới được mở để tiếp tục',\n        subtitleNewTab: 'Quay lại tab trước để tiếp tục',\n        title: 'Xác minh email thành công',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Tôi đồng ý với {{ privacyPolicyLink || link(\"Chính sách bảo mật\") }}',\n        label__onlyTermsOfService: 'Tôi đồng ý với {{ termsOfServiceLink || link(\"Điều khoản dịch vụ\") }}',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Tôi đồng ý với {{ termsOfServiceLink || link(\"Điều khoản dịch vụ\") }} và {{ privacyPolicyLink || link(\"Chính sách bảo mật\") }}',\n      },\n      continue: {\n        subtitle: 'Vui lòng đọc và đồng ý với các điều khoản để tiếp tục',\n        title: 'Đồng ý với điều khoản',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Nhập mã xác minh đã gửi đến số điện thoại của bạn',\n      formTitle: 'Mã xác minh',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'Nhập mã xác minh đã gửi đến số điện thoại của bạn',\n      title: 'Xác minh điện thoại',\n    },\n    restrictedAccess: {\n      actionLink: 'Đăng nhập',\n      actionText: 'Đã có tài khoản?',\n      blockButton__emailSupport: 'Email hỗ trợ',\n      blockButton__joinWaitlist: 'Tham gia danh sách chờ',\n      subtitle: 'Đăng ký hiện không khả dụng. Nếu bạn tin rằng bạn có quyền truy cập, vui lòng liên hệ hỗ trợ.',\n      subtitleWaitlist:\n        'Đăng ký hiện không khả dụng. Để biết thông tin sớm nhất khi chúng tôi khởi chạy, hãy tham gia danh sách chờ.',\n      title: 'Quyền truy cập bị giới hạn',\n    },\n    start: {\n      actionLink: 'Đăng nhập',\n      actionLink__use_email: 'Sử dụng email thay vì',\n      actionLink__use_phone: 'Sử dụng điện thoại thay vì',\n      actionText: 'Đã có tài khoản?',\n      alternativePhoneCodeProvider: {\n        actionLink: 'Sử dụng phương thức khác',\n        label: '{{provider}} số điện thoại',\n        subtitle: 'Nhập số điện thoại của bạn để nhận mã xác minh trên {{provider}}.',\n        title: 'Đăng ký vào {{applicationName}} với {{provider}}',\n      },\n      subtitle: 'Chào mừng! Vui lòng điền các chi tiết để bắt đầu.',\n      subtitleCombined: 'Chào mừng! Vui lòng điền các chi tiết để bắt đầu.',\n      title: 'Tạo tài khoản của bạn',\n      titleCombined: 'Tạo tài khoản của bạn',\n    },\n  },\n  socialButtonsBlockButton: 'Tiếp tục với {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: '{{email}} đã là thành viên của tổ chức.',\n    captcha_invalid: undefined,\n    captcha_unavailable:\n      'Đăng ký không thành công do lỗi bot. Vui lòng tải lại trang để thử lại hoặc liên hệ hỗ trợ để được hỗ trợ.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: undefined,\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: undefined,\n    form_param_format_invalid__phone_number: undefined,\n    form_param_max_length_exceeded__first_name: undefined,\n    form_param_max_length_exceeded__last_name: undefined,\n    form_param_max_length_exceeded__name: undefined,\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: 'Mật khẩu của bạn quá ngắn. Nó phải có ít nhất 8 ký tự.',\n    form_password_not_strong_enough: 'Mật khẩu của bạn không đủ mạnh.',\n    form_password_pwned:\n      'Mật khẩu này đã được tìm thấy trong một rò rỉ và không thể được sử dụng, vui lòng thử một mật khẩu khác.',\n    form_password_pwned__sign_in:\n      'Mật khẩu này đã được tìm thấy trong một rò rỉ và không thể được sử dụng, vui lòng đặt lại mật khẩu của bạn.',\n    form_password_size_in_bytes_exceeded: undefined,\n    form_password_validation_failed: undefined,\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: 'Tên người dùng của bạn phải có giữa {{min_length}} và {{max_length}} ký tự.',\n    identification_deletion_failed: undefined,\n    not_allowed_access: undefined,\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: 'Mã passkey đã được đăng ký với thiết bị này.',\n    passkey_not_supported: 'Mã passkey không được hỗ trợ trên thiết bị này.',\n    passkey_pa_not_supported: 'Đăng ký yêu cầu một bộ xác thực nền tảng nhưng thiết bị không hỗ trợ nó.',\n    passkey_registration_cancelled: 'Đăng ký mã passkey đã bị hủy hoặc hết hạn.',\n    passkey_retrieval_cancelled: 'Xác minh mã passkey đã bị hủy hoặc hết hạn.',\n    passwordComplexity: {\n      maximumLength: 'ít hơn {{length}} ký tự',\n      minimumLength: '{{length}} hoặc nhiều hơn ký tự',\n      requireLowercase: 'một chữ cái viết thường',\n      requireNumbers: 'một số',\n      requireSpecialCharacter: 'một ký tự đặc biệt',\n      requireUppercase: 'một chữ cái viết hoa',\n      sentencePrefix: 'Mật khẩu của bạn phải chứa',\n    },\n    phone_number_exists: undefined,\n    session_exists: undefined,\n    web3_missing_identifier: 'Không tìm thấy phần mở rộng Web3 Wallet. Vui lòng cài đặt một phần mở rộng để tiếp tục.',\n    zxcvbn: {\n      couldBeStronger: 'Mật khẩu của bạn hoạt động, nhưng có thể mạnh hơn. Hãy thử thêm nhiều ký tự.',\n      goodPassword: 'Mật khẩu của bạn đáp ứng tất cả các yêu cầu cần thiết.',\n      notEnough: 'Mật khẩu của bạn không đủ mạnh.',\n      suggestions: {\n        allUppercase: 'Viết hoa một số chữ cái, nhưng không phải tất cả.',\n        anotherWord: 'Thêm nhiều từ khác nhau hơn.',\n        associatedYears: 'Tránh năm mà bạn liên quan đến.',\n        capitalization: 'Viết hoa nhiều hơn chữ cái đầu tiên.',\n        dates: 'Tránh ngày và năm mà bạn liên quan đến.',\n        l33t: \"Tránh các thay thế chữ cái dễ dự đoán như '@' thay vì 'a'.\",\n        longerKeyboardPattern: 'Sử dụng các khuôn mẫu bàn phím dài hơn và thay đổi hướng gõ nhiều lần.',\n        noNeed: 'Bạn có thể tạo mật khẩu mạnh mà không sử dụng các ký tự đặc biệt, số hoặc chữ cái viết hoa.',\n        pwned: 'Nếu bạn sử dụng mật khẩu này ở nơi khác, bạn nên thay đổi nó.',\n        recentYears: 'Tránh năm gần đây.',\n        repeated: 'Tránh các từ và ký tự lặp lại.',\n        reverseWords: 'Tránh các từ viết ngược của các từ thông dụng.',\n        sequences: 'Tránh các chuỗi ký tự thông dụng.',\n        useWords: 'Sử dụng nhiều từ, nhưng tránh các cụm từ thông dụng.',\n      },\n      warnings: {\n        common: 'Đây là một mật khẩu thường được sử dụng.',\n        commonNames: 'Tên và họ thường được sử dụng.',\n        dates: 'Ngày thường được sử dụng.',\n        extendedRepeat: 'Các mẫu ký tự lặp lại như \"abcabcabc\" dễ dự đoán.',\n        keyPattern: 'Các mẫu bàn phím ngắn dễ dự đoán.',\n        namesByThemselves: 'Tên hoặc họ dễ dự đoán.',\n        pwned: 'Mật khẩu của bạn đã bị rò rỉ qua một rò rỉ dữ liệu trên Internet.',\n        recentYears: 'Năm gần đây dễ dự đoán.',\n        sequences: 'Các chuỗi ký tự thông dụng như \"abc\" dễ dự đoán.',\n        similarToCommon: 'Đây là tương tự như một mật khẩu thường được sử dụng.',\n        simpleRepeat: 'Các ký tự lặp lại như \"aaa\" dễ dự đoán.',\n        straightRow: 'Các hàng ký tự trên bàn phím của bạn dễ dự đoán.',\n        topHundred: 'Đây là một mật khẩu thường được sử dụng.',\n        topTen: 'Đây là một mật khẩu thường được sử dụng.',\n        userInputs: 'Không nên có bất kỳ dữ liệu cá nhân hoặc liên quan đến trang nào.',\n        wordByItself: 'Các từ đơn dễ dự đoán.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Thêm tài khoản',\n    action__manageAccount: 'Quản lý tài khoản',\n    action__signOut: 'Đăng xuất',\n    action__signOutAll: 'Đăng xuất tất cả tài khoản',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: 'Khoá API',\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Đã sao chép!',\n      actionLabel__copy: 'Sao chép tất cả',\n      actionLabel__download: 'Tải xuống .txt',\n      actionLabel__print: 'In',\n      infoText1: 'Mã sao lưu sẽ được bật cho tài khoản này.',\n      infoText2:\n        'Giữ mã sao lưu bí mật và lưu trữ chúng một cách an toàn. Bạn có thể tạo lại mã sao lưu nếu bạn nghi ngờ chúng đã bị rò rỉ.',\n      subtitle__codelist: 'Lưu trữ chúng một cách an toàn và giữ bí mật.',\n      successMessage:\n        'Mã sao lưu đã được bật. Bạn có thể sử dụng một trong số chúng để đăng nhập vào tài khoản của bạn, nếu bạn mất quyền truy cập vào thiết bị xác thực của bạn. Mỗi mã chỉ có thể được sử dụng một lần.',\n      successSubtitle:\n        'Bạn có thể sử dụng một trong số chúng để đăng nhập vào tài khoản của bạn, nếu bạn mất quyền truy cập vào thiết bị xác thực của bạn.',\n      title: 'Thêm xác minh mã sao lưu',\n      title__codelist: 'Mã sao lưu',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: 'Không có lịch sử thanh toán',\n        notFound: 'Không tìm thấy lịch sử thanh toán',\n        tableHeader__amount: 'Số tiền',\n        tableHeader__date: 'Ngày',\n        tableHeader__status: 'Trạng thái',\n      },\n      paymentSourcesSection: {\n        actionLabel__default: 'Làm mặc định',\n        actionLabel__remove: 'Xóa',\n        add: 'Thêm phương thức thanh toán mới',\n        addSubtitle: 'Thêm một phương thức thanh toán mới vào tài khoản của bạn.',\n        cancelButton: 'Hủy',\n        formButtonPrimary__add: 'Thêm phương thức thanh toán',\n        formButtonPrimary__pay: 'Thanh toán {{amount}}',\n        payWithTestCardButton: 'Thanh toán với thẻ thử',\n        removeResource: {\n          messageLine1: '{{identifier}} sẽ bị xóa khỏi tài khoản này.',\n          messageLine2:\n            'Bạn sẽ không còn thể sử dụng nguồn thanh toán này và bất kỳ đăng ký lặp lại nào phụ thuộc vào nó sẽ không còn hoạt động.',\n          successMessage: '{{paymentSource}} đã bị xóa khỏi tài khoản của bạn.',\n          title: 'Xóa phương thức thanh toán',\n        },\n        title: 'Phương thức thanh toán',\n      },\n      start: {\n        headerTitle__payments: 'Thanh toán',\n        headerTitle__plans: 'Gói',\n        headerTitle__statements: 'Báo cáo',\n        headerTitle__subscriptions: 'Đăng ký',\n      },\n      statementsSection: {\n        empty: 'Không có báo cáo để hiển thị',\n        itemCaption__paidForPlan: 'Thanh toán cho {{plan}} {{period}} gói',\n        itemCaption__proratedCredit: 'Tín dụng phân chia cho sử dụng một phần của đăng ký trước',\n        itemCaption__subscribedAndPaidForPlan: 'Đăng ký và thanh toán cho {{plan}} {{period}} gói',\n        notFound: 'Không tìm thấy báo cáo',\n        tableHeader__amount: 'Số tiền',\n        tableHeader__date: 'Ngày',\n        title: 'Báo cáo',\n        totalPaid: 'Tổng thanh toán',\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: 'Đăng ký gói',\n        actionLabel__switchPlan: 'Chuyển gói',\n        tableHeader__edit: 'Sửa',\n        tableHeader__plan: 'Gói',\n        tableHeader__startDate: 'Ngày bắt đầu',\n        title: 'Đăng ký',\n      },\n      subscriptionsSection: {\n        actionLabel__default: 'Quản lý',\n      },\n      switchPlansSection: {\n        title: 'Chuyển gói',\n      },\n      title: 'Thanh toán',\n    },\n    connectedAccountPage: {\n      formHint: 'Chọn nhà cung cấp để kết nối tài khoản của bạn.',\n      formHint__noAccounts: 'Không có nhà cung cấp tài khoản bên ngoài.',\n      removeResource: {\n        messageLine1: '{{identifier}} sẽ bị xóa khỏi tài khoản này.',\n        messageLine2:\n          'Bạn sẽ không còn thể sử dụng tài khoản kết nối này và bất kỳ tính năng phụ thuộc nào sẽ không còn hoạt động.',\n        successMessage: '{{connectedAccount}} đã bị xóa khỏi tài khoản của bạn.',\n        title: 'Xóa tài khoản kết nối',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}',\n      successMessage: 'Nhà cung cấp đã được thêm vào tài khoản của bạn',\n      title: 'Thêm tài khoản kết nối',\n    },\n    deletePage: {\n      actionDescription: 'Nhập \"Xóa tài khoản\" dưới đây để tiếp tục.',\n      confirm: 'Xóa tài khoản',\n      messageLine1: 'Bạn có chắc chắn muốn xóa tài khoản của bạn?',\n      messageLine2: 'Hành động này là vĩnh viễn và không thể hoàn tác.',\n      title: 'Xóa tài khoản',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Một email chứa mã xác thực sẽ được gửi đến địa chỉ email này.',\n        formSubtitle: 'Nhập mã xác thực được gửi đến {{identifier}}',\n        formTitle: 'Mã xác thực',\n        resendButton: 'Không nhận được mã? Gửi lại',\n        successMessage: 'Email {{identifier}} đã được thêm vào tài khoản của bạn.',\n      },\n      emailLink: {\n        formHint: 'Một email chứa liên kết xác thực sẽ được gửi đến địa chỉ email này.',\n        formSubtitle: 'Nhấp vào liên kết xác thực trong email được gửi đến {{identifier}}',\n        formTitle: 'Liên kết xác thực',\n        resendButton: 'Không nhận được liên kết? Gửi lại',\n        successMessage: 'Email {{identifier}} đã được thêm vào tài khoản của bạn.',\n      },\n      enterpriseSSOLink: {\n        formButton: 'Nhấp để đăng nhập',\n        formSubtitle: 'Hoàn thành đăng nhập với {{identifier}}',\n      },\n      formHint: 'Bạn cần xác thực địa chỉ email này trước khi nó có thể được thêm vào tài khoản của bạn.',\n      removeResource: {\n        messageLine1: '{{identifier}} sẽ bị xóa khỏi tài khoản này.',\n        messageLine2: 'Bạn sẽ không còn thể đăng nhập bằng địa chỉ email này.',\n        successMessage: '{{emailAddress}} đã bị xóa khỏi tài khoản của bạn.',\n        title: 'Xóa địa chỉ email',\n      },\n      title: 'Thêm địa chỉ email',\n      verifyTitle: 'Xác thực địa chỉ email',\n    },\n    formButtonPrimary__add: 'Thêm',\n    formButtonPrimary__continue: 'Tiếp tục',\n    formButtonPrimary__finish: 'Hoàn thành',\n    formButtonPrimary__remove: 'Xóa',\n    formButtonPrimary__save: 'Lưu',\n    formButtonReset: 'Hủy',\n    mfaPage: {\n      formHint: 'Chọn phương thức để thêm.',\n      title: 'Thêm xác thực hai bước',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Sử dụng số điện thoại hiện có',\n      primaryButton__addPhoneNumber: 'Thêm số điện thoại',\n      removeResource: {\n        messageLine1: '{{identifier}} sẽ không còn nhận được mã xác thực khi đăng nhập.',\n        messageLine2: 'Tài khoản của bạn có thể không an toàn hơn. Bạn có chắc chắn muốn tiếp tục?',\n        successMessage: 'Xác thực mã SMS hai bước đã bị xóa cho {{mfaPhoneCode}}',\n        title: 'Xóa xác thực hai bước',\n      },\n      subtitle__availablePhoneNumbers:\n        'Chọn số điện thoại hiện có để đăng ký xác thực mã SMS hai bước hoặc thêm số điện thoại mới.',\n      subtitle__unavailablePhoneNumbers:\n        'Không có số điện thoại nào để đăng ký xác thực mã SMS hai bước, vui lòng thêm số điện thoại mới.',\n      successMessage1: 'Khi đăng nhập, bạn sẽ cần nhập mã xác thực được gửi đến số điện thoại này làm bước thêm.',\n      successMessage2:\n        'Lưu các mã sao lưu này và lưu trữ chúng ở một nơi an toàn. Nếu bạn mất quyền truy cập vào thiết bị xác thực của bạn, bạn có thể sử dụng mã sao lưu để đăng nhập.',\n      successTitle: 'Xác thực mã SMS đã được bật',\n      title: 'Thêm xác thực mã SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Quét mã QR thay vì',\n        buttonUnableToScan__nonPrimary: 'Không thể quét mã QR?',\n        infoText__ableToScan:\n          'Thiết lập phương thức đăng nhập mới trong ứng dụng xác thực của bạn và quét mã QR sau để kết nối với tài khoản của bạn.',\n        infoText__unableToScan:\n          'Thiết lập phương thức đăng nhập mới trong ứng dụng xác thực của bạn và nhập khóa được cung cấp dưới đây.',\n        inputLabel__unableToScan1:\n          'Đảm bảo mã thời gian hoặc mã một lần đã được bật, sau đó hoàn thành việc kết nối tài khoản của bạn.',\n        inputLabel__unableToScan2:\n          'Ngoài ra, nếu ứng dụng xác thực của bạn hỗ trợ TOTP URIs, bạn cũng có thể sao chép URI đầy đủ.',\n      },\n      removeResource: {\n        messageLine1: 'Mã xác thực từ ứng dụng xác thực này sẽ không còn được yêu cầu khi đăng nhập.',\n        messageLine2: 'Tài khoản của bạn có thể không an toàn hơn. Bạn có chắc chắn muốn tiếp tục?',\n        successMessage: 'Xác thực hai bước qua ứng dụng xác thực đã bị xóa.',\n        title: 'Xóa xác thực hai bước',\n      },\n      successMessage:\n        'Xác thực hai bước đã được bật. Khi đăng nhập, bạn sẽ cần nhập mã xác thực từ ứng dụng xác thực này làm bước thêm.',\n      title: 'Thêm ứng dụng xác thực',\n      verifySubtitle: 'Nhập mã xác thực được tạo bởi ứng dụng xác thực của bạn',\n      verifyTitle: 'Mã xác thực',\n    },\n    mobileButton__menu: 'Menu',\n    navbar: {\n      account: 'Tài khoản',\n      apiKeys: 'Khoá API',\n      billing: 'Thanh toán',\n      description: 'Quản lý thông tin tài khoản của bạn.',\n      security: 'Bảo mật',\n      title: 'Tài khoản',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} sẽ bị xóa khỏi tài khoản này.',\n        title: 'Xóa passkey',\n      },\n      subtitle__rename: 'Bạn có thể thay đổi tên passkey để dễ dàng tìm kiếm.',\n      title__rename: 'Đổi tên Passkey',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Được khuyến nghị đăng xuất khỏi tất cả các thiết bị khác có thể đã sử dụng mật khẩu cũ của bạn.',\n      readonly:\n        'Mật khẩu của bạn hiện không thể được chỉnh sửa vì bạn chỉ có thể đăng nhập thông qua kết nối doanh nghiệp.',\n      successMessage__set: 'Mật khẩu của bạn đã được thiết lập.',\n      successMessage__signOutOfOtherSessions: 'Tất cả các thiết bị khác đã được đăng xuất.',\n      successMessage__update: 'Mật khẩu của bạn đã được cập nhật.',\n      title__set: 'Thiết lập mật khẩu',\n      title__update: 'Cập nhật mật khẩu',\n    },\n    phoneNumberPage: {\n      infoText:\n        'Một tin nhắn văn bản chứa mã xác thực sẽ được gửi đến số điện thoại này. Có thể áp dụng tỷ lệ phí tin nhắn và dữ liệu.',\n      removeResource: {\n        messageLine1: '{{identifier}} sẽ bị xóa khỏi tài khoản này.',\n        messageLine2: 'Bạn sẽ không còn thể đăng nhập bằng số điện thoại này.',\n        successMessage: '{{phoneNumber}} đã bị xóa khỏi tài khoản của bạn.',\n        title: 'Xóa số điện thoại',\n      },\n      successMessage: '{{identifier}} đã được thêm vào tài khoản của bạn.',\n      title: 'Thêm số điện thoại',\n      verifySubtitle: 'Nhập mã xác thực được gửi đến {{identifier}}',\n      verifyTitle: 'Xác thực số điện thoại',\n    },\n    plansPage: {\n      title: 'Gói',\n    },\n    profilePage: {\n      fileDropAreaHint: 'Kích thước khuyến nghị 1:1, tối đa 10MB.',\n      imageFormDestructiveActionSubtitle: 'Xóa',\n      imageFormSubtitle: 'Tải lên',\n      imageFormTitle: 'Ảnh hồ sơ',\n      readonly: 'Thông tin hồ sơ của bạn đã được cung cấp thông qua kết nối doanh nghiệp và không thể được chỉnh sửa.',\n      successMessage: 'Hồ sơ của bạn đã được cập nhật.',\n      title: 'Cập nhật hồ sơ',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Đăng xuất khỏi thiết bị',\n        title: 'Thiết bị hoạt động',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Kết nối lại',\n        actionLabel__reauthorize: 'Xác thực ngay',\n        destructiveActionTitle: 'Xóa',\n        primaryButton: 'Kết nối tài khoản',\n        subtitle__disconnected: 'Tài khoản này đã bị ngắt kết nối.',\n        subtitle__reauthorize:\n          'Các phạm vi được yêu cầu đã được cập nhật, và bạn có thể đang gặp phải chức năng giới hạn. Vui lòng xác thực lại ứng dụng này để tránh bất kỳ vấn đề nào',\n        title: 'Tài khoản kết nối',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Xóa tài khoản',\n        title: 'Xóa tài khoản',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Xóa email',\n        detailsAction__nonPrimary: 'Đặt làm chính',\n        detailsAction__primary: 'Hoàn thành xác thực',\n        detailsAction__unverified: 'Xác thực',\n        primaryButton: 'Thêm địa chỉ email',\n        title: 'Địa chỉ email',\n      },\n      enterpriseAccountsSection: {\n        title: 'Tài khoản doanh nghiệp',\n      },\n      headerTitle__account: 'Chi tiết hồ sơ',\n      headerTitle__security: 'Bảo mật',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Tạo lại',\n          headerTitle: 'Mã sao lưu',\n          subtitle__regenerate:\n            'Lấy một bộ mã sao lưu mới và an toàn. Các mã sao lưu trước đó sẽ bị xóa và không thể được sử dụng.',\n          title__regenerate: 'Tạo lại mã sao lưu',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Đặt làm chính',\n          destructiveActionLabel: 'Xóa',\n        },\n        primaryButton: 'Thêm xác thực hai bước',\n        title: 'Xác thực hai bước',\n        totp: {\n          destructiveActionTitle: 'Xóa',\n          headerTitle: 'Ứng dụng xác thực',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Xóa',\n        menuAction__rename: 'Đổi tên',\n        primaryButton: 'Thêm passkey',\n        title: 'Passkeys',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Thiết lập mật khẩu',\n        primaryButton__updatePassword: 'Cập nhật mật khẩu',\n        title: 'Mật khẩu',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Xóa số điện thoại',\n        detailsAction__nonPrimary: 'Đặt làm chính',\n        detailsAction__primary: 'Hoàn thành xác thực',\n        detailsAction__unverified: 'Xác thực số điện thoại',\n        primaryButton: 'Thêm số điện thoại',\n        title: 'Số điện thoại',\n      },\n      profileSection: {\n        primaryButton: 'Cập nhật hồ sơ',\n        title: 'Hồ sơ',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Thiết lập tên người dùng',\n        primaryButton__updateUsername: 'Cập nhật tên người dùng',\n        title: 'Tên người dùng',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Xóa ví',\n        detailsAction__nonPrimary: 'Đặt làm chính',\n        primaryButton: 'Kết nối ví',\n        title: 'Ví Web3',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Tên người dùng của bạn đã được cập nhật.',\n      title__set: 'Thiết lập tên người dùng',\n      title__update: 'Cập nhật tên người dùng',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} sẽ bị xóa khỏi tài khoản này.',\n        messageLine2: 'Bạn sẽ không còn thể đăng nhập bằng ví web3 này.',\n        successMessage: '{{web3Wallet}} đã bị xóa khỏi tài khoản của bạn.',\n        title: 'Xóa ví web3',\n      },\n      subtitle__availableWallets: 'Chọn một ví web3 để kết nối với tài khoản của bạn.',\n      subtitle__unavailableWallets: 'Không có ví web3 nào có sẵn.',\n      successMessage: 'Ví đã được thêm vào tài khoản của bạn.',\n      title: 'Thêm ví web3',\n      web3WalletButtonsBlockButton: '{{provider|titleize}}',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: 'Đăng nhập',\n      actionText: 'Đã có quyền truy cập?',\n      formButton: 'Tham gia danh sách chờ',\n      subtitle: 'Nhập địa chỉ email của bạn và chúng tôi sẽ thông báo khi vị trí của bạn đã sẵn sàng',\n      title: 'Tham gia danh sách chờ',\n    },\n    success: {\n      message: 'Bạn sẽ được chuyển hướng sớm...',\n      subtitle: 'Chúng tôi sẽ liên hệ khi vị trí của bạn đã sẵn sàng',\n      title: 'Cảm ơn bạn đã tham gia danh sách chờ!',\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCACE;AAAA,IACF,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IAEX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BACE;AAAA,IACF,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBACE;AAAA,IACF,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBACE;AAAA,MACF,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBACE;AAAA,EACF,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cACE;AAAA,UACF,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBAAiB;AAAA,IACjB,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BACE;AAAA,IACF,sCAAsC;AAAA,IACtC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBAAoB;AAAA,IACpB,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cACE;AAAA,UACF,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBAAiB;AAAA,MACjB,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UACE;AAAA,MACF,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}