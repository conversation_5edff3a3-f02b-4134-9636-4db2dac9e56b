// src/nb-NO.ts
var nbNO = {
  locale: "nb-NO",
  apiKeys: {
    action__add: void 0,
    action__search: void 0,
    createdAndExpirationStatus__expiresOn: void 0,
    createdAndExpirationStatus__never: void 0,
    detailsTitle__emptyRow: void 0,
    formButtonPrimary__add: void 0,
    formFieldCaption__expiration__expiresOn: void 0,
    formFieldCaption__expiration__never: void 0,
    formFieldOption__expiration__180d: void 0,
    formFieldOption__expiration__1d: void 0,
    formFieldOption__expiration__1y: void 0,
    formFieldOption__expiration__30d: void 0,
    formFieldOption__expiration__60d: void 0,
    formFieldOption__expiration__7d: void 0,
    formFieldOption__expiration__90d: void 0,
    formFieldOption__expiration__never: void 0,
    formHint: void 0,
    formTitle: void 0,
    lastUsed__days: void 0,
    lastUsed__hours: void 0,
    lastUsed__minutes: void 0,
    lastUsed__months: void 0,
    lastUsed__seconds: void 0,
    lastUsed__years: void 0,
    menuAction__revoke: void 0,
    revokeConfirmation: {
      confirmationText: void 0,
      formButtonPrimary__revoke: void 0,
      formHint: void 0,
      formTitle: void 0
    }
  },
  backButton: "Tilbake",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Standard",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Annen imitators enhet",
  badge__primary: "Prim\xE6r",
  badge__renewsAt: void 0,
  badge__requiresAction: "Krever handling",
  badge__startsAt: void 0,
  badge__thisDevice: "Denne enheten",
  badge__unverified: "Ikke verifisert",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Brukerens enhet",
  badge__you: "Du",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      pastDueNotice: void 0,
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    credit: void 0,
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDue: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Opprett organisasjon",
    invitePage: {
      formButtonReset: "Hopp over"
    },
    title: "Opprett organisasjon"
  },
  dates: {
    lastDay: "I g\xE5r kl. {{ date | timeString('nb-NO') }}",
    next6Days: "{{ date | weekday('nb-NO','long') }} kl. {{ date | timeString('nb-NO') }}",
    nextDay: "I morgen kl. {{ date | timeString('nb-NO') }}",
    numeric: "{{ date | numeric('nb-NO') }}",
    previous6Days: "Sist {{ date | weekday('nb-NO','long') }} kl. {{ date | timeString('nb-NO') }}",
    sameDay: "I dag kl. {{ date | timeString('nb-NO') }}"
  },
  dividerText: "eller",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Bruk en annen metode",
  footerPageLink__help: "Hjelp",
  footerPageLink__privacy: "Personvern",
  footerPageLink__terms: "Vilk\xE5r",
  formButtonPrimary: "Fortsett",
  formButtonPrimary__verify: "Verifiser",
  formFieldAction__forgotPassword: "Glemt passord?",
  formFieldError__matchingPasswords: "Passordene stemmer overens.",
  formFieldError__notMatchingPasswords: "Passordene stemmer ikke overens.",
  formFieldError__verificationLinkExpired: "Verifikasjonslenken har utl\xF8pt. Vennligst be om en ny lenke.",
  formFieldHintText__optional: "Valgfritt",
  formFieldHintText__slug: "En slug er en menneskelesbar ID som m\xE5 v\xE6re unik. Den brukes ofte i URL-er.",
  formFieldInputPlaceholder__apiKeyDescription: void 0,
  formFieldInputPlaceholder__apiKeyExpirationDate: void 0,
  formFieldInputPlaceholder__apiKeyName: void 0,
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Slett konto",
  formFieldInputPlaceholder__emailAddress: void 0,
  formFieldInputPlaceholder__emailAddress_username: void 0,
  formFieldInputPlaceholder__emailAddresses: "Skriv inn eller lim inn \xE9n eller flere e-postadresser, separert med mellomrom eller komma",
  formFieldInputPlaceholder__firstName: void 0,
  formFieldInputPlaceholder__lastName: void 0,
  formFieldInputPlaceholder__organizationDomain: void 0,
  formFieldInputPlaceholder__organizationDomainEmailAddress: void 0,
  formFieldInputPlaceholder__organizationName: void 0,
  formFieldInputPlaceholder__organizationSlug: void 0,
  formFieldInputPlaceholder__password: void 0,
  formFieldInputPlaceholder__phoneNumber: void 0,
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__apiKeyDescription: void 0,
  formFieldLabel__apiKeyExpiration: void 0,
  formFieldLabel__apiKeyName: void 0,
  formFieldLabel__automaticInvitations: "Skru p\xE5 automatiske invitasjoner for dette domenet",
  formFieldLabel__backupCode: "Sikkerhetskode",
  formFieldLabel__confirmDeletion: "Bekreftelse",
  formFieldLabel__confirmPassword: "Bekreft passord",
  formFieldLabel__currentPassword: "N\xE5v\xE6rende passord",
  formFieldLabel__emailAddress: "E-postadresse",
  formFieldLabel__emailAddress_username: "E-postadresse eller brukernavn",
  formFieldLabel__emailAddresses: "E-postadresser",
  formFieldLabel__firstName: "Fornavn",
  formFieldLabel__lastName: "Etternavn",
  formFieldLabel__newPassword: "Nytt passord",
  formFieldLabel__organizationDomain: "Domene",
  formFieldLabel__organizationDomainDeletePending: "Slett ventende invitasjoner og forslag",
  formFieldLabel__organizationDomainEmailAddress: "Verifikasjon e-postadresse",
  formFieldLabel__organizationDomainEmailAddressDescription: "Oppgi en e-postadresse under dette domenet for \xE5 motta en kode og verifisere domenet.",
  formFieldLabel__organizationName: "Organisasjonsnavn",
  formFieldLabel__organizationSlug: "Slug URL",
  formFieldLabel__passkeyName: void 0,
  formFieldLabel__password: "Passord",
  formFieldLabel__phoneNumber: "Telefonnummer",
  formFieldLabel__role: "Rolle",
  formFieldLabel__signOutOfOtherSessions: "Logg ut fra alle andre enheter",
  formFieldLabel__username: "Brukernavn",
  impersonationFab: {
    action__signOut: "Logg ut",
    title: "Logget inn som {{identifier}}"
  },
  maintenanceMode: void 0,
  membershipRole__admin: "Administrator",
  membershipRole__basicMember: "Medlem",
  membershipRole__guestMember: "Gjest",
  organizationList: {
    action__createOrganization: "Lag organisasjon",
    action__invitationAccept: "Bli med",
    action__suggestionsAccept: "Sp\xF8r om \xE5 bli med",
    createOrganization: "Lag Organisasjon",
    invitationAcceptedLabel: "Blitt med",
    subtitle: "for \xE5 fortsette til {{applicationName}}",
    suggestionsAcceptedLabel: "Venter p\xE5 godkjenning",
    title: "Velg en bruker",
    titleWithoutPersonal: "Velg en organiasjon"
  },
  organizationProfile: {
    apiKeysPage: {
      title: void 0
    },
    badge__automaticInvitation: "Automatisk invitasjon",
    badge__automaticSuggestion: "Automatisk forslag",
    badge__manualInvitation: "Ingen automatisk registrering",
    badge__unverified: "Uverifisert",
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Legg til domenet som skal verifiseres. Brukere med e-postadresser p\xE5 dette domenet kan automatisk bli med i organisasjonen eller be om \xE5 f\xE5 bli med.",
      title: "Legg til domene"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Invitasjonene kunne ikke sendes. Fiks f\xF8lgende og pr\xF8v igjen:",
      formButtonPrimary__continue: "Send invitasjoner",
      selectDropdown__role: "Velg rolle",
      subtitle: "Inviter nye medlemmer til denne organisasjonen",
      successMessage: "Invitasjoner er sendt",
      title: "Inviter medlemmer"
    },
    membersPage: {
      action__invite: "Inviter",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "Fjern medlem",
        tableHeader__actions: void 0,
        tableHeader__joined: "Ble med",
        tableHeader__role: "Rolle",
        tableHeader__user: "Bruker"
      },
      detailsTitle__emptyRow: "Ingen medlemmer \xE5 vise",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Inviter brukere ved \xE5 koble et e-postdomene til organisasjonen din. Alle som registrerer seg med et matchende e-postdomene vil kunne bli med i organisasjonen n\xE5r som helst.",
          headerTitle: "Automatiske invitasjoner",
          primaryButton: "Administrer verifiserte domener"
        },
        table__emptyRow: "Ingen invitasjoner \xE5 vise"
      },
      invitedMembersTab: {
        menuAction__revoke: "Tilbakekall invitasjon",
        tableHeader__invited: "Invitert"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Brukere som registrerer seg med et matchende e-postdomene, vil kunne se et forslag om \xE5 be om \xE5 bli med i organisasjonen din.",
          headerTitle: "Automatiske forslag",
          primaryButton: "Administrer verifiserte domener"
        },
        menuAction__approve: "Godta",
        menuAction__reject: "Avsl\xE5",
        tableHeader__requested: "Tilgangsforesp\xF8sler",
        table__emptyRow: "Ingen forsesp\xF8rsler \xE5 vise"
      },
      start: {
        headerTitle__invitations: "Invitasjoner",
        headerTitle__members: "Medlemmer",
        headerTitle__requests: "Foresp\xF8rsler"
      }
    },
    navbar: {
      apiKeys: void 0,
      billing: void 0,
      description: "Administrer organisasjonen din.",
      general: "Generelt",
      members: "Medlemmer",
      title: "Organisasjon"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Skriv "{{organizationName}}" under for \xE5 bekrefte.',
          messageLine1: "Er du sikker p\xE5 at du vil slette denne organisasjonen?",
          messageLine2: "Denne handlingen er permanent og kan ikke reverseres.",
          successMessage: "Du har slettet organisasjonen.",
          title: "Slett organisasjonen"
        },
        leaveOrganization: {
          actionDescription: 'Skriv "{{organizationName}}" under for \xE5 fortsette.',
          messageLine1: "Er du sikker p\xE5 at du vil forlate denne organisasjonen? Du vil miste tilgangen til denne organisasjonen og dens applikasjoner.",
          messageLine2: "Denne handlingen er permanent og kan ikke reverseres.",
          successMessage: "Du har forlatt organisasjonen.",
          title: "Forlat organisasjonen"
        },
        title: "Fare"
      },
      domainSection: {
        menuAction__manage: "Administrer",
        menuAction__remove: "Slett",
        menuAction__verify: "Verifiser",
        primaryButton: "Legg til domene",
        subtitle: "Tillat brukere \xE5 bli med i organisasjonen automatisk eller be om \xE5 bli med basert p\xE5 et verifisert e-postdomene.",
        title: "Verifiserte domener"
      },
      successMessage: "Organisasjonen er oppdatert.",
      title: "Organisasjonsprofil"
    },
    removeDomainPage: {
      messageLine1: "E-postdomenet {{domain}} vil bli fjernet.",
      messageLine2: "Brukere vil ikke kunne bli med i organisasjonen automatisk etter dette.",
      successMessage: "{{domain}} har blitt fjernet.",
      title: "Fjern domene"
    },
    start: {
      headerTitle__general: "Generelt",
      headerTitle__members: "Medlemmer",
      profileSection: {
        primaryButton: void 0,
        title: "Organisasjonsprofil",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "\xC5 fjerne dette domenet vil p\xE5virke inviterte brukere.",
        removeDomainActionLabel__remove: "Fjern domene",
        removeDomainSubtitle: "Fjern domenet fra dine verifiserte domener",
        removeDomainTitle: "Fjern domene"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Brukere blir automatisk invitert til \xE5 bli med i organisasjonen n\xE5r de registrerer seg og kan bli med n\xE5r som helst.",
        automaticInvitationOption__label: "Automatiske invitasjoner",
        automaticSuggestionOption__description: "Brukere mottar et forslag om \xE5 be om \xE5 bli med, men m\xE5 godkjennes av en administrator f\xF8r de kan bli med i organisasjonen.",
        automaticSuggestionOption__label: "Automatiske forslag",
        calloutInfoLabel: "\xC5 endre p\xE5meldingsmodus vil kun p\xE5virke nye brukere.",
        calloutInvitationCountLabel: "Ventende invitasjoner sendt til brukere: {{count}}",
        calloutSuggestionCountLabel: "Ventende forslag sendt til brukere: {{count}}",
        manualInvitationOption__description: "Brukere kan kun bli invitert manuelt til organisasjonen.",
        manualInvitationOption__label: "Ingen automatisk registrering",
        subtitle: "Velg hvordan brukere fra dette domenet kan bli med i organisasjonen."
      },
      start: {
        headerTitle__danger: "Fare",
        headerTitle__enrollment: "Registreringsalternativer"
      },
      subtitle: "Domenet {{domain}} har blitt verifisert. Fortsett ved \xE5 velge registreringsmodus.",
      title: "Oppdater {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Skriv inn verifiseringskoden som ble sendt til e-postadressen din",
      formTitle: "Verifiseringskode",
      resendButton: "Ikke mottatt kode? Send p\xE5 nytt",
      subtitle: "Domenet {{domainName}} m\xE5 verifiseres gjennom e-post.",
      subtitleVerificationCodeScreen: "En verifiseringskode har blitt sendt til {{emailAddress}}. Skriv inn koden for \xE5 fortsette.",
      title: "Verifiser domene"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Opprett organisasjon",
    action__invitationAccept: "Bli med",
    action__manageOrganization: "Administrer organisasjon",
    action__suggestionsAccept: "Sp\xF8r om \xE5 bli med",
    notSelected: "Ingen organisasjon valgt",
    personalWorkspace: "Personlig arbeidsomr\xE5de",
    suggestionsAcceptedLabel: "Venter p\xE5 godkjenning"
  },
  paginationButton__next: "Neste",
  paginationButton__previous: "Forrige",
  paginationRowText__displaying: "Viser",
  paginationRowText__of: "av",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Legg til konto",
      action__signOutAll: "Logg ut av alle kontoer",
      subtitle: "Velg kontoen du \xF8nsker \xE5 fortsette med.",
      title: "Velg konto"
    },
    alternativeMethods: {
      actionLink: "F\xE5 hjelp",
      actionText: "Har du ingen av disse?",
      blockButton__backupCode: "Bruk en sikkerhetskopi-kode",
      blockButton__emailCode: "Send e-postkode til {{identifier}}",
      blockButton__emailLink: "Send lenke til {{identifier}}",
      blockButton__passkey: void 0,
      blockButton__password: "Logg inn med passordet ditt",
      blockButton__phoneCode: "Send SMS-kode til {{identifier}}",
      blockButton__totp: "Bruk autentiseringsappen din",
      getHelp: {
        blockButton__emailSupport: "Kontakt kundest\xF8tte via e-post",
        content: "Hvis du har problemer med \xE5 logge inn p\xE5 kontoen din, kan du sende oss en e-post, og vi vil jobbe med deg for \xE5 gjenopprette tilgangen s\xE5 snart som mulig.",
        title: "F\xE5 hjelp"
      },
      subtitle: "Opplever du problemer? Du kan bruke hvilken som helst av disse metodene for \xE5 logge inn.",
      title: "Bruk en annen metode"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "for \xE5 fortsette til {{applicationName}}",
      title: "Skriv inn en sikkerhetskopi-kode"
    },
    emailCode: {
      formTitle: "Verifiseringskode",
      resendButton: "Send kode p\xE5 nytt",
      subtitle: "for \xE5 fortsette til {{applicationName}}",
      title: "Sjekk e-posten din"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      expired: {
        subtitle: "G\xE5 tilbake til den opprinnelige fanen for \xE5 fortsette.",
        title: "Denne verifiseringslenken er utl\xF8pt"
      },
      failed: {
        subtitle: "G\xE5 tilbake til den opprinnelige fanen for \xE5 fortsette.",
        title: "Denne verifiseringslenken er ugyldig"
      },
      formSubtitle: "Bruk verifiseringslenken som er sendt til e-postadressen din",
      formTitle: "Verifiseringslenke",
      loading: {
        subtitle: "Du blir omdirigert snart",
        title: "Logger inn..."
      },
      resendButton: "Send lenke p\xE5 nytt",
      subtitle: "for \xE5 fortsette til {{applicationName}}",
      title: "Sjekk e-posten din",
      unusedTab: {
        title: "Du kan lukke denne fanen"
      },
      verified: {
        subtitle: "Du blir omdirigert snart",
        title: "Innloggingen var vellykket"
      },
      verifiedSwitchTab: {
        subtitle: "G\xE5 tilbake til den opprinnelige fanen for \xE5 fortsette",
        subtitleNewTab: "G\xE5 tilbake til den ny\xE5pnede fanen for \xE5 fortsette",
        titleNewTab: "Logget inn p\xE5 en annen fane"
      }
    },
    forgotPassword: {
      formTitle: "Tilbakestill passord-kode",
      resendButton: "Send kode p\xE5 nytt",
      subtitle: "for \xE5 tilbakestille passordet ditt",
      subtitle_email: "F\xF8rst, skriv inn koden som ble sendt til e-posten din",
      subtitle_phone: "F\xF8rst, skriv inn koden som ble sendt til telefonen din",
      title: "Tilbakestill passord"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Tilbakestill passordet ditt",
      label__alternativeMethods: "Eller logg inn med en annen metode.",
      title: "Glemt passord?"
    },
    noAvailableMethods: {
      message: "Kan ikke fortsette med innloggingen. Det er ingen tilgjengelige autentiseringsfaktorer.",
      subtitle: "En feil oppstod",
      title: "Kan ikke logge inn"
    },
    passkey: {
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "Bruk en annen metode",
      subtitle: "for \xE5 fortsette til {{applicationName}}",
      title: "Skriv inn passordet ditt"
    },
    passwordPwned: {
      title: void 0
    },
    phoneCode: {
      formTitle: "Verifiseringskode",
      resendButton: "Send kode p\xE5 nytt",
      subtitle: "for \xE5 fortsette til {{applicationName}}",
      title: "Sjekk telefonen din"
    },
    phoneCodeMfa: {
      formTitle: "Verifiseringskode",
      resendButton: "Send kode p\xE5 nytt",
      subtitle: void 0,
      title: "Sjekk telefonen din"
    },
    resetPassword: {
      formButtonPrimary: "Tilbakestill passordet",
      requiredMessage: "En konto eksisterer allerede med en uverifisert e-postadresse. Vennligst tilbakestill passordet ditt av sikkerhetshensyn.",
      successMessage: "Passordet ditt er blitt tilbakestilt. Logger deg inn, vennligst vent et \xF8yeblikk.",
      title: "Tilbakestill passordet"
    },
    resetPasswordMfa: {
      detailsLabel: "Vi m\xE5 bekrefte identiteten din f\xF8r vi tilbakestiller passordet ditt."
    },
    start: {
      actionLink: "Opprett konto",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "Bruk e-post",
      actionLink__use_email_username: "Bruk e-post eller brukernavn",
      actionLink__use_passkey: void 0,
      actionLink__use_phone: "Bruk telefon",
      actionLink__use_username: "Bruk brukernavn",
      actionText: "Ingen konto?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "for \xE5 fortsette til {{applicationName}}",
      subtitleCombined: void 0,
      title: "Logg inn",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Verifiseringskode",
      subtitle: void 0,
      title: "To-trinns verifisering"
    }
  },
  signInEnterPasswordTitle: "Skriv inn passordet ditt",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Logg inn",
      actionText: "Har du allerede en konto?",
      subtitle: "for \xE5 fortsette til {{applicationName}}",
      title: "Fyll ut manglende felt"
    },
    emailCode: {
      formSubtitle: "Skriv inn verifiseringskoden som er sendt til e-postadressen din",
      formTitle: "Verifiseringskode",
      resendButton: "Send kode p\xE5 nytt",
      subtitle: "for \xE5 fortsette til {{applicationName}}",
      title: "Verifiser e-posten din"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      formSubtitle: "Bruk verifiseringslenken som er sendt til e-postadressen din",
      formTitle: "Verifiseringslenke",
      loading: {
        title: "Registrerer deg..."
      },
      resendButton: "Send lenke p\xE5 nytt",
      subtitle: "for \xE5 fortsette til {{applicationName}}",
      title: "Verifiser e-posten din",
      verified: {
        title: "Registreringen var vellykket"
      },
      verifiedSwitchTab: {
        subtitle: "G\xE5 tilbake til den nylig \xE5pnede fanen for \xE5 fortsette",
        subtitleNewTab: "G\xE5 tilbake til forrige fane for \xE5 fortsette",
        title: "E-posten ble verifisert"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: void 0,
        label__onlyTermsOfService: void 0,
        label__termsOfServiceAndPrivacyPolicy: void 0
      },
      continue: {
        subtitle: void 0,
        title: void 0
      }
    },
    phoneCode: {
      formSubtitle: "Skriv inn verifiseringskoden som er sendt til telefonnummeret ditt",
      formTitle: "Verifiseringskode",
      resendButton: "Send kode p\xE5 nytt",
      subtitle: "for \xE5 fortsette til {{applicationName}}",
      title: "Verifiser telefonen din"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "Logg inn",
      actionLink__use_email: void 0,
      actionLink__use_phone: void 0,
      actionText: "Har du allerede en konto?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "for \xE5 fortsette til {{applicationName}}",
      subtitleCombined: "for \xE5 fortsette til {{applicationName}}",
      title: "Opprett kontoen din",
      titleCombined: "Opprett kontoen din"
    }
  },
  socialButtonsBlockButton: "Fortsett med {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "Registreringen mislyktes p\xE5 grunn av mislykkede sikkerhetsvalideringer. Vennligst oppdater siden og pr\xF8v igjen, eller ta kontakt med brukerst\xF8tte for mer hjelp.",
    captcha_unavailable: "Registreringen mislyktes p\xE5 grunn av mislykkede bot-valideringer. Vennligst oppdater siden og pr\xF8v igjen, eller ta kontakt med brukerst\xF8tte for mer hjelp.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: void 0,
    form_identifier_exists__phone_number: void 0,
    form_identifier_exists__username: void 0,
    form_identifier_not_found: "Vi klarte ikke finne en konto med disse detaljene.",
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: "E-postadressen m\xE5 v\xE6re en gyldig e-postadresse",
    form_param_format_invalid__phone_number: "Telefonnummeret m\xE5 v\xE6re i et gyldig internasjonalt format",
    form_param_max_length_exceeded__first_name: "Fornavn kan ikke v\xE6re lengre enn 256 bokstaver.",
    form_param_max_length_exceeded__last_name: "Etternavn kan ikke v\xE6re lengre enn 256 bokstaver.",
    form_param_max_length_exceeded__name: "Navn kan ikke v\xE6re lengre enn 256 bokstaver.",
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: void 0,
    form_password_not_strong_enough: "Passordet ditt er ikke sterkt nok.",
    form_password_pwned: "Dette passordet er funnet som en del av et datainnbrudd og kan ikke brukes. Vennligst pr\xF8v et annet passord.",
    form_password_pwned__sign_in: void 0,
    form_password_size_in_bytes_exceeded: "Passordet ditt har overskredet maksimalt antall byte tillatt. Vennligst forkort det eller fjern noen spesialtegn.",
    form_password_validation_failed: "Feil passord",
    form_username_invalid_character: void 0,
    form_username_invalid_length: void 0,
    identification_deletion_failed: "You cannot delete your last identification.",
    not_allowed_access: "E-postadressen eller telefonnummeret ditt er ikke tillatt for registrering. Dette kan v\xE6re p\xE5 grunn av bruk av '+', '=', '#' eller '.' i e-postadressen din, bruk av et domenn som er tilknyttet en midlertidig e-posttjeneste, eller eksplisitt blokkering. Hvis du mener dette er en feil, vennligst kontakt st\xF8tte.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: void 0,
    passkey_not_supported: void 0,
    passkey_pa_not_supported: void 0,
    passkey_registration_cancelled: void 0,
    passkey_retrieval_cancelled: void 0,
    passwordComplexity: {
      maximumLength: "mindre enn {{length}} tegn",
      minimumLength: "{{length}} eller flere tegn",
      requireLowercase: "en liten bokstav",
      requireNumbers: "et tall",
      requireSpecialCharacter: "et spesialtegn",
      requireUppercase: "en stor bokstav",
      sentencePrefix: "Passordet ditt m\xE5 inneholde"
    },
    phone_number_exists: "Dette telefonnummeret er allerede i bruk. Vennligst bruk et annet telefonnummer.",
    session_exists: "Du er allerede logget inn.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "Passordet ditt fungerer, men det kan v\xE6re sterkere. Pr\xF8v \xE5 legge til flere tegn.",
      goodPassword: "Godt jobbet. Dette er et utmerket passord.",
      notEnough: "Passordet ditt er ikke sterkt nok.",
      suggestions: {
        allUppercase: "Stor bokstav p\xE5 noen, men ikke alle bokstaver.",
        anotherWord: "Legg til flere ord som er mindre vanlige.",
        associatedYears: "Unng\xE5 \xE5r som er knyttet til deg.",
        capitalization: "Sett stor bokstav p\xE5 mer enn den f\xF8rste bokstaven.",
        dates: "Unng\xE5 datoer og \xE5r som er knyttet til deg.",
        l33t: "Unng\xE5 forutsigbare bokstavbytter som '@' for 'a'.",
        longerKeyboardPattern: "Bruk lengre tastaturm\xF8nstre og endre skrivretning flere ganger.",
        noNeed: "Du kan lage sterke passord uten \xE5 bruke symboler, tall eller store bokstaver.",
        pwned: "Hvis du bruker dette passordet andre steder, b\xF8r du endre det.",
        recentYears: "Unng\xE5 nylige \xE5r.",
        repeated: "Unng\xE5 gjentatte ord og tegn.",
        reverseWords: "Unng\xE5 omvendte stavelser av vanlige ord.",
        sequences: "Unng\xE5 vanlige tegnsekvenser.",
        useWords: "Bruk flere ord, men unng\xE5 vanlige fraser."
      },
      warnings: {
        common: "Dette er et vanlig brukt passord.",
        commonNames: "Vanlige navn og etternavn er lett \xE5 gjette.",
        dates: "Datoer er lett \xE5 gjette.",
        extendedRepeat: 'Gjentatte tegnm\xF8nstre som "abcabcabc" er lett \xE5 gjette.',
        keyPattern: "Korte tastaturm\xF8nstre er lett \xE5 gjette.",
        namesByThemselves: "Enkelt navn eller etternavn er lett \xE5 gjette.",
        pwned: "Passordet ditt ble eksponert i et datainnbrudd p\xE5 internett.",
        recentYears: "Nylige \xE5r er lett \xE5 gjette.",
        sequences: 'Vanlige tegnsekvenser som "abc" er lett \xE5 gjette.',
        similarToCommon: "Dette ligner p\xE5 et vanlig brukt passord.",
        simpleRepeat: 'Gjentatte tegn som "aaa" er lett \xE5 gjette.',
        straightRow: "Rette rader med tastene p\xE5 tastaturet ditt er lett \xE5 gjette.",
        topHundred: "Dette er et ofte brukt passord.",
        topTen: "Dette er et mye brukt passord.",
        userInputs: "Det b\xF8r ikke v\xE6re personlige eller sidetilknyttede data.",
        wordByItself: "Enkeltord er lett \xE5 gjette."
      }
    }
  },
  userButton: {
    action__addAccount: "Legg til konto",
    action__manageAccount: "Administrer konto",
    action__signOut: "Logg ut",
    action__signOutAll: "Logg ut av alle kontoer"
  },
  userProfile: {
    apiKeysPage: {
      title: void 0
    },
    backupCodePage: {
      actionLabel__copied: "Kopiert!",
      actionLabel__copy: "Kopier alle",
      actionLabel__download: "Last ned .txt",
      actionLabel__print: "Skriv ut",
      infoText1: "Sikkerhetskoder vil bli aktivert for denne kontoen.",
      infoText2: "Hold sikkerhetskodene hemmelige og oppbevar dem sikkert. Du kan generere nye sikkerhetskoder hvis du mistenker at de er kompromittert.",
      subtitle__codelist: "Oppbevar dem sikkert og hold dem hemmelige.",
      successMessage: "Sikkerhetskoder er n\xE5 aktivert. Du kan bruke en av disse til \xE5 logge inn p\xE5 kontoen din hvis du mister tilgangen til autentiseringsenheten. Hver kode kan bare brukes \xE9n gang.",
      successSubtitle: "Du kan bruke en av disse til \xE5 logge inn p\xE5 kontoen din hvis du mister tilgangen til autentiseringsenheten.",
      title: "Legg til sikkerhetskopieringskodeverifisering",
      title__codelist: "Sikkerhetskoder"
    },
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Velg en tilbyder for \xE5 koble til kontoen din.",
      formHint__noAccounts: "Det er ingen tilgjengelige eksterne konto-tilbydere.",
      removeResource: {
        messageLine1: "{{identifier}} vil bli fjernet fra denne kontoen.",
        messageLine2: "Du vil ikke lenger kunne bruke denne tilknyttede kontoen, og eventuelle avhengige funksjoner vil ikke lenger fungere.",
        successMessage: "{{connectedAccount}} har blitt fjernet fra kontoen din.",
        title: "Fjern tilknyttet konto"
      },
      socialButtonsBlockButton: "Koble til {{provider|titleize}}-konto",
      successMessage: "Tilbyderen har blitt lagt til kontoen din.",
      title: "Legg til tilknyttet konto"
    },
    deletePage: {
      actionDescription: 'Skriv inn "Slett konto" under for \xE5 fortsette.',
      confirm: "Slett konto",
      messageLine1: "Er du sikker p\xE5 at du vil slette kontoen din?",
      messageLine2: "Denne handlingen er permanent og kan ikke reverseres.",
      title: "Slett konto"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "En e-post med en verifiseringskode vil bli sendt til denne e-postadressen.",
        formSubtitle: "Skriv inn verifiseringskoden som er sendt til {{identifier}}",
        formTitle: "Verifiseringskode",
        resendButton: "Send kode p\xE5 nytt",
        successMessage: "E-posten {{identifier}} har blitt lagt til kontoen din."
      },
      emailLink: {
        formHint: "En e-post med en verifiseringslenke vil bli sendt til denne e-postadressen.",
        formSubtitle: "Klikk p\xE5 verifiseringslenken i e-posten sendt til {{identifier}}",
        formTitle: "Verifiseringslenke",
        resendButton: "Send lenke p\xE5 nytt",
        successMessage: "E-posten {{identifier}} har blitt lagt til kontoen din."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} vil bli fjernet fra denne kontoen.",
        messageLine2: "Du vil ikke lenger kunne logge inn med denne e-postadressen.",
        successMessage: "{{emailAddress}} har blitt fjernet fra kontoen din.",
        title: "Fjern e-postadresse"
      },
      title: "Legg til e-postadresse",
      verifyTitle: "Verifiser e-postadresse"
    },
    formButtonPrimary__add: "Legg til",
    formButtonPrimary__continue: "Fortsett",
    formButtonPrimary__finish: "Fullf\xF8r",
    formButtonPrimary__remove: "Fjern",
    formButtonPrimary__save: "Lagre",
    formButtonReset: "Avbryt",
    mfaPage: {
      formHint: "Velg en metode for \xE5 legge til.",
      title: "Legg til to-trinns verifisering"
    },
    mfaPhoneCodePage: {
      backButton: "Bruk eksisterende nummer",
      primaryButton__addPhoneNumber: "Legg til et telefonnummer",
      removeResource: {
        messageLine1: "{{identifier}} vil ikke lenger motta verifiseringskoder ved p\xE5logging.",
        messageLine2: "Kontoen din kan bli mindre sikker. Er du sikker p\xE5 at du vil fortsette?",
        successMessage: "SMS-kode to-trinns verifisering er fjernet for {{mfaPhoneCode}}",
        title: "Fjern to-trinns verifisering"
      },
      subtitle__availablePhoneNumbers: "Velg et telefonnummer for \xE5 registrere deg for SMS-kode to-trinns verifisering.",
      subtitle__unavailablePhoneNumbers: "Det er ingen tilgjengelige telefonnummer \xE5 registrere seg for SMS-kode to-trinns verifisering.",
      successMessage1: "Ved innlogging vil du m\xE5tte skrive inn en verifiseringskode sendt til dette telfonnummeret som et tilleggssteg.",
      successMessage2: "Ta vare p\xE5 disse reservekodene og oppbevar dem p\xE5 et trygt sted. Hvis du mister tilgang til autentiseringsenheten din kan du bruke reservekodene for \xE5 logge inn.",
      successTitle: "SMS-kodeverifisering lagt til",
      title: "Legg til SMS-kodeverifisering"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Skan QR-kode i stedet",
        buttonUnableToScan__nonPrimary: "Kan ikke skanne QR-kode?",
        infoText__ableToScan: "Sett opp en ny innloggingsmetode i autentiseringsappen din og skann f\xF8lgende QR-kode for \xE5 koble den til kontoen din.",
        infoText__unableToScan: "Sett opp en ny innloggingsmetode i autentiseringsappen og skriv inn n\xF8kkelen som er oppgitt nedenfor.",
        inputLabel__unableToScan1: "S\xF8rg for at tidsbaserte eller engangspassord er aktivert, og fullf\xF8r deretter koblingen av kontoen din.",
        inputLabel__unableToScan2: "Alternativt, hvis autentiseringsappen din st\xF8tter TOTP URI-er, kan du ogs\xE5 kopiere hele URI-en."
      },
      removeResource: {
        messageLine1: "Verifiseringskoder fra denne autentiseringsappen vil ikke lenger v\xE6re p\xE5krevd ved p\xE5logging.",
        messageLine2: "Kontoen din kan bli mindre sikker. Er du sikker p\xE5 at du vil fortsette?",
        successMessage: "To-trinns verifisering via autentiseringsappen er fjernet.",
        title: "Fjern to-trinns verifisering"
      },
      successMessage: "To-trinns verifisering er n\xE5 aktivert. N\xE5r du logger inn, m\xE5 du angi en verifiseringskode fra denne autentiseringsappen som et ekstra trinn.",
      title: "Legg til autentiseringsapp",
      verifySubtitle: "Skriv inn verifiseringskoden generert av autentiseringsappen din",
      verifyTitle: "Verifiseringskode"
    },
    mobileButton__menu: "Meny",
    navbar: {
      account: "Profil",
      apiKeys: void 0,
      billing: void 0,
      description: "Administrer kontoinformasjonen din.",
      security: "Sikkerhet",
      title: "Konto"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: void 0,
        title: void 0
      },
      subtitle__rename: void 0,
      title__rename: void 0
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Det er anbefalt \xE5 logge ut av alle de andre enhetene dine som kan ha brukt ditt gamle passord.",
      readonly: "Passordet ditt kan for \xF8yeblikket ikke endres fordi du kun kan logge inn via bedriftstilkoblingen.",
      successMessage__set: "Passordet ditt er satt.",
      successMessage__signOutOfOtherSessions: "Alle andre enheter har blitt logget ut.",
      successMessage__update: "Passordet ditt har blitt oppdatert.",
      title__set: "Sett passord",
      title__update: "Endre passord"
    },
    phoneNumberPage: {
      infoText: "En tekstmelding med en verifiseringslenke vil bli sendt til dette telefonnummeret.",
      removeResource: {
        messageLine1: "{{identifier}} vil bli fjernet fra denne kontoen.",
        messageLine2: "Du vil ikke lenger kunne logge inn med dette telefonnummeret.",
        successMessage: "{{phoneNumber}} har blitt fjernet fra kontoen din.",
        title: "Fjern telefonnummer"
      },
      successMessage: "{{identifier}} har blitt lagt til kontoen din.",
      title: "Legg til telefonnummer",
      verifySubtitle: "Skriv inn verifiseringskoden som ble sendt til {{identifier}}",
      verifyTitle: "Verifiser telefonnummer"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "Last opp et JPG, PNG, GIF eller WEBP-bilde som er mindre enn 10 MB",
      imageFormDestructiveActionSubtitle: "Fjern bilde",
      imageFormSubtitle: "Last opp bilde",
      imageFormTitle: "Profilbilde",
      readonly: "Informasjonen om profilen din er levert av bedriftstilkoblingen og kan ikke redigeres.",
      successMessage: "Profilen din har blitt oppdatert.",
      title: "Oppdater profil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Logg ut fra enhet",
        title: "Aktive enheter"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Pr\xF8v p\xE5 nytt",
        actionLabel__reauthorize: "Autoriser n\xE5",
        destructiveActionTitle: "Fjern",
        primaryButton: "Koble til konto",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues",
        title: "Tilkoblede kontoer"
      },
      dangerSection: {
        deleteAccountButton: "Slett konto",
        title: "Fare"
      },
      emailAddressesSection: {
        destructiveAction: "Fjern e-postadresse",
        detailsAction__nonPrimary: "Angi som prim\xE6r",
        detailsAction__primary: "Fullf\xF8r verifisering",
        detailsAction__unverified: "Fullf\xF8r verifisering",
        primaryButton: "Legg til en e-postadresse",
        title: "E-postadresser"
      },
      enterpriseAccountsSection: {
        title: "Bedriftskontoer"
      },
      headerTitle__account: "Konto",
      headerTitle__security: "Sikkerhet",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Generer koder p\xE5 nytt",
          headerTitle: "Sikkerhetskoder",
          subtitle__regenerate: "F\xE5 en ny serie med sikre sikkerhetskoder. Tidligere sikkerhetskoder vil bli slettet og kan ikke brukes.",
          title__regenerate: "Generer nye sikkerhetskoder"
        },
        phoneCode: {
          actionLabel__setDefault: "Angi som standard",
          destructiveActionLabel: "Fjern telefonnummer"
        },
        primaryButton: "Legg til to-trinns verifisering",
        title: "To-trinns verifisering",
        totp: {
          destructiveActionTitle: "Fjern",
          headerTitle: "Autentiseringsapplikasjon"
        }
      },
      passkeysSection: {
        menuAction__destructive: void 0,
        menuAction__rename: void 0,
        primaryButton: void 0,
        title: void 0
      },
      passwordSection: {
        primaryButton__setPassword: "Opprett passord",
        primaryButton__updatePassword: "Endre passord",
        title: "Passord"
      },
      phoneNumbersSection: {
        destructiveAction: "Fjern telefonnummer",
        detailsAction__nonPrimary: "Angi som prim\xE6r",
        detailsAction__primary: "Fullf\xF8r verifisering",
        detailsAction__unverified: "Fullf\xF8r verifisering",
        primaryButton: "Legg til et telefonnummer",
        title: "Telefonnumre"
      },
      profileSection: {
        primaryButton: void 0,
        title: "Profil"
      },
      usernameSection: {
        primaryButton__setUsername: "Angi brukernavn",
        primaryButton__updateUsername: "Endre brukernavn",
        title: "Brukernavn"
      },
      web3WalletsSection: {
        destructiveAction: "Fjern lommebok",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3-lommeb\xF8ker",
        title: "Web3-lommeb\xF8ker"
      }
    },
    usernamePage: {
      successMessage: "Brukernavnet ditt har blitt oppdatert.",
      title__set: "Oppdater brukernavn",
      title__update: "Oppdater brukernavn"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} vil bli fjernet fra denne kontoen.",
        messageLine2: "Du vil ikke lenger kunne logge inn med denne web3-lommeboken.",
        successMessage: "{{web3Wallet}} har blitt fjernet fra kontoen din.",
        title: "Fjern web3-lommebok"
      },
      subtitle__availableWallets: "Velg en web3-lommebok for \xE5 koble til kontoen din.",
      subtitle__unavailableWallets: "Det er ingen tilgjengelige web3-lommeb\xF8ker.",
      successMessage: "Lommeboken har blitt lagt til kontoen din.",
      title: "Legg til web3-lommebok",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  nbNO
};
//# sourceMappingURL=nb-NO.mjs.map