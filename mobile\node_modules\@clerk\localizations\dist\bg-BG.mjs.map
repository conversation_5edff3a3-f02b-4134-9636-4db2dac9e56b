{"version": 3, "sources": ["../src/bg-BG.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const bgBG: LocalizationResource = {\n  locale: 'bg-BG',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Назад',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'По подразбиране',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Друго устройство за имитация',\n  badge__primary: 'Основен',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Изисква действие',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Това устройство',\n  badge__unverified: 'Непотвърден',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Потребителско устройство',\n  badge__you: 'Вие',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Създаване на организация',\n    invitePage: {\n      formButtonReset: 'Пропусни',\n    },\n    title: 'Създаване на организация',\n  },\n  dates: {\n    lastDay: \"Вчера в {{ date | timeString('bg-BG') }}\",\n    next6Days: \"{{ date | weekday('bg-BG','long') }} в {{ date | timeString('bg-BG') }}\",\n    nextDay: \"Утре в {{ date | timeString('bg-BG') }}\",\n    numeric: \"{{ date | numeric('bg-BG') }}\",\n    previous6Days: \"Последно в {{ date | weekday('bg-BG','long') }} в {{ date | timeString('bg-BG') }}\",\n    sameDay: \"Днес в {{ date | timeString('bg-BG') }}\",\n  },\n  dividerText: 'или',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Използвайте друг метод',\n  footerPageLink__help: 'Помощ',\n  footerPageLink__privacy: 'Поверителност',\n  footerPageLink__terms: 'Условия',\n  formButtonPrimary: 'Продължи',\n  formButtonPrimary__verify: 'Потвърди',\n  formFieldAction__forgotPassword: 'Забравена парола?',\n  formFieldError__matchingPasswords: 'Паролите съвпадат.',\n  formFieldError__notMatchingPasswords: 'Паролите не съвпадат.',\n  formFieldError__verificationLinkExpired: 'Линкът за потвърждение е изтекъл. Моля, заявете нов линк.',\n  formFieldHintText__optional: 'По избор',\n  formFieldHintText__slug: 'Slug е четим идентификатор, който трябва да бъде уникален. Често се използва в URL адреси.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: 'Въведете резервен код',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Изтрий акаунта',\n  formFieldInputPlaceholder__emailAddress: '<EMAIL>',\n  formFieldInputPlaceholder__emailAddress_username: '<EMAIL> или име_потребител',\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: 'Вашето фамилно име',\n  formFieldInputPlaceholder__organizationDomain: 'example.com',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: '<EMAIL>',\n  formFieldInputPlaceholder__organizationName: 'Име на организация',\n  formFieldInputPlaceholder__organizationSlug: 'slug-организация',\n  formFieldInputPlaceholder__password: 'Парола',\n  formFieldInputPlaceholder__phoneNumber: '+359 123 456 789',\n  formFieldInputPlaceholder__username: 'Име на потребител',\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Включи автоматични покани за този домейн',\n  formFieldLabel__backupCode: 'Резервен код',\n  formFieldLabel__confirmDeletion: 'Потвърждение',\n  formFieldLabel__confirmPassword: 'Потвърдете паролата',\n  formFieldLabel__currentPassword: 'Текуща парола',\n  formFieldLabel__emailAddress: 'Имейл адрес',\n  formFieldLabel__emailAddress_username: 'Имейл адрес или потребителско име',\n  formFieldLabel__emailAddresses: 'Имейл адреси',\n  formFieldLabel__firstName: 'Име',\n  formFieldLabel__lastName: 'Фамилия',\n  formFieldLabel__newPassword: 'Нова парола',\n  formFieldLabel__organizationDomain: 'Домейн',\n  formFieldLabel__organizationDomainDeletePending: 'Изтриване на изчакващи покани и предложения',\n  formFieldLabel__organizationDomainEmailAddress: 'Имейл адрес за потвърждение',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Въведете имейл адрес под този домейн, за да получите код и да потвърдите домейна.',\n  formFieldLabel__organizationName: 'Име',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: undefined,\n  formFieldLabel__password: 'Парола',\n  formFieldLabel__phoneNumber: 'Телефонен номер',\n  formFieldLabel__role: 'Роля',\n  formFieldLabel__signOutOfOtherSessions: 'Изход от всички други устройства',\n  formFieldLabel__username: 'Потребителско име',\n  impersonationFab: {\n    action__signOut: 'Изход',\n    title: 'Влезли сте като {{identifier}}',\n  },\n  maintenanceMode: 'Режим на поддръжка',\n  membershipRole__admin: 'Админ',\n  membershipRole__basicMember: 'Член',\n  membershipRole__guestMember: 'Гост',\n  organizationList: {\n    action__createOrganization: 'Създаване на организация',\n    action__invitationAccept: 'Присъединяване',\n    action__suggestionsAccept: 'Заявка за присъединяване',\n    createOrganization: 'Създаване на организация',\n    invitationAcceptedLabel: 'Присъединен',\n    subtitle: 'за продължаване към {{applicationName}}',\n    suggestionsAcceptedLabel: 'Чакащо одобрение',\n    title: 'Изберете акаунт',\n    titleWithoutPersonal: 'Изберете организация',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Автоматични покани',\n    badge__automaticSuggestion: 'Автоматични предложения',\n    badge__manualInvitation: 'Няма автоматично включване',\n    badge__unverified: 'Неверифициран',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Добавете домейн за верификация. Потребителите с имейл адреси на този домейн могат да се присъединят към организацията автоматично или да заявят да се присъединят.',\n      title: 'Добавяне на домейн',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Поканите не могат да бъдат изпратени. Вече има чакащи покани за следните имейл адреси: {{email_addresses}}.',\n      formButtonPrimary__continue: 'Изпращане на покани',\n      selectDropdown__role: 'Select role',\n      subtitle: 'Въведете или поставете един или повече имейл адреси, разделени с интервали или запетая.',\n      successMessage: 'Поканите бяха успешно изпратени',\n      title: 'Покана за нови членове',\n    },\n    membersPage: {\n      action__invite: 'Покани',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Премахване на член',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'Присъединил се',\n        tableHeader__role: 'Роля',\n        tableHeader__user: 'Потребител',\n      },\n      detailsTitle__emptyRow: 'Няма членове за показване',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Поканете потребители, свързващи имейл домейн с вашата организация. Всеки, който се регистрира със съответния имейл домейн, ще може да се присъедини към организацията по всяко време.',\n          headerTitle: 'Автоматични покани',\n          primaryButton: 'Управление на верифицираните домейни',\n        },\n        table__emptyRow: 'Няма покани за показване',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Отмяна на поканата',\n        tableHeader__invited: 'Поканени',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Потребители, които се регистрират със съответния имейл домейн, ще видят предложение за заявка за присъединяване към вашата организация.',\n          headerTitle: 'Автоматични предложения',\n          primaryButton: 'Управление на верифицираните домейни',\n        },\n        menuAction__approve: 'Одобрение',\n        menuAction__reject: 'Отхвърляне',\n        tableHeader__requested: 'Заявен достъп',\n        table__emptyRow: 'Няма заявки за показване',\n      },\n      start: {\n        headerTitle__invitations: 'Покани',\n        headerTitle__members: 'Членове',\n        headerTitle__requests: 'Заявки',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Управление на вашата организация.',\n      general: 'Общи',\n      members: 'Членове',\n      title: 'Организация',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Въведете \"{{organizationName}}\" по-долу, за да продължите.',\n          messageLine1: 'Сигурни ли сте, че искате да изтриете тази организация?',\n          messageLine2: 'Това действие е перманентно и необратимо.',\n          successMessage: 'Изтрихте организацията.',\n          title: 'Изтриване на организацията',\n        },\n        leaveOrganization: {\n          actionDescription: 'Въведете \"{{organizationName}}\" по-долу, за да продължите.',\n          messageLine1:\n            'Сигурни ли сте, че искате да напуснете тази организация? Ще загубите достъп до тази организация и нейните приложения.',\n          messageLine2: 'Това действие е перманентно и необратимо.',\n          successMessage: 'Излязохте от организацията.',\n          title: 'Напускане на организацията',\n        },\n        title: 'Опасност',\n      },\n      domainSection: {\n        menuAction__manage: 'Manage',\n        menuAction__remove: 'Delete',\n        menuAction__verify: 'Verify',\n        primaryButton: 'Добавяне на домейн',\n        subtitle:\n          'Позволява на потребителите да се присъединяват към организацията автоматично или да заявят да се присъединят на база верифициран имейл домейн.',\n        title: 'Верифицирани домейни',\n      },\n      successMessage: 'Организацията е актуализирана.',\n      title: 'Актуализиране на профила',\n    },\n    removeDomainPage: {\n      messageLine1: 'Имейл домейнът {{domain}} ще бъде премахнат.',\n      messageLine2: 'Потребителите няма да могат да се присъединяват автоматично към организацията след това.',\n      successMessage: '{{domain}} беше премахнат.',\n      title: 'Премахване на домейн',\n    },\n    start: {\n      headerTitle__general: 'General',\n      headerTitle__members: 'Членове',\n      profileSection: {\n        primaryButton: undefined,\n        title: 'Organization Profile',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Премахването на този домейн ще засегне поканени потребители.',\n        removeDomainActionLabel__remove: 'Премахване на домейн',\n        removeDomainSubtitle: 'Премахнете този домейн от верифицираните си домейни',\n        removeDomainTitle: 'Премахване на домейн',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Потребителите се поканват автоматично да се присъединят към организацията, когато се регистрират и могат да се присъединят по всяко време.',\n        automaticInvitationOption__label: 'Автоматични покани',\n        automaticSuggestionOption__description:\n          'Потребителите получават предложение да заявят да се присъединят, но трябва да бъдат одобрени от администратор, преди да могат да се присъединят към организацията.',\n        automaticSuggestionOption__label: 'Автоматични предложения',\n        calloutInfoLabel: 'Промяната на режима на записване ще засегне само новите потребители.',\n        calloutInvitationCountLabel: 'Чакащи покани, изпратени на потребители: {{count}}',\n        calloutSuggestionCountLabel: 'Чакащи предложения, изпратени на потребители: {{count}}',\n        manualInvitationOption__description: 'Потребителите могат да бъдат поканени само ръчно в организацията.',\n        manualInvitationOption__label: 'Без автоматично записване',\n        subtitle: 'Изберете как потребителите от този домейн могат да се присъединят към организацията.',\n      },\n      start: {\n        headerTitle__danger: 'Опасност',\n        headerTitle__enrollment: 'Опции за записване',\n      },\n      subtitle: 'Домейнът {{domain}} вече е верифициран. Продължете, като изберете режим на записване.',\n      title: 'Актуализиране на {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Въведете кода за верификация, изпратен на вашия имейл адрес',\n      formTitle: 'Код за верификация',\n      resendButton: 'Не сте получили код? Изпрати отново',\n      subtitle: 'Домейнът {{domainName}} трябва да бъде верифициран чрез имейл.',\n      subtitleVerificationCodeScreen:\n        'Беше изпратен код за верификация на {{emailAddress}}. Въведете кода, за да продължите.',\n      title: 'Верификация на домейн',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Създаване на организация',\n    action__invitationAccept: 'Присъединяване',\n    action__manageOrganization: 'Управление',\n    action__suggestionsAccept: 'Заявка за присъединяване',\n    notSelected: 'Няма избрана организация',\n    personalWorkspace: 'Личен акаунт',\n    suggestionsAcceptedLabel: 'Чакащо одобрение',\n  },\n  paginationButton__next: 'Следващ',\n  paginationButton__previous: 'Предишен',\n  paginationRowText__displaying: 'Показване на',\n  paginationRowText__of: 'от',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Get help',\n      actionText: \"Don't have one of these?\",\n      blockButton__backupCode: 'Use backup code',\n      blockButton__emailCode: 'Send code to email',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Login with password',\n      blockButton__phoneCode: 'Send code to phone',\n      blockButton__totp: 'Use authenticator app',\n      getHelp: {\n        blockButton__emailSupport: 'Contact support via email',\n        content: 'If you need assistance, please contact support.',\n        title: 'Need help?',\n      },\n      subtitle: 'Choose an alternative method to verify your identity.',\n      title: 'Alternative methods',\n    },\n    backupCodeMfa: {\n      subtitle: 'Enter the backup code sent to your email.',\n      title: 'Backup code verification',\n    },\n    emailCode: {\n      formTitle: 'Enter the verification code sent to your email address.',\n      resendButton: \"Didn't receive the code? Send it again.\",\n      subtitle: \"We've sent a code to your email.\",\n      title: 'Email verification',\n    },\n    noAvailableMethods: {\n      message: 'Unable to proceed. No available authentication methods.',\n      subtitle: 'Something went wrong.',\n      title: 'Authentication failed',\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Use another method',\n      subtitle: 'Enter your current password to continue using \"{{applicationName}}\"',\n      title: 'Enter your password',\n    },\n    phoneCode: {\n      formTitle: 'Enter the verification code sent to your phone number.',\n      resendButton: \"Didn't receive the code? Resend it.\",\n      subtitle: \"We've sent a code to your phone.\",\n      title: 'Phone verification',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Enter the verification code sent to your phone number.',\n      resendButton: \"Didn't receive the code? Resend it.\",\n      subtitle: 'Verify using the code sent to your phone.',\n      title: 'Phone number MFA',\n    },\n    totpMfa: {\n      formTitle: 'Enter the verification code from your authenticator app.',\n      subtitle: 'Use your authenticator app to get the code.',\n      title: 'Authenticator App MFA',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Add account',\n      action__signOutAll: 'Sign out of all accounts',\n      subtitle: 'Select the account with which you wish to continue.',\n      title: 'Choose an account',\n    },\n    alternativeMethods: {\n      actionLink: 'Получете помощ',\n      actionText: 'Нямате нито един от тях?',\n      blockButton__backupCode: 'Използвай резервен код',\n      blockButton__emailCode: 'Изпрати код по имейл до {{identifier}}',\n      blockButton__emailLink: 'Изпрати линк по имейл до {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Влез с парола',\n      blockButton__phoneCode: 'Изпрати SMS код до {{identifier}}',\n      blockButton__totp: 'Използвай приложение за удостоверяване',\n      getHelp: {\n        blockButton__emailSupport: 'Имейл поддръжка',\n        content:\n          'Ако имате затруднения при влизане в профила си, изпратете ни имейл и ще работим с вас, за да възстановим достъпа възможно най-скоро.',\n        title: 'Получи помощ',\n      },\n      subtitle: 'Имате проблеми? Можете да използвате някой от тези методи за влизане.',\n      title: 'Използвайте друг метод',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'Вашият резервен код е този, който сте получили при настройване на двустепенната аутентикация.',\n      title: 'Въведете резервен код',\n    },\n    emailCode: {\n      formTitle: 'Код за потвърждение',\n      resendButton: 'Не сте получили код? Изпрати отново',\n      subtitle: 'за да продължите към {{applicationName}}',\n      title: 'Проверете вашия имейл',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: 'Върнете се в оригиналния таб, за да продължите.',\n        title: 'Този линк за потвърждение е изтекъл',\n      },\n      failed: {\n        subtitle: 'Върнете се в оригиналния таб, за да продължите.',\n        title: 'Този линк за потвърждение е невалиден',\n      },\n      formSubtitle: 'Използвайте линкът за потвърждение, изпратен на вашия имейл адрес',\n      formTitle: 'Линк за потвърждение',\n      loading: {\n        subtitle: 'Ще бъдете пренасочени скоро',\n        title: 'Влизане...',\n      },\n      resendButton: 'Не сте получили линк? Изпрати отново',\n      subtitle: 'за да продължите към {{applicationName}}',\n      title: 'Проверете вашия имейл',\n      unusedTab: {\n        title: 'Можете да затворите този таб',\n      },\n      verified: {\n        subtitle: 'Ще бъдете пренасочени скоро',\n        title: 'Успешно влезли',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Върнете се в оригиналния таб, за да продължите',\n        subtitleNewTab: 'Върнете се в новоотворения таб, за да продължите',\n        titleNewTab: 'Влезнали сте в друг таб',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Код за нулиране на парола',\n      resendButton: 'Не сте получили код? Изпрати отново',\n      subtitle: 'за да нулирате паролата си',\n      subtitle_email: 'Първо, въведете кода, изпратен на вашия имейл',\n      subtitle_phone: 'Първо, въведете кода, изпратен на вашия телефон',\n      title: 'Нулиране на парола',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Нулирайте вашата парола',\n      label__alternativeMethods: 'Или, влезте с друг метод',\n      title: 'Забравена парола?',\n    },\n    noAvailableMethods: {\n      message: 'Не може да се продължи с влизането. Няма наличен метод за удостоверяване.',\n      subtitle: 'Възникна грешка',\n      title: 'Неуспешно влизане',\n    },\n    passkey: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Използвайте друг метод',\n      subtitle: 'Въведете паролата, свързана с вашия акаунт',\n      title: 'Въведете вашата парола',\n    },\n    passwordPwned: {\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: 'Код за потвърждение',\n      resendButton: 'Не сте получили код? Изпрати отново',\n      subtitle: 'за да продължите към {{applicationName}}',\n      title: 'Проверете вашия телефон',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Код за потвърждение',\n      resendButton: 'Не сте получили код? Изпрати отново',\n      subtitle: 'За да продължите, моля въведете кода за потвърждение, изпратен на вашия телефон',\n      title: 'Проверете вашия телефон',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Нулирай паролата',\n      requiredMessage: 'Вече съществува акаунт с непотвърден имейл адрес. Моля, нулирайте паролата си за сигурност.',\n      successMessage: 'Паролата ви беше успешно променена. Влизане, моля изчакайте момент.',\n      title: 'Задайте нова парола',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Трябва да потвърдим вашата самоличност, преди да нулираме паролата ви.',\n    },\n    start: {\n      actionLink: 'Регистрирайте се',\n      actionLink__join_waitlist: 'Присъединете се към листата за изчакване',\n      actionLink__use_email: 'Използвайте имейл',\n      actionLink__use_email_username: 'Използвайте имейл или потребителско име',\n      actionLink__use_passkey: undefined,\n      actionLink__use_phone: 'Използвайте телефон',\n      actionLink__use_username: 'Използвайте потребителско име',\n      actionText: 'Нямате акаунт?',\n      actionText__join_waitlist: 'Все още нямате акаунт? Присъединете се към листата за изчакване.',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Добре дошли обратно! Моля, влезте, за да продължите',\n      subtitleCombined: undefined,\n      title: 'Влезте в {{applicationName}}',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Код за потвърждение',\n      subtitle:\n        'За да продължите, моля въведете кода за потвърждение, генериран от вашето приложение за удостоверяване',\n      title: 'Двустепенна верификация',\n    },\n  },\n  signInEnterPasswordTitle: 'Въведете вашата парола',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Влезте',\n      actionText: 'Вече имате акаунт?',\n      subtitle: 'Моля, попълнете останалите данни, за да продължите.',\n      title: 'Попълнете липсващите полета',\n    },\n    emailCode: {\n      formSubtitle: 'Въведете кода за потвърждение, изпратен на вашия имейл адрес',\n      formTitle: 'Код за потвърждение',\n      resendButton: 'Не сте получили код? Изпрати отново',\n      subtitle: 'Въведете кода за потвърждение, изпратен на вашия имейл',\n      title: 'Потвърдете вашия имейл',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: 'Използвайте линка за потвърждение, изпратен на вашия имейл адрес',\n      formTitle: 'Линк за потвърждение',\n      loading: {\n        title: 'Регистриране...',\n      },\n      resendButton: 'Не сте получили линк? Изпрати отново',\n      subtitle: 'за да продължите към {{applicationName}}',\n      title: 'Потвърдете вашия имейл',\n      verified: {\n        title: 'Успешно регистриран',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Върнете се към новоотворения таб, за да продължите',\n        subtitleNewTab: 'Върнете се към предходния таб, за да продължите',\n        title: 'Успешно потвърден имейл',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Съгласен съм само с политиката за конфиденциалност',\n        label__onlyTermsOfService: 'Съгласен съм само с условията за ползване',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Съгласен съм с {{ termsOfServiceLink || link(\"условията за ползване\") }} и {{ privacyPolicyLink || link(\"политиката за конфиденциалност\") }}',\n      },\n      continue: {\n        subtitle: 'Продължете, за да завършите процеса',\n        title: 'Прочетете и се съгласете с условията',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Въведете кода за потвърждение, изпратен на вашия телефонен номер',\n      formTitle: 'Код за потвърждение',\n      resendButton: 'Не сте получили код? Изпрати отново',\n      subtitle: 'Въведете кода за потвърждение, изпратен на вашия телефон',\n      title: 'Потвърдете вашия телефон',\n    },\n    restrictedAccess: {\n      actionLink: 'Получете помощ',\n      actionText: 'Нямате нито един от тях?',\n      blockButton__emailSupport: 'Свържете се с поддръжката по имейл',\n      blockButton__joinWaitlist: 'Присъединете се към чакащия списък',\n      subtitle: 'Възникна проблем с достъпа ви.',\n      subtitleWaitlist: 'Можете да се присъедините към чакащия списък за бъдещи възможности.',\n      title: 'Ограничен достъп',\n    },\n    start: {\n      actionLink: 'Влезте',\n      actionLink__use_email: 'Използвайте имейл',\n      actionLink__use_phone: 'Използвайте телефон',\n      actionText: 'Вече имате акаунт?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Добре дошли! Моля, попълнете данните, за да започнете.',\n      subtitleCombined: 'Добре дошли! Моля, попълнете данните, за да започнете.',\n      title: 'Създайте своя акаунт',\n      titleCombined: 'Създайте своя акаунт',\n    },\n  },\n  socialButtonsBlockButton: 'Продължи с {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: 'Вие вече сте член на тази организация.',\n    captcha_invalid: undefined,\n    captcha_unavailable: undefined,\n    form_code_incorrect: 'Невалиден код. Моля, опитайте отново.',\n    form_identifier_exists__email_address: 'Този имейл адрес вече е регистриран.',\n    form_identifier_exists__phone_number: 'Този телефонен номер вече е използван.',\n    form_identifier_exists__username: 'Това потребителско име вече съществува.',\n    form_identifier_not_found: 'Не можем да намерим този идентификатор.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'Имейл адресът не е във валиден формат.',\n    form_param_format_invalid__phone_number: 'Телефонният номер не е във валиден формат.',\n    form_param_max_length_exceeded__first_name: 'Първото име не трябва да е по-дълго от 256 символа.',\n    form_param_max_length_exceeded__last_name: 'Фамилията не трябва да е по-дълга от 256 символа.',\n    form_param_max_length_exceeded__name: 'Името не трябва да надвишава 256 символа.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: 'Невалидна парола. Моля, опитайте отново.',\n    form_password_length_too_short: 'Паролата е твърде кратка. Моля, въведете поне 8 символа.',\n    form_password_not_strong_enough:\n      'Паролата трябва да съдържа поне една главна буква, една цифра и един специален символ.',\n    form_password_pwned: 'Тази парола е компрометирана в изтекли данни. Моля, изберете друга.',\n    form_password_pwned__sign_in: undefined,\n    form_password_size_in_bytes_exceeded: 'Паролата ви е твърде дълга. Моля, съкратете я.',\n    form_password_validation_failed: 'Невалидна парола.',\n    form_username_invalid_character: 'Потребителското име съдържа невалидни символи.',\n    form_username_invalid_length: 'Потребителското име трябва да бъде между 3 и 256 символа.',\n    identification_deletion_failed: 'Не можете да изтриете последната си идентификация.',\n    not_allowed_access:\n      'Имейл адресът или телефонният номер не са разрешени за регистрация. Това може да се дължи на използването на „+“, „=“, „#“ или „.“ във вашия имейл адрес, като използвате домейн, свързан с временна имейл услуга, или сте изрично блокирани. Ако смятате, че това е грешка, моля, свържете се с поддръжката.',\n    organization_domain_blocked: 'Доменът на организацията е блокиран.',\n    organization_domain_common: 'Доменът на организацията е твърде общ.',\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: 'Квотата за членове на организацията е изчерпана.',\n    organization_minimum_permissions_needed: 'Трябва да имате минимални разрешения за достъп.',\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: 'по-малко от {{length}} символа',\n      minimumLength: '{{length}} или повече символа',\n      requireLowercase: 'малка буква',\n      requireNumbers: 'число',\n      requireSpecialCharacter: 'специален символ',\n      requireUppercase: 'главна буква',\n      sentencePrefix: 'Вашата парола трябва да съдържа',\n    },\n    phone_number_exists: 'Този телефонен номер е зает. Моля, опитайте с друг.',\n    session_exists: 'Вече сте влезнали.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Вашата парола работи, но може да бъде по-сигурна. Опитайте да добавите повече символи.',\n      goodPassword: 'Вашата парола отговаря на всички необходими изисквания.',\n      notEnough: 'Вашата парола не е достатъчно сигурна.',\n      suggestions: {\n        allUppercase: 'Направете главни някои, но не всички букви.',\n        anotherWord: 'Добавете още думи, които са по-малко обичайни.',\n        associatedYears: 'Избягвайте години, свързани с вас.',\n        capitalization: 'Напревете главни повече от първата буква.',\n        dates: 'Избягвайте дати и години, свързани с вас.',\n        l33t: 'Избягвайте предвидими замествания на букви като \"@\" за \"a\".',\n        longerKeyboardPattern: 'Използвайте по-дълги клавишни шаблони и променяйте посоката на набиране няколко пъти.',\n        noNeed: 'Можете да създадете сигурни пароли и без да използвате символи, числа или главни букви.',\n        pwned: 'Ако използвате тази парола на друго място, трябва да я промените.',\n        recentYears: 'Избягвайте скорошни години.',\n        repeated: 'Избягвайте повтарящи се думи и символи.',\n        reverseWords: 'Избягвайте обърнати написания на обичайни думи.',\n        sequences: 'Избягвайте обичайни последователности от символи.',\n        useWords: 'Използвайте няколко думи, но избягвайте обичайни фрази.',\n      },\n      warnings: {\n        common: 'Това е често използвана парола.',\n        commonNames: 'Обичайни имена и фамилии са лесни за отгадване.',\n        dates: 'Датите са лесни за отгадване.',\n        extendedRepeat: 'Повтарящи се символни шаблони като \"abcabcabc\" са лесни за отгадване.',\n        keyPattern: 'Кратки клавишни шаблони са лесни за отгадване.',\n        namesByThemselves: 'Единични имена или фамилии са лесни за отгадване.',\n        pwned: 'Вашата парола е била компроментирана от изтекли данни в Интернет.',\n        recentYears: 'Скорошни години са лесни за отгадване.',\n        sequences: 'Обичайни символни последователности като \"abc\" са лесни за отгадване.',\n        similarToCommon: 'Това е подобно на често използвана парола.',\n        simpleRepeat: 'Повтарящи се символи като \"aaa\" са лесни за отгадване.',\n        straightRow: 'Редове от клавиши на клавиатурата са лесни за отгадване.',\n        topHundred: 'Това е често използвана парола.',\n        topTen: 'Това е често използвана парола.',\n        userInputs: 'Не трябва да има лични или свързани със страницата данни.',\n        wordByItself: 'Единични думи са лесни за отгадване.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Добавяне на акаунт',\n    action__manageAccount: 'Управление на акаунта',\n    action__signOut: 'Изход',\n    action__signOutAll: 'Изход от всички акаунти',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Копирано!',\n      actionLabel__copy: 'Копиране на всички',\n      actionLabel__download: 'Изтегляне на .txt',\n      actionLabel__print: 'Отпечатване',\n      infoText1: 'Резервните кодове ще бъдат активирани за този акаунт.',\n      infoText2:\n        'Дръжте резервните кодове в тайна и ги съхранявайте сигурно. Можете да генерирате нови резервни кодове, ако подозирате, че те са били компрометирани.',\n      subtitle__codelist: 'Запазете ги сигурно и ги държете в тайна.',\n      successMessage:\n        'Резервните кодове са активирани. Можете да използвате един от тях, за да влезете в акаунта си, ако загубите достъпа до устройството си за удостоверяване. Всеки код може да се използва само веднъж.',\n      successSubtitle:\n        'Можете да използвате един от тях, за да влезете в акаунта си, ако загубите достъпа до устройството си за удостоверяване.',\n      title: 'Добавяне на резервен код за потвърждение',\n      title__codelist: 'Резервни кодове',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Изберете доставчик, за да свържете вашия профил.',\n      formHint__noAccounts: 'Няма налични външни акаунт доставчици.',\n      removeResource: {\n        messageLine1: '{{identifier}} ще бъде премахнат от този профил.',\n        messageLine2:\n          'Няма да можете да използвате този свързан акаунт, и всякакви зависими функции няма да работят повече.',\n        successMessage: '{{connectedAccount}} беше премахнат от вашия профил.',\n        title: 'Премахни свързан акаунт',\n      },\n      socialButtonsBlockButton: 'Свържи акаунт {{provider|titleize}}',\n      successMessage: 'Доставчикът беше добавен към вашия профил',\n      title: 'Добави свързан акаунт',\n    },\n    deletePage: {\n      actionDescription: 'Напишете \"Изтриване на акаунта\" по-долу, за да продължите.',\n      confirm: 'Изтриване на акаунта',\n      messageLine1: 'Сигурни ли сте, че искате да изтриете акаунта си?',\n      messageLine2: 'Това действие е перманентно и необратимо.',\n      title: 'Изтриване на акаунта',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'На този имейл адрес ще бъде изпратен имейл с код за потвърждение.',\n        formSubtitle: 'Въведете кода за потвърждение, изпратен на {{identifier}}',\n        formTitle: 'Код за потвърждение',\n        resendButton: 'Не сте получили код? Изпрати отново',\n        successMessage: 'Имейлът {{identifier}} беше добавен към вашия профил.',\n      },\n      emailLink: {\n        formHint: 'На този имейл адрес ще бъде изпратен имейл с линк за потвърждение.',\n        formSubtitle: 'Кликнете върху линка за потвърждение в имейла, изпратен на {{identifier}}',\n        formTitle: 'Линк за потвърждение',\n        resendButton: 'Не сте получили линк? Изпрати отново',\n        successMessage: 'Имейлът {{identifier}} беше добавен към вашия профил.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} ще бъде премахнат от този профил.',\n        messageLine2: 'Няма да можете да влезете в профила си, използвайки този имейл адрес.',\n        successMessage: '{{emailAddress}} беше премахнат от вашия профил.',\n        title: 'Премахни имейл адрес',\n      },\n      title: 'Добави имейл адрес',\n      verifyTitle: 'Verify email address',\n    },\n    formButtonPrimary__add: 'Add',\n    formButtonPrimary__continue: 'Продължи',\n    formButtonPrimary__finish: 'Завърши',\n    formButtonPrimary__remove: 'Remove',\n    formButtonPrimary__save: 'Save',\n    formButtonReset: 'Откажи',\n    mfaPage: {\n      formHint: 'Изберете метод, който да добавите.',\n      title: 'Добави двустепенна верификация',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Use existing number',\n      primaryButton__addPhoneNumber: 'Добави телефонен номер',\n      removeResource: {\n        messageLine1: '{{identifier}} няма да получава повече кодове за потвърждение при влизане.',\n        messageLine2: 'Вашият акаунт може да не е толкова сигурен. Сигурни ли сте, че искате да продължите?',\n        successMessage: 'Двустепенното удостоверяване с SMS код беше премахнато за {{mfaPhoneCode}}',\n        title: 'Премахване на двустепенното удостоверяване',\n      },\n      subtitle__availablePhoneNumbers:\n        'Изберете телефонен номер, за да се регистрирате за двустепенното удостоверяване с SMS код.',\n      subtitle__unavailablePhoneNumbers:\n        'Няма налични телефонни номера за регистрация за двустепенното удостоверяване с SMS код.',\n      successMessage1:\n        'When signing in, you will need to enter a verification code sent to this phone number as an additional step.',\n      successMessage2:\n        'Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.',\n      successTitle: 'SMS code verification enabled',\n      title: 'Добавяне на SMS код за потвърждение',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Сканирай QR кода вместо това',\n        buttonUnableToScan__nonPrimary: 'Не може да се сканира QR кода?',\n        infoText__ableToScan:\n          'Настройте нов метод за влизане във вашия удостоверителен апликатор и сканирайте следващия QR код, за да го свържете с вашия акаунт.',\n        infoText__unableToScan:\n          'Настройте нов метод за влизане във вашия удостоверител и въведете предоставения по-долу ключ.',\n        inputLabel__unableToScan1:\n          'Уверете се, че времевите или еднократните пароли са активирани, след което завършете свързването на вашия акаунт.',\n        inputLabel__unableToScan2:\n          'Алтернативно, ако вашият удостоверител поддържа TOTP URI, можете също да копирате пълния URI.',\n      },\n      removeResource: {\n        messageLine1: 'Кодовете за потвърждение от този удостоверител вече няма да са необходими при влизане.',\n        messageLine2: 'Вашият акаунт може да не е толкова сигурен. Сигурни ли сте, че искате да продължите?',\n        successMessage: 'Двустепенното удостоверяване чрез приложение за удостоверяване беше премахнато.',\n        title: 'Премахване на двустепенното удостоверяване',\n      },\n      successMessage:\n        'Двустепенното удостоверяване е активирано. При влизане, ще трябва да въведете код за потвърждение от това удостоверително приложение като допълнителна стъпка.',\n      title: 'Добавяне на приложение за удостоверяване',\n      verifySubtitle: 'Въведете код за потвърждение, генериран от вашето удостоверително приложение',\n      verifyTitle: 'Код за потвърждение',\n    },\n    mobileButton__menu: 'Меню',\n    navbar: {\n      account: 'Profile',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Управлявайте информацията в профила си.',\n      security: 'Security',\n      title: 'Профил',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: undefined,\n        title: undefined,\n      },\n      subtitle__rename: undefined,\n      title__rename: undefined,\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'It is recommended to sign out of all other devices which may have used your old password.',\n      readonly:\n        'Вашата парола в момента не може да бъде редактирана, тъй като можете да влизате само чрез корпоративна връзка.',\n      successMessage__set: 'Паролата ви беше зададена.',\n      successMessage__signOutOfOtherSessions: 'Всички други устройства бяха излезли.',\n      successMessage__update: 'Паролата ви беше обновена.',\n      title__set: 'Задай парола',\n      title__update: 'Промени паролата',\n    },\n    phoneNumberPage: {\n      infoText: 'Съобщение, съдържащо линк за потвърждение, ще бъде изпратено на този телефонен номер.',\n      removeResource: {\n        messageLine1: '{{identifier}} ще бъде премахнат от този профил.',\n        messageLine2: 'Няма да можете да влезете в профила си, използвайки този телефонен номер.',\n        successMessage: '{{phoneNumber}} беше премахнат от вашия профил.',\n        title: 'Премахни телефонен номер',\n      },\n      successMessage: '{{identifier}} беше добавен към вашия профил.',\n      title: 'Добави телефонен номер',\n      verifySubtitle: 'Enter the verification code sent to {{identifier}}',\n      verifyTitle: 'Verify phone number',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Препоръчителен размер 1:1, до 10MB.',\n      imageFormDestructiveActionSubtitle: 'Премахни',\n      imageFormSubtitle: 'Качи',\n      imageFormTitle: 'Профилна снимка',\n      readonly: 'Информацията ви за профила е предоставена от корпоративната връзка и не може да бъде редактирана.',\n      successMessage: 'Вашият профил е актуализиран.',\n      title: 'Актуализиране на профила',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Излез от устройството',\n        title: 'Активни устройства',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Опитайте отново',\n        actionLabel__reauthorize: 'Авторизирайте сега',\n        destructiveActionTitle: 'Премахни',\n        primaryButton: 'Свържи акаунт',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues',\n        title: 'Свързани акаунти',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Изтрий профила',\n        title: 'Прекратяване на профила',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Премахни имейл',\n        detailsAction__nonPrimary: 'Задайте като основен',\n        detailsAction__primary: 'Завърши потвърждение',\n        detailsAction__unverified: 'Потвърдете',\n        primaryButton: 'Добави имейл адрес',\n        title: 'Имейл адреси',\n      },\n      enterpriseAccountsSection: {\n        title: 'Корпоративни акаунти',\n      },\n      headerTitle__account: 'Профил',\n      headerTitle__security: 'Сигурност',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Прегенерирай',\n          headerTitle: 'Резервни кодове',\n          subtitle__regenerate:\n            'Вземете нов набор от сигурни резервни кодове. Предходните резервни кодове ще бъдат изтрити и не могат да бъдат използвани.',\n          title__regenerate: 'Прегенерирай резервни кодове',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Задай като стандартен',\n          destructiveActionLabel: 'Премахни',\n        },\n        primaryButton: 'Добави двустепенна верификация',\n        title: 'Двустепенна верификация',\n        totp: {\n          destructiveActionTitle: 'Премахни',\n          headerTitle: 'Приложение за удостоверяване',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: undefined,\n        menuAction__rename: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Задай парола',\n        primaryButton__updatePassword: 'Промени паролата',\n        title: 'Парола',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Премахни телефонен номер',\n        detailsAction__nonPrimary: 'Задайте като основен',\n        detailsAction__primary: 'Завърши потвърждение',\n        detailsAction__unverified: 'Потвърдете телефонния номер',\n        primaryButton: 'Добави телефонен номер',\n        title: 'Телефонни номера',\n      },\n      profileSection: {\n        primaryButton: 'Редактирайте профила',\n        title: 'Профил',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Задай потребителско име',\n        primaryButton__updateUsername: 'Промени потребителското име',\n        title: 'Потребителско име',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Премахни портфейл',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 портфейли',\n        title: 'Web3 портфейли',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Вашето потребителско име е актуализирано.',\n      title__set: 'Актуализиране на потребителското име',\n      title__update: 'Актуализиране на потребителското име',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} ще бъде премахнат от този профил.',\n        messageLine2: 'Няма да можете да влезете в профила си, използвайки този web3 портфейл.',\n        successMessage: '{{web3Wallet}} беше премахнат от вашия профил.',\n        title: 'Премахни web3 портфейл',\n      },\n      subtitle__availableWallets: 'Изберете web3 портфейл, за да го свържете с вашия профил.',\n      subtitle__unavailableWallets: 'Няма налични web3 портфейли.',\n      successMessage: 'Портфейлът беше добавен към вашия профил.',\n      title: 'Добави web3 портфейл',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: 'Sign up',\n      actionText: \"Don't have an account?\",\n      formButton: 'Join waitlist',\n      subtitle: \"Join our waitlist and we'll notify you once we are ready.\",\n      title: 'Join our waitlist',\n    },\n    success: {\n      message: 'You have successfully joined the waitlist. We will notify you when access is available.',\n      subtitle: 'Thank you for your patience.',\n      title: 'Waitlist successful',\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCACE;AAAA,IACF,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,sCAAsC;AAAA,IACtC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}