{"version": 3, "sources": ["../src/it-IT.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const itIT: LocalizationResource = {\n  locale: 'it-IT',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Indietro',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Predefinito',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Altro dispositivo impersonato',\n  badge__primary: 'Primario',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Richiede azione',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Questo dispositivo',\n  badge__unverified: 'Non verificato',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Dispositivo utente',\n  badge__you: 'Tu',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Crea organizzazione',\n    invitePage: {\n      formButtonReset: 'Salta',\n    },\n    title: 'Crea organizzazione',\n  },\n  dates: {\n    lastDay: \"Ieri alle {{ date | timeString('it-IT') }}\",\n    next6Days: \"{{ date | weekday('it-IT','long') }} alle {{ date | timeString('it-IT') }}\",\n    nextDay: \"Domani alle {{ date | timeString('it-IT') }}\",\n    numeric: \"{{ date | numeric('it-IT') }}\",\n    previous6Days: \"{{ date | weekday('it-IT','long') }} alle {{ date | timeString('it-IT') }}\",\n    sameDay: \"Oggi alle {{ date | timeString('it-IT') }}\",\n  },\n  dividerText: 'oppure',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Utilizzo un altro metodo',\n  footerPageLink__help: 'Aiuto',\n  footerPageLink__privacy: 'Privacy',\n  footerPageLink__terms: 'Termini',\n  formButtonPrimary: 'Continua',\n  formButtonPrimary__verify: 'Verifica',\n  formFieldAction__forgotPassword: 'Password dimenticata?',\n  formFieldError__matchingPasswords: 'Le password coincidono.',\n  formFieldError__notMatchingPasswords: 'Le password non coincidono.',\n  formFieldError__verificationLinkExpired: 'Il link di verifica è scaduto. Per favore richiedi un nuovo link.',\n  formFieldHintText__optional: 'Opzionale',\n  formFieldHintText__slug:\n    'Uno slug è un identificativo leggibile dall’uomo che deve essere univoco. Spesso viene usato negli URL.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: 'Inserisci il codice di backup',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: undefined,\n  formFieldInputPlaceholder__emailAddress: \"Inserisci l'indirizzo email\",\n  formFieldInputPlaceholder__emailAddress_username: \"Inserisci l'indirizzo email o il nome utente\",\n  formFieldInputPlaceholder__emailAddresses: undefined,\n  formFieldInputPlaceholder__firstName: 'Inserisci il tuo nome',\n  formFieldInputPlaceholder__lastName: 'Inserisci il tuo cognome',\n  formFieldInputPlaceholder__organizationDomain: \"Inserisci il dominio dell'organizzazione\",\n  formFieldInputPlaceholder__organizationDomainEmailAddress:\n    \"Inserisci l'indirizzo email del dominio dell'organizzazione\",\n  formFieldInputPlaceholder__organizationName: \"Inserisci il nome dell'organizzazione\",\n  formFieldInputPlaceholder__organizationSlug: \"Inserisci lo slug dell'organizzazione\",\n  formFieldInputPlaceholder__password: 'Inserisci la tua password',\n  formFieldInputPlaceholder__phoneNumber: 'Inserisci il numero di telefono',\n  formFieldInputPlaceholder__username: 'Inserisci il nome utente',\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Abilita inviti automatici per questo dominio',\n  formFieldLabel__backupCode: 'Codice di backup',\n  formFieldLabel__confirmDeletion: 'Conferma',\n  formFieldLabel__confirmPassword: 'Conferma password',\n  formFieldLabel__currentPassword: 'Password corrente',\n  formFieldLabel__emailAddress: 'Indirizzo email',\n  formFieldLabel__emailAddress_username: 'Indirizzo email o nome utente',\n  formFieldLabel__emailAddresses: 'Indirizzi email',\n  formFieldLabel__firstName: 'Nome',\n  formFieldLabel__lastName: 'Cognome',\n  formFieldLabel__newPassword: 'Nuova password',\n  formFieldLabel__organizationDomain: 'Dominio',\n  formFieldLabel__organizationDomainDeletePending: 'Elimina inviti e suggerimenti pendenti',\n  formFieldLabel__organizationDomainEmailAddress: 'Indirizzo email di verifica',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Inserisci un indirizzo email sotto questo dominio per ricevere un codice e verificare questo dominio.',\n  formFieldLabel__organizationName: 'Nome organizzazione',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Nome della passkey',\n  formFieldLabel__password: 'Password',\n  formFieldLabel__phoneNumber: 'Numero di telefono',\n  formFieldLabel__role: 'Ruolo',\n  formFieldLabel__signOutOfOtherSessions: 'Disconnetti tutti gli altri dispositivi',\n  formFieldLabel__username: 'Nome utente',\n  impersonationFab: {\n    action__signOut: 'Disconnetti',\n    title: 'Accesso tramite {{identifier}}',\n  },\n  maintenanceMode: 'Modalità di manutenzione',\n  membershipRole__admin: 'Amministratore',\n  membershipRole__basicMember: 'Utente',\n  membershipRole__guestMember: 'Ospite',\n  organizationList: {\n    action__createOrganization: 'Crea organizzazione',\n    action__invitationAccept: 'Unisciti',\n    action__suggestionsAccept: 'Richiedi di unirti',\n    createOrganization: 'Crea organizzazione',\n    invitationAcceptedLabel: 'Unito',\n    subtitle: 'per continuare a {{applicationName}}',\n    suggestionsAcceptedLabel: 'In attesa di approvazione',\n    title: 'Scegli un account',\n    titleWithoutPersonal: 'Scegli un’organizzazione',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Inviti automatici',\n    badge__automaticSuggestion: 'Suggerimenti automatici',\n    badge__manualInvitation: 'Nessuna iscrizione automatica',\n    badge__unverified: 'Non verificato',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Aggiungi il dominio da verificare. Gli utenti con indirizzi email in questo dominio possono unirsi all’organizzazione automaticamente o richiedere di unirsi.',\n      title: 'Aggiungi dominio',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed: \"L'invito non puó essere inviato. Correggi i seguenti e riprova:\",\n      formButtonPrimary__continue: 'Invia inviti',\n      selectDropdown__role: 'Seleziona ruolo',\n      subtitle: 'Invita un nuovo membro in questa organizzazione',\n      successMessage: 'Invito inviato con successo',\n      title: 'Invita membri',\n    },\n    membersPage: {\n      action__invite: 'Invita',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Rimuovi membro',\n        tableHeader__actions: 'Azioni',\n        tableHeader__joined: 'Iscritto',\n        tableHeader__role: 'Ruolo',\n        tableHeader__user: 'Utente',\n      },\n      detailsTitle__emptyRow: 'Nessun membro da visualizzare',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Invita utenti collegando un dominio email con la tua organizzazione. Chiunque si iscriva con un dominio email corrispondente potrà unirsi all’organizzazione in qualsiasi momento.',\n          headerTitle: 'Inviti automatici',\n          primaryButton: 'Gestisci domini verificati',\n        },\n        table__emptyRow: 'Nessun invito da visualizzare',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Revoca invito',\n        tableHeader__invited: 'Invitato',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Gli utenti che si iscrivono con un dominio email corrispondente, potranno vedere un suggerimento di richiesta di unirsi alla tua organizzazione.',\n          headerTitle: 'Suggerimenti automatici',\n          primaryButton: 'Gestisci domini verificati',\n        },\n        menuAction__approve: 'Approva',\n        menuAction__reject: 'Rifiuta',\n        tableHeader__requested: 'Richiesta accesso',\n        table__emptyRow: 'Nessuna richiesta da visualizzare',\n      },\n      start: {\n        headerTitle__invitations: 'Inviti',\n        headerTitle__members: 'Membri',\n        headerTitle__requests: 'Richieste',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Gestisci la tua organizzazione.',\n      general: 'Generale',\n      members: 'Membri',\n      title: 'Organizzazione',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Scrivi \"{{organizationName}}\" qui sotto per continuare.',\n          messageLine1: 'Sei sicuro di voler eliminare questa organizzazione?',\n          messageLine2: 'Questa azione è permanente e irreversibile.',\n          successMessage: 'Hai eliminato l’organizzazione.',\n          title: 'Elimina organizzazione',\n        },\n        leaveOrganization: {\n          actionDescription: 'Scrivi \"{{organizationName}}\" qui sotto per continuare.',\n          messageLine1:\n            'Sei sicuro di voler lasciare questa organizzazione? Perderai accesso a questa organizzazione e le sue applicazioni.',\n          messageLine2: 'Questa azione è permanente ed irreversibile.',\n          successMessage: \"Hai lasciato l'organizzazione.\",\n          title: 'Lascia organizzazione',\n        },\n        title: 'Pericolo',\n      },\n      domainSection: {\n        menuAction__manage: 'Gestisci',\n        menuAction__remove: 'Elimina',\n        menuAction__verify: 'Verifica',\n        primaryButton: 'Aggiungi dominio',\n        subtitle:\n          \"Consenti agli utenti di unirsi automaticamente all'organizzazione oppure di richiedere di unirsi in base a un dominio email verificato.\",\n        title: 'Domini verificati',\n      },\n      successMessage: \"L'organizzazione è stata aggiornata.\",\n      title: \"Profilo dell'organizzazione\",\n    },\n    removeDomainPage: {\n      messageLine1: 'Il dominio email {{domain}} verrà rimosso.',\n      messageLine2: \"Gli utenti non potranno più unirsi automaticamente all'organizzazione dopo questo.\",\n      successMessage: '{{domain}} è stato rimosso.',\n      title: 'Rimuovi dominio',\n    },\n    start: {\n      headerTitle__general: 'Generale',\n      headerTitle__members: 'Membri',\n      profileSection: {\n        primaryButton: 'Modifica profilo',\n        title: \"Profilo dell'organizzazione\",\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Rimuovere questo dominio influirà sugli utenti invitati.',\n        removeDomainActionLabel__remove: 'Rimuovi dominio',\n        removeDomainSubtitle: 'Rimuovi questo dominio dai tuoi domini verificati',\n        removeDomainTitle: 'Rimuovi dominio',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          \"Gli utenti sono automaticamente invitati a unirsi all'organizzazione quando si registrano e possono unirsi in qualsiasi momento.\",\n        automaticInvitationOption__label: 'Inviti automatici',\n        automaticSuggestionOption__description:\n          \"Gli utenti ricevono un suggerimento per richiedere di unirsi, ma devono essere approvati da un amministratore prima di poter accedere all'organizzazione.\",\n        automaticSuggestionOption__label: 'Suggerimenti automatici',\n        calloutInfoLabel: 'La modifica della modalità di iscrizione influenzerà solo i nuovi utenti.',\n        calloutInvitationCountLabel: 'Inviti in sospeso inviati agli utenti: {{count}}',\n        calloutSuggestionCountLabel: 'Suggerimenti in sospeso inviati agli utenti: {{count}}',\n        manualInvitationOption__description: \"Gli utenti possono essere invitati manualmente solo all'organizzazione.\",\n        manualInvitationOption__label: 'Nessuna iscrizione automatica',\n        subtitle: \"Scegli come gli utenti di questo dominio possono unirsi all'organizzazione.\",\n      },\n      start: {\n        headerTitle__danger: 'Pericolo',\n        headerTitle__enrollment: 'Opzioni di iscrizione',\n      },\n      subtitle: 'Il dominio {{domain}} è ora verificato. Continua selezionando la modalità di iscrizione.',\n      title: 'Aggiorna {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Inserisci il codice di verifica inviato al tuo indirizzo email',\n      formTitle: 'Codice di verifica',\n      resendButton: 'Non hai ricevuto un codice? Reinvia',\n      subtitle: 'Il dominio {{domainName}} deve essere verificato tramite email.',\n      subtitleVerificationCodeScreen:\n        'È stato inviato un codice di verifica a {{emailAddress}}. Inserisci il codice per continuare.',\n      title: 'Verifica dominio',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Crea Organizzazione',\n    action__invitationAccept: 'Unisciti',\n    action__manageOrganization: 'Gestisci Organizzazione',\n    action__suggestionsAccept: 'Richiedi di unirti',\n    notSelected: 'Nessuna organizzazione selezionata',\n    personalWorkspace: 'Spazio di lavoro personale',\n    suggestionsAcceptedLabel: 'In attesa di approvazione',\n  },\n  paginationButton__next: 'Prossimo',\n  paginationButton__previous: 'Precedente',\n  paginationRowText__displaying: 'Visualizzando',\n  paginationRowText__of: 'di',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Clicca qui per utilizzare un metodo alternativo',\n      actionText: 'Usa un metodo di verifica alternativo',\n      blockButton__backupCode: 'Verifica con il codice di backup',\n      blockButton__emailCode: 'Verifica con il codice via email',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Verifica con la password',\n      blockButton__phoneCode: 'Verifica con il codice SMS',\n      blockButton__totp: 'Verifica con il TOTP',\n      getHelp: {\n        blockButton__emailSupport: 'Contatta il supporto via email',\n        content: 'Se hai bisogno di assistenza, contatta il nostro supporto.',\n        title: 'Ottieni aiuto',\n      },\n      subtitle: 'Scegli un metodo per verificarti',\n      title: 'Verifica necessaria',\n    },\n    backupCodeMfa: {\n      subtitle: 'Usa il codice di backup che ti è stato fornito al momento della registrazione.',\n      title: 'Verifica con il codice di backup',\n    },\n    emailCode: {\n      formTitle: 'Inserisci il codice che ti è stato inviato via email.',\n      resendButton: 'Invia di nuovo il codice',\n      subtitle: 'Controlla la tua email per il codice di verifica.',\n      title: 'Verifica con il codice email',\n    },\n    noAvailableMethods: {\n      message: 'Non sono disponibili metodi di verifica.',\n      subtitle: 'Contatta il supporto per assistenza.',\n      title: 'Nessun metodo disponibile',\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Reimposta la password',\n      subtitle: 'Inserisci la tua password per continuare.',\n      title: 'Verifica con la password',\n    },\n    phoneCode: {\n      formTitle: 'Inserisci il codice che ti è stato inviato via SMS.',\n      resendButton: 'Invia di nuovo il codice',\n      subtitle: 'Controlla il tuo SMS per il codice di verifica.',\n      title: 'Verifica con il codice SMS',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Inserisci il codice che ti abbiamo inviato via SMS.',\n      resendButton: 'Invia di nuovo il codice',\n      subtitle: 'Controlla il tuo SMS per il codice di verifica.',\n      title: 'Verifica SMS (MFA)',\n    },\n    totpMfa: {\n      formTitle: 'Inserisci il codice dalla tua app di autenticazione.',\n      subtitle: 'Usa l’app di autenticazione che hai configurato.',\n      title: 'Verifica TOTP (MFA)',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Aggiungi account',\n      action__signOutAll: 'Disconnettiti da tutti gli account',\n      subtitle: \"Seleziona l'account con cui desideri continuare.\",\n      title: 'Scegli un account',\n    },\n    alternativeMethods: {\n      actionLink: 'Richiedi aiuto',\n      actionText: 'Non ne possiedi nessuno?',\n      blockButton__backupCode: 'Usa un codice di backup',\n      blockButton__emailCode: 'Invia codice a {{identifier}}',\n      blockButton__emailLink: 'Invia link a {{identifier}}',\n      blockButton__passkey: 'Usa una passkey',\n      blockButton__password: 'Accedi con la tua password',\n      blockButton__phoneCode: 'Invia codice a {{identifier}}',\n      blockButton__totp: 'Usa la tua app di autenticazione',\n      getHelp: {\n        blockButton__emailSupport: 'Supporto email',\n        content:\n          'Se stai incontrando delle difficoltà ad accedere al tuo account, inviaci una email e ti aiuteremo a ripristinare il tuo account il prima possibile.',\n        title: 'Richiedi aiuto',\n      },\n      subtitle: 'Problemi di accesso? Puoi usare uno di questi metodi per accedere.',\n      title: 'Usa un altro metodo',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'per continuare su {{applicationName}}',\n      title: 'Inserisci un codice di backup',\n    },\n    emailCode: {\n      formTitle: 'Codice di verifica',\n      resendButton: 'Rinvia codice',\n      subtitle: 'per continuare su {{applicationName}}',\n      title: 'Controlla la tua email',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Il client utilizzato non corrisponde al tipo di account associato.',\n        title: 'Errore di mismatch del client',\n      },\n      expired: {\n        subtitle: 'Ritorna alla scheda originaria per continuare',\n        title: 'Questo link di verifica è scaduto',\n      },\n      failed: {\n        subtitle: 'Ritorna alla scheda originaria per continuare',\n        title: 'Questo link di verifica non è valido',\n      },\n      formSubtitle: 'Usa il link di verifica inviato al tuo indirizzo email',\n      formTitle: 'Link di verifica',\n      loading: {\n        subtitle: 'Verrai presto reindirizzato',\n        title: 'Accesso in corso...',\n      },\n      resendButton: 'Rinvia link',\n      subtitle: 'per continuare su {{applicationName}}',\n      title: 'Controlla la tua email',\n      unusedTab: {\n        title: 'Puoi chiudere questa scheda',\n      },\n      verified: {\n        subtitle: 'Verrai presto reindirizzato',\n        title: 'Accesso avvenuto con successo',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Ritorna alla scheda originaria per continuare',\n        subtitleNewTab: 'Ritorna sulla nuova scheda aperta per continuare',\n        titleNewTab: \"Accedi da un'altra scheda\",\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Codice di reset della password',\n      resendButton: 'Non hai ricevuto un codice? Reinvia',\n      subtitle: 'per resettare la tua password',\n      subtitle_email: 'Per prima cosa, inserisci il codice inviato al tuo ID email',\n      subtitle_phone: 'Per prima cosa, inserisci il codice inviato al tuo telefono',\n      title: 'Resetta la password',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Resetta la tua password',\n      label__alternativeMethods: 'Oppure, accedi con un altro metodo',\n      title: 'Hai dimenticato la password?',\n    },\n    noAvailableMethods: {\n      message: \"Impossibile procedere con l'accesso. Non ci sono strumenti di autenticazione disponibili.\",\n      subtitle: 'Si è verificato un errore',\n      title: 'Impossibile accedere',\n    },\n    passkey: {\n      subtitle: 'Usa una passkey per un accesso più sicuro e rapido.',\n      title: 'Autenticazione tramite passkey',\n    },\n    password: {\n      actionLink: 'Usa un altro metodo',\n      subtitle: 'per continuare su {{applicationName}}',\n      title: 'Inserisci la tua password',\n    },\n    passwordPwned: {\n      title: 'La tua password è stata trovata in un data breach.',\n    },\n    phoneCode: {\n      formTitle: 'Codice di verifica',\n      resendButton: 'Rinvia il codice',\n      subtitle: 'per accedere a {{applicationName}}',\n      title: 'Controlla il tuo telefono',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Codice di verifica',\n      resendButton: 'Rinvia il codice',\n      subtitle: 'per accedere a {{applicationName}}',\n      title: 'Controlla il tuo telefono',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Resetta la password',\n      requiredMessage: 'Per motivi di sicurezza, è richiesto di resettare la tua password.',\n      successMessage: 'La tua password è stata cambiata con successo. Ti stiamo collegando, attendi un momento.',\n      title: 'Imposta nuova password',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Dobbiamo verificare la tua identità prima di resettare la tua password.',\n    },\n    start: {\n      actionLink: 'Registrati',\n      actionLink__join_waitlist: \"Unisciti alla lista d'attesa\",\n      actionLink__use_email: 'Usa email',\n      actionLink__use_email_username: 'Usa email o username',\n      actionLink__use_passkey: 'Usa passkey',\n      actionLink__use_phone: 'Usa telefono',\n      actionLink__use_username: 'Usa username',\n      actionText: 'Non hai un account?',\n      actionText__join_waitlist: \"Unisciti alla lista d'attesa\",\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'per continuare su {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Accedi',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Codice di verifica',\n      subtitle: 'Inserisci il codice di verifica dalla tua app di autenticazione.',\n      title: 'Verifica in due passaggi',\n    },\n  },\n  signInEnterPasswordTitle: 'Inserisci la tua password',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Accedi',\n      actionText: 'Hai un account?',\n      subtitle: 'per continuare su {{applicationName}}',\n      title: 'Compila i campi mancanti',\n    },\n    emailCode: {\n      formSubtitle: 'Inserisci il codice di verifica inviato alla tua email',\n      formTitle: 'Codice di verifica',\n      resendButton: 'Rinvia codice',\n      subtitle: 'per continuare su {{applicationName}}',\n      title: 'Verifica la tua email',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Il client che stai utilizzando non corrisponde a quello previsto per il tuo account.',\n        title: 'Errore di incompatibilità del client',\n      },\n      formSubtitle: 'Usa il link di verifica inviato al tuo indirizzo email',\n      formTitle: 'Link di verifica',\n      loading: {\n        title: 'Registrando...',\n      },\n      resendButton: 'Rinvia link',\n      subtitle: 'per continuare su {{applicationName}}',\n      title: 'Verifica la tua email',\n      verified: {\n        title: 'Registrato con successo',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Ritorna alla nuova tab aperta per continuare',\n        subtitleNewTab: 'Ritorna alla tab precedente per continuare',\n        title: 'Email verificata con successo',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Accetto la Politica sulla Privacy',\n        label__onlyTermsOfService: 'Accetto i Termini di Servizio',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Accetto i {{ termsOfServiceLink || link(\"Termini di Servizio\") }} e la {{ privacyPolicyLink || link(\"Politica sulla Privacy\") }}',\n      },\n      continue: {\n        subtitle: 'Per completare la registrazione, accetta i termini e la privacy policy.',\n        title: 'Continua',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Inserisci il codice di verifica inviato al tuo numero di telefono',\n      formTitle: 'Codice di verifica',\n      resendButton: 'Rinvia codice',\n      subtitle: 'per continuare su {{applicationName}}',\n      title: 'Verifica il tuo numero di telefono',\n    },\n    restrictedAccess: {\n      actionLink: 'Contatta il supporto',\n      actionText: 'Hai bisogno di aiuto?',\n      blockButton__emailSupport: 'Email al supporto',\n      blockButton__joinWaitlist: \"Unisciti alla lista d'attesa\",\n      subtitle:\n        \"L'accesso è limitato per il momento. Ti invitiamo a contattare il supporto per ulteriori informazioni.\",\n      subtitleWaitlist: \"Puoi unirti alla lista d'attesa e ti avviseremo quando l'accesso sarà disponibile.\",\n      title: 'Accesso limitato',\n    },\n    start: {\n      actionLink: 'Accedi',\n      actionLink__use_email: 'Usa email',\n      actionLink__use_phone: 'Usa telefono',\n      actionText: 'Hai già un account?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'per continuare su {{applicationName}}',\n      subtitleCombined: 'per continuare su {{applicationName}}',\n      title: 'Crea il tuo account',\n      titleCombined: 'Crea il tuo account',\n    },\n  },\n  socialButtonsBlockButton: 'Continua con {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: 'Sei già un membro di questa organizzazione.',\n    captcha_invalid:\n      'Registrazione non riuscita a causa di fallite convalide di sicurezza. Per favore, ricarica la pagina e riprova o contatta il supporto per ulteriore assistenza.',\n    captcha_unavailable:\n      'Registrazione non riuscita a causa della convalida del bot non riuscita. Per favore, ricarica la pagina e riprova o contatta il supporto per ulteriore assistenza.',\n    form_code_incorrect: 'Il codice inserito non è corretto. Riprova.',\n    form_identifier_exists__email_address: 'Questa email è già registrata.',\n    form_identifier_exists__phone_number: 'Questo numero di telefono è già registrato.',\n    form_identifier_exists__username: 'Questo username è già in uso.',\n    form_identifier_not_found: 'Non abbiamo trovato nessun account con queste informazioni.',\n    form_param_format_invalid: 'Formato non valido.',\n    form_param_format_invalid__email_address: \"L'indirizzo email deve essere un indirizzo email valido.\",\n    form_param_format_invalid__phone_number: 'Il numero di telefono deve essere in un formato internazionale valido.',\n    form_param_max_length_exceeded__first_name: 'Il nome non deve superare i 256 caratteri.',\n    form_param_max_length_exceeded__last_name: 'Il cognome non deve superare i 256 caratteri.',\n    form_param_max_length_exceeded__name: 'Il nome non deve superare i 256 caratteri.',\n    form_param_nil: 'Questo campo è obbligatorio.',\n    form_param_value_invalid: 'Valore non valido.',\n    form_password_incorrect: 'Password errata.',\n    form_password_length_too_short: 'La password deve avere almeno 8 caratteri.',\n    form_password_not_strong_enough: 'La tua password non è abbastanza forte.',\n    form_password_pwned: 'Questa password è stata trovata in una violazione dei dati. Scegli una password diversa.',\n    form_password_pwned__sign_in:\n      'Questa password è stata trovata in una violazione dei dati. Non può essere utilizzata. Reimposta la tua password.',\n    form_password_size_in_bytes_exceeded:\n      'La tua password ha superato il numero massimo di byte consentiti, per favore accorciala o rimuovi alcuni caratteri speciali.',\n    form_password_validation_failed: 'Password errata.',\n    form_username_invalid_character: 'Il nome utente contiene caratteri non validi.',\n    form_username_invalid_length: 'Il nome utente deve avere una lunghezza compresa tra 3 e 32 caratteri.',\n    identification_deletion_failed: 'Non puoi eliminare la tua ultima identificazione.',\n    not_allowed_access:\n      \"L'indirizzo email o il numero di telefono non è autorizzato per la registrazione. Questo può essere dovuto all'uso di '+', '=', '#' o '.' nell'indirizzo email, l'uso di un dominio collegato a un servizio email temporaneo o l'esclusione esplicita. Se ritieni che si tratti di un errore, contattaci.\",\n    organization_domain_blocked: \"Il dominio dell'organizzazione è stato bloccato.\",\n    organization_domain_common: 'Questo dominio è troppo comune per essere utilizzato.',\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: \"Hai raggiunto il limite massimo di membri nell'organizzazione.\",\n    organization_minimum_permissions_needed: 'Non hai i permessi minimi necessari per completare questa operazione.',\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: undefined,\n      minimumLength: undefined,\n      requireLowercase: undefined,\n      requireNumbers: undefined,\n      requireSpecialCharacter: undefined,\n      requireUppercase: undefined,\n      sentencePrefix: undefined,\n    },\n    phone_number_exists: 'Questo numero di telefono è già in uso. Per favore, prova con un altro.',\n    session_exists: 'Sei già loggato.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: undefined,\n      goodPassword: undefined,\n      notEnough: undefined,\n      suggestions: {\n        allUppercase: undefined,\n        anotherWord: undefined,\n        associatedYears: undefined,\n        capitalization: undefined,\n        dates: undefined,\n        l33t: undefined,\n        longerKeyboardPattern: undefined,\n        noNeed: undefined,\n        pwned: undefined,\n        recentYears: undefined,\n        repeated: undefined,\n        reverseWords: undefined,\n        sequences: undefined,\n        useWords: undefined,\n      },\n      warnings: {\n        common: undefined,\n        commonNames: undefined,\n        dates: undefined,\n        extendedRepeat: undefined,\n        keyPattern: undefined,\n        namesByThemselves: undefined,\n        pwned: undefined,\n        recentYears: undefined,\n        sequences: undefined,\n        similarToCommon: undefined,\n        simpleRepeat: undefined,\n        straightRow: undefined,\n        topHundred: undefined,\n        topTen: undefined,\n        userInputs: undefined,\n        wordByItself: undefined,\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Aggiungi account',\n    action__manageAccount: 'Gestisci account',\n    action__signOut: 'Disconnetti',\n    action__signOutAll: 'Disconnetti da tutti gli accounts',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Copiati!',\n      actionLabel__copy: 'Copia tutti',\n      actionLabel__download: 'Scarica .txt',\n      actionLabel__print: 'Stampa',\n      infoText1: 'I codici di backup saranno abilitati per questo account.',\n      infoText2:\n        'Tieni segreti i codici di backup e salvali in maniera sicura. Potrai generare altri codici di backup se pensi che possano essere compromessi.',\n      subtitle__codelist: 'Salvali in maniera sicura e tienili segreti.',\n      successMessage:\n        'I codici di backup sono ora abilitati. Puoi ora utilizzare questi codici di backup per accedere al tuo account, nel caso di perdita del proprio strumento di autenticazione. Ogni codice potrà essere utilizzato una sola volta.',\n      successSubtitle:\n        'Puoi ora utilizzare questi codici di backup per accedere al tuo account, nel caso di perdita del proprio strumento di autenticazione.',\n      title: 'Aggiungi verifica codici backup',\n      title__codelist: 'Codici di backup',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Seleziona un provider per collegare il tuo account.',\n      formHint__noAccounts: 'Non ci sono provider esterni disponibili.',\n      removeResource: {\n        messageLine1: '{{identifier}} sarà rimosso dal tuo account.',\n        messageLine2:\n          'Non sarai piú in grado di accedere utilizzando questo account e le funzionalità collegate smetteranno di funzionare.',\n        successMessage: '{{connectedAccount}} è stato rimosso dal tuo account.',\n        title: 'Rimuovi account collegato',\n      },\n      socialButtonsBlockButton: 'Collega {{provider|titleize}} account',\n      successMessage: 'Il provider è stato aggiunto al tuo account',\n      title: 'Aggiungi account collegato',\n    },\n    deletePage: {\n      actionDescription: 'Digita \"Elimina account\" qui sotto per continuare.',\n      confirm: 'Elimina account',\n      messageLine1: 'Sei sicuro di voler eliminare il tuo account?',\n      messageLine2: 'Questa azione è permanente e irreversibile.',\n      title: 'Elimina account',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Una email contenente un codice di verifica è stata inviata a questo indirizzo email.',\n        formSubtitle: 'Inserisci il codice di verifica inviato a {{identifier}}',\n        formTitle: 'Codice di verifica',\n        resendButton: 'Rinvia codice',\n        successMessage: \"L'indirizzo email {{identifier}} è stato aggiunto al tuo account.\",\n      },\n      emailLink: {\n        formHint: 'Una email contenente un link di verifica sarà inviata a questo indirizzo email.',\n        formSubtitle: 'Clicca sul link di verifica nella email inviata a {{identifier}}',\n        formTitle: 'Link di verifica',\n        resendButton: 'Rinvia link',\n        successMessage: \"L'indirizzo email {{identifier}} è stato aggiunto al tuo account.\",\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} sarà rimosso dal tuo account.',\n        messageLine2: 'Non sarai piú in grado di accedere utilizzando questo indirizzo email.',\n        successMessage: '{{emailAddress}} è stato rimosso dal tuo account.',\n        title: 'Rimuovi indirizzo email',\n      },\n      title: 'Aggiungi un indirizzo email',\n      verifyTitle: 'Verifica indirizzo email',\n    },\n    formButtonPrimary__add: 'Aggiungi',\n    formButtonPrimary__continue: 'Continua',\n    formButtonPrimary__finish: 'Concludi',\n    formButtonPrimary__remove: 'Rimuovi',\n    formButtonPrimary__save: 'Salva',\n    formButtonReset: 'Cancella',\n    mfaPage: {\n      formHint: 'Seleziona un metodo da aggiungere.',\n      title: 'Aggiungi verifica in 2 passaggi',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Usa numero esistente',\n      primaryButton__addPhoneNumber: 'Aggiungi un numero di telefono',\n      removeResource: {\n        messageLine1: \"{{identifier}} non riceverà piú i codici di verifica all'accesso.\",\n        messageLine2: 'Il tuo account potrebbe essere non sicuro. Sei sicuro di voler continuare?',\n        successMessage: 'La verifica in 2 passaggi tramite SMS è stata rimossa per {{mfaPhoneCode}}',\n        title: 'Rimuovi verifica in 2 passaggi',\n      },\n      subtitle__availablePhoneNumbers:\n        'Seleziona un numero di telefono da registrare per la verifica in 2 passaggi tramite SMS.',\n      subtitle__unavailablePhoneNumbers:\n        'Non ci sono numeri di telefono da registrare per la verifica in 2 passaggi tramite SMS.',\n      successMessage1:\n        'Quando accedi, dovrai inserire un codice di verifica inviato a questo numero di telefono come passaggio aggiuntivo.',\n      successMessage2:\n        \"Salva questi codici di backup e conservali in un luogo sicuro. Se perdi l'accesso al tuo dispositivo di autenticazione, puoi utilizzare i codici di backup per accedere.\",\n      successTitle: 'Verifica codice SMS abilitata',\n      title: 'Aggiungi verifica tramite SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Scansiona il codice QR',\n        buttonUnableToScan__nonPrimary: 'Non è possibile scansionare il codice QR?',\n        infoText__ableToScan:\n          'Aggiungi un nuovo metodo di accesso nella tua app di autenticazione e scansione il seguente codice QR per associarla a questo account.',\n        infoText__unableToScan:\n          'Aggiungi un nuovo metodo di accesso nella tua app di autenticazione ed aggiungi la chiave di sicurezza generata sotto.',\n        inputLabel__unableToScan1:\n          'Assicurarsi che le password Time-based oppure One-time siano abilitate, poi continua il collegamento al tuo account.',\n        inputLabel__unableToScan2:\n          \"Alternativamente, se il tuo autenticatore supporta TOTP URIs, puoi anche copiare l'intera URI.\",\n      },\n      removeResource: {\n        messageLine1: \"I codici di verifica di questo autenticatore non saranno piú richiesti all'accesso.\",\n        messageLine2: 'Il tuo account potrebbe essere non sicuro. Sei sicuro di voler continuare?',\n        successMessage: 'La verifica in 2 passaggi tramite autenticatore è stata rimossa.',\n        title: 'Rimuovi verifica in 2 passaggi',\n      },\n      successMessage:\n        \"Verifica in 2 passaggi attivata. All'accesso sarà richiesto di inserire un codice di verifica generato dall'app di autenticazione come ulteriore passaggio.\",\n      title: 'Aggiungi app di autenticazione',\n      verifySubtitle: 'Inserisci il codice di verifica generato dalla tua app di autenticazione',\n      verifyTitle: 'Codice di verifica',\n    },\n    mobileButton__menu: 'Menu',\n    navbar: {\n      account: 'Profilo',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Gestisci il tuo account.',\n      security: 'Sicurezza',\n      title: 'Account',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: 'Sei sicuro di voler rimuovere questa chiave di accesso dal tuo account?',\n        title: 'Rimuovi chiave di accesso',\n      },\n      subtitle__rename: 'Modifica il nome della tua chiave di accesso',\n      title__rename: 'Rinomina chiave di accesso',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'È consigliabile disconnettersi da tutti gli altri dispositivi che potrebbero aver utilizzato la tua vecchia password.',\n      readonly:\n        'La tua password corrente non può essere modificata perché puoi accedere solo tramite la connessione aziendale.',\n      successMessage__set: 'La tua password è stata impostata.',\n      successMessage__signOutOfOtherSessions: 'Tutti gli altri dispositivi sono stati disconnessi.',\n      successMessage__update: 'La tua password è stata aggiornata.',\n      title__set: 'Imposta password',\n      title__update: 'Cambia password',\n    },\n    phoneNumberPage: {\n      infoText: 'Un SMS contenente un link di verifica è stato inviato a questo numero di telefono.',\n      removeResource: {\n        messageLine1: '{{identifier}} sarà rimosso dal tuo account.',\n        messageLine2: 'Non sarai piú in grado di accedere utilizzando questo numero di telefono.',\n        successMessage: '{{phoneNumber}} è stato rimosso dal tuo account.',\n        title: 'Rimuovi numero di telefono',\n      },\n      successMessage: '{{identifier}} è stato aggiunto al tuo account.',\n      title: 'Aggiungi numero di telefono',\n      verifySubtitle: 'Inserisci il codice di verifica inviato a {{identifier}}',\n      verifyTitle: 'Verifica numero di telefono',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Carica una immagine piú piccola di 10MB di tipo JPG, PNG, GIF, oppure WEBP',\n      imageFormDestructiveActionSubtitle: 'Rimuovi immagine',\n      imageFormSubtitle: 'Carica immagine',\n      imageFormTitle: 'Immagine di profilo',\n      readonly:\n        'Le informazioni del tuo profilo sono state fornite dalla connessione aziendale e non possono essere modificate.',\n      successMessage: 'Il tuo profile è stato aggiornato.',\n      title: 'Aggiorna profilo',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Disconnetti dal dispositivo',\n        title: 'Dispositivi attivi',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Riprova',\n        actionLabel__reauthorize: 'Autorizza ora',\n        destructiveActionTitle: 'Rimuovi',\n        primaryButton: 'Collega account',\n        subtitle__disconnected: 'Il tuo account è disconnesso. Per favore, riconnetti il tuo account per continuare.',\n        subtitle__reauthorize:\n          'Gli ambiti richiesti sono stati aggiornati e potresti riscontrare funzionalità limitate. Per favore, ri-autorizza questa applicazione per evitare problemi.',\n        title: 'Account collegati',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Elimina account',\n        title: 'Terminazione account',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Rimuovi indirizzo email',\n        detailsAction__nonPrimary: 'Imposta come principale',\n        detailsAction__primary: 'Completa la verifica',\n        detailsAction__unverified: 'Completa la verifica',\n        primaryButton: 'Aggiungi un indirizzo email',\n        title: 'Indirizzi email',\n      },\n      enterpriseAccountsSection: {\n        title: 'Account aziendali',\n      },\n      headerTitle__account: 'Dettagli profilo',\n      headerTitle__security: 'Sicurezza',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Rigenera codici',\n          headerTitle: 'Codice di backup',\n          subtitle__regenerate:\n            'Ottieni una nuova lista di codici di sicurezza di backup. I codici di sicurezza di backup precedentemente generati saranno eliminati e non saranno utilizzabili.',\n          title__regenerate: 'Rigenera codici di backup',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Imposta come predefinito',\n          destructiveActionLabel: 'Rimuovi numero di telefono',\n        },\n        primaryButton: 'Aggiungi verifica in 2 passaggi',\n        title: 'Verifica in 2 passaggi',\n        totp: {\n          destructiveActionTitle: 'Rimuovi',\n          headerTitle: 'Applicazione di autenticazione',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Rimuovi chiave di accesso',\n        menuAction__rename: 'Rinomina chiave di accesso',\n        primaryButton: undefined,\n        title: 'Gestisci le tue chiavi di accesso',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Imposta password',\n        primaryButton__updatePassword: 'Cambia password',\n        title: 'Password',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Rimuovi numero di telefono',\n        detailsAction__nonPrimary: 'Imposta come principale',\n        detailsAction__primary: 'Completa la verifica',\n        detailsAction__unverified: 'Completa la verifica',\n        primaryButton: 'Aggiungi un numero di telefono',\n        title: 'Numeri di telefono',\n      },\n      profileSection: {\n        primaryButton: 'Salva modifiche',\n        title: 'Profilo',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Imposta username',\n        primaryButton__updateUsername: 'Cambia username',\n        title: 'Username',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Rimuovi wallet',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 wallets',\n        title: 'Web3 wallets',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Il tuo username è stato aggiornato.',\n      title__set: 'Aggiorna username',\n      title__update: 'Aggiorna username',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} sarà rimosso dal tuo account.',\n        messageLine2: 'Non sarai piú in grado di accedere utilizzando questo web3 wallet.',\n        successMessage: '{{web3Wallet}} è stato rimosso dal tuo account.',\n        title: 'Rimuovi web3 wallet',\n      },\n      subtitle__availableWallets: 'Seleziona un web3 wallet da collegare al tuo account.',\n      subtitle__unavailableWallets: 'Non ci sono web3 wallets disponibili.',\n      successMessage: 'Il wallet è stato aggiunto al tuo account.',\n      title: 'Aggiungi web3 wallet',\n      web3WalletButtonsBlockButton: 'Continua con il tuo portafoglio Web3',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBACE;AAAA,EACF,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DACE;AAAA,EACF,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UACE;AAAA,MACF,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BACE;AAAA,IACF,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UACE;AAAA,MACF,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}