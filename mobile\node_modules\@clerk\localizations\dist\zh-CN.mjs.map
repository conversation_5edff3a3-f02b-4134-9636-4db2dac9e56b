{"version": 3, "sources": ["../src/zh-CN.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const zhCN: LocalizationResource = {\n  locale: 'zh-CN',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: '返回',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: '默认',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: '其他模拟器设备',\n  badge__primary: '主要',\n  badge__renewsAt: undefined,\n  badge__requiresAction: '需要操作',\n  badge__startsAt: undefined,\n  badge__thisDevice: '此设备',\n  badge__unverified: '未验证',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: '用户设备',\n  badge__you: '您',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: '创建组织',\n    invitePage: {\n      formButtonReset: '跳过',\n    },\n    title: '创建组织',\n  },\n  dates: {\n    lastDay: \"昨天{{ date | timeString('zh-CN') }}\",\n    next6Days: \"{{ date | weekday('zh-CN','long') }} {{ date | timeString('zh-CN') }}\",\n    nextDay: \"明天{{ date | timeString('zh-CN') }}\",\n    numeric: \"{{ date | numeric('zh-CN') }}\",\n    previous6Days: \"上周{{ date | weekday('zh-CN','long') }} {{ date | timeString('zh-CN') }}\",\n    sameDay: \"今天{{ date | timeString('zh-CN') }}\",\n  },\n  dividerText: '或者',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: '使用另一种方法',\n  footerPageLink__help: '帮助',\n  footerPageLink__privacy: '隐私',\n  footerPageLink__terms: '条款',\n  formButtonPrimary: '继续',\n  formButtonPrimary__verify: '验证',\n  formFieldAction__forgotPassword: '忘记密码？',\n  formFieldError__matchingPasswords: '密码匹配。',\n  formFieldError__notMatchingPasswords: '密码不匹配。',\n  formFieldError__verificationLinkExpired: '验证链接已过期。请申请新的链接。',\n  formFieldHintText__optional: '选填',\n  formFieldHintText__slug: 'Slug 是一个人类可读的 ID，它必须是唯一的。它通常用于 URL 中。',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: '删除帐户',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses: '输入或粘贴一个或多个电子邮件地址，用空格或逗号分隔',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: '我的组织',\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: '为此域名启用自动邀请',\n  formFieldLabel__backupCode: '备用代码',\n  formFieldLabel__confirmDeletion: '确认',\n  formFieldLabel__confirmPassword: '确认密码',\n  formFieldLabel__currentPassword: '当前密码',\n  formFieldLabel__emailAddress: '电子邮件地址',\n  formFieldLabel__emailAddress_username: '电子邮件地址或用户名',\n  formFieldLabel__emailAddresses: '电子邮件地址',\n  formFieldLabel__firstName: '名字',\n  formFieldLabel__lastName: '姓氏',\n  formFieldLabel__newPassword: '新密码',\n  formFieldLabel__organizationDomain: '域名',\n  formFieldLabel__organizationDomainDeletePending: '删除待处理的邀请和建议',\n  formFieldLabel__organizationDomainEmailAddress: '验证邮箱地址',\n  formFieldLabel__organizationDomainEmailAddressDescription: '输入此域名下的一个邮箱地址以接收验证码并验证此域名。',\n  formFieldLabel__organizationName: '组织名称',\n  formFieldLabel__organizationSlug: 'URL 简称',\n  formFieldLabel__passkeyName: undefined,\n  formFieldLabel__password: '密码',\n  formFieldLabel__phoneNumber: '电话号码',\n  formFieldLabel__role: '角色',\n  formFieldLabel__signOutOfOtherSessions: '登出所有其他设备',\n  formFieldLabel__username: '用户名',\n  impersonationFab: {\n    action__signOut: '退出登录',\n    title: '以 {{identifier}} 登录',\n  },\n  maintenanceMode: undefined,\n  membershipRole__admin: '管理员',\n  membershipRole__basicMember: '成员',\n  membershipRole__guestMember: '访客',\n  organizationList: {\n    action__createOrganization: '创建组织',\n    action__invitationAccept: '加入',\n    action__suggestionsAccept: '请求加入',\n    createOrganization: '创建组织',\n    invitationAcceptedLabel: '已加入',\n    subtitle: '以继续使用 {{applicationName}}',\n    suggestionsAcceptedLabel: '等待批准',\n    title: '选择一个账户',\n    titleWithoutPersonal: '选择一个组织',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: '自动邀请',\n    badge__automaticSuggestion: '自动建议',\n    badge__manualInvitation: '无自动注册',\n    badge__unverified: '未验证',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle: '添加域名以进行验证。具有此域名电子邮件地址的用户可以自动加入组织或请求加入。',\n      title: '添加域名',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed: '邀请无法发送。修复以下问题然后重试：',\n      formButtonPrimary__continue: '发送邀请',\n      selectDropdown__role: '选择角色',\n      subtitle: '邀请新成员加入此组织',\n      successMessage: '邀请成功发送',\n      title: '邀请成员',\n    },\n    membersPage: {\n      action__invite: '邀请',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: '移除成员',\n        tableHeader__actions: undefined,\n        tableHeader__joined: '加入',\n        tableHeader__role: '角色',\n        tableHeader__user: '用户',\n      },\n      detailsTitle__emptyRow: '没有可显示的成员',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            '通过将电子邮件域名与您的组织连接来邀请用户。任何使用匹配电子邮件域名注册的人都可以随时加入组织。',\n          headerTitle: '自动邀请',\n          primaryButton: '管理已验证域名',\n        },\n        table__emptyRow: '没有可显示的邀请',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: '撤销邀请',\n        tableHeader__invited: '已邀请',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle: '使用匹配电子邮件域名注册的用户将能够看到请求加入您组织的建议。',\n          headerTitle: '自动建议',\n          primaryButton: '管理已验证域名',\n        },\n        menuAction__approve: '批准',\n        menuAction__reject: '拒绝',\n        tableHeader__requested: '已请求访问',\n        table__emptyRow: '没有可显示的请求',\n      },\n      start: {\n        headerTitle__invitations: '邀请',\n        headerTitle__members: '成员',\n        headerTitle__requests: '请求',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: '管理您的组织。',\n      general: '常规',\n      members: '成员',\n      title: '组织',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: '在下方输入 \"{{organizationName}}\" 以继续。',\n          messageLine1: '您确定要删除此组织吗？',\n          messageLine2: '此操作是永久性的且无法撤销。',\n          successMessage: '您已删除该组织。',\n          title: '删除组织',\n        },\n        leaveOrganization: {\n          actionDescription: '在下方输入 \"{{organizationName}}\" 以继续。',\n          messageLine1: '您确定要离开此组织吗？您将失去对此组织及其应用程序的访问权限。',\n          messageLine2: '此操作是永久性的且无法撤销。',\n          successMessage: '您已离开了组织。',\n          title: '离开组织',\n        },\n        title: '危险',\n      },\n      domainSection: {\n        menuAction__manage: '管理',\n        menuAction__remove: '删除',\n        menuAction__verify: '验证',\n        primaryButton: '添加域名',\n        subtitle: '允许用户根据已验证的电子邮件域名自动加入组织或请求加入。',\n        title: '已验证域名',\n      },\n      successMessage: '组织信息已更新。',\n      title: '组织简介',\n    },\n    removeDomainPage: {\n      messageLine1: '电子邮件域名 {{domain}} 将被删除。',\n      messageLine2: '此后，用户将无法自动加入组织。',\n      successMessage: '{{domain}} 已被删除。',\n      title: '删除域名',\n    },\n    start: {\n      headerTitle__general: '常规',\n      headerTitle__members: '成员',\n      profileSection: {\n        primaryButton: '更新组织简介',\n        title: '组织简介',\n        uploadAction__title: '标志',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: '删除此域名将影响受邀用户。',\n        removeDomainActionLabel__remove: '删除域名',\n        removeDomainSubtitle: '从您的已验证域名中删除此域名',\n        removeDomainTitle: '删除域名',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description: '用户在注册时会自动收到加入组织的邀请，并且可以随时加入。',\n        automaticInvitationOption__label: '自动邀请',\n        automaticSuggestionOption__description: '用户会收到请求加入的建议，但在加入组织之前必须得到管理员的批准。',\n        automaticSuggestionOption__label: '自动建议',\n        calloutInfoLabel: '更改注册模式只会影响新用户。',\n        calloutInvitationCountLabel: '已发送给用户的待处理邀请：{{count}}',\n        calloutSuggestionCountLabel: '已发送给用户的待处理建议：{{count}}',\n        manualInvitationOption__description: '用户只能被手动邀请加入组织。',\n        manualInvitationOption__label: '无自动注册',\n        subtitle: '选择此域名的用户如何加入组织。',\n      },\n      start: {\n        headerTitle__danger: '危险',\n        headerTitle__enrollment: '注册选项',\n      },\n      subtitle: '域名 {{domain}} 现已验证。继续选择注册模式。',\n      title: '更新 {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: '输入发送到您邮箱地址的验证码',\n      formTitle: '验证码',\n      resendButton: '未收到验证码？重新发送',\n      subtitle: '域名 {{domainName}} 需要通过电子邮件进行验证。',\n      subtitleVerificationCodeScreen: '验证码已发送到 {{emailAddress}}。输入验证码以继续。',\n      title: '验证域名',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: '创建组织',\n    action__invitationAccept: '加入',\n    action__manageOrganization: '管理组织',\n    action__suggestionsAccept: '请求加入',\n    notSelected: '未选择组织',\n    personalWorkspace: '个人工作区',\n    suggestionsAcceptedLabel: '等待批准',\n  },\n  paginationButton__next: '下一页',\n  paginationButton__previous: '上一页',\n  paginationRowText__displaying: '显示',\n  paginationRowText__of: '的',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: '添加账户',\n      action__signOutAll: '退出所有账户',\n      subtitle: '选择您要继续使用的账户。',\n      title: '选择一个账户',\n    },\n    alternativeMethods: {\n      actionLink: '获取帮助',\n      actionText: '没有这些？',\n      blockButton__backupCode: '使用备用代码',\n      blockButton__emailCode: '电子邮件验证码到 {{identifier}}',\n      blockButton__emailLink: '电子邮件链接到 {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: '使用您的密码登录',\n      blockButton__phoneCode: '发送短信代码到 {{identifier}}',\n      blockButton__totp: '使用您的验证应用程序',\n      getHelp: {\n        blockButton__emailSupport: '邮件支持',\n        content: '如果您在登录账户时遇到困难，请给我们发送电子邮件，我们将尽快让您恢复访问。',\n        title: '获取帮助',\n      },\n      subtitle: '遇到问题？您可以使用以下任何方法登录。',\n      title: '使用其他方法',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: '继续使用 {{applicationName}}',\n      title: '输入备用代码',\n    },\n    emailCode: {\n      formTitle: '验证码',\n      resendButton: '重新发送验证码',\n      subtitle: '继续使用 {{applicationName}}',\n      title: '查看您的电子邮件',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: '返回原始标签页继续。',\n        title: '此验证链接已过期',\n      },\n      failed: {\n        subtitle: '返回原始标签页继续。',\n        title: '此验证链接无效',\n      },\n      formSubtitle: '使用发送到您的电子邮件的验证链接',\n      formTitle: '验证链接',\n      loading: {\n        subtitle: '即将为您重定向',\n        title: '正在登录...',\n      },\n      resendButton: '重新发送链接',\n      subtitle: '继续使用 {{applicationName}}',\n      title: '查看您的电子邮件',\n      unusedTab: {\n        title: '您可以关闭此标签页',\n      },\n      verified: {\n        subtitle: '即将为您重定向',\n        title: '成功登录',\n      },\n      verifiedSwitchTab: {\n        subtitle: '返回原始标签页继续',\n        subtitleNewTab: '返回新打开的标签页继续',\n        titleNewTab: '在其他标签页上登录',\n      },\n    },\n    forgotPassword: {\n      formTitle: '重置密码代码',\n      resendButton: '重新发送代码',\n      subtitle: '以重置您的密码',\n      subtitle_email: '首先，输入发送到您的电子邮件 ID 的代码',\n      subtitle_phone: '首先，输入发送到您手机的代码',\n      title: '重置密码',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: '重置密码',\n      label__alternativeMethods: '或者，使用其他方式登录。',\n      title: '忘记密码？',\n    },\n    noAvailableMethods: {\n      message: '无法继续登录。没有可用的身份验证因素。',\n      subtitle: '出现错误',\n      title: '无法登录',\n    },\n    passkey: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: '使用其他方法',\n      subtitle: '继续使用 {{applicationName}}',\n      title: '输入您的密码',\n    },\n    passwordPwned: {\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: '验证码',\n      resendButton: '重新发送验证码',\n      subtitle: '继续使用 {{applicationName}}',\n      title: '检查手机短信',\n    },\n    phoneCodeMfa: {\n      formTitle: '验证码',\n      resendButton: '重新发送验证码',\n      subtitle: '请继续输入发送到您手机的验证码。',\n      title: '检查手机短信',\n    },\n    resetPassword: {\n      formButtonPrimary: '重置密码',\n      requiredMessage: '出于安全原因，需要重置您的密码。',\n      successMessage: '您的密码已成功更改。正在为您登录，请稍等。',\n      title: '重置密码',\n    },\n    resetPasswordMfa: {\n      detailsLabel: '我们需要验证您的身份才能重置您的密码。',\n    },\n    start: {\n      actionLink: '注册',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: '使用电子邮件',\n      actionLink__use_email_username: '使用电子邮件或用户名',\n      actionLink__use_passkey: undefined,\n      actionLink__use_phone: '使用电话',\n      actionLink__use_username: '使用用户名',\n      actionText: '还没有账户？',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: '继续使用 {{applicationName}}',\n      subtitleCombined: undefined,\n      title: '登录',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: '验证码',\n      subtitle: '请继续输入由您的身份验证应用生成的验证码。',\n      title: '两步验证',\n    },\n  },\n  signInEnterPasswordTitle: '输入您的密码',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: '登录',\n      actionText: '已经有账户了？',\n      subtitle: '继续使用 {{applicationName}}',\n      title: '填写缺少的字段',\n    },\n    emailCode: {\n      formSubtitle: '输入发送到您的电子邮件地址的验证码',\n      formTitle: '验证码',\n      resendButton: '重新发送验证码',\n      subtitle: '继续使用 {{applicationName}}',\n      title: '验证您的电子邮件',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: '使用发送到您的电子邮件地址的验证链接',\n      formTitle: '验证链接',\n      loading: {\n        title: '正在注册...',\n      },\n      resendButton: '重新发送链接',\n      subtitle: '继续使用 {{applicationName}}',\n      title: '验证您的电子邮件',\n      verified: {\n        title: '成功注册',\n      },\n      verifiedSwitchTab: {\n        subtitle: '返回新打开的标签页继续',\n        subtitleNewTab: '返回上一个标签页继续',\n        title: '成功验证电子邮件',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: '输入发送到您的电话号码的验证码',\n      formTitle: '验证码',\n      resendButton: '重新发送验证码',\n      subtitle: '继续使用 {{applicationName}}',\n      title: '验证您的电话',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: '登录',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: '已经有账户了？',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: '继续使用 {{applicationName}}',\n      subtitleCombined: '继续使用 {{applicationName}}',\n      title: '创建您的账户',\n      titleCombined: '创建您的账户',\n    },\n  },\n  socialButtonsBlockButton: '使用 {{provider|titleize}} 登录',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid: '由于安全验证失败，注册未成功。请刷新页面重试或联系支持获取更多帮助。',\n    captcha_unavailable: '注册失败，原因是未通过机器人验证。请刷新页面重试或联系支持团队以获取更多帮助。',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: '我们无法找到具有这些信息的账户。',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: '邮箱地址必须是有效的邮箱格式。',\n    form_param_format_invalid__phone_number: '电话号码必须符合有效的国际格式。',\n    form_param_max_length_exceeded__first_name: '名字长度不得超过256个字符。',\n    form_param_max_length_exceeded__last_name: '姓氏长度不得超过256个字符。',\n    form_param_max_length_exceeded__name: '姓名长度不得超过256个字符。',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: '您的密码强度不够。',\n    form_password_pwned: '这个密码在数据泄露中被发现，不能使用，请换一个密码试试。',\n    form_password_pwned__sign_in: undefined,\n    form_password_size_in_bytes_exceeded: '您的密码超过了允许的最大字节数，请缩短它或去掉一些特殊字符。',\n    form_password_validation_failed: '密码错误',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: '您无法删除最后一个身份标识。',\n    not_allowed_access:\n      \"您使用的电子邮件地址或电话号码不允许注册。这可能是因为您在电子邮件地址中使用了 '+', '=', '#' 或 '.'，使用了与临时电子邮件服务关联的域名，或者有明确的排除。如果您认为这是错误，请联系支持。\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: '少于{{length}}个字符',\n      minimumLength: '{{length}}个或更多字符',\n      requireLowercase: '一个小写字母',\n      requireNumbers: '一个数字',\n      requireSpecialCharacter: '一个特殊字符',\n      requireUppercase: '一个大写字母',\n      sentencePrefix: '您的密码必须包含',\n    },\n    phone_number_exists: '该电话号码已被使用，请尝试其他号码。',\n    session_exists: '您已登录。',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: '您的密码可以用，但可以更强。试着添加更多字符。',\n      goodPassword: '做得好。这是一个优秀的密码。',\n      notEnough: '您的密码强度不够。',\n      suggestions: {\n        allUppercase: '大写一些，但不是所有的字母。',\n        anotherWord: '添加更不常见的更多单词。',\n        associatedYears: '避免与你有关的年份。',\n        capitalization: '大写不仅仅是第一个字母。',\n        dates: '避免与你有关的日期和年份。',\n        l33t: '避免预测的字母替换，如\"@\"代替\"a\"。',\n        longerKeyboardPattern: '使用更长的键盘模式，并多次改变打字方向。',\n        noNeed: '你可以创建强密码，而无需使用符号，数字或大写字母。',\n        pwned: '如果您在其他地方使用此密码，您应该更改它。',\n        recentYears: '避免近年来。',\n        repeated: '避免重复的单词和字符。',\n        reverseWords: '避免常用词的反向拼写。',\n        sequences: '避免常见字符序列。',\n        useWords: '使用多个单词，但避免常见短语。',\n      },\n      warnings: {\n        common: '这是一个常用的密码。',\n        commonNames: '常见的名字和姓氏易被猜到。',\n        dates: '日期易被猜到。',\n        extendedRepeat: '像\"abcabcabc\"这样的重复字符模式易被猜到。',\n        keyPattern: '短键盘模式易被猜到。',\n        namesByThemselves: '单个名字或姓氏易被猜到。',\n        pwned: '您的密码在互联网上的数据泄露中被暴露。',\n        recentYears: '近年来易被猜到。',\n        sequences: '像\"abc\"这样的常见字符序列易被猜到。',\n        similarToCommon: '这个密码和常用密码相似。',\n        simpleRepeat: '像\"aaa\"这样的重复字符易被猜到。',\n        straightRow: '键盘上的直行键易被猜到。',\n        topHundred: '这是一个频繁使用的密码。',\n        topTen: '这是一个大量使用的密码。',\n        userInputs: '不应该有任何个人或页面相关的数据。',\n        wordByItself: '单个单词易被猜到。',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: '添加账户',\n    action__manageAccount: '管理账户',\n    action__signOut: '退出登录',\n    action__signOutAll: '退出所有账户',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: '已复制！',\n      actionLabel__copy: '复制全部',\n      actionLabel__download: '下载 .txt',\n      actionLabel__print: '打印',\n      infoText1: '将为此账户启用备份代码。',\n      infoText2: '保密并安全存储备份代码。如果您怀疑它们已经泄露，您可以重新生成备份代码。',\n      subtitle__codelist: '安全存储并保守秘密。',\n      successMessage:\n        '现在已启用备份代码。如果您失去了验证设备的访问权限，您可以使用其中之一登录您的账户。每个代码只能使用一次。',\n      successSubtitle: '如果您失去了验证设备的访问权限，您可以使用其中之一登录您的账户。',\n      title: '添加备份代码验证',\n      title__codelist: '备份代码',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: '选择一个供应商来连接您的账户。',\n      formHint__noAccounts: '没有可用的外部账户供应商。',\n      removeResource: {\n        messageLine1: '{{identifier}} 将从此账户中被移除。',\n        messageLine2: '您将无法再使用这个已连接的账户，任何依赖的功能将不再工作。',\n        successMessage: '{{connectedAccount}} 已从您的账户中移除。',\n        title: '移除已连接的账户',\n      },\n      socialButtonsBlockButton: '连接 {{provider|titleize}} 账户',\n      successMessage: '供应商已被添加到您的账户',\n      title: '添加已连接的账户',\n    },\n    deletePage: {\n      actionDescription: '在下方输入 \"Delete account\" 以继续。',\n      confirm: 'Delete account',\n      messageLine1: '您确定要删除您的账户吗？',\n      messageLine2: '此操作是永久且不可逆的。',\n      title: '删除账户',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: '一封含有验证码的邮件将会被发送到这个电子邮件地址。',\n        formSubtitle: '输入发送到 {{identifier}} 的验证码',\n        formTitle: '验证码',\n        resendButton: '重发验证码',\n        successMessage: '电子邮件 {{identifier}} 已被添加到您的账户。',\n      },\n      emailLink: {\n        formHint: '一封含有验证链接的邮件将会被发送到这个电子邮件地址。',\n        formSubtitle: '点击发送到 {{identifier}} 的邮件中的验证链接',\n        formTitle: '验证链接',\n        resendButton: '重发链接',\n        successMessage: '电子邮件 {{identifier}} 已被添加到您的账户。',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} 将从此账户中被移除。',\n        messageLine2: '您将无法使用这个电子邮件地址登录。',\n        successMessage: '电子邮件 {{emailAddress}} 已从您的账户中移除。',\n        title: '移除电子邮件地址',\n      },\n      title: '添加电子邮件地址',\n      verifyTitle: 'Verify email address',\n    },\n    formButtonPrimary__add: '添加',\n    formButtonPrimary__continue: '继续',\n    formButtonPrimary__finish: '完成',\n    formButtonPrimary__remove: '移除',\n    formButtonPrimary__save: '保存',\n    formButtonReset: '取消',\n    mfaPage: {\n      formHint: '选择一个添加的方法。',\n      title: '添加两步验证',\n    },\n    mfaPhoneCodePage: {\n      backButton: '使用现有号码',\n      primaryButton__addPhoneNumber: '添加电话号码',\n      removeResource: {\n        messageLine1: '{{identifier}} 将不再在登录时接收验证代码。',\n        messageLine2: '您的账户可能不再安全。您确定要继续吗？',\n        successMessage: '已移除{{mfaPhoneCode}}的短信验证码两步验证',\n        title: '移除两步验证',\n      },\n      subtitle__availablePhoneNumbers: '选择一个电话号码来注册短信验证码两步验证。',\n      subtitle__unavailablePhoneNumbers: '没有可用的电话号码来注册短信验证码两步验证。',\n      successMessage1: '登录时，您需要输入发送到此电话号码的验证码作为额外的步骤。',\n      successMessage2: '请保存这些备份代码并将其妥善存放。如果您无法访问身份验证设备，可以使用备份代码进行登录。',\n      successTitle: '短信验证码验证已启用',\n      title: '添加短信验证码验证',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: '扫描二维码',\n        buttonUnableToScan__nonPrimary: '不能扫描二维码？',\n        infoText__ableToScan: '在您的验证器应用中设置一个新的登录方法，并扫描下面的二维码将其链接到您的账户。',\n        infoText__unableToScan: '在验证器中设置一个新的登录方法，并输入下面提供的 Key。',\n        inputLabel__unableToScan1: '确保启用了基于时间或一次性密码，然后完成链接您的账户。',\n        inputLabel__unableToScan2: '或者，如果您的验证器支持 TOTP URIs，您也可以复制完整的 URI。',\n      },\n      removeResource: {\n        messageLine1: '登录时，将不再需要来自此验证器的验证码。',\n        messageLine2: '您的账户可能不再安全。您确定要继续吗？',\n        successMessage: '已移除通过验证器应用程序的两步验证。',\n        title: '移除两步验证',\n      },\n      successMessage: '现在已启用两步验证。在登录时，您需要输入来自此验证器的验证码作为额外步骤。',\n      title: '添加验证器应用程序',\n      verifySubtitle: '输入您的验证器生成的验证码',\n      verifyTitle: '验证代码',\n    },\n    mobileButton__menu: '菜单',\n    navbar: {\n      account: '账户',\n      apiKeys: undefined,\n      billing: undefined,\n      description: '管理您的账户。',\n      security: '安全',\n      title: '账户',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: undefined,\n        title: undefined,\n      },\n      subtitle__rename: undefined,\n      title__rename: undefined,\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions: '建议您从所有可能使用过旧密码的其他设备上退出登录。',\n      readonly: '由于您只能通过企业连接登录，当前无法编辑您的密码。',\n      successMessage__set: '您的密码已设置。',\n      successMessage__signOutOfOtherSessions: '所有其他设备已退出。',\n      successMessage__update: '您的密码已更新。',\n      title__set: '设置密码',\n      title__update: '更改密码',\n    },\n    phoneNumberPage: {\n      infoText: '一条包含验证链接的短信将会发送到这个电话号码。',\n      removeResource: {\n        messageLine1: '{{identifier}} 将从此账户中被移除。',\n        messageLine2: '您将无法使用这个电话号码登录。',\n        successMessage: '电话号码 {{phoneNumber}} 已从您的账户中移除。',\n        title: '移除电话号码',\n      },\n      successMessage: '{{identifier}} 已被添加到您的账户。',\n      title: '添加电话号码',\n      verifySubtitle: '请输入发送至 {{identifier}} 的验证码',\n      verifyTitle: '验证电话号码',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: '上传小于10MB的JPG, PNG, GIF, 或WEBP格式的图片',\n      imageFormDestructiveActionSubtitle: '移除图片',\n      imageFormSubtitle: '上传图片',\n      imageFormTitle: '个人资料图片',\n      readonly: '您的个人信息由企业连接提供，无法进行编辑。',\n      successMessage: '您的个人资料已更新。',\n      title: '更新个人资料',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: '退出设备',\n        title: '活动设备',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: '再试一次',\n        actionLabel__reauthorize: '立即授权',\n        destructiveActionTitle: '移除',\n        primaryButton: '连接账户',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          '“所需的权限范围已更新，您可能会遇到功能受限的问题。请重新授权此应用程序，以避免出现任何问题。”',\n        title: '已连接的账户',\n      },\n      dangerSection: {\n        deleteAccountButton: '删除账户',\n        title: '终止账户',\n      },\n      emailAddressesSection: {\n        destructiveAction: '移除电子邮件地址',\n        detailsAction__nonPrimary: '设为主要',\n        detailsAction__primary: '完成验证',\n        detailsAction__unverified: '完成验证',\n        primaryButton: '添加电子邮件地址',\n        title: '电子邮件地址',\n      },\n      enterpriseAccountsSection: {\n        title: '企业账户',\n      },\n      headerTitle__account: '账户',\n      headerTitle__security: '安全',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: '重新生成代码',\n          headerTitle: '备份代码',\n          subtitle__regenerate: '获取一套新的安全备份代码。之前的备份代码将被删除，无法使用。',\n          title__regenerate: '重新生成备份代码',\n        },\n        phoneCode: {\n          actionLabel__setDefault: '设为默认',\n          destructiveActionLabel: '移除电话号码',\n        },\n        primaryButton: '添加两步验证',\n        title: '两步验证',\n        totp: {\n          destructiveActionTitle: '移除',\n          headerTitle: '验证器应用程序',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: undefined,\n        menuAction__rename: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      passwordSection: {\n        primaryButton__setPassword: '设置密码',\n        primaryButton__updatePassword: '更改密码',\n        title: '密码',\n      },\n      phoneNumbersSection: {\n        destructiveAction: '移除电话号码',\n        detailsAction__nonPrimary: '设为主要',\n        detailsAction__primary: '完成验证',\n        detailsAction__unverified: '完成验证',\n        primaryButton: '添加电话号码',\n        title: '电话号码',\n      },\n      profileSection: {\n        primaryButton: '更新个人资料',\n        title: '个人资料',\n      },\n      usernameSection: {\n        primaryButton__setUsername: '设置用户名',\n        primaryButton__updateUsername: '更改用户名',\n        title: '用户名',\n      },\n      web3WalletsSection: {\n        destructiveAction: '移除钱包',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 钱包',\n        title: 'Web3 钱包',\n      },\n    },\n    usernamePage: {\n      successMessage: '您的用户名已更新。',\n      title__set: '更新用户名',\n      title__update: '更新用户名',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} 将从此账户中被移除。',\n        messageLine2: '您将无法使用这个 web3 钱包登录。',\n        successMessage: '{{web3Wallet}} 已从您的账户中移除。',\n        title: '移除 web3 钱包',\n      },\n      subtitle__availableWallets: '选择一个 web3 钱包连接到您的账户。',\n      subtitle__unavailableWallets: '没有可用的 web3 钱包。',\n      successMessage: '钱包已被添加到您的账户。',\n      title: '添加web3钱包',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCAAwC;AAAA,QACxC,kCAAkC;AAAA,QAClC,wCAAwC;AAAA,QACxC,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,sCAAsC;AAAA,IACtC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WAAW;AAAA,MACX,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCAAiC;AAAA,MACjC,mCAAmC;AAAA,MACnC,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,MAC7B;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CAA0C;AAAA,MAC1C,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBAAsB;AAAA,UACtB,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}