// src/zh-TW.ts
var zhTW = {
  locale: "zh-TW",
  apiKeys: {
    action__add: void 0,
    action__search: void 0,
    createdAndExpirationStatus__expiresOn: void 0,
    createdAndExpirationStatus__never: void 0,
    detailsTitle__emptyRow: void 0,
    formButtonPrimary__add: void 0,
    formFieldCaption__expiration__expiresOn: void 0,
    formFieldCaption__expiration__never: void 0,
    formFieldOption__expiration__180d: void 0,
    formFieldOption__expiration__1d: void 0,
    formFieldOption__expiration__1y: void 0,
    formFieldOption__expiration__30d: void 0,
    formFieldOption__expiration__60d: void 0,
    formFieldOption__expiration__7d: void 0,
    formFieldOption__expiration__90d: void 0,
    formFieldOption__expiration__never: void 0,
    formHint: void 0,
    formTitle: void 0,
    lastUsed__days: void 0,
    lastUsed__hours: void 0,
    lastUsed__minutes: void 0,
    lastUsed__months: void 0,
    lastUsed__seconds: void 0,
    lastUsed__years: void 0,
    menuAction__revoke: void 0,
    revokeConfirmation: {
      confirmationText: void 0,
      formButtonPrimary__revoke: void 0,
      formHint: void 0,
      formTitle: void 0
    }
  },
  backButton: "\u8FD4\u56DE",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "\u9ED8\u8A8D",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "\u5176\u4ED6\u6A21\u64EC\u5668\u8A2D\u5099",
  badge__primary: "\u4E3B\u8981",
  badge__renewsAt: void 0,
  badge__requiresAction: "\u9700\u8981\u64CD\u4F5C",
  badge__startsAt: void 0,
  badge__thisDevice: "\u6B64\u8A2D\u5099",
  badge__unverified: "\u672A\u9A57\u8B49",
  badge__upcomingPlan: void 0,
  badge__userDevice: "\u7528\u6236\u8A2D\u5099",
  badge__you: "\u60A8",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      pastDueNotice: void 0,
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    credit: void 0,
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDue: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "\u5275\u5EFA\u7D44\u7E54",
    invitePage: {
      formButtonReset: "\u8DF3\u904E"
    },
    title: "\u5275\u5EFA\u7D44\u7E54"
  },
  dates: {
    lastDay: "\u6628\u5929{{ date | timeString('zh-TW') }}",
    next6Days: "{{ date | weekday('zh-TW','long') }} {{ date | timeString('zh-TW') }}",
    nextDay: "\u660E\u5929{{ date | timeString('zh-TW') }}",
    numeric: "{{ date | numeric('zh-TW') }}",
    previous6Days: "\u4E0A\u9031{{ date | weekday('zh-TW','long') }} {{ date | timeString('zh-TW') }}",
    sameDay: "\u4ECA\u5929{{ date | timeString('zh-TW') }}"
  },
  dividerText: "\u6216\u8005",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "\u4F7F\u7528\u53E6\u4E00\u7A2E\u65B9\u6CD5",
  footerPageLink__help: "\u5E6B\u52A9",
  footerPageLink__privacy: "\u96B1\u79C1",
  footerPageLink__terms: "\u689D\u6B3E",
  formButtonPrimary: "\u7E7C\u7E8C",
  formButtonPrimary__verify: "\u6838\u5BE6",
  formFieldAction__forgotPassword: "\u5FD8\u8A18\u5BC6\u78BC\uFF1F",
  formFieldError__matchingPasswords: "\u5BC6\u78BC\u5339\u914D\u3002",
  formFieldError__notMatchingPasswords: "\u5BC6\u78BC\u4E0D\u5339\u914D\u3002",
  formFieldError__verificationLinkExpired: "\u9A57\u8B49\u9023\u7D50\u5DF2\u904E\u671F\u3002\u8ACB\u8ACB\u6C42\u65B0\u7684\u9023\u7D50\u3002",
  formFieldHintText__optional: "\u9078\u586B",
  formFieldHintText__slug: "slug \u662F\u4EBA\u985E\u53EF\u8B80\u7684 ID\uFF0C\u5FC5\u9808\u662F\u552F\u4E00\u7684\u3002\u5B83\u7D93\u5E38\u5728 URL \u4E2D\u4F7F\u7528\u3002",
  formFieldInputPlaceholder__apiKeyDescription: void 0,
  formFieldInputPlaceholder__apiKeyExpirationDate: void 0,
  formFieldInputPlaceholder__apiKeyName: void 0,
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "\u522A\u9664\u5E33\u6236",
  formFieldInputPlaceholder__emailAddress: void 0,
  formFieldInputPlaceholder__emailAddress_username: void 0,
  formFieldInputPlaceholder__emailAddresses: "\u8F38\u5165\u6216\u9ECF\u8CBC\u4E00\u500B\u6216\u591A\u500B\u96FB\u5B50\u90F5\u4EF6\u5730\u5740\uFF0C\u7528\u7A7A\u683C\u6216\u9017\u865F\u5206\u9694",
  formFieldInputPlaceholder__firstName: void 0,
  formFieldInputPlaceholder__lastName: void 0,
  formFieldInputPlaceholder__organizationDomain: void 0,
  formFieldInputPlaceholder__organizationDomainEmailAddress: void 0,
  formFieldInputPlaceholder__organizationName: void 0,
  formFieldInputPlaceholder__organizationSlug: void 0,
  formFieldInputPlaceholder__password: void 0,
  formFieldInputPlaceholder__phoneNumber: void 0,
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__apiKeyDescription: void 0,
  formFieldLabel__apiKeyExpiration: void 0,
  formFieldLabel__apiKeyName: void 0,
  formFieldLabel__automaticInvitations: "\u70BA\u8A72\u7DB2\u57DF\u555F\u7528\u81EA\u52D5\u9080\u8ACB",
  formFieldLabel__backupCode: "\u5099\u7528\u4EE3\u78BC",
  formFieldLabel__confirmDeletion: "\u78BA\u5B9A",
  formFieldLabel__confirmPassword: "\u78BA\u8A8D\u5BC6\u78BC",
  formFieldLabel__currentPassword: "\u7576\u524D\u5BC6\u78BC",
  formFieldLabel__emailAddress: "\u96FB\u5B50\u90F5\u4EF6\u5730\u5740",
  formFieldLabel__emailAddress_username: "\u96FB\u5B50\u90F5\u4EF6\u5730\u5740\u6216\u4F7F\u7528\u8005\u540D\u7A31",
  formFieldLabel__emailAddresses: "\u96FB\u5B50\u90F5\u4EF6\u5730\u5740",
  formFieldLabel__firstName: "\u540D\u5B57",
  formFieldLabel__lastName: "\u59D3\u6C0F",
  formFieldLabel__newPassword: "\u65B0\u5BC6\u78BC",
  formFieldLabel__organizationDomain: "\u9818\u57DF",
  formFieldLabel__organizationDomainDeletePending: "\u522A\u9664\u5F85\u8655\u7406\u7684\u9080\u8ACB\u548C\u5EFA\u8B70",
  formFieldLabel__organizationDomainEmailAddress: "\u9A57\u8B49\u96FB\u5B50\u90F5\u4EF6\u5730\u5740",
  formFieldLabel__organizationDomainEmailAddressDescription: "\u8F38\u5165\u6B64\u7DB2\u57DF\u4E0B\u7684\u96FB\u5B50\u90F5\u4EF6\u5730\u5740\u4EE5\u63A5\u6536\u4EE3\u78BC\u4E26\u9A57\u8B49\u6B64\u7DB2\u57DF\u540D\u7A31\u3002",
  formFieldLabel__organizationName: "\u7D44\u7E54\u540D\u7A31",
  formFieldLabel__organizationSlug: "URL \u7C21\u7A31",
  formFieldLabel__passkeyName: void 0,
  formFieldLabel__password: "\u5BC6\u78BC",
  formFieldLabel__phoneNumber: "\u96FB\u8A71\u865F\u78BC",
  formFieldLabel__role: "\u89D2\u8272",
  formFieldLabel__signOutOfOtherSessions: "\u767B\u51FA\u6240\u6709\u5176\u4ED6\u8A2D\u5099",
  formFieldLabel__username: "\u4F7F\u7528\u8005\u540D\u7A31",
  impersonationFab: {
    action__signOut: "\u9000\u51FA\u767B\u9304",
    title: "\u4EE5 {{identifier}} \u767B\u9304"
  },
  maintenanceMode: void 0,
  membershipRole__admin: "\u7BA1\u7406\u54E1",
  membershipRole__basicMember: "\u6210\u54E1",
  membershipRole__guestMember: "\u8A2A\u5BA2",
  organizationList: {
    action__createOrganization: "\u5275\u5EFA\u7D44\u7E54",
    action__invitationAccept: "\u52A0\u5165",
    action__suggestionsAccept: "\u7533\u8ACB\u52A0\u5165",
    createOrganization: "\u5275\u5EFA\u7D44\u7E54",
    invitationAcceptedLabel: "\u5DF2\u52A0\u5165",
    subtitle: "\u7E7C\u7E8C {{applicationName}}",
    suggestionsAcceptedLabel: "\u5F85\u6279\u51C6",
    title: "\u9078\u64C7\u4E00\u500B\u5E33\u6236",
    titleWithoutPersonal: "\u9078\u64C7\u4E00\u500B\u7D44\u7E54"
  },
  organizationProfile: {
    apiKeysPage: {
      title: void 0
    },
    badge__automaticInvitation: "\u81EA\u52D5\u9080\u8ACB",
    badge__automaticSuggestion: "\u81EA\u52D5\u5EFA\u8B70",
    badge__manualInvitation: "\u4E0D\u81EA\u52D5\u8A3B\u518A",
    badge__unverified: "\u672A\u7D93\u9A57\u8B49",
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "\u65B0\u589E\u8981\u9A57\u8B49\u7684\u7DB2\u57DF\u3002\u64C1\u6709\u6B64\u7DB2\u57DF\u96FB\u5B50\u90F5\u4EF6\u5730\u5740\u7684\u4F7F\u7528\u8005\u53EF\u4EE5\u81EA\u52D5\u52A0\u5165\u8A72\u7D44\u7E54\u6216\u8981\u6C42\u52A0\u5165\u3002",
      title: "\u65B0\u589E\u7DB2\u57DF"
    },
    invitePage: {
      detailsTitle__inviteFailed: "\u9080\u8ACB\u7121\u6CD5\u767C\u9001\u3002\u4FEE\u8986\u4EE5\u4E0B\u554F\u984C\u7136\u5F8C\u91CD\u8A66\uFF1A",
      formButtonPrimary__continue: "\u767C\u9001\u9080\u8ACB",
      selectDropdown__role: "\u9078\u64C7\u89D2\u8272",
      subtitle: "\u9080\u8ACB\u65B0\u6210\u54E1\u52A0\u5165\u6B64\u7D44\u7E54",
      successMessage: "\u9080\u8ACB\u6210\u529F\u767C\u9001",
      title: "\u9080\u8ACB\u6210\u54E1"
    },
    membersPage: {
      action__invite: "\u9080\u8ACB",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "\u79FB\u9664\u6210\u54E1",
        tableHeader__actions: void 0,
        tableHeader__joined: "\u52A0\u5165",
        tableHeader__role: "\u89D2\u8272",
        tableHeader__user: "\u7528\u6236"
      },
      detailsTitle__emptyRow: "\u6C92\u6709\u53EF\u986F\u793A\u7684\u6210\u54E1",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "\u900F\u904E\u5C07\u96FB\u5B50\u90F5\u4EF6\u7DB2\u57DF\u8207\u60A8\u7684\u7D44\u7E54\u9023\u63A5\u4F86\u9080\u8ACB\u4F7F\u7528\u8005\u3002\u4EFB\u4F55\u4F7F\u7528\u5339\u914D\u96FB\u5B50\u90F5\u4EF6\u7DB2\u57DF\u8A3B\u518A\u7684\u4EBA\u90FD\u53EF\u4EE5\u96A8\u6642\u52A0\u5165\u8A72\u7D44\u7E54\u3002",
          headerTitle: "\u81EA\u52D5\u9080\u8ACB",
          primaryButton: "\u7BA1\u7406\u5DF2\u9A57\u8B49\u7684\u57DF\u540D"
        },
        table__emptyRow: "\u6C92\u6709\u53EF\u986F\u793A\u7684\u9080\u8ACB"
      },
      invitedMembersTab: {
        menuAction__revoke: "\u64A4\u92B7\u9080\u8ACB",
        tableHeader__invited: "\u5DF2\u9080\u8ACB"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "\u4F7F\u7528\u7B26\u5408\u7684\u96FB\u5B50\u90F5\u4EF6\u7DB2\u57DF\u8A3B\u518A\u7684\u7528\u6236\u5C07\u80FD\u5920\u770B\u5230\u8ACB\u6C42\u52A0\u5165\u60A8\u7684\u7D44\u7E54\u7684\u5EFA\u8B70\u3002",
          headerTitle: "\u81EA\u52D5\u5EFA\u8B70",
          primaryButton: "\u7BA1\u7406\u5DF2\u9A57\u8B49\u7684\u57DF\u540D"
        },
        menuAction__approve: "\u6279\u51C6",
        menuAction__reject: "\u62D2\u7D55",
        tableHeader__requested: "\u8ACB\u6C42\u5B58\u53D6\u6B0A\u9650",
        table__emptyRow: "\u6C92\u6709\u986F\u793A\u8ACB\u6C42"
      },
      start: {
        headerTitle__invitations: "\u9080\u8ACB\u51FD",
        headerTitle__members: "\u6210\u54E1",
        headerTitle__requests: "\u8ACB\u6C42"
      }
    },
    navbar: {
      apiKeys: void 0,
      billing: void 0,
      description: "\u7BA1\u7406\u60A8\u7684\u7D44\u7E54\u3002",
      general: "\u4E00\u822C\u7684",
      members: "\u6210\u54E1",
      title: "\u7D44\u7E54"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: '\u985E\u578B "{{organizationName}}" \u4E0B\u9762\u7E7C\u7E8C\u3002',
          messageLine1: "\u60A8\u78BA\u5B9A\u8981\u522A\u9664\u8A72\u7D44\u7E54\u55CE\uFF1F",
          messageLine2: "\u6B64\u64CD\u4F5C\u662F\u6C38\u4E45\u6027\u7684\u4E14\u4E0D\u53EF\u9006\u8F49\u7684\u3002",
          successMessage: "\u60A8\u5DF2\u522A\u9664\u8A72\u7D44\u7E54\u3002",
          title: "\u522A\u9664\u7D44\u7E54"
        },
        leaveOrganization: {
          actionDescription: '\u985E\u578B "{{organizationName}}" \u4E0B\u9762\u7E7C\u7E8C\u3002',
          messageLine1: "\u60A8\u78BA\u5B9A\u8981\u96E2\u958B\u6B64\u7D44\u7E54\u55CE\uFF1F\u60A8\u5C07\u5931\u53BB\u5C0D\u6B64\u7D44\u7E54\u53CA\u5176\u61C9\u7528\u7A0B\u5F0F\u7684\u8A2A\u554F\u6B0A\u9650\u3002",
          messageLine2: "\u6B64\u64CD\u4F5C\u662F\u6C38\u4E45\u6027\u7684\u4E14\u7121\u6CD5\u64A4\u92B7\u3002",
          successMessage: "\u60A8\u5DF2\u96E2\u958B\u4E86\u7D44\u7E54\u3002",
          title: "\u96E2\u958B\u7D44\u7E54"
        },
        title: "\u5371\u96AA"
      },
      domainSection: {
        menuAction__manage: "\u7BA1\u7406",
        menuAction__remove: "\u522A\u9664",
        menuAction__verify: "\u6838\u5BE6",
        primaryButton: "\u65B0\u589E\u7DB2\u57DF",
        subtitle: "\u5141\u8A31\u4F7F\u7528\u8005\u81EA\u52D5\u52A0\u5165\u7D44\u7E54\u6216\u6839\u64DA\u5DF2\u9A57\u8B49\u7684\u96FB\u5B50\u90F5\u4EF6\u7DB2\u57DF\u8ACB\u6C42\u52A0\u5165\u3002",
        title: "\u5DF2\u9A57\u8B49\u57DF\u540D"
      },
      successMessage: "\u7D44\u7E54\u5DF2\u66F4\u65B0\u3002",
      title: "\u7D44\u7E54\u7C21\u4ECB"
    },
    removeDomainPage: {
      messageLine1: "\u96FB\u5B50\u90F5\u4EF6\u57DF\u540D {{domain}} \u5C07\u88AB\u522A\u9664\u3002",
      messageLine2: "\u6B64\u5F8C\u7528\u6236\u5C07\u7121\u6CD5\u81EA\u52D5\u52A0\u5165\u8A72\u7D44\u7E54\u3002",
      successMessage: "{{domain}} \u5DF2\u522A\u9664\u3002",
      title: "\u522A\u9664\u7DB2\u57DF"
    },
    start: {
      headerTitle__general: "\u4E00\u822C\u7684",
      headerTitle__members: "\u6210\u54E1",
      profileSection: {
        primaryButton: void 0,
        title: "\u7D44\u7E54\u7C21\u4ECB",
        uploadAction__title: "\u6A19\u8B58"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "\u522A\u9664\u6B64\u7DB2\u57DF\u5C07\u5F71\u97FF\u53D7\u9080\u4F7F\u7528\u8005\u3002",
        removeDomainActionLabel__remove: "\u522A\u9664\u7DB2\u57DF",
        removeDomainSubtitle: "\u5F9E\u60A8\u7684\u5DF2\u9A57\u8B49\u57DF\u540D\u4E2D\u79FB\u9664\u6B64\u57DF\u540D",
        removeDomainTitle: "\u522A\u9664\u7DB2\u57DF"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "\u7528\u6236\u8A3B\u518A\u6642\u6703\u81EA\u52D5\u88AB\u9080\u8ACB\u52A0\u5165\u8A72\u7D44\u7E54\uFF0C\u4E26\u4E14\u53EF\u4EE5\u96A8\u6642\u52A0\u5165\u3002",
        automaticInvitationOption__label: "\u81EA\u52D5\u9080\u8ACB",
        automaticSuggestionOption__description: "\u4F7F\u7528\u8005\u6703\u6536\u5230\u52A0\u5165\u8ACB\u6C42\u7684\u5EFA\u8B70\uFF0C\u4F46\u5FC5\u9808\u5F97\u5230\u7BA1\u7406\u54E1\u7684\u6279\u51C6\u624D\u80FD\u52A0\u5165\u7D44\u7E54\u3002",
        automaticSuggestionOption__label: "\u81EA\u52D5\u5EFA\u8B70",
        calloutInfoLabel: "\u66F4\u6539\u8A3B\u518A\u6A21\u5F0F\u53EA\u6703\u5F71\u97FF\u65B0\u7528\u6236\u3002",
        calloutInvitationCountLabel: "\u5DF2\u5411\u7528\u6236\u767C\u9001\u5F85\u8655\u7406\u7684\u9080\u8ACB: {{count}}",
        calloutSuggestionCountLabel: "\u5DF2\u5C07\u5F85\u8655\u7406\u7684\u5EFA\u8B70\u767C\u9001\u7D66\u7528\u6236: {{count}}",
        manualInvitationOption__description: "\u53EA\u80FD\u624B\u52D5\u9080\u8ACB\u4F7F\u7528\u8005\u52A0\u5165\u7D44\u7E54\u3002",
        manualInvitationOption__label: "\u4E0D\u81EA\u52D5\u8A3B\u518A",
        subtitle: "\u9078\u64C7\u6B64\u7DB2\u57DF\u4E2D\u7684\u4F7F\u7528\u8005\u52A0\u5165\u7D44\u7E54\u7684\u65B9\u5F0F\u3002"
      },
      start: {
        headerTitle__danger: "\u5371\u96AA",
        headerTitle__enrollment: "\u8A3B\u518A\u9078\u9805"
      },
      subtitle: "\u7DB2\u57DF {{domain}} \u73FE\u5DF2\u9A57\u8B49\u3002\u9078\u64C7\u8A3B\u518A\u6A21\u5F0F\u7E7C\u7E8C\u3002",
      title: "\u66F4\u65B0 {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "\u8F38\u5165\u767C\u9001\u5230\u60A8\u4FE1\u7BB1\u7684\u9A57\u8B49\u78BC",
      formTitle: "\u9A57\u8B49\u78BC",
      resendButton: "\u6C92\u6709\u6536\u5230\u4EE3\u78BC\uFF1F\u91CD\u65B0\u767C\u9001",
      subtitle: "\u7DB2\u57DF {{domainName}} \u9700\u8981\u900F\u904E\u96FB\u5B50\u90F5\u4EF6\u9032\u884C\u9A57\u8B49\u3002",
      subtitleVerificationCodeScreen: "\u9A57\u8B49\u78BC\u5DF2\u767C\u9001\u81F3 {{emailAddress}}\u3002\u8F38\u5165\u4EE3\u78BC\u4EE5\u7E7C\u7E8C\u3002",
      title: "\u9A57\u8B49\u57DF\u540D"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "\u5275\u5EFA\u7D44\u7E54",
    action__invitationAccept: "\u52A0\u5165",
    action__manageOrganization: "\u7BA1\u7406\u7D44\u7E54",
    action__suggestionsAccept: "\u7533\u8ACB\u52A0\u5165",
    notSelected: "\u672A\u9078\u64C7\u7D44\u7E54",
    personalWorkspace: "\u500B\u4EBA\u5DE5\u4F5C\u5340",
    suggestionsAcceptedLabel: "\u5F85\u6279\u51C6"
  },
  paginationButton__next: "\u4E0B\u4E00\u9801",
  paginationButton__previous: "\u4E0A\u4E00\u9801",
  paginationRowText__displaying: "\u986F\u793A",
  paginationRowText__of: "\u7684",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "\u65B0\u589E\u5E33\u6236",
      action__signOutAll: "\u767B\u51FA\u6240\u6709\u5E33\u6236",
      subtitle: "\u9078\u64C7\u60A8\u60F3\u8981\u7E7C\u7E8C\u4F7F\u7528\u7684\u5E33\u6236\u3002",
      title: "\u9078\u64C7\u4E00\u500B\u5E33\u6236"
    },
    alternativeMethods: {
      actionLink: "\u7372\u53D6\u5E6B\u52A9",
      actionText: "\u9019\u4E9B\u90FD\u6C92\u6709\u55CE\uFF1F",
      blockButton__backupCode: "\u4F7F\u7528\u5099\u7528\u4EE3\u78BC",
      blockButton__emailCode: "\u96FB\u5B50\u90F5\u4EF6\u9A57\u8B49\u78BC\u5230 {{identifier}}",
      blockButton__emailLink: "\u96FB\u5B50\u90F5\u4EF6\u9023\u7D50\u5230 {{identifier}}",
      blockButton__passkey: void 0,
      blockButton__password: "\u4F7F\u7528\u60A8\u7684\u5BC6\u78BC\u767B\u9304",
      blockButton__phoneCode: "\u767C\u9001\u7C21\u8A0A\u4EE3\u78BC\u5230 {{identifier}}",
      blockButton__totp: "\u4F7F\u7528\u60A8\u7684\u9A57\u8B49\u61C9\u7528\u7A0B\u5F0F",
      getHelp: {
        blockButton__emailSupport: "\u90F5\u4EF6\u652F\u6301",
        content: "\u5982\u679C\u60A8\u5728\u767B\u5165\u5E33\u6236\u6642\u9047\u5230\u56F0\u96E3\uFF0C\u8ACB\u7D66\u6211\u5011\u767C\u9001\u96FB\u5B50\u90F5\u4EF6\uFF0C\u6211\u5011\u5C07\u76E1\u5FEB\u8B93\u60A8\u6062\u8986\u8A2A\u554F\u3002",
        title: "\u7372\u53D6\u5E6B\u52A9"
      },
      subtitle: "\u9047\u5230\u554F\u984C\u4E86\u55CE\uFF1F\u60A8\u53EF\u4EE5\u4F7F\u7528\u5176\u4E2D\u4EFB\u4F55\u4E00\u7A2E\u65B9\u5F0F\u767B\u5165\u3002",
      title: "\u4F7F\u7528\u5176\u4ED6\u65B9\u6CD5"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "\u7E7C\u7E8C\u4F7F\u7528 {{applicationName}}",
      title: "\u8F38\u5165\u5099\u7528\u4EE3\u78BC"
    },
    emailCode: {
      formTitle: "\u9A57\u8B49\u78BC",
      resendButton: "\u91CD\u65B0\u767C\u9001\u9A57\u8B49\u78BC",
      subtitle: "\u7E7C\u7E8C\u4F7F\u7528 {{applicationName}}",
      title: "\u67E5\u770B\u60A8\u7684\u96FB\u5B50\u90F5\u4EF6"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      expired: {
        subtitle: "\u8FD4\u56DE\u539F\u59CB\u6A19\u7C64\u9801\u7E7C\u7E8C\u3002",
        title: "\u6B64\u9A57\u8B49\u9023\u7D50\u5DF2\u904E\u671F"
      },
      failed: {
        subtitle: "\u8FD4\u56DE\u539F\u59CB\u6A19\u7C64\u9801\u7E7C\u7E8C\u3002",
        title: "\u6B64\u9A57\u8B49\u9023\u7D50\u7121\u6548"
      },
      formSubtitle: "\u4F7F\u7528\u767C\u9001\u5230\u60A8\u7684\u96FB\u5B50\u90F5\u4EF6\u7684\u9A57\u8B49\u9023\u7D50",
      formTitle: "\u9A57\u8B49\u9023\u7D50",
      loading: {
        subtitle: "\u5373\u5C07\u70BA\u60A8\u91CD\u5B9A\u5411",
        title: "\u6B63\u5728\u767B\u9304..."
      },
      resendButton: "\u91CD\u65B0\u767C\u9001\u9023\u7D50",
      subtitle: "\u7E7C\u7E8C\u4F7F\u7528 {{applicationName}}",
      title: "\u67E5\u770B\u60A8\u7684\u96FB\u5B50\u90F5\u4EF6",
      unusedTab: {
        title: "\u60A8\u53EF\u4EE5\u95DC\u9589\u6B64\u6A19\u7C64\u9801"
      },
      verified: {
        subtitle: "\u5373\u5C07\u70BA\u60A8\u91CD\u5B9A\u5411",
        title: "\u6210\u529F\u767B\u9304"
      },
      verifiedSwitchTab: {
        subtitle: "\u8FD4\u56DE\u539F\u59CB\u6A19\u7C64\u9801\u7E7C\u7E8C",
        subtitleNewTab: "\u8FD4\u56DE\u65B0\u6253\u958B\u7684\u6A19\u7C64\u9801\u7E7C\u7E8C",
        titleNewTab: "\u5728\u5176\u4ED6\u6A19\u7C64\u9801\u4E0A\u767B\u5165"
      }
    },
    forgotPassword: {
      formTitle: "\u91CD\u8A2D\u5BC6\u78BC\u4EE3\u78BC",
      resendButton: "\u91CD\u65B0\u767C\u9001\u4EE3\u78BC",
      subtitle: "\u91CD\u8A2D\u60A8\u7684\u5BC6\u78BC",
      subtitle_email: "\u9996\u5148\uFF0C\u8F38\u5165\u50B3\u9001\u5230\u60A8\u7684\u96FB\u5B50\u90F5\u4EF6 ID \u7684\u4EE3\u78BC",
      subtitle_phone: "\u9996\u5148\uFF0C\u8F38\u5165\u767C\u9001\u5230\u60A8\u624B\u6A5F\u7684\u4EE3\u78BC",
      title: "\u91CD\u8A2D\u5BC6\u78BC"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "\u91CD\u8A2D\u5BC6\u78BC",
      label__alternativeMethods: "\u6216\u8005\uFF0C\u4F7F\u7528\u5176\u4ED6\u65B9\u5F0F\u767B\u9304\u3002",
      title: "\u5FD8\u8A18\u5BC6\u78BC\uFF1F"
    },
    noAvailableMethods: {
      message: "\u7121\u6CD5\u7E7C\u7E8C\u767B\u9304\u3002\u6C92\u6709\u53EF\u7528\u7684\u8EAB\u4EFD\u9A57\u8B49\u56E0\u7D20\u3002",
      subtitle: "\u51FA\u73FE\u932F\u8AA4",
      title: "\u7121\u6CD5\u767B\u9304"
    },
    passkey: {
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "\u4F7F\u7528\u5176\u4ED6\u65B9\u6CD5",
      subtitle: "\u7E7C\u7E8C\u4F7F\u7528 {{applicationName}}",
      title: "\u8F38\u5165\u60A8\u7684\u5BC6\u78BC"
    },
    passwordPwned: {
      title: void 0
    },
    phoneCode: {
      formTitle: "\u9A57\u8B49\u78BC",
      resendButton: "\u91CD\u65B0\u767C\u9001\u9A57\u8B49\u78BC",
      subtitle: "\u7E7C\u7E8C\u4F7F\u7528 {{applicationName}}",
      title: "\u6AA2\u67E5\u624B\u6A5F\u7C21\u8A0A"
    },
    phoneCodeMfa: {
      formTitle: "\u9A57\u8B49\u78BC",
      resendButton: "\u91CD\u65B0\u767C\u9001\u9A57\u8B49\u78BC",
      subtitle: void 0,
      title: "\u6AA2\u67E5\u624B\u6A5F\u7C21\u8A0A"
    },
    resetPassword: {
      formButtonPrimary: "\u91CD\u8A2D\u5BC6\u78BC",
      requiredMessage: "\u51FA\u65BC\u5B89\u5168\u539F\u56E0\uFF0C\u9700\u8981\u91CD\u8A2D\u60A8\u7684\u5BC6\u78BC\u3002",
      successMessage: "\u60A8\u7684\u5BC6\u78BC\u5DF2\u6210\u529F\u66F4\u6539\u3002\u6B63\u5728\u70BA\u60A8\u767B\u9304\uFF0C\u8ACB\u7A0D\u7B49\u3002",
      title: "\u91CD\u8A2D\u5BC6\u78BC"
    },
    resetPasswordMfa: {
      detailsLabel: "\u6211\u5011\u9700\u8981\u9A57\u8B49\u60A8\u7684\u8EAB\u4EFD\u624D\u80FD\u91CD\u8A2D\u60A8\u7684\u5BC6\u78BC\u3002"
    },
    start: {
      actionLink: "\u8A3B\u518A",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "\u4F7F\u7528\u96FB\u5B50\u90F5\u4EF6",
      actionLink__use_email_username: "\u4F7F\u7528\u96FB\u5B50\u90F5\u4EF6\u6216\u4F7F\u7528\u8005\u540D\u7A31",
      actionLink__use_passkey: void 0,
      actionLink__use_phone: "\u4F7F\u7528\u96FB\u8A71",
      actionLink__use_username: "\u4F7F\u7528\u4F7F\u7528\u8005\u540D\u7A31",
      actionText: "\u9084\u6C92\u6709\u5E33\u6236\uFF1F",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "\u7E7C\u7E8C\u4F7F\u7528 {{applicationName}}",
      subtitleCombined: void 0,
      title: "\u767B\u9304",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "\u9A57\u8B49\u78BC",
      subtitle: void 0,
      title: "\u5169\u6B65\u9A57\u8B49"
    }
  },
  signInEnterPasswordTitle: "\u8F38\u5165\u60A8\u7684\u5BC6\u78BC",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "\u767B\u9304",
      actionText: "\u5DF2\u7D93\u6709\u5E33\u6236\u4E86\uFF1F",
      subtitle: "\u7E7C\u7E8C\u4F7F\u7528 {{applicationName}}",
      title: "\u586B\u5BEB\u7F3A\u5C11\u7684\u6B04\u4F4D"
    },
    emailCode: {
      formSubtitle: "\u8F38\u5165\u767C\u9001\u5230\u60A8\u7684\u96FB\u5B50\u90F5\u4EF6\u5730\u5740\u7684\u9A57\u8B49\u78BC",
      formTitle: "\u9A57\u8B49\u78BC",
      resendButton: "\u91CD\u65B0\u767C\u9001\u9A57\u8B49\u78BC",
      subtitle: "\u7E7C\u7E8C\u4F7F\u7528 {{applicationName}}",
      title: "\u9A57\u8B49\u60A8\u7684\u96FB\u5B50\u90F5\u4EF6"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      formSubtitle: "\u4F7F\u7528\u767C\u9001\u5230\u60A8\u7684\u96FB\u5B50\u90F5\u4EF6\u5730\u5740\u7684\u9A57\u8B49\u9023\u7D50",
      formTitle: "\u9A57\u8B49\u9023\u7D50",
      loading: {
        title: "\u6B63\u5728\u8A3B\u518A..."
      },
      resendButton: "\u91CD\u65B0\u767C\u9001\u9023\u7D50",
      subtitle: "\u7E7C\u7E8C\u4F7F\u7528 {{applicationName}}",
      title: "\u9A57\u8B49\u60A8\u7684\u96FB\u5B50\u90F5\u4EF6",
      verified: {
        title: "\u6210\u529F\u8A3B\u518A"
      },
      verifiedSwitchTab: {
        subtitle: "\u8FD4\u56DE\u65B0\u6253\u958B\u7684\u6A19\u7C64\u9801\u7E7C\u7E8C",
        subtitleNewTab: "\u8FD4\u56DE\u4E0A\u4E00\u500B\u6A19\u7C64\u9801\u7E7C\u7E8C",
        title: "\u6210\u529F\u9A57\u8B49\u96FB\u5B50\u90F5\u4EF6"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: '\u6211\u540C\u610F {{ privacyPolicyLink || link("\u96B1\u79C1\u689D\u6B3E") }}',
        label__onlyTermsOfService: '\u6211\u540C\u610F {{ termsOfServiceLink || link("\u4F7F\u7528\u689D\u6B3E") }}',
        label__termsOfServiceAndPrivacyPolicy: '\u6211\u540C\u610F {{ termsOfServiceLink || link("\u4F7F\u7528\u689D\u6B3E") }} \u548C {{ privacyPolicyLink || link("\u96B1\u79C1\u689D\u6B3E") }}'
      },
      continue: {
        subtitle: "\u8ACB\u95B1\u8B80\u4E26\u63A5\u53D7\u689D\u6B3E\u4EE5\u7E7C\u7E8C",
        title: "\u540C\u610F\u689D\u6B3E"
      }
    },
    phoneCode: {
      formSubtitle: "\u8F38\u5165\u767C\u9001\u5230\u60A8\u7684\u96FB\u8A71\u865F\u78BC\u7684\u9A57\u8B49\u78BC",
      formTitle: "\u9A57\u8B49\u78BC",
      resendButton: "\u91CD\u65B0\u767C\u9001\u9A57\u8B49\u78BC",
      subtitle: "\u7E7C\u7E8C\u4F7F\u7528 {{applicationName}}",
      title: "\u9A57\u8B49\u60A8\u7684\u96FB\u8A71"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "\u767B\u9304",
      actionLink__use_email: void 0,
      actionLink__use_phone: void 0,
      actionText: "\u5DF2\u7D93\u6709\u5E33\u6236\u4E86\uFF1F",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "\u7E7C\u7E8C\u4F7F\u7528 {{applicationName}}",
      subtitleCombined: "\u7E7C\u7E8C\u4F7F\u7528 {{applicationName}}",
      title: "\u5275\u5EFA\u60A8\u7684\u5E33\u6236",
      titleCombined: "\u5275\u5EFA\u60A8\u7684\u5E33\u6236"
    }
  },
  socialButtonsBlockButton: "\u4F7F\u7528 {{provider|titleize}} \u767B\u9304",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "\u7531\u65BC\u5B89\u5168\u9A57\u8B49\u5931\u6557\uFF0C\u8A3B\u518A\u672A\u6210\u529F\u3002\u8ACB\u5237\u65B0\u9801\u9762\u91CD\u8A66\u6216\u806F\u7D61\u652F\u6301\u7372\u53D6\u66F4\u591A\u5E6B\u52A9\u3002",
    captcha_unavailable: "\u7531\u65BC\u6A5F\u5668\u4EBA\u9A57\u8B49\u5931\u6557\uFF0C\u8A3B\u518A\u5931\u6557\u3002\u8ACB\u91CD\u65B0\u6574\u7406\u9801\u9762\u91CD\u8A66\u6216\u806F\u7D61\u652F\u63F4\u4EBA\u54E1\u4EE5\u53D6\u5F97\u66F4\u591A\u5354\u52A9\u3002",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: void 0,
    form_identifier_exists__phone_number: void 0,
    form_identifier_exists__username: void 0,
    form_identifier_not_found: "\u6211\u5011\u7121\u6CD5\u627E\u5230\u5177\u6709\u9019\u4E9B\u4FE1\u606F\u7684\u5E33\u6236\u3002",
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: "\u96FB\u5B50\u90F5\u4EF6\u5730\u5740\u5FC5\u9808\u662F\u6709\u6548\u7684\u96FB\u5B50\u90F5\u4EF6\u5730\u5740\u3002",
    form_param_format_invalid__phone_number: "\u96FB\u8A71\u865F\u78BC\u5FC5\u9808\u63A1\u7528\u6709\u6548\u7684\u570B\u969B\u683C\u5F0F\u3002",
    form_param_max_length_exceeded__first_name: "\u540D\u5B57\u4E0D\u8D85\u904E 256 \u500B\u5B57\u5143\u3002",
    form_param_max_length_exceeded__last_name: "\u59D3\u6C0F\u4E0D\u5F97\u8D85\u904E 256 \u500B\u5B57\u5143\u3002",
    form_param_max_length_exceeded__name: "\u540D\u7A31\u4E0D\u61C9\u8D85\u904E 256 \u500B\u5B57\u5143\u3002",
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: void 0,
    form_password_not_strong_enough: "\u60A8\u7684\u5BC6\u78BC\u5F37\u5EA6\u4E0D\u5920\u3002",
    form_password_pwned: "\u9019\u500B\u5BC6\u78BC\u5728\u6578\u64DA\u6D29\u9732\u4E2D\u88AB\u767C\u73FE\uFF0C\u4E0D\u80FD\u4F7F\u7528\uFF0C\u8ACB\u63DB\u4E00\u500B\u5BC6\u78BC\u8A66\u8A66\u3002",
    form_password_pwned__sign_in: void 0,
    form_password_size_in_bytes_exceeded: "\u60A8\u7684\u5BC6\u78BC\u8D85\u904E\u4E86\u5141\u8A31\u7684\u6700\u5927\u4F4D\u5143\u7D44\u6578\uFF0C\u8ACB\u7E2E\u77ED\u5B83\u6216\u53BB\u6389\u4E00\u4E9B\u7279\u6B8A\u5B57\u5143\u3002",
    form_password_validation_failed: "\u5BC6\u78BC\u932F\u8AA4",
    form_username_invalid_character: void 0,
    form_username_invalid_length: void 0,
    identification_deletion_failed: "\u60A8\u7121\u6CD5\u522A\u9664\u60A8\u7684\u6700\u5F8C\u4E00\u500B\u8EAB\u5206\u8B49\u660E\u3002",
    not_allowed_access: "\u60A8\u4F7F\u7528\u7684\u96FB\u5B50\u90F5\u4EF6\u5730\u5740\u6216\u96FB\u8A71\u865F\u78BC\u4E0D\u5141\u8A31\u8A3B\u518A\u3002\u9019\u53EF\u80FD\u56E0\u70BA\u60A8\u5728\u96FB\u5B50\u90F5\u4EF6\u5730\u5740\u4E2D\u4F7F\u7528\u4E86 '+', '=', '#' \u6216 '.'\uFF0C\u4F7F\u7528\u4E86\u8207\u81E8\u6642\u96FB\u5B50\u90F5\u4EF6\u670D\u52D9\u95DC\u806F\u7684\u57DF\u540D\uFF0C\u6216\u8005\u6709\u660E\u78BA\u7684\u6392\u9664\u3002\u5982\u679C\u60A8\u8A8D\u70BA\u9019\u662F\u932F\u8AA4\uFF0C\u8ACB\u806F\u7D61\u652F\u6301\u3002",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: void 0,
    passkey_not_supported: void 0,
    passkey_pa_not_supported: void 0,
    passkey_registration_cancelled: void 0,
    passkey_retrieval_cancelled: void 0,
    passwordComplexity: {
      maximumLength: "\u5C11\u65BC{{length}}\u500B\u5B57\u5143",
      minimumLength: "{{length}}\u500B\u6216\u66F4\u591A\u5B57\u5143",
      requireLowercase: "\u4E00\u500B\u5C0F\u5BEB\u5B57\u6BCD",
      requireNumbers: "\u4E00\u500B\u6578\u5B57",
      requireSpecialCharacter: "\u4E00\u500B\u7279\u6B8A\u5B57\u5143",
      requireUppercase: "\u4E00\u500B\u5927\u5BEB\u5B57\u6BCD",
      sentencePrefix: "\u60A8\u7684\u5BC6\u78BC\u5FC5\u9808\u5305\u542B"
    },
    phone_number_exists: "\u9019\u500B\u96FB\u8A71\u865F\u78BC\u5DF2\u88AB\u4F7F\u7528\u3002\u8ACB\u5617\u8A66\u53E6\u4E00\u500B\u3002",
    session_exists: "\u60A8\u5DF2\u7D93\u767B\u9304\u3002",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "\u60A8\u7684\u5BC6\u78BC\u53EF\u4EE5\u7528\uFF0C\u4F46\u53EF\u4EE5\u66F4\u5F37\u3002\u8A66\u8457\u6DFB\u52A0\u66F4\u591A\u5B57\u5143\u3002",
      goodPassword: "\u505A\u5F97\u597D\u3002\u9019\u662F\u4E00\u500B\u512A\u79C0\u7684\u5BC6\u78BC\u3002",
      notEnough: "\u60A8\u7684\u5BC6\u78BC\u5F37\u5EA6\u4E0D\u5920\u3002",
      suggestions: {
        allUppercase: "\u5927\u5BEB\u4E00\u4E9B\uFF0C\u4F46\u4E0D\u662F\u6240\u6709\u7684\u5B57\u6BCD\u3002",
        anotherWord: "\u6DFB\u52A0\u66F4\u4E0D\u5E38\u898B\u7684\u66F4\u591A\u55AE\u5B57\u3002",
        associatedYears: "\u907F\u514D\u8207\u4F60\u6709\u95DC\u7684\u5E74\u4EFD\u3002",
        capitalization: "\u5927\u5BEB\u4E0D\u50C5\u50C5\u662F\u7B2C\u4E00\u500B\u5B57\u6BCD\u3002",
        dates: "\u907F\u514D\u8207\u4F60\u6709\u95DC\u7684\u65E5\u671F\u548C\u5E74\u4EFD\u3002",
        l33t: '\u907F\u514D\u9810\u6E2C\u7684\u5B57\u6BCD\u66FF\u63DB\uFF0C\u5982"@"\u4EE3\u66FF"a"\u3002',
        longerKeyboardPattern: "\u4F7F\u7528\u66F4\u9577\u7684\u9375\u76E4\u6A21\u5F0F\uFF0C\u4E26\u591A\u6B21\u6539\u8B8A\u6253\u5B57\u65B9\u5411\u3002",
        noNeed: "\u4F60\u53EF\u4EE5\u5275\u5EFA\u5F37\u5BC6\u78BC\uFF0C\u800C\u7121\u9700\u4F7F\u7528\u7B26\u865F\uFF0C\u6578\u5B57\u6216\u5927\u5BEB\u5B57\u6BCD\u3002",
        pwned: "\u5982\u679C\u60A8\u5728\u5176\u4ED6\u5730\u65B9\u4F7F\u7528\u6B64\u5BC6\u78BC\uFF0C\u60A8\u61C9\u8A72\u66F4\u6539\u5B83\u3002",
        recentYears: "\u907F\u514D\u8FD1\u5E74\u4F86\u3002",
        repeated: "\u907F\u514D\u91CD\u8907\u7684\u55AE\u5B57\u548C\u5B57\u5143\u3002",
        reverseWords: "\u907F\u514D\u5E38\u7528\u8A5E\u7684\u53CD\u5411\u62FC\u5BEB\u3002",
        sequences: "\u907F\u514D\u5E38\u898B\u5B57\u5143\u5E8F\u5217\u3002",
        useWords: "\u4F7F\u7528\u591A\u500B\u55AE\u5B57\uFF0C\u4F46\u907F\u514D\u5E38\u898B\u77ED\u8A9E\u3002"
      },
      warnings: {
        common: "\u9019\u662F\u4E00\u500B\u5E38\u7528\u7684\u5BC6\u78BC\u3002",
        commonNames: "\u5E38\u898B\u7684\u540D\u5B57\u548C\u59D3\u6C0F\u6613\u88AB\u731C\u5230\u3002",
        dates: "\u65E5\u671F\u6613\u88AB\u731C\u5230\u3002",
        extendedRepeat: '\u50CF"abcabcabc"\u9019\u6A23\u7684\u91CD\u8907\u5B57\u5143\u6A21\u5F0F\u6613\u88AB\u731C\u5230\u3002',
        keyPattern: "\u77ED\u9375\u76E4\u6A21\u5F0F\u6613\u88AB\u731C\u5230\u3002",
        namesByThemselves: "\u55AE\u500B\u540D\u5B57\u6216\u59D3\u6C0F\u6613\u88AB\u731C\u5230\u3002",
        pwned: "\u60A8\u7684\u5BC6\u78BC\u5728\u7DB2\u8DEF\u4E0A\u7684\u6578\u64DA\u6D29\u9732\u4E2D\u88AB\u66B4\u9732\u3002",
        recentYears: "\u8FD1\u5E74\u4F86\u6613\u88AB\u731C\u5230\u3002",
        sequences: '\u50CF"abc"\u9019\u6A23\u7684\u5E38\u898B\u5B57\u5143\u5E8F\u5217\u6613\u88AB\u731C\u5230\u3002',
        similarToCommon: "\u9019\u500B\u5BC6\u78BC\u548C\u5E38\u7528\u5BC6\u78BC\u76F8\u4F3C\u3002",
        simpleRepeat: '\u50CF"aaa"\u9019\u6A23\u7684\u91CD\u8907\u5B57\u5143\u6613\u88AB\u731C\u5230\u3002',
        straightRow: "\u9375\u76E4\u4E0A\u7684\u76F4\u884C\u9375\u6613\u88AB\u731C\u5230\u3002",
        topHundred: "\u9019\u662F\u4E00\u500B\u983B\u7E41\u4F7F\u7528\u7684\u5BC6\u78BC\u3002",
        topTen: "\u9019\u662F\u4E00\u500B\u5927\u91CF\u4F7F\u7528\u7684\u5BC6\u78BC\u3002",
        userInputs: "\u4E0D\u61C9\u8A72\u6709\u4EFB\u4F55\u500B\u4EBA\u6216\u9801\u9762\u76F8\u95DC\u7684\u6578\u64DA\u3002",
        wordByItself: "\u55AE\u500B\u55AE\u5B57\u6613\u88AB\u731C\u5230\u3002"
      }
    }
  },
  userButton: {
    action__addAccount: "\u6DFB\u52A0\u5E33\u6236",
    action__manageAccount: "\u7BA1\u7406\u5E33\u6236",
    action__signOut: "\u9000\u51FA\u767B\u9304",
    action__signOutAll: "\u9000\u51FA\u6240\u6709\u5E33\u6236"
  },
  userProfile: {
    apiKeysPage: {
      title: void 0
    },
    backupCodePage: {
      actionLabel__copied: "\u5DF2\u8907\u88FD\uFF01",
      actionLabel__copy: "\u8907\u88FD\u5168\u90E8",
      actionLabel__download: "\u4E0B\u8F09 .txt",
      actionLabel__print: "\u5217\u5370",
      infoText1: "\u5C07\u70BA\u6B64\u5E33\u6236\u555F\u7528\u5099\u4EFD\u4EE3\u78BC\u3002",
      infoText2: "\u4FDD\u5BC6\u4E26\u5B89\u5168\u5132\u5B58\u5099\u4EFD\u4EE3\u78BC\u3002\u5982\u679C\u60A8\u61F7\u7591\u5B83\u5011\u5DF2\u7D93\u6D29\u9732\uFF0C\u60A8\u53EF\u4EE5\u91CD\u65B0\u751F\u6210\u5099\u4EFD\u4EE3\u78BC\u3002",
      subtitle__codelist: "\u5B89\u5168\u5132\u5B58\u4E26\u4FDD\u5B88\u79D8\u5BC6\u3002",
      successMessage: "\u73FE\u5728\u5DF2\u555F\u7528\u5099\u4EFD\u4EE3\u78BC\u3002\u5982\u679C\u60A8\u5931\u53BB\u4E86\u9A57\u8B49\u8A2D\u5099\u7684\u8A2A\u554F\u6B0A\u9650\uFF0C\u60A8\u53EF\u4EE5\u4F7F\u7528\u5176\u4E2D\u4E4B\u4E00\u767B\u5165\u60A8\u7684\u5E33\u6236\u3002\u6BCF\u500B\u4EE3\u78BC\u53EA\u80FD\u4F7F\u7528\u4E00\u6B21\u3002",
      successSubtitle: "\u5982\u679C\u60A8\u5931\u53BB\u4E86\u9A57\u8B49\u8A2D\u5099\u7684\u8A2A\u554F\u6B0A\u9650\uFF0C\u60A8\u53EF\u4EE5\u4F7F\u7528\u5176\u4E2D\u4E4B\u4E00\u767B\u5165\u60A8\u7684\u5E33\u6236\u3002",
      title: "\u6DFB\u52A0\u5099\u4EFD\u4EE3\u78BC\u9A57\u8B49",
      title__codelist: "\u5099\u4EFD\u4EE3\u78BC"
    },
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "\u9078\u64C7\u4E00\u500B\u4F9B\u61C9\u5546\u4F86\u9023\u63A5\u60A8\u7684\u5E33\u6236\u3002",
      formHint__noAccounts: "\u6C92\u6709\u53EF\u7528\u7684\u5916\u90E8\u5E33\u6236\u4F9B\u61C9\u5546\u3002",
      removeResource: {
        messageLine1: "{{identifier}} \u5C07\u5F9E\u6B64\u5E33\u6236\u4E2D\u88AB\u79FB\u9664\u3002",
        messageLine2: "\u60A8\u5C07\u7121\u6CD5\u518D\u4F7F\u7528\u9019\u500B\u5DF2\u9023\u63A5\u7684\u5E33\u6236\uFF0C\u4EFB\u4F55\u4F9D\u8CF4\u7684\u529F\u80FD\u5C07\u4E0D\u518D\u5DE5\u4F5C\u3002",
        successMessage: "{{connectedAccount}} \u5DF2\u5F9E\u60A8\u7684\u5E33\u6236\u4E2D\u79FB\u9664\u3002",
        title: "\u79FB\u9664\u5DF2\u9023\u63A5\u7684\u5E33\u6236"
      },
      socialButtonsBlockButton: "\u9023\u63A5 {{provider|titleize}} \u5E33\u6236",
      successMessage: "\u4F9B\u61C9\u5546\u5DF2\u88AB\u6DFB\u52A0\u5230\u60A8\u7684\u5E33\u6236",
      title: "\u6DFB\u52A0\u5DF2\u9023\u63A5\u7684\u5E33\u6236"
    },
    deletePage: {
      actionDescription: "\u8ACB\u5728\u4E0B\u65B9\u8F38\u5165\u201CDelete account\u201D\u4EE5\u7E7C\u7E8C\u3002",
      confirm: "\u79FB\u9664\u5E33\u6236",
      messageLine1: "\u60A8\u78BA\u5B9A\u8981\u522A\u9664\u60A8\u7684\u5E33\u6236\u55CE\uFF1F",
      messageLine2: "\u9019\u500B\u52D5\u4F5C\u662F\u6C38\u4E45\u6027\u4E14\u7121\u6CD5\u9084\u539F\u7684\u3002",
      title: "\u79FB\u9664\u5E33\u6236"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "\u4E00\u5C01\u542B\u6709\u9A57\u8B49\u78BC\u7684\u90F5\u4EF6\u5C07\u6703\u88AB\u767C\u9001\u5230\u9019\u500B\u96FB\u5B50\u90F5\u4EF6\u5730\u5740\u3002",
        formSubtitle: "\u8F38\u5165\u767C\u9001\u5230 {{identifier}} \u7684\u9A57\u8B49\u78BC",
        formTitle: "\u9A57\u8B49\u78BC",
        resendButton: "\u91CD\u767C\u9A57\u8B49\u78BC",
        successMessage: "\u96FB\u5B50\u90F5\u4EF6 {{identifier}} \u5DF2\u88AB\u6DFB\u52A0\u5230\u60A8\u7684\u5E33\u6236\u3002"
      },
      emailLink: {
        formHint: "\u4E00\u5C01\u542B\u6709\u9A57\u8B49\u9023\u7D50\u7684\u90F5\u4EF6\u5C07\u6703\u88AB\u767C\u9001\u5230\u9019\u500B\u96FB\u5B50\u90F5\u4EF6\u5730\u5740\u3002",
        formSubtitle: "\u9EDE\u64CA\u767C\u9001\u5230 {{identifier}} \u7684\u90F5\u4EF6\u4E2D\u7684\u9A57\u8B49\u9023\u7D50",
        formTitle: "\u9A57\u8B49\u9023\u7D50",
        resendButton: "\u91CD\u767C\u9023\u7D50",
        successMessage: "\u96FB\u5B50\u90F5\u4EF6 {{identifier}} \u5DF2\u88AB\u6DFB\u52A0\u5230\u60A8\u7684\u5E33\u6236\u3002"
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} \u5C07\u5F9E\u6B64\u5E33\u6236\u4E2D\u88AB\u79FB\u9664\u3002",
        messageLine2: "\u60A8\u5C07\u7121\u6CD5\u4F7F\u7528\u9019\u500B\u96FB\u5B50\u90F5\u4EF6\u5730\u5740\u767B\u9304\u3002",
        successMessage: "\u96FB\u5B50\u90F5\u4EF6 {{emailAddress}} \u5DF2\u5F9E\u60A8\u7684\u5E33\u6236\u4E2D\u79FB\u9664\u3002",
        title: "\u79FB\u9664\u96FB\u5B50\u90F5\u4EF6\u5730\u5740"
      },
      title: "\u6DFB\u52A0\u96FB\u5B50\u90F5\u4EF6\u5730\u5740",
      verifyTitle: "Verify email address"
    },
    formButtonPrimary__add: "Add",
    formButtonPrimary__continue: "\u7E7C\u7E8C",
    formButtonPrimary__finish: "\u5B8C\u6210",
    formButtonPrimary__remove: "Remove",
    formButtonPrimary__save: "Save",
    formButtonReset: "\u53D6\u6D88",
    mfaPage: {
      formHint: "\u9078\u64C7\u4E00\u500B\u6DFB\u52A0\u7684\u65B9\u6CD5\u3002",
      title: "\u6DFB\u52A0\u5169\u6B65\u9A57\u8B49"
    },
    mfaPhoneCodePage: {
      backButton: "\u4F7F\u7528\u73FE\u6709\u865F\u78BC",
      primaryButton__addPhoneNumber: "\u6DFB\u52A0\u96FB\u8A71\u865F\u78BC",
      removeResource: {
        messageLine1: "{{identifier}} \u5C07\u4E0D\u518D\u5728\u767B\u9304\u6642\u63A5\u6536\u9A57\u8B49\u4EE3\u78BC\u3002",
        messageLine2: "\u60A8\u7684\u5E33\u6236\u53EF\u80FD\u4E0D\u518D\u5B89\u5168\u3002\u60A8\u78BA\u5B9A\u8981\u7E7C\u7E8C\u55CE\uFF1F",
        successMessage: "\u5DF2\u79FB\u9664{{mfaPhoneCode}}\u7684\u7C21\u8A0A\u9A57\u8B49\u78BC\u5169\u6B65\u9A57\u8B49",
        title: "\u79FB\u9664\u5169\u6B65\u9A57\u8B49"
      },
      subtitle__availablePhoneNumbers: "\u9078\u64C7\u4E00\u500B\u96FB\u8A71\u865F\u78BC\u4F86\u8A3B\u518A\u7C21\u8A0A\u9A57\u8B49\u78BC\u5169\u6B65\u9A57\u8B49\u3002",
      subtitle__unavailablePhoneNumbers: "\u6C92\u6709\u53EF\u7528\u7684\u96FB\u8A71\u865F\u78BC\u4F86\u8A3B\u518A\u7C21\u8A0A\u9A57\u8B49\u78BC\u5169\u6B65\u9A57\u8B49\u3002",
      successMessage1: "\u767B\u5165\u6642\uFF0C\u60A8\u9700\u8981\u8F38\u5165\u767C\u9001\u5230\u6B64\u96FB\u8A71\u865F\u78BC\u7684\u9A57\u8B49\u78BC\u4F5C\u70BA\u9644\u52A0\u6B65\u9A5F\u3002",
      successMessage2: "\u4FDD\u5B58\u9019\u4E9B\u5099\u4EFD\u4EE3\u78BC\u4E26\u5C07\u5176\u5132\u5B58\u5728\u5B89\u5168\u7684\u5730\u65B9\u3002\u5982\u679C\u60A8\u7121\u6CD5\u5B58\u53D6\u8EAB\u4EFD\u9A57\u8B49\u8A2D\u5099\uFF0C\u5247\u53EF\u4EE5\u4F7F\u7528\u5099\u7528\u4EE3\u78BC\u767B\u5165\u3002",
      successTitle: "\u5DF2\u555F\u7528\u7C21\u8A0A\u9A57\u8B49\u78BC",
      title: "\u6DFB\u52A0\u7C21\u8A0A\u9A57\u8B49\u78BC\u9A57\u8B49"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "\u6383\u63CF\u4E8C\u7DAD\u78BC",
        buttonUnableToScan__nonPrimary: "\u4E0D\u80FD\u6383\u63CF\u4E8C\u7DAD\u78BC\uFF1F",
        infoText__ableToScan: "\u5728\u60A8\u7684\u9A57\u8B49\u5668\u61C9\u7528\u4E2D\u8A2D\u7F6E\u4E00\u500B\u65B0\u7684\u767B\u9304\u65B9\u6CD5\uFF0C\u4E26\u6383\u63CF\u4E0B\u9762\u7684\u4E8C\u7DAD\u78BC\u5C07\u5176\u9023\u7D50\u5230\u60A8\u7684\u5E33\u6236\u3002",
        infoText__unableToScan: "\u5728\u9A57\u8B49\u5668\u4E2D\u8A2D\u7F6E\u4E00\u500B\u65B0\u7684\u767B\u9304\u65B9\u6CD5\uFF0C\u4E26\u8F38\u5165\u4E0B\u9762\u63D0\u4F9B\u7684 Key\u3002",
        inputLabel__unableToScan1: "\u78BA\u4FDD\u555F\u7528\u4E86\u57FA\u65BC\u6642\u9593\u6216\u4E00\u6B21\u6027\u5BC6\u78BC\uFF0C\u7136\u5F8C\u5B8C\u6210\u9023\u7D50\u60A8\u7684\u5E33\u6236\u3002",
        inputLabel__unableToScan2: "\u6216\u8005\uFF0C\u5982\u679C\u60A8\u7684\u9A57\u8B49\u5668\u652F\u6301 TOTP URIs\uFF0C\u60A8\u4E5F\u53EF\u4EE5\u8907\u88FD\u5B8C\u6574\u7684 URI\u3002"
      },
      removeResource: {
        messageLine1: "\u767B\u9304\u6642\uFF0C\u5C07\u4E0D\u518D\u9700\u8981\u4F86\u81EA\u6B64\u9A57\u8B49\u5668\u7684\u9A57\u8B49\u78BC\u3002",
        messageLine2: "\u60A8\u7684\u5E33\u6236\u53EF\u80FD\u4E0D\u518D\u5B89\u5168\u3002\u60A8\u78BA\u5B9A\u8981\u7E7C\u7E8C\u55CE\uFF1F",
        successMessage: "\u5DF2\u79FB\u9664\u901A\u904E\u9A57\u8B49\u5668\u61C9\u7528\u7A0B\u5F0F\u7684\u5169\u6B65\u9A57\u8B49\u3002",
        title: "\u79FB\u9664\u5169\u6B65\u9A57\u8B49"
      },
      successMessage: "\u73FE\u5728\u5DF2\u555F\u7528\u5169\u6B65\u9A57\u8B49\u3002\u5728\u767B\u9304\u6642\uFF0C\u60A8\u9700\u8981\u8F38\u5165\u4F86\u81EA\u6B64\u9A57\u8B49\u5668\u7684\u9A57\u8B49\u78BC\u4F5C\u70BA\u984D\u5916\u6B65\u9A5F\u3002",
      title: "\u6DFB\u52A0\u9A57\u8B49\u5668\u61C9\u7528\u7A0B\u5F0F",
      verifySubtitle: "\u8F38\u5165\u60A8\u7684\u9A57\u8B49\u5668\u751F\u6210\u7684\u9A57\u8B49\u78BC",
      verifyTitle: "\u9A57\u8B49\u4EE3\u78BC"
    },
    mobileButton__menu: "\u83DC\u55AE",
    navbar: {
      account: "\u8F2A\u5ED3",
      apiKeys: void 0,
      billing: void 0,
      description: "\u7BA1\u7406\u60A8\u7684\u5E33\u6236\u8CC7\u8A0A\u3002",
      security: "\u5B89\u5168",
      title: "\u5E33\u6236"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: void 0,
        title: void 0
      },
      subtitle__rename: void 0,
      title__rename: void 0
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "\u5EFA\u8B70\u9000\u51FA\u6240\u6709\u53EF\u80FD\u4F7F\u7528\u904E\u820A\u5BC6\u78BC\u7684\u5176\u4ED6\u88DD\u7F6E\u3002",
      readonly: "\u60A8\u7684\u5BC6\u78BC\u76EE\u524D\u7121\u6CD5\u7DE8\u8F2F\uFF0C\u56E0\u70BA\u60A8\u53EA\u80FD\u900F\u904E\u4F01\u696D\u9023\u7DDA\u767B\u5165\u3002",
      successMessage__set: "\u60A8\u7684\u5BC6\u78BC\u5DF2\u8A2D\u7F6E\u3002",
      successMessage__signOutOfOtherSessions: "\u6240\u6709\u5176\u4ED6\u8A2D\u5099\u5DF2\u9000\u51FA\u3002",
      successMessage__update: "\u60A8\u7684\u5BC6\u78BC\u5DF2\u66F4\u65B0\u3002",
      title__set: "\u8A2D\u7F6E\u5BC6\u78BC",
      title__update: "\u66F4\u6539\u5BC6\u78BC"
    },
    phoneNumberPage: {
      infoText: "\u4E00\u689D\u5305\u542B\u9A57\u8B49\u9023\u7D50\u7684\u7C21\u8A0A\u5C07\u6703\u767C\u9001\u5230\u9019\u500B\u96FB\u8A71\u865F\u78BC\u3002",
      removeResource: {
        messageLine1: "{{identifier}} \u5C07\u5F9E\u6B64\u5E33\u6236\u4E2D\u88AB\u79FB\u9664\u3002",
        messageLine2: "\u60A8\u5C07\u7121\u6CD5\u4F7F\u7528\u9019\u500B\u96FB\u8A71\u865F\u78BC\u767B\u9304\u3002",
        successMessage: "\u96FB\u8A71\u865F\u78BC {{phoneNumber}} \u5DF2\u5F9E\u60A8\u7684\u5E33\u6236\u4E2D\u79FB\u9664\u3002",
        title: "\u79FB\u9664\u96FB\u8A71\u865F\u78BC"
      },
      successMessage: "{{identifier}} \u5DF2\u88AB\u6DFB\u52A0\u5230\u60A8\u7684\u5E33\u6236\u3002",
      title: "\u6DFB\u52A0\u96FB\u8A71\u865F\u78BC",
      verifySubtitle: "\u8F38\u5165\u767C\u9001\u81F3\u7684\u9A57\u8B49\u78BC {{identifier}}",
      verifyTitle: "\u9A57\u8B49\u96FB\u8A71\u865F\u78BC"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "\u4E0A\u50B3\u5C0F\u65BC10MB\u7684JPG, PNG, GIF, \u6216WEBP\u683C\u5F0F\u7684\u5716\u7247",
      imageFormDestructiveActionSubtitle: "\u79FB\u9664\u5716\u7247",
      imageFormSubtitle: "\u4E0A\u50B3\u5716\u7247",
      imageFormTitle: "\u500B\u4EBA\u8CC7\u6599\u5716\u7247",
      readonly: "\u60A8\u7684\u500B\u4EBA\u8CC7\u6599\u8CC7\u8A0A\u5DF2\u7531\u4F01\u696D\u9023\u7DDA\u63D0\u4F9B\uFF0C\u7121\u6CD5\u7DE8\u8F2F\u3002",
      successMessage: "\u60A8\u7684\u500B\u4EBA\u8CC7\u6599\u5DF2\u66F4\u65B0\u3002",
      title: "\u66F4\u65B0\u500B\u4EBA\u8CC7\u6599"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "\u9000\u51FA\u8A2D\u5099",
        title: "\u6D3B\u52D5\u8A2D\u5099"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "\u518D\u8A66\u4E00\u6B21",
        actionLabel__reauthorize: "\u7ACB\u5373\u6388\u6B0A",
        destructiveActionTitle: "\u79FB\u9664",
        primaryButton: "\u9023\u63A5\u5E33\u6236",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "\u6240\u9700\u7BC4\u570D\u5DF2\u66F4\u65B0\uFF0C\u60A8\u53EF\u80FD\u6703\u9047\u5230\u529F\u80FD\u53D7\u9650\u7684\u60C5\u6CC1\u3002\u8ACB\u91CD\u65B0\u6388\u6B0A\u6B64\u61C9\u7528\u7A0B\u5F0F\u4EE5\u907F\u514D\u4EFB\u4F55\u554F\u984C\u3002",
        title: "\u5DF2\u9023\u63A5\u7684\u5E33\u6236"
      },
      dangerSection: {
        deleteAccountButton: "\u79FB\u9664\u5E33\u6236",
        title: "\u5371\u96AA"
      },
      emailAddressesSection: {
        destructiveAction: "\u79FB\u9664\u96FB\u5B50\u90F5\u4EF6\u5730\u5740",
        detailsAction__nonPrimary: "\u8A2D\u70BA\u4E3B\u8981",
        detailsAction__primary: "\u5B8C\u6210\u9A57\u8B49",
        detailsAction__unverified: "\u5B8C\u6210\u9A57\u8B49",
        primaryButton: "\u6DFB\u52A0\u96FB\u5B50\u90F5\u4EF6\u5730\u5740",
        title: "\u96FB\u5B50\u90F5\u4EF6\u5730\u5740"
      },
      enterpriseAccountsSection: {
        title: "\u4F01\u696D\u5E33\u6236"
      },
      headerTitle__account: "\u5E33\u6236",
      headerTitle__security: "\u5B89\u5168",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "\u91CD\u65B0\u751F\u6210\u4EE3\u78BC",
          headerTitle: "\u5099\u4EFD\u4EE3\u78BC",
          subtitle__regenerate: "\u7372\u53D6\u4E00\u5957\u65B0\u7684\u5B89\u5168\u5099\u4EFD\u4EE3\u78BC\u3002\u4E4B\u524D\u7684\u5099\u4EFD\u4EE3\u78BC\u5C07\u88AB\u522A\u9664\uFF0C\u7121\u6CD5\u4F7F\u7528\u3002",
          title__regenerate: "\u91CD\u65B0\u751F\u6210\u5099\u4EFD\u4EE3\u78BC"
        },
        phoneCode: {
          actionLabel__setDefault: "\u8A2D\u70BA\u9ED8\u8A8D",
          destructiveActionLabel: "\u79FB\u9664\u96FB\u8A71\u865F\u78BC"
        },
        primaryButton: "\u6DFB\u52A0\u5169\u6B65\u9A57\u8B49",
        title: "\u5169\u6B65\u9A57\u8B49",
        totp: {
          destructiveActionTitle: "\u79FB\u9664",
          headerTitle: "\u9A57\u8B49\u5668\u61C9\u7528\u7A0B\u5F0F"
        }
      },
      passkeysSection: {
        menuAction__destructive: void 0,
        menuAction__rename: void 0,
        primaryButton: void 0,
        title: void 0
      },
      passwordSection: {
        primaryButton__setPassword: "\u8A2D\u7F6E\u5BC6\u78BC",
        primaryButton__updatePassword: "\u66F4\u6539\u5BC6\u78BC",
        title: "\u5BC6\u78BC"
      },
      phoneNumbersSection: {
        destructiveAction: "\u79FB\u9664\u96FB\u8A71\u865F\u78BC",
        detailsAction__nonPrimary: "\u8A2D\u70BA\u4E3B\u8981",
        detailsAction__primary: "\u5B8C\u6210\u9A57\u8B49",
        detailsAction__unverified: "\u5B8C\u6210\u9A57\u8B49",
        primaryButton: "\u6DFB\u52A0\u96FB\u8A71\u865F\u78BC",
        title: "\u96FB\u8A71\u865F\u78BC"
      },
      profileSection: {
        primaryButton: "\u66F4\u65B0\u500B\u4EBA\u8CC7\u6599",
        title: "\u500B\u4EBA\u8CC7\u6599"
      },
      usernameSection: {
        primaryButton__setUsername: "\u8A2D\u7F6E\u4F7F\u7528\u8005\u540D\u7A31",
        primaryButton__updateUsername: "\u66F4\u6539\u4F7F\u7528\u8005\u540D\u7A31",
        title: "\u4F7F\u7528\u8005\u540D\u7A31"
      },
      web3WalletsSection: {
        destructiveAction: "\u79FB\u9664\u9322\u5305",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3 \u9322\u5305",
        title: "Web3 \u9322\u5305"
      }
    },
    usernamePage: {
      successMessage: "\u60A8\u7684\u4F7F\u7528\u8005\u540D\u7A31\u5DF2\u66F4\u65B0\u3002",
      title__set: "\u66F4\u65B0\u4F7F\u7528\u8005\u540D\u7A31",
      title__update: "\u66F4\u65B0\u4F7F\u7528\u8005\u540D\u7A31"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} \u5C07\u5F9E\u6B64\u5E33\u6236\u4E2D\u88AB\u79FB\u9664\u3002",
        messageLine2: "\u60A8\u5C07\u7121\u6CD5\u4F7F\u7528\u9019\u500B web3 \u9322\u5305\u767B\u9304\u3002",
        successMessage: "{{web3Wallet}} \u5DF2\u5F9E\u60A8\u7684\u5E33\u6236\u4E2D\u79FB\u9664\u3002",
        title: "\u79FB\u9664 web3 \u9322\u5305"
      },
      subtitle__availableWallets: "\u9078\u64C7\u4E00\u500B web3 \u9322\u5305\u9023\u63A5\u5230\u60A8\u7684\u5E33\u6236\u3002",
      subtitle__unavailableWallets: "\u6C92\u6709\u53EF\u7528\u7684 web3 \u9322\u5305\u3002",
      successMessage: "\u9322\u5305\u5DF2\u88AB\u6DFB\u52A0\u5230\u60A8\u7684\u5E33\u6236\u3002",
      title: "\u6DFB\u52A0web3\u9322\u5305",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  zhTW
};
//# sourceMappingURL=zh-TW.mjs.map