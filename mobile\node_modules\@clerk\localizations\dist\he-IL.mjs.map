{"version": 3, "sources": ["../src/he-IL.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const heIL: LocalizationResource = {\n  locale: 'he-IL',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'חזור',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'ברירת מחדל',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'מכשיר מחקה אחר',\n  badge__primary: 'ראשי',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'דורש פעולה',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'מכשיר זה',\n  badge__unverified: 'לא מאומת',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'מכשיר משתמש',\n  badge__you: 'אתה',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'צור ארגון',\n    invitePage: {\n      formButtonReset: 'דלג',\n    },\n    title: 'צור ארגון',\n  },\n  dates: {\n    lastDay: \"אתמול ב-{{ date | timeString('iw') }}\",\n    next6Days: \"{{ date | weekday('iw','long') }} ב-{{ date | timeString('iw') }}\",\n    nextDay: \"מחר ב-{{ date | timeString('iw') }}\",\n    numeric: \"{{ date | numeric('iw') }}\",\n    previous6Days: \"{{ date | weekday('iw','long') }} האחרון ב-{{ date | timeString('iw') }}\",\n    sameDay: \"היום ב-{{ date | timeString('iw') }}\",\n  },\n  dividerText: 'או',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'השתמש בשיטה אחרת',\n  footerPageLink__help: 'עזרה',\n  footerPageLink__privacy: 'פרטיות',\n  footerPageLink__terms: 'תנאים',\n  formButtonPrimary: 'המשך',\n  formButtonPrimary__verify: 'Verify',\n  formFieldAction__forgotPassword: 'שכחת סיסמה?',\n  formFieldError__matchingPasswords: 'הסיסמאות תואמות.',\n  formFieldError__notMatchingPasswords: 'הסיסמאות אינן תואמות.',\n  formFieldError__verificationLinkExpired: 'הקישור לאימות פג תוקף. אנא בקש/י קישור חדש.',\n  formFieldHintText__optional: 'אופציונלי',\n  formFieldHintText__slug: '(slug) הוא מזהה קריא שמיועד להיות ייחודי. הוא משמש לעיתים קרובות בכתובות URL.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'מחיקת חשבון',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: 'הארגון-שלי',\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'הפעל הזמנות אוטומטיות לדומיין הזה',\n  formFieldLabel__backupCode: 'קוד גיבוי',\n  formFieldLabel__confirmDeletion: 'אישור',\n  formFieldLabel__confirmPassword: 'אמת סיסמה',\n  formFieldLabel__currentPassword: 'סיסמה נוכחית',\n  formFieldLabel__emailAddress: 'כתובת דוא\"ל',\n  formFieldLabel__emailAddress_username: 'כתובת דוא\"ל או שם משתמש',\n  formFieldLabel__emailAddresses: 'כתובות דוא\"ל',\n  formFieldLabel__firstName: 'שם פרטי',\n  formFieldLabel__lastName: 'שם משפחה',\n  formFieldLabel__newPassword: 'סיסמה חדשה',\n  formFieldLabel__organizationDomain: 'דומיין',\n  formFieldLabel__organizationDomainDeletePending: 'מחק הזמנות והצעות ממתינות',\n  formFieldLabel__organizationDomainEmailAddress: 'כתובת אימייל לאימות',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'הזן כתובת אימייל תחת דומיין זה כדי לקבל קוד ולאמת את הדומיין.',\n  formFieldLabel__organizationName: 'שם הארגון',\n  formFieldLabel__organizationSlug: 'כתובת URL של הארגון',\n  formFieldLabel__passkeyName: 'שם או מפתח סיסמה',\n  formFieldLabel__password: 'סיסמה',\n  formFieldLabel__phoneNumber: 'מספר טלפון',\n  formFieldLabel__role: 'תפקיד',\n  formFieldLabel__signOutOfOtherSessions: 'התנתק מכל המכשירים האחרים',\n  formFieldLabel__username: 'שם משתמש',\n  impersonationFab: {\n    action__signOut: 'התנתק',\n    title: 'מחובר כ{{identifier}}',\n  },\n  maintenanceMode: undefined,\n  membershipRole__admin: 'מנהל',\n  membershipRole__basicMember: 'חבר',\n  membershipRole__guestMember: 'אורח',\n  organizationList: {\n    action__createOrganization: 'צור ארגון',\n    action__invitationAccept: 'הצטרף',\n    action__suggestionsAccept: 'בקשה להצטרפות',\n    createOrganization: 'צור ארגון',\n    invitationAcceptedLabel: 'הצטרף',\n    subtitle: 'כדי להמשיך ל{{applicationName}}',\n    suggestionsAcceptedLabel: 'ממתין לאישור',\n    title: 'בחר חשבון',\n    titleWithoutPersonal: 'בחר ארגון',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'הזמנות אוטומטיות',\n    badge__automaticSuggestion: 'הצעות אוטומטיות',\n    badge__manualInvitation: 'ללא הרשמה אוטומטית',\n    badge__unverified: 'לא מאומת',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'הוסף את הדומיין לאימות. משתמשים עם כתובות אימייל תחת דומיין זה יכולים להצטרף לארגון באופן אוטומטי או לבקש להצטרף.',\n      title: 'הוסף דומיין',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed: 'לא היה ניתן לשלוח את ההזמנות. תקן את הבעיות הבאות ונסה שוב:',\n      formButtonPrimary__continue: 'שלח הזמנות',\n      selectDropdown__role: 'בחר תפקיד',\n      subtitle: 'הזמן חברים חדשים לארגון זה',\n      successMessage: 'ההזמנות נשלחו בהצלחה',\n      title: 'הזמן חברים',\n    },\n    membersPage: {\n      action__invite: 'הזמן',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'הסר חבר',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'הצטרף',\n        tableHeader__role: 'תפקיד',\n        tableHeader__user: 'משתמש',\n      },\n      detailsTitle__emptyRow: 'אין חברים להצגה',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'הזמן משתמשים על ידי חיבור דומיין אימייל לארגון שלך. כל מי שנרשם עם דומיין אימייל תואם יוכל להצטרף לארגון בכל עת.',\n          headerTitle: 'הזמנות אוטומטיות',\n          primaryButton: 'נהל דומיינים מאומתים',\n        },\n        table__emptyRow: 'אין הזמנות להצגה',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'בטל הזמנה',\n        tableHeader__invited: 'הוזמן',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle: 'משתמשים שנרשמים עם דומיין אימייל תואם יוכלו לראות הצעה לבקש להצטרף לארגון שלך.',\n          headerTitle: 'הצעות אוטומטיות',\n          primaryButton: 'נהל דומיינים מאומתים',\n        },\n        menuAction__approve: 'אשר',\n        menuAction__reject: 'דחה',\n        tableHeader__requested: 'בקשת גישה',\n        table__emptyRow: 'אין בקשות להצגה',\n      },\n      start: {\n        headerTitle__invitations: 'הזמנות',\n        headerTitle__members: 'חברים',\n        headerTitle__requests: 'בקשות',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'נהל את הארגון שלך.',\n      general: 'כללי',\n      members: 'חברים',\n      title: 'ארגון',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'הקלד \"{{organizationName}}\" למטה כדי להמשיך.',\n          messageLine1: 'האם אתה בטוח שאתה רוצה למחוק את הארגון הזה?',\n          messageLine2: 'פעולה זו היא סופית ובלתי הפיכה.',\n          successMessage: 'מחקת את הארגון.',\n          title: 'מחק ארגון',\n        },\n        leaveOrganization: {\n          actionDescription: 'הקלד \"{{organizationName}}\" למטה כדי להמשיך.',\n          messageLine1: 'האם אתה בטוח שאתה רוצה לעזוב את הארגון הזה? תאבד גישה לארגון זה וליישומים שלו.',\n          messageLine2: 'פעולה זו היא סופית ובלתי הפיכה.',\n          successMessage: 'עזבת את הארגון.',\n          title: 'עזוב את הארגון',\n        },\n        title: 'סיכון',\n      },\n      domainSection: {\n        menuAction__manage: 'נהל',\n        menuAction__remove: 'מחק',\n        menuAction__verify: 'אמת',\n        primaryButton: 'הוסף דומיין',\n        subtitle: 'אפשר למשתמשים להצטרף לארגון באופן אוטומטי או לבקש להצטרף על בסיס דומיין אימייל מאומת.',\n        title: 'דומיינים מאומתים',\n      },\n      successMessage: 'הארגון עודכן.',\n      title: 'פרופיל ארגון',\n    },\n    removeDomainPage: {\n      messageLine1: 'דומיין האימייל {{domain}} יוסר.',\n      messageLine2: 'משתמשים לא יוכלו להצטרף לארגון באופן אוטומטי לאחר פעולה זו.',\n      successMessage: '{{domain}} הוסר.',\n      title: 'הסר דומיין',\n    },\n    start: {\n      headerTitle__general: 'כללי',\n      headerTitle__members: 'חברים',\n      profileSection: {\n        primaryButton: 'עדכן פרופיל',\n        title: 'פרופיל ארגון',\n        uploadAction__title: 'לוגו',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'הסרת דומיין זה תשפיע על המשתמשים המוזמנים.',\n        removeDomainActionLabel__remove: 'הסר דומיין',\n        removeDomainSubtitle: 'הסר דומיין זה מרשימת הדומיינים המאומתים שלך',\n        removeDomainTitle: 'הסר דומיין',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'משתמשים מוזמנים אוטומטית להצטרף לארגון כשהם נרשמים ויכולים להצטרף בכל עת.',\n        automaticInvitationOption__label: 'הזמנות אוטומטיות',\n        automaticSuggestionOption__description:\n          'משתמשים מקבלים הצעה לבקש להצטרף, אך חייבים להיות מאושרים על ידי מנהל לפני שיוכלו להצטרף לארגון.',\n        automaticSuggestionOption__label: 'הצעות אוטומטיות',\n        calloutInfoLabel: 'שינוי מצב ההרשמה ישפיע רק על משתמשים חדשים.',\n        calloutInvitationCountLabel: 'הזמנות ממתינות שנשלחו למשתמשים: {{count}}',\n        calloutSuggestionCountLabel: 'הצעות ממתינות שנשלחו למשתמשים: {{count}}',\n        manualInvitationOption__description: 'משתמשים יכולים להצטרף לארגון רק באמצעות הזמנה ידנית.',\n        manualInvitationOption__label: 'ללא הרשמה אוטומטית',\n        subtitle: 'בחר כיצד משתמשים מדומיין זה יכולים להצטרף לארגון.',\n      },\n      start: {\n        headerTitle__danger: 'סיכון',\n        headerTitle__enrollment: 'אפשרויות הרשמה',\n      },\n      subtitle: 'הדומיין {{domain}} אומת בהצלחה. המשך בבחירת מצב הרשמה.',\n      title: 'עדכן {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'הזן את קוד האימות שנשלח לכתובת האימייל שלך',\n      formTitle: 'קוד אימות',\n      resendButton: 'לא קיבלת קוד? שלח שוב',\n      subtitle: 'הדומיין {{domainName}} צריך להיות מאומת באמצעות אימייל.',\n      subtitleVerificationCodeScreen: 'קוד אימות נשלח ל{{emailAddress}}. הזן את הקוד כדי להמשיך.',\n      title: 'אמת דומיין',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'צור ארגון',\n    action__invitationAccept: 'הצטרף',\n    action__manageOrganization: 'נהל ארגון',\n    action__suggestionsAccept: 'בקשה להצטרפות',\n    notSelected: 'לא נבחר ארגון',\n    personalWorkspace: 'אזור אישי',\n    suggestionsAcceptedLabel: 'ממתין לאישור',\n  },\n  paginationButton__next: 'הבא',\n  paginationButton__previous: 'הקודם',\n  paginationRowText__displaying: 'מציג',\n  paginationRowText__of: 'מתוך',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'השג עזרה',\n      actionText: 'אין לך אף אחד מאלה?',\n      blockButton__backupCode: 'השתמש בקוד גיבוי',\n      blockButton__emailCode: 'קוד אימייל ל {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'המשך עם הסיסמה שלך',\n      blockButton__phoneCode: 'שלח קוד SMS ל {{identifier}}',\n      blockButton__totp: 'השתמש באפליקציית האימות שלך',\n      getHelp: {\n        blockButton__emailSupport: 'תמיכה באימייל',\n        content: 'אם יש לך בעיה באימות חשבונך, שלח לנו אימייל ואנחנו ניצור איתך קשר על מנת לשחזר את הגישה בהקדם האפשרי',\n        title: 'השג עזרה',\n      },\n      subtitle: 'נתקלת בבעיות? אתה יכול להשתמש בכל אחת מהשיטות הללו עבור אימות',\n      title: 'השתמש בשיטה אחרת',\n    },\n    backupCodeMfa: {\n      subtitle: 'קוד הגיבוי שלך הוא הקוד שקיבלת כאשר הגדרת את האימות הדו-שלבי',\n      title: 'הכנס את קוד הגיבוי',\n    },\n    emailCode: {\n      formTitle: 'קוד אימות',\n      resendButton: 'לא קיבלת קוד? שלח שוב',\n      subtitle: 'המשך ל {{applicationName}}',\n      title: 'בדוק את האימייל שלך',\n    },\n    noAvailableMethods: {\n      message: 'לא ניתן להמשיך עם האימות. אין גורם אימות זמין',\n      subtitle: 'קרתה תקלה',\n      title: 'לא ניתן לאמת את חשבונך',\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'השתמש בשיטה אחרת',\n      subtitle: 'הכנס את הסיסמה המקושרת עם חשבונך',\n      title: 'הכנס סיסמה',\n    },\n    phoneCode: {\n      formTitle: 'קוד אימות',\n      resendButton: 'לא קיבלת קוד? שלח שוב',\n      subtitle: 'להמשך ל {{applicationName}}',\n      title: 'בדוק את הטלפון שלך',\n    },\n    phoneCodeMfa: {\n      formTitle: 'קוד אימות',\n      resendButton: 'לא קיבלת קוד? שלח שוב',\n      subtitle: 'להמשך, אנא הכנס את קוד האימות שנשלח לטלפון שלך',\n      title: 'בדוק את הטלפון שלך',\n    },\n    totpMfa: {\n      formTitle: 'קוד אימות',\n      subtitle: 'להמשך, אנא הכנס את קוד האימות שנוצר על ידי אפליקציית האימות שלך',\n      title: 'אימות דו-שלבי',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'הוסף חשבון',\n      action__signOutAll: 'התנתק מכל החשבונות',\n      subtitle: 'בחר את החשבון איתו תרצה להמשיך.',\n      title: 'בחר חשבון',\n    },\n    alternativeMethods: {\n      actionLink: 'קבל עזרה',\n      actionText: 'אין לך אף אחת מהשיטות האלה?',\n      blockButton__backupCode: 'השתמש בקוד גיבוי',\n      blockButton__emailCode: 'שלח קוד באימייל ל-{{identifier}}',\n      blockButton__emailLink: 'שלח קישור באימייל ל-{{identifier}}',\n      blockButton__passkey: 'היכנס עם המפתח שלך',\n      blockButton__password: 'התחבר עם הסיסמה שלך',\n      blockButton__phoneCode: 'שלח קוד SMS ל-{{identifier}}',\n      blockButton__totp: 'השתמש באפליקציית האימות שלך',\n      getHelp: {\n        blockButton__emailSupport: 'מייל לתמיכה',\n        content: 'אם אתה נתקל בקשיים בהתחברות לחשבונך, שלח לנו מייל ונעבוד איתך כדי לשחזר את הגישה בהקדם האפשרי.',\n        title: 'קבל עזרה',\n      },\n      subtitle: 'נתקלת בבעיה? תוכל להשתמש באחת מהשיטות האלה כדי להתחבר.',\n      title: 'השתמש בשיטה אחרת',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'להמשיך אל {{applicationName}}',\n      title: 'הכנס קוד גיבוי',\n    },\n    emailCode: {\n      formTitle: 'קוד אימות',\n      resendButton: 'שלח קוד שוב',\n      subtitle: 'להמשיך אל {{applicationName}}',\n      title: 'בדוק את הדוא\"ל שלך',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'להמשך, פתח את קוד האימות מהמכשיר והדפדפן ממנו אתה מתכוון לבצע כניסה',\n        title: 'קישור האימות הזה לא חוקי עבור מכשיר זה',\n      },\n      expired: {\n        subtitle: 'חזור לכרטיסייה המקורית להמשך.',\n        title: 'קישור האימות הזה פג תוקף',\n      },\n      failed: {\n        subtitle: 'חזור לכרטיסייה המקורית להמשך.',\n        title: 'קישור האימות הזה לא חוקי',\n      },\n      formSubtitle: 'השתמש בקישור האימות שנשלח לדוא\"ל שלך',\n      formTitle: 'קישור אימות',\n      loading: {\n        subtitle: 'תועבר בקרוב',\n        title: 'מתחבר...',\n      },\n      resendButton: 'שלח קישור שוב',\n      subtitle: 'להמשיך אל {{applicationName}}',\n      title: 'בדוק את הדוא\"ל שלך',\n      unusedTab: {\n        title: 'אתה יכול לסגור את הכרטיסייה הזו',\n      },\n      verified: {\n        subtitle: 'תועבר בקרוב',\n        title: 'נכנסת בהצלחה',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'חזור לכרטיסייה המקורית להמשך',\n        subtitleNewTab: 'חזור לכרטיסייה שנפתחה חדשה להמשך',\n        titleNewTab: 'נכנס בכרטיסייה אחרת',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'אפס קוד הסיסמה',\n      resendButton: 'שלח קוד שוב',\n      subtitle: 'כדי לאפס את הסיסמה שלך',\n      subtitle_email: 'ראשית, הכנס את הקוד שנשלח לאימייל שלך',\n      subtitle_phone: 'ראשית, הכנס את הקוד שנשלח לטלפון שלך',\n      title: 'איפוס סיסמה',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'אפס את הסיסמה שלך',\n      label__alternativeMethods: 'או, התחבר באמצעות שיטה אחרת.',\n      title: 'שכחת סיסמה?',\n    },\n    noAvailableMethods: {\n      message: 'לא ניתן להמשיך בהתחברות. אין גורם אימות זמין.',\n      subtitle: 'אירעה שגיאה',\n      title: 'לא ניתן להתחבר',\n    },\n    passkey: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'השתמש בשיטה אחרת',\n      subtitle: 'להמשיך אל {{applicationName}}',\n      title: 'הכנס את סיסמתך',\n    },\n    passwordPwned: {\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: 'קוד אימות',\n      resendButton: 'שלח את הקוד שוב',\n      subtitle: 'להמשיך אל {{applicationName}}',\n      title: 'בדוק את הטלפון שלך',\n    },\n    phoneCodeMfa: {\n      formTitle: 'קוד אימות',\n      resendButton: 'שלח את הקוד שוב',\n      subtitle: undefined,\n      title: 'בדוק את הטלפון שלך',\n    },\n    resetPassword: {\n      formButtonPrimary: 'אפס סיסמה',\n      requiredMessage: 'מטעמי אבטחה, יש לאפס את הסיסמה שלך.',\n      successMessage: 'הסיסמה שלך שונתה בהצלחה. מחבר אותך, אנא המתן רגע.',\n      title: 'אפס סיסמה',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'אנחנו צריכים לאמת את זהותך לפני שנאפס את הסיסמה שלך.',\n    },\n    start: {\n      actionLink: 'הרשמה',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'השתמש בדוא\"ל',\n      actionLink__use_email_username: 'השתמש בדוא\"ל או שם משתמש',\n      actionLink__use_passkey: 'השתמש במפתח סיסמה במקום',\n      actionLink__use_phone: 'השתמש בטלפון',\n      actionLink__use_username: 'השתמש בשם משתמש',\n      actionText: 'אין לך חשבון?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'להמשיך אל {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'התחבר',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'קוד אימות',\n      subtitle: 'להמשך, אנא הכנס את קוד האימות שנוצר על ידי אפליקציית האימות שלך',\n      title: 'אימות שני שלבים',\n    },\n  },\n  signInEnterPasswordTitle: 'הזן את הסיסמה שלך',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'התחבר',\n      actionText: 'יש לך חשבון?',\n      subtitle: 'להמשיך אל {{applicationName}}',\n      title: 'מלא שדות חסרים',\n    },\n    emailCode: {\n      formSubtitle: 'הכנס את קוד האימות שנשלח לכתובת הדוא\"ל שלך',\n      formTitle: 'קוד אימות',\n      resendButton: 'שלח קוד שוב',\n      subtitle: 'להמשיך אל {{applicationName}}',\n      title: 'אמת את כתובת הדוא\"ל שלך',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'להמשך, פתח את קוד האימות מהמכשיר והדפדפן ממנו אתה מתכוון לבצע את הרישום',\n        title: 'קישור האימות אינו חוקי עבור מכשיר זה',\n      },\n      formSubtitle: 'השתמש בקישור האימות שנשלח לכתובת הדוא\"ל שלך',\n      formTitle: 'קישור לאימות',\n      loading: {\n        title: 'מתחיל להירשם...',\n      },\n      resendButton: 'שלח קישור שוב',\n      subtitle: 'להמשיך אל {{applicationName}}',\n      title: 'אמת את כתובת הדוא\"ל שלך',\n      verified: {\n        title: 'נרשמת בהצלחה',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'חזור לכרטיסייה שנפתחה לאחרונה להמשיך',\n        subtitleNewTab: 'חזור לכרטיסייה הקודמת להמשיך',\n        title: 'אימות דוא\"ל הצליח',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'הכנס את קוד האימות שנשלח למספר הטלפון שלך',\n      formTitle: 'קוד אימות',\n      resendButton: 'שלח קוד שוב',\n      subtitle: 'להמשיך אל {{applicationName}}',\n      title: 'אמת את מספר הטלפון שלך',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'התחבר',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: 'יש לך חשבון?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'להמשיך אל {{applicationName}}',\n      subtitleCombined: 'להמשיך אל {{applicationName}}',\n      title: 'צור את החשבון שלך',\n      titleCombined: 'צור את החשבון שלך',\n    },\n  },\n  socialButtonsBlockButton: 'המשך עם {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: '{{email}} כבר חבר בארגון',\n    captcha_invalid: 'ההרשמה נכשלה עקב כשל באימות האבטחה. אנא רענן את הדף ונסה שוב, או פנה לתמיכה לעזרה נוספת.',\n    captcha_unavailable: 'ההרשמה נכשלה עקב כשל באימות נגד בוטים. אנא רענן את הדף ונסה שוב, או פנה לתמיכה לעזרה נוספת.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: 'כתובת המייל הזאת כבר תפוסה. אנא נסה אחרת.',\n    form_identifier_exists__phone_number: 'מספר הטלפון הזה כבר תפוס. אנא נסה מספר אחר.',\n    form_identifier_exists__username: 'שם המשתמש הזה כבר תפוס. אנא נסה שם משתמש אחר',\n    form_identifier_not_found: 'לא ניתן למצוא חשבון עם אלו הפרטים.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'כתובת האימייל חייבת להיות כתובת אימייל תקינה.',\n    form_param_format_invalid__phone_number: 'מספר הטלפון חייב להיות בפורמט בינלאומי תקין.',\n    form_param_max_length_exceeded__first_name: 'שם פרטי לא צריך לעלות על 256 תווים.',\n    form_param_max_length_exceeded__last_name: 'שם משפחה לא צריך לעלות על 256 תווים.',\n    form_param_max_length_exceeded__name: 'שם לא צריך לעלות על 256 תווים.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: 'הסיסמה שלך אינה מספיק חזקה.',\n    form_password_pwned: 'הסיסמה הזו נמצאה כחלק מהפרטים שנחשפו בהפרת נתונים ולא ניתן להשתמש בה, נסה סיסמה אחרת במקום.',\n    form_password_pwned__sign_in:\n      'הסיסמה הזו נמצאה כחלק מהפרטים שנחשפו בהפרת נתונים ולא ניתן להשתמש בה, אנא בצע איתחול לסיסמה שלך.',\n    form_password_size_in_bytes_exceeded:\n      'הסיסמה שלך חורגת ממספר הבייטים המרבי המותר, נסה לקצר אותה או להסיר כמה תווים מיוחדים.',\n    form_password_validation_failed: 'סיסמה שגויה',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: 'לא ניתן למחוק את הזיהוי האחרון שלך.',\n    not_allowed_access:\n      \"האימייל או מספר הטלפון אינו מותר להרשמה. זה עשוי להיות בגלל השימוש ב-'+', '=', '#' או '.' בכתובת האימייל שלך, השימוש בתחום המחובר לשירות אימייל זמני או הפרסט בכתובת האימייל שלך. אם מחזרים שגיאה, נא ליצור קשר עם תמיכה.\",\n    organization_domain_blocked: 'הדומיין של ספק האימייל הזה חסום. אנא נסה אחד שונה.',\n    organization_domain_common: 'הדומיין של ספק האימייל הזה נפוץ. אנא נסה אחד שונה.',\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: 'הגעת למגבלת החברות בארגון, כולל הזמנות יוצאות דופן.',\n    organization_minimum_permissions_needed: 'חייב להיות חבר ארגון אחד לפחות עם ההרשאות המינימליות הנדרשות.',\n    passkey_already_exists: 'מפתח הסיסמה כבר רשום במכשיר זה.',\n    passkey_not_supported: 'מפתחות סיסמה אינם נתמכים במכשיר זה.',\n    passkey_pa_not_supported: 'ההרשמה דורשת מאמת פלטפורמה אך המכשיר אינו תומך בכך.',\n    passkey_registration_cancelled: 'רישום מפתח הסיסמה בוטל או פג הזמן הקצוב.',\n    passkey_retrieval_cancelled: 'אימות מפתח הסיסמה בוטל או פג הזמן הקצוב.',\n    passwordComplexity: {\n      maximumLength: 'פחות מ-{{length}} תווים',\n      minimumLength: '{{length}} תווים או יותר',\n      requireLowercase: 'אות קטנה',\n      requireNumbers: 'מספר',\n      requireSpecialCharacter: 'תו מיוחד',\n      requireUppercase: 'אות גדולה',\n      sentencePrefix: 'הסיסמה שלך חייבת להכיל',\n    },\n    phone_number_exists: 'מספר הטלפון הזה כבר בשימוש. אנא נסה מספר אחר.',\n    session_exists: 'אתה כבר מחובר לחשבון.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'הסיסמה שלך תקפה, אך יכולה להיות חזקה יותר. נסה להוסיף יותר תווים.',\n      goodPassword: 'עבודה טובה. זו סיסמה מצוינת.',\n      notEnough: 'הסיסמה שלך אינה מספיק חזקה.',\n      suggestions: {\n        allUppercase: 'הגדל כמה, אך לא את כל האותיות.',\n        anotherWord: 'הוסף עוד מילים שהן פחות נפוצות.',\n        associatedYears: 'הימנע משנים שקשורות אליך.',\n        capitalization: 'הגדל יותר מאות אחת.',\n        dates: 'המנע מתאריכים ושנים שקשורים אליך.',\n        l33t: \"המנע מהחלפות תווים צפויות כמו '@' במקום 'a'.\",\n        longerKeyboardPattern: 'השתמש בדפוסי מקלדת ארוכים יותר ושנה את כיוון ההקלדה מספר פעמים.',\n        noNeed: 'אתה יכול ליצור סיסמאות חזקות ללא שימוש בסמלים, מספרים, או אותיות גדולות.',\n        pwned: 'אם אתה משתמש בסיסמה זו במקומות אחרים, עליך לשנותה.',\n        recentYears: 'הימנע משנים אחרונות.',\n        repeated: 'הימנע ממילים ותווים מוחזרים.',\n        reverseWords: 'הימנע מכתיבה הפוכה של מילים נפוצות.',\n        sequences: 'המנע מרצפות תווים נפוצות.',\n        useWords: 'השתמש במילים מרובות, אך הימנע מביטויים נפוצים.',\n      },\n      warnings: {\n        common: 'זו סיסמה שנמצאת בשימוש נפוץ.',\n        commonNames: 'שמות נפוצים ושמות משפחה קלים לניחוש.',\n        dates: 'תאריכים קלים לניחוש.',\n        extendedRepeat: 'דפוסים מוחזרים של תווים כמו \"abcabcabc\" קלים לניחוש.',\n        keyPattern: 'דפוסים קצרים של מקלדת קלים לניחוש.',\n        namesByThemselves: 'שמות בודדים או שמות משפחה קלים לניחוש.',\n        pwned: 'הסיסמה שלך הוחשפה במהלך הפרת נתונים באינטרנט.',\n        recentYears: 'שנים אחרונות קלות לניחוש.',\n        sequences: 'רצפות תווים נפוצות כמו \"abc\" קלות לניחוש.',\n        similarToCommon: 'זו סיסמה דומה לסיסמה שנמצאת בשימוש נפוץ.',\n        simpleRepeat: 'תווים מוחזרים כמו \"aaa\" קלים לניחוש.',\n        straightRow: 'שורות ישרות של מקשים במקלדת שלך קלות לניחוש.',\n        topHundred: 'זו סיסמה שנמצאת בשימוש תכוף.',\n        topTen: 'זו סיסמה שנמצאת בשימוש כבד.',\n        userInputs: 'אין להזין נתונים אישיים או קשורים לדף.',\n        wordByItself: 'מילים בודדות קלות לניחוש.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'הוסף חשבון',\n    action__manageAccount: 'נהל חשבון',\n    action__signOut: 'התנתק',\n    action__signOutAll: 'התנתק מכל החשבונות',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'הועתק!',\n      actionLabel__copy: 'העתק הכל',\n      actionLabel__download: 'הורד .txt',\n      actionLabel__print: 'הדפס',\n      infoText1: 'קודי גיבוי יהיו מופעלים לחשבון זה.',\n      infoText2: 'שמור את קודי הגיבוי בסוד ואחסן אותם בבטחה. תוכל לחולל מחדש קודי גיבוי אם אתה חושד שהם נפגעו.',\n      subtitle__codelist: 'אחסן אותם בבטחה ושמור עליהם בסוד.',\n      successMessage:\n        'קודי הגיבוי מופעלים כעת. תוכל להשתמש באחד מאלה כדי להתחבר לחשבון שלך, אם אתה מאבד גישה למכשיר האימות שלך. כל קוד יכול להשתמש בו רק פעם אחת.',\n      successSubtitle: 'תוכל להשתמש באחד מאלה כדי להתחבר לחשבון שלך, אם אתה מאבד גישה למכשיר האימות שלך.',\n      title: 'הוסף אימות קוד גיבוי',\n      title__codelist: 'קודי גיבוי',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'בחר ספק כדי לחבר את החשבון שלך.',\n      formHint__noAccounts: 'אין ספקים זמינים לחשבונות חיצוניים.',\n      removeResource: {\n        messageLine1: '{{identifier}} יוסר מהחשבון הזה.',\n        messageLine2: 'לא תוכל יותר להשתמש בחשבון מחובר זה וכל התכונות התלויות לא יעבדו.',\n        successMessage: '{{connectedAccount}} הוסר מהחשבון שלך.',\n        title: 'הסר חשבון מחובר',\n      },\n      socialButtonsBlockButton: 'חבר חשבון {{provider|titleize}}',\n      successMessage: 'הספק התווסף לחשבון שלך',\n      title: 'הוסף חשבון מחובר',\n    },\n    deletePage: {\n      actionDescription: 'הקלד \"מחק חשבון\" למטה כדי להמשיך.',\n      confirm: 'מחק חשבון',\n      messageLine1: 'האם אתה בטוח שאתה רוצה למחוק את החשבון שלך?',\n      messageLine2: 'פעולה זו היא סופית ובלתי הפיכה.',\n      title: 'מחק חשבון',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'אימייל שמכיל קוד אימות ישלח לכתובת זו.',\n        formSubtitle: 'הכנס את קוד האימות שנשלח ל-{{identifier}}',\n        formTitle: 'קוד אימות',\n        resendButton: 'שלח קוד מחדש',\n        successMessage: 'האימייל {{identifier}} התווסף לחשבון שלך.',\n      },\n      emailLink: {\n        formHint: 'אימייל שמכיל קישור לאימות ישלח לכתובת זו.',\n        formSubtitle: 'לחץ על קישור האימות באימייל שנשלח ל-{{identifier}}',\n        formTitle: 'קישור לאימות',\n        resendButton: 'שלח קישור מחדש',\n        successMessage: 'האימייל {{identifier}} התווסף לחשבון שלך.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} יוסר מהחשבון הזה.',\n        messageLine2: 'לא תוכל יותר להתחבר באמצעות כתובת אימייל זו.',\n        successMessage: 'האימייל {{emailAddress}} הוסר מהחשבון שלך.',\n        title: 'הסר כתובת אימייל',\n      },\n      title: 'הוסף כתובת אימייל',\n      verifyTitle: 'אמת כתובת אימייל',\n    },\n    formButtonPrimary__add: 'הוסף',\n    formButtonPrimary__continue: 'המשך',\n    formButtonPrimary__finish: 'סיים',\n    formButtonPrimary__remove: 'מחק',\n    formButtonPrimary__save: 'שמור',\n    formButtonReset: 'בטל',\n    mfaPage: {\n      formHint: 'בחר שיטה להוספה.',\n      title: 'הוסף אימות דו-שלבי',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'השתמש במספר קיים',\n      primaryButton__addPhoneNumber: 'הוסף מספר טלפון',\n      removeResource: {\n        messageLine1: '{{identifier}} לא יקבל יותר קודים לאימות בעת ההתחברות.',\n        messageLine2: 'החשבון שלך עשוי לא להיות בטוח כמו שהוא. האם אתה בטוח שאתה רוצה להמשיך?',\n        successMessage: 'אימות קוד SMS דו-שלבי הוסר ל{{mfaPhoneCode}}',\n        title: 'הסר אימות דו-שלבי',\n      },\n      subtitle__availablePhoneNumbers: 'בחר מספר טלפון להרשמה לאימות קוד SMS דו-שלבי.',\n      subtitle__unavailablePhoneNumbers: 'אין מספרי טלפון זמינים להרשמה לאימות קוד SMS דו-שלבי.',\n      successMessage1: 'בעת ההתחברות, תצטרך להזין קוד אימות שנשלח למספר טלפון זה כשלב נוסף.',\n      successMessage2:\n        'שמור קודי גיבוי אלו במקום בטוח. אם תאבד גישה למכשיר האימות שלך, תוכל להשתמש בקודי גיבוי כדי להתחבר.',\n      successTitle: 'אימות קוד SMS הופעל',\n      title: 'הוסף אימות קוד SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'סרוק קוד QR במקום',\n        buttonUnableToScan__nonPrimary: 'לא יכול לסרוק קוד QR?',\n        infoText__ableToScan:\n          'הגדר שיטת התחברות חדשה באפליקציית האותנטיקטור שלך וסרוק את קוד ה-QR הבא כדי לחברו לחשבון שלך.',\n        infoText__unableToScan: 'הגדר שיטת התחברות חדשה באותנטיקטור שלך והכנס את המפתח שסופק למטה.',\n        inputLabel__unableToScan1: 'וודא שסיסמאות מבוססות-זמן או חד-פעמיות מופעלות, ואז סיים לחבר את החשבון שלך.',\n        inputLabel__unableToScan2:\n          'לחלופין, אם האותנטיקטור שלך תומך בכתובת URI של TOTP, תוכל גם להעתיק את הכתובת המלאה.',\n      },\n      removeResource: {\n        messageLine1: 'קודי האימות מהאותנטיקטור הזה לא יהיו נדרשים יותר בעת ההתחברות.',\n        messageLine2: 'החשבון שלך עשוי לא להיות בטוח כמו שהוא. האם אתה בטוח שאתה רוצה להמשיך?',\n        successMessage: 'אימות דו-שלבי באמצעות אפליקצית האותנטיקטור הוסר.',\n        title: 'הסר אימות דו-שלבי',\n      },\n      successMessage: 'האימות הדו-שלבי מופעל כעת. בעת ההתחברות, תידרש להכניס קוד אימות מהאותנטיקטור זה כשלב נוסף.',\n      title: 'הוסף אפליקציית אימות',\n      verifySubtitle: 'הכנס את קוד האימות שנוצר על ידי האותנטיקטור שלך',\n      verifyTitle: 'קוד אימות',\n    },\n    mobileButton__menu: 'תפריט',\n    navbar: {\n      account: 'פרופיל',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'נהל את פרטי החשבון שלך.',\n      security: 'אבטחה',\n      title: 'חשבון',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} יוסר מחשבון זה',\n        title: 'הסר מפתח סיסמה',\n      },\n      subtitle__rename: 'אתה יכול לשנות את שם מפתח הסיסמה על מנת למצוא אותו בקלות יותר',\n      title__rename: 'שנה שם למפתח סיסמה',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions: 'מומלץ להתנתק מכל המכשירים האחרים שעלולים היו להשתמש בסיסמה הישנה שלך.',\n      readonly: 'כרגע לא ניתן לערוך את הסיסמה שלך מכיוון שניתן להתחבר רק דרך חיבור ארגוני.',\n      successMessage__set: 'הסיסמה שלך הוגדרה.',\n      successMessage__signOutOfOtherSessions: 'כל המכשירים האחרים התנתקו.',\n      successMessage__update: 'הסיסמה שלך עודכנה.',\n      title__set: 'הגדר סיסמה',\n      title__update: 'שנה סיסמה',\n    },\n    phoneNumberPage: {\n      infoText: 'הודעת טקסט שמכילה קישור לאימות תישלח למספר טלפון זה.',\n      removeResource: {\n        messageLine1: '{{identifier}} יוסר מהחשבון הזה.',\n        messageLine2: 'לא תוכל יותר להתחבר באמצעות מספר טלפון זה.',\n        successMessage: '{{phoneNumber}} הוסר מהחשבון שלך.',\n        title: 'הסר מספר טלפון',\n      },\n      successMessage: '{{identifier}} התווסף לחשבון שלך.',\n      title: 'הוסף מספר טלפון',\n      verifySubtitle: 'הזן את קוד האימות שנשלח ל{{identifier}}',\n      verifyTitle: 'אמת מספר טלפון',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'העלה תמונה בפורמט JPG, PNG, GIF, או WEBP הקטנה מ-10 מ\"ב',\n      imageFormDestructiveActionSubtitle: 'הסר תמונה',\n      imageFormSubtitle: 'העלה תמונה',\n      imageFormTitle: 'תמונת פרופיל',\n      readonly: 'פרטי הפרופיל שלך ניתנים על ידי החיבור הארגוני ואינם ניתנים לעריכה.',\n      successMessage: 'הפרופיל שלך עודכן.',\n      title: 'עדכן פרופיל',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'התנתק מהמכשיר',\n        title: 'מכשירים פעילים',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'נסה שוב',\n        actionLabel__reauthorize: 'אשר עכשיו',\n        destructiveActionTitle: 'הסר',\n        primaryButton: 'חבר חשבון',\n        subtitle__disconnected: 'החשבון הזה נותק',\n        subtitle__reauthorize:\n          'ההרשאות הנדרשות עודכנו, ייתכן שאתה חווה פונקציונליות מוגבלת. אנא אשר מחדש את האפליקציה כדי להימנע מבעיות.',\n        title: 'חשבונות מחוברים',\n      },\n      dangerSection: {\n        deleteAccountButton: 'מחק חשבון',\n        title: 'מסוכן',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'הסר כתובת אימייל',\n        detailsAction__nonPrimary: 'הגדר כעיקרית',\n        detailsAction__primary: 'השלם אימות',\n        detailsAction__unverified: 'השלם אימות',\n        primaryButton: 'הוסף כתובת אימייל',\n        title: 'כתובת אימייל',\n      },\n      enterpriseAccountsSection: {\n        title: 'חשבונות ארגוניים',\n      },\n      headerTitle__account: 'חשבון',\n      headerTitle__security: 'אבטחה',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'צור קודים מחדש',\n          headerTitle: 'קוד גיבוי',\n          subtitle__regenerate: 'קבל קבוצה חדשה של קודי גיבוי מאובטחים. קודי גיבוי קודמים יימחקו ולא ניתן להשתמש בהם.',\n          title__regenerate: 'צור מחדש קודי גיבוי',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'הגדר כברירת מחדל',\n          destructiveActionLabel: 'הסר מספר טלפון',\n        },\n        primaryButton: 'הוסף אימות דו-שלבי',\n        title: 'אימות דו-שלבי',\n        totp: {\n          destructiveActionTitle: 'הסר',\n          headerTitle: 'אפליקציית מאמת',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'הגדר סיסמה',\n        menuAction__rename: 'עדכן סיסמה',\n        primaryButton: undefined,\n        title: 'סיסמה',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'הגדר סיסמה',\n        primaryButton__updatePassword: 'שנה סיסמה',\n        title: 'סיסמה',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'הסר מספר טלפון',\n        detailsAction__nonPrimary: 'הגדר כראשי',\n        detailsAction__primary: 'השלם אימות',\n        detailsAction__unverified: 'השלם אימות',\n        primaryButton: 'הוסף מספר טלפון',\n        title: 'מספרי טלפון',\n      },\n      profileSection: {\n        primaryButton: 'עדכן פרופיל',\n        title: 'פרופיל',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'הגדר שם משתמש',\n        primaryButton__updateUsername: 'שנה שם משתמש',\n        title: 'שם משתמש',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'הסר ארנק',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'ארנקי Web3',\n        title: 'ארנקי Web3',\n      },\n    },\n    usernamePage: {\n      successMessage: 'שם המשתמש שלך עודכן.',\n      title__set: 'עדכן שם משתמש',\n      title__update: 'עדכן שם משתמש',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} יוסר מהחשבון הזה.',\n        messageLine2: 'לא תוכל יותר להתחבר באמצעות ארנק web3 זה.',\n        successMessage: '{{web3Wallet}} הוסר מהחשבון שלך.',\n        title: 'הסר ארנק web3',\n      },\n      subtitle__availableWallets: 'בחר ארנק web3 לחיבור לחשבון שלך.',\n      subtitle__unavailableWallets: 'אין ארנקי web3 זמינים.',\n      successMessage: 'הארנק התווסף לחשבון שלך.',\n      title: 'הוסף ארנק web3',\n      web3WalletButtonsBlockButton: '{{provider|titleize}}',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BACE;AAAA,IACF,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WAAW;AAAA,MACX,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCAAiC;AAAA,MACjC,mCAAmC;AAAA,MACnC,iBAAiB;AAAA,MACjB,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CAA0C;AAAA,MAC1C,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBAAsB;AAAA,UACtB,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}