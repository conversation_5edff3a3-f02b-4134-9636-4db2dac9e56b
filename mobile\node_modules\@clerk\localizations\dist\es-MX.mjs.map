{"version": 3, "sources": ["../src/es-MX.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const esMX: LocalizationResource = {\n  locale: 'es-MX',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Atrás',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Por defecto',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Otro dispositivo de imitación',\n  badge__primary: 'Primario',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Requiere acción',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Este dispositivo',\n  badge__unverified: 'No confirmado',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Dispositivo de usuario',\n  badge__you: 'Usted',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Crear organización',\n    invitePage: {\n      formButtonReset: 'Saltar',\n    },\n    title: 'Crear organización',\n  },\n  dates: {\n    lastDay: \"Ayer a las {{ date | timeString('es-ES') }}\",\n    next6Days: \"{{ date | weekday('es-ES','long') }} a las {{ date | timeString('es-ES') }}\",\n    nextDay: \"Mañana a las {{ date | timeString('es-ES') }}\",\n    numeric: \"{{ date | numeric('es-ES') }}\",\n    previous6Days: \"Último {{ date | weekday('es-ES','long') }} en {{ date | timeString('es-ES') }}\",\n    sameDay: \"Hoy a las {{ date | timeString('es-ES') }}\",\n  },\n  dividerText: 'o',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Usar otro método',\n  footerPageLink__help: 'Ayuda',\n  footerPageLink__privacy: 'Privacidad',\n  footerPageLink__terms: 'Términos',\n  formButtonPrimary: 'Continuar',\n  formButtonPrimary__verify: 'Verificar',\n  formFieldAction__forgotPassword: 'Has olvidado tu contraseña?',\n  formFieldError__matchingPasswords: 'Las contraseñas coinciden.',\n  formFieldError__notMatchingPasswords: 'Las no contraseñas coinciden.',\n  formFieldError__verificationLinkExpired: 'El link de verificación expiro. Porfavor vuelva a solicitarlo.',\n  formFieldHintText__optional: 'Opcional',\n  formFieldHintText__slug:\n    'Un slug es una identificación legible por humanos que debe ser única. Se utiliza a menudo en URL.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Eliminar cuenta',\n  formFieldInputPlaceholder__emailAddress: 'Ingresa tu correo electrónico',\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses:\n    'Ingrese o pegue una o más direcciones de correo electrónico, separadas por espacios o comas',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: undefined,\n  formFieldInputPlaceholder__password: 'Ingresa tu contraseña',\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Activar invitaciones automaticas para este dominio',\n  formFieldLabel__backupCode: 'Código de respaldo',\n  formFieldLabel__confirmDeletion: 'Confirmarción',\n  formFieldLabel__confirmPassword: 'Confirme la contraseña',\n  formFieldLabel__currentPassword: 'Contraseña actual',\n  formFieldLabel__emailAddress: 'Correo electrónico',\n  formFieldLabel__emailAddress_username: 'Correo electrónico o nombre de usuario',\n  formFieldLabel__emailAddresses: 'Direcciones de correo',\n  formFieldLabel__firstName: 'Nombre',\n  formFieldLabel__lastName: 'Apellido',\n  formFieldLabel__newPassword: 'Nueva contraseña',\n  formFieldLabel__organizationDomain: 'Domain',\n  formFieldLabel__organizationDomainDeletePending: 'Eliminar invitaciones y sugerencias pendientes',\n  formFieldLabel__organizationDomainEmailAddress: 'Verificación de correo',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Entrar una dirección de correo electrónico bajo este dominio para recibir un código y verificarlo.',\n  formFieldLabel__organizationName: 'Nombre de la Organización',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Nombre de llave de acceso',\n  formFieldLabel__password: 'Contraseña',\n  formFieldLabel__phoneNumber: 'Número telefónico',\n  formFieldLabel__role: 'Rol',\n  formFieldLabel__signOutOfOtherSessions: 'Cerrar sesión en todos los demás dispositivos',\n  formFieldLabel__username: 'Nombre de usuario',\n  impersonationFab: {\n    action__signOut: 'Cerrar',\n    title: 'Registrado como {{identifier}}',\n  },\n  maintenanceMode: 'Actualmente estamos en mantenimiento, pero no te preocupes, no debería llevar más de unos minutos.',\n  membershipRole__admin: 'Administrador',\n  membershipRole__basicMember: 'Miembro',\n  membershipRole__guestMember: 'Invitado',\n  organizationList: {\n    action__createOrganization: 'Crear organización',\n    action__invitationAccept: 'Unirse',\n    action__suggestionsAccept: 'Pedir unirse',\n    createOrganization: 'Crear Organización',\n    invitationAcceptedLabel: 'Unido',\n    subtitle: 'Para continuar con {{applicationName}}',\n    suggestionsAcceptedLabel: 'Aprobación pendiente',\n    title: 'Elige una cuenta',\n    titleWithoutPersonal: 'Elige una organización',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Invitaciones automaticas',\n    badge__automaticSuggestion: 'Sugerencias automaticas',\n    badge__manualInvitation: 'Sin inscripciónes automaticas',\n    badge__unverified: 'No verificado',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Añada el email para verificar. Los usuarios con direcciones de correo electrónico en este dominio pueden unirse a la organización aquí o pedir unirse.',\n      title: 'Anadir dominio',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'No se pudieron enviar las invitaciones. Solucione lo siguiente y vuelva a intentarlo:',\n      formButtonPrimary__continue: 'Enviar invitaciones',\n      selectDropdown__role: 'Select role',\n      subtitle: 'Invitar nuevos miembros a esta organización',\n      successMessage: 'Invitaciones enviadas con éxito',\n      title: 'Invitar miembros',\n    },\n    membersPage: {\n      action__invite: 'Invitar',\n      action__search: 'Buscar',\n      activeMembersTab: {\n        menuAction__remove: 'Eliminar miembro',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'Se unió',\n        tableHeader__role: 'Rol',\n        tableHeader__user: 'Usuario',\n      },\n      detailsTitle__emptyRow: 'No hay miembros para mostrar',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Invita usuarios conectando un dominio de correo electrónico con su organización. Cualquiera que se registre con un dominio de correo electrónico coincidente podrá unirse a la organización en cualquier momento.',\n          headerTitle: 'Invitaciones automaticas',\n          primaryButton: 'Gestionar dominios verificados',\n        },\n        table__emptyRow: 'No hay invitaciones para mostrar',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Revocar invitación',\n        tableHeader__invited: 'Invitado',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Los usuarios que inicien sesión con un dominio de correo electrónico coincidente podrán ver una sugerencia para solicitar unirse a su organización.',\n          headerTitle: 'Sugerencias automaticas',\n          primaryButton: 'Gestionar dominios verificados',\n        },\n        menuAction__approve: 'Aprobado',\n        menuAction__reject: 'Rechazado',\n        tableHeader__requested: 'Acceso solicitado',\n        table__emptyRow: 'No hay solicitudes para mostrar',\n      },\n      start: {\n        headerTitle__invitations: 'Invitaciones',\n        headerTitle__members: 'Miembros',\n        headerTitle__requests: 'Solicitudes',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Gestiona tu organización.',\n      general: 'General',\n      members: 'Miembros',\n      title: 'Organización',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Escribe \"{{organizationName}}\" a continuación para continuar.',\n          messageLine1: '¿Estas seguro que quieres eliminar esta organización?',\n          messageLine2: 'Esta acción es permanente e irreversible.',\n          successMessage: 'Haz eliminado la organización.',\n          title: 'Eliminar la organización',\n        },\n        leaveOrganization: {\n          actionDescription: 'Escribe \"{{organizationName}}\" a continuación para continuar.',\n          messageLine1:\n            '¿Está seguro de que desea abandonar esta organización? Perderá el acceso a esta organización y sus aplicaciones.',\n          messageLine2: 'Esta acción es permanente e irreversible.',\n          successMessage: 'Has dejado la organización.',\n          title: 'Abandonar la organización',\n        },\n        title: 'Peligro',\n      },\n      domainSection: {\n        menuAction__manage: 'Gestionar',\n        menuAction__remove: 'Eliminar',\n        menuAction__verify: 'Verificar',\n        primaryButton: 'Añadir dominio',\n        subtitle:\n          'Permite a los usuarios conectarse automaticamente o solicitar unirse a la organización basado en un dominio de correo electrónico verificado.',\n        title: 'Verified domains',\n      },\n      successMessage: 'La organización ha sido actualizada.',\n      title: 'Perfil de la organización',\n    },\n    removeDomainPage: {\n      messageLine1: 'Se eliminará el dominio de correo electrónico {{domain}}.',\n      messageLine2:\n        'Los usuarios no podrán unirse a la organización de manera automática una vez que se haya eliminado.',\n      successMessage: '{{domain}} se ha eliminado.',\n      title: 'Eliminar dominio',\n    },\n    start: {\n      headerTitle__general: 'General',\n      headerTitle__members: 'Miembros',\n      profileSection: {\n        primaryButton: 'Actualizar perfil',\n        title: 'Perfil de la organización',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Eliminar este dominio afectará a los usuarios invitados.',\n        removeDomainActionLabel__remove: 'Eliminar dominio',\n        removeDomainSubtitle: 'Eliminar este dominio de los dominios verificados',\n        removeDomainTitle: 'Eliminar dominio',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Los usuarios se unen automáticamente a la organización cuando se registran y pueden unirse en cualquier momento.',\n        automaticInvitationOption__label: 'Invitaciones automáticas',\n        automaticSuggestionOption__description:\n          'Los usuarios reciben una sugerencia para solicitar unirse, pero deben ser aprobados por un administrador antes de poder unirse a la organización.',\n        automaticSuggestionOption__label: 'Sugerencias automáticas',\n        calloutInfoLabel: 'Cambiar el modo de inscripción solo afectará a los nuevos usuarios.',\n        calloutInvitationCountLabel: 'Invitaciones pendientes enviadas a usuarios: {{count}}',\n        calloutSuggestionCountLabel: 'Sugerencias pendientes enviadas a usuarios: {{count}}',\n        manualInvitationOption__description: 'Los usuarios solo pueden ser invitados manualmente a la organización.',\n        manualInvitationOption__label: 'Sin inscripción automática',\n        subtitle: 'Seleccione cómo los usuarios de este dominio pueden unirse a la organización.',\n      },\n      start: {\n        headerTitle__danger: 'Peligro',\n        headerTitle__enrollment: 'Opciones de inscripción',\n      },\n      subtitle: 'El dominio {{domain}} ahora está verificado. Continúa seleccionando el modo de inscripción.',\n      title: 'Actualizar {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Introduce el código de verificación enviado a su dirección de correo electrónico',\n      formTitle: 'Código de verificación',\n      resendButton: 'No recibiste un código? Reenviar',\n      subtitle: 'El dominio {{domainName}} necesita ser verificado vía correo electrónico.',\n      subtitleVerificationCodeScreen:\n        'Se envió un código de verificación a {{emailAddress}}. Introduzca el código para continuar.',\n      title: 'Verificar dominio',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Crear Organización',\n    action__invitationAccept: 'Unirse',\n    action__manageOrganization: 'Administrar Organización',\n    action__suggestionsAccept: 'Solicitar unirse',\n    notSelected: 'Ninguna organización seleccionada',\n    personalWorkspace: 'Espacio personal',\n    suggestionsAcceptedLabel: 'Pendiente de aprobación',\n  },\n  paginationButton__next: 'Siguiente',\n  paginationButton__previous: 'Anterior',\n  paginationRowText__displaying: 'Mostrando',\n  paginationRowText__of: 'de',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Agregar cuenta',\n      action__signOutAll: 'Cerrar sesión de todas las cuentas',\n      subtitle: 'Seleccione la cuenta con la que desea continuar.',\n      title: 'Elija una cuenta',\n    },\n    alternativeMethods: {\n      actionLink: 'Obtener ayuda',\n      actionText: '¿No tienes ninguno de estos?',\n      blockButton__backupCode: 'Usa un código de respaldo',\n      blockButton__emailCode: 'Enviar código a{{identifier}}',\n      blockButton__emailLink: 'Enviar enlace a{{identifier}}',\n      blockButton__passkey: 'Inicia sesión con tu llave de acceso',\n      blockButton__password: 'Inicia sesión con tu contraseña',\n      blockButton__phoneCode: 'Enviar código a{{identifier}}',\n      blockButton__totp: 'Usa tu aplicación de autenticación',\n      getHelp: {\n        blockButton__emailSupport: 'Soporte de correo electrónico',\n        content:\n          'Si tiene problemas para ingresar a su cuenta, envíenos un correo electrónico y trabajaremos con usted para restablecer el acceso lo antes posible.',\n        title: 'Obtener ayuda',\n      },\n      subtitle: 'Si está experimentando problemas, puede utilizar uno de estos métodos para ingresar.',\n      title: 'Utiliza otro método',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Introduce un código de seguridad',\n    },\n    emailCode: {\n      formTitle: 'Código de verificación',\n      resendButton: 'Re-enviar código',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Revise su correo electrónico',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: 'Regresa a la pestaña original para continuar.',\n        title: 'Este enlace de verificación ha expirado',\n      },\n      failed: {\n        subtitle: 'Regresa a la pestaña original para continuar.',\n        title: 'Este enlace de verificación es invalido',\n      },\n      formSubtitle: 'Utilice el enlace de verificación enviado a su correo electrónico',\n      formTitle: 'Enlace de verificación',\n      loading: {\n        subtitle: 'Serás redirigido pronto',\n        title: 'Iniciando sesión...',\n      },\n      resendButton: 'Reenviar enlace',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Revise su correo electrónico',\n      unusedTab: {\n        title: 'Puede cerrar esta pestaña',\n      },\n      verified: {\n        subtitle: 'Serás redirigido pronto',\n        title: 'Inició sesión con éxito',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Regrese a la pestaña original para continuar',\n        subtitleNewTab: 'Regrese a la pestaña recién abierta para continuar',\n        titleNewTab: 'Inició sesión en otra pestaña',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Código para restablecer la contraseña',\n      resendButton: 'No recibiste un código? Reenviar',\n      subtitle: 'para restablecer tu contraseña',\n      subtitle_email: 'Primero, introduce el código enviado a tu {{email}}',\n      subtitle_phone: 'Primero, introduce el código enviado a tu {{phone}}',\n      title: 'Restablecer contraseña',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Restablecer tu contraseña',\n      label__alternativeMethods: 'O, inicia sesión con otro método',\n      title: '¿Olvidaste tu contraseña?',\n    },\n    noAvailableMethods: {\n      message: 'No se puede continuar con el inicio de sesión. No hay ningún factor de autenticación disponible.',\n      subtitle: 'Ocurrió un error',\n      title: 'No puedo iniciar sesión',\n    },\n    passkey: {\n      subtitle:\n        'Usando tu llave de acceso confirmas que eres tú. Tu dispositivo puede pedirte la huella dactilar, el rostro o el bloqueo de pantalla.',\n      title: 'Usa tu llave de acceso',\n    },\n    password: {\n      actionLink: 'Use otro método',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Introduzca su contraseña',\n    },\n    passwordPwned: {\n      title: 'Contraseña en peligro',\n    },\n    phoneCode: {\n      formTitle: 'Código de verificación',\n      resendButton: 'Reenviar código',\n      subtitle: 'para continuar con {{applicationName}}',\n      title: 'Revisa tu teléfono',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Código de verificación',\n      resendButton: 'Reenviar código',\n      subtitle: 'para continuar con {{applicationName}}',\n      title: 'Revisa tu teléfono',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Restablecer contraseña',\n      requiredMessage: 'Por razones de seguridad, es necesario restablecer su contraseña.',\n      successMessage:\n        'Tu contraseña se ha cambiado correctamente. Te estamos redirigiendo, por favor espera un momento.',\n      title: 'Establecer nueva contraseña',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Es necesario verificar su identidad para restablecer su contraseña.',\n    },\n    start: {\n      actionLink: 'Registrarse',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Utilizar correo electrónico',\n      actionLink__use_email_username: 'Utilizar correo electrónico o nombre de usuario',\n      actionLink__use_passkey: 'Usar llave de acceso',\n      actionLink__use_phone: 'Utilizar teléfono',\n      actionLink__use_username: 'Utilizar nombre de usuario',\n      actionText: '¿No tiene cuenta?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'para continuar con {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Iniciar sesión',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Código de verificación',\n      subtitle: 'Para continuar, por favor introduce el código generado por tu aplicación de autenticación',\n      title: 'Verificación de dos factores',\n    },\n  },\n  signInEnterPasswordTitle: 'Ingresa tu contraseña',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Entrar',\n      actionText: '¿Tiene una cuenta?',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Rellene los campos que faltan',\n    },\n    emailCode: {\n      formSubtitle: 'Introduzca el código de verificación enviado a su correo electrónico',\n      formTitle: 'Código de verificación',\n      resendButton: 'Reenviar código',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Verifique su correo electrónico',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: 'Utilice el enlace de verificación enviado a su dirección de correo electrónico',\n      formTitle: 'Enlace de verificación',\n      loading: {\n        title: 'Registrando...',\n      },\n      resendButton: 'Reenviar enlace',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Verifica tu correo electrónico',\n      verified: {\n        title: 'Registrado con éxito',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Regrese a la pestaña recién abierta para continuar',\n        subtitleNewTab: 'Volver a la pestaña anterior para continuar',\n        title: 'Correo electrónico verificado con éxito',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Introduzca el código de verificación enviado a su número de teléfono.',\n      formTitle: 'Código de verificación',\n      resendButton: 'Reenviar código',\n      subtitle: 'para continuar a {{applicationName}}',\n      title: 'Verifique su teléfono',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Acceder',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: '¿Tienes una cuenta?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'para continuar con {{applicationName}}',\n      subtitleCombined: 'para continuar con {{applicationName}}',\n      title: 'Crea tu cuenta',\n      titleCombined: 'Crea tu cuenta',\n    },\n  },\n  socialButtonsBlockButton: 'Continuar con {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'El registro falló debido a fallos en la validación de seguridad. Por favor, recargue la página o contáctenos para obtener más asistencia.',\n    captcha_unavailable:\n      'El registro falló debido a fallos en la validación de bot. Por favor, recargue la página o contáctenos para obtener más asistencia.',\n    form_code_incorrect: 'Código incorrecto.',\n    form_identifier_exists__email_address: 'La dirección de correo ya existe.',\n    form_identifier_exists__phone_number: 'El número de teléfono ya existe.',\n    form_identifier_exists__username: 'El nombre de usuario ya existe.',\n    form_identifier_not_found: 'No se encontró una cuenta con esos detalles.',\n    form_param_format_invalid: 'Formato inválido.',\n    form_param_format_invalid__email_address: 'La dirección de correo debe ser válida.',\n    form_param_format_invalid__phone_number: 'El número de teléfono debe ser en un formato válido internacional.',\n    form_param_max_length_exceeded__first_name: 'El nombre debe tener menos de 256 caracteres.',\n    form_param_max_length_exceeded__last_name: 'El apellido debe tener menos de 256 caracteres.',\n    form_param_max_length_exceeded__name: 'El nombre debe tener menos de 256 caracteres.',\n    form_param_nil: 'Campo vacío.',\n    form_param_value_invalid: undefined,\n    form_password_incorrect: 'Contraseña incorrecta.',\n    form_password_length_too_short: 'La contraseña es muy corta.',\n    form_password_not_strong_enough: 'La contraseña no es suficientemente segura.',\n    form_password_pwned:\n      'Esta contraseña se encontró como parte de una infracción y no se puede usar; pruebe con otra contraseña.',\n    form_password_pwned__sign_in: 'La contraseña es muy insegura.',\n    form_password_size_in_bytes_exceeded:\n      'La contraseña excede el número máximo de bytes permitidos. Por favor, elimine algunos caracteres especiales o reduzca la longitud de la contraseña.',\n    form_password_validation_failed: 'Contraseña incorrecta',\n    form_username_invalid_character: 'Carácter inválido.',\n    form_username_invalid_length: 'La longitud del nombre de usuario es demasiado corta.',\n    identification_deletion_failed: 'No se puede eliminar la última identificación.',\n    not_allowed_access:\n      \"La dirección de correo electrónico o el número de teléfono no está permitido para registrarse. Esto puede deberse al uso de '+', '=', '#' o '.' en tu dirección de correo electrónico, el uso de un dominio conectado a un servicio de correo electrónico temporal o la exclusión explícita. Si cree que se trata de un error, póngase en contacto con el soporte.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: 'Ya se ha registrado una llave de acceso en este dispositivo.',\n    passkey_not_supported: 'Las llaves de acceso no son compatibles con este dispositivo.',\n    passkey_pa_not_supported: 'El registro requiere un autenticador de plataforma, pero el dispositivo no lo admite.',\n    passkey_registration_cancelled: 'El registro de la llave de acceso se ha cancelado o ha expirado.',\n    passkey_retrieval_cancelled: 'La verificación de la llave de acceso se ha cancelado o ha expirado.',\n    passwordComplexity: {\n      maximumLength: 'menos de {{length}} caracteres',\n      minimumLength: '{{length}} o más caracteres',\n      requireLowercase: 'al menos una letra minúscula',\n      requireNumbers: 'al menos un número',\n      requireSpecialCharacter: 'al menos un caracter especial',\n      requireUppercase: 'al menos una letra mayúscula',\n      sentencePrefix: 'Tu contraseña debe contener',\n    },\n    phone_number_exists: 'Este número de teléfono ya está en uso. Por favor, trata con otro.',\n    session_exists: 'Ya has iniciado sesión',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Tu contraseña funciona, pero puede ser más segura. Prueba añadiendo más caracteres.',\n      goodPassword: 'Tu contraseña cumple con todos los requisitos necesarios.',\n      notEnough: 'Tu contraseña no es lo suficientemente segura.',\n      suggestions: {\n        allUppercase: 'Escribe algunas letras en mayúsculas, pero no todas.',\n        anotherWord: 'Añade palabras menos comunes.',\n        associatedYears: 'Evita años asociados contigo.',\n        capitalization: 'Escribe algunas letras en mayúsculas además de la primera.',\n        dates: 'Evita fechas asociadas contigo.',\n        l33t: \"Evita sustituciones predecibles como '@' por 'a'\",\n        longerKeyboardPattern: 'Usa patrones de teclado más largos y cambia la dirección de escritura varias veces.',\n        noNeed: 'Puedes crear contraseñas seguras sin usar símbolos, números o mayúsculas.',\n        pwned: 'Si utiliza esta contraseña en otro lugar, debería cambiarla.',\n        recentYears: 'Evita años recientes.',\n        repeated: 'Evita palabras y letras repetidas.',\n        reverseWords: 'Evita palabras comunes escritas al revés',\n        sequences: 'Evita secuencias de letras comunes.',\n        useWords: 'Usa varias palabras, pero evita frases comunes.',\n      },\n      warnings: {\n        common: 'Es una contraseña usada comúnmente.',\n        commonNames: 'Nombres y apellidos comunes son fáciles de adivinar.',\n        dates: 'Las fechas son fáciles de adivinar.',\n        extendedRepeat: 'Patrones repetidos como \"abcabcabc\" son fáciles de adivinar.',\n        keyPattern: 'Patrones cortos son fáciles de adivinar.',\n        namesByThemselves: 'Nombres y apellidos a solas son fáciles de adivinar.',\n        pwned: 'Su contraseña fue expuesta por una violación de datos en Internet.',\n        recentYears: 'Los años recientes son fáciles de adivinar.',\n        sequences: 'Patrones comunes como \"abc\" son fáciles de adivinar',\n        similarToCommon: 'Es similar a una contraseña usada habitualmente.',\n        simpleRepeat: 'Caracteres repetidos como \"aaa\" son fáciles de adivinar',\n        straightRow: 'Teclas consecutivas en tu teclado son fáciles de adivinar.',\n        topHundred: 'Es una contraseña usada con mucha frecuencia.',\n        topTen: 'Es de las contraseñas más usadas.',\n        userInputs: 'No debería haber datos personales o relacionados con esta página.',\n        wordByItself: 'Palabras únicas son fáciles de adivinar.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Añadir cuenta',\n    action__manageAccount: 'Administrar cuenta',\n    action__signOut: 'Cerrar sesión',\n    action__signOutAll: 'Salir de todas las cuentas',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Copiado!',\n      actionLabel__copy: 'Copiar todo',\n      actionLabel__download: 'Descargar .txt',\n      actionLabel__print: 'Imprimir',\n      infoText1: 'Se habilitarán códigos de respaldo para esta cuenta.',\n      infoText2:\n        'Mantenga los códigos de respaldo en secreto y guárdelos de forma segura. Puede regenerar códigos de respaldo si sospecha que se han visto comprometidos.',\n      subtitle__codelist: 'Guardelos de forma segura y manténgalos en secreto.',\n      successMessage:\n        'Los códigos de respaldo ahora están habilitados. Puede usar uno de estos para iniciar sesión en su cuenta, si pierde el acceso a su dispositivo de autenticación. Cada código solo se puede utilizar una vez.',\n      successSubtitle:\n        'Puede usar uno de estos para iniciar sesión en su cuenta, si pierde el acceso a su dispositivo de autenticación.',\n      title: 'Agregar verificación de código de respaldo',\n      title__codelist: 'Códigos de respaldo',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Seleccione un proveedor para conectar su cuenta.',\n      formHint__noAccounts: 'No hay proveedores de cuentas externas disponibles.',\n      removeResource: {\n        messageLine1: '{{identifier}} será eliminado de esta cuenta.',\n        messageLine2: 'Ya no podrá usar esta cuenta activa y las funciones dependientes ya no funcionarán.',\n        successMessage: '{{connectedAccount}} ha sido eliminado de su cuenta.',\n        title: 'Eliminar cuenta conectada',\n      },\n      socialButtonsBlockButton: 'Conectar cuenta de {{provider|titleize}}',\n      successMessage: 'El proveedor ha sido agregado a su cuenta',\n      title: 'Agregar cuenta conectada',\n    },\n    deletePage: {\n      actionDescription: 'Escribe \"Eliminar cuenta\" a continuación para continuar',\n      confirm: 'Eliminar cuenta',\n      messageLine1: '¿Estas seguro que quieres eliminar tu cuenta?',\n      messageLine2: 'Esta acción es permanente e irreversible.',\n      title: 'Eliminar cuenta',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint:\n          'A esta dirección de correo electrónico se le enviará un correo electrónico con un Código de verificación.',\n        formSubtitle: 'Introduzca el código de verificación enviado a {{identifier}}',\n        formTitle: 'Código de verificación',\n        resendButton: 'Re-enviar código',\n        successMessage: 'El correo electrónico {{identifier}} se ha agregado a su cuenta.',\n      },\n      emailLink: {\n        formHint:\n          'Se enviará un correo electrónico con un enlace de verificación a esta dirección de correo electrónico.',\n        formSubtitle: 'Haga clic en el enlace de verificación en el correo electrónico enviado a {{identifier}}',\n        formTitle: 'Enlace de verificación',\n        resendButton: 'Reenviar enlace',\n        successMessage: 'El correo electrónico {{identifier}} se ha agregado a su cuenta.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} será eliminado de esta cuenta.',\n        messageLine2: 'Ya no podrá iniciar sesión con esta dirección de correo electrónico.',\n        successMessage: '{{emailAddress}} ha sido eliminado de su cuenta.',\n        title: 'Eliminar dirección de correo electrónico',\n      },\n      title: 'Agregar dirección de correo electrónico',\n      verifyTitle: 'Verificar dirección de correo electrónico',\n    },\n    formButtonPrimary__add: 'Agregar',\n    formButtonPrimary__continue: 'Continuar',\n    formButtonPrimary__finish: 'Terminar',\n    formButtonPrimary__remove: 'Eliminar',\n    formButtonPrimary__save: 'Guardar',\n    formButtonReset: 'Cancelar',\n    mfaPage: {\n      formHint: 'Seleccione un método para agregar.',\n      title: 'Agregar verificación en dos pasos',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Usar número existente',\n      primaryButton__addPhoneNumber: 'Agregar número de teléfono',\n      removeResource: {\n        messageLine1: '{{identifier}} dejará de recibir el Código de verificación al iniciar sesión.',\n        messageLine2: 'Es posible que su cuenta no sea tan segura. ¿Estás seguro de que quieres continuar?',\n        successMessage: 'Se eliminó la verificación de dos pasos del código SMS para {{mfaPhoneCode}}',\n        title: 'Eliminar la verificación en dos pasos',\n      },\n      subtitle__availablePhoneNumbers:\n        'Seleccione un número de teléfono para registrarse para la verificación en dos pasos del código SMS.',\n      subtitle__unavailablePhoneNumbers:\n        'No hay números de teléfono disponibles para registrarse para la verificación en dos pasos del código SMS.',\n      successMessage1:\n        'Al iniciar sesión, se le pedirá un código de verificación enviado a este número de teléfono como un paso adicional.',\n      successMessage2:\n        'Guarde estos códigos de respaldo y almacénelos en un lugar seguro. Si pierde el acceso a su dispositivo de autenticación, puede utilizar los códigos de respaldo para iniciar sesión.',\n      successTitle: 'Verificación de código SMS habilitada',\n      title: 'Agregar verificación de código SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Escanea el código QR en su lugar',\n        buttonUnableToScan__nonPrimary: '¿No puedes escanear el código QR?',\n        infoText__ableToScan:\n          'Configure un nuevo método de inicio de sesión en su aplicación de autenticación y escanee el siguiente código QR para vincularlo a su cuenta.',\n        infoText__unableToScan:\n          'Configure un nuevo método de inicio de sesión en su autenticador e ingrese la clave que se proporciona a continuación.',\n        inputLabel__unableToScan1:\n          'Asegúrese de que las contraseñas basadas en el tiempo o de un solo uso estén habilitadas, luego finalice de vincular su cuenta.',\n        inputLabel__unableToScan2:\n          'Alternativamente, si su autenticador admite URIs TOTP, también puede copiar la URI completa.',\n      },\n      removeResource: {\n        messageLine1: 'El código de verificación de este autenticador ya no será necesario al iniciar sesión.',\n        messageLine2: 'Es posible que su cuenta no sea tan segura. ¿Estás seguro de que quieres continuar?',\n        successMessage: 'Se eliminó la verificación en dos pasos a través de la aplicación de autenticación.',\n        title: 'Eliminar la verificación en dos pasos',\n      },\n      successMessage:\n        'La verificación en dos pasos ahora está habilitada. Al iniciar sesión, deberá ingresar un Código de verificación de este autenticador como un paso adicional.',\n      title: 'Agregar aplicación de autenticación',\n      verifySubtitle: 'Ingrese el Código de verificación generado por su autenticador',\n      verifyTitle: 'Código de verificación',\n    },\n    mobileButton__menu: 'Menú',\n    navbar: {\n      account: 'Perfil',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Administra tu información de cuenta.',\n      security: 'Seguridad',\n      title: 'Cuenta',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} será eliminada de esta cuenta.',\n        title: 'Eliminar llave de acceso',\n      },\n      subtitle__rename: 'Puedes cambiar el nombre de la llave de acceso para que sea más fácil de encontrar.',\n      title__rename: 'Renombrar llave de acceso',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Se recomienda cerrar la sesión de todos los otros dispositivos que hayan utilizado su antigua contraseña.',\n      readonly:\n        'Tu contraseña no se puede editar actualmente porque solo se puede acceder a través de la conexión de empresa.',\n      successMessage__set: 'Se ha establecido tu contraseña.',\n      successMessage__signOutOfOtherSessions: 'Se cerró la sesión de todos los demás dispositivos.',\n      successMessage__update: 'Tu contraseña ha sido actualizada.',\n      title__set: 'Configurar la clave',\n      title__update: 'Cambiar contraseña',\n    },\n    phoneNumberPage: {\n      infoText: 'Se enviará un mensaje de texto con un enlace de verificación a este número de teléfono.',\n      removeResource: {\n        messageLine1: '{{identifier}} será eliminado de esta cuenta.',\n        messageLine2: 'Ya no podrá iniciar sesión con este número de teléfono.',\n        successMessage: '{{phoneNumber}} ha sido eliminado de su cuenta.',\n        title: 'Eliminar número de teléfono',\n      },\n      successMessage: '{{identifier}} ha sido añadido a tu cuenta.',\n      title: 'Agregar el número de teléfono',\n      verifySubtitle: 'Ingrese el código de verificación enviado a {{identifier}}',\n      verifyTitle: 'Verificar número de teléfono',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Cargue una imagen JPG, PNG, GIF o WEBP de menos de 10 MB',\n      imageFormDestructiveActionSubtitle: 'Eliminar la imagen',\n      imageFormSubtitle: 'Cargar imagen',\n      imageFormTitle: 'Imagen de perfil',\n      readonly: 'Tu información de perfil ha sido proporcionada por la conexión de empresa y no se puede editar.',\n      successMessage: 'Tu perfil ha sido actualizado.',\n      title: 'Actualizar perfil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Cerrar sesión en el dispositivo',\n        title: 'Dispositivos activos',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Inténtelo nuevamente',\n        actionLabel__reauthorize: 'Autorizar ahora',\n        destructiveActionTitle: 'Quitar',\n        primaryButton: 'Conectar cuenta',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'Los permisos requeridos han sido actualizados, y podría experimentar limitaciones. Por favor, autorice de nuevo esta aplicación para evitar cualquier problema',\n        title: 'Cuentas conectadas',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Eliminar cuenta',\n        title: 'Peligro',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Eliminar dirección de correo electrónico',\n        detailsAction__nonPrimary: 'Establecer como primario',\n        detailsAction__primary: 'Completar la verificación',\n        detailsAction__unverified: 'Completar la verificación',\n        primaryButton: 'Agregar una dirección de correo electrónico',\n        title: 'Correos electrónicos',\n      },\n      enterpriseAccountsSection: {\n        title: 'Cuentas de empresa',\n      },\n      headerTitle__account: 'Cuenta',\n      headerTitle__security: 'Seguridad',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Regenerar códigos',\n          headerTitle: 'Códigos de respaldo',\n          subtitle__regenerate:\n            'Obtenga un nuevo conjunto de códigos de respaldo seguros. Los códigos de respaldo anteriores se eliminarán y no podrán ser usados.',\n          title__regenerate: 'Regenerar códigos de respaldo',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Establecer por defecto',\n          destructiveActionLabel: 'Eliminar número telefónico',\n        },\n        primaryButton: 'Añadir verificación de dos pasos',\n        title: 'Verificación de dos pasos',\n        totp: {\n          destructiveActionTitle: 'Eliminar',\n          headerTitle: 'Aplicación de autenticación',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Eliminar',\n        menuAction__rename: 'Renombrar',\n        primaryButton: undefined,\n        title: 'Llaves de acceso',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Establecer contraseña ',\n        primaryButton__updatePassword: 'Cambiar contraseña',\n        title: 'Contraseña',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Quitar número de teléfono',\n        detailsAction__nonPrimary: 'Establecer como primario',\n        detailsAction__primary: 'Completar la verificación',\n        detailsAction__unverified: 'Completar la verificación',\n        primaryButton: 'Agregar un número de teléfono',\n        title: 'Números telefónicos',\n      },\n      profileSection: {\n        primaryButton: 'Actualizar perfil',\n        title: 'Perfil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Crear nombre de usuario',\n        primaryButton__updateUsername: 'Cambiar nombre de usuario',\n        title: 'Nombre de usuario',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Quitar cartera',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 cartera',\n        title: 'Web3 cartera',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Su nombre de usuario ha sido actualizado.',\n      title__set: 'Actualizar nombre de usuario',\n      title__update: 'Actualizar nombre de usuario',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} será eliminado de esta cuenta.',\n        messageLine2: 'Ya no podrá iniciar sesión con esta billetera web3.',\n        successMessage: '{{web3Wallet}} ha sido eliminado de su cuenta.',\n        title: 'Eliminar la billetera web3',\n      },\n      subtitle__availableWallets: 'Seleccione una billetera web3 para conectarse a su cuenta.',\n      subtitle__unavailableWallets: 'No hay billetera web3 disponibles.',\n      successMessage: 'La billetera ha sido agregada a su cuenta.',\n      title: 'Añadir web3 billetera',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: 'Acceder',\n      actionText: 'Cuentas con acceso?',\n      formButton: 'Unete a la lista de espera',\n      subtitle: 'Ingresa tu correo electrónico y te avisaremos cuando tu lugar esté listo',\n      title: 'Unete a la lista de espera',\n    },\n    success: {\n      message: 'Serás redirigido pronto...',\n      subtitle: 'Nos pondremos en contacto contigo cuando tu lugar esté listo',\n      title: 'Gracias por unirte a la lista de espera!',\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBACE;AAAA,EACF,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cACE;AAAA,MACF,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UACE;AAAA,QACF,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UACE;AAAA,QACF,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}