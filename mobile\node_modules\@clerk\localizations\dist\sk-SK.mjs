// src/sk-SK.ts
var skSK = {
  locale: "sk-SK",
  apiKeys: {
    action__add: void 0,
    action__search: void 0,
    createdAndExpirationStatus__expiresOn: void 0,
    createdAndExpirationStatus__never: void 0,
    detailsTitle__emptyRow: void 0,
    formButtonPrimary__add: void 0,
    formFieldCaption__expiration__expiresOn: void 0,
    formFieldCaption__expiration__never: void 0,
    formFieldOption__expiration__180d: void 0,
    formFieldOption__expiration__1d: void 0,
    formFieldOption__expiration__1y: void 0,
    formFieldOption__expiration__30d: void 0,
    formFieldOption__expiration__60d: void 0,
    formFieldOption__expiration__7d: void 0,
    formFieldOption__expiration__90d: void 0,
    formFieldOption__expiration__never: void 0,
    formHint: void 0,
    formTitle: void 0,
    lastUsed__days: void 0,
    lastUsed__hours: void 0,
    lastUsed__minutes: void 0,
    lastUsed__months: void 0,
    lastUsed__seconds: void 0,
    lastUsed__years: void 0,
    menuAction__revoke: void 0,
    revokeConfirmation: {
      confirmationText: void 0,
      formButtonPrimary__revoke: void 0,
      formHint: void 0,
      formTitle: void 0
    }
  },
  backButton: "Sp\xE4\u0165",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Predvolen\xE9",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "In\xE9 zariadenie z\xE1stupcu",
  badge__primary: "Hlavn\xFD",
  badge__renewsAt: void 0,
  badge__requiresAction: "Vy\u017Eaduje akciu",
  badge__startsAt: void 0,
  badge__thisDevice: "Toto zariadenie",
  badge__unverified: "Nepotvrden\xE9",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Zariadenie pou\u017E\xEDvate\u013Ea",
  badge__you: "Vy",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      pastDueNotice: void 0,
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    credit: void 0,
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDue: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Vytvori\u0165 organiz\xE1ciu",
    invitePage: {
      formButtonReset: "Presko\u010Di\u0165"
    },
    title: "Vytvori\u0165 organiz\xE1ciu"
  },
  dates: {
    lastDay: "V\u010Dera o {{ date | timeString('sk-SK') }}",
    next6Days: "P\u0159\xED\u0161t\xED {{ date | weekday('sk-SK','long') }} o {{ date | timeString('sk-SK') }}",
    nextDay: "Zajtra o {{ date | timeString('sk-SK') }}",
    numeric: "{{ date | numeric('sk-SK') }}",
    previous6Days: "Minul\xFD {{ date | weekday('sk-SK','long') }} o {{ date | timeString('sk-SK') }}",
    sameDay: "Dnes o {{ date | timeString('sk-SK') }}"
  },
  dividerText: "alebo",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Pou\u017Ei\u0165 in\xFA met\xF3du",
  footerPageLink__help: "Pomoc",
  footerPageLink__privacy: "Ochrana s\xFAkromia",
  footerPageLink__terms: "Podmienky",
  formButtonPrimary: "Pokra\u010Dova\u0165",
  formButtonPrimary__verify: "Verify",
  formFieldAction__forgotPassword: "Zabudli ste heslo?",
  formFieldError__matchingPasswords: "Hesl\xE1 sa zhoduj\xFA.",
  formFieldError__notMatchingPasswords: "Hesl\xE1 sa nezhoduj\xFA.",
  formFieldError__verificationLinkExpired: "The verification link expired. Please request a new link.",
  formFieldHintText__optional: "Volite\u013En\xE9",
  formFieldHintText__slug: "A slug is a human-readable ID that must be unique. It\u2019s often used in URLs.",
  formFieldInputPlaceholder__apiKeyDescription: void 0,
  formFieldInputPlaceholder__apiKeyExpirationDate: void 0,
  formFieldInputPlaceholder__apiKeyName: void 0,
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Delete account",
  formFieldInputPlaceholder__emailAddress: void 0,
  formFieldInputPlaceholder__emailAddress_username: void 0,
  formFieldInputPlaceholder__emailAddresses: "Zadajte alebo vlo\u017Ete jednu alebo viac emailov\xFDch adries oddelen\xFDch medzerou alebo \u010Diarkou",
  formFieldInputPlaceholder__firstName: void 0,
  formFieldInputPlaceholder__lastName: void 0,
  formFieldInputPlaceholder__organizationDomain: void 0,
  formFieldInputPlaceholder__organizationDomainEmailAddress: void 0,
  formFieldInputPlaceholder__organizationName: void 0,
  formFieldInputPlaceholder__organizationSlug: void 0,
  formFieldInputPlaceholder__password: void 0,
  formFieldInputPlaceholder__phoneNumber: void 0,
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__apiKeyDescription: void 0,
  formFieldLabel__apiKeyExpiration: void 0,
  formFieldLabel__apiKeyName: void 0,
  formFieldLabel__automaticInvitations: "Enable automatic invitations for this domain",
  formFieldLabel__backupCode: "Z\xE1lo\u017En\xFD k\xF3d",
  formFieldLabel__confirmDeletion: "Confirmation",
  formFieldLabel__confirmPassword: "Potvrdi\u0165 heslo",
  formFieldLabel__currentPassword: "S\xFA\u010Dasn\xE9 heslo",
  formFieldLabel__emailAddress: "Emailov\xE1 adresa",
  formFieldLabel__emailAddress_username: "Emailov\xE1 adresa alebo u\u017E\xEDvate\u013Esk\xE9 meno",
  formFieldLabel__emailAddresses: "Emailov\xE9 adresy",
  formFieldLabel__firstName: "Meno",
  formFieldLabel__lastName: "Priezvisko",
  formFieldLabel__newPassword: "Nov\xE9 heslo",
  formFieldLabel__organizationDomain: "Domain",
  formFieldLabel__organizationDomainDeletePending: "Delete pending invitations and suggestions",
  formFieldLabel__organizationDomainEmailAddress: "Verification email address",
  formFieldLabel__organizationDomainEmailAddressDescription: "Enter an email address under this domain to receive a code and verify this domain.",
  formFieldLabel__organizationName: "N\xE1zov organiz\xE1cie",
  formFieldLabel__organizationSlug: "URL adresa",
  formFieldLabel__passkeyName: void 0,
  formFieldLabel__password: "Heslo",
  formFieldLabel__phoneNumber: "Telef\xF3nne \u010D\xEDslo",
  formFieldLabel__role: "Rola",
  formFieldLabel__signOutOfOtherSessions: "Odhl\xE1si\u0165 sa zo v\u0161etk\xFDch ostatn\xFDch zariaden\xED",
  formFieldLabel__username: "U\u017E\xEDvate\u013Esk\xE9 meno",
  impersonationFab: {
    action__signOut: "Odhl\xE1si\u0165 sa",
    title: "Prihl\xE1sen\xFD(\xE1) ako {{identifier}}"
  },
  maintenanceMode: void 0,
  membershipRole__admin: "Spr\xE1vca",
  membershipRole__basicMember: "\u010Clen",
  membershipRole__guestMember: "Host",
  organizationList: {
    action__createOrganization: "Create organization",
    action__invitationAccept: "Join",
    action__suggestionsAccept: "Request to join",
    createOrganization: "Create Organization",
    invitationAcceptedLabel: "Joined",
    subtitle: "to continue to {{applicationName}}",
    suggestionsAcceptedLabel: "Pending approval",
    title: "Choose an account",
    titleWithoutPersonal: "Choose an organization"
  },
  organizationProfile: {
    apiKeysPage: {
      title: void 0
    },
    badge__automaticInvitation: "Automatic invitations",
    badge__automaticSuggestion: "Automatic suggestions",
    badge__manualInvitation: "No automatic enrollment",
    badge__unverified: "Unverified",
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Add the domain to verify. Users with email addresses at this domain can join the organization automatically or request to join.",
      title: "Add domain"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Pozv\xE1nky sa nepodarilo odosla\u0165. Opravte nasleduj\xFAce a sk\xFAste to znovu:",
      formButtonPrimary__continue: "Odosla\u0165 pozv\xE1nky",
      selectDropdown__role: "Select role",
      subtitle: "Pozva\u0165 nov\xFDch \u010Dlenov do tejto organiz\xE1cie",
      successMessage: "Pozv\xE1nky boli \xFAspe\u0161ne odoslan\xE9.",
      title: "Pozva\u0165 \u010Dlenov"
    },
    membersPage: {
      action__invite: "Pozva\u0165",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "Odstr\xE1ni\u0165 \u010Dlena",
        tableHeader__actions: void 0,
        tableHeader__joined: "Pripojil sa",
        tableHeader__role: "Rola",
        tableHeader__user: "U\u017E\xEDvate\u013E"
      },
      detailsTitle__emptyRow: "\u017Diadni \u010Dlenovia na zobrazenie",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Invite users by connecting an email domain with your organization. Anyone who signs up with a matching email domain will be able to join the organization anytime.",
          headerTitle: "Automatic invitations",
          primaryButton: "Manage verified domains"
        },
        table__emptyRow: "No invitations to display"
      },
      invitedMembersTab: {
        menuAction__revoke: "Zru\u0161i\u0165 pozvanie",
        tableHeader__invited: "Pozvan\xED"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Users who sign up with a matching email domain, will be able to see a suggestion to request to join your organization.",
          headerTitle: "Automatic suggestions",
          primaryButton: "Manage verified domains"
        },
        menuAction__approve: "Approve",
        menuAction__reject: "Reject",
        tableHeader__requested: "Requested access",
        table__emptyRow: "No requests to display"
      },
      start: {
        headerTitle__invitations: "Invitations",
        headerTitle__members: "Members",
        headerTitle__requests: "Requests"
      }
    },
    navbar: {
      apiKeys: void 0,
      billing: void 0,
      description: "Manage your organization.",
      general: "General",
      members: "Members",
      title: "Organization"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Type "{{organizationName}}" below to continue.',
          messageLine1: "Are you sure you want to delete this organization?",
          messageLine2: "This action is permanent and irreversible.",
          successMessage: "You have deleted the organization.",
          title: "Delete organization"
        },
        leaveOrganization: {
          actionDescription: 'Type "{{organizationName}}" below to continue.',
          messageLine1: "Naozaj chcete opusti\u0165 t\xFAto organiz\xE1ciu? Strat\xEDte pr\xEDstup k tejto organiz\xE1cii a jej aplik\xE1ci\xE1m.",
          messageLine2: "T\xE1to akcia je trval\xE1 a nezvratn\xE1.",
          successMessage: "Opustili ste organiz\xE1ciu.",
          title: "Opusti\u0165 organiz\xE1ciu"
        },
        title: "Upozornenie"
      },
      domainSection: {
        menuAction__manage: "Manage",
        menuAction__remove: "Delete",
        menuAction__verify: "Verify",
        primaryButton: "Add domain",
        subtitle: "Allow users to join the organization automatically or request to join based on a verified email domain.",
        title: "Verified domains"
      },
      successMessage: "Organiz\xE1cia bola aktualizovan\xE1.",
      title: "Profil organiz\xE1cie"
    },
    removeDomainPage: {
      messageLine1: "The email domain {{domain}} will be removed.",
      messageLine2: "Users won\u2019t be able to join the organization automatically after this.",
      successMessage: "{{domain}} has been removed.",
      title: "Remove domain"
    },
    start: {
      headerTitle__general: "General",
      headerTitle__members: "\u010Clenovia",
      profileSection: {
        primaryButton: void 0,
        title: "Organization Profile",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Removing this domain will affect invited users.",
        removeDomainActionLabel__remove: "Remove domain",
        removeDomainSubtitle: "Remove this domain from your verified domains",
        removeDomainTitle: "Remove domain"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Users are automatically invited to join the organization when they sign-up and can join anytime.",
        automaticInvitationOption__label: "Automatic invitations",
        automaticSuggestionOption__description: "Users receive a suggestion to request to join, but must be approved by an admin before they are able to join the organization.",
        automaticSuggestionOption__label: "Automatic suggestions",
        calloutInfoLabel: "Changing the enrollment mode will only affect new users.",
        calloutInvitationCountLabel: "Pending invitations sent to users: {{count}}",
        calloutSuggestionCountLabel: "Pending suggestions sent to users: {{count}}",
        manualInvitationOption__description: "Users can only be invited manually to the organization.",
        manualInvitationOption__label: "No automatic enrollment",
        subtitle: "Choose how users from this domain can join the organization."
      },
      start: {
        headerTitle__danger: "Danger",
        headerTitle__enrollment: "Enrollment options"
      },
      subtitle: "The domain {{domain}} is now verified. Continue by selecting enrollment mode.",
      title: "Update {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Enter the verification code sent to your email address",
      formTitle: "Verification code",
      resendButton: "Didn't receive a code? Resend",
      subtitle: "The domain {{domainName}} needs to be verified via email.",
      subtitleVerificationCodeScreen: "A verification code was sent to {{emailAddress}}. Enter the code to continue.",
      title: "Verify domain"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Vytvori\u0165 organiz\xE1ciu",
    action__invitationAccept: "Join",
    action__manageOrganization: "Spravova\u0165 organiz\xE1ciu",
    action__suggestionsAccept: "Request to join",
    notSelected: "Nie je vybran\xE1 \u017Eiadna organiz\xE1cia",
    personalWorkspace: "Osobn\xFD pracovn\xFD priestor",
    suggestionsAcceptedLabel: "Pending approval"
  },
  paginationButton__next: "\u010Eal\u0161ie",
  paginationButton__previous: "Predch\xE1dzaj\xFAce",
  paginationRowText__displaying: "Zobrazuje sa",
  paginationRowText__of: "z",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Add account",
      action__signOutAll: "Sign out of all accounts",
      subtitle: "Select the account with which you wish to continue.",
      title: "Choose an account"
    },
    alternativeMethods: {
      actionLink: "Z\xEDska\u0165 pomoc",
      actionText: "Don\u2019t have any of these?",
      blockButton__backupCode: "Pou\u017Ei\u0165 z\xE1lo\u017En\xFD k\xF3d",
      blockButton__emailCode: "Odosla\u0165 overovac\xED k\xF3d na email {{identifier}}",
      blockButton__emailLink: "Odosla\u0165 odkaz na email {{identifier}}",
      blockButton__passkey: void 0,
      blockButton__password: "Prihl\xE1si\u0165 sa pomocou hesla",
      blockButton__phoneCode: "Posla\u0165 SMS k\xF3d na telef\xF3nne \u010D\xEDslo {{identifier}}",
      blockButton__totp: "Pou\u017Ei\u0165 autentifika\u010Dn\xFA aplik\xE1ciu",
      getHelp: {
        blockButton__emailSupport: "Podpora cez email",
        content: "Ak m\xE1te probl\xE9my s prihl\xE1sen\xEDm do svojho \xFA\u010Dtu, kontaktujte n\xE1s emailom a pok\xFAsime sa v\xE1m \u010Do najsk\xF4r obnovi\u0165 pr\xEDstup.",
        title: "Z\xEDska\u0165 pomoc"
      },
      subtitle: "Facing issues? You can use any of these methods to sign in.",
      title: "Pou\u017Ei\u0165 in\xFA met\xF3du"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "pre pokra\u010Dovanie do {{applicationName}}",
      title: "Zadajte z\xE1lo\u017En\xFD k\xF3d"
    },
    emailCode: {
      formTitle: "Overovac\xED k\xF3d",
      resendButton: "Znovu posla\u0165 k\xF3d",
      subtitle: "pre pokra\u010Dovanie do {{applicationName}}",
      title: "Skontrolujte svoj email"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      expired: {
        subtitle: "Vr\xE1\u0165te sa do p\xF4vodn\xE9ho okna pre pokra\u010Dovanie.",
        title: "Tento overovac\xED odkaz vypr\u0161al"
      },
      failed: {
        subtitle: "Vr\xE1\u0165te sa do p\xF4vodn\xE9ho okna pre pokra\u010Dovanie.",
        title: "Tento overovac\xED odkaz je neplatn\xFD"
      },
      formSubtitle: "Pou\u017Eite overovac\xED odkaz zaslan\xFD na v\xE1\u0161 email",
      formTitle: "Overovac\xED odkaz",
      loading: {
        subtitle: "\u010Coskoro budete presmerovan\xED",
        title: "Prihlasujem..."
      },
      resendButton: "Znovu posla\u0165 odkaz",
      subtitle: "pre pokra\u010Dovanie do {{applicationName}}",
      title: "Skontrolujte svoj email",
      unusedTab: {
        title: "M\xF4\u017Eete zatvori\u0165 toto okno"
      },
      verified: {
        subtitle: "\u010Coskoro budete presmerovan\xED",
        title: "\xDAspe\u0161ne prihl\xE1sen\xE9"
      },
      verifiedSwitchTab: {
        subtitle: "Vr\xE1\u0165te sa do p\xF4vodn\xE9ho okna pre pokra\u010Dovanie",
        subtitleNewTab: "Vr\xE1\u0165te sa do novootvoren\xE9ho okna pre pokra\u010Dovanie",
        titleNewTab: "Prihl\xE1sen\xE9 v inom okne"
      }
    },
    forgotPassword: {
      formTitle: "Overovac\xED k\xF3d pre obnovenie hesla",
      resendButton: "Znovu posla\u0165 k\xF3d",
      subtitle: "to reset your password",
      subtitle_email: "First, enter the code sent to your email ID",
      subtitle_phone: "First, enter the code sent to your phone",
      title: "Reset password"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Obnovi\u0165 heslo",
      label__alternativeMethods: "Alebo sa prihl\xE1ste pomocou inej met\xF3dy.",
      title: "Zabudli ste heslo?"
    },
    noAvailableMethods: {
      message: "Nemo\u017Eno pokra\u010Dova\u0165 v prihl\xE1sen\xED. Nie je k dispoz\xEDcii \u017Eiadna dostupn\xE1 autentifika\u010Dn\xE1 met\xF3da.",
      subtitle: "Do\u0161lo k chybe",
      title: "Nie je mo\u017En\xE9 sa prihl\xE1si\u0165"
    },
    passkey: {
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "Pou\u017Ei\u0165 in\xFA met\xF3du",
      subtitle: "pre pokra\u010Dovanie do {{applicationName}}",
      title: "Zadajte svoje heslo"
    },
    passwordPwned: {
      title: void 0
    },
    phoneCode: {
      formTitle: "Overovac\xED k\xF3d",
      resendButton: "Znova odosla\u0165 k\xF3d",
      subtitle: "pre pokra\u010Dovanie do {{applicationName}}",
      title: "Skontrolujte v\xE1\u0161 telef\xF3n"
    },
    phoneCodeMfa: {
      formTitle: "Overovac\xED k\xF3d",
      resendButton: "Znova odosla\u0165 k\xF3d",
      subtitle: void 0,
      title: "Skontrolujte v\xE1\u0161 telef\xF3n"
    },
    resetPassword: {
      formButtonPrimary: "Obnovi\u0165 heslo",
      requiredMessage: "For security reasons, it is required to reset your password.",
      successMessage: "Va\u0161e heslo bolo \xFAspe\u0161ne zmenen\xE9. Prihlasujem v\xE1s, pros\xEDm po\u010Dkajte okam\u017Eite.",
      title: "Obnovi\u0165 heslo"
    },
    resetPasswordMfa: {
      detailsLabel: "Pred obnoven\xEDm hesla je potrebn\xE9 overi\u0165 va\u0161u toto\u017Enos\u0165."
    },
    start: {
      actionLink: "Registrova\u0165 sa",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "Pou\u017Ei\u0165 email",
      actionLink__use_email_username: "Pou\u017Ei\u0165 email alebo u\u017E\xEDvate\u013Esk\xE9 meno",
      actionLink__use_passkey: void 0,
      actionLink__use_phone: "Pou\u017Ei\u0165 telef\xF3n",
      actionLink__use_username: "Pou\u017Ei\u0165 u\u017E\xEDvate\u013Esk\xE9 meno",
      actionText: "Nem\xE1te \xFA\u010Det?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "pre pokra\u010Dovanie do {{applicationName}}",
      subtitleCombined: void 0,
      title: "Prihl\xE1si\u0165 sa",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Overovac\xED k\xF3d",
      subtitle: void 0,
      title: "Dvojfaktorov\xE9 overenie"
    }
  },
  signInEnterPasswordTitle: "Zadajte svoje heslo",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Prihl\xE1si\u0165 sa",
      actionText: "M\xE1te \xFA\u010Det?",
      subtitle: "pre pokra\u010Dovanie do {{applicationName}}",
      title: "Vypl\u0148te ch\xFDbaj\xFAce polia"
    },
    emailCode: {
      formSubtitle: "Zadajte overovac\xED k\xF3d poslan\xFD na va\u0161u emailov\xFA adresu",
      formTitle: "Overovac\xED k\xF3d",
      resendButton: "Znovu posla\u0165 k\xF3d",
      subtitle: "pre pokra\u010Dovanie do {{applicationName}}",
      title: "Overte svoj email"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      formSubtitle: "Pou\u017Eite overovac\xED odkaz poslan\xFD na va\u0161u emailov\xFA adresu",
      formTitle: "Overovac\xED odkaz",
      loading: {
        title: "Prebieha registr\xE1cia..."
      },
      resendButton: "Znovu posla\u0165 odkaz",
      subtitle: "pre pokra\u010Dovanie do {{applicationName}}",
      title: "Overte svoj email",
      verified: {
        title: "\xDAspe\u0161ne zaregistrovan\xE9"
      },
      verifiedSwitchTab: {
        subtitle: "Vr\xE1\u0165te sa do novootvoren\xE9ho okna pre pokra\u010Dovanie",
        subtitleNewTab: "Vr\xE1\u0165te sa do predch\xE1dzaj\xFAceho okna pre pokra\u010Dovanie",
        title: "Email \xFAspe\u0161ne overen\xFD"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: void 0,
        label__onlyTermsOfService: void 0,
        label__termsOfServiceAndPrivacyPolicy: void 0
      },
      continue: {
        subtitle: void 0,
        title: void 0
      }
    },
    phoneCode: {
      formSubtitle: "Zadajte overovac\xED k\xF3d poslan\xFD na va\u0161e telef\xF3nne \u010D\xEDslo",
      formTitle: "Overovac\xED k\xF3d",
      resendButton: "Znovu posla\u0165 k\xF3d",
      subtitle: "pre pokra\u010Dovanie do {{applicationName}}",
      title: "Overte svoj telef\xF3n"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "Prihl\xE1si\u0165 sa",
      actionLink__use_email: void 0,
      actionLink__use_phone: void 0,
      actionText: "M\xE1te \xFA\u010Det?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "pre pokra\u010Dovanie do {{applicationName}}",
      subtitleCombined: "pre pokra\u010Dovanie do {{applicationName}}",
      title: "Vytvorte si \xFA\u010Det",
      titleCombined: "Vytvorte si \xFA\u010Det"
    }
  },
  socialButtonsBlockButton: "Pokra\u010Dova\u0165 s {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "Sign up unsuccessful due to failed security validations. Please refresh the page to try again or reach out to support for more assistance.",
    captcha_unavailable: "Sign up unsuccessful due to failed bot validation. Please refresh the page to try again or reach out to support for more assistance.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: void 0,
    form_identifier_exists__phone_number: void 0,
    form_identifier_exists__username: void 0,
    form_identifier_not_found: "Nie je mo\u017En\xE9 n\xE1js\u0165 \xFA\u010Det s t\xFDmi ist\xFDmi \xFAdajmi.",
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: "Email address must be a valid email address.",
    form_param_format_invalid__phone_number: "Phone number must be in a valid international format",
    form_param_max_length_exceeded__first_name: "First name should not exceed 256 characters.",
    form_param_max_length_exceeded__last_name: "Last name should not exceed 256 characters.",
    form_param_max_length_exceeded__name: "Name should not exceed 256 characters.",
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: void 0,
    form_password_not_strong_enough: "Va\u0161e heslo nie je dostato\u010Dne siln\xE9.",
    form_password_pwned: "Toto heslo bolo n\xE1jden\xE9 v r\xE1mci \xFAniku d\xE1t a nem\xF4\u017Ee by\u0165 pou\u017Eit\xE9, pros\xEDm zvo\u013Ete in\xE9 heslo.",
    form_password_pwned__sign_in: void 0,
    form_password_size_in_bytes_exceeded: "Va\u0161e heslo prekro\u010Dilo maxim\xE1lny povolen\xFD po\u010Det bytov, pros\xEDm skr\xE1\u0165te ho alebo odstr\xE1\u0148te niektor\xE9 \u0161peci\xE1lne znaky.",
    form_password_validation_failed: "Nespr\xE1vne heslo",
    form_username_invalid_character: void 0,
    form_username_invalid_length: void 0,
    identification_deletion_failed: "You cannot delete your last identification.",
    not_allowed_access: "Adresa e-mailu alebo telef\xF3nneho \u010D\xEDsla nie je povolen\xE1 pre registr\xE1ciu. Toto m\xF4\u017Ee by\u0165 sp\xF4soben\xE9 pou\u017Eit\xEDm '+', '=', '#' alebo '.' v adres\xE1ri e-mailu, pou\u017Eit\xEDm dom\xE9ny, ktor\xE1 je pripojen\xE1 k do\u010Dasnej e-mailovej slu\u017Ebe, alebo explicitn\xE9mu vyl\xFA\u010Deniu.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: void 0,
    passkey_not_supported: void 0,
    passkey_pa_not_supported: void 0,
    passkey_registration_cancelled: void 0,
    passkey_retrieval_cancelled: void 0,
    passwordComplexity: {
      maximumLength: "menej ako {{length}} znakov",
      minimumLength: "{{length}} alebo viac znakov",
      requireLowercase: "mal\xE9 p\xEDsmeno",
      requireNumbers: "\u010D\xEDslicu",
      requireSpecialCharacter: "\u0161peci\xE1lny znak",
      requireUppercase: "ve\u013Ek\xE9 p\xEDsmeno",
      sentencePrefix: "Va\u0161e heslo mus\xED obsahova\u0165"
    },
    phone_number_exists: "This phone number is taken. Please try another.",
    session_exists: "Jste u\u017E p\u0159ihl\xE1\u0161en.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "Va\u0161e heslo funguje, ale mohlo by by\u0165 silnej\u0161ie. Sk\xFAste prida\u0165 viac znakov.",
      goodPassword: "Dobr\xE1 pr\xE1ca. Toto je vynikaj\xFAce heslo.",
      notEnough: "Va\u0161e heslo nen\xED dostato\u010Dne siln\xE9.",
      suggestions: {
        allUppercase: "Pou\u017Eite ve\u013Ek\xE9 p\xEDsmena len u niektor\xFDch, nie v\u0161etk\xFDch p\xEDsmen.",
        anotherWord: "Pridajte viac slov, ktor\xE9 nie s\xFA tak be\u017En\xE9.",
        associatedYears: "Vyhnite sa rokom, ktor\xE9 s\xFA s vami spojen\xE9.",
        capitalization: "P\xEDsmen\xE1 p\xED\u0161te s ve\u013Ek\xFDm po\u010Diato\u010Dn\xFDm p\xEDsmenom a viac ako len prv\xFDm p\xEDsmenom.",
        dates: "Vyhnite sa d\xE1tumom a rokom, ktor\xE9 s\xFA s vami spojen\xE9.",
        l33t: "Vyhnite sa predv\xEDdate\u013En\xFDm n\xE1hrad\xE1m p\xEDsmen, napr\xEDklad '@' miesto 'a'.",
        longerKeyboardPattern: "Pou\u017Eite dlh\u0161ie vzory na kl\xE1vesnici a menite smer p\xEDsania viackr\xE1t.",
        noNeed: "M\xF4\u017Eete vytv\xE1ra\u0165 siln\xE9 hesl\xE1 aj bez pou\u017Eitia symbolov, \u010D\xEDsel alebo ve\u013Ek\xFDch p\xEDsmen.",
        pwned: "Ak pou\u017E\xEDvate toto heslo aj niekde inde, mali by ste ho zmeni\u0165.",
        recentYears: "Vyhnite sa ned\xE1vnym rokom.",
        repeated: "Vyhnite sa opakuj\xFAcim sa slov\xE1m a znakom.",
        reverseWords: "Vyhnite sa obr\xE1ten\xFDm pravopisom be\u017En\xFDch slov.",
        sequences: "Vyhnite sa be\u017En\xFDm sekvenci\xE1m znakov.",
        useWords: "Pou\u017Eite viac slov, ale vyhnite sa be\u017En\xFDm fr\xE1zam."
      },
      warnings: {
        common: "Toto je be\u017Ene pou\u017E\xEDvan\xE9 heslo.",
        commonNames: "Be\u017En\xE9 men\xE1 a priezvisk\xE1 s\xFA \u013Eahko uh\xE1dnute\u013En\xE9.",
        dates: "D\xE1tum je \u013Eahko uh\xE1dnute\u013En\xFD.",
        extendedRepeat: 'Opakuj\xFAce sa vzory znakov ako "abcabcabc" s\xFA \u013Eahko uh\xE1dnute\u013En\xE9.',
        keyPattern: "Kr\xE1tke vzory na kl\xE1vesnici s\xFA \u013Eahko uh\xE1dnute\u013En\xE9.",
        namesByThemselves: "Samostatn\xE9 men\xE1 alebo priezvisk\xE1 s\xFA \u013Eahko uh\xE1dnute\u013En\xE9.",
        pwned: "Va\u0161e heslo bolo odhalen\xE9 pri \xFAniku \xFAdajov na internete.",
        recentYears: "Ned\xE1vne roky s\xFA \u013Eahko uh\xE1dnute\u013En\xE9.",
        sequences: 'Be\u017En\xE9 sekvencie znakov ako "abc" s\xFA \u013Eahko uh\xE1dnute\u013En\xE9.',
        similarToCommon: "Toto je podobn\xE9 be\u017Ene pou\u017E\xEDvan\xE9mu heslu.",
        simpleRepeat: 'Opakuj\xFAce sa znaky ako "aaa" s\xFA \u013Eahko uh\xE1dnute\u013En\xE9.',
        straightRow: "Rady kl\xE1vesnice s\xFA \u013Eahko uh\xE1dnute\u013En\xE9.",
        topHundred: "Toto je \u010Dasto pou\u017E\xEDvan\xE9 heslo.",
        topTen: "Toto je \u010Dasto pou\u017E\xEDvan\xE9 heslo.",
        userInputs: "Heslo by nemalo obsahova\u0165 osobn\xE9 alebo str\xE1nkou s\xFAvisiace \xFAdaje.",
        wordByItself: "Samostatn\xE9 slov\xE1 s\xFA \u013Eahko uh\xE1dnute\u013En\xE9."
      }
    }
  },
  userButton: {
    action__addAccount: "Prida\u0165 \xFA\u010Det",
    action__manageAccount: "Spravova\u0165 \xFA\u010Det",
    action__signOut: "Odhl\xE1si\u0165 sa",
    action__signOutAll: "Odhl\xE1si\u0165 sa zo v\u0161etk\xFDch \xFA\u010Dtov"
  },
  userProfile: {
    apiKeysPage: {
      title: void 0
    },
    backupCodePage: {
      actionLabel__copied: "Skop\xEDrovan\xE9!",
      actionLabel__copy: "Kop\xEDrova\u0165 v\u0161etko",
      actionLabel__download: "Stiahnu\u0165 .txt",
      actionLabel__print: "Vytla\u010Di\u0165",
      infoText1: "Pre tento \xFA\u010Det bud\xFA povolen\xE9 z\xE1lo\u017En\xE9 k\xF3dy.",
      infoText2: "Z\xE1lo\u017En\xE9 k\xF3dy uchov\xE1vajte tajne a bezpe\u010Dne. M\xF4\u017Eete vygenerova\u0165 nov\xE9 z\xE1lo\u017En\xE9 k\xF3dy, ak m\xE1te podozrenie, \u017Ee boli skompromitovan\xE9.",
      subtitle__codelist: "Uchov\xE1vajte ich bezpe\u010Dne a tajne.",
      successMessage: "Z\xE1lo\u017En\xE9 k\xF3dy s\xFA teraz povolen\xE9. Ak strat\xEDte pr\xEDstup k v\xE1\u0161mu overovaciemu zariadeniu, m\xF4\u017Eete pou\u017Ei\u0165 jeden z t\xFDchto k\xF3dov na prihl\xE1senie do v\xE1\u0161ho \xFA\u010Dtu. Ka\u017Ed\xFD k\xF3d mo\u017Eno pou\u017Ei\u0165 iba raz.",
      successSubtitle: "Pou\u017Eite jeden z t\xFDchto k\xF3dov na prihl\xE1senie do v\xE1\u0161ho \xFA\u010Dtu, ak strat\xEDte pr\xEDstup k v\xE1\u0161mu overovaciemu zariadeniu.",
      title: "Prida\u0165 overovanie pomocou z\xE1lo\u017En\xFDch k\xF3dov",
      title__codelist: "Z\xE1lo\u017En\xE9 k\xF3dy"
    },
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Vyberte poskytovate\u013Ea pre pripojenie v\xE1\u0161ho \xFA\u010Dtu.",
      formHint__noAccounts: "Nie s\xFA k dispoz\xEDcii \u017Eiadni dostupn\xED extern\xED poskytovatelia \xFA\u010Dtov.",
      removeResource: {
        messageLine1: "{{identifier}} bude odobran\xFD z tohto \xFA\u010Dtu.",
        messageLine2: "Nebudete u\u017E m\xF4c\u0165 pou\u017E\xEDva\u0165 tento pripojen\xFD \xFA\u010Det a ak\xE9ko\u013Evek z\xE1visl\xE9 funkcie prestan\xFA fungova\u0165.",
        successMessage: "{{connectedAccount}} bol odstr\xE1nen\xFD z v\xE1\u0161ho \xFA\u010Dtu.",
        title: "Odstr\xE1ni\u0165 pripojen\xFD \xFA\u010Det"
      },
      socialButtonsBlockButton: "Pripoji\u0165 \xFA\u010Det {{provider|titleize}}",
      successMessage: "Poskytovate\u013E bol pridan\xFD k v\xE1\u0161mu \xFA\u010Dtu.",
      title: "Prida\u0165 pripojen\xFD \xFA\u010Det"
    },
    deletePage: {
      actionDescription: 'Type "Delete account" below to continue.',
      confirm: "Delete account",
      messageLine1: "Are you sure you want to delete your account?",
      messageLine2: "This action is permanent and irreversible.",
      title: "Delete account"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "Na t\xFAto e-mailov\xFA adresu bude odoslan\xFD overovac\xED k\xF3d.",
        formSubtitle: "Zadajte overovac\xED k\xF3d zaslan\xFD na adresu {{identifier}}",
        formTitle: "Overovac\xED k\xF3d",
        resendButton: "Znovu odosla\u0165 k\xF3d",
        successMessage: "E-mailov\xE1 adresa {{identifier}} bola pridan\xE1 k v\xE1\u0161mu \xFA\u010Dtu."
      },
      emailLink: {
        formHint: "Na t\xFAto e-mailov\xFA adresu bude odoslan\xFD overovac\xED odkaz.",
        formSubtitle: "Kliknite na overovac\xED odkaz v e-maile zaslanom na adresu {{identifier}}",
        formTitle: "Overovac\xED odkaz",
        resendButton: "Znovu odosla\u0165 odkaz",
        successMessage: "E-mailov\xE1 adresa {{identifier}} bola pridan\xE1 k v\xE1\u0161mu \xFA\u010Dtu."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} bude odstr\xE1nen\xE1 z tohto \xFA\u010Dtu.",
        messageLine2: "Nebudete sa m\xF4c\u0165 prihl\xE1si\u0165 pomocou tejto e-mailovej adresy.",
        successMessage: "{{emailAddress}} bola odobran\xE1 z v\xE1\u0161ho \xFA\u010Dtu.",
        title: "Odstr\xE1ni\u0165 e-mailov\xFA adresu"
      },
      title: "Prida\u0165 e-mailov\xFA adresu",
      verifyTitle: "Verify email address"
    },
    formButtonPrimary__add: "Add",
    formButtonPrimary__continue: "Pokra\u010Dova\u0165",
    formButtonPrimary__finish: "Dokon\u010Di\u0165",
    formButtonPrimary__remove: "Remove",
    formButtonPrimary__save: "Save",
    formButtonReset: "Zru\u0161i\u0165",
    mfaPage: {
      formHint: "Vyberte sp\xF4sob pridania.",
      title: "Prida\u0165 dvojfaktorov\xE9 overenie"
    },
    mfaPhoneCodePage: {
      backButton: "Use existing number",
      primaryButton__addPhoneNumber: "Prida\u0165 telef\xF3nne \u010D\xEDslo",
      removeResource: {
        messageLine1: "{{identifier}} u\u017E nebude dost\xE1va\u0165 overovacie k\xF3dy pri prihlasovan\xED.",
        messageLine2: "V\xE1\u0161 \xFA\u010Det nemus\xED by\u0165 tak bezpe\u010Dn\xFD. Naozaj chcete pokra\u010Dova\u0165?",
        successMessage: "Dvojfaktorov\xE9 overovanie pomocou SMS k\xF3du bolo odstr\xE1nen\xE9 pre {{mfaPhoneCode}}",
        title: "Odstr\xE1ni\u0165 dvojfaktorov\xE9 overovanie"
      },
      subtitle__availablePhoneNumbers: "Vyberte telef\xF3nne \u010D\xEDslo pre registr\xE1ciu dvojfaktorov\xE9ho overovania pomocou SMS k\xF3du.",
      subtitle__unavailablePhoneNumbers: "Nie s\xFA k dispoz\xEDcii \u017Eiadne dostupn\xE9 telef\xF3nne \u010D\xEDsla pre registr\xE1ciu dvojfaktorov\xE9ho overovania pomocou SMS k\xF3du.",
      successMessage1: "When signing in, you will need to enter a verification code sent to this phone number as an additional step.",
      successMessage2: "Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.",
      successTitle: "SMS code verification enabled",
      title: "Prida\u0165 overovanie pomocou SMS k\xF3du"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Namiesto toho naskenujte QR k\xF3d",
        buttonUnableToScan__nonPrimary: "Nem\xF4\u017Eete naskenova\u0165 QR k\xF3d?",
        infoText__ableToScan: "Nastavte nov\xFA met\xF3du prihl\xE1senia vo va\u0161ej aplik\xE1cii pre overovanie a naskenujte nasleduj\xFAci QR k\xF3d, aby ste ju spojili so svoj\xEDm \xFA\u010Dtom.",
        infoText__unableToScan: "Nastavte nov\xFA met\xF3du prihl\xE1senia vo va\u0161ej aplik\xE1cii pre overovanie a zadajte ni\u017E\u0161ie uveden\xFD k\u013E\xFA\u010D.",
        inputLabel__unableToScan1: "Uistite sa, \u017Ee je povolen\xE9 \u010Dasovo z\xE1visl\xE9 alebo jednor\xE1zov\xE9 heslo a dokon\u010Dite spojenie v\xE1\u0161ho \xFA\u010Dtu.",
        inputLabel__unableToScan2: "Alternat\xEDvne, ak va\u0161a aplik\xE1cia pre overovanie podporuje TOTP URI, m\xF4\u017Eete tie\u017E skop\xEDrova\u0165 cel\xFD URI."
      },
      removeResource: {
        messageLine1: "Pri prihlasovan\xED u\u017E nebud\xFA vy\u017Eadovan\xE9 overovacie k\xF3dy z tejto aplik\xE1cie pre overovanie.",
        messageLine2: "V\xE1\u0161 \xFA\u010Det nemus\xED by\u0165 tak bezpe\u010Dn\xFD. Naozaj chcete pokra\u010Dova\u0165?",
        successMessage: "Dvojfaktorov\xE9 overovanie pomocou aplik\xE1cie pre overovanie bolo odstr\xE1nen\xE9.",
        title: "Odstr\xE1ni\u0165 dvojfaktorov\xE9 overovanie"
      },
      successMessage: "Dvojfaktorov\xE9 overovanie je teraz povolen\xE9. Pri prihl\xE1sen\xED budete musie\u0165 zada\u0165 overovac\xED k\xF3d z tejto aplik\xE1cie pre overovanie ako \u010Fal\u0161\xED krok.",
      title: "Prida\u0165 aplik\xE1ciu pre overovanie",
      verifySubtitle: "Zadajte overovac\xED k\xF3d generovan\xFD va\u0161ou aplik\xE1ciou pre overovanie.",
      verifyTitle: "Overovac\xED k\xF3d"
    },
    mobileButton__menu: "Menu",
    navbar: {
      account: "Profile",
      apiKeys: void 0,
      billing: void 0,
      description: "Manage your account info.",
      security: "Security",
      title: "Account"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: void 0,
        title: void 0
      },
      subtitle__rename: void 0,
      title__rename: void 0
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "It is recommended to sign out of all other devices which may have used your old password.",
      readonly: "Your password can currently not be edited because you can sign in only via the enterprise connection.",
      successMessage__set: "Va\u0161e heslo bolo nastaven\xE9.",
      successMessage__signOutOfOtherSessions: "V\u0161etky ostatn\xE9 zariadenia boli odhl\xE1sen\xE9.",
      successMessage__update: "Va\u0161e heslo bolo aktualizovan\xE9.",
      title__set: "Nastavi\u0165 heslo",
      title__update: "Zmeni\u0165 heslo"
    },
    phoneNumberPage: {
      infoText: "Na toto telef\xF3nne \u010D\xEDslo bude odoslan\xE1 textov\xE1 spr\xE1va obsahuj\xFAca overovac\xED odkaz.",
      removeResource: {
        messageLine1: "{{identifier}} bude odobran\xE9 z tohto \xFA\u010Dtu.",
        messageLine2: "Nebudete sa m\xF4c\u0165 prihl\xE1si\u0165 pomocou tohto telef\xF3nneho \u010D\xEDsla.",
        successMessage: "{{phoneNumber}} bolo odstr\xE1nen\xE9 z v\xE1\u0161ho \xFA\u010Dtu.",
        title: "Odstr\xE1ni\u0165 telef\xF3nne \u010D\xEDslo"
      },
      successMessage: "{{identifier}} bolo pridan\xE9 k v\xE1\u0161mu \xFA\u010Dtu.",
      title: "Prida\u0165 telef\xF3nne \u010D\xEDslo",
      verifySubtitle: "Enter the verification code sent to {{identifier}}",
      verifyTitle: "Verify phone number"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "Nahrajte obr\xE1zok vo form\xE1toch JPG, PNG, GIF alebo WEBP s ve\u013Ekos\u0165ou men\u0161ou ne\u017E 10 MB",
      imageFormDestructiveActionSubtitle: "Odstr\xE1ni\u0165 obr\xE1zok",
      imageFormSubtitle: "Nahra\u0165 obr\xE1zok",
      imageFormTitle: "Profilov\xFD obr\xE1zok",
      readonly: "Your profile information has been provided by the enterprise connection and cannot be edited.",
      successMessage: "V\xE1\u0161 profil bol aktualizovan\xFD.",
      title: "Aktualizova\u0165 profil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Odhl\xE1si\u0165 sa zo zariadenia",
        title: "Akt\xEDvne zariadenia"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Sk\xFAsi\u0165 znovu",
        actionLabel__reauthorize: "Autorizova\u0165 teraz",
        destructiveActionTitle: "Odstr\xE1ni\u0165",
        primaryButton: "Pripoji\u0165 \xFA\u010Det",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues",
        title: "Pripojen\xE9 \xFA\u010Dty"
      },
      dangerSection: {
        deleteAccountButton: "Delete Account",
        title: "Account termination"
      },
      emailAddressesSection: {
        destructiveAction: "Odstr\xE1ni\u0165 emailov\xFA adresu",
        detailsAction__nonPrimary: "Nastavi\u0165 ako hlavn\xFA",
        detailsAction__primary: "Dokon\u010Di\u0165 overenie",
        detailsAction__unverified: "Dokon\u010Di\u0165 overenie",
        primaryButton: "Prida\u0165 emailov\xFA adresu",
        title: "Emailov\xE9 adresy"
      },
      enterpriseAccountsSection: {
        title: "Enterprise accounts"
      },
      headerTitle__account: "\xDA\u010Det",
      headerTitle__security: "Bezpe\u010Dnos\u0165",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Obnovi\u0165 k\xF3dy",
          headerTitle: "Z\xE1lo\u017En\xE9 k\xF3dy",
          subtitle__regenerate: "Z\xEDskajte nov\xFA sadu zabezpe\u010Den\xFDch z\xE1lo\u017En\xFDch k\xF3dov. Predch\xE1dzaj\xFAce z\xE1lo\u017En\xE9 k\xF3dy bud\xFA zmazan\xE9 a nebud\xFA pou\u017Eite\u013En\xE9.",
          title__regenerate: "Obnovi\u0165 z\xE1lo\u017En\xE9 k\xF3dy"
        },
        phoneCode: {
          actionLabel__setDefault: "Nastavi\u0165 ako predvolen\xE9",
          destructiveActionLabel: "Odstr\xE1ni\u0165 telef\xF3nne \u010D\xEDslo"
        },
        primaryButton: "Prida\u0165 dvojfaktorov\xFA autentifik\xE1ciu",
        title: "Dvojfaktorov\xE1 autentifik\xE1cia",
        totp: {
          destructiveActionTitle: "Odstr\xE1ni\u0165",
          headerTitle: "Aplik\xE1cia Authenticator"
        }
      },
      passkeysSection: {
        menuAction__destructive: void 0,
        menuAction__rename: void 0,
        primaryButton: void 0,
        title: void 0
      },
      passwordSection: {
        primaryButton__setPassword: "Nastavi\u0165 heslo",
        primaryButton__updatePassword: "Zmeni\u0165 heslo",
        title: "Heslo"
      },
      phoneNumbersSection: {
        destructiveAction: "Odstr\xE1ni\u0165 telef\xF3nne \u010D\xEDslo",
        detailsAction__nonPrimary: "Nastavi\u0165 ako hlavn\xE9",
        detailsAction__primary: "Dokon\u010Di\u0165 overenie",
        detailsAction__unverified: "Dokon\u010Di\u0165 overenie",
        primaryButton: "Prida\u0165 telef\xF3nne \u010D\xEDslo",
        title: "Telef\xF3nne \u010D\xEDsla"
      },
      profileSection: {
        primaryButton: void 0,
        title: "Profil"
      },
      usernameSection: {
        primaryButton__setUsername: "Nastavi\u0165 u\u017E\xEDvate\u013Esk\xE9 meno",
        primaryButton__updateUsername: "Zmeni\u0165 u\u017E\xEDvate\u013Esk\xE9 meno",
        title: "U\u017E\xEDvate\u013Esk\xE9 meno"
      },
      web3WalletsSection: {
        destructiveAction: "Odstr\xE1ni\u0165 pe\u0148a\u017Eenku",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3 pe\u0148a\u017Eenky",
        title: "Web3 pe\u0148a\u017Eenky"
      }
    },
    usernamePage: {
      successMessage: "Va\u0161e u\u017E\xEDvate\u013Esk\xE9 meno bolo aktualizovan\xE9.",
      title__set: "Aktualizova\u0165 u\u017E\xEDvate\u013Esk\xE9 meno",
      title__update: "Aktualizova\u0165 u\u017E\xEDvate\u013Esk\xE9 meno"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} bude odobran\xE1 z tohto \xFA\u010Dtu.",
        messageLine2: "Nebudete sa u\u017E m\xF4c\u0165 prihl\xE1si\u0165 pomocou tejto web3 pe\u0148a\u017Eenky.",
        successMessage: "{{web3Wallet}} bola odstr\xE1nen\xE1 z v\xE1\u0161ho \xFA\u010Dtu.",
        title: "Odstr\xE1ni\u0165 web3 pe\u0148a\u017Eenku"
      },
      subtitle__availableWallets: "Vyberte web3 pe\u0148a\u017Eenku na pripojenie k v\xE1\u0161mu \xFA\u010Dtu.",
      subtitle__unavailableWallets: "Nie s\xFA k dispoz\xEDcii \u017Eiadne dostupn\xE9 web3 pe\u0148a\u017Eenky.",
      successMessage: "Pe\u0148a\u017Eenka bola pridan\xE1 k v\xE1\u0161mu \xFA\u010Dtu.",
      title: "Prida\u0165 web3 pe\u0148a\u017Eenku",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  skSK
};
//# sourceMappingURL=sk-SK.mjs.map