{"version": 3, "sources": ["../src/fi-FI.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const fiFI: LocalizationResource = {\n  locale: 'fi-FI',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Takaisin',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Oletus',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Toinen jäljitelty laite',\n  badge__primary: 'Ensisijainen',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Vaaditaan toimia',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Tämä laite',\n  badge__unverified: 'Vahvistamaton',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Käyttäjän laite',\n  badge__you: 'Sinä',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Luo organisaatio',\n    invitePage: {\n      formButtonReset: 'Ohita',\n    },\n    title: 'Luo organisaatio',\n  },\n  dates: {\n    lastDay: \"Eilen klo {{ date | timeString('fi-FI') }}\",\n    next6Days: \"{{ date | weekday('fi-FI','long') }} klo {{ date | timeString('fi-FI') }}\",\n    nextDay: \"Huomenna klo {{ date | timeString('fi-FI') }}\",\n    numeric: \"{{ date | numeric('fi-FI') }}\",\n    previous6Days: \"Viime {{ date | weekday('fi-FI','long') }} klo {{ date | timeString('fi-FI') }}\",\n    sameDay: \"Tänään klo {{ date | timeString('fi-FI') }}\",\n  },\n  dividerText: 'tai',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Käytä toista tapaa',\n  footerPageLink__help: 'Apua',\n  footerPageLink__privacy: 'Tietosuoja',\n  footerPageLink__terms: 'Käyttöehdot',\n  formButtonPrimary: 'Jatka',\n  formButtonPrimary__verify: 'Vahvista',\n  formFieldAction__forgotPassword: 'Unohditko salasanasi?',\n  formFieldError__matchingPasswords: 'Salasanat täsmäävät.',\n  formFieldError__notMatchingPasswords: 'Salasanat eivät täsmää.',\n  formFieldError__verificationLinkExpired: 'Vahvistuslinkki on vanhentunut. Pyydä uusi linkki.',\n  formFieldHintText__optional: 'Valinnainen',\n  formFieldHintText__slug:\n    'Slug on luettava tunniste, joka on oltava yksilöllinen. Sitä käytetään usein URL-osoitteissa.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Delete account',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: 'minun-org',\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Ota automaattiset kutsut käyttöön tälle verkkotunnukselle',\n  formFieldLabel__backupCode: 'Varakoodi',\n  formFieldLabel__confirmDeletion: 'Vahvistus',\n  formFieldLabel__confirmPassword: 'Vahvista salasana',\n  formFieldLabel__currentPassword: 'Nykyinen salasana',\n  formFieldLabel__emailAddress: 'Sähköpostiosoite',\n  formFieldLabel__emailAddress_username: 'Sähköpostiosoite tai käyttäjänimi',\n  formFieldLabel__emailAddresses: 'Sähköpostiosoitteet',\n  formFieldLabel__firstName: 'Etunimi',\n  formFieldLabel__lastName: 'Sukunimi',\n  formFieldLabel__newPassword: 'Uusi salasana',\n  formFieldLabel__organizationDomain: 'Verkkotunnus',\n  formFieldLabel__organizationDomainDeletePending: 'Poista odottavat kutsut ja ehdotukset',\n  formFieldLabel__organizationDomainEmailAddress: 'Vahvistussähköpostiosoite',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Syötä sähköpostiosoite tälle verkkotunnukselle saadaksesi koodin ja vahvistaaksesi tämän verkkotunnuksen.',\n  formFieldLabel__organizationName: 'Nimi',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Pääsyavaimen nimi',\n  formFieldLabel__password: 'Salasana',\n  formFieldLabel__phoneNumber: 'Puhelinnumero',\n  formFieldLabel__role: 'Rooli',\n  formFieldLabel__signOutOfOtherSessions: 'Kirjaudu ulos kaikista muista laitteista',\n  formFieldLabel__username: 'Käyttäjänimi',\n  impersonationFab: {\n    action__signOut: 'Kirjaudu ulos',\n    title: 'Kirjautuneena käyttäjänä {{identifier}}',\n  },\n  maintenanceMode: 'Olemme tällä hetkellä huoltotilassa, mutta älä huoli, se ei kestä kauempaa kuin muutama minuutti.',\n  membershipRole__admin: 'Ylläpitäjä',\n  membershipRole__basicMember: 'Jäsen',\n  membershipRole__guestMember: 'Vieras',\n  organizationList: {\n    action__createOrganization: 'Luo organisaatio',\n    action__invitationAccept: 'Liity',\n    action__suggestionsAccept: 'Pyydä liittymistä',\n    createOrganization: 'Luo organisaatio',\n    invitationAcceptedLabel: 'Liittynyt',\n    subtitle: 'jatkaaksesi kohteeseen {{applicationName}}',\n    suggestionsAcceptedLabel: 'Odottaa hyväksyntää',\n    title: 'Valitse tili',\n    titleWithoutPersonal: 'Valitse organisaatio',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Automaattiset kutsut',\n    badge__automaticSuggestion: 'Automaattiset ehdotukset',\n    badge__manualInvitation: 'Ei automaattista liittymistä',\n    badge__unverified: 'Vahvistamaton',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Lisää verkkotunnus vahvistaaksesi. Tämän verkkotunnuksen sähköpostiosoitteilla varustetut käyttäjät voivat liittyä organisaatioon automaattisesti tai pyytää liittymistä.',\n      title: 'Lisää verkkotunnus',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Kutsuja ei voitu lähettää. Seuraaville sähköpostiosoitteille on jo odottavia kutsuja: {{email_addresses}}.',\n      formButtonPrimary__continue: 'Lähetä kutsuja',\n      selectDropdown__role: 'Valitse rooli',\n      subtitle: 'Kirjoita tai liitä yksi tai useampi sähköpostiosoite, erotettuna välilyönneillä tai pilkuilla.',\n      successMessage: 'Kutsut lähetetty onnistuneesti',\n      title: 'Kutsu uusia jäseniä',\n    },\n    membersPage: {\n      action__invite: 'Kutsu',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Poista jäsen',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'Liittynyt',\n        tableHeader__role: 'Rooli',\n        tableHeader__user: 'Käyttäjä',\n      },\n      detailsTitle__emptyRow: 'Ei jäseniä näytettäväksi',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Kutsu käyttäjiä yhdistämällä sähköpostiverkkotunnus organisaatioosi. Kaikki, jotka rekisteröityvät vastaavalla sähköpostiverkkotunnuksella, voivat liittyä organisaatioon milloin tahansa.',\n          headerTitle: 'Automaattiset kutsut',\n          primaryButton: 'Hallitse vahvistettuja verkkotunnuksia',\n        },\n        table__emptyRow: 'Ei kutsuja näytettäväksi',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Peruuta kutsu',\n        tableHeader__invited: 'Kutsuttu',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Käyttäjät, jotka rekisteröityvät vastaavalla sähköpostiverkkotunnuksella, voivat nähdä ehdotuksen liittymisestä organisaatioosi.',\n          headerTitle: 'Automaattiset ehdotukset',\n          primaryButton: 'Hallitse vahvistettuja verkkotunnuksia',\n        },\n        menuAction__approve: 'Hyväksy',\n        menuAction__reject: 'Hylkää',\n        tableHeader__requested: 'Pyydetty pääsy',\n        table__emptyRow: 'Ei pyyntöjä näytettäväksi',\n      },\n      start: {\n        headerTitle__invitations: 'Kutsut',\n        headerTitle__members: 'Jäsenet',\n        headerTitle__requests: 'Pyyntöjä',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Hallitse organisaatiotasi.',\n      general: 'Yleinen',\n      members: 'Jäsenet',\n      title: 'Organisaatio',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Kirjoita \"{{organizationName}}\" jatkaaksesi.',\n          messageLine1: 'Oletko varma, että haluat poistaa tämän organisaation?',\n          messageLine2: 'Tämä toiminto on pysyvä ja peruuttamaton.',\n          successMessage: 'Olet poistanut organisaation.',\n          title: 'Poista organisaatio',\n        },\n        leaveOrganization: {\n          actionDescription: 'Kirjoita \"{{organizationName}}\" jatkaaksesi.',\n          messageLine1:\n            'Oletko varma, että haluat poistua tästä organisaatiosta? Menetät pääsyn tähän organisaatioon ja sen sovelluksiin.',\n          messageLine2: 'Tämä toiminto on pysyvä ja peruuttamaton.',\n          successMessage: 'Olet poistunut organisaatiosta.',\n          title: 'Poistu organisaatiosta',\n        },\n        title: 'Vaara',\n      },\n      domainSection: {\n        menuAction__manage: 'Hallitse',\n        menuAction__remove: 'Poista',\n        menuAction__verify: 'Vahvista',\n        primaryButton: 'Lisää verkkotunnus',\n        subtitle:\n          'Salli käyttäjien liittyä organisaatioon automaattisesti tai pyytää liittymistä vahvistetun sähköpostiverkkotunnuksen perusteella.',\n        title: 'Vahvistetut verkkotunnukset',\n      },\n      successMessage: 'Organisaatiota on päivitetty.',\n      title: 'Päivitä profiili',\n    },\n    removeDomainPage: {\n      messageLine1: 'Sähköpostiverkkotunnus {{domain}} poistetaan.',\n      messageLine2: 'Käyttäjät eivät voi liittyä organisaatioon automaattisesti tämän jälkeen.',\n      successMessage: '{{domain}} on poistettu.',\n      title: 'Poista verkkotunnus',\n    },\n    start: {\n      headerTitle__general: 'Yleinen',\n      headerTitle__members: 'Jäsenet',\n      profileSection: {\n        primaryButton: 'Päivitä profiili',\n        title: 'Organisaation profiili',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Tämän verkkotunnuksen poistaminen vaikuttaa kutsuttuihin käyttäjiin.',\n        removeDomainActionLabel__remove: 'Poista verkkotunnus',\n        removeDomainSubtitle: 'Poista tämä verkkotunnus vahvistetuista verkkotunnuksistasi',\n        removeDomainTitle: 'Poista verkkotunnus',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Käyttäjät kutsutaan automaattisesti liittymään organisaatioon rekisteröityessään ja voivat liittyä milloin tahansa.',\n        automaticInvitationOption__label: 'Automaattiset kutsut',\n        automaticSuggestionOption__description:\n          'Käyttäjät saavat ehdotuksen liittymisestä, mutta heidän on saatava ylläpitäjän hyväksyntä ennen kuin he voivat liittyä organisaatioon.',\n        automaticSuggestionOption__label: 'Automaattiset ehdotukset',\n        calloutInfoLabel: 'Enrollment-tilan muuttaminen vaikuttaa vain uusiin käyttäjiin.',\n        calloutInvitationCountLabel: 'Käyttäjille lähetetyt odottavat kutsut: {{count}}',\n        calloutSuggestionCountLabel: 'Käyttäjille lähetetyt odottavat ehdotukset: {{count}}',\n        manualInvitationOption__description: 'Käyttäjiä voidaan kutsua vain manuaalisesti organisaatioon.',\n        manualInvitationOption__label: 'Ei automaattista liittymistä',\n        subtitle: 'Valitse, miten tämän verkkotunnuksen käyttäjät voivat liittyä organisaatioon.',\n      },\n      start: {\n        headerTitle__danger: 'Vaara',\n        headerTitle__enrollment: 'Liittymisvaihtoehdot',\n      },\n      subtitle: 'Verkkotunnus {{domain}} on nyt vahvistettu. Jatka valitsemalla liittymistila.',\n      title: 'Päivitä {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Syötä sähköpostiosoitteeseesi lähetetty vahvistuskoodi',\n      formTitle: 'Vahvistuskoodi',\n      resendButton: 'Etkö saanut koodia? Lähetä uudelleen',\n      subtitle: 'Verkkotunnus {{domainName}} on vahvistettava sähköpostitse.',\n      subtitleVerificationCodeScreen:\n        'Vahvistuskoodi lähetettiin osoitteeseen {{emailAddress}}. Syötä koodi jatkaaksesi.',\n      title: 'Vahvista verkkotunnus',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Luo organisaatio',\n    action__invitationAccept: 'Liity',\n    action__manageOrganization: 'Hallitse',\n    action__suggestionsAccept: 'Pyydä liittymistä',\n    notSelected: 'Ei valittua organisaatiota',\n    personalWorkspace: 'Henkilökohtainen tili',\n    suggestionsAcceptedLabel: 'Odottaa hyväksyntää',\n  },\n  paginationButton__next: 'Seuraava',\n  paginationButton__previous: 'Edellinen',\n  paginationRowText__displaying: 'Näytetään',\n  paginationRowText__of: 'yhteensä',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Lisää tili',\n      action__signOutAll: 'Kirjaudu ulos kaikista tileistä',\n      subtitle: 'Valitse tili, jolla haluat jatkaa.',\n      title: 'Valitse tili',\n    },\n    alternativeMethods: {\n      actionLink: 'Hanki apua',\n      actionText: 'Eikö sinulla ole näitä?',\n      blockButton__backupCode: 'Käytä varakoodia',\n      blockButton__emailCode: 'Lähetä koodi sähköpostitse {{identifier}}',\n      blockButton__emailLink: 'Lähetä linkki sähköpostitse {{identifier}}',\n      blockButton__passkey: 'Kirjaudu sisään pääsyavaimellasi',\n      blockButton__password: 'Kirjaudu sisään salasanallasi',\n      blockButton__phoneCode: 'Lähetä SMS-koodi osoitteeseen {{identifier}}',\n      blockButton__totp: 'Käytä todennussovellustasi',\n      getHelp: {\n        blockButton__emailSupport: 'Sähköpostituki',\n        content:\n          'Jos sinulla on vaikeuksia kirjautua tilillesi, lähetä meille sähköpostia, niin autamme sinua palauttamaan pääsyn tiliisi mahdollisimman pian.',\n        title: 'Hanki apua',\n      },\n      subtitle: 'Ongelmia? Voit kirjautua sisään millä tahansa näistä tavoista.',\n      title: 'Käytä toista tapaa',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'Varakoodi on se, jonka sait asettaessasi kaksivaiheisen todennuksen.',\n      title: 'Syötä varakoodi',\n    },\n    emailCode: {\n      formTitle: 'Vahvistuskoodi',\n      resendButton: 'Etkö saanut koodia? Lähetä uudelleen',\n      subtitle: 'jatkaaksesi kohteeseen {{applicationName}}',\n      title: 'Tarkista sähköpostisi',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Jatkaaksesi avaa vahvistuslinkki laitteella ja selaimella, josta aloitit kirjautumisen',\n        title: 'Vahvistuslinkki on virheellinen tälle laitteelle',\n      },\n      expired: {\n        subtitle: 'Palaa alkuperäiseen välilehteen jatkaaksesi.',\n        title: 'Tämä vahvistuslinkki on vanhentunut',\n      },\n      failed: {\n        subtitle: 'Palaa alkuperäiseen välilehteen jatkaaksesi.',\n        title: 'Tämä vahvistuslinkki on virheellinen',\n      },\n      formSubtitle: 'Käytä sähköpostiisi lähetettyä vahvistuslinkkiä',\n      formTitle: 'Vahvistuslinkki',\n      loading: {\n        subtitle: 'Sinut ohjataan pian',\n        title: 'Kirjaudutaan sisään...',\n      },\n      resendButton: 'Et saanut linkkiä? Lähetä uudelleen',\n      subtitle: 'jatkaaksesi kohteeseen {{applicationName}}',\n      title: 'Tarkista sähköpostisi',\n      unusedTab: {\n        title: 'Voit sulkea tämän välilehden',\n      },\n      verified: {\n        subtitle: 'Sinut ohjataan pian',\n        title: 'Kirjautuminen onnistui',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Palaa alkuperäiseen välilehteen jatkaaksesi',\n        subtitleNewTab: 'Palaa uuteen välilehteen jatkaaksesi',\n        titleNewTab: 'Kirjautunut toiseen välilehteen',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Nollaa salasana',\n      resendButton: 'Etkö saanut koodia? Lähetä uudelleen',\n      subtitle: 'nollataksesi salasanasi',\n      subtitle_email: 'Syötä ensin sähköpostiisi lähetetty koodi',\n      subtitle_phone: 'Syötä ensin puhelimeesi lähetetty koodi',\n      title: 'Nollaa salasana',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Nollaa salasanasi',\n      label__alternativeMethods: 'tai kirjaudu sisään toisella tavalla',\n      title: 'Unohditko salasanasi?',\n    },\n    noAvailableMethods: {\n      message: 'Kirjautuminen ei onnistu. Käytettävissä ei ole yhtään todennusmenetelmää.',\n      subtitle: 'Tapahtui virhe',\n      title: 'Ei voi kirjautua',\n    },\n    passkey: {\n      subtitle:\n        'Käyttämällä pääsyavaintasi vahvistat, että olet se joka väität olevasi. Laite voi pyytää sormenjälkeäsi, kasvojasi tai näytön lukitusta.',\n      title: 'Käytä pääsyavaintasi',\n    },\n    password: {\n      actionLink: 'Käytä toista tapaa',\n      subtitle: 'Syötä tilisi salasana',\n      title: 'Syötä salasanasi',\n    },\n    passwordPwned: {\n      title: 'Salasana kompromisoitu',\n    },\n    phoneCode: {\n      formTitle: 'Vahvistuskoodi',\n      resendButton: 'Etkö saanut koodia? Lähetä uudelleen',\n      subtitle: 'jatkaaksesi kohteeseen {{applicationName}}',\n      title: 'Tarkista puhelimesi',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Vahvistuskoodi',\n      resendButton: 'Etkö saanut koodia? Lähetä uudelleen',\n      subtitle: 'Syötä jatkaaksesi puhelimeesi lähetetty vahvistuskoodi',\n      title: 'Tarkista puhelimesi',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Nollaa salasana',\n      requiredMessage: 'Turvallisuussyistä on tarpeen nollata salasanasi.',\n      successMessage: 'Salasanasi on vaihdettu onnistuneesti. Kirjaudutaan sisään, odota hetki.',\n      title: 'Aseta uusi salasana',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Ennen salasanan nollaamista on varmistettava henkilöllisyytesi.',\n    },\n    start: {\n      actionLink: 'Rekisteröidy',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Käytä sähköpostia',\n      actionLink__use_email_username: 'Käytä sähköpostia tai käyttäjänimeä',\n      actionLink__use_passkey: 'Käytä pääsyavainta',\n      actionLink__use_phone: 'Käytä puhelinta',\n      actionLink__use_username: 'Käytä käyttäjänimeä',\n      actionText: 'Eikö sinulla ole tiliä?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'jatkaaksesi kohteeseen {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Kirjaudu sisään',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Todennuskoodi',\n      subtitle: 'Syötä todennuskoodi autentikointisovelluksestasi',\n      title: 'Kaksivaiheinen todennus',\n    },\n  },\n  signInEnterPasswordTitle: 'Syötä salasanasi',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Kirjaudu sisään',\n      actionText: 'Onko sinulla jo tili?',\n      subtitle: 'Täytä loput tiedot jatkaaksesi.',\n      title: 'Täytä puuttuvat kentät.',\n    },\n    emailCode: {\n      formSubtitle: 'Syötä sähköpostiisi lähetetty koodi',\n      formTitle: 'Vahvistuskoodi',\n      resendButton: 'Etkö saanut koodia? Lähetä uudelleen',\n      subtitle: 'Syötä sähköpostiisi lähetetty koodi jatkaaksesi.',\n      title: 'Tarkista sähköpostisi',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Jatkaaksesi avaa vahvistuslinkki laitteella ja selaimella, josta aloitit rekisteröitymisen',\n        title: 'Vahvistuslinkki on virheellinen tälle laitteelle',\n      },\n      formSubtitle: 'Käytä sähköpostiisi lähetettyä vahvistuslinkkiä',\n      formTitle: 'Vahvistuslinkki',\n      loading: {\n        title: 'Rekisteröidytään...',\n      },\n      resendButton: 'Etkö saanut linkkiä? Lähetä uudelleen',\n      subtitle: 'jatkaaksesi kohteeseen {{applicationName}}',\n      title: 'Vahvista sähköpostisi',\n      verified: {\n        title: 'Rekisteröityminen onnistui',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Palaa alkuperäiseen välilehteen jatkaaksesi',\n        subtitleNewTab: 'Palaa uuteen välilehteen jatkaaksesi',\n        title: 'Rekisteröitynyt toiseen välilehteen',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Hyväksyn {{ privacyPolicyLink || link(\"tietosuojaselosteen\") }}',\n        label__onlyTermsOfService: 'Hyväksyn {{ termsOfServiceLink || link(\"käyttöehdot\") }}',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Hyväksyn {{ termsOfServiceLink || link(\"käyttöehdot\") }} ja {{ privacyPolicyLink || link(\"tietosuojaselosteen\") }}',\n      },\n      continue: {\n        subtitle: 'Lue ja hyväksy ehdot jatkaaksesi',\n        title: 'Käyttöehdot ja tietosuojaseloste',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Syötä puhelimeesi lähetetty koodi',\n      formTitle: 'Vahvistuskoodi',\n      resendButton: 'Etkö saanut koodia? Lähetä uudelleen',\n      subtitle: 'Syötä puhelimeesi lähetetty koodi jatkaaksesi.',\n      title: 'Tarkista puhelimesi',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Kirjaudu sisään',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: 'Onko sinulla jo tili?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Tervetuloa! Luo tili jatkaaksesi.',\n      subtitleCombined: 'Tervetuloa! Luo tili jatkaaksesi.',\n      title: 'Luo tili',\n      titleCombined: 'Luo tili',\n    },\n  },\n  socialButtonsBlockButton: 'Jatka palvelun {{provider|titleize}} avulla',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'Rekisteröityminen epäonnistui epäonnistuneiden tietoturvatarkistusten vuoksi. Päivitä sivu ja yritä uudelleen tai ota yhteyttä tukeen.',\n    captcha_unavailable:\n      'Rekisteröityminen epäonnistui, koska botin vahvistus epäonnistui. Päivitä sivu ja yritä uudelleen tai ota yhteyttä tukeen.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: 'Tämä sähköpostiosoite on jo käytössä. Kokeile toista.',\n    form_identifier_exists__phone_number: 'Tämä puhelinnumero on jo käytössä. Kokeile toista.',\n    form_identifier_exists__username: 'Tämä käyttäjänimi on jo käytössä. Kokeile toista.',\n    form_identifier_not_found: 'Ei voi löytää tiliä näillä tiedoilla.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'Sähköpostiosoiteen tulee olla kelvollinen.',\n    form_param_format_invalid__phone_number: 'Puhelinnumeron on oltava kelvollisessa kansainvälisessä muodossa',\n    form_param_max_length_exceeded__first_name: 'Etunimi saa olla enintään 256 merkkiä pitkä.',\n    form_param_max_length_exceeded__last_name: 'Sukunimi saa olla enintään 256 merkkiä pitkä.',\n    form_param_max_length_exceeded__name: 'Nimi saa olla enintään 256 merkkiä pitkä.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: 'Salasana ei ole riittävän vahva.',\n    form_password_pwned: 'Salasana on ollut osallisena tietovuodossa. Valitse toinen salasana.',\n    form_password_pwned__sign_in: 'Salasana on ollut osallisena tietovuodossa. Vaihdathan salasanasi.',\n    form_password_size_in_bytes_exceeded:\n      'Salasanasi on ylittänyt sallitun tavumäärän, lyhennä sitä tai poista joitain erikoismerkkejä.',\n    form_password_validation_failed: 'Väärä salasana.',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: 'Et voi poistaa viimeistä henkilöllisyyttäsi.',\n    not_allowed_access:\n      \"Sähköpostiosoite tai puhelinnumero ei ole sallittu rekisteröityäksesi. Tämä voi johtua siitä, että sähköpostiosoite sisältää '+', '=', '#' tai '.' merkkejä, käyttäät aluetta, joka on sidottu tilapäisyyden sähköpostitilaukseen, tai olet eksplisiittisesti estetty. Jos uskoo, että tämä on virhe, ota yhteyttä tukeen.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: 'Pääsyavain on jo rekisteröity tähän laitteeseen.',\n    passkey_not_supported: 'Pääsyavain ei ole tuettu tällä laitteella.',\n    passkey_pa_not_supported: 'Rekisteröinti vaatii alustan autentikaattorin, mutta laite ei tue sitä.',\n    passkey_registration_cancelled: 'Pääsyavaimen lisääminen peruutettiin tai aikakatkaistiin.',\n    passkey_retrieval_cancelled: 'Pääsyavaimella kirjautuminen peruutettiin tai aikakatkaistiin.',\n    passwordComplexity: {\n      maximumLength: 'enintään {{length}} merkkiä',\n      minimumLength: 'vähintään {{length}} merkkiä',\n      requireLowercase: 'pieni kirjain',\n      requireNumbers: 'numero',\n      requireSpecialCharacter: 'erikoismerkki',\n      requireUppercase: 'iso kirjain',\n      sentencePrefix: 'Salasanan on sisällettävä',\n    },\n    phone_number_exists: 'Tämä puhelinnumero on jo käytössä. Kokeile toista.',\n    session_exists: 'Olet jo kirjautunut sisään.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Salasanasi toimii, mutta se voisi olla vahvempi. Kokeile lisätä erikoismerkkejä tai numeroita.',\n      goodPassword: 'Salasanasi täyttää kaikki tarvittavat vaatimukset.',\n      notEnough: 'Salasanasi ei ole riittävän vahva.',\n      suggestions: {\n        allUppercase: 'Käytä isoja kirjaimia, mutta ei kaikkia kirjaimia.',\n        anotherWord: 'Älä käytä yleisiä sanoja.',\n        associatedYears: 'Älä käytä vuosilukuja, jotka liittyvät sinuun.',\n        capitalization: 'Käytä isoja ja pieniä kirjaimia.',\n        dates: 'Älä käytä päivämääriä.',\n        l33t: 'Älä käytä l33t-kieltä.',\n        longerKeyboardPattern: 'Vältä pitkiä näppäinkuvioita.',\n        noNeed: 'Älä käytä tätä sanaa.',\n        pwned: 'Älä käytä salasanaa, joka on ollut osallisena tietovuodossa.',\n        recentYears: 'Älä käytä viimeaikaisia vuosilukuja.',\n        repeated: 'Älä käytä toistuvia sanoja.',\n        reverseWords: 'Älä käytä sanoja takaperin.',\n        sequences: 'Vältä peräkkäisiä numeroita tai kirjaimia.',\n        useWords: 'Älä käytä yleisiä sanoja.',\n      },\n      warnings: {\n        common: 'Tämä on yleinen salasana.',\n        commonNames: 'Yleiset nimet ja sukunimet ovat helppo arvata.',\n        dates: 'Päivämäärät ovat helppo arvata.',\n        extendedRepeat: 'Toistuvat merkit ovat helppo arvata.',\n        keyPattern: 'Näppäinkuvioita on helppo arvata.',\n        namesByThemselves: 'Nimet ovat helppo arvata.',\n        pwned: 'Tämä salasana on ollut osallisena tietovuodossa.',\n        recentYears: 'Viimeaikaiset vuodet ovat helppo arvata.',\n        sequences: 'Peräkkäiset numerot ja kirjaimet ovat helppo arvata.',\n        similarToCommon: 'Tämä on samanlainen kuin yleinen salasana.',\n        simpleRepeat: 'Toistuvat merkit ovat helppo arvata.',\n        straightRow: 'Peräkkäiset merkit ovat helppo arvata.',\n        topHundred: 'Tämä on yleinen salasana.',\n        topTen: 'Tämä on yleinen salasana.',\n        userInputs: 'Salasana perustuu käyttäjän syötteisiin.',\n        wordByItself: 'Yksittäinen sana on helppo arvata.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Lisää tili',\n    action__manageAccount: 'Hallitse tiliä',\n    action__signOut: 'Kirjaudu ulos',\n    action__signOutAll: 'Kirjaudu ulos kaikista tileistä',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Kopioitu',\n      actionLabel__copy: 'Kopioi',\n      actionLabel__download: 'Lataa .txt',\n      actionLabel__print: 'Tulosta',\n      infoText1: 'Varakoodit otetaan käyttöön tälle tilille.',\n      infoText2:\n        'Pidä varakoodit salassa ja säilytä ne turvallisesti. Voit luoda uudelleen varakoodit, jos epäilet, että ne ovat vaarantuneet.',\n      subtitle__codelist: 'Säilytä varakoodit turvallisessa paikassa ja pidä ne salassa.',\n      successMessage:\n        'Varakoodit ovat nyt käytössä. Voit käyttää jotakin näistä kirjautuaksesi tilillesi, jos menetät käyttöoikeuden todennuslaitteeseesi. Jokaista koodia voi käyttää vain kerran.',\n      successSubtitle:\n        'Voit käyttää jotakin näistä kirjautuaksesi tilillesi, jos menetät käyttöoikeuden todennuslaitteeseesi.',\n      title: 'Lisää varakoodin vahvistus',\n      title__codelist: 'Varakoodit',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Valitse palveluntarjoaja yhdistääksesi tilisi.',\n      formHint__noAccounts: 'Ulkoisia tilintarjoajia ei ole saatavilla.',\n      removeResource: {\n        messageLine1: '{{identifier}} poistetaan tililtäsi.',\n        messageLine2:\n          'Tämä ei poista tiliäsi palveluntarjoajalta, mutta et voi enää käyttää sitä kirjautumiseen tai muihin toimintoihin tämän tilin kautta.',\n        successMessage: '{{connectedAccount}} on poistettu tililtäsi.',\n        title: 'Poista yhdistetty tili',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}',\n      successMessage: 'Palveluntarjoaja on lisätty tilillesi',\n      title: 'Lisää yhdistetty tili',\n    },\n    deletePage: {\n      actionDescription: 'Kirjoita \"Delete account\" poistaaksesi tilisi.',\n      confirm: 'Poista tili',\n      messageLine1: 'Oletko varma, että haluat poistaa tilisi?',\n      messageLine2: 'Tämä toiminto on pysyvä ja peruuttamaton.',\n      title: 'Poista tili',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Vahvistuskoodin sisältävä sähköposti lähetetään tähän sähköpostiosoitteeseen.',\n        formSubtitle: 'Syötä sähköpostiisi {{identifier}} lähetetty koodi',\n        formTitle: 'Vahvistuskoodi',\n        resendButton: 'Etkö saanut koodia? Lähetä uudelleen',\n        successMessage: 'Sähköpostiosoitteesi {{identifier}} on nyt lisätty tilillesi.',\n      },\n      emailLink: {\n        formHint: 'Vahvistuslinkki lähetetään tähän sähköpostiosoitteeseen.',\n        formSubtitle: 'Käytä sähköpostiisi lähetettyä vahvistuslinkkiä',\n        formTitle: 'Vahvistuslinkki',\n        resendButton: 'Et saanut linkkiä? Lähetä uudelleen',\n        successMessage: 'Sähköpostiosoitteesi {{identifier}} on nyt lisätty tilillesi.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} poistetaan tililtäsi.',\n        messageLine2:\n          'Tämä ei poista sähköpostiosoitettasi, mutta et voi enää käyttää sitä kirjautumiseen tai muihin toimintoihin tämän tilin kautta.',\n        successMessage: '{{emailAddress}} on poistettu tililtäsi.',\n        title: 'Poista sähköpostiosoite',\n      },\n      title: 'Lisää sähköpostiosoite',\n      verifyTitle: 'Vahvista sähköpostiosoite',\n    },\n    formButtonPrimary__add: 'Lisää',\n    formButtonPrimary__continue: 'Jatka',\n    formButtonPrimary__finish: 'Valmis',\n    formButtonPrimary__remove: 'Poista',\n    formButtonPrimary__save: 'Tallenna',\n    formButtonReset: 'Peruuta',\n    mfaPage: {\n      formHint: 'Valitse todennusmenetelmä.',\n      title: 'Lisää kaksivaiheinen todennus',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Käytä olemassa olevaa numeroa',\n      primaryButton__addPhoneNumber: 'Lisää puhelinnumero',\n      removeResource: {\n        messageLine1: '{{identifier}} ei enää vastaanota vahvistuskoodeja kirjautuessaan sisään.',\n        messageLine2: 'Tilisi ei ehkä ole yhtä turvallinen. Haluatko varmasti jatkaa?',\n        successMessage: 'SMS-koodin kaksivaiheinen todennus on poistettu {{mfaPhoneCode}}',\n        title: 'Poista kaksivaiheinen todennus',\n      },\n      subtitle__availablePhoneNumbers:\n        'Valitse olemassa oleva puhelinnumero rekisteröityäksesi SMS-koodin kaksivaiheiseen todennukseen tai lisää uusi.',\n      subtitle__unavailablePhoneNumbers:\n        'Ei ole käytettävissä olevia puhelinnumeroita rekisteröityäksesi SMS-koodin kaksivaiheiseen todennukseen, lisää uusi.',\n      successMessage1:\n        'Kirjautuessasi sinun on annettava vahvistuskoodi, joka on lähetetty tähän puhelinnumeroon lisävaiheena.',\n      successMessage2:\n        'Tallenna nämä varakoodit ja säilytä ne jossain turvallisessa paikassa. Jos menetät pääsyn todennuslaitteeseesi, voit käyttää varakoodeja kirjautuaksesi sisään.',\n      successTitle: 'SMS-koodin todennus on otettu käyttöön',\n      title: 'Lisää SMS-koodin todennus',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Skannaa sen sijaan QR-koodi',\n        buttonUnableToScan__nonPrimary: 'Et voi skannata QR-koodia?',\n        infoText__ableToScan:\n          'Aseta uusi kirjautumistapa todennussovellukseesi ja skannaa seuraava QR-koodi linkittääksesi se tilillesi.',\n        infoText__unableToScan: 'Aseta uusi kirjautumistapa todennussovellukseesi ja syötä alla annettu avain.',\n        inputLabel__unableToScan1:\n          'Varmista, että Aikaperusteiset tai Yksittäiset salasanat on käytössä ja viimeistele tilin linkitys.',\n        inputLabel__unableToScan2:\n          'Vaihtoehtoisesti, jos todennussovelluksesi tukee TOTP-URI:ta, voit myös kopioida koko URI:n.',\n      },\n      removeResource: {\n        messageLine1: 'Tämän todennussovelluksen avulla ei enää tarvita vahvistuskoodia kirjautuessasi sisään.',\n        messageLine2: 'Tilisi ei ehkä ole yhtä turvallinen. Haluatko varmasti jatkaa?',\n        successMessage: 'Kaksivaiheinen todennus todennussovelluksen avulla on poistettu.',\n        title: 'Poista kaksivaiheinen todennus',\n      },\n      successMessage:\n        'Kaksivaiheinen todennus on nyt otettu käyttöön. Kirjautuessasi sinun on annettava vahvistuskoodi tästä todennussovelluksesta lisävaiheena.',\n      title: 'Lisää todennussovellus',\n      verifySubtitle: 'Syötä todennuskoodi, jonka todennussovelluksesi on luonut',\n      verifyTitle: 'Vahvistuskoodi',\n    },\n    mobileButton__menu: 'Valikko',\n    navbar: {\n      account: 'Profiili',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Hallitse tilisi tietoja',\n      security: 'Turvallisuus',\n      title: 'Tili',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} poistetaan tililtäsi.',\n        title: 'Poista pääsyavain',\n      },\n      subtitle__rename: 'Voit muuttaa pääsyavaimen nimeä helpottaaksesi sen löytämistä.',\n      title__rename: 'Nimeä pääsyavain uudelleen',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Suositellaan kirjautumista ulos kaikista muista laitteista, jotka saattavat käyttää vanhaa salasanaasi.',\n      readonly: 'Salasanaa ei voi muuttaa, koska kirjautuminen on mahdollista vain yrityksen yhteyden kautta.',\n      successMessage__set: 'Salasanasi on asetettu.',\n      successMessage__signOutOfOtherSessions: 'Kaikki muut laitteet on kirjattu ulos.',\n      successMessage__update: 'Salasanasi on päivitetty.',\n      title__set: 'Aseta salasana',\n      title__update: 'Päivitä salasana',\n    },\n    phoneNumberPage: {\n      infoText:\n        'Vahvistuskoodin sisältävä tekstiviesti lähetetään tähän puhelinnumeroon. Viesti- ja tiedonsiirtomaksuja saatetaan periä.',\n      removeResource: {\n        messageLine1: '{{identifier}} poistetaan tililtäsi.',\n        messageLine2:\n          'Tämä ei poista puhelinnumeroasi, mutta et voi enää käyttää sitä kirjautumiseen tai muihin toimintoihin tämän tilin kautta.',\n        successMessage: '{{phoneNumber}} on poistettu tililtäsi.',\n        title: 'Poista puhelinnumero',\n      },\n      successMessage: 'Puhelinnumerosi {{identifier}} on nyt lisätty tilillesi.',\n      title: 'Lisää puhelinnumero',\n      verifySubtitle: 'Syötä puhelimeesi lähetetty vahvistuskoodi: {{identifier}}',\n      verifyTitle: 'Vahvista puhelinnumero',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Suositeltu koko 1:1, enintään 10 Mt.',\n      imageFormDestructiveActionSubtitle: 'Poista kuva',\n      imageFormSubtitle: 'Lataa kuva',\n      imageFormTitle: 'Profiilikuva',\n      readonly: 'Profiilitietosi on annettu yrityksen yhteyden kautta eikä niitä voi muuttaa.',\n      successMessage: 'Profiilisi on päivitetty.',\n      title: 'Päivitä profiili',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Kirjaudu ulos laitteesta',\n        title: 'Aktiiviset laitteet',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Yritä uudelleen',\n        actionLabel__reauthorize: 'Valtuuta nyt',\n        destructiveActionTitle: 'Poista',\n        primaryButton: 'Yhdistä tili',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'Tarvittavat käyttöoikeudet on päivitetty, ja saatat kokea rajoitettua toiminnallisuutta. Valtuuta tämä sovellus välttääksesi mahdolliset ongelmat.',\n        title: 'Yhdistetyt tilit',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Poista tili',\n        title: 'Poista tili',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Poista sähköpostiosoite',\n        detailsAction__nonPrimary: 'Aseta ensisijaiseksi',\n        detailsAction__primary: 'Viimeistele vahvistus',\n        detailsAction__unverified: 'Vahvista sähköpostiosoite',\n        primaryButton: 'Lisää sähköpostiosoite',\n        title: 'Sähköpostiosoitteet',\n      },\n      enterpriseAccountsSection: {\n        title: 'Yritystilit',\n      },\n      headerTitle__account: 'Tili',\n      headerTitle__security: 'Turvallisuus',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Luo uudet',\n          headerTitle: 'Varakoodit',\n          subtitle__regenerate:\n            'Hanki uusi sarja turvallisia varakoodeja. Aiemmat varakoodit poistetaan eivätkä ne ole enää käytettävissä.',\n          title__regenerate: 'Luo uudet varakoodit',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Aseta oletukseksi',\n          destructiveActionLabel: 'Poista',\n        },\n        primaryButton: 'Lisää kaksivaiheinen todennus',\n        title: 'Kaksivaiheinen todennus',\n        totp: {\n          destructiveActionTitle: 'Poista',\n          headerTitle: 'Todennussovellus',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Poista',\n        menuAction__rename: 'Nimeä uudelleen',\n        primaryButton: undefined,\n        title: 'Pääsyavaimet',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Aseta salasana',\n        primaryButton__updatePassword: 'Päivitä salasana',\n        title: 'Salasana',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Poista puhelinnumero',\n        detailsAction__nonPrimary: 'Aseta ensisijaiseksi',\n        detailsAction__primary: 'Viimeistele vahvistus',\n        detailsAction__unverified: 'Vahvista puhelinnumero',\n        primaryButton: 'Lisää puhelinnumero',\n        title: 'Puhelinnumerot',\n      },\n      profileSection: {\n        primaryButton: 'Päivitä profiili',\n        title: 'Profiili',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Aseta käyttäjänimi',\n        primaryButton__updateUsername: 'Päivitä käyttäjänimi',\n        title: 'Käyttäjänimi',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Poista lompakko',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3-lompakot',\n        title: 'Web3-lompakot',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Käyttäjänimesi on päivitetty.',\n      title__set: 'Aseta käyttäjänimi',\n      title__update: 'Päivitä käyttäjänimi',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} poistetaan tililtäsi.',\n        messageLine2: 'Et voi enää kirjautua sisään tällä web3-lompakolla.',\n        successMessage: '{{web3Wallet}} on poistettu tililtäsi.',\n        title: 'Poista web3-lompakko',\n      },\n      subtitle__availableWallets: 'Valitse web3-lompakko yhdistääksesi tilisi.',\n      subtitle__unavailableWallets: 'Ei ole käytettävissä olevia web3-lompakoita yhdistääksesi tilisi.',\n      successMessage: 'Web3-lompakko on lisätty tilillesi.',\n      title: 'Lisää web3-lompakko',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBACE;AAAA,EACF,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBAAwB;AAAA,QACxB,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UACE;AAAA,MACF,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}