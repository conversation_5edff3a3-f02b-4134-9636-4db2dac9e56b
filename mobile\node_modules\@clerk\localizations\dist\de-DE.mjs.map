{"version": 3, "sources": ["../src/de-DE.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const deDE: LocalizationResource = {\n  locale: 'de-DE',\n  apiKeys: {\n    action__add: 'Neuen API-Key hinzufügen',\n    action__search: 'Suche',\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: 'Keine API-Keys gefunden',\n    formButtonPrimary__add: 'API-Key erstellen',\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: 'Geben Sie einen Namen an, um einen API-Key zu erstellen. Sie können ihn jederzeit widerrufen.',\n    formTitle: 'Neuen API-Key hinzufügen',\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: 'API-Key widerrufen',\n    revokeConfirmation: {\n      confirmationText: 'Widerrufen',\n      formButtonPrimary__revoke: 'API-Key widerrufen',\n      formHint: 'Sind Sie sicher, dass Sie diesen API-Key löschen wollen?',\n      formTitle: 'API-Key \"{{apiKeyName}}\" widerrufen?',\n    },\n  },\n  backButton: 'Zurück',\n  badge__activePlan: 'Aktiv',\n  badge__canceledEndsAt: \"Storniert • Endet am {{ date | shortDate('de-DE') }}\",\n  badge__currentPlan: 'Aktueller Plan',\n  badge__default: 'Standard',\n  badge__endsAt: \"Endet am {{ date | shortDate('de-DE') }}\",\n  badge__expired: 'Abgelaufen',\n  badge__otherImpersonatorDevice: 'Anderes Imitationsgerät',\n  badge__primary: 'Primär',\n  badge__renewsAt: \"Verlängert sich am {{ date | shortDate('de-DE') }}\",\n  badge__requiresAction: 'Handlung erforderlich',\n  badge__startsAt: \"Startet am {{ date | shortDate('de-DE') }}\",\n  badge__thisDevice: 'Dieses Gerät',\n  badge__unverified: 'Unbestätigt',\n  badge__upcomingPlan: 'Bevorstehend',\n  badge__userDevice: 'Benutzergerät',\n  badge__you: 'Du',\n  commerce: {\n    addPaymentMethod: 'Zahlungsmethode hinzufügen',\n    alwaysFree: 'Immer kostenlos',\n    annually: 'Jährlich',\n    availableFeatures: 'Verfügbare Funktionen',\n    billedAnnually: 'Jährlich abgerechnet',\n    billedMonthlyOnly: 'Nur monatlich abgerechnet',\n    cancelSubscription: 'Abonnement kündigen',\n    cancelSubscriptionAccessUntil:\n      \"Sie haben Zugriff auf '{{plan}}' Funktionen bis zum {{ date | longDate('de-DE') }}. Danach haben Sie keinen Zugriff mehr.\",\n    cancelSubscriptionNoCharge: 'Für dieses Abonnement fallen keine Kosten an.',\n    cancelSubscriptionTitle: '{{plan}} Abonnement kündigen?',\n    cannotSubscribeMonthly:\n      'Sie können diesen Plan nicht monatlich abonnieren, da nur eine jährliche Abrechnung verfügbar ist.',\n    checkout: {\n      description__paymentSuccessful: 'Ihre Bezahlung war erfolgreich.',\n      description__subscriptionSuccessful: 'Ihr Abonnement wurde erfolgreich aktiviert.',\n      downgradeNotice:\n        'Sie behalten Ihr aktuelles Abonnement bis zum Ende des Abrechnungszeitraums. So lange können Sie weiterhin alle Funktionen nutzen, danach werden Sie auf dieses Abonnement umgestellt.',\n      emailForm: {\n        subtitle: 'Geben Sie eine E-Mail-Adresse an, um Abrechnungsbelege zu erhalten.',\n        title: 'E-Mail-Adresse hinzufügen',\n      },\n      lineItems: {\n        title__paymentMethod: 'Bezahlmethode',\n        title__statementId: 'Statement-ID',\n        title__subscriptionBegins: 'Abonnement beginnt',\n        title__totalPaid: 'Insgesamt bezahlt',\n      },\n      pastDueNotice: undefined,\n      perMonth: 'pro Monat',\n      title: 'Bezahlung',\n      title__paymentSuccessful: 'Zahlung erfolgreich!',\n      title__subscriptionSuccessful: 'Geschafft!',\n    },\n    credit: undefined,\n    creditRemainder: 'Verbleibendes Guthaben für den restlichen Abrechnungszeitraum.',\n    defaultFreePlanActive: 'Sie nutzen aktuell den kostenlosen Plan.',\n    free: 'Kostenlos',\n    getStarted: 'Jetzt starten',\n    keepSubscription: 'Abonnement behalten',\n    manage: 'Verwalten',\n    manageSubscription: 'Mitgliedschaft verwalten',\n    month: 'Monat',\n    monthly: 'Monatlich',\n    pastDue: undefined,\n    pay: '{{amount}} bezahlen',\n    paymentMethods: 'Zahlungsmethoden',\n    paymentSource: {\n      applePayDescription: {\n        annual: 'Jährlich abgerechnet',\n        monthly: 'Monatlich abgerechnet',\n      },\n      dev: {\n        anyNumbers: 'Alle Zahlen',\n        cardNumber: 'Kartennummer',\n        cvcZip: 'CVC, PLZ',\n        developmentMode: 'Entwicklermodus',\n        expirationDate: 'Ablaufdatum',\n        testCardInfo: 'Test-Kreditkarteninformationen',\n      },\n    },\n    popular: 'Beliebt',\n    pricingTable: {\n      billingCycle: 'Abrechnungszyklus',\n      included: 'Enthalten',\n    },\n    reSubscribe: 'Erneut abonnieren',\n    seeAllFeatures: 'Alle Funktionen anzeigen',\n    subscribe: 'Abonnieren',\n    subtotal: 'Zwischensumme',\n    switchPlan: 'Zu diesem Plan wechseln',\n    switchToAnnual: 'Wechsel zu jährlich',\n    switchToMonthly: 'Wechsel zu monatlich',\n    totalDue: undefined,\n    totalDueToday: 'Heute fällig',\n    viewFeatures: 'Funktionen anzeigen',\n    year: 'Jahr',\n  },\n  createOrganization: {\n    formButtonSubmit: 'Organisation erstellen',\n    invitePage: {\n      formButtonReset: 'Überspringen',\n    },\n    title: 'Organisation erstellen',\n  },\n  dates: {\n    lastDay: \"Gestern um {{ date | timeString('de-DE') }}\",\n    next6Days: \"{{ date | weekday('de-DE','long') }} bei {{ date | timeString('de-DE') }}\",\n    nextDay: \"Morgen um {{ date | timeString('de-DE') }}\",\n    numeric: \"{{ date | numeric('de-DE') }}\",\n    previous6Days: \"Letzte {{ date | weekday('de-DE','long') }} um {{ date | timeString('de-DE') }}\",\n    sameDay: \"Heute um {{ date | timeString('de-DE') }}\",\n  },\n  dividerText: 'oder',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Verwenden Sie eine andere Methode',\n  footerPageLink__help: 'Hilfe',\n  footerPageLink__privacy: 'Privatsphäre',\n  footerPageLink__terms: 'Bedingungen',\n  formButtonPrimary: 'Fortsetzen',\n  formButtonPrimary__verify: 'Verify',\n  formFieldAction__forgotPassword: 'Passwort vergessen?',\n  formFieldError__matchingPasswords: 'Passwörter stimmen überein.',\n  formFieldError__notMatchingPasswords: 'Passwörter stimmen nicht überein.',\n  formFieldError__verificationLinkExpired:\n    'Der Bestätigungslink ist abgelaufen. Bitte fordern Sie einen neuen Link an.',\n  formFieldHintText__optional: 'Optional',\n  formFieldHintText__slug:\n    'Der Slug ist eine für Menschen lesbare ID. Sie muss einzigartig sein und wird oft in URLs verwendet.',\n  formFieldInputPlaceholder__apiKeyDescription: 'Geben Sie eine Beschreibung an',\n  formFieldInputPlaceholder__apiKeyExpirationDate: 'Geben Sie ein Ablaufdatum an',\n  formFieldInputPlaceholder__apiKeyName: 'Geben Sie einen Namen an',\n  formFieldInputPlaceholder__backupCode: 'Sicherheitscode eingeben',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Konto löschen',\n  formFieldInputPlaceholder__emailAddress: 'E-Mail-Adresse eingeben',\n  formFieldInputPlaceholder__emailAddress_username: 'E-Mail-Adresse oder Benutzername eingeben',\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: 'Vorname eingeben',\n  formFieldInputPlaceholder__lastName: 'Nachname eingeben',\n  formFieldInputPlaceholder__organizationDomain: 'Organisations-Domain eingeben',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: 'E-Mail-Adresse der Organisations-Domain eingeben',\n  formFieldInputPlaceholder__organizationName: 'Name der Organisation eingeben',\n  formFieldInputPlaceholder__organizationSlug: 'my-org',\n  formFieldInputPlaceholder__password: 'Passwort eingeben',\n  formFieldInputPlaceholder__phoneNumber: 'Telefonnummer eingeben',\n  formFieldInputPlaceholder__username: 'Benutzername eingeben',\n  formFieldLabel__apiKeyDescription: 'Beschreibung',\n  formFieldLabel__apiKeyExpiration: 'Ablaufdatum',\n  formFieldLabel__apiKeyName: 'Name',\n  formFieldLabel__automaticInvitations: 'Aktivieren Sie automatische Einladungen für diese Domain',\n  formFieldLabel__backupCode: 'Sicherungscode',\n  formFieldLabel__confirmDeletion: 'Bestätigung',\n  formFieldLabel__confirmPassword: 'Passwort bestätigen',\n  formFieldLabel__currentPassword: 'Aktuelles Passwort',\n  formFieldLabel__emailAddress: 'E-Mail-Adresse',\n  formFieldLabel__emailAddress_username: 'E-Mail-Adresse oder Benutzername',\n  formFieldLabel__emailAddresses: 'E-Mail-Adressen',\n  formFieldLabel__firstName: 'Vorname',\n  formFieldLabel__lastName: 'Nachname',\n  formFieldLabel__newPassword: 'Neues Passwort',\n  formFieldLabel__organizationDomain: 'Domain',\n  formFieldLabel__organizationDomainDeletePending: 'Ausstehende Einladungen und Vorschläge löschen',\n  formFieldLabel__organizationDomainEmailAddress: 'E-Mail-Adresse für die Verifizierung',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Geben Sie eine E-Mail-Adresse dieser Domain ein, um einen Code zu erhalten und diese Domain zu verifizieren.',\n  formFieldLabel__organizationName: 'Organisationsname',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Name des Passkeys',\n  formFieldLabel__password: 'Passwort',\n  formFieldLabel__phoneNumber: 'Telefonnummer',\n  formFieldLabel__role: 'Rolle',\n  formFieldLabel__signOutOfOtherSessions: 'Alle anderen Geräte abmelden',\n  formFieldLabel__username: 'Nutzername',\n  impersonationFab: {\n    action__signOut: 'Ausloggen',\n    title: 'Angemeldet als {{identifier}}',\n  },\n  maintenanceMode:\n    'Wir führen derzeit Wartungsarbeiten durch, aber keine Sorge, es sollte nicht länger als ein paar Minuten dauern.',\n  membershipRole__admin: 'Administrator',\n  membershipRole__basicMember: 'Mitglied',\n  membershipRole__guestMember: 'Gast',\n  organizationList: {\n    action__createOrganization: 'Organisation erstellen',\n    action__invitationAccept: 'Beitreten',\n    action__suggestionsAccept: 'Beitritt anfragen',\n    createOrganization: 'Organisation erstellen',\n    invitationAcceptedLabel: 'Beitreten',\n    subtitle: 'um fortzufahren zu {{applicationName}}',\n    suggestionsAcceptedLabel: 'Genehmigung ausstehend',\n    title: 'Konto auswählen',\n    titleWithoutPersonal: 'Organisation auswählen',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: 'API-Keys',\n    },\n    badge__automaticInvitation: 'Automatische Einladungen',\n    badge__automaticSuggestion: 'Automatische Vorschläge',\n    badge__manualInvitation: 'Keine automatische Aufnahme',\n    badge__unverified: 'Nicht verifiziert',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: 'Als Standard festlegen',\n        actionLabel__remove: 'Entfernen',\n        add: 'Neue Zahlungsmethode hinzufügen',\n        addSubtitle: 'Fügen Sie eine neue Zahlungsmethode hinzu.',\n        cancelButton: 'Abbrechen',\n        formButtonPrimary__add: 'Zahlungsmethode hinzufügen',\n        formButtonPrimary__pay: '{{amount}} bezahlen',\n        payWithTestCardButton: 'Mit Test-Kreditkarte bezahlen',\n        removeResource: {\n          messageLine1: '{{identifier}} wird von diesem Konto entfernt.',\n          messageLine2:\n            'In Zukunft können Sie diese Zahlungsmethode nicht mehr verwenden. Alle laufenden Abonnements, die diese Zahlungsmethode verwenden, werden aufhören zu funktionieren.',\n          successMessage: '{{paymentSource}} wurde von diesem Konto entfernt.',\n          title: 'Zahlungsmethode entfernen',\n        },\n        title: 'Zahlungsmethoden',\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: 'Pläne',\n        headerTitle__statements: 'Abrechnungen',\n        headerTitle__subscriptions: 'Abonnements',\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: 'Plan abonnieren',\n        actionLabel__switchPlan: 'Plan wechseln',\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: 'Abonnement',\n      },\n      subscriptionsSection: {\n        actionLabel__default: 'Verwalten',\n      },\n      switchPlansSection: {\n        title: 'Plan wechseln',\n      },\n      title: 'Abrechnung',\n    },\n    createDomainPage: {\n      subtitle:\n        'Fügen Sie die zu überprüfende Domain hinzu. Benutzer mit E-Mail-Adressen von dieser Domain können der Organisation automatisch beitreten oder einen Antrag auf Beitritt stellen.',\n      title: 'Domain hinzufügen',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Die Einladungen konnten nicht versendet werden. Beheben Sie Folgendes und versuchen Sie es erneut:',\n      formButtonPrimary__continue: 'Einladungen verschicken',\n      selectDropdown__role: 'Rolle wählen',\n      subtitle: 'Laden Sie neue Mitglieder zu dieser Organisation ein',\n      successMessage: 'Einladungen erfolgreich versendet',\n      title: 'Mitglieder einladen',\n    },\n    membersPage: {\n      action__invite: 'Einladen',\n      action__search: 'Suchen',\n      activeMembersTab: {\n        menuAction__remove: 'Mitglied entfernen',\n        tableHeader__actions: 'Aktionen',\n        tableHeader__joined: 'Trat bei',\n        tableHeader__role: 'Rolle',\n        tableHeader__user: 'Benutzer',\n      },\n      detailsTitle__emptyRow: 'Keine Mitglieder zum Anzeigen',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Laden Sie Benutzer ein, indem Sie eine E-Mail-Domain mit Ihrer Organisation verbinden. Jeder, der sich mit dieser passenden E-Mail-Domain anmeldet, kann der Organisation jederzeit beitreten.',\n          headerTitle: 'Automatische Einladungen',\n          primaryButton: 'Verwalten Sie verifizierte Domains',\n        },\n        table__emptyRow: 'Keine Einladungen verfügbar',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Einladung widerrufen',\n        tableHeader__invited: 'Eingeladen',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Benutzer, die sich mit einer passenden E-Mail-Domain anmelden, können einen Vorschlag für eine Beitrittsanfrage zu Ihrer Organisation sehen.',\n          headerTitle: 'Automatische Vorschläge',\n          primaryButton: 'Verifizierte Domains verwalten',\n        },\n        menuAction__approve: 'Bestätigen',\n        menuAction__reject: 'Ablehnen',\n        tableHeader__requested: 'Angefragte Zugänge',\n        table__emptyRow: 'Keine Anfragen verfügbar',\n      },\n      start: {\n        headerTitle__invitations: 'Einladungen',\n        headerTitle__members: 'Mitglieder',\n        headerTitle__requests: 'Anfragen',\n      },\n    },\n    navbar: {\n      apiKeys: 'API-Keys',\n      billing: 'Abrechnung',\n      description: 'Verwalten Sie ihre Organisation.',\n      general: 'Allgemein',\n      members: 'Mitglieder',\n      title: 'Organisation',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling:\n          'Sie haben keine Berechtigung, die Abrechnungen für diese Organisation zu verwalten.',\n      },\n      title: 'Pläne',\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Geben Sie \"{{organizationName}}\" unten ein, um fortzufahren.',\n          messageLine1: 'Sind Sie sicher, dass Sie diese Organisation löschen wollen?',\n          messageLine2: 'Diese Aktion ist dauerhaft und irreversibel.',\n          successMessage: 'Sie haben die Organisation gelöscht.',\n          title: 'Organisation löschen',\n        },\n        leaveOrganization: {\n          actionDescription: 'Geben Sie \"{{organizationName}}\" unten ein, um fortzufahren.',\n          messageLine1:\n            'Möchten Sie diese Organisation wirklich verlassen? Sie verlieren den Zugriff auf diese Organisation und Ihre Anwendungen.',\n          messageLine2: 'Diese Aktion ist dauerhaft und irreversibel.',\n          successMessage: 'Sie haben die Organisation verlassen.',\n          title: 'Organisation verlassen',\n        },\n        title: 'Achtung',\n      },\n      domainSection: {\n        menuAction__manage: 'Verwalten',\n        menuAction__remove: 'Löschen',\n        menuAction__verify: 'Verifizieren',\n        primaryButton: 'Domain hinzufügen',\n        subtitle:\n          'Erlauben Sie Benutzern, der Organisation automatisch beizutreten oder den Beitritt auf der Grundlage einer verifizierten E-Mail-Domain anzufragen.',\n        title: 'Verifizierte Domains',\n      },\n      successMessage: 'Die Organisation wurde aktualisiert.',\n      title: 'Organisationsprofil',\n    },\n    removeDomainPage: {\n      messageLine1: 'Die E-mail-Domain {{domain}} wird entfernt.',\n      messageLine2: 'Benutzer können der Organisation danach nicht mehr automatisch beitreten.',\n      successMessage: '{{domain}} wurde entfernt.',\n      title: 'Domain entfernen',\n    },\n    start: {\n      headerTitle__general: 'Allgemein',\n      headerTitle__members: 'Mitglieder',\n      profileSection: {\n        primaryButton: 'Profil bearbeiten',\n        title: 'Organisationsprofil',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Das Entfernen dieser Domain betrifft die eingeladenen Benutzer.',\n        removeDomainActionLabel__remove: 'Domain entfernen',\n        removeDomainSubtitle: 'Sie können diese Domain von den verifizierten Domains entfernen',\n        removeDomainTitle: 'Domain entfernen',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Benutzer werden bei der Anmeldung automatisch eingeladen, der Organisation beizutreten, und können jederzeit beitreten.',\n        automaticInvitationOption__label: 'Automatische Einladungen',\n        automaticSuggestionOption__description:\n          'Benutzer erhalten einen Vorschlag für eine Beitrittsanfrage, müssen aber von einem Administrator genehmigt werden, bevor sie der Organisation beitreten können.',\n        automaticSuggestionOption__label: 'Automatische Vorschläge',\n        calloutInfoLabel: 'Änderungen des Anmeldemodus wirkt sich nur auf neue Benutzer aus.',\n        calloutInvitationCountLabel: 'Ausstehende Einladungen gesendet an Benutzer: {{count}}',\n        calloutSuggestionCountLabel: 'Ausstehende Vorschläge gesendet an Benutzer: {{count}}',\n        manualInvitationOption__description: 'Benutzer können nur manuell in die Organisation eingeladen werden.',\n        manualInvitationOption__label: 'Keine automatische Aufnahme',\n        subtitle: 'Wählen Sie, wie Benutzer mit dieser Domain der Organisation beitreten können.',\n      },\n      start: {\n        headerTitle__danger: 'Gefahr',\n        headerTitle__enrollment: 'Optionen für die Aufnahme',\n      },\n      subtitle: 'Die Domain {{domain}} ist nun verifiziert. Bitte wählen Sie einen Aufnahmemodus aus.',\n      title: '{{domain}} aktualisieren',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Geben Sie den an Ihre E-Mail-Adresse gesendeten Verifizierungscode ein',\n      formTitle: 'Verifizierungscode',\n      resendButton: 'Sie haben keinen Code erhalten? Erneut senden',\n      subtitle: 'Die Domain {{domainName}} muss per E-mail verifiziert werden.',\n      subtitleVerificationCodeScreen:\n        'Ein Verifizierungscode wurde an {{emailAddress}} gesendet. Geben Sie den Code ein, um fortzufahren.',\n      title: 'Domain verifizieren',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Organisation erstellen',\n    action__invitationAccept: 'Beitreten',\n    action__manageOrganization: 'Organisation verwalten',\n    action__suggestionsAccept: 'Beitritt anfragen',\n    notSelected: 'Keine Organisation ausgewählt',\n    personalWorkspace: 'Persönlicher Arbeitsbereich',\n    suggestionsAcceptedLabel: 'Annahme ausstehend',\n  },\n  paginationButton__next: 'Nächste',\n  paginationButton__previous: 'Vorherige',\n  paginationRowText__displaying: 'Anzeigen',\n  paginationRowText__of: 'von',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Klicken Sie hier, um eine alternative Methode zu verwenden',\n      actionText: 'Verwenden Sie eine alternative Verifizierungsmethode',\n      blockButton__backupCode: 'Mit Backup-Code verifizieren',\n      blockButton__emailCode: 'Mit E-Mail-Code verifizieren',\n      blockButton__passkey: 'Verwenden Sie Ihren Passkey',\n      blockButton__password: 'Mit Passwort verifizieren',\n      blockButton__phoneCode: 'Mit SMS-Code verifizieren',\n      blockButton__totp: 'Mit TOTP verifizieren',\n      getHelp: {\n        blockButton__emailSupport: 'E-Mail-Support kontaktieren',\n        content: 'Wenn Sie Hilfe benötigen, wenden Sie sich bitte an unseren Support.',\n        title: 'Hilfe erhalten',\n      },\n      subtitle: 'Wählen Sie eine Methode, um sich zu verifizieren',\n      title: 'Verifizierung erforderlich',\n    },\n    backupCodeMfa: {\n      subtitle: 'Verwenden Sie den Backup-Code, der Ihnen bei der Registrierung zur Verfügung gestellt wurde.',\n      title: 'Backup-Code Verifizierung',\n    },\n    emailCode: {\n      formTitle: 'Geben Sie den Code ein, den wir an Ihre E-Mail-Adresse gesendet haben.',\n      resendButton: 'Code erneut senden',\n      subtitle: 'Überprüfen Sie Ihre E-Mail auf den Verifizierungscode.',\n      title: 'E-Mail-Code Verifizierung',\n    },\n    noAvailableMethods: {\n      message: 'Es sind keine Verifizierungsmethoden mehr verfügbar.',\n      subtitle: 'Bitte kontaktieren Sie den Support, um Hilfe zu erhalten.',\n      title: 'Keine verfügbaren Methoden',\n    },\n    passkey: {\n      blockButton__passkey: 'Verwenden Sie Ihren Passkey',\n      subtitle:\n        'Die Verwendung Ihres Passkeys bestätigt Ihre Identität. Ihr Gerät kann nach Ihrem Fingerabdruck, Gesicht oder Bildschirmsperre fragen.',\n      title: 'Verwenden Sie Ihren Passkey',\n    },\n    password: {\n      actionLink: 'Passwort zurücksetzen',\n      subtitle: 'Geben Sie Ihr Passwort ein, um fortzufahren.',\n      title: 'Passwort-Verifizierung',\n    },\n    phoneCode: {\n      formTitle: 'Geben Sie den Code ein, den wir an Ihre Telefonnummer gesendet haben.',\n      resendButton: 'Code erneut senden',\n      subtitle: 'Überprüfen Sie Ihre SMS-Nachricht auf den Verifizierungscode.',\n      title: 'SMS-Code Verifizierung',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Geben Sie den Code ein, den wir Ihnen per SMS gesendet haben.',\n      resendButton: 'Code erneut senden',\n      subtitle: 'Überprüfen Sie Ihre SMS auf den Verifizierungscode.',\n      title: 'SMS-Code (MFA) Verifizierung',\n    },\n    totpMfa: {\n      formTitle: 'Geben Sie den Code aus Ihrer Authentifikator-App ein.',\n      subtitle: 'Verwenden Sie die Authentifikator-App, die Sie eingerichtet haben.',\n      title: 'TOTP-Verifizierung (MFA)',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Konto hinzufügen',\n      action__signOutAll: 'Von allen Konten abmelden',\n      subtitle: 'Wählen Sie das Konto, mit dem Sie fortfahren möchten.',\n      title: 'Wählen Sie ein Konto',\n    },\n    alternativeMethods: {\n      actionLink: 'Hilfe',\n      actionText: 'Haben Sie keine davon?',\n      blockButton__backupCode: 'Verwenden Sie einen Backup-Code',\n      blockButton__emailCode: 'Code an {{identifier}} senden',\n      blockButton__emailLink: 'Link senden an {{identifier}}',\n      blockButton__passkey: 'Melden Sie sich mit Ihrem Passkey an',\n      blockButton__password: 'Melden Sie sich mit Ihrem Passwort an',\n      blockButton__phoneCode: 'Code an {{identifier}} senden',\n      blockButton__totp: 'Verwenden Sie Ihre Authentifizierungs-App',\n      getHelp: {\n        blockButton__emailSupport: 'Unterstützung per E-Mail',\n        content:\n          'Wenn Sie Schwierigkeiten haben, sich mit Ihrem Konto anzumelden, senden Sie uns eine E-Mail und wir werden mit Ihnen zusammenarbeiten, um den Zugriff so schnell wie möglich wiederherzustellen.',\n        title: 'Hilfe',\n      },\n      subtitle: 'Haben Sie Probleme? Sie können eine der folgenden Methoden zur Anmeldung verwenden.',\n      title: 'Verwenden Sie eine andere Methode',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: 'Bestätigungscode',\n      resendButton: 'Bestätigungscode nicht erhalten? Erneut senden',\n      subtitle: 'weiter zu {{applicationName}}',\n      title: 'Überprüfen Sie {{provider}}',\n    },\n    backupCodeMfa: {\n      subtitle: 'weiter zu {{applicationName}}',\n      title: 'Geben Sie einen Backup-Code ein',\n    },\n    emailCode: {\n      formTitle: 'Bestätigungscode',\n      resendButton: 'Code erneut senden',\n      subtitle: 'weiter zu {{applicationName}}',\n      title: 'Überprüfen Sie Ihren Posteingang',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Die Anfrage stammt von einem nicht kompatiblen Client.',\n        title: 'Client-Kompatibilitätsfehler',\n      },\n      expired: {\n        subtitle: 'Kehren Sie zum ursprünglichen Tab zurück, um fortzufahren.',\n        title: 'Dieser Bestätigungslink ist abgelaufen',\n      },\n      failed: {\n        subtitle: 'Kehren Sie zum ursprünglichen Tab zurück, um fortzufahren.',\n        title: 'Dieser Bestätigungslink ist ungültig',\n      },\n      formSubtitle: 'Verwenden Sie den an Ihre E-Mail gesendeten Bestätigungslink',\n      formTitle: 'Bestätigungslink',\n      loading: {\n        subtitle: 'Sie werden in Kürze weitergeleitet',\n        title: 'Einloggen...',\n      },\n      resendButton: 'Link erneut senden',\n      subtitle: 'weiter zu {{applicationName}}',\n      title: 'Überprüfen Sie Ihren Posteingang',\n      unusedTab: {\n        title: 'Sie können diesen Tab schließen',\n      },\n      verified: {\n        subtitle: 'Sie werden in Kürze weitergeleitet',\n        title: 'Erfolgreich angemeldet',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Kehren Sie zum ursprünglichem Tab zurück, um fortzufahren',\n        subtitleNewTab: 'Kehren Sie zum neu geöffneten Tab zurück, um fortzufahren',\n        titleNewTab: 'In einem anderen Tab angemeldet',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Passwort-Code zurücksetzen',\n      resendButton: 'Sie haben keinen Code erhalten? Erneut senden',\n      subtitle: 'um Passwort zurückzusetzen',\n      subtitle_email: 'Geben Sie zunächst den an Ihre E-Mail gesendeten Code ein',\n      subtitle_phone: 'Geben Sie zunächst den auf Ihr Mobiltelefon geschickten Code ein',\n      title: 'Passwort zurücksetzen',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Passwort zurücksetzen',\n      label__alternativeMethods: 'Oder melden Sie sich mit einer anderen Methode an',\n      title: 'Passwort vergessen?',\n    },\n    noAvailableMethods: {\n      message: 'Die Anmeldung kann nicht fortgesetzt werden. Es ist kein Authentifizierungsfaktor verfügbar.',\n      subtitle: 'Ein Fehler ist aufgetreten',\n      title: 'Anmeldung nicht möglich',\n    },\n    passkey: {\n      subtitle:\n        'Die Verwendung Ihres Passkeys bestätigt, dass Sie es sind. Ihr Gerät kann nach Ihrem Fingerabdruck, Ihrem Gesicht oder der Bildschirmsperre fragen.',\n      title: 'Verwenden Sie Ihren Passkey',\n    },\n    password: {\n      actionLink: 'Verwenden Sie eine andere Methode',\n      subtitle: 'weiter zu {{applicationName}}',\n      title: 'Geben Sie Ihr Passwort ein',\n    },\n    passwordPwned: {\n      title: 'Passwort kompromittiert',\n    },\n    phoneCode: {\n      formTitle: 'Bestätigungscode',\n      resendButton: 'Code erneut senden',\n      subtitle: 'weiter zu {{applicationName}}',\n      title: 'Schau auf dein Telefon',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Bestätigungscode',\n      resendButton: 'Code erneut senden',\n      subtitle: 'Um fortzufahren, geben Sie bitte den Bestätigungscode ein, der an Ihre Telefonnummer gesendet wurde',\n      title: 'Schau auf dein Telefon',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Passwort zurücksetzen',\n      requiredMessage:\n        'Es existiert bereits ein Konto mit einer nicht verifizierten E-Mail Adresse. Bitte setzen Sie Ihr Passwort zur Sicherheit zurück.',\n      successMessage: 'Ihr Passwort wurde erfolgreich geändert. Bitte warten Sie einen Moment, um Sie anzumelden.',\n      title: 'Neues Passwort setzen',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Bevor wir Ihr Passwort zurücksetzen können, müssen wir Ihre Identität überprüfen.',\n    },\n    start: {\n      actionLink: 'Anmelden',\n      actionLink__join_waitlist: 'Warteliste beitreten',\n      actionLink__use_email: 'E-mail nutzen',\n      actionLink__use_email_username: 'E-mail oder Benutzernamen nutzen',\n      actionLink__use_passkey: 'Passkey nutzen',\n      actionLink__use_phone: 'Mobiltelefon nutzen',\n      actionLink__use_username: 'Benutzername nutzen',\n      actionText: 'Kein Account?',\n      actionText__join_waitlist: 'Warteliste beitreten',\n      alternativePhoneCodeProvider: {\n        actionLink: 'Andere Methode verwenden',\n        label: '{{provider}} Telefonnummer',\n        subtitle: 'Geben Sie Ihre Telefonnummer ein, um einen Bestätigungscode per {{provider}} zu erhalten.',\n        title: 'In {{applicationName}} mit {{provider}} einloggen',\n      },\n      subtitle: 'weiter zu {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'In {{applicationName}} einloggen',\n      titleCombined: 'Weiter zu {{applicationName}}',\n    },\n    totpMfa: {\n      formTitle: 'Bestätigungscode',\n      subtitle:\n        'Um fortzufahren, geben Sie bitte den Verifizierungscode ein, der von Ihrer Authenticator-App generiert wurde.',\n      title: 'Bestätigung in zwei Schritten',\n    },\n  },\n  signInEnterPasswordTitle: 'Geben Sie Ihr Passwort ein',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: 'Bestätigungscode nicht erhalten? Erneut senden',\n      subtitle: 'Geben Sie den Bestätigungscode ein, der an {{provider}} gesendet wurde',\n      title: '{{provider}} verifizieren',\n    },\n    continue: {\n      actionLink: 'Einloggen',\n      actionText: 'Haben Sie ein Konto?',\n      subtitle: 'weiter zu {{applicationName}}',\n      title: 'Füllen Sie fehlende Felder aus',\n    },\n    emailCode: {\n      formSubtitle: 'Geben Sie den Bestätigungscode ein, der an Ihre E-Mail-Adresse gesendet wurde',\n      formTitle: 'Bestätigungscode',\n      resendButton: 'Code erneut senden',\n      subtitle: 'weiter zu {{applicationName}}',\n      title: 'Bestätigen Sie Ihre E-Mail',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Die Anfrage konnte nicht verarbeitet werden, da der Client nicht kompatibel ist.',\n        title: 'Fehler: Inkompatibler Client',\n      },\n      formSubtitle: 'Verwenden Sie den an Ihre E-Mail-Adresse gesendeten Bestätigungslink',\n      formTitle: 'Bestätigungslink',\n      loading: {\n        title: 'Anmeldung...',\n      },\n      resendButton: 'Link erneut senden',\n      subtitle: 'weiter zu {{applicationName}}',\n      title: 'Bestätigen Sie Ihre E-Mail',\n      verified: {\n        title: 'Erfolgreich angemeldet',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Kehren Sie zum neu geöffneten Tab zurück, um fortzufahren',\n        subtitleNewTab: 'Kehren Sie zum vorherigen Tab zurück, um fortzufahren',\n        title: 'E-Mail erfolgreich verifiziert',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Ich stimme der {{ privacyPolicyLink || link(\"Datenschutzerklärung\") }} zu',\n        label__onlyTermsOfService: 'Ich stimme den {{ termsOfServiceLink || link(\"Nutzungsbedingungen\") }} zu',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Ich stimme den {{ termsOfServiceLink || link(\"Nutzungsbedingungen\") }} und der {{ privacyPolicyLink || link(\"Datenschutzerklärung\") }} zu',\n      },\n      continue: {\n        subtitle: 'Bitte lesen und akzeptieren Sie die Bedingungen, um fortzufahren',\n        title: 'Rechtliche Einwilligung',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Geben Sie den Bestätigungscode ein, der an Ihre Telefonnummer gesendet wurde',\n      formTitle: 'Bestätigungscode',\n      resendButton: 'Code erneut senden',\n      subtitle: 'weiter zu {{applicationName}}',\n      title: 'Verifizieren Sie Ihre Telefonnummer',\n    },\n    restrictedAccess: {\n      actionLink: 'Mehr erfahren',\n      actionText: 'Zugang verweigert?',\n      blockButton__emailSupport: 'E-Mail-Support kontaktieren',\n      blockButton__joinWaitlist: 'Warteliste beitreten',\n      subtitle: 'Ihr Zugang ist momentan eingeschränkt.',\n      subtitleWaitlist: 'Treten Sie der Warteliste bei, um Benachrichtigungen zu erhalten.',\n      title: 'Zugang verweigert',\n    },\n    start: {\n      actionLink: 'Einloggen',\n      actionLink__use_email: 'Mit E-Mail einloggen',\n      actionLink__use_phone: 'Mit Telefonnummer einloggen',\n      actionText: 'Haben Sie ein Konto?',\n      alternativePhoneCodeProvider: {\n        actionLink: 'Andere Methode verwenden',\n        label: '{{provider}} Telefonnummer',\n        subtitle: 'Geben Sie Ihre Telefonnummer ein, um einen Bestätigungscode per {{provider}} zu erhalten.',\n        title: 'In {{applicationName}} mit {{provider}} anmelden',\n      },\n      subtitle: 'weiter zu {{applicationName}}',\n      subtitleCombined: 'weiter zu {{applicationName}}',\n      title: 'Erstelle deinen Account',\n      titleCombined: 'Erstelle deinen Account',\n    },\n  },\n  socialButtonsBlockButton: 'Weiter mit {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: 'Sie sind bereits Mitglied in dieser Organisation.',\n    captcha_invalid:\n      'Anmeldung aufgrund fehlgeschlagener Sicherheitsüberprüfung nicht erfolgreich. Bitte versuchen Sie es erneut oder kontaktieren Sie uns für weitere Unterstützung.',\n    captcha_unavailable:\n      'Die Anmeldung ist aufgrund einer fehlgeschlagenen Bot-Validierung fehlgeschlagen. Bitte aktualisieren Sie die Seite, um es erneut zu versuchen, oder wenden Sie sich an den Support, um weitere Unterstützung zu erhalten.',\n    form_code_incorrect: 'Der eingegebene Code ist falsch. Bitte überprüfen Sie ihn und versuchen Sie es erneut.',\n    form_identifier_exists__email_address: 'Diese E-Mail-Adresse ist bereits vergeben. Bitte wählen Sie eine andere.',\n    form_identifier_exists__phone_number: 'Diese Telefonnummer ist bereits vergeben. Bitte wählen Sie eine andere.',\n    form_identifier_exists__username: 'Dieser Benutzername ist bereits vergeben. Bitte wählen Sie einen anderen.',\n    form_identifier_not_found: 'Wir konnten kein Konto mit diesen Details finden.',\n    form_param_format_invalid: 'Das Format des eingegebenen Parameters ist ungültig.',\n    form_param_format_invalid__email_address: 'Bitte geben Sie eine gültige E-Mail-Adresse ein.',\n    form_param_format_invalid__phone_number: 'Die Telefonnummer muss ein gültiges internationales Format haben.',\n    form_param_max_length_exceeded__first_name: 'Der Vorname sollte nicht mehr als 256 Zeichen umfassen.',\n    form_param_max_length_exceeded__last_name: 'Der Nachname sollte nicht mehr als 256 Zeichen umfassen.',\n    form_param_max_length_exceeded__name: 'Der Name sollte nicht länger als 256 Zeichen sein.',\n    form_param_nil: 'Ein erforderliches Feld wurde nicht ausgefüllt. Bitte überprüfen Sie Ihre Eingaben.',\n    form_param_value_invalid: 'Der eingegebene Wert ist ungültig.',\n    form_password_incorrect: 'Das eingegebene Passwort ist falsch.',\n    form_password_length_too_short: 'Das Passwort ist zu kurz. Es muss mindestens 8 Zeichen lang sein.',\n    form_password_not_strong_enough: 'Passwort nicht stark genug.',\n    form_password_pwned:\n      'Das gewählte Passwort wurde bei einem Datenleck im Internet gefunden. Wählen Sie aus Sicherheitsgründen bitte ein anderes Passwort.',\n    form_password_pwned__sign_in:\n      'Dieses Passwort wurde in einem Datenleck gefunden und kann nicht verwendet werden. Bitte setzen Sie Ihr Passwort zurück.',\n    form_password_size_in_bytes_exceeded:\n      'Das Passwort hat die maximale Anzahl an Bytes überschritten. Bitte kürzen oder Sonderzeichen entfernen.',\n    form_password_validation_failed: 'Falsches Passwort.',\n    form_username_invalid_character:\n      'Der Benutzername enthält ungültige Zeichen. Bitte verwenden Sie nur alphanumerische Zeichen und Unterstriche.',\n    form_username_invalid_length: 'Der Benutzername muss zwischen 3 und 30 Zeichen lang sein.',\n    identification_deletion_failed: 'Sie können Ihre letzte Kennung nicht löschen.',\n    not_allowed_access:\n      \"Die E-Mail-Adresse oder Telefonnummer ist für die Anmeldung nicht zulässig. Dies kann daran liegen, dass Ihre E-Mail-Adresse die Zeichen '+', '=', '#' oder '.' enthält, Sie eine Domain verwenden, die mit einem temporären E-Mail-Dienst verknüpft ist, oder dass Sie explizit gesperrt sind. Wenn Sie glauben, dass dies ein Fehler ist, wenden Sie sich bitte an den Support.\",\n    organization_domain_blocked: 'Diese E-Mail-Provider-Domain ist gesperrt. Bitte verwenden Sie eine andere.',\n    organization_domain_common: 'Dies ist eine gängige E-Mail-Provider-Domain. Bitte verwenden Sie eine andere.',\n    organization_domain_exists_for_enterprise_connection:\n      'Diese Domain wird bereits für das SSO Ihrer Organisation verwendet',\n    organization_membership_quota_exceeded:\n      'Sie haben Ihr Limit an Organisationsmitgliedschaften einschließlich ausstehender Einladungen erreicht.',\n    organization_minimum_permissions_needed:\n      'Es muss mindestens ein Organisationsmitglied mit den erforderlichen Mindestberechtigungen geben.',\n    passkey_already_exists: 'Auf diesem Gerät ist bereits ein Passkey registriert.',\n    passkey_not_supported: 'Passkeys werden auf diesem Gerät nicht unterstützt.',\n    passkey_pa_not_supported:\n      'Die Registrierung erfordert einen Plattformauthentifikator, der vom Gerät nicht unterstützt wird.',\n    passkey_registration_cancelled:\n      'Die Passkey-Registrierung wurde abgebrochen oder das Zeitlimit wurde überschritten.',\n    passkey_retrieval_cancelled: 'Die Passkey-Registrierung wurde abgebrochen oder das Zeitlimit wurde überschritten.',\n    passwordComplexity: {\n      maximumLength: 'weniger als {{length}} Zeichen lang sein',\n      minimumLength: 'mindestens {{length}} Zeichen lang sein',\n      requireLowercase: 'einen Kleinbuchstaben enthalten',\n      requireNumbers: 'eine Zahl enthalten',\n      requireSpecialCharacter: 'ein Sonderzeichen enthalten',\n      requireUppercase: 'einen Großbuchstaben enthalten',\n      sentencePrefix: 'Das Passwort muss',\n    },\n    phone_number_exists: 'Diese Telefonnummer ist bereits vergeben. Bitte wählen Sie eine Andere.',\n    session_exists: 'Sie sind bereits angemeldet.',\n    web3_missing_identifier:\n      'Eine Web3 Wallet-Erweiterung wurde nicht gefunden. Bitte installieren Sie eine, um fortzufahren.',\n    zxcvbn: {\n      couldBeStronger: 'Ihr Passwort funktioniert, könnte aber besser sein. Versuchen Sie, mehr Zeichen hinzuzufügen.',\n      goodPassword: 'Ihr Passwort erfüllt alle notwendigen Anforderungen.',\n      notEnough: 'Ihr Passwort ist nicht stark genug.',\n      suggestions: {\n        allUppercase: 'Einige, aber nicht alle Buchstaben groß schreiben.',\n        anotherWord: 'Weitere Wörter, die weniger häufig vorkommen, hinzufügen.',\n        associatedYears: 'Jahre, die mit persönlichen Daten in Verbindung gebracht werden können, vermeiden.',\n        capitalization: 'Nicht nur den ersten Buchstaben groß schreiben.',\n        dates: 'Daten, die mit persönlichen Daten in Verbindung gebracht werden können, vermeiden.',\n        l33t: \"Vorhersehbare Buchstabenersetzungen wie '@' für 'a' vermeiden.\",\n        longerKeyboardPattern: 'Längere Tastaturmuster in unterschiedlicher Tipprichtung verwenden.',\n        noNeed:\n          'Es ist möglich, starke Passwörter zu erstellen, ohne Symbole, Zahlen oder Großbuchstaben zu verwenden.',\n        pwned: 'Wenn Sie dieses Passwort an anderer Stelle verwenden, sollten Sie es ändern.',\n        recentYears: 'Die jüngsten Jahreszahlen vermeiden.',\n        repeated: 'Wort- und Zeichenwiederholungen vermeiden.',\n        reverseWords: 'Umgekehrte Schreibweise von gebräuchlichen Wörtern vermeiden.',\n        sequences: 'Häufige Zeichenfolgen vermeiden.',\n        useWords: 'Mehrere Wörter verwenden, aber allgemeine Phrasen vermeiden.',\n      },\n      warnings: {\n        common: 'Dies ist ein oft verwendetes Passwort.',\n        commonNames: 'Vornamen und Nachnamen sind leicht zu erraten.',\n        dates: 'Ein Datum ist leicht zu erraten.',\n        extendedRepeat: 'Sich wiederholende Zeichenmuster wie \"abcabcabc\" sind leicht zu erraten.',\n        keyPattern: 'Kurze Tastaturmuster sind leicht zu erraten.',\n        namesByThemselves: 'Einzelne Namen oder Nachnamen sind leicht zu erraten.',\n        pwned: 'Ihr Passwort wurde durch eine Datenpanne im Internet offengelegt.',\n        recentYears: 'Die jüngsten Jahreszahlen sind leicht zu erraten.',\n        sequences: 'Häufige Zeichenfolgen wie \"abc\" sind leicht zu erraten.',\n        similarToCommon: 'Dies weist Ähnlichkeit zu anderen oft verwendeten Passwörtern auf.',\n        simpleRepeat: 'Sich wiederholende Zeichen wie \"aaa\" sind leicht zu erraten.',\n        straightRow: 'Gerade Linien von Tasten auf der Tastatur sind leicht zu erraten.',\n        topHundred: 'Dies ist ein häufig verwendetes Passwort.',\n        topTen: 'Dies ist ein sehr häufig verwendetes Passwort.',\n        userInputs: 'Es sollten keine persönlichen oder Seiten relevanten Daten vorkommen.',\n        wordByItself: 'Einzelne Wörter sind leicht zu erraten.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Konto hinzufügen',\n    action__manageAccount: 'Konto verwalten',\n    action__signOut: 'Ausloggen',\n    action__signOutAll: 'Melden Sie sich von allen Konten ab',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: 'API-Keys',\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Kopiert!',\n      actionLabel__copy: 'Kopiere alle',\n      actionLabel__download: 'Laden Sie .txt herunter',\n      actionLabel__print: 'Drucken',\n      infoText1: 'Backup-Codes werden für dieses Konto aktiviert.',\n      infoText2:\n        'Halten Sie die Backup-Codes geheim und bewahren Sie sie sicher auf. Sie können Sicherungscodes neu generieren, wenn Sie vermuten, dass sie kompromittiert wurden.',\n      subtitle__codelist: 'Bewahren Sie die Codes sicher auf und halten Sie sie geheim.',\n      successMessage:\n        'Sicherungscodes sind jetzt aktiviert. Sie können eines davon verwenden, um sich bei Ihrem Konto anzumelden, wenn Sie den Zugriff auf Ihr Authentifizierungsgerät verlieren. Jeder Code kann nur einmal verwendet werden.',\n      successSubtitle:\n        'Sie können diese Codes verwenden, um sich bei Ihrem Konto anzumelden, wenn Sie den Zugriff auf Ihr Authentifizierungsgerät verlieren.',\n      title: 'Backup-Code-Verifizierung hinzufügen',\n      title__codelist: 'Sicherungscodes',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: 'Als Standard festlegen',\n        actionLabel__remove: 'Entfernen',\n        add: 'Neue Zahlungsmethode hinzufügen',\n        addSubtitle: 'Fügen Sie eine neue Zahlungsmethode hinzu.',\n        cancelButton: 'Abbrechen',\n        formButtonPrimary__add: 'Zahlungsmethode hinzufügen',\n        formButtonPrimary__pay: '{{amount}} bezahlen',\n        payWithTestCardButton: 'Mit Test-Kreditkarte bezahlen',\n        removeResource: {\n          messageLine1: '{{identifier}} wird von diesem Konto entfernt.',\n          messageLine2:\n            'In Zukunft können Sie diese Zahlungsmethode nicht mehr verwenden. Alle laufenden Abonnements, die diese Zahlungsmethode verwenden, werden aufhören zu funktionieren.',\n          successMessage: '{{paymentSource}} wurde von diesem Konto entfernt.',\n          title: 'Zahlungsmethode entfernen',\n        },\n        title: 'Zahlungsmethoden',\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: 'Pläne',\n        headerTitle__statements: 'Abrechnungen',\n        headerTitle__subscriptions: 'Abonnements',\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: 'Plan abonnieren',\n        actionLabel__switchPlan: 'Plan wechseln',\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: 'Abonnement',\n      },\n      subscriptionsSection: {\n        actionLabel__default: 'Verwalten',\n      },\n      switchPlansSection: {\n        title: 'Plan wechseln',\n      },\n      title: 'Abrechnung',\n    },\n    connectedAccountPage: {\n      formHint: 'Wählen Sie einen Anbieter aus, um Ihr Konto zu verbinden.',\n      formHint__noAccounts: 'Es sind keine externen Kontoanbieter verfügbar.',\n      removeResource: {\n        messageLine1: '{{identifier}} wird aus diesem Konto entfernt.',\n        messageLine2:\n          'Sie können dieses verbundene Konto nicht mehr verwenden und alle abhängigen Funktionen funktionieren nicht mehr.',\n        successMessage: '{{connectedAccount}} wurde aus Ihrem Konto entfernt.',\n        title: 'Verbundenes Konto entfernen',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}-Konto verbinden',\n      successMessage: 'Der Anbieter wurde Ihrem Konto hinzugefügt',\n      title: 'Verbundenes Konto hinzufügen',\n    },\n    deletePage: {\n      actionDescription: 'Geben Sie \"Konto löschen\" ein, um fortzufahren.',\n      confirm: 'Konto löschen',\n      messageLine1: 'Sind Sie sicher, dass Sie ihr Konto löschen möchten?',\n      messageLine2: 'Diese Aktion ist endgültig und kann nicht rückgängig gemacht werden.',\n      title: 'Konto löschen',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'An diese E-Mail-Adresse wird eine E-Mail mit einem Bestätigungscode gesendet.',\n        formSubtitle: 'Geben Sie den Bestätigungscode ein, der an {{identifier}} gesendet wird',\n        formTitle: 'Verifizierungs-Schlüssel',\n        resendButton: 'Code erneut senden',\n        successMessage: 'Die E-Mail-Adresse {{identifier}} wurde Ihrem Konto hinzugefügt.',\n      },\n      emailLink: {\n        formHint: 'An diese E-Mail-Adresse wird eine E-Mail mit einem Bestätigungslink gesendet.',\n        formSubtitle: 'Klicken Sie auf den Bestätigungslink in der an {{identifier}} gesendeten E-Mail',\n        formTitle: 'Bestätigungslink',\n        resendButton: 'Link erneut senden',\n        successMessage: 'Die E-Mail-Adresse {{identifier}} wurde Ihrem Konto hinzugefügt.',\n      },\n      enterpriseSSOLink: {\n        formButton: 'Klicken Sie zum Anmelden',\n        formSubtitle: 'Schließen Sie die Anmeldung mit {{identifier}} ab',\n      },\n      formHint: 'Sie müssen diese E-Mail-Adresse verifizieren, bevor sie Ihrem Konto hinzugefügt werden kann.',\n      removeResource: {\n        messageLine1: '{{identifier}} wird aus diesem Konto entfernt.',\n        messageLine2: 'Sie können sich nicht mehr mit dieser E-Mail-Adresse anmelden.',\n        successMessage: '{{emailAddress}} wurde aus Ihrem Konto entfernt.',\n        title: 'E-Mail-Adresse entfernen',\n      },\n      title: 'E-Mail-Adresse hinzufügen',\n      verifyTitle: 'E-Mail Adresse verifizieren',\n    },\n    formButtonPrimary__add: 'Add',\n    formButtonPrimary__continue: 'Fortsetzen',\n    formButtonPrimary__finish: 'Fertig',\n    formButtonPrimary__remove: 'Entfernen',\n    formButtonPrimary__save: 'Speichern',\n    formButtonReset: 'Zurücksetzen',\n    mfaPage: {\n      formHint: 'Wählen Sie eine Methode aus.',\n      title: 'Aktivieren Sie Zweifaktor-Authentifizierung',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Vorhandene Nummer verwenden',\n      primaryButton__addPhoneNumber: 'Fügen Sie eine Telefonnummer hinzu',\n      removeResource: {\n        messageLine1: '{{identifier}} erhält bei der Anmeldung keine Bestätigungscodes mehr.',\n        messageLine2: 'Ihr Konto ist möglicherweise nicht so sicher. Bist du dir sicher, dass du weitermachen willst?',\n        successMessage: 'SMS-Code-Bestätigung in zwei Schritten wurde für {{mfaPhoneCode}} entfernt',\n        title: 'Entfernen Sie die Bestätigung in zwei Schritten',\n      },\n      subtitle__availablePhoneNumbers:\n        'Wählen Sie eine Telefonnummer aus, um sich für die Bestätigung in zwei Schritten per SMS-Code zu registrieren.',\n      subtitle__unavailablePhoneNumbers:\n        'Es sind keine Telefonnummern verfügbar, um sich für die SMS-Code-Bestätigung in zwei Schritten zu registrieren.',\n      successMessage1:\n        'When signing in, you will need to enter a verification code sent to this phone number as an additional step.',\n      successMessage2:\n        'Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.',\n      successTitle: 'SMS code verification enabled',\n      title: 'SMS-Code-Bestätigung hinzufügen',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Scannen Sie stattdessen den QR-Code',\n        buttonUnableToScan__nonPrimary: 'QR-Code kann nicht gescannt werden?',\n        infoText__ableToScan:\n          'Richten Sie eine neue Anmeldemethode in Ihrer Authentifizierungs-App ein und scannen Sie den folgenden QR-Code, um ihn mit Ihrem Konto zu verknüpfen.',\n        infoText__unableToScan:\n          'Richten Sie eine neue Anmeldemethode in Ihrem Authentifikator ein und geben Sie den unten angegebenen Schlüssel ein.',\n        inputLabel__unableToScan1:\n          'Stellen Sie sicher, dass zeitbasierte oder einmalige Passwörter aktiviert sind, und schließen Sie dann die Verknüpfung Ihres Kontos ab.',\n        inputLabel__unableToScan2:\n          'Wenn Ihr Authentifikator TOTP-URIs unterstützt, können Sie alternativ auch die vollständige URI kopieren.',\n      },\n      removeResource: {\n        messageLine1: 'Bei der Anmeldung sind keine Bestätigungscodes von diesem Authentifikator mehr erforderlich.',\n        messageLine2:\n          'Ihr Konto ist möglicherweise nicht mehr so sicher. Sind Sie sich sicher, dass Sie fortfahren wollen?',\n        successMessage: 'Die zweistufige Verifizierung über die Authentifizierungs-App wurde entfernt.',\n        title: 'Entfernen Sie die Bestätigung in zwei Schritten',\n      },\n      successMessage:\n        'Die Bestätigung in zwei Schritten ist jetzt aktiviert. Bei der Anmeldung müssen Sie als zusätzlichen Schritt einen Bestätigungscode von diesem Authentifikator eingeben.',\n      title: 'Authentifizierungs-App hinzufügen',\n      verifySubtitle: 'Geben Sie den von Ihrem Authentifikator generierten Bestätigungscode ein',\n      verifyTitle: 'Verifizierungs-Schlüssel',\n    },\n    mobileButton__menu: 'Menü',\n    navbar: {\n      account: 'Profil',\n      apiKeys: 'API-Keys',\n      billing: 'Abrechnung',\n      description: 'Verwalten Sie Ihre Kontoinformationen.',\n      security: 'Sicherheit',\n      title: 'Benutzerkonto',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} wird von diesem Konto entfernt.',\n        title: 'Passkey entfernen',\n      },\n      subtitle__rename: 'Sie können den Namen des Passkeys ändern, um ihn leichter zu finden.',\n      title__rename: 'Passkey umbenennen',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Es wird empfohlen, sich von allen anderen Geräten abzumelden, die möglicherweise Ihr altes Passwort verwendet haben.',\n      readonly:\n        'Ihr Passwort kann derzeit nicht geändert werden, da Sie sich nur über die Enterprise-Verbindung anmelden können.',\n      successMessage__set: 'Ihr Passwort wurde festgelegt.',\n      successMessage__signOutOfOtherSessions: 'Alle anderen Geräte wurden abgemeldet.',\n      successMessage__update: 'Dein Passwort wurde aktualisiert.',\n      title__set: 'Passwort festlegen',\n      title__update: 'Passwort ändern',\n    },\n    phoneNumberPage: {\n      infoText: 'An diese Telefonnummer wird eine SMS mit einem Bestätigungslink gesendet.',\n      removeResource: {\n        messageLine1: '{{identifier}} wird aus diesem Konto entfernt.',\n        messageLine2: 'Sie können sich nicht mehr mit dieser Telefonnummer anmelden.',\n        successMessage: '{{phoneNumber}} wurde aus Ihrem Konto entfernt.',\n        title: 'Telefonnummer entfernen',\n      },\n      successMessage: '{{identifier}} wurde Ihrem Konto hinzugefügt.',\n      title: 'Telefonnummer hinzufügen',\n      verifySubtitle: 'Enter the verification code sent to {{identifier}}',\n      verifyTitle: 'Verify phone number',\n    },\n    plansPage: {\n      title: 'Pläne',\n    },\n    profilePage: {\n      fileDropAreaHint: 'Laden Sie ein JPG-, PNG-, GIF- oder WEBP-Bild hoch, das kleiner als 10 MB ist',\n      imageFormDestructiveActionSubtitle: 'Bild entfernen',\n      imageFormSubtitle: 'Bild hochladen',\n      imageFormTitle: 'Profilbild',\n      readonly: 'Your profile information has been provided by the enterprise connection and cannot be edited.',\n      successMessage: 'Ihr Profil wurde aktualisiert.',\n      title: 'Profil aktualisieren',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Vom Gerät abmelden',\n        title: 'Aktive Geräte',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Versuchen Sie es nochmal',\n        actionLabel__reauthorize: 'Jetzt autorisieren',\n        destructiveActionTitle: 'Entfernen',\n        primaryButton: 'Konto verbinden',\n        subtitle__disconnected: 'Ihr Konto ist derzeit getrennt. Bitte verbinden Sie es erneut.',\n        subtitle__reauthorize:\n          'The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues',\n        title: 'Verbundene Konten',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Konto löschen',\n        title: 'Achtung',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'E-Mail-Adresse entfernen',\n        detailsAction__nonPrimary: 'Als primär festlegen',\n        detailsAction__primary: 'Verifizierung abschließen',\n        detailsAction__unverified: 'Verifizierung abschließen',\n        primaryButton: 'Fügen Sie eine E-Mail-Adresse hinzu',\n        title: 'E-Mail-Adressen',\n      },\n      enterpriseAccountsSection: {\n        title: 'Unternehmens-Konten',\n      },\n      headerTitle__account: 'Konto',\n      headerTitle__security: 'Sicherheit',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Codes neu generieren',\n          headerTitle: 'Backup-Codes',\n          subtitle__regenerate:\n            'Generieren Sie einen neuen Satz sicherer Backup-Codes. Alte Backup-Code werden gelöscht und können nicht mehr verwendet werden.',\n          title__regenerate: 'Backup-Codes neu generieren',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Als Standard einstellen',\n          destructiveActionLabel: 'Telefonnummer entfernen',\n        },\n        primaryButton: 'Aktivieren Sie die Zweifaktor-Authentifizierung',\n        title: 'Zweifaktor-Authentifizierung',\n        totp: {\n          destructiveActionTitle: 'Entfernen',\n          headerTitle: 'Authentifizierungs-App',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Entfernen',\n        menuAction__rename: 'Umbenennen',\n        primaryButton: 'Passkey hinzufügen',\n        title: 'Passkeys',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Passwort festlegen',\n        primaryButton__updatePassword: 'Passwort ändern',\n        title: 'Passwort',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Telefonnummer entfernen',\n        detailsAction__nonPrimary: 'Als primär festlegen',\n        detailsAction__primary: 'Verifizierung abschließen',\n        detailsAction__unverified: 'Verifizierung abschließen',\n        primaryButton: 'Fügen Sie eine Telefonnummer hinzu',\n        title: 'Telefonnummern',\n      },\n      profileSection: {\n        primaryButton: 'Profil bearbeiten',\n        title: 'Profil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Benutzernamen festlegen',\n        primaryButton__updateUsername: 'Benutzernamen ändern',\n        title: 'Nutzername',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Wallet entfernen',\n        detailsAction__nonPrimary: 'Als primär festlegen',\n        primaryButton: 'Web3-Wallets',\n        title: 'Web3-Wallets',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Ihr Benutzername wurde aktualisiert.',\n      title__set: 'Benutzernamen aktualisieren',\n      title__update: 'Benutzernamen aktualisieren',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} wird aus diesem Konto entfernt.',\n        messageLine2: 'Sie können sich nicht mehr mit diesem Web3-Wallet anmelden.',\n        successMessage: '{{web3Wallet}} wurde aus Ihrem Konto entfernt.',\n        title: 'Entfernen Sie das Web3-Wallet',\n      },\n      subtitle__availableWallets: 'Wählen Sie ein Web3-Wallet aus, um sich mit Ihrem Konto zu verbinden.',\n      subtitle__unavailableWallets: 'Es sind keine Web3-Wallets verfügbar.',\n      successMessage: 'Die Brieftasche wurde Ihrem Konto hinzugefügt.',\n      title: 'Web3-Wallet hinzufügen',\n      web3WalletButtonsBlockButton: '{{provider|titleize}}',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: 'Jetzt anmelden',\n      actionText: 'Kein Zugang? Auf die Warteliste setzen!',\n      formButton: 'Zur Warteliste hinzufügen',\n      subtitle: 'Es tut uns leid, aber derzeit sind keine Plätze verfügbar.',\n      title: 'Warteliste beitreten',\n    },\n    success: {\n      message:\n        'Sie wurden erfolgreich auf die Warteliste gesetzt. Wir benachrichtigen Sie, sobald Plätze verfügbar sind.',\n      subtitle: 'Vielen Dank für Ihre Geduld. Sie erhalten eine Benachrichtigung, sobald der Zugang freigegeben wird.',\n      title: 'Erfolgreich auf die Warteliste gesetzt',\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BACE;AAAA,IACF,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBACE;AAAA,IACF,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBACE;AAAA,MACF,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCACE;AAAA,EACF,6BAA6B;AAAA,EAC7B,yBACE;AAAA,EACF,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBACE;AAAA,EACF,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cACE;AAAA,UACF,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BACE;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBACE;AAAA,MACF,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BACE;AAAA,IACF,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCACE;AAAA,IACF,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDACE;AAAA,IACF,wCACE;AAAA,IACF,yCACE;AAAA,IACF,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BACE;AAAA,IACF,gCACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBACE;AAAA,IACF,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QACE;AAAA,QACF,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cACE;AAAA,UACF,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SACE;AAAA,MACF,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}