{"version": 3, "sources": ["../src/fr-FR.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const frFR: LocalizationResource = {\n  locale: 'fr-FR',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Retour',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Défaut',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: \"Autre dispositif d'imitation\",\n  badge__primary: 'Principal',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Nécessite une action',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Cet appareil',\n  badge__unverified: 'Non vérifié',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Appareil utilisateur',\n  badge__you: 'Vous',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Créer l’organisation',\n    invitePage: {\n      formButtonReset: 'Passer',\n    },\n    title: 'Créer une organisation',\n  },\n  dates: {\n    lastDay: \"Hier à {{ date | timeString('fr-FR') }}\",\n    next6Days: \"{{ date | weekday('fr-FR','long') }} à {{ date | timeString('fr-FR') }}\",\n    nextDay: \"Demain à {{ date | timeString('fr-FR') }}\",\n    numeric: \"{{ date | numeric('fr-FR') }}\",\n    previous6Days: \"{{ date | weekday('fr-FR','long') }} dernier à {{ date | timeString('fr-FR') }}\",\n    sameDay: \"Aujourd'hui à {{ date | timeString('fr-FR') }}\",\n  },\n  dividerText: 'ou',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Utiliser une autre méthode',\n  footerPageLink__help: 'Aide',\n  footerPageLink__privacy: 'Vie privée',\n  footerPageLink__terms: 'Conditions',\n  formButtonPrimary: 'Continuer',\n  formButtonPrimary__verify: 'Vérifier',\n  formFieldAction__forgotPassword: 'Mot de passe oublié ?',\n  formFieldError__matchingPasswords: 'Les mots de passe correspondent.',\n  formFieldError__notMatchingPasswords: 'Les mots de passe ne correspondent pas.',\n  formFieldError__verificationLinkExpired: 'Le lien de vérification a expiré. Merci de demander un nouveau lien.',\n  formFieldHintText__optional: 'Optionnel',\n  formFieldHintText__slug:\n    'Un slug est un identifiant lisible qui doit être unique. Il est souvent utilisé dans les URL.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: 'Code de sauvegarde',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Supprimer le compte',\n  formFieldInputPlaceholder__emailAddress: 'Adresse e-mail',\n  formFieldInputPlaceholder__emailAddress_username: \"Nom d'utilisateur ou adresse e-mail\",\n  formFieldInputPlaceholder__emailAddresses:\n    'Saisissez ou collez une ou plusieurs adresses e-mail, séparées par des espaces ou des virgules',\n  formFieldInputPlaceholder__firstName: 'Prénom',\n  formFieldInputPlaceholder__lastName: 'Nom de famille',\n  formFieldInputPlaceholder__organizationDomain: \"Domaine de l'organisation\",\n  formFieldInputPlaceholder__organizationDomainEmailAddress: \"Adresse e-mail de l'organisation\",\n  formFieldInputPlaceholder__organizationName: \"Nom de l'organisation\",\n  formFieldInputPlaceholder__organizationSlug: \"Identifiant de l'organisation\",\n  formFieldInputPlaceholder__password: 'Mot de passe',\n  formFieldInputPlaceholder__phoneNumber: 'Numéro de téléphone',\n  formFieldInputPlaceholder__username: \"Nom d'utilisateur\",\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Autoriser les invitations automatiques pour ce domaine',\n  formFieldLabel__backupCode: 'Code de récupération',\n  formFieldLabel__confirmDeletion: 'Confirmation',\n  formFieldLabel__confirmPassword: 'Confirmer le mot de passe',\n  formFieldLabel__currentPassword: 'Mot de passe actuel',\n  formFieldLabel__emailAddress: 'Adresse e-mail',\n  formFieldLabel__emailAddress_username: \"Adresse e-mail ou nom d'utilisateur\",\n  formFieldLabel__emailAddresses: 'Adresses e-mail',\n  formFieldLabel__firstName: 'Prénom',\n  formFieldLabel__lastName: 'Nom de famille',\n  formFieldLabel__newPassword: 'Nouveau mot de passe',\n  formFieldLabel__organizationDomain: 'Domaine',\n  formFieldLabel__organizationDomainDeletePending: 'Supprimer les invitations et suggestions en attente',\n  formFieldLabel__organizationDomainEmailAddress: 'E-mail de vérification',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Entrer une adresse e-mail appartenant à ce domaine pour recevoir un code et vérifier ce domaine.',\n  formFieldLabel__organizationName: \"Nom de l'organisation\",\n  formFieldLabel__organizationSlug: 'Slug URL',\n  formFieldLabel__passkeyName: undefined,\n  formFieldLabel__password: 'Mot de passe',\n  formFieldLabel__phoneNumber: 'Numéro de téléphone',\n  formFieldLabel__role: 'Rôle',\n  formFieldLabel__signOutOfOtherSessions: 'Se déconnecter de tous les autres appareils',\n  formFieldLabel__username: \"Nom d'utilisateur\",\n  impersonationFab: {\n    action__signOut: 'Déconnexion',\n    title: 'Connecté en tant que {{identifier}}',\n  },\n  maintenanceMode: undefined,\n  membershipRole__admin: 'Administrateur',\n  membershipRole__basicMember: 'Membre',\n  membershipRole__guestMember: 'Invité',\n  organizationList: {\n    action__createOrganization: 'Créer une organisation',\n    action__invitationAccept: 'Rejoindre',\n    action__suggestionsAccept: \"Demande d'adhésion\",\n    createOrganization: 'Créer une Organisation',\n    invitationAcceptedLabel: 'Acceptée',\n    subtitle: 'pour continuer vers {{applicationName}}',\n    suggestionsAcceptedLabel: 'En attente d’approbation',\n    title: 'Choisissez un compte',\n    titleWithoutPersonal: 'Choisissez une organisation',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Invitations automatiques',\n    badge__automaticSuggestion: 'Suggestions automatiques',\n    badge__manualInvitation: \"Pas d'inscription automatique\",\n    badge__unverified: 'Non vérifié',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        \"Ajoutez le domaine pour le vérifier. Les utilisateurs possédant une adresses e-mail sur ce domaine peuvent rejoindre l'organisation automatiquement ou faire une demande pour y adhérer.\",\n      title: 'Ajouter un domaine',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Les invitations suivantes n’ont pas pu être envoyées. Veuillez régler ce problème et réessayer:',\n      formButtonPrimary__continue: 'Envoyer des invitations',\n      selectDropdown__role: 'Sélectionner un rôle',\n      subtitle: 'Inviter des membres à rejoindre l’organisation',\n      successMessage: 'Les invitations ont été envoyées.',\n      title: 'Inviter des membres',\n    },\n    membersPage: {\n      action__invite: 'Inviter',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Supprimer',\n        tableHeader__actions: 'Actions',\n        tableHeader__joined: 'Rejoint',\n        tableHeader__role: 'Rôle',\n        tableHeader__user: 'Utilisateur',\n      },\n      detailsTitle__emptyRow: 'Aucun membre',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            \"Invitez des utilisateurs en connectant un domaine de messagerie à votre organisation. Toute personne s'inscrivant avec une adresses e-mail sur ce domaine pourra rejoindre l'organisation.\",\n          headerTitle: 'Invitations automatiques',\n          primaryButton: 'Gérer les domaines validés',\n        },\n        table__emptyRow: \"Pas d'invitations à afficher\",\n      },\n      invitedMembersTab: {\n        menuAction__revoke: \"Révoquer l'invitation\",\n        tableHeader__invited: 'Invité',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            \"Les utilisateurs qui s'inscrivent avec un domaine de messagerie identique verront une suggestion pour demander à rejoindre votre organisation.\",\n          headerTitle: 'Suggestions automatiques',\n          primaryButton: 'Gérer les domaines validés',\n        },\n        menuAction__approve: 'Approuver',\n        menuAction__reject: 'Rejeter',\n        tableHeader__requested: 'Accès demandé',\n        table__emptyRow: 'Pas de demandes à afficher',\n      },\n      start: {\n        headerTitle__invitations: 'Invitations',\n        headerTitle__members: 'Membres',\n        headerTitle__requests: 'Demandes',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Gérer votre organisation.',\n      general: 'Général',\n      members: 'Membres',\n      title: 'Organisation',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Saisissez {{organizationName}} ci-dessous pour continuer.',\n          messageLine1: 'Êtes-vous sûr(e) de vouloir supprimer cette organisation ?',\n          messageLine2: 'Cette action est définitive et irréversible.',\n          successMessage: \"Vous avez supprimé l'organisation.\",\n          title: \"Supprimer l'organisation\",\n        },\n        leaveOrganization: {\n          actionDescription: 'Saisissez {{organizationName}} ci-dessous pour continuer.',\n          messageLine1:\n            \"Êtes-vous sûr de vouloir quitter cette organisation ? Vous perdrez l'accès à cette organisation et à ses applications.\",\n          messageLine2: 'Cette action est permanente et irréversible.',\n          successMessage: \"Vous avez quitté l'organisation.\",\n          title: \"Quitter l'organisation\",\n        },\n        title: 'Danger',\n      },\n      domainSection: {\n        menuAction__manage: 'Gérer',\n        menuAction__remove: 'Supprimer',\n        menuAction__verify: 'Valider',\n        primaryButton: 'Ajouter un domaine',\n        subtitle:\n          \"Permettre aux utilisateurs de rejoindre l'organisation automatiquement ou de faire une demande d'adhésion si leur domaine de messagerie est vérifié.\",\n        title: 'Domaines vérifiés',\n      },\n      successMessage: \"L'organisation a été mise à jour.\",\n      title: 'Profil de l’organisation',\n    },\n    removeDomainPage: {\n      messageLine1: 'Le domaine de messagerie {{domain}} sera supprimé.',\n      messageLine2: \"Les utilisateurs ne pourront plus rejoindre l'organisation automatiquement après cela.\",\n      successMessage: '{{domain}} a été supprimé.',\n      title: 'Supprimer un domaine',\n    },\n    start: {\n      headerTitle__general: 'Général',\n      headerTitle__members: 'Membres',\n      profileSection: {\n        primaryButton: 'Mettre à jour le profil',\n        title: \"Profil de l'organisation\",\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Supprimer ce domaine affectera les utilisateurs invités.',\n        removeDomainActionLabel__remove: 'Supprimer ce domaine',\n        removeDomainSubtitle: 'Supprimer ce domaine de vos domaines vérifiés',\n        removeDomainTitle: 'Supprimer un domaine',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          \"Les utilisateurs sont automatiquement invités à rejoindre l'organisation lors de leur inscription et peuvent la rejoindre à tout moment.\",\n        automaticInvitationOption__label: 'Invitations automatiques',\n        automaticSuggestionOption__description:\n          \"Les utilisateurs reçoivent une suggestion d'adhésion à l'organisation, mais doivent être approuvés par un administrateur avant de pouvoir y adhérer.\",\n        automaticSuggestionOption__label: 'Suggestions automatiques',\n        calloutInfoLabel: \"Changer le mode d'inscription n'affectera que les nouveaux utilisateurs.\",\n        calloutInvitationCountLabel: 'Invitations en attente envoyées aux utilisateurs : {{count}}',\n        calloutSuggestionCountLabel: 'Suggestions en attente envoyées aux utilisateurs : {{count}}',\n        manualInvitationOption__description:\n          \"Les utilisateurs ne peuvent être invités à l'organisation que manuellement.\",\n        manualInvitationOption__label: \"Pas d'inscription automatique\",\n        subtitle: \"Choisissez comment les utilisateurs de ce domaine peuvent rejoindre l'organisation.\",\n      },\n      start: {\n        headerTitle__danger: 'Danger',\n        headerTitle__enrollment: \"Option d'inscription\",\n      },\n      subtitle: \"Le domaine {{domain}} est maintenant vérifié. Poursuivez en sélectionnant le mode d'inscription.\",\n      title: 'Mettre à jour {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Saisissez le code de vérification envoyé à votre adresse e-mail',\n      formTitle: 'Code de vérification',\n      resendButton: \"Vous n'avez pas reçu de code ? Renvoyer\",\n      subtitle: 'Le domaine {{domainName}} doit être vérifié par e-mail.',\n      subtitleVerificationCodeScreen:\n        'Un code de vérification a été envoyé à {{emailAddress}}. Saisissez le code pour continuer.',\n      title: 'Vérifier un domaine',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Créer une organisation',\n    action__invitationAccept: 'Rejoindre',\n    action__manageOrganization: \"Gérer l'organisation\",\n    action__suggestionsAccept: 'Demander à rejoindre',\n    notSelected: 'Aucune organisation sélectionnée',\n    personalWorkspace: 'Espace de travail personnel',\n    suggestionsAcceptedLabel: \"En attente d'acceptation\",\n  },\n  paginationButton__next: 'Prochain',\n  paginationButton__previous: 'Précédent',\n  paginationRowText__displaying: 'Affichage',\n  paginationRowText__of: 'de',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Utiliser une autre méthode',\n      actionText: 'Vous ne pouvez pas accéder à votre compte ?',\n      blockButton__backupCode: 'Utiliser un code de récupération',\n      blockButton__emailCode: 'Recevoir un code par e-mail',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Utiliser le mot de passe',\n      blockButton__phoneCode: 'Recevoir un code par téléphone',\n      blockButton__totp: 'Utiliser un code d’application d’authentification',\n      getHelp: {\n        blockButton__emailSupport: 'Contacter le support par e-mail',\n        content:\n          \"Si vous ne pouvez pas accéder à votre compte, contactez notre équipe de support pour obtenir de l'aide.\",\n        title: \"Obtenir de l'aide\",\n      },\n      subtitle: 'Choisissez une méthode alternative pour vérifier votre identité.',\n      title: 'Vérification alternative',\n    },\n    backupCodeMfa: {\n      subtitle: \"Entrez l'un de vos codes de récupération pour vérifier votre compte.\",\n      title: 'Vérification par code de récupération',\n    },\n    emailCode: {\n      formTitle: 'Entrez le code de vérification',\n      resendButton: 'Renvoyer le code',\n      subtitle: 'Un code a été envoyé à votre adresse e-mail.',\n      title: 'Vérification par e-mail',\n    },\n    noAvailableMethods: {\n      message: \"Aucune méthode de vérification n'est disponible.\",\n      subtitle: 'Impossible de procéder à la vérification.',\n      title: 'Aucune méthode disponible',\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Réinitialiser le mot de passe',\n      subtitle: 'Entrez votre mot de passe pour continuer.',\n      title: 'Vérification par mot de passe',\n    },\n    phoneCode: {\n      formTitle: 'Entrez le code de vérification',\n      resendButton: 'Renvoyer le code',\n      subtitle: 'Un code a été envoyé à votre téléphone.',\n      title: 'Vérification par téléphone',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Entrez le code de vérification',\n      resendButton: 'Renvoyer le code',\n      subtitle: 'Un code a été envoyé à votre téléphone pour vérification.',\n      title: 'Vérification par téléphone',\n    },\n    totpMfa: {\n      formTitle: 'Entrez le code de vérification',\n      subtitle: \"Entrez le code généré par votre application d'authentification.\",\n      title: 'Vérification par application d’authentification',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Ajouter un compte',\n      action__signOutAll: 'Se déconnecter de tous les comptes',\n      subtitle: 'Sélectionnez le compte avec lequel vous souhaitez continuer.',\n      title: 'Choisissez un compte',\n    },\n    alternativeMethods: {\n      actionLink: \"Obtenir de l'aide\",\n      actionText: \"Aucune de ces méthode d'authentification ?\",\n      blockButton__backupCode: 'Utiliser un code de récupération',\n      blockButton__emailCode: 'Envoyer le code à {{identifier}}',\n      blockButton__emailLink: 'Envoyer le lien à {{identifier}}',\n      blockButton__passkey: 'Utiliser une clé de sécurité',\n      blockButton__password: 'Connectez-vous avec votre mot de passe',\n      blockButton__phoneCode: 'Envoyer le code à {{identifier}}',\n      blockButton__totp: \"Utilisez votre application d'authentification\",\n      getHelp: {\n        blockButton__emailSupport: 'Assistance par e-mail',\n        content:\n          \"Si vous rencontrez des difficultés pour vous connecter à votre compte, envoyez-nous un e-mail et nous travaillerons avec vous pour rétablir l'accès dès que possible.\",\n        title: \"Obtenir de l'aide\",\n      },\n      subtitle: \"Vous rencontrez des problèmes ? Vous pouvez utiliser l'une de ces méthodes pour vous connecter.\",\n      title: 'Utiliser une autre méthode',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'pour continuer vers {{applicationName}}',\n      title: 'Entrez un code de récupération',\n    },\n    emailCode: {\n      formTitle: 'Le code de vérification',\n      resendButton: 'Renvoyer le code',\n      subtitle: 'pour continuer vers {{applicationName}}',\n      title: 'Vérifiez votre messagerie',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Ce lien ne correspond pas à la demande en cours.',\n        title: 'Erreur de correspondance du client',\n      },\n      expired: {\n        subtitle: \"Retournez à l'onglet d'origine pour continuer\",\n        title: 'Ce lien de vérification a expiré',\n      },\n      failed: {\n        subtitle: \"Retourner à l'onglet original pour continuer\",\n        title: \"Ce lien de vérification n'est pas valide\",\n      },\n      formSubtitle: 'Utilisez le lien de vérification envoyé par e-mail',\n      formTitle: 'lien de vérification',\n      loading: {\n        subtitle: 'Vous allez bientôt être redirigé',\n        title: 'Signing in...',\n      },\n      resendButton: 'Renvoyer le lien',\n      subtitle: 'pour continuer vers {{applicationName}}',\n      title: 'Vérifiez votre messagerie',\n      unusedTab: {\n        title: 'Vous pouvez fermer cet onglet',\n      },\n      verified: {\n        subtitle: 'Vous serez bientôt redirigé',\n        title: 'Connexion réussie',\n      },\n      verifiedSwitchTab: {\n        subtitle: \"Revenir à l'onglet d'origine pour continuer\",\n        subtitleNewTab: \"Revenez à l'onglet nouvellement ouvert pour continuer\",\n        titleNewTab: 'Connecté sur un autre onglet',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Code de réinitialisation du mot de passe',\n      resendButton: \"Vous n'avez pas reçu de code ? Renvoyer\",\n      subtitle: 'pour réinitialiser votre mot de passe',\n      subtitle_email: \"Tout d'abord, saisissez le code envoyé à votre adresse e-mail.\",\n      subtitle_phone: \"Tout d'abord, saisissez le code envoyé à votre téléphone.\",\n      title: 'Réinitialiser le mot de passe',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Réinitialiser votre mot de passe',\n      label__alternativeMethods: 'Ou connectez-vous avec une autre méthode.',\n      title: 'Mot de passe oublié ?',\n    },\n    noAvailableMethods: {\n      message: \"Impossible de poursuivre la connexion. Aucun facteur d'authentification n'est disponible.\",\n      subtitle: \"Une erreur s'est produite\",\n      title: 'Impossible de se connecter',\n    },\n    passkey: {\n      subtitle: 'Utilisez une clé de sécurité pour continuer.',\n      title: 'Clé de sécurité',\n    },\n    password: {\n      actionLink: 'Utiliser une autre méthode',\n      subtitle: 'pour continuer vers {{applicationName}}',\n      title: 'Tapez votre mot de passe',\n    },\n    passwordPwned: {\n      title: 'Mot de passe compromis',\n    },\n    phoneCode: {\n      formTitle: 'Code de vérification',\n      resendButton: \"Vous n'avez pas reçu de code ? Renvoyer\",\n      subtitle: 'pour continuer vers {{applicationName}}',\n      title: 'Vérifiez votre téléphone',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Code de vérification',\n      resendButton: \"Vous n'avez pas reçu de code ? Renvoyer\",\n      subtitle: 'Entrez le code envoyé à votre téléphone pour continuer.',\n      title: 'Vérifiez votre téléphone',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Réinitialiser',\n      requiredMessage: 'Pour des raisons de sécurité, il est nécessaire de réinitialiser votre mot de passe.',\n      successMessage:\n        'Votre mot de passe a été modifié avec succès. Nous vous reconnectons, veuillez patienter un instant.',\n      title: 'Réinitialiser le mot de passe',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Nous devons vérifier votre identité avant de réinitialiser votre mot de passe.',\n    },\n    start: {\n      actionLink: \"S'inscrire\",\n      actionLink__join_waitlist: \"Rejoindre la liste d'attente\",\n      actionLink__use_email: 'Utiliser e-mail',\n      actionLink__use_email_username: \"Utiliser l'e-mail ou le nom d'utilisateur\",\n      actionLink__use_passkey: 'Utiliser une clé de sécurité',\n      actionLink__use_phone: 'Utiliser téléphone',\n      actionLink__use_username: \"Utiliser le nom d'utilisateur\",\n      actionText: \"Vous n'avez pas encore de compte ?\",\n      actionText__join_waitlist: \"Inscrivez-vous sur la liste d'attente\",\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'pour continuer vers {{applicationName}}',\n      subtitleCombined: undefined,\n      title: \"S'identifier\",\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Le code de vérification',\n      subtitle: \"Entrez le code de l'application d'authentification.\",\n      title: 'Vérification en deux étapes',\n    },\n  },\n  signInEnterPasswordTitle: 'Tapez votre mot de passe',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: \"S'identifier\",\n      actionText: 'Vous avez déjà un compte ?',\n      subtitle: 'pour continuer vers {{applicationName}}',\n      title: 'Remplir les champs manquants',\n    },\n    emailCode: {\n      formSubtitle: 'Entrez le code de vérification envoyé à votre adresse e-mail',\n      formTitle: 'Le code de vérification',\n      resendButton: 'Renvoyer le code',\n      subtitle: 'pour continuer vers {{applicationName}}',\n      title: 'Vérifiez votre e-mail',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Ce lien ne correspond pas à la demande en cours.',\n        title: 'Erreur de correspondance du client',\n      },\n      formSubtitle: 'Utilisez le lien de vérification envoyé à votre adresse e-mail',\n      formTitle: 'lien de vérification',\n      loading: {\n        title: 'Création de votre compte...',\n      },\n      resendButton: 'Renvoyer le lien',\n      subtitle: 'pour continuer vers {{applicationName}}',\n      title: 'Vérifiez votre e-mail',\n      verified: {\n        title: 'Compte créé',\n      },\n      verifiedSwitchTab: {\n        subtitle: \"Revenez à l'onglet nouvellement ouvert pour continuer\",\n        subtitleNewTab: \"Revenir à l'onglet précédent pour continuer\",\n        title: 'Courriel vérifié avec succès',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: \"J'accepte la Politique de confidentialité.\",\n        label__onlyTermsOfService: \"J'accepte les Conditions d'utilisation.\",\n        label__termsOfServiceAndPrivacyPolicy:\n          'J\\'accepte les {{ termsOfServiceLink || link(\"Conditions d\\'utilisation\") }} et la {{ privacyPolicyLink || link(\"Politique de confidentialité\") }}.',\n      },\n      continue: {\n        subtitle: 'Lisez et acceptez les conditions pour continuer.',\n        title: 'Consentement légal',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Entrez le code de vérification envoyé à votre numéro de téléphone',\n      formTitle: 'Le code de vérification',\n      resendButton: 'Renvoyer le code',\n      subtitle: 'pour continuer vers {{applicationName}}',\n      title: 'Vérifiez votre téléphone',\n    },\n    restrictedAccess: {\n      actionLink: 'Contacter le support',\n      actionText: 'Accès restreint.',\n      blockButton__emailSupport: 'Contacter le support par e-mail',\n      blockButton__joinWaitlist: \"Rejoindre la liste d'attente\",\n      subtitle: \"Vous n'avez pas la permission d'accéder à cette page.\",\n      subtitleWaitlist: \"Inscrivez-vous pour demander l'accès.\",\n      title: 'Accès restreint',\n    },\n    start: {\n      actionLink: \"S'identifier\",\n      actionLink__use_email: 'Utiliser votre adresse e-mail',\n      actionLink__use_phone: 'Utiliser votre téléphone',\n      actionText: 'Vous avez déjà un compte ?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'pour continuer vers {{applicationName}}',\n      subtitleCombined: 'pour continuer vers {{applicationName}}',\n      title: 'Créez votre compte',\n      titleCombined: 'Créez votre compte',\n    },\n  },\n  socialButtonsBlockButton: 'Continuer avec {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: 'Vous êtes déjà membre de cette organisation.',\n    captcha_invalid:\n      \"Inscription échouée en raison de validations de sécurité incorrectes. Veuillez rafraîchir la page pour réessayer ou contacter le support pour obtenir de l'aide.\",\n    captcha_unavailable:\n      \"Inscription échouée en raison d'une validation de captcha non réussie. Veuillez actualiser la page pour réessayer ou contacter le support pour obtenir de l'aide.\",\n    form_code_incorrect: 'Code incorrect',\n    form_identifier_exists__email_address: 'Cette adresse e-mail existe déjà.',\n    form_identifier_exists__phone_number: 'Ce numéro de téléphone existe déjà.',\n    form_identifier_exists__username: \"Ce nom d'utilisateur existe déjà.\",\n    form_identifier_not_found: \"Nous n'avons pas trouvé de compte avec ces détails.\",\n    form_param_format_invalid: 'Le format est invalide',\n    form_param_format_invalid__email_address: \"L'adresse e-mail doit être une adresse e-mail valide.\",\n    form_param_format_invalid__phone_number: 'Le numéro de téléphone doit être au format international.',\n    form_param_max_length_exceeded__first_name: 'Le prénom ne doit pas dépasser 256 caractères.',\n    form_param_max_length_exceeded__last_name: 'Le nom ne doit pas dépasser 256 caractères.',\n    form_param_max_length_exceeded__name: 'Le nom ne doit pas dépasser 256 caractères.',\n    form_param_nil: 'Ce champ est requis.',\n    form_param_value_invalid: 'La valeur fournie est invalide.',\n    form_password_incorrect: 'Mot de passe incorrect',\n    form_password_length_too_short: 'Votre mot de passe est trop court.',\n    form_password_not_strong_enough: \"Votre mot de passe n'est pas assez fort.\",\n    form_password_pwned:\n      'Ce mot de passe a été compromis et ne peut pas être utilisé. Veuillez essayer un autre mot de passe à la place.',\n    form_password_pwned__sign_in: 'Mot de passe compromis. Veuillez le réinitialiser.',\n    form_password_size_in_bytes_exceeded:\n      \"Votre mot de passe a dépassé le nombre maximum d'octets autorisés. Veuillez le raccourcir ou supprimer certains caractères spéciaux.\",\n    form_password_validation_failed: 'Mot de passe incorrect',\n    form_username_invalid_character: \"L'identifiant contient des caractères invalides.\",\n    form_username_invalid_length: \"Le nombre de caractères de l'identifiant est invalide.\",\n    identification_deletion_failed: 'Vous ne pouvez pas supprimer votre dernière identification.',\n    not_allowed_access:\n      \"L'adresse e-mail ou le numéro de téléphone n'est pas autorisée à s'inscrire. Cela peut être dû à l'utilisation de '+', '=', '#' ou '.' dans votre adresse e-mail, l'utilisation d'un domaine connecté à un service de messagerie temporaire ou l'exclusion explicite. Si vous pensez que c'est une erreur, veuillez contacter le support.\",\n    organization_domain_blocked: \"Ce domaine d'organisation est bloqué.\",\n    organization_domain_common: 'Ce domaine est trop courant pour une organisation.',\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: \"Le quota de membres de l'organisation a été dépassé.\",\n    organization_minimum_permissions_needed: 'Permissions minimales nécessaires pour accéder à cette organisation.',\n    passkey_already_exists: 'Cette clé de sécurité existe déjà.',\n    passkey_not_supported: 'Les clés de sécurité ne sont pas prises en charge sur cet appareil.',\n    passkey_pa_not_supported: 'Les clés de sécurité ne sont pas prises en charge dans cet environnement.',\n    passkey_registration_cancelled: 'Enregistrement de la clé de sécurité annulé.',\n    passkey_retrieval_cancelled: 'Récupération de la clé de sécurité annulée.',\n    passwordComplexity: {\n      maximumLength: 'moins de {{length}} caractères',\n      minimumLength: '{{length}} caractères ou plus',\n      requireLowercase: 'une lettre minuscule',\n      requireNumbers: 'un chiffre',\n      requireSpecialCharacter: 'un caractère spécial',\n      requireUppercase: 'une lettre majuscule',\n      sentencePrefix: 'Votre mot de passe doit contenir',\n    },\n    phone_number_exists: 'Ce numéro de téléphone est déjà utilisé. Veuillez essayer un autre.',\n    session_exists: 'Vous êtes déjà connecté.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: \"Votre mot de passe fonctionne mais pourrait être plus sûr. Essayez d'ajouter des caractères.\",\n      goodPassword: \"Bien joué. C'est un excellent mot de passe.\",\n      notEnough: \"Votre mot de passe n'est pas assez fort.\",\n      suggestions: {\n        allUppercase: 'Mettez quelques lettres en majuscules, mais pas toutes.',\n        anotherWord: 'Ajoutez des mots moins courants.',\n        associatedYears: 'Évitez les années qui vous sont associées. (ex: date de naissance)',\n        capitalization: 'Capitalisez mais pas seulement la première lettre.',\n        dates: 'Évitez les dates et les années qui vous sont associées. (ex: date ou année de naissance)',\n        l33t: \"Évitez les substitutions de lettres prévisibles comme '@' pour 'a'.\",\n        longerKeyboardPattern: 'Utilisez des motifs de clavier plus longs et changez de sens de frappe plusieurs fois.',\n        noNeed:\n          'Vous pouvez créer des mots de passe forts sans utiliser de symboles, de chiffres ou de lettres majuscules.',\n        pwned: 'Si vous utilisez ce mot de passe ailleurs, vous devriez le modifier.',\n        recentYears: 'Évitez les dernières années.',\n        repeated: 'Évitez les mots et les caractères répétés.',\n        reverseWords: 'Évitez les orthographes inversées des mots courants',\n        sequences: 'Évitez les séquences de caractères courantes.',\n        useWords: 'Utilisez plusieurs mots, mais évitez les phrases courantes.',\n      },\n      warnings: {\n        common: 'Ce mot de passe est couramment utilisé.',\n        commonNames: 'Les noms communs et les noms de famille sont faciles à deviner.',\n        dates: 'Les dates sont faciles à deviner.',\n        extendedRepeat: \"Les caractères répétés comme 'abcabcabc' sont faciles à deviner.\",\n        keyPattern: 'Des motifs de clavier courts sont faciles à deviner.',\n        namesByThemselves: 'Des noms ou des prénoms simples sont faciles à deviner.',\n        pwned: 'Votre mot de passe a été divulgué suite à une violation de données sur Internet.',\n        recentYears: 'Les années récentes sont faciles à deviner.',\n        sequences: \"Des séquences de caractères communs comme 'abc' sont faciles à deviner.\",\n        similarToCommon: 'Ce mot de passe est similaire à un mot de passe couramment utilisé.',\n        simpleRepeat: \"Les caractères répétés comme 'aaa' sont faciles à deviner.\",\n        straightRow: 'Les lignes droites de touches de votre clavier sont faciles à deviner.',\n        topHundred: 'Ce mot de passe est fréquemment utilisé.',\n        topTen: 'Ce mot de passe est très utilisé.',\n        userInputs: 'Le mot de passe ne doit pas comporter de données personnelles ou liées au site.',\n        wordByItself: 'Les mots simples sont faciles à deviner.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Ajouter un compte',\n    action__manageAccount: 'Gérer son compte',\n    action__signOut: 'Déconnexion',\n    action__signOutAll: 'Se déconnecter de tous les comptes',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Copié !',\n      actionLabel__copy: 'Copier tous les codes',\n      actionLabel__download: 'Télécharger en .txt',\n      actionLabel__print: 'Imprimer',\n      infoText1: 'Les codes de récupération seront activés pour ce compte.',\n      infoText2:\n        \"Gardez les codes de récupération secrets et stockez-les en toute sécurité. Vous pouvez régénérer les codes de récupération si vous pensez qu'ils ont été compromis.\",\n      subtitle__codelist: 'Conservez-les en toute sécurité et gardez-les secrets.',\n      successMessage:\n        \"Les codes de récupération sont maintenant activés. Vous pouvez utiliser l'un d'entre eux pour vous connecter à votre compte, si vous perdez l'accès à votre dispositif d'authentification. Chaque code ne peut être utilisé qu'une seule fois.\",\n      successSubtitle:\n        \"Vous pouvez utiliser l'un d'entre eux pour vous connecter à votre compte, si vous perdez l'accès à votre dispositif d'authentification.\",\n      title: 'Ajouter la vérification du code de récupération',\n      title__codelist: 'Codes de récupération',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Sélectionnez un fournisseur pour connecter votre compte.',\n      formHint__noAccounts: \"Aucun fournisseur de compte externe n'est disponible.\",\n      removeResource: {\n        messageLine1: '{{identifier}} sera supprimé de ce compte.',\n        messageLine2:\n          'Vous ne pourrez plus utiliser ce compte connecté et toutes les fonctionnalités dépendantes ne fonctionneront plus.',\n        successMessage: '{{connectedAccount}} a été supprimé de votre compte.',\n        title: 'Supprimer le compte connecté',\n      },\n      socialButtonsBlockButton: 'Connecter {{provider|titleize}} compte',\n      successMessage: 'Le fournisseur a été ajouté à votre compte',\n      title: 'Ajouter un compte connecté',\n    },\n    deletePage: {\n      actionDescription: 'Saisissez \"Supprimer le compte\" ci-dessous pour continuer.',\n      confirm: 'Supprimer le compte',\n      messageLine1: 'Êtes-vous sûr(e) de vouloir supprimer votre compte ?',\n      messageLine2: 'Cette action est définitive et irréversible.',\n      title: 'Supprimer le compte',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Un e-mail contenant un code de vérification sera envoyé à cette adresse e-mail.',\n        formSubtitle: 'Saisissez le code de vérification envoyé à {{identifier}}',\n        formTitle: 'Le code de vérification',\n        resendButton: 'Renvoyer le lien',\n        successMessage: \"L'e-mail {{identifier}} a été vérifié et ajouté à votre compte.\",\n      },\n      emailLink: {\n        formHint: 'Un e-mail contenant un lien de vérification sera envoyé à cette adresse e-mail.',\n        formSubtitle: \"Cliquez sur le lien de vérification dans l'e-mail envoyé à {{identifier}}\",\n        formTitle: 'lien de vérification',\n        resendButton: 'Renvoyer le lien',\n        successMessage: \"L'e-mail {{identifier}} a été vérifié et ajouté à votre compte.\",\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} sera supprimé de ce compte.',\n        messageLine2: 'Vous ne pourrez plus vous connecter avec cette adresse e-mail.',\n        successMessage: '{{emailAddress}} a été supprimé de votre compte.',\n        title: \"Supprimer l'adresse e-mail\",\n      },\n      title: 'Ajouter une adresse e-mail',\n      verifyTitle: 'Verifier un e-mail',\n    },\n    formButtonPrimary__add: 'Ajouter',\n    formButtonPrimary__continue: 'Continuer',\n    formButtonPrimary__finish: 'Retour',\n    formButtonPrimary__remove: 'Supprimer',\n    formButtonPrimary__save: 'Sauvegarder',\n    formButtonReset: 'Annuler',\n    mfaPage: {\n      formHint: 'Sélectionnez une méthode à ajouter.',\n      title: 'Ajouter la vérification en deux étapes',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Utiliser un numéro existant',\n      primaryButton__addPhoneNumber: 'Ajouter un numéro de téléphone',\n      removeResource: {\n        messageLine1: '{{identifier}} ne recevra plus de codes de validation lors de la connexion.',\n        messageLine2: 'Votre compte sera moins sécurisé. Souhaitez-vous continuer ?',\n        successMessage: 'La vérification en deux étapes du code SMS a été supprimée pour {{mfaPhoneCode}}',\n        title: 'Supprimer la vérification en deux étapes',\n      },\n      subtitle__availablePhoneNumbers:\n        'Sélectionnez un numéro de téléphone pour vous inscrire à la vérification en deux étapes du code SMS.',\n      subtitle__unavailablePhoneNumbers:\n        \"Il n'y a pas de numéros de téléphone disponibles pour s'inscrire à la vérification en deux étapes du code SMS.\",\n      successMessage1:\n        'Lors de la connexion, vous devrez entrer un code de vérification envoyé à ce numéro de téléphone comme étape supplémentaire.',\n      successMessage2:\n        \"Enregistrez ces codes de récupérations et conservez-les en lieu sûr. Si vous perdez l'accès à votre appareil d'authentification, vous pourrez utiliser les codes de récupérations pour vous connecter.\",\n      successTitle: 'Vérification par SMS activée',\n      title: 'Ajouter la vérification du code SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Scannez le QR code à la place',\n        buttonUnableToScan__nonPrimary: 'Vous ne pouvez pas scanner le QR code ?',\n        infoText__ableToScan:\n          \"Configurez une nouvelle méthode de connexion dans votre application d'authentification et scannez le QR code suivant pour le lier à votre compte.\",\n        infoText__unableToScan:\n          'Configurez une nouvelle méthode de connexion dans votre authentificateur et entrez la clé fournie ci-dessous.',\n        inputLabel__unableToScan1:\n          'Assurez-vous que les mots de passe basés sur le temps ou à usage unique sont activés, puis terminez la liaison de votre compte.',\n        inputLabel__unableToScan2:\n          \"Alternativement, si votre authentificateur prend en charge les URI TOTP, vous pouvez également copier l'URI complet.\",\n      },\n      removeResource: {\n        messageLine1: 'Les codes de vérification de cet authentificateur ne seront plus requis lors de la connexion.',\n        messageLine2: 'Votre compte sera moins sécurisé. Souhaitez-vous continuer ?',\n        successMessage: \"La vérification en deux étapes via l'application d'authentification a été supprimée.\",\n        title: 'Supprimer la vérification en deux étapes',\n      },\n      successMessage:\n        'La vérification en deux étapes est maintenant activée. Lors de la connexion, vous devrez saisir un code de vérification de cet authentificateur comme étape supplémentaire.',\n      title: \"Ajouter une application d'authentification\",\n      verifySubtitle: \"Entrez le code de vérification généré par votre application d'authentification\",\n      verifyTitle: 'Le code de vérification',\n    },\n    mobileButton__menu: 'Menu',\n    navbar: {\n      account: 'Compte',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Gérer votre compte.',\n      security: 'Sécurité',\n      title: 'Profil',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: 'Êtes-vous sûr de vouloir supprimer cette clé de sécurité ?',\n        title: 'Supprimer la clé de sécurité',\n      },\n      subtitle__rename: 'Renommez votre clé de sécurité pour une identification facile.',\n      title__rename: 'Renommer la clé de sécurité',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Il est recommandé de se déconnecter de tous les autres appareils qui pourraient avoir utilisé votre ancien mot de passe.',\n      readonly:\n        \"Votre mot de passe ne peut pas être modifié pour l'instant car vous ne pouvez vous connecter qu'à l'aide de la connexion entreprise.\",\n      successMessage__set: 'Votre mot de passe a été mis à jour.',\n      successMessage__signOutOfOtherSessions: 'Tous les autres appareils ont été déconnectés.',\n      successMessage__update: 'Votre mot de passe a été mis à jour.',\n      title__set: 'Mettre à jour le mot de passe',\n      title__update: 'Changer le mot de passe',\n    },\n    phoneNumberPage: {\n      infoText: 'Un SMS contenant un lien de vérification sera envoyé à ce numéro de téléphone.',\n      removeResource: {\n        messageLine1: '{{identifier}} sera supprimé de ce compte.',\n        messageLine2: 'Vous ne pourrez plus vous connecter avec ce numéro de téléphone.',\n        successMessage: '{{phoneNumber}} a été supprimé de votre compte.',\n        title: 'Supprimer le numéro de téléphone',\n      },\n      successMessage: '{{identifier}} a été vérifié et ajouté à votre compte.',\n      title: 'Ajouter un numéro de téléphone',\n      verifySubtitle: 'Saisissez le code de vérification envoyé à {{identifier}}',\n      verifyTitle: 'Vérification du numéro de téléphone',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Téléchargez une image JPG, PNG, GIF ou WEBP inférieure à 10 Mo',\n      imageFormDestructiveActionSubtitle: \"Supprimer l'image\",\n      imageFormSubtitle: 'Télécharger une image',\n      imageFormTitle: 'Photo de profil',\n      readonly:\n        \"Les informations de votre profil ont été fournies par la connexion d'entreprise et ne peuvent pas être modifiées.\",\n      successMessage: 'Votre profil a été mis a jour.',\n      title: 'Mettre à jour le profil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: \"Se déconnecter de l'appareil\",\n        title: 'Appareils actifs',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Réessayer',\n        actionLabel__reauthorize: 'Autoriser maintenant',\n        destructiveActionTitle: 'Retirer',\n        primaryButton: 'Connecter le compte',\n        subtitle__disconnected: 'Compte déconnecté. Connectez-vous à nouveau pour accéder aux fonctionnalités.',\n        subtitle__reauthorize:\n          'Les autorisations requises ont été mises à jour, ce qui peut entraîner des fonctionnalités limitées. Veuillez ré-autoriser cette application pour éviter tout problème.',\n        title: 'Comptes connectés',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Supprimer le compte',\n        title: 'Danger',\n      },\n      emailAddressesSection: {\n        destructiveAction: \"Supprimer l'adresse e-mail\",\n        detailsAction__nonPrimary: 'Définir comme principale',\n        detailsAction__primary: 'Compléter la vérification',\n        detailsAction__unverified: 'Compléter la vérification',\n        primaryButton: 'Ajouter une adresse e-mail',\n        title: 'Adresses e-mail',\n      },\n      enterpriseAccountsSection: {\n        title: 'Comptes entreprises',\n      },\n      headerTitle__account: 'Compte',\n      headerTitle__security: 'Sécurité',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Régénérer les codes',\n          headerTitle: 'Codes de récupération',\n          subtitle__regenerate:\n            'Obtenez de nouveaux codes de récupération sécurisés. Les codes de récupération antérieurs seront supprimés et ne pourront pas être utilisés.',\n          title__regenerate: 'Régénérer les codes de récupération',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Définir par défaut',\n          destructiveActionLabel: 'Supprimer le numéro de téléphone',\n        },\n        primaryButton: 'Ajouter la vérification en deux étapes',\n        title: 'Vérification en deux étapes',\n        totp: {\n          destructiveActionTitle: 'Désactiver',\n          headerTitle: \"Application d'authentification\",\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Supprimer',\n        menuAction__rename: 'Renommer',\n        primaryButton: undefined,\n        title: 'Clés de sécurité',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Définir le mot de passe',\n        primaryButton__updatePassword: 'Changer le mot de passe',\n        title: 'Mot de passe',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Supprimer le numéro de téléphone',\n        detailsAction__nonPrimary: 'Définir comme principale',\n        detailsAction__primary: 'Compléter la vérification',\n        detailsAction__unverified: 'Compléter la vérification',\n        primaryButton: 'Ajouter un numéro de téléphone',\n        title: 'Numéros de téléphone',\n      },\n      profileSection: {\n        primaryButton: 'Mettre à jour le profil',\n        title: 'Profil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: \"Définir le nom d'utilisateur\",\n        primaryButton__updateUsername: \"Changer le nom d'utilisateur\",\n        title: \"Nom d'utilisateur\",\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Supprimer le portefeuille',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Portefeuilles Web3',\n        title: 'Portefeuilles Web3',\n      },\n    },\n    usernamePage: {\n      successMessage: \"Votre nom d'utilisateur a été mis à jour.\",\n      title__set: \"Mettre à jour le nom d'utilisateur\",\n      title__update: \"Mettre à jour le nom d'utilisateur\",\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} sera supprimé de ce compte.',\n        messageLine2: 'Vous ne pourrez plus vous connecter avec ce portefeuille web3.',\n        successMessage: '{{web3Wallet}} a été supprimé de votre compte.',\n        title: 'Supprimer le portefeuille Web3',\n      },\n      subtitle__availableWallets: 'Sélectionnez un portefeuille Web3 pour vous connecter à votre compte.',\n      subtitle__unavailableWallets: \"Il n'y a pas de portefeuilles Web3 disponibles.\",\n      successMessage: 'Le portefeuille a été ajouté à votre compte.',\n      title: 'Ajouter un portefeuille Web3',\n      web3WalletButtonsBlockButton: 'Utiliser un portefeuille Web3',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: \"Rejoindre la liste d'attente\",\n      actionText: 'Intéressé ? Rejoignez-nous !',\n      formButton: \"S'inscrire\",\n      subtitle: 'Soyez parmi les premiers à découvrir notre nouveau service.',\n      title: \"Rejoindre la liste d'attente\",\n    },\n    success: {\n      message: \"Merci ! Vous avez été ajouté à la liste d'attente.\",\n      subtitle: 'Nous vous contacterons bientôt avec plus de détails.',\n      title: 'Inscription réussie',\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBACE;AAAA,EACF,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCACE;AAAA,QACF,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QACE;AAAA,QACF,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UACE;AAAA,MACF,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}