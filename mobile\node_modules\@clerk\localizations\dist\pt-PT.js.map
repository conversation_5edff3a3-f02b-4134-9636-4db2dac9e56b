{"version": 3, "sources": ["../src/pt-PT.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const ptPT: LocalizationResource = {\n  locale: 'pt-PT',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Voltar',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Padrão',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Personificar outro dispositivo',\n  badge__primary: 'Principal',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Requer ação',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Este dispositivo',\n  badge__unverified: 'Não verificado',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Dispositivo do utilizador',\n  badge__you: 'O utilizador',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Criar organização',\n    invitePage: {\n      formButtonReset: 'Ignorar',\n    },\n    title: 'Criar organização',\n  },\n  dates: {\n    lastDay: \"Ontem às {{ date | timeString('pt-PT') }}\",\n    next6Days: \"{{ date | weekday('pt-PT','long') }} às {{ date | timeString('pt-PT') }}\",\n    nextDay: \"Amanhã às {{ date | timeString('pt-PT') }}\",\n    numeric: \"{{ date | numeric('pt-PT') }}\",\n    previous6Days: \"Último {{ date | weekday('pt-PT','long') }} às {{ date | timeString('pt-PT') }}\",\n    sameDay: \"Hoje às {{ date | timeString('pt-PT') }}\",\n  },\n  dividerText: 'ou',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Utilize outro método',\n  footerPageLink__help: 'Ajuda',\n  footerPageLink__privacy: 'Privacidade',\n  footerPageLink__terms: 'Termos de uso',\n  formButtonPrimary: 'Continuar',\n  formButtonPrimary__verify: 'Verify',\n  formFieldAction__forgotPassword: 'Esqueceu a palavra-passe?',\n  formFieldError__matchingPasswords: 'Passwords match.',\n  formFieldError__notMatchingPasswords: \"Passwords don't match.\",\n  formFieldError__verificationLinkExpired: 'The verification link expired. Please request a new link.',\n  formFieldHintText__optional: 'Opcional',\n  formFieldHintText__slug: 'A slug is a human-readable ID that must be unique. It’s often used in URLs.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: 'Insira o código de backup',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Eliminar conta',\n  formFieldInputPlaceholder__emailAddress: 'Insira o seu endereço de e-mail',\n  formFieldInputPlaceholder__emailAddress_username: 'Insira o seu e-mail ou nome de utilizador',\n  formFieldInputPlaceholder__emailAddresses: 'Insira um ou mais endereços de e-mail separados por espaços ou vírgulas',\n  formFieldInputPlaceholder__firstName: 'Insira o seu primeiro nome',\n  formFieldInputPlaceholder__lastName: 'Insira o seu apelido',\n  formFieldInputPlaceholder__organizationDomain: 'Insira o domínio da organização',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: 'Insira o endereço de e-mail do domínio da organização',\n  formFieldInputPlaceholder__organizationName: 'Insira o nome da organização',\n  formFieldInputPlaceholder__organizationSlug: 'Insira o identificador da organização (slug)',\n  formFieldInputPlaceholder__password: 'Insira a sua palavra-passe',\n  formFieldInputPlaceholder__phoneNumber: 'Insira o seu número de telefone',\n  formFieldInputPlaceholder__username: 'Insira o seu nome de utilizador',\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Ativar convites automáticos para este domínio',\n  formFieldLabel__backupCode: 'Código de backup',\n  formFieldLabel__confirmDeletion: 'Confirmar exclusão',\n  formFieldLabel__confirmPassword: 'Confirmar palavra-passe',\n  formFieldLabel__currentPassword: 'Palavra-passe atual',\n  formFieldLabel__emailAddress: 'Insira o seu e-mail',\n  formFieldLabel__emailAddress_username: 'E-mail ou nome de utilizador',\n  formFieldLabel__emailAddresses: 'Endereços de e-mail',\n  formFieldLabel__firstName: 'Nome',\n  formFieldLabel__lastName: 'Apelido',\n  formFieldLabel__newPassword: 'Nova palavra-passe',\n  formFieldLabel__organizationDomain: 'Domínio',\n  formFieldLabel__organizationDomainDeletePending: 'Excluir convites e sugestões pendentes',\n  formFieldLabel__organizationDomainEmailAddress: 'Endereço de e-mail de verificação',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Endereço de e-mail para receber um código e verificar este domínio',\n  formFieldLabel__organizationName: 'Nome da organização',\n  formFieldLabel__organizationSlug: 'URL Slug',\n  formFieldLabel__passkeyName: 'Nome da Chave de Acesso',\n  formFieldLabel__password: 'Palavra-passe',\n  formFieldLabel__phoneNumber: 'Telemóvel',\n  formFieldLabel__role: 'Função',\n  formFieldLabel__signOutOfOtherSessions: 'Desconectar de todos os outros dispositivos',\n  formFieldLabel__username: 'Nome de utilizador',\n  impersonationFab: {\n    action__signOut: 'Terminar sessão',\n    title: 'Sessão iniciada como {{identifier}}',\n  },\n  maintenanceMode: 'Modo de Manutenção',\n  membershipRole__admin: 'Administrador',\n  membershipRole__basicMember: 'Membro',\n  membershipRole__guestMember: 'Convidado',\n  organizationList: {\n    action__createOrganization: 'Criar organização',\n    action__invitationAccept: 'Participar',\n    action__suggestionsAccept: 'Solicitar participação',\n    createOrganization: 'Criar organização',\n    invitationAcceptedLabel: 'Participando',\n    subtitle: 'para continuar no {{applicationName}}',\n    suggestionsAcceptedLabel: 'Aprovação pendente',\n    title: 'Selecione uma conta',\n    titleWithoutPersonal: 'Selecione uma organização',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Convites automáticos',\n    badge__automaticSuggestion: 'Sugestões automáticas',\n    badge__manualInvitation: 'Sem inscrição automática',\n    badge__unverified: 'Não verificado',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Adicione o domínio para verificar. Utilizadores com endereços de e-mail neste domínio podem entrar na organização automaticamente ou solicitar entrada.',\n      title: 'Adicionar domínio',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed: 'Os convites não puderam ser enviados. Corrija o seguinte e tente novamente:',\n      formButtonPrimary__continue: 'Enviar convites',\n      selectDropdown__role: 'Select role',\n      subtitle: 'Convidar novos membros para esta organização',\n      successMessage: 'Convites enviados com sucesso',\n      title: 'Convidar membros',\n    },\n    membersPage: {\n      action__invite: 'Convidar',\n      action__search: 'Pesquisar',\n      activeMembersTab: {\n        menuAction__remove: 'Remover membro',\n        tableHeader__actions: 'Ações',\n        tableHeader__joined: 'Entrou',\n        tableHeader__role: 'Função',\n        tableHeader__user: 'Utilizador',\n      },\n      detailsTitle__emptyRow: 'Nenhum membro para mostrar',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Convide utilizadores conectando um domínio de e-mail com a sua organização. Qualquer pessoa que se inscrever com um domínio de e-mail correspondente poderá se entrar na organização a qualquer momento.',\n          headerTitle: 'Convites automáticos',\n          primaryButton: 'Configurar domínios verificados',\n        },\n        table__emptyRow: 'Nenhum convite a mostrar',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Cancelar convite',\n        tableHeader__invited: 'Convidado',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Utilizadores que se inscrevem com um domínio de e-mail correspondente podem ver uma sugestão para solicitar participação na sua organização.',\n          headerTitle: 'Sugestões automáticas',\n          primaryButton: 'Configurar domínios verificados',\n        },\n        menuAction__approve: 'Aprovar',\n        menuAction__reject: 'Rejeitar',\n        tableHeader__requested: 'Acesso solicitado',\n        table__emptyRow: 'Nenhuma solicitação a mostrar',\n      },\n      start: {\n        headerTitle__invitations: 'Convites',\n        headerTitle__members: 'Membros',\n        headerTitle__requests: 'Pedidos',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Manage your organization.',\n      general: 'General',\n      members: 'Members',\n      title: 'Organization',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Escreva {{organizationName}} abaixo para continuar.',\n          messageLine1: 'Tem certeza de que deseja excluir esta organização?',\n          messageLine2: 'Esta ação é permanente e irreversível.',\n          successMessage: 'Você excluiu a organização.',\n          title: 'Excluir organização',\n        },\n        leaveOrganization: {\n          actionDescription: 'Escreva {{organizationName}} abaixo para continuar.',\n          messageLine1:\n            'Tem certeza de que deseja sair desta organização? Você perderá o acesso a esta organização e às suas aplicações.',\n          messageLine2: 'Esta ação é permanente e não pode ser desfeita.',\n          successMessage: 'Você saiu da organização.',\n          title: 'Sair da organização',\n        },\n        title: 'Perigo',\n      },\n      domainSection: {\n        menuAction__manage: 'Manage',\n        menuAction__remove: 'Delete',\n        menuAction__verify: 'Verify',\n        primaryButton: 'Adicionar domínio',\n        subtitle:\n          'Permita que os utilizadores juntem-se à organização automaticamente ou solicitem participação com base num domínio de e-mail verificado.',\n        title: 'Domínios verificados',\n      },\n      successMessage: 'A organização foi atualizada.',\n      title: 'Perfil da organização',\n    },\n    removeDomainPage: {\n      messageLine1: 'O domínio de e-mail {{domain}} será removido.',\n      messageLine2: 'Os utilizadores não conseguirão entrar na organização após isso.',\n      successMessage: '{{domain}} foi removido.',\n      title: 'Excluir domínio',\n    },\n    start: {\n      headerTitle__general: 'General',\n      headerTitle__members: 'Membros',\n      profileSection: {\n        primaryButton: 'Guardar',\n        title: 'Organization Profile',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'A exclusão deste domínio afetará os utilizadores convidados.',\n        removeDomainActionLabel__remove: 'Excluir domínio',\n        removeDomainSubtitle: 'Remova este domínio dos seus domínios verificados',\n        removeDomainTitle: 'Excluir domínio',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Os utilizadores são automaticamente convidados a entrar na organização quando se inscrevem.',\n        automaticInvitationOption__label: 'Convites automáticos',\n        automaticSuggestionOption__description:\n          'Os utilizadores recebem uma sugestão para solicitar participação, mas devem ser aprovados por um administrador antes de poderem entrar na organização.',\n        automaticSuggestionOption__label: 'Sugestões automáticas',\n        calloutInfoLabel: 'Alterar o modo de inscrição afetará apenas os novos utilizadores.',\n        calloutInvitationCountLabel: 'Convites pendentes enviados aos utilizadores: {{count}}',\n        calloutSuggestionCountLabel: 'Sugestões pendentes enviadas aos utilizadores: {{count}}',\n        manualInvitationOption__description: 'Os utilizadores só podem ser convidados manualmente para a organização.',\n        manualInvitationOption__label: 'Sem inscrição automática',\n        subtitle: 'Escolha como os utilizadores deste domínio se podem entrar na organização.',\n      },\n      start: {\n        headerTitle__danger: 'Perigo',\n        headerTitle__enrollment: 'Opções de inscrição',\n      },\n      subtitle: 'O domínio {{domain}} agora está verificado. Continue por selecionar o modo de inscrição.',\n      title: 'Update {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Insira o código de verificação enviado para o seu endereço de e-mail',\n      formTitle: 'Código de verificação',\n      resendButton: 'Não recebeu um código? Reenviar',\n      subtitle: 'O domínio {{domainName}} precisa ser verificado por e-mail.',\n      subtitleVerificationCodeScreen:\n        'Um código de verificação foi enviado para {{emailAddress}}. Insira-o para continuar.',\n      title: 'Verificar domínio',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Criar organização',\n    action__invitationAccept: 'Participar',\n    action__manageOrganization: 'Configurar organização',\n    action__suggestionsAccept: 'Solicitar participação',\n    notSelected: 'Nenhuma organização selecionada',\n    personalWorkspace: 'Conta pessoal',\n    suggestionsAcceptedLabel: 'Aprovação pendente',\n  },\n  paginationButton__next: 'Próximo',\n  paginationButton__previous: 'Anterior',\n  paginationRowText__displaying: 'Apresentando',\n  paginationRowText__of: 'de',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Add account',\n      action__signOutAll: 'Sign out of all accounts',\n      subtitle: 'Select the account with which you wish to continue.',\n      title: 'Choose an account',\n    },\n    alternativeMethods: {\n      actionLink: 'Ajuda',\n      actionText: 'Don’t have any of these?',\n      blockButton__backupCode: 'Utilize um código de backup',\n      blockButton__emailCode: 'Enviar código para {{identifier}}',\n      blockButton__emailLink: 'Enviar link para {{identifier}}',\n      blockButton__passkey: 'Utilizar chave de acesso',\n      blockButton__password: 'Fazer login com palavra-passe',\n      blockButton__phoneCode: 'Enviar código para {{identifier}}',\n      blockButton__totp: 'Utilize o seu autenticador',\n      getHelp: {\n        blockButton__emailSupport: 'E-mail de suporte',\n        content:\n          'Se estiver com dificuldades para entrar na sua conta, envie-nos um e-mail e iremos ajudar-te a restaurar o acesso o mais rápido possível.',\n        title: 'Ajuda',\n      },\n      subtitle: 'Facing issues? You can use any of these methods to sign in.',\n      title: 'Utilize outro método',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Insira um código de backup',\n    },\n    emailCode: {\n      formTitle: 'Código de verificação',\n      resendButton: 'Reenviar código',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Verifique o seu e-mail',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'O cliente não corresponde ao esperado. Tente novamente.',\n        title: 'Erro de cliente',\n      },\n      expired: {\n        subtitle: 'Retorne para a aba original para continuar',\n        title: 'Este link de verificação expirou',\n      },\n      failed: {\n        subtitle: 'Retorne para a aba original para continuar',\n        title: 'Este link de verificação é inválido',\n      },\n      formSubtitle: 'Utilize o link enviado no seu e-mail',\n      formTitle: 'Link de verificação',\n      loading: {\n        subtitle: 'Será redirecionado em breve',\n        title: 'Entrando...',\n      },\n      resendButton: 'Não recebeu um link? Reenviar',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Verifique o seu e-mail',\n      unusedTab: {\n        title: 'Já pode fechar esta aba',\n      },\n      verified: {\n        subtitle: 'Será redirecionado em breve',\n        title: 'Login realizado com sucesso',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Retorne para a aba original para continuar',\n        subtitleNewTab: 'Retorne para a nova aba que foi aberta para continuar',\n        titleNewTab: 'Conectado em outra aba',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Código de redefinição de palavra-passe',\n      resendButton: 'Não recebeu um código? Reenviar',\n      subtitle: 'to reset your password',\n      subtitle_email: 'First, enter the code sent to your email ID',\n      subtitle_phone: 'First, enter the code sent to your phone',\n      title: 'Reset password',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Repor a palavra-passe',\n      label__alternativeMethods: 'Ou, faça login com outro método.',\n      title: 'Esqueceu-se da palavra-passe?',\n    },\n    noAvailableMethods: {\n      message: 'Não foi possível fazer login. Não há nenhum método de autenticação disponível.',\n      subtitle: 'Ocorreu um erro',\n      title: 'Não foi possível fazer login',\n    },\n    passkey: {\n      subtitle: 'Utilize a sua chave de acesso para autenticação.',\n      title: 'Autenticação com Chave de Acesso',\n    },\n    password: {\n      actionLink: 'Utilize outro método',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Insira a sua palavra-passe',\n    },\n    passwordPwned: {\n      title: 'Este password foi comprometido em uma violação de dados. Escolha outro por motivos de segurança.',\n    },\n    phoneCode: {\n      formTitle: 'Código de verificação',\n      resendButton: 'Reenviar código',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Verifique o seu telemóvel',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Código de verificação',\n      resendButton: 'Reenviar código',\n      subtitle: 'Insira o código enviado para o seu número de telefone',\n      title: 'Verifique o seu telemóvel',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Repor Palavra-passe',\n      requiredMessage: 'For security reasons, it is required to reset your password.',\n      successMessage: 'A sua palavra-passe foi alterada com sucesso. Entrando, por favor aguarde um momento.',\n      title: 'Repor Palavra-passe',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Precisamos verificar a sua identidade antes de redefinir a palavra-passe.',\n    },\n    start: {\n      actionLink: 'Registre-se',\n      actionLink__join_waitlist: 'Juntar-se à lista de espera',\n      actionLink__use_email: 'Usar e-mail',\n      actionLink__use_email_username: 'Usar e-mail ou nome de utilizador',\n      actionLink__use_passkey: 'Usar chave de acesso',\n      actionLink__use_phone: 'Usar telemóvel',\n      actionLink__use_username: 'Usar nome de utilizador',\n      actionText: 'Não possui uma conta?',\n      actionText__join_waitlist: 'Ainda não tem uma conta? Junte-se à lista de espera.',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'para continuar em {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Entrar',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Código de verificação',\n      subtitle: 'Insira o código de verificação enviado para o seu dispositivo.',\n      title: 'Verificação de duas etapas',\n    },\n  },\n  signInEnterPasswordTitle: 'Insira a sua palavra-passe',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Entrar',\n      actionText: 'Já possui uma conta?',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Preencha os campos ausentes',\n    },\n    emailCode: {\n      formSubtitle: 'Insira o código enviado para o seu e-mail',\n      formTitle: 'Código de verificação',\n      resendButton: 'Não recebeu o código? Reenviar',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Verifique o seu e-mail',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Parece que houve um erro com a sua sessão. Tente novamente.',\n        title: 'Erro de sessão',\n      },\n      formSubtitle: 'Utilize o link enviado no seu e-mail',\n      formTitle: 'Link de verificação',\n      loading: {\n        title: 'Entrando...',\n      },\n      resendButton: 'Reenviar link',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Verifique seu e-mail',\n      verified: {\n        title: 'Registo realizado com sucesso',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Volte para a nova aba que foi aberta para continuar',\n        subtitleNewTab: 'Volte para a aba anterior para continuar',\n        title: 'E-mail verificado com sucesso',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'Aceito a Política de Privacidade',\n        label__onlyTermsOfService: 'Aceito os Termos de Serviço',\n        label__termsOfServiceAndPrivacyPolicy:\n          'Aceito os {{ termsOfServiceLink || link(\"Termos de Serviço\") }} e a {{ privacyPolicyLink || link(\"Política de Privacidade\") }}',\n      },\n      continue: {\n        subtitle: 'Ao continuar, você concorda com os termos acima.',\n        title: 'Continuar',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Insira o código enviado para o seu telemóvel',\n      formTitle: 'Código de verificação',\n      resendButton: 'Não recebeu o código? Reenviar',\n      subtitle: 'para continuar em {{applicationName}}',\n      title: 'Verifique o seu telemóvel',\n    },\n    restrictedAccess: {\n      actionLink: 'Contactar suporte',\n      actionText: 'Precisa de ajuda?',\n      blockButton__emailSupport: 'Enviar e-mail para suporte',\n      blockButton__joinWaitlist: 'Junte-se à lista de espera',\n      subtitle: 'O seu acesso está restrito. Para mais informações, entre em contacto connosco.',\n      subtitleWaitlist: 'Estamos a aguardar a sua entrada na lista de espera. Agradecemos pela paciência.',\n      title: 'Acesso Restrito',\n    },\n    start: {\n      actionLink: 'Entrar',\n      actionLink__use_email: 'Usar e-mail',\n      actionLink__use_phone: 'Usar telemóvel',\n      actionText: 'Já tem uma conta?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'para continuar em {{applicationName}}',\n      subtitleCombined: 'para continuar em {{applicationName}}',\n      title: 'Criar a sua conta',\n      titleCombined: 'Criar a sua conta',\n    },\n  },\n  socialButtonsBlockButton: 'Continuar com {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: 'Já é membro nesta organização.',\n    captcha_invalid:\n      'Não foi possível inscrever-se devido a falhas nas validações de segurança. Por favor, atualize a página para tentar novamente ou entre em contato com o suporte para obter mais ajuda.',\n    captcha_unavailable:\n      'Inscrição mal-sucedida devido a falha na validação de bot. Por favor, atualize a página para tentar novamente ou entre em contato com o suporte para obter mais ajuda.',\n    form_code_incorrect: 'Código incorreto.',\n    form_identifier_exists__email_address: 'O endereço de e-mail já está em uso.',\n    form_identifier_exists__phone_number: 'O número de telemóvel já está em uso.',\n    form_identifier_exists__username: 'O nome de utilizador já está em uso.',\n    form_identifier_not_found: 'Não foi possível encontrar uma conta com esses detalhes.',\n    form_param_format_invalid: 'Formato de parâmetro inválido.',\n    form_param_format_invalid__email_address: 'O endereço de e-mail deve ser válido.',\n    form_param_format_invalid__phone_number: 'O número de telemóvel deve ser válido.',\n    form_param_max_length_exceeded__first_name: 'O primeiro nome não deve exceder 256 caracteres.',\n    form_param_max_length_exceeded__last_name: 'O apelido não deve exceder 256 caracteres.',\n    form_param_max_length_exceeded__name: 'O nome não deve exceder 256 caracteres.',\n    form_param_nil: 'Parâmetro não pode ser nulo.',\n    form_param_value_invalid: 'Valor de parâmetro inválido.',\n    form_password_incorrect: 'Palavra-passe incorreta.',\n    form_password_length_too_short: 'A palavra-passe é muito curta.',\n    form_password_not_strong_enough: 'A sua palavra-passe não é forte o suficiente.',\n    form_password_pwned:\n      'Esta palavra-passe foi encontrada como parte de uma violação e não pode ser usada, por favor, tente outra palavra-passe.',\n    form_password_pwned__sign_in:\n      'Esta palavra-passe foi encontrada como parte de uma violação e não pode ser utilizada para login. Por favor, escolha outra.',\n    form_password_size_in_bytes_exceeded:\n      'A sua palavra-passe excedeu o número máximo de bytes permitidos, por favor, encurte-a ou remova alguns caracteres especiais.',\n    form_password_validation_failed: 'Falha na validação da palavra-passe.',\n    form_username_invalid_character: 'O nome de utilizador contém caracteres inválidos.',\n    form_username_invalid_length: 'O nome de utilizador deve ter entre 3 e 50 caracteres.',\n    identification_deletion_failed: 'Você não pode excluir a sua última identificação.',\n    not_allowed_access:\n      \"O endereço de e-mail ou número de telefone não é permitido para registro. Isso pode ser devido ao uso de '+', '=', '#' ou '.' no endereço de e-mail, o uso de um domínio associado a um serviço de e-mail temporário ou uma exclusão explícita.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: 'menos de {{length}} caracteres',\n      minimumLength: '{{length}} ou mais caracteres',\n      requireLowercase: 'uma letra minúscula',\n      requireNumbers: 'um número',\n      requireSpecialCharacter: 'um caractere especial',\n      requireUppercase: 'uma letra maiúscula',\n      sentencePrefix: 'A sua palavra-passe deve conter',\n    },\n    phone_number_exists: 'Este número de telemóvel já está em uso. Por favor, tente outro.',\n    session_exists: 'Já está conectado.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'A sua palavra-passe funciona, mas poderia ser mais forte. Tente adicionar mais caracteres.',\n      goodPassword: 'A sua palavra-passe atende a todos os requisitos necessários.',\n      notEnough: 'A sua palavra-passe não é forte o suficiente.',\n      suggestions: {\n        allUppercase: 'Utilize apenas algumas letras maiúsculas, não todas.',\n        anotherWord: 'Adicione palavras menos comuns.',\n        associatedYears: 'Evite anos associados a você.',\n        capitalization: 'Utilize outras letras maiúsculas, além do que primeira.',\n        dates: 'Evite datas e anos associados a você.',\n        l33t: \"Evite substituições previsíveis de letras, como '@' por 'a'.\",\n        longerKeyboardPattern: 'Use padrões de teclado mais longos e mude a direção da digitação várias vezes.',\n        noNeed: 'Você pode criar palavras-passes fortes sem usar símbolos, números ou letras maiúsculas.',\n        pwned: 'Se usar esta palavra-passe noutro lugar, deve mudá-la.',\n        recentYears: 'Evite anos recentes.',\n        repeated: 'Evite palavras e caracteres repetidos.',\n        reverseWords: 'Evite utilizar palavras comuns escritas de \"trás para frente\".',\n        sequences: 'Evite sequências comuns de caracteres.',\n        useWords: 'Use várias palavras, mas evite frases comuns.',\n      },\n      warnings: {\n        common: 'Esta é uma palavra-passe comumente usada.',\n        commonNames: 'Nomes e apelidos comuns são fáceis de adivinhar.',\n        dates: 'Datas são fáceis de adivinhar.',\n        extendedRepeat: 'Padrões de caracteres repetidos, como \"abcabcabc\" são fáceis de adivinhar.',\n        keyPattern: 'Padrões curtos de teclado são fáceis de adivinhar.',\n        namesByThemselves: 'Nomes ou apelidos são fáceis de adivinhar.',\n        pwned: 'A sua palavra-passe foi exposta numa violação de dados na Internet.',\n        recentYears: 'Anos recentes são fáceis de adivinhar.',\n        sequences: 'Sequências comuns de caracteres, como \"abc\" são fáceis de adivinhar.',\n        similarToCommon: 'Esta é semelhante a uma palavra-passe comumente usada.',\n        simpleRepeat: 'Caracteres repetidos, como \"aaa\" são fáceis de adivinhar.',\n        straightRow: 'Letras que vêm em sequência no teclado são fáceis de adivinhar.',\n        topHundred: 'Esta é uma palavra-passe usada frequentemente.',\n        topTen: 'Esta é uma palavra-passe muito usada.',\n        userInputs: 'Não deve haver nenhum dado pessoal ou relacionado à página.',\n        wordByItself: 'Palavras simples são fáceis de adivinhar.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Adicionar conta',\n    action__manageAccount: 'Configurar conta',\n    action__signOut: 'Terminar sessão',\n    action__signOutAll: 'Terminar sessão de todas as contas',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Copiado!',\n      actionLabel__copy: 'Copiar tudo',\n      actionLabel__download: 'Download .txt',\n      actionLabel__print: 'Imprimir',\n      infoText1: 'Códigos de backup serão ativados para esta conta.',\n      infoText2:\n        'Guarde-os em segurança e não os partilhe. Você pode gerar novos códigos de backup se suspeitar que eles tenham sido comprometidos.',\n      subtitle__codelist: 'Guarde-os em segurança e não os partilhe.',\n      successMessage:\n        'Códigos de backup foram ativados para esta conta. Pode usar um deles para fazer login na sua conta caso perca o acesso ao seu dispositivo de autenticação. Cada código poderá ser utilizado apenas uma vez.',\n      successSubtitle:\n        'Pode usar um deles para fazer login na sua conta caso perca o acesso ao seu dispositivo de autenticação.',\n      title: 'Adicionar código de backup para verificação',\n      title__codelist: 'Códigos de backup',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Selecione um provedor para conectar à sua conta.',\n      formHint__noAccounts: 'Não há provedores de conta externos disponíveis.',\n      removeResource: {\n        messageLine1: '{{identifier}} será removido desta conta.',\n        messageLine2: 'Não vai conseguir usar esta conta e, quaisquer recursos dependentes dela deixarão de funcionar.',\n        successMessage: '{{connectedAccount}} foi removido da sua conta.',\n        title: 'Remover conta conectada',\n      },\n      socialButtonsBlockButton: 'Conectar conta {{provider|titleize}}',\n      successMessage: 'O provedor foi adicionado à sua conta',\n      title: 'Conecte uma conta',\n    },\n    deletePage: {\n      actionDescription: 'Escreva Excluir conta abaixo para continuar.',\n      confirm: 'Excluir conta',\n      messageLine1: 'Tem certeza de que deseja excluir a sua conta?',\n      messageLine2: 'Esta ação é permanente e irreversível.',\n      title: 'Excluir conta',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Um e-mail contendo um código de verificação será enviado para este endereço de e-mail.',\n        formSubtitle: 'Insira o código de verificação enviado para {{identifier}}',\n        formTitle: 'Código de verificação',\n        resendButton: 'Não recebeu um código? Reenviar',\n        successMessage: 'O e-mail {{identifier}} foi adicionado à sua conta.',\n      },\n      emailLink: {\n        formHint: 'Um e-mail contendo um link de verificação será enviado para este endereço de e-mail.',\n        formSubtitle: 'Clique no link de verificação enviado para {{identifier}}',\n        formTitle: 'Link de verificação',\n        resendButton: 'Não recebeu um código? Reenviar',\n        successMessage: 'O e-mail {{identifier}} foi adicionado à sua conta.',\n      },\n      enterpriseSSOLink: {\n        formButton: 'Clique para autenticar',\n        formSubtitle: 'Complete a autenticação com {{identifier}}',\n      },\n      formHint: 'Você precisará verificar este endereço de email antes de poder adicioná-lo à sua conta.',\n      removeResource: {\n        messageLine1: '{{identifier}} será removido desta conta.',\n        messageLine2: 'Não vai conseguir fazer login novamente com este endereço de e-mail.',\n        successMessage: '{{emailAddress}} foi removido da sua conta.',\n        title: 'Remover e-mail',\n      },\n      title: 'Adicionar e-mail',\n      verifyTitle: 'Verify email address',\n    },\n    formButtonPrimary__add: 'Add',\n    formButtonPrimary__continue: 'Continuar',\n    formButtonPrimary__finish: 'Finalizar',\n    formButtonPrimary__remove: 'Remove',\n    formButtonPrimary__save: 'Save',\n    formButtonReset: 'Cancelar',\n    mfaPage: {\n      formHint: 'Selecione um método para adicionar.',\n      title: 'Adicione verificação de duas etapas',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Use existing number',\n      primaryButton__addPhoneNumber: 'Adicione um número de telemóvel',\n      removeResource: {\n        messageLine1: '{{identifier}} não receberá mais códigos de verificação ao realizar o login.',\n        messageLine2: 'A sua conta pode ficar menos segura. Tem certeza que deseja continuar?',\n        successMessage: 'Código SMS de verificação de duas etapas foi removido para {{mfaPhoneCode}}',\n        title: 'Remover verificação de duas etapas',\n      },\n      subtitle__availablePhoneNumbers:\n        'Selecione um número de telemóvel para registrar a verificação de duas etapas por código SMS.',\n      subtitle__unavailablePhoneNumbers:\n        'Não há números de telemóvel disponíveis para registrar a verificação de duas etapas por código SMS.',\n      successMessage1:\n        'When signing in, you will need to enter a verification code sent to this phone number as an additional step.',\n      successMessage2:\n        'Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.',\n      successTitle: 'SMS code verification enabled',\n      title: 'Adicionar verificação por SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Ler código QR em vez disso',\n        buttonUnableToScan__nonPrimary: 'Não pode ler o código QR?',\n        infoText__ableToScan:\n          'Configure um novo método de login no seu autenticador e leia o seguinte código QR para vinculá-lo à sua conta.',\n        infoText__unableToScan:\n          'Configure um novo método de login no seu autenticador e insira a chave informada abaixo.',\n        inputLabel__unableToScan1:\n          \"Certifique-se de que o 'One-time passwords' está ativo, de seguida, conclua a conexão da sua conta.\",\n        inputLabel__unableToScan2:\n          'Alternativamente, se o seu autenticador suportar URIs TOTP, também pode copiar a URI completa.',\n      },\n      removeResource: {\n        messageLine1: 'Os códigos de verificação deste autenticador não serão mais necessários ao fazer login.',\n        messageLine2: 'A sua conta pode ficar menos segura. Tem certeza que deseja continuar?',\n        successMessage: 'A verificação de duas etapas via autenticador foi removida.',\n        title: 'Remover verificação de duas etapas',\n      },\n      successMessage:\n        'A verificação de duas etapas está agora ativa. Ao fazer login, precisará de inserir um código de verificação deste autenticador como uma etapa adicional.',\n      title: 'Adicionar um autenticador',\n      verifySubtitle: 'Insira o código de verificação gerado pelo seu autenticador',\n      verifyTitle: 'Código de verificação',\n    },\n    mobileButton__menu: 'Menu',\n    navbar: {\n      account: 'Profile',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Manage your account info.',\n      security: 'Security',\n      title: 'Account',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: 'Tem a certeza de que deseja remover este recurso?',\n        title: 'Remover Recurso',\n      },\n      subtitle__rename: 'Altere o nome do recurso, se necessário.',\n      title__rename: 'Renomear Recurso',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'It is recommended to sign out of all other devices which may have used your old password.',\n      readonly: 'A sua palavra-passe não pode ser editada porque só pode fazer login por meio da conexão da empresa.',\n      successMessage__set: 'A sua palavra-passe foi guardada.',\n      successMessage__signOutOfOtherSessions: 'Todos os outros dispositivos foram desconectados.',\n      successMessage__update: 'A sua palavra-passe foi atualizada.',\n      title__set: 'Defina a palavra-passe',\n      title__update: 'Trocar palavra-passe',\n    },\n    phoneNumberPage: {\n      infoText: 'Um SMS contendo um link de verificação será enviado para este telemóvel.',\n      removeResource: {\n        messageLine1: '{{identifier}} será removido desta conta.',\n        messageLine2: 'Não vai conseguir fazer login novamente com este número de telemóvel.',\n        successMessage: '{{phoneNumber}} foi removido da sua conta.',\n        title: 'Remover telemóvel',\n      },\n      successMessage: '{{identifier}} foi adicionado à sua conta.',\n      title: 'Adicionar telemóvel',\n      verifySubtitle: 'Enter the verification code sent to {{identifier}}',\n      verifyTitle: 'Verify phone number',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Carregue uma imagem JPG, PNG, GIF ou WEBP menor que 10MB',\n      imageFormDestructiveActionSubtitle: 'Remover imagem',\n      imageFormSubtitle: 'Carregar imagem',\n      imageFormTitle: 'Imagem de perfil',\n      readonly: 'As informações do perfil foram fornecidas pela conexão corporativa e não podem ser editadas.',\n      successMessage: 'O perfil foi atualizado.',\n      title: 'Atualizar perfil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Terminar sessão',\n        title: 'Dispositivos ativos',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Tentar novamente',\n        actionLabel__reauthorize: 'Reautorizar agora',\n        destructiveActionTitle: 'Remover',\n        primaryButton: 'Conectar conta',\n        subtitle__disconnected: 'A conta foi desconectada. Clique abaixo para conectar novamente.',\n        subtitle__reauthorize:\n          'The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues',\n        title: 'Contas conectadas',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Excluir Conta',\n        title: 'Perigo',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Remover e-mail',\n        detailsAction__nonPrimary: 'Definir como principal',\n        detailsAction__primary: 'Completar verificação',\n        detailsAction__unverified: 'Completar verificação',\n        primaryButton: 'Adicionar um e-mail',\n        title: 'Endereços de e-mail',\n      },\n      enterpriseAccountsSection: {\n        title: 'Contas corporativas',\n      },\n      headerTitle__account: 'Conta',\n      headerTitle__security: 'Segurança',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Gerar novos códigos',\n          headerTitle: 'Códigos de backup',\n          subtitle__regenerate:\n            'Obter um novo conjunto de códigos de backup seguros. Os códigos de backup anteriores serão excluídos e não poderão ser utilizados novamente.',\n          title__regenerate: 'Gerar novos códigos de backup',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Definir como principal',\n          destructiveActionLabel: 'Remover telemóvel',\n        },\n        primaryButton: 'Adicione verificação',\n        title: 'Verificação de duas etapas',\n        totp: {\n          destructiveActionTitle: 'Remover',\n          headerTitle: 'Autenticador',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Remover chave de acesso',\n        menuAction__rename: 'Renomear chave de acesso',\n        primaryButton: undefined,\n        title: 'Chaves de Acesso',\n      },\n      passwordSection: {\n        primaryButton__setPassword: undefined,\n        primaryButton__updatePassword: undefined,\n        title: undefined,\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Remover telemóvel',\n        detailsAction__nonPrimary: 'Definir como principal',\n        detailsAction__primary: 'Completar verificação',\n        detailsAction__unverified: 'Completar verificação',\n        primaryButton: 'Adicione um telemóvel',\n        title: 'Números de telemóvel',\n      },\n      profileSection: {\n        primaryButton: 'Salvar alterações',\n        title: 'Perfil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Definir nome de utilizador',\n        primaryButton__updateUsername: 'Trocar nome de utilizador',\n        title: 'Nome de utilizador',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Remover carteira',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Carteiras Web3',\n        title: 'Carteiras Web3',\n      },\n    },\n    usernamePage: {\n      successMessage: 'O nome de utilizador foi atualizado.',\n      title__set: 'Atualizar nome de utilizador',\n      title__update: 'Atualizar nome de utilizador',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} será removido desta conta.',\n        messageLine2: 'Não vai conseguir usar esta carteira Web3 para entrar na sua conta.',\n        successMessage: '{{Web3Wallet}} foi removido da sua conta.',\n        title: 'Remover carteira Web3',\n      },\n      subtitle__availableWallets: 'Selecione uma carteira Web3 para conectar à sua conta.',\n      subtitle__unavailableWallets: 'Não há carteiras Web3 disponíveis.',\n      successMessage: 'A carteira foi adicionada à sua conta.',\n      title: 'Adicionar carteira Web3',\n      web3WalletButtonsBlockButton: 'Conectar carteira Web3',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: 'Junte-se à lista de espera',\n      actionText: 'Ainda não tem uma conta? Junte-se à lista de espera.',\n      formButton: 'Submeter',\n      subtitle: 'Deixe os seus dados para se juntar à lista de espera.',\n      title: 'Juntar-se à lista de espera',\n    },\n    success: {\n      message: 'Obrigado por se inscrever! Você será notificado assim que tiver acesso.',\n      subtitle: 'Aguarde enquanto processamos o seu pedido.',\n      title: 'Inscrição bem-sucedida na lista de espera',\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BACE;AAAA,IACF,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}