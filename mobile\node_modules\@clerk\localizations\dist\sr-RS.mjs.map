{"version": 3, "sources": ["../src/sr-RS.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const srRS: LocalizationResource = {\n  locale: 'sr-RS',\n  apiKeys: {\n    action__add: undefined,\n    action__search: undefined,\n    createdAndExpirationStatus__expiresOn: undefined,\n    createdAndExpirationStatus__never: undefined,\n    detailsTitle__emptyRow: undefined,\n    formButtonPrimary__add: undefined,\n    formFieldCaption__expiration__expiresOn: undefined,\n    formFieldCaption__expiration__never: undefined,\n    formFieldOption__expiration__180d: undefined,\n    formFieldOption__expiration__1d: undefined,\n    formFieldOption__expiration__1y: undefined,\n    formFieldOption__expiration__30d: undefined,\n    formFieldOption__expiration__60d: undefined,\n    formFieldOption__expiration__7d: undefined,\n    formFieldOption__expiration__90d: undefined,\n    formFieldOption__expiration__never: undefined,\n    formHint: undefined,\n    formTitle: undefined,\n    lastUsed__days: undefined,\n    lastUsed__hours: undefined,\n    lastUsed__minutes: undefined,\n    lastUsed__months: undefined,\n    lastUsed__seconds: undefined,\n    lastUsed__years: undefined,\n    menuAction__revoke: undefined,\n    revokeConfirmation: {\n      confirmationText: undefined,\n      formButtonPrimary__revoke: undefined,\n      formHint: undefined,\n      formTitle: undefined,\n    },\n  },\n  backButton: 'Nazad',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Podrazumevano',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Drugi uređaj koji se predstavlja',\n  badge__primary: 'Primarni',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Zahteva akciju',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Ovaj uređaj',\n  badge__unverified: 'Nepotvrđen',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Korisnički uređaj',\n  badge__you: 'Vi',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      pastDueNotice: undefined,\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    credit: undefined,\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDue: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Kreiraj organizaciju',\n    invitePage: {\n      formButtonReset: 'Preskoči',\n    },\n    title: 'Kreiraj organizaciju',\n  },\n  dates: {\n    lastDay: \"Juče u {{ date | timeString('sr-RS') }}\",\n    next6Days: \"{{ date | weekday('sr-RS','long') }} u {{ date | timeString('sr-RS') }}\",\n    nextDay: \"Sutra u {{ date | timeString('sr-RS') }}\",\n    numeric: \"{{ date | numeric('sr-RS') }}\",\n    previous6Days: \"Prošli {{ date | weekday('sr-RS','long') }} u {{ date | timeString('sr-RS') }}\",\n    sameDay: \"Danas u {{ date | timeString('sr-RS') }}\",\n  },\n  dividerText: 'ili',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Koristi drugu metodu',\n  footerPageLink__help: 'Pomoć',\n  footerPageLink__privacy: 'Privatnost',\n  footerPageLink__terms: 'Uslovi',\n  formButtonPrimary: 'Nastavi',\n  formButtonPrimary__verify: 'Verifikuj',\n  formFieldAction__forgotPassword: 'Zaboravljena lozinka?',\n  formFieldError__matchingPasswords: 'Lozinke se poklapaju.',\n  formFieldError__notMatchingPasswords: 'Lozinke se ne poklapaju.',\n  formFieldError__verificationLinkExpired: 'Link za verifikaciju je istekao. Molimo zatražite novi link.',\n  formFieldHintText__optional: 'Opciono',\n  formFieldHintText__slug: 'Slug je lako čitljivi ID koji mora biti jedinstven. Često se koristi u URL-ovima.',\n  formFieldInputPlaceholder__apiKeyDescription: undefined,\n  formFieldInputPlaceholder__apiKeyExpirationDate: undefined,\n  formFieldInputPlaceholder__apiKeyName: undefined,\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Obriši nalog',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: 'moja-org',\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__apiKeyDescription: undefined,\n  formFieldLabel__apiKeyExpiration: undefined,\n  formFieldLabel__apiKeyName: undefined,\n  formFieldLabel__automaticInvitations: 'Omogući automatske pozivnice za ovaj domen',\n  formFieldLabel__backupCode: 'Rezervni kod',\n  formFieldLabel__confirmDeletion: 'Potvrda',\n  formFieldLabel__confirmPassword: 'Potvrdi lozinku',\n  formFieldLabel__currentPassword: 'Trenutna lozinka',\n  formFieldLabel__emailAddress: 'E-mail adresa',\n  formFieldLabel__emailAddress_username: 'E-mail adresa ili korisničko ime',\n  formFieldLabel__emailAddresses: 'E-mail adrese',\n  formFieldLabel__firstName: 'Ime',\n  formFieldLabel__lastName: 'Prezime',\n  formFieldLabel__newPassword: 'Nova lozinka',\n  formFieldLabel__organizationDomain: 'Domen',\n  formFieldLabel__organizationDomainDeletePending: 'Obriši čekajuće pozivnice i predloge',\n  formFieldLabel__organizationDomainEmailAddress: 'E-mail adresa za verifikaciju',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Unesite e-mail adresu pod ovim domenom da biste primili kod i verifikovali ovaj domen.',\n  formFieldLabel__organizationName: 'Naziv',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'Naziv ključa za prolaz',\n  formFieldLabel__password: 'Lozinka',\n  formFieldLabel__phoneNumber: 'Telefonski broj',\n  formFieldLabel__role: 'Uloga',\n  formFieldLabel__signOutOfOtherSessions: 'Odjavi se sa svih drugih uređaja',\n  formFieldLabel__username: 'Korisničko ime',\n  impersonationFab: {\n    action__signOut: 'Odjavi se',\n    title: 'Prijavljeni ste kao {{identifier}}',\n  },\n  maintenanceMode: 'Trenutno smo u modu održavanja, ali ne brinite, neće trajati duže od nekoliko minuta.',\n  membershipRole__admin: 'Administrator',\n  membershipRole__basicMember: 'Član',\n  membershipRole__guestMember: 'Gost',\n  organizationList: {\n    action__createOrganization: 'Kreiraj organizaciju',\n    action__invitationAccept: 'Pridruži se',\n    action__suggestionsAccept: 'Zatraži pridruživanje',\n    createOrganization: 'Kreiraj organizaciju',\n    invitationAcceptedLabel: 'Pridružen',\n    subtitle: 'da nastavite na {{applicationName}}',\n    suggestionsAcceptedLabel: 'Čeka odobrenje',\n    title: 'Izaberi nalog',\n    titleWithoutPersonal: 'Izaberi organizaciju',\n  },\n  organizationProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    badge__automaticInvitation: 'Automatske pozivnice',\n    badge__automaticSuggestion: 'Automatski predlozi',\n    badge__manualInvitation: 'Bez automatskog uključivanja',\n    badge__unverified: 'Nepotvrđen',\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Dodajte domen za verifikaciju. Korisnici sa e-mail adresama na ovom domenu mogu se automatski pridružiti organizaciji ili zatražiti pridruživanje.',\n      title: 'Dodaj domen',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Pozivnice nisu poslate. Već postoje čekajuće pozivnice za sledeće e-mail adrese: {{email_addresses}}.',\n      formButtonPrimary__continue: 'Pošalji pozivnice',\n      selectDropdown__role: 'Izaberi ulogu',\n      subtitle: 'Unesi ili nalepi jednu ili više e-mail adresa, razdvojene razmacima ili zarezima.',\n      successMessage: 'Pozivnice su uspešno poslate',\n      title: 'Pozovi nove članove',\n    },\n    membersPage: {\n      action__invite: 'Pozovi',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Ukloni člana',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'Pridružio se',\n        tableHeader__role: 'Uloga',\n        tableHeader__user: 'Korisnik',\n      },\n      detailsTitle__emptyRow: 'Nema članova za prikaz',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Pozovi korisnike povezivanjem e-mail domena sa vašom organizacijom. Svako ko se registruje sa odgovarajućim e-mail domenom može se pridružiti organizaciji u bilo koje vreme.',\n          headerTitle: 'Automatske pozivnice',\n          primaryButton: 'Upravljaj potvrđenim domenima',\n        },\n        table__emptyRow: 'Nema pozivnica za prikaz',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Poništi pozivnicu',\n        tableHeader__invited: 'Pozvan',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Korisnici koji se registruju sa odgovarajućim e-mail domenom, moći će da vide predlog da zatraže pridruživanje vašoj organizaciji.',\n          headerTitle: 'Automatski predlozi',\n          primaryButton: 'Upravljaj potvrđenim domenima',\n        },\n        menuAction__approve: 'Odobri',\n        menuAction__reject: 'Odbaci',\n        tableHeader__requested: 'Zatražen pristup',\n        table__emptyRow: 'Nema zahteva za prikaz',\n      },\n      start: {\n        headerTitle__invitations: 'Pozivnice',\n        headerTitle__members: 'Članovi',\n        headerTitle__requests: 'Zahtevi',\n      },\n    },\n    navbar: {\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Upravljaj svojom organizacijom.',\n      general: 'Opšte',\n      members: 'Članovi',\n      title: 'Organizacija',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Upiši \"{{organizationName}}\" ispod da nastaviš.',\n          messageLine1: 'Da li ste sigurni da želite da obrišete ovu organizaciju?',\n          messageLine2: 'Ova akcija je trajna i nepovratna.',\n          successMessage: 'Organizacija je obrisana.',\n          title: 'Obriši organizaciju',\n        },\n        leaveOrganization: {\n          actionDescription: 'Upiši \"{{organizationName}}\" ispod da nastaviš.',\n          messageLine1:\n            'Da li ste sigurni da želite da napustite ovu organizaciju? Izgubićete pristup ovoj organizaciji i njenim aplikacijama.',\n          messageLine2: 'Ova akcija je trajna i nepovratna.',\n          successMessage: 'Napustili ste organizaciju.',\n          title: 'Napusti organizaciju',\n        },\n        title: 'Opasnost',\n      },\n      domainSection: {\n        menuAction__manage: 'Upravljaj',\n        menuAction__remove: 'Obriši',\n        menuAction__verify: 'Verifikuj',\n        primaryButton: 'Dodaj domen',\n        subtitle:\n          'Dozvoli korisnicima da se automatski pridruže organizaciji ili zatraže pridruživanje na osnovu potvrđenog e-mail domena.',\n        title: 'Potvrđeni domeni',\n      },\n      successMessage: 'Organizacija je ažurirana.',\n      title: 'Ažuriraj profil',\n    },\n    removeDomainPage: {\n      messageLine1: 'E-mail domen {{domain}} će biti uklonjen.',\n      messageLine2: 'Korisnici više neće moći automatski da se pridruže organizaciji nakon ovoga.',\n      successMessage: '{{domain}} je uklonjen.',\n      title: 'Ukloni domen',\n    },\n    start: {\n      headerTitle__general: 'Opšte',\n      headerTitle__members: 'Članovi',\n      profileSection: {\n        primaryButton: 'Ažuriraj profil',\n        title: 'Profil organizacije',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Uklanjanje ovog domena će uticati na pozvane korisnike.',\n        removeDomainActionLabel__remove: 'Ukloni domen',\n        removeDomainSubtitle: 'Ukloni ovaj domen iz tvojih potvrđenih domena',\n        removeDomainTitle: 'Ukloni domen',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Korisnici se automatski pozivaju da se pridruže organizaciji kada se registruju i mogu se pridružiti u bilo koje vreme.',\n        automaticInvitationOption__label: 'Automatske pozivnice',\n        automaticSuggestionOption__description:\n          'Korisnici dobijaju predlog da zatraže pridruživanje, ali moraju biti odobreni od strane administratora pre nego što mogu da se pridruže organizaciji.',\n        automaticSuggestionOption__label: 'Automatski predlozi',\n        calloutInfoLabel: 'Promena načina upisa će uticati samo na nove korisnike.',\n        calloutInvitationCountLabel: 'Čekajuće pozivnice poslate korisnicima: {{count}}',\n        calloutSuggestionCountLabel: 'Čekajući predlozi poslati korisnicima: {{count}}',\n        manualInvitationOption__description: 'Korisnici mogu biti pozvani samo ručno u organizaciju.',\n        manualInvitationOption__label: 'Bez automatskog uključivanja',\n        subtitle: 'Izaberi kako korisnici iz ovog domena mogu da se pridruže organizaciji.',\n      },\n      start: {\n        headerTitle__danger: 'Opasnost',\n        headerTitle__enrollment: 'Opcije upisa',\n      },\n      subtitle: 'Domen {{domain}} je sada verifikovan. Nastavi biranjem načina upisa.',\n      title: 'Ažuriraj {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Unesi verifikacioni kod poslat na tvoju e-mail adresu',\n      formTitle: 'Verifikacioni kod',\n      resendButton: 'Nisi primio kod? Pošalji ponovo',\n      subtitle: 'Domen {{domainName}} mora biti verifikovan putem e-maila.',\n      subtitleVerificationCodeScreen: 'Verifikacioni kod je poslat na {{emailAddress}}. Unesi kod da nastaviš.',\n      title: 'Verifikuj domen',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Kreiraj organizaciju',\n    action__invitationAccept: 'Pridruži se',\n    action__manageOrganization: 'Upravljaj',\n    action__suggestionsAccept: 'Zatraži pridruživanje',\n    notSelected: 'Organizacija nije izabrana',\n    personalWorkspace: 'Lični nalog',\n    suggestionsAcceptedLabel: 'Čeka odobrenje',\n  },\n  paginationButton__next: 'Sledeći',\n  paginationButton__previous: 'Prethodni',\n  paginationRowText__displaying: 'Prikazujem',\n  paginationRowText__of: 'od',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Dodaj nalog',\n      action__signOutAll: 'Odjavi se sa svih naloga',\n      subtitle: 'Izaberi nalog s kojim želiš da nastaviš.',\n      title: 'Izaberi nalog',\n    },\n    alternativeMethods: {\n      actionLink: 'Zatraži pomoć',\n      actionText: 'Nemaš ni jednu od ovih opcija?',\n      blockButton__backupCode: 'Koristi rezervni kod',\n      blockButton__emailCode: 'Pošalji kod na e-mail {{identifier}}',\n      blockButton__emailLink: 'Pošalji link na e-mail {{identifier}}',\n      blockButton__passkey: 'Prijavi se sa svojim ključem za prolaz',\n      blockButton__password: 'Prijavi se sa svojom lozinkom',\n      blockButton__phoneCode: 'Pošalji SMS kod na {{identifier}}',\n      blockButton__totp: 'Koristi svoju aplikaciju za autentifikaciju',\n      getHelp: {\n        blockButton__emailSupport: 'Pošalji e-mail podršci',\n        content:\n          'Ako imaš problema sa prijavljivanjem na svoj nalog, pošalji nam e-mail i pomoći ćemo ti da što pre povratiš pristup.',\n        title: 'Zatraži pomoć',\n      },\n      subtitle: 'Imaš problema? Možeš koristiti bilo koju od ovih metoda za prijavljivanje.',\n      title: 'Koristi drugu metodu',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'Tvoj rezervni kod je onaj koji si dobio kada si postavio dvostepenu autentifikaciju.',\n      title: 'Unesi rezervni kod',\n    },\n    emailCode: {\n      formTitle: 'Verifikacioni kod',\n      resendButton: 'Nisi primio kod? Pošalji ponovo',\n      subtitle: 'da nastaviš na {{applicationName}}',\n      title: 'Proveri svoj e-mail',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: 'Vrati se na originalni tab da nastaviš.',\n        title: 'Ovaj verifikacioni link je istekao',\n      },\n      failed: {\n        subtitle: 'Vrati se na originalni tab da nastaviš.',\n        title: 'Ovaj verifikacioni link je nevažeći',\n      },\n      formSubtitle: 'Koristi verifikacioni link poslat na tvoj e-mail',\n      formTitle: 'Verifikacioni link',\n      loading: {\n        subtitle: 'Uskoro ćeš biti preusmeren',\n        title: 'Prijavljujem se...',\n      },\n      resendButton: 'Nisi primio link? Pošalji ponovo',\n      subtitle: 'da nastaviš na {{applicationName}}',\n      title: 'Proveri svoj e-mail',\n      unusedTab: {\n        title: 'Možeš zatvoriti ovaj tab',\n      },\n      verified: {\n        subtitle: 'Uskoro ćeš biti preusmeren',\n        title: 'Uspešno si prijavljen',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Vrati se na originalni tab da nastaviš',\n        subtitleNewTab: 'Vrati se na novootvoreni tab da nastaviš',\n        titleNewTab: 'Prijavljen na drugom tabu',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Kod za resetovanje lozinke',\n      resendButton: 'Nisi primio kod? Pošalji ponovo',\n      subtitle: 'da resetuješ svoju lozinku',\n      subtitle_email: 'Prvo, unesi kod poslat na tvoju e-mail adresu',\n      subtitle_phone: 'Prvo, unesi kod poslat na tvoj telefon',\n      title: 'Resetuj lozinku',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Resetuj svoju lozinku',\n      label__alternativeMethods: 'Ili, prijavi se drugom metodom',\n      title: 'Zaboravljena lozinka?',\n    },\n    noAvailableMethods: {\n      message: 'Nije moguće nastaviti sa prijavom. Nema dostupnih metoda autentifikacije.',\n      subtitle: 'Došlo je do greške',\n      title: 'Nije moguće prijaviti se',\n    },\n    passkey: {\n      subtitle:\n        'Korišćenje tvojeg ključa za prolaz potvrđuje da si to ti. Tvoj uređaj može zatražiti otisak prsta, lice ili ekran zaključavanja.',\n      title: 'Koristi svoj ključ za prolaz',\n    },\n    password: {\n      actionLink: 'Koristi drugu metodu',\n      subtitle: 'Unesi lozinku koja je povezana sa tvojim nalogom',\n      title: 'Unesi svoju lozinku',\n    },\n    passwordPwned: {\n      title: 'Lozinka kompromitovana',\n    },\n    phoneCode: {\n      formTitle: 'Verifikacioni kod',\n      resendButton: 'Nisi primio kod? Pošalji ponovo',\n      subtitle: 'da nastaviš na {{applicationName}}',\n      title: 'Proveri svoj telefon',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Verifikacioni kod',\n      resendButton: 'Nisi primio kod? Pošalji ponovo',\n      subtitle: 'Da nastaviš, molimo unesi verifikacioni kod poslat na tvoj telefon',\n      title: 'Proveri svoj telefon',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Resetuj lozinku',\n      requiredMessage: 'Iz sigurnosnih razloga, potrebno je da resetuješ svoju lozinku.',\n      successMessage: 'Tvoja lozinka je uspešno promenjena. Prijavljujem te, sačekaj trenutak.',\n      title: 'Postavi novu lozinku',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Potrebno je da potvrdimo tvoj identitet pre resetovanja lozinke.',\n    },\n    start: {\n      actionLink: 'Registruj se',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Koristi e-mail',\n      actionLink__use_email_username: 'Koristi e-mail ili korisničko ime',\n      actionLink__use_passkey: 'Koristi ključ za prolaz umesto toga',\n      actionLink__use_phone: 'Koristi telefon',\n      actionLink__use_username: 'Koristi korisničko ime',\n      actionText: 'Nemaš nalog?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Dobro došao nazad! Molimo prijavi se da nastaviš',\n      subtitleCombined: undefined,\n      title: 'Prijavi se na {{applicationName}}',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Verifikacioni kod',\n      subtitle: 'Da nastaviš, molimo unesi verifikacioni kod generisan tvojom aplikacijom za autentifikaciju',\n      title: 'Dvostepena verifikacija',\n    },\n  },\n  signInEnterPasswordTitle: 'Unesi svoju lozinku',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Prijavi se',\n      actionText: 'Već imaš nalog?',\n      subtitle: 'Molimo popuni preostale detalje da nastaviš.',\n      title: 'Popuni nedostajuća polja',\n    },\n    emailCode: {\n      formSubtitle: 'Unesi verifikacioni kod poslat na tvoju e-mail adresu',\n      formTitle: 'Verifikacioni kod',\n      resendButton: 'Nisi primio kod? Pošalji ponovo',\n      subtitle: 'Unesi verifikacioni kod poslat na tvoj e-mail',\n      title: 'Verifikuj svoj e-mail',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: 'Koristi verifikacioni link poslat na tvoju e-mail adresu',\n      formTitle: 'Verifikacioni link',\n      loading: {\n        title: 'Registrujem se...',\n      },\n      resendButton: 'Nisi primio link? Pošalji ponovo',\n      subtitle: 'da nastaviš na {{applicationName}}',\n      title: 'Verifikuj svoj e-mail',\n      verified: {\n        title: 'Uspešno registrovan',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Vrati se na novootvoreni tab da nastaviš',\n        subtitleNewTab: 'Vrati se na prethodni tab da nastaviš',\n        title: 'Uspešno verifikovan e-mail',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Unesi verifikacioni kod poslat na tvoj telefonski broj',\n      formTitle: 'Verifikacioni kod',\n      resendButton: 'Nisi primio kod? Pošalji ponovo',\n      subtitle: 'Unesi verifikacioni kod poslat na tvoj telefon',\n      title: 'Verifikuj svoj telefon',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Prijavi se',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: 'Već imaš nalog?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'Dobrodošao! Molimo popuni detalje da započneš.',\n      subtitleCombined: 'Dobrodošao! Molimo popuni detalje da započneš.',\n      title: 'Kreiraj svoj nalog',\n      titleCombined: 'Kreiraj svoj nalog',\n    },\n  },\n  socialButtonsBlockButton: 'Nastavi sa {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'Registracija neuspešna zbog neuspelog sigurnosnog proveravanja. Osveži stranicu da pokušaš ponovo ili se obrati podršci za više pomoći.',\n    captcha_unavailable:\n      'Registracija neuspešna zbog neuspelog proveravanja bota. Osveži stranicu da pokušaš ponovo ili se obrati podršci za više pomoći.',\n    form_code_incorrect: 'Uneti kod je netačan.',\n    form_identifier_exists__email_address: 'Ova e-mail adresa je zauzeta. Molimo pokušaj sa drugom.',\n    form_identifier_exists__phone_number: 'Ovaj telefonski broj je zauzet. Molimo pokušaj sa drugim.',\n    form_identifier_exists__username: 'Ovo korisničko ime je zauzeto. Molimo pokušaj sa drugim.',\n    form_identifier_not_found: 'Nismo mogli pronaći nalog sa ovim podacima.',\n    form_param_format_invalid: 'Format parametra je nevažeći.',\n    form_param_format_invalid__email_address: 'E-mail adresa mora biti važeća e-mail adresa.',\n    form_param_format_invalid__phone_number: 'Telefonski broj mora biti u važećem međunarodnom formatu',\n    form_param_max_length_exceeded__first_name: 'Ime ne sme premašiti 256 karaktera.',\n    form_param_max_length_exceeded__last_name: 'Prezime ne sme premašiti 256 karaktera.',\n    form_param_max_length_exceeded__name: 'Naziv ne sme premašiti 256 karaktera.',\n    form_param_nil: 'Parametar ne može biti prazan.',\n    form_param_value_invalid: undefined,\n    form_password_incorrect: 'Lozinka je netačna.',\n    form_password_length_too_short: 'Lozinka je prekratka.',\n    form_password_not_strong_enough: 'Tvoja lozinka nije dovoljno jaka.',\n    form_password_pwned:\n      'Ova lozinka je pronađena kao deo kompromitovanih podataka i ne može se koristiti, molimo pokušaj sa drugom lozinkom.',\n    form_password_pwned__sign_in:\n      'Ova lozinka je pronađena kao deo kompromitovanih podataka i ne može se koristiti, molimo resetuj svoju lozinku.',\n    form_password_size_in_bytes_exceeded:\n      'Tvoja lozinka je premašila maksimalni dozvoljeni broj bajtova, molimo skrati je ili ukloni neke specijalne znakove.',\n    form_password_validation_failed: 'Neispravna lozinka',\n    form_username_invalid_character: 'Korisničko ime sadrži nevažeće karaktere.',\n    form_username_invalid_length: 'Dužina korisničkog imena nije validna.',\n    identification_deletion_failed: 'Ne možeš obrisati svoju poslednju identifikaciju.',\n    not_allowed_access:\n      \"Adresa e-maila ili broja telefona nije dozvoljena za registraciju. Ovo može biti zbog korišćenja '+', '=', '#' ili '.' u adresi e-maila, korišćenja domena koji je povezan sa vremenskom e-mail uslugom ili eksplicitnom isključenju.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: 'Ključ za prolaz je već registrovan sa ovim uređajem.',\n    passkey_not_supported: 'Ključevi za prolaz nisu podržani na ovom uređaju.',\n    passkey_pa_not_supported: 'Registracija zahteva platformski autentifikator, ali uređaj to ne podržava.',\n    passkey_registration_cancelled: 'Registracija ključa za prolaz je otkazana ili je isteklo vreme.',\n    passkey_retrieval_cancelled: 'Verifikacija ključa za prolaz je otkazana ili je isteklo vreme.',\n    passwordComplexity: {\n      maximumLength: 'manje od {{length}} karaktera',\n      minimumLength: '{{length}} ili više karaktera',\n      requireLowercase: 'malo slovo',\n      requireNumbers: 'broj',\n      requireSpecialCharacter: 'specijalni znak',\n      requireUppercase: 'veliko slovo',\n      sentencePrefix: 'Tvoja lozinka mora sadržati',\n    },\n    phone_number_exists: 'Ovaj telefonski broj je zauzet. Molimo pokušaj sa drugim.',\n    session_exists: 'Već ste prijavljeni.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Tvoja lozinka funkcioniše, ali može biti jača. Pokušaj dodati više karaktera.',\n      goodPassword: 'Tvoja lozinka ispunjava sve potrebne zahteve.',\n      notEnough: 'Tvoja lozinka nije dovoljno jaka.',\n      suggestions: {\n        allUppercase: 'Kapitalizuj neka, ali ne sva slova.',\n        anotherWord: 'Dodaj više reči koje su manje uobičajene.',\n        associatedYears: 'Izbegavaj godine koje su povezane sa tobom.',\n        capitalization: 'Kapitalizuj više od prvog slova.',\n        dates: 'Izbegavaj datume i godine koje su povezane sa tobom.',\n        l33t: \"Izbegavaj predvidljive zamene slova kao što su '@' za 'a'.\",\n        longerKeyboardPattern: 'Koristi duže šablone na tastaturi i promeni smer kucanja više puta.',\n        noNeed: 'Možeš kreirati jake lozinke bez korišćenja simbola, brojeva ili velikih slova.',\n        pwned: 'Ako koristiš ovu lozinku negde drugde, trebalo bi da je promeniš.',\n        recentYears: 'Izbegavaj skorašnje godine.',\n        repeated: 'Izbegavaj ponavljane reči i karaktere.',\n        reverseWords: 'Izbegavaj obrnuto napisane uobičajene reči.',\n        sequences: 'Izbegavaj uobičajene sekvence karaktera.',\n        useWords: 'Koristi više reči, ali izbegavaj uobičajene fraze.',\n      },\n      warnings: {\n        common: 'Ovo je često korišćena lozinka.',\n        commonNames: 'Uobičajena imena i prezimena su lako za pogoditi.',\n        dates: 'Datumi su lako za pogoditi.',\n        extendedRepeat: 'Ponavljani obrasci karaktera kao što su \"abcabcabc\" su lako za pogoditi.',\n        keyPattern: 'Kratki šabloni na tastaturi su lako za pogoditi.',\n        namesByThemselves: 'Pojedinačna imena ili prezimena su lako za pogoditi.',\n        pwned: 'Tvoja lozinka je otkrivena u kršenju podataka na internetu.',\n        recentYears: 'Skorašnje godine su lako za pogoditi.',\n        sequences: 'Uobičajene sekvence karaktera kao što su \"abc\" su lako za pogoditi.',\n        similarToCommon: 'Ovo je slično često korišćenoj lozinki.',\n        simpleRepeat: 'Ponavljani karakteri kao što su \"aaa\" su lako za pogoditi.',\n        straightRow: 'Direktne linije tastera na tvojoj tastaturi su lako za pogoditi.',\n        topHundred: 'Ovo je često korišćena lozinka.',\n        topTen: 'Ovo je veoma često korišćena lozinka.',\n        userInputs: 'Ne sme biti ličnih podataka ili podataka vezanih za stranicu.',\n        wordByItself: 'Pojedinačne reči su lako za pogoditi.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Dodaj nalog',\n    action__manageAccount: 'Upravljaj nalogom',\n    action__signOut: 'Odjavi se',\n    action__signOutAll: 'Odjavi se sa svih naloga',\n  },\n  userProfile: {\n    apiKeysPage: {\n      title: undefined,\n    },\n    backupCodePage: {\n      actionLabel__copied: 'Kopirano!',\n      actionLabel__copy: 'Kopiraj sve',\n      actionLabel__download: 'Preuzmi .txt',\n      actionLabel__print: 'Štampaj',\n      infoText1: 'Rezervni kodovi će biti omogućeni za ovaj nalog.',\n      infoText2:\n        'Čuvaj rezervne kodove u tajnosti i čuvaj ih na sigurnom mestu. Možeš regenerisati rezervne kodove ako sumnjaš da su kompromitovani.',\n      subtitle__codelist: 'Čuvaj ih na sigurnom i drži ih u tajnosti.',\n      successMessage:\n        'Rezervni kodovi su sada omogućeni. Možeš koristiti jedan od ovih kodova za prijavu na svoj nalog, ako izgubiš pristup svom uređaju za autentifikaciju. Svaki kod može biti korišćen samo jednom.',\n      successSubtitle:\n        'Možeš koristiti jedan od ovih kodova za prijavu na svoj nalog, ako izgubiš pristup svom uređaju za autentifikaciju.',\n      title: 'Dodaj verifikaciju rezervnim kodom',\n      title__codelist: 'Rezervni kodovi',\n    },\n    billingPage: {\n      paymentHistorySection: {\n        empty: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        tableHeader__status: undefined,\n      },\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__payments: undefined,\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      statementsSection: {\n        empty: undefined,\n        itemCaption__paidForPlan: undefined,\n        itemCaption__proratedCredit: undefined,\n        itemCaption__subscribedAndPaidForPlan: undefined,\n        notFound: undefined,\n        tableHeader__amount: undefined,\n        tableHeader__date: undefined,\n        title: undefined,\n        totalPaid: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        tableHeader__edit: undefined,\n        tableHeader__plan: undefined,\n        tableHeader__startDate: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Izaberi provajdera da povežeš svoj nalog.',\n      formHint__noAccounts: 'Nema dostupnih spoljnih provajdera naloga.',\n      removeResource: {\n        messageLine1: '{{identifier}} će biti uklonjen iz ovog naloga.',\n        messageLine2: 'Više nećeš moći da koristiš ovaj povezani nalog i bilo koje zavisne funkcije više neće raditi.',\n        successMessage: '{{connectedAccount}} je uklonjen iz tvog naloga.',\n        title: 'Ukloni povezani nalog',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}',\n      successMessage: 'Provajder je dodat na tvoj nalog',\n      title: 'Dodaj povezani nalog',\n    },\n    deletePage: {\n      actionDescription: 'Upiši \"Delete account\" ispod da nastaviš.',\n      confirm: 'Obriši nalog',\n      messageLine1: 'Da li si siguran da želiš da obrišeš svoj nalog?',\n      messageLine2: 'Ova akcija je trajna i nepovratna.',\n      title: 'Obriši nalog',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'E-mail sadrži verifikacioni kod koji će biti poslat na ovu e-mail adresu.',\n        formSubtitle: 'Unesi verifikacioni kod poslat na {{identifier}}',\n        formTitle: 'Verifikacioni kod',\n        resendButton: 'Nisi primio kod? Pošalji ponovo',\n        successMessage: 'E-mail {{identifier}} je dodat na tvoj nalog.',\n      },\n      emailLink: {\n        formHint: 'E-mail sadrži verifikacioni link koji će biti poslat na ovu e-mail adresu.',\n        formSubtitle: 'Klikni na verifikacioni link u e-mailu poslatom na {{identifier}}',\n        formTitle: 'Verifikacioni link',\n        resendButton: 'Nisi primio link? Pošalji ponovo',\n        successMessage: 'E-mail {{identifier}} je dodat na tvoj nalog.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} će biti uklonjen iz ovog naloga.',\n        messageLine2: 'Više nećeš moći da se prijaviš koristeći ovu e-mail adresu.',\n        successMessage: '{{emailAddress}} je uklonjen iz tvog naloga.',\n        title: 'Ukloni e-mail adresu',\n      },\n      title: 'Dodaj e-mail adresu',\n      verifyTitle: 'Verifikuj e-mail adresu',\n    },\n    formButtonPrimary__add: 'Dodaj',\n    formButtonPrimary__continue: 'Nastavi',\n    formButtonPrimary__finish: 'Završi',\n    formButtonPrimary__remove: 'Ukloni',\n    formButtonPrimary__save: 'Sačuvaj',\n    formButtonReset: 'Otkaži',\n    mfaPage: {\n      formHint: 'Izaberi metodu za dodavanje.',\n      title: 'Dodaj dvostepenu verifikaciju',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Koristi postojeći broj',\n      primaryButton__addPhoneNumber: 'Dodaj telefonski broj',\n      removeResource: {\n        messageLine1: '{{identifier}} više neće primati verifikacione kodove prilikom prijavljivanja.',\n        messageLine2: 'Tvoj nalog možda neće biti toliko siguran. Da li si siguran da želiš da nastaviš?',\n        successMessage: 'SMS kod dvostepene verifikacije je uklonjen za {{mfaPhoneCode}}',\n        title: 'Ukloni dvostepenu verifikaciju',\n      },\n      subtitle__availablePhoneNumbers:\n        'Izaberi postojeći telefonski broj za registraciju SMS kod dvostepene verifikacije ili dodaj novi.',\n      subtitle__unavailablePhoneNumbers:\n        'Nema dostupnih telefonskih brojeva za registraciju SMS kod dvostepene verifikacije, molimo dodaj novi.',\n      successMessage1:\n        'Kada se prijaviš, moraćeš uneti verifikacioni kod poslat na ovaj telefonski broj kao dodatni korak.',\n      successMessage2:\n        'Sačuvaj ove rezervne kodove i čuvaj ih na sigurnom mestu. Ako izgubiš pristup svom uređaju za autentifikaciju, možeš koristiti rezervne kodove za prijavu.',\n      successTitle: 'SMS kod verifikacija je omogućena',\n      title: 'Dodaj SMS kod verifikaciju',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Umesto toga, skeniraj QR kod',\n        buttonUnableToScan__nonPrimary: 'Ne možeš skenirati QR kod?',\n        infoText__ableToScan:\n          'Podesi novi način prijave u svojoj aplikaciji za autentifikaciju i skeniraj sledeći QR kod da ga povežeš sa svojim nalogom.',\n        infoText__unableToScan: 'Podesi novi način prijave u svojoj autentifikaciji i unesi ključ naveden ispod.',\n        inputLabel__unableToScan1:\n          'Uveri se da su vremenski bazirane ili jednokratne lozinke omogućene, zatim završi povezivanje svog naloga.',\n        inputLabel__unableToScan2:\n          'Alternativno, ako tvoja autentifikacija podržava TOTP URI, možeš takođe kopirati celu URI adresu.',\n      },\n      removeResource: {\n        messageLine1: 'Verifikacioni kodovi iz ove autentifikacije više neće biti potrebni prilikom prijavljivanja.',\n        messageLine2: 'Tvoj nalog možda neće biti toliko siguran. Da li si siguran da želiš da nastaviš?',\n        successMessage: 'Dvostepena verifikacija preko autentifikacione aplikacije je uklonjena.',\n        title: 'Ukloni dvostepenu verifikaciju',\n      },\n      successMessage:\n        'Dvostepena verifikacija je sada omogućena. Kada se prijaviš, moraćeš uneti verifikacioni kod iz ove autentifikacije kao dodatni korak.',\n      title: 'Dodaj autentifikacionu aplikaciju',\n      verifySubtitle: 'Unesi verifikacioni kod generisan tvojom autentifikacijom',\n      verifyTitle: 'Verifikacioni kod',\n    },\n    mobileButton__menu: 'Meni',\n    navbar: {\n      account: 'Profil',\n      apiKeys: undefined,\n      billing: undefined,\n      description: 'Upravljaj informacijama svog naloga.',\n      security: 'Sigurnost',\n      title: 'Nalog',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} će biti uklonjen iz ovog naloga.',\n        title: 'Ukloni ključ za prolaz',\n      },\n      subtitle__rename: 'Možeš promeniti ime ključa za prolaz kako bi ga lakše pronašao.',\n      title__rename: 'Preimenuj ključ za prolaz',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Preporučuje se da se odjaviš sa svih drugih uređaja koji su možda koristili tvoju staru lozinku.',\n      readonly: 'Tvoja lozinka trenutno ne može biti uređivana jer se možeš prijaviti samo preko korporativne veze.',\n      successMessage__set: 'Tvoja lozinka je postavljena.',\n      successMessage__signOutOfOtherSessions: 'Svi drugi uređaji su odjavljeni.',\n      successMessage__update: 'Tvoja lozinka je ažurirana.',\n      title__set: 'Postavi lozinku',\n      title__update: 'Ažuriraj lozinku',\n    },\n    phoneNumberPage: {\n      infoText:\n        'Tekstualna poruka sa verifikacionim kodom će biti poslata na ovaj telefonski broj. Moguće su naknade za poruke i podatke.',\n      removeResource: {\n        messageLine1: '{{identifier}} će biti uklonjen iz ovog naloga.',\n        messageLine2: 'Više nećeš moći da se prijaviš koristeći ovaj telefonski broj.',\n        successMessage: '{{phoneNumber}} je uklonjen iz tvog naloga.',\n        title: 'Ukloni telefonski broj',\n      },\n      successMessage: '{{identifier}} je dodat na tvoj nalog.',\n      title: 'Dodaj telefonski broj',\n      verifySubtitle: 'Unesi verifikacioni kod poslat na {{identifier}}',\n      verifyTitle: 'Verifikuj telefonski broj',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Preporučena veličina 1:1, do 10MB.',\n      imageFormDestructiveActionSubtitle: 'Ukloni',\n      imageFormSubtitle: 'Otpremi',\n      imageFormTitle: 'Profilna slika',\n      readonly: 'Tvoje profilne informacije su obezbeđene preko korporativne veze i ne mogu biti uređivane.',\n      successMessage: 'Tvoj profil je ažuriran.',\n      title: 'Ažuriraj profil',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Odjavi uređaj',\n        title: 'Aktivni uređaji',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Pokušaj ponovo',\n        actionLabel__reauthorize: 'Autorizuj sada',\n        destructiveActionTitle: 'Ukloni',\n        primaryButton: 'Poveži nalog',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'Potrebna ovlašćenja su ažurirana, i možda doživljavaš ograničenu funkcionalnost. Molimo re-autorizuj ovu aplikaciju da izbegneš bilo kakve probleme',\n        title: 'Povezani nalozi',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Obriši nalog',\n        title: 'Obriši nalog',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Ukloni e-mail',\n        detailsAction__nonPrimary: 'Postavi kao primarni',\n        detailsAction__primary: 'Završi verifikaciju',\n        detailsAction__unverified: 'Verifikuj',\n        primaryButton: 'Dodaj e-mail adresu',\n        title: 'E-mail adrese',\n      },\n      enterpriseAccountsSection: {\n        title: 'Korporativni nalozi',\n      },\n      headerTitle__account: 'Detalji profila',\n      headerTitle__security: 'Sigurnost',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Regeneriši',\n          headerTitle: 'Rezervni kodovi',\n          subtitle__regenerate:\n            'Dobij novi set sigurnih rezervnih kodova. Prethodni rezervni kodovi će biti obrisani i neće moći biti korišćeni.',\n          title__regenerate: 'Regeneriši rezervne kodove',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Postavi kao podrazumevani',\n          destructiveActionLabel: 'Ukloni',\n        },\n        primaryButton: 'Dodaj dvostepenu verifikaciju',\n        title: 'Dvostepena verifikacija',\n        totp: {\n          destructiveActionTitle: 'Ukloni',\n          headerTitle: 'Autentifikaciona aplikacija',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Ukloni',\n        menuAction__rename: 'Preimenuj',\n        primaryButton: undefined,\n        title: 'Ključevi za prolaz',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Postavi lozinku',\n        primaryButton__updatePassword: 'Ažuriraj lozinku',\n        title: 'Lozinka',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Ukloni telefonski broj',\n        detailsAction__nonPrimary: 'Postavi kao podrazumevani',\n        detailsAction__primary: 'Završi verifikaciju',\n        detailsAction__unverified: 'Verifikuj telefonski broj',\n        primaryButton: 'Dodaj telefonski broj',\n        title: 'Telefonski brojevi',\n      },\n      profileSection: {\n        primaryButton: 'Ažuriraj profil',\n        title: 'Profil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Postavi korisničko ime',\n        primaryButton__updateUsername: 'Ažuriraj korisničko ime',\n        title: 'Korisničko ime',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Ukloni novčanik',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 novčanici',\n        title: 'Web3 novčanici',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Tvoje korisničko ime je ažurirano.',\n      title__set: 'Postavi korisničko ime',\n      title__update: 'Ažuriraj korisničko ime',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} će biti uklonjen iz ovog naloga.',\n        messageLine2: 'Više nećeš moći da se prijaviš koristeći ovaj web3 novčanik.',\n        successMessage: '{{web3Wallet}} je uklonjen iz tvog naloga.',\n        title: 'Ukloni web3 novčanik',\n      },\n      subtitle__availableWallets: 'Izaberi web3 novčanik da ga povežeš sa svojim nalogom.',\n      subtitle__unavailableWallets: 'Nema dostupnih web3 novčanika.',\n      successMessage: 'Novčanik je dodat na tvoj nalog.',\n      title: 'Dodaj web3 novčanik',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,yCAAyC;AAAA,IACzC,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,MAClB,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe;AAAA,MACf,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BACE;AAAA,IACF,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,mBAAmB;AAAA,QACjB,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBAAwB;AAAA,QACxB,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UACE;AAAA,MACF,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}