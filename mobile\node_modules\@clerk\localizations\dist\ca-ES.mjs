// src/ca-ES.ts
var caES = {
  locale: "ca-ES",
  apiKeys: {
    action__add: void 0,
    action__search: void 0,
    createdAndExpirationStatus__expiresOn: void 0,
    createdAndExpirationStatus__never: void 0,
    detailsTitle__emptyRow: void 0,
    formButtonPrimary__add: void 0,
    formFieldCaption__expiration__expiresOn: void 0,
    formFieldCaption__expiration__never: void 0,
    formFieldOption__expiration__180d: void 0,
    formFieldOption__expiration__1d: void 0,
    formFieldOption__expiration__1y: void 0,
    formFieldOption__expiration__30d: void 0,
    formFieldOption__expiration__60d: void 0,
    formFieldOption__expiration__7d: void 0,
    formFieldOption__expiration__90d: void 0,
    formFieldOption__expiration__never: void 0,
    formHint: void 0,
    formTitle: void 0,
    lastUsed__days: void 0,
    lastUsed__hours: void 0,
    lastUsed__minutes: void 0,
    lastUsed__months: void 0,
    lastUsed__seconds: void 0,
    lastUsed__years: void 0,
    menuAction__revoke: void 0,
    revokeConfirmation: {
      confirmationText: void 0,
      formButtonPrimary__revoke: void 0,
      formHint: void 0,
      formTitle: void 0
    }
  },
  backButton: "Enrere",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Per defecte",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Un altre dispositiu impostor",
  badge__primary: "Principal",
  badge__renewsAt: void 0,
  badge__requiresAction: "Requereix acci\xF3",
  badge__startsAt: void 0,
  badge__thisDevice: "Aquest dispositiu",
  badge__unverified: "No verificat",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Dispositiu de l'usuari",
  badge__you: "Tu",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      pastDueNotice: void 0,
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    credit: void 0,
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDue: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Crea organitzaci\xF3",
    invitePage: {
      formButtonReset: "Omet"
    },
    title: "Crea organitzaci\xF3"
  },
  dates: {
    lastDay: "Ahir a les {{ date | timeString('en-US') }}",
    next6Days: "{{ date | weekday('en-US','long') }} a les {{ date | timeString('en-US') }}",
    nextDay: "Dem\xE0 a les {{ date | timeString('en-US') }}",
    numeric: "{{ date | numeric('en-US') }}",
    previous6Days: "El darrer {{ date | weekday('en-US','long') }} a les {{ date | timeString('en-US') }}",
    sameDay: "Avui a les {{ date | timeString('en-US') }}"
  },
  dividerText: "o",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Utilitza un altre m\xE8tode",
  footerPageLink__help: "Ajuda",
  footerPageLink__privacy: "Privacitat",
  footerPageLink__terms: "Termes",
  formButtonPrimary: "Continua",
  formButtonPrimary__verify: "Verifica",
  formFieldAction__forgotPassword: "Has oblidat la contrasenya?",
  formFieldError__matchingPasswords: "Les contrasenyes coincideixen.",
  formFieldError__notMatchingPasswords: "Les contrasenyes no coincideixen.",
  formFieldError__verificationLinkExpired: "L'enlla\xE7 de verificaci\xF3 ha caducat. Si us plau, sol\xB7licita un nou enlla\xE7.",
  formFieldHintText__optional: "Opcional",
  formFieldHintText__slug: "Un slug \xE9s un ID llegible per humans que ha de ser \xFAnic. Sovint s'utilitza en URL.",
  formFieldInputPlaceholder__apiKeyDescription: void 0,
  formFieldInputPlaceholder__apiKeyExpirationDate: void 0,
  formFieldInputPlaceholder__apiKeyName: void 0,
  formFieldInputPlaceholder__backupCode: "Codi de seguretat",
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Eliminar compte",
  formFieldInputPlaceholder__emailAddress: "<EMAIL>",
  formFieldInputPlaceholder__emailAddress_username: "<EMAIL> o nom d'usuari",
  formFieldInputPlaceholder__emailAddresses: "<EMAIL>, <EMAIL>",
  formFieldInputPlaceholder__firstName: "Nom",
  formFieldInputPlaceholder__lastName: "Cognoms",
  formFieldInputPlaceholder__organizationDomain: "exemple.com",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "<EMAIL>",
  formFieldInputPlaceholder__organizationName: "Nom de l'organitzaci\xF3",
  formFieldInputPlaceholder__organizationSlug: "la-meva-org",
  formFieldInputPlaceholder__password: "Contrasenya",
  formFieldInputPlaceholder__phoneNumber: "N\xFAmero de tel\xE8fon",
  formFieldInputPlaceholder__username: "Nom d'usuari",
  formFieldLabel__apiKeyDescription: void 0,
  formFieldLabel__apiKeyExpiration: void 0,
  formFieldLabel__apiKeyName: void 0,
  formFieldLabel__automaticInvitations: "Activa invitacions autom\xE0tiques per a aquest domini",
  formFieldLabel__backupCode: "Codi de seguretat",
  formFieldLabel__confirmDeletion: "Confirmaci\xF3",
  formFieldLabel__confirmPassword: "Confirma la contrasenya",
  formFieldLabel__currentPassword: "Contrasenya actual",
  formFieldLabel__emailAddress: "Adre\xE7a de correu electr\xF2nic",
  formFieldLabel__emailAddress_username: "Adre\xE7a de correu electr\xF2nic o nom d'usuari",
  formFieldLabel__emailAddresses: "Adreces de correu electr\xF2nic",
  formFieldLabel__firstName: "Nom",
  formFieldLabel__lastName: "Cognoms",
  formFieldLabel__newPassword: "Nova contrasenya",
  formFieldLabel__organizationDomain: "Domini",
  formFieldLabel__organizationDomainDeletePending: "Elimina invitacions pendents i suggeriments",
  formFieldLabel__organizationDomainEmailAddress: "Adre\xE7a de correu electr\xF2nic de verificaci\xF3",
  formFieldLabel__organizationDomainEmailAddressDescription: "Introdueix una adre\xE7a de correu electr\xF2nic sota aquest domini per rebre un codi i verificar aquest domini.",
  formFieldLabel__organizationName: "Nom",
  formFieldLabel__organizationSlug: "Slug",
  formFieldLabel__passkeyName: void 0,
  formFieldLabel__password: "Contrasenya",
  formFieldLabel__phoneNumber: "N\xFAmero de tel\xE8fon",
  formFieldLabel__role: "Rol",
  formFieldLabel__signOutOfOtherSessions: "Tanca la sessi\xF3 de tots els altres dispositius",
  formFieldLabel__username: "Nom d'usuari",
  impersonationFab: {
    action__signOut: "Tanca la sessi\xF3",
    title: "Connectat com a {{identifier}}"
  },
  maintenanceMode: void 0,
  membershipRole__admin: "Administrador",
  membershipRole__basicMember: "Membre",
  membershipRole__guestMember: "Convidat",
  organizationList: {
    action__createOrganization: "Crea organitzaci\xF3",
    action__invitationAccept: "Uneix-te",
    action__suggestionsAccept: "Sol\xB7licita unir-te",
    createOrganization: "Crea Organitzaci\xF3",
    invitationAcceptedLabel: "Unit",
    subtitle: "per continuar a {{applicationName}}",
    suggestionsAcceptedLabel: "Aprovaci\xF3 pendent",
    title: "Trieu un compte",
    titleWithoutPersonal: "Trieu una organitzaci\xF3"
  },
  organizationProfile: {
    apiKeysPage: {
      title: void 0
    },
    badge__automaticInvitation: "Invitacions autom\xE0tiques",
    badge__automaticSuggestion: "Suggeriments autom\xE0tics",
    badge__manualInvitation: "Sense inscripci\xF3 autom\xE0tica",
    badge__unverified: "No verificat",
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Afegeix el domini per verificar. Els usuaris amb adreces de correu electr\xF2nic en aquest domini poden unir-se a l'organitzaci\xF3 autom\xE0ticament o sol\xB7licitar unir-se.",
      title: "Afegeix domini"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Les invitacions no s'han pogut enviar. Ja hi ha invitacions pendents per a les seg\xFCents adreces de correu electr\xF2nic: {{email_addresses}}.",
      formButtonPrimary__continue: "Envia invitacions",
      selectDropdown__role: "Selecciona rol",
      subtitle: "Introdueix o enganxa una o m\xE9s adreces de correu electr\xF2nic, separades per espais o comes.",
      successMessage: "Les invitacions s'han enviat correctament",
      title: "Convida nous membres"
    },
    membersPage: {
      action__invite: "Convida",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "Elimina membre",
        tableHeader__actions: void 0,
        tableHeader__joined: "Unit",
        tableHeader__role: "Rol",
        tableHeader__user: "Usuari"
      },
      detailsTitle__emptyRow: "No hi ha membres per mostrar",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Convida usuaris connectant un domini de correu electr\xF2nic amb la teva organitzaci\xF3. Qualsevol que es registri amb un domini de correu electr\xF2nic coincident podr\xE0 unir-se a l'organitzaci\xF3 en qualsevol moment.",
          headerTitle: "Invitacions autom\xE0tiques",
          primaryButton: "Gestiona dominis verificats"
        },
        table__emptyRow: "No hi ha invitacions per mostrar"
      },
      invitedMembersTab: {
        menuAction__revoke: "Revoca invitaci\xF3",
        tableHeader__invited: "Convidat"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Els usuaris que es registren amb un domini de correu electr\xF2nic coincident, podran veure una sugger\xE8ncia per sol\xB7licitar unir-se a la teva organitzaci\xF3.",
          headerTitle: "Suggeriments autom\xE0tics",
          primaryButton: "Gestiona dominis verificats"
        },
        menuAction__approve: "Aprova",
        menuAction__reject: "Rebutja",
        tableHeader__requested: "Acc\xE9s sol\xB7licitat",
        table__emptyRow: "No hi ha sol\xB7licituds per mostrar"
      },
      start: {
        headerTitle__invitations: "Invitacions",
        headerTitle__members: "Membres",
        headerTitle__requests: "Sol\xB7licituds"
      }
    },
    navbar: {
      apiKeys: void 0,
      billing: void 0,
      description: "Gestiona la teva organitzaci\xF3.",
      general: "General",
      members: "Membres",
      title: "Organitzaci\xF3"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Escriu "{{organizationName}}" a continuaci\xF3 per continuar.',
          messageLine1: "Est\xE0s segur que vols eliminar aquesta organitzaci\xF3?",
          messageLine2: "Aquesta acci\xF3 \xE9s permanent i irreversible.",
          successMessage: "Has eliminat l'organitzaci\xF3.",
          title: "Elimina organitzaci\xF3"
        },
        leaveOrganization: {
          actionDescription: 'Escriu "{{organizationName}}" a continuaci\xF3 per continuar.',
          messageLine1: "Est\xE0s segur que vols deixar aquesta organitzaci\xF3? Perdr\xE0s l'acc\xE9s a aquesta organitzaci\xF3 i les seves aplicacions.",
          messageLine2: "Aquesta acci\xF3 \xE9s permanent i irreversible.",
          successMessage: "Has deixat l'organitzaci\xF3.",
          title: "Deixa organitzaci\xF3"
        },
        title: "Perill"
      },
      domainSection: {
        menuAction__manage: "Gestiona",
        menuAction__remove: "Elimina",
        menuAction__verify: "Verifica",
        primaryButton: "Afegeix domini",
        subtitle: "Permet als usuaris unir-se a l'organitzaci\xF3 autom\xE0ticament o sol\xB7licitar unir-se basant-se en un domini de correu electr\xF2nic verificat.",
        title: "Dominis verificats"
      },
      successMessage: "L'organitzaci\xF3 s'ha actualitzat.",
      title: "Actualitza perfil"
    },
    removeDomainPage: {
      messageLine1: "El domini de correu electr\xF2nic {{domain}} ser\xE0 eliminat.",
      messageLine2: "Els usuaris ja no podran unir-se a l'organitzaci\xF3 autom\xE0ticament despr\xE9s d'aix\xF2.",
      successMessage: "{{domain}} ha estat eliminat.",
      title: "Elimina domini"
    },
    start: {
      headerTitle__general: "General",
      headerTitle__members: "Membres",
      profileSection: {
        primaryButton: "Actualitza perfil",
        title: "Perfil de l'Organitzaci\xF3",
        uploadAction__title: "Logotip"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Eliminar aquest domini afectar\xE0 els usuaris convidats.",
        removeDomainActionLabel__remove: "Elimina domini",
        removeDomainSubtitle: "Elimina aquest domini dels teus dominis verificats",
        removeDomainTitle: "Elimina domini"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Els usuaris s\xF3n convidats autom\xE0ticament a unir-se a l'organitzaci\xF3 quan es registren i poden unir-se en qualsevol moment.",
        automaticInvitationOption__label: "Invitacions autom\xE0tiques",
        automaticSuggestionOption__description: "Els usuaris reben una sugger\xE8ncia per sol\xB7licitar unir-se, per\xF2 ha de ser aprovat per un administrador abans de poder-se unir a l'organitzaci\xF3.",
        automaticSuggestionOption__label: "Suggeriments autom\xE0tics",
        calloutInfoLabel: "Canviar el mode d'inscripci\xF3 nom\xE9s afectar\xE0 els nous usuaris.",
        calloutInvitationCountLabel: "Invitacions pendents enviades a usuaris: {{count}}",
        calloutSuggestionCountLabel: "Suggeriments pendents enviats a usuaris: {{count}}",
        manualInvitationOption__description: "Els usuaris nom\xE9s poden ser convidats manualment a l'organitzaci\xF3.",
        manualInvitationOption__label: "Sense inscripci\xF3 autom\xE0tica",
        subtitle: "Trieu com els usuaris d'aquest domini poden unir-se a l'organitzaci\xF3."
      },
      start: {
        headerTitle__danger: "Perill",
        headerTitle__enrollment: "Opcions d'inscripci\xF3"
      },
      subtitle: "El domini {{domain}} ara est\xE0 verificat. Continua seleccionant el mode d'inscripci\xF3.",
      title: "Actualitza {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Introdueix el codi de verificaci\xF3 enviat al teu correu electr\xF2nic",
      formTitle: "Codi de verificaci\xF3",
      resendButton: "No has rebut el codi? Reenvia",
      subtitle: "El domini {{domainName}} necessita ser verificat via correu electr\xF2nic.",
      subtitleVerificationCodeScreen: "S'ha enviat un codi de verificaci\xF3 a {{emailAddress}}. Introdueix el codi per continuar.",
      title: "Verifica domini"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Crea organitzaci\xF3",
    action__invitationAccept: "Uneix-te",
    action__manageOrganization: "Gestiona",
    action__suggestionsAccept: "Sol\xB7licita unir-te",
    notSelected: "No s'ha seleccionat cap organitzaci\xF3",
    personalWorkspace: "Compte personal",
    suggestionsAcceptedLabel: "Aprovaci\xF3 pendent"
  },
  paginationButton__next: "Seg\xFCent",
  paginationButton__previous: "Anterior",
  paginationRowText__displaying: "Mostrant",
  paginationRowText__of: "de",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Afegeix compte",
      action__signOutAll: "Tanca la sessi\xF3 de tots els comptes",
      subtitle: "Selecciona el compte amb el qual vols continuar.",
      title: "Trieu un compte"
    },
    alternativeMethods: {
      actionLink: "Obt\xE9n ajuda",
      actionText: "No tens cap d'aquests?",
      blockButton__backupCode: "Utilitza un codi de seguretat",
      blockButton__emailCode: "Envia codi per correu electr\xF2nic a {{identifier}}",
      blockButton__emailLink: "Envia enlla\xE7 per correu electr\xF2nic a {{identifier}}",
      blockButton__passkey: void 0,
      blockButton__password: "Inicia sessi\xF3 amb la teva contrasenya",
      blockButton__phoneCode: "Envia codi SMS a {{identifier}}",
      blockButton__totp: "Utilitza la teva aplicaci\xF3 d'autenticaci\xF3",
      getHelp: {
        blockButton__emailSupport: "Suport per correu electr\xF2nic",
        content: "Si tens dificultats per iniciar sessi\xF3 al teu compte, envia'ns un correu electr\xF2nic i treballarem amb tu per restaurar l'acc\xE9s tan aviat com sigui possible.",
        title: "Obt\xE9n ajuda"
      },
      subtitle: "Tens problemes? Pots utilitzar qualsevol d'aquests m\xE8todes per iniciar sessi\xF3.",
      title: "Utilitza un altre m\xE8tode"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "El teu codi de seguretat \xE9s el que vas obtenir quan vas configurar l'autenticaci\xF3 de dos passos.",
      title: "Introdueix un codi de seguretat"
    },
    emailCode: {
      formTitle: "Codi de verificaci\xF3",
      resendButton: "No has rebut el codi? Reenvia",
      subtitle: "per continuar a {{applicationName}}",
      title: "Comprova el teu correu electr\xF2nic"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      expired: {
        subtitle: "Torna a la pestanya original per continuar.",
        title: "Aquest enlla\xE7 de verificaci\xF3 ha caducat"
      },
      failed: {
        subtitle: "Torna a la pestanya original per continuar.",
        title: "Aquest enlla\xE7 de verificaci\xF3 no \xE9s v\xE0lid"
      },
      formSubtitle: "Utilitza l'enlla\xE7 de verificaci\xF3 enviat al teu correu electr\xF2nic",
      formTitle: "Enlla\xE7 de verificaci\xF3",
      loading: {
        subtitle: "Ser\xE0s redirigit aviat",
        title: "Iniciant sessi\xF3..."
      },
      resendButton: "No has rebut l'enlla\xE7? Reenvia",
      subtitle: "per continuar a {{applicationName}}",
      title: "Comprova el teu correu electr\xF2nic",
      unusedTab: {
        title: "Pots tancar aquesta pestanya"
      },
      verified: {
        subtitle: "Ser\xE0s redirigit aviat",
        title: "Has iniciat sessi\xF3 amb \xE8xit"
      },
      verifiedSwitchTab: {
        subtitle: "Torna a la pestanya original per continuar",
        subtitleNewTab: "Torna a la pestanya recentment oberta per continuar",
        titleNewTab: "S'ha iniciat sessi\xF3 en una altra pestanya"
      }
    },
    forgotPassword: {
      formTitle: "Codi de restabliment de contrasenya",
      resendButton: "No has rebut el codi? Reenvia",
      subtitle: "per restablir la teva contrasenya",
      subtitle_email: "Primer, introdueix el codi enviat al teu ID de correu electr\xF2nic",
      subtitle_phone: "Primer, introdueix el codi enviat al teu tel\xE8fon",
      title: "Restableix la contrasenya"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Restableix la teva contrasenya",
      label__alternativeMethods: "O b\xE9, inicia sessi\xF3 amb un altre m\xE8tode",
      title: "Has oblidat la contrasenya?"
    },
    noAvailableMethods: {
      message: "No es pot procedir amb l'inici de sessi\xF3. No hi ha cap factor d'autenticaci\xF3 disponible.",
      subtitle: "S'ha produ\xEFt un error",
      title: "No es pot iniciar sessi\xF3"
    },
    passkey: {
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "Utilitza un altre m\xE8tode",
      subtitle: "Introdueix la contrasenya associada al teu compte",
      title: "Introdueix la teva contrasenya"
    },
    passwordPwned: {
      title: void 0
    },
    phoneCode: {
      formTitle: "Codi de verificaci\xF3",
      resendButton: "No has rebut el codi? Reenvia",
      subtitle: "per continuar a {{applicationName}}",
      title: "Comprova el teu tel\xE8fon"
    },
    phoneCodeMfa: {
      formTitle: "Codi de verificaci\xF3",
      resendButton: "No has rebut el codi? Reenvia",
      subtitle: "Per continuar, introdueix el codi de verificaci\xF3 enviat al teu tel\xE8fon",
      title: "Comprova el teu tel\xE8fon"
    },
    resetPassword: {
      formButtonPrimary: "Restableix la contrasenya",
      requiredMessage: "Ja existeix un compte amb una adre\xE7a de correu electr\xF2nic no verificada. Si us plau, restableix la teva contrasenya per seguretat.",
      successMessage: "La teva contrasenya s'ha canviat amb \xE8xit. Iniciant sessi\xF3, espera un moment.",
      title: "Estableix una nova contrasenya"
    },
    resetPasswordMfa: {
      detailsLabel: "Necessitem verificar la teva identitat abans de restablir la teva contrasenya."
    },
    start: {
      actionLink: "Registra't",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "Utilitza correu electr\xF2nic",
      actionLink__use_email_username: "Utilitza correu electr\xF2nic o nom d'usuari",
      actionLink__use_passkey: void 0,
      actionLink__use_phone: "Utilitza tel\xE8fon",
      actionLink__use_username: "Utilitza nom d'usuari",
      actionText: "No tens un compte?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "Benvingut de nou! Si us plau, inicia sessi\xF3 per continuar",
      subtitleCombined: void 0,
      title: "Inicia sessi\xF3 a {{applicationName}}",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Codi de verificaci\xF3",
      subtitle: "Per continuar, introdueix el codi de verificaci\xF3 generat per la teva aplicaci\xF3 d'autenticaci\xF3",
      title: "Verificaci\xF3 de dos passos"
    }
  },
  signInEnterPasswordTitle: "Introdueix la teva contrasenya",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Inicia sessi\xF3",
      actionText: "Ja tens un compte?",
      subtitle: "Si us plau, completa els detalls restants per continuar.",
      title: "Completa els camps que falten"
    },
    emailCode: {
      formSubtitle: "Introdueix el codi de verificaci\xF3 enviat a la teva adre\xE7a de correu electr\xF2nic",
      formTitle: "Codi de verificaci\xF3",
      resendButton: "No has rebut el codi? Reenvia",
      subtitle: "Introdueix el codi de verificaci\xF3 enviat al teu correu",
      title: "Verifica el teu correu electr\xF2nic"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      formSubtitle: "Utilitza l'enlla\xE7 de verificaci\xF3 enviat a la teva adre\xE7a de correu electr\xF2nic",
      formTitle: "Enlla\xE7 de verificaci\xF3",
      loading: {
        title: "Registrant..."
      },
      resendButton: "No has rebut l'enlla\xE7? Reenvia",
      subtitle: "per continuar a {{applicationName}}",
      title: "Verifica el teu correu electr\xF2nic",
      verified: {
        title: "Registre completat amb \xE8xit"
      },
      verifiedSwitchTab: {
        subtitle: "Torna a la pestanya recentment oberta per continuar",
        subtitleNewTab: "Torna a la pestanya anterior per continuar",
        title: "Correu electr\xF2nic verificat amb \xE8xit"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: void 0,
        label__onlyTermsOfService: void 0,
        label__termsOfServiceAndPrivacyPolicy: void 0
      },
      continue: {
        subtitle: void 0,
        title: void 0
      }
    },
    phoneCode: {
      formSubtitle: "Introdueix el codi de verificaci\xF3 enviat al teu n\xFAmero de tel\xE8fon",
      formTitle: "Codi de verificaci\xF3",
      resendButton: "No has rebut el codi? Reenvia",
      subtitle: "Introdueix el codi de verificaci\xF3 enviat al teu tel\xE8fon",
      title: "Verifica el teu tel\xE8fon"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "Inicia sessi\xF3",
      actionLink__use_email: void 0,
      actionLink__use_phone: void 0,
      actionText: "Ja tens un compte?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "Benvingut! Si us plau, completa els detalls per comen\xE7ar.",
      subtitleCombined: "Benvingut! Si us plau, completa els detalls per comen\xE7ar.",
      title: "Crea el teu compte",
      titleCombined: "Crea el teu compte"
    }
  },
  socialButtonsBlockButton: "Continua amb {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "El registre no ha estat exit\xF3s a causa de validacions de seguretat fallides. Si us plau, actualitza la p\xE0gina per tornar-ho a intentar o posa't en contacte amb el suport per obtenir m\xE9s assist\xE8ncia.",
    captcha_unavailable: "El registre no ha estat exit\xF3s a causa de la validaci\xF3 fallida de bot. Si us plau, actualitza la p\xE0gina per tornar-ho a intentar o posa't en contacte amb el suport per obtenir m\xE9s assist\xE8ncia.",
    form_code_incorrect: "El codi introdu\xEFt no \xE9s v\xE0lid. Si us plau, comprova el codi i torna-ho a intentar.",
    form_identifier_exists__email_address: void 0,
    form_identifier_exists__phone_number: void 0,
    form_identifier_exists__username: void 0,
    form_identifier_not_found: "No hem trobat cap compte amb aquests detalls.",
    form_param_format_invalid: "Format de par\xE0metre no v\xE0lid.",
    form_param_format_invalid__email_address: "L'adre\xE7a de correu electr\xF2nic ha de ser una adre\xE7a v\xE0lida.",
    form_param_format_invalid__phone_number: "El n\xFAmero de tel\xE8fon ha de tenir un format internacional v\xE0lid.",
    form_param_max_length_exceeded__first_name: "El nom no ha de superar els 256 car\xE0cters.",
    form_param_max_length_exceeded__last_name: "Els cognoms no han de superar els 256 car\xE0cters.",
    form_param_max_length_exceeded__name: "El nom no ha de superar els 256 car\xE0cters.",
    form_param_nil: "El valor del camp no pot ser nul. Si us plau, completa aquest camp.",
    form_param_value_invalid: void 0,
    form_password_incorrect: "La contrasenya introdu\xEFda \xE9s incorrecta.",
    form_password_length_too_short: "La teva contrasenya ha de tenir almenys 8 car\xE0cters.",
    form_password_not_strong_enough: "La teva contrasenya no \xE9s prou forta.",
    form_password_pwned: "Aquesta contrasenya ha aparegut en una filtraci\xF3 i no es pot utilitzar, si us plau, prova una altra contrasenya.",
    form_password_pwned__sign_in: void 0,
    form_password_size_in_bytes_exceeded: "La teva contrasenya ha superat el nombre m\xE0xim de bytes permesos, si us plau, redueix-la o elimina alguns car\xE0cters especials.",
    form_password_validation_failed: "Contrasenya incorrecta",
    form_username_invalid_character: "El nom d'usuari cont\xE9 car\xE0cters no v\xE0lids.",
    form_username_invalid_length: "El nom d'usuari ha de tenir entre 3 i 50 car\xE0cters.",
    identification_deletion_failed: "No pots eliminar la teva \xFAltima identificaci\xF3.",
    not_allowed_access: "L'adre\xE7a de correu electr\xF2nic o el n\xFAmero de tel\xE8fon no es permet registrar-se. Aix\xF2 podria ser degut a l'\xFAs de '+', '=', '#' o '.' a la vostra adre\xE7a de correu electr\xF2nic, utilitzant un domini connectat amb un servei de correu electr\xF2nic temporal o bloquejant-se expl\xEDcitament. Si creieu que es tracta d'un error, poseu-vos en contacte amb el servei d'assist\xE8ncia.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: void 0,
    passkey_not_supported: void 0,
    passkey_pa_not_supported: void 0,
    passkey_registration_cancelled: void 0,
    passkey_retrieval_cancelled: void 0,
    passwordComplexity: {
      maximumLength: "menys de {{length}} car\xE0cters",
      minimumLength: "{{length}} o m\xE9s car\xE0cters",
      requireLowercase: "una lletra min\xFAscula",
      requireNumbers: "un n\xFAmero",
      requireSpecialCharacter: "un car\xE0cter especial",
      requireUppercase: "una lletra maj\xFAscula",
      sentencePrefix: "La teva contrasenya ha de contenir"
    },
    phone_number_exists: "Aquest n\xFAmero de tel\xE8fon ja est\xE0 en \xFAs. Si us plau, prova'n un altre.",
    session_exists: "Ja est\xE0s connectat.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "La teva contrasenya funciona, per\xF2 podria ser m\xE9s forta. Prova afegint m\xE9s car\xE0cters.",
      goodPassword: "La teva contrasenya compleix tots els requisits necessaris.",
      notEnough: "La teva contrasenya no \xE9s prou forta.",
      suggestions: {
        allUppercase: "Capitalitza algunes, per\xF2 no totes les lletres.",
        anotherWord: "Afegeix m\xE9s paraules que siguin menys comunes.",
        associatedYears: "Evita anys que estiguin associats amb tu.",
        capitalization: "Capitalitza m\xE9s que la primera lletra.",
        dates: "Evita dates i anys que estiguin associats amb tu.",
        l33t: "Evita substitucions de lletres previsibles com '@' per 'a'.",
        longerKeyboardPattern: "Utilitza patrons de teclat m\xE9s llargs i canvia la direcci\xF3 de la tipografia diverses vegades.",
        noNeed: "Pots crear contrasenyes fortes sense utilitzar s\xEDmbols, n\xFAmeros o lletres maj\xFAscules.",
        pwned: "Si utilitzes aquesta contrasenya en un altre lloc, hauries de canviar-la.",
        recentYears: "Evita anys recents.",
        repeated: "Evita paraules i car\xE0cters repetits.",
        reverseWords: "Evita ortografies invertides de paraules comunes.",
        sequences: "Evita seq\xFC\xE8ncies de car\xE0cters comuns.",
        useWords: "Utilitza diverses paraules, per\xF2 evita frases comunes."
      },
      warnings: {
        common: "Aquesta \xE9s una contrasenya molt utilitzada.",
        commonNames: "Els noms i cognoms comuns s\xF3n f\xE0cils d'endevinar.",
        dates: "Les dates s\xF3n f\xE0cils d'endevinar.",
        extendedRepeat: `Els patrons de car\xE0cters repetits com "abcabcabc" s\xF3n f\xE0cils d'endevinar.`,
        keyPattern: "Els patrons curts de teclat s\xF3n f\xE0cils d'endevinar.",
        namesByThemselves: "Els noms o cognoms sols s\xF3n f\xE0cils d'endevinar.",
        pwned: "La teva contrasenya va ser exposada per una violaci\xF3 de dades a Internet.",
        recentYears: "Els anys recents s\xF3n f\xE0cils d'endevinar.",
        sequences: `Les seq\xFC\xE8ncies de car\xE0cters comuns com "abc" s\xF3n f\xE0cils d'endevinar.`,
        similarToCommon: "Aix\xF2 \xE9s similar a una contrasenya molt utilitzada.",
        simpleRepeat: `Els car\xE0cters repetits com "aaa" s\xF3n f\xE0cils d'endevinar.`,
        straightRow: "Les files rectes de tecles del teclat s\xF3n f\xE0cils d'endevinar.",
        topHundred: "Aquesta \xE9s una contrasenya molt utilitzada.",
        topTen: "Aquesta \xE9s una contrasenya altament utilitzada.",
        userInputs: "No hi hauria de tenir cap dada personal ni relacionada amb la p\xE0gina.",
        wordByItself: "Les paraules soles s\xF3n f\xE0cils d'endevinar."
      }
    }
  },
  userButton: {
    action__addAccount: "Afegeix compte",
    action__manageAccount: "Gestiona compte",
    action__signOut: "Tanca sessi\xF3",
    action__signOutAll: "Tanca sessi\xF3 de tots els comptes"
  },
  userProfile: {
    apiKeysPage: {
      title: void 0
    },
    backupCodePage: {
      actionLabel__copied: "Copiat!",
      actionLabel__copy: "Copia tot",
      actionLabel__download: "Descarrega .txt",
      actionLabel__print: "Imprimeix",
      infoText1: "Els codis de seguretat s'activaran per a aquest compte.",
      infoText2: "Guarda els codis de seguretat en secret i emmagatzema'ls de manera segura. Pots regenerar els codis de seguretat si sospites que han estat compromesos.",
      subtitle__codelist: "Emmagatzema'ls de manera segura i mantingues-los en secret.",
      successMessage: "Ara els codis de seguretat estan activats. Pots utilitzar un d'aquests per iniciar sessi\xF3 al teu compte, si perds l'acc\xE9s al teu dispositiu d'autenticaci\xF3. Cada codi nom\xE9s es pot utilitzar una vegada.",
      successSubtitle: "Pots utilitzar un d'aquests per iniciar sessi\xF3 al teu compte, si perds l'acc\xE9s al teu dispositiu d'autenticaci\xF3.",
      title: "Afegeix verificaci\xF3 amb codi de seguretat",
      title__codelist: "Codis de seguretat"
    },
    billingPage: {
      paymentHistorySection: {
        empty: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        tableHeader__status: void 0
      },
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      statementsSection: {
        empty: void 0,
        itemCaption__paidForPlan: void 0,
        itemCaption__proratedCredit: void 0,
        itemCaption__subscribedAndPaidForPlan: void 0,
        notFound: void 0,
        tableHeader__amount: void 0,
        tableHeader__date: void 0,
        title: void 0,
        totalPaid: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        tableHeader__edit: void 0,
        tableHeader__plan: void 0,
        tableHeader__startDate: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Selecciona un prove\xEFdor per connectar el teu compte.",
      formHint__noAccounts: "No hi ha prove\xEFdors de comptes externs disponibles.",
      removeResource: {
        messageLine1: "{{identifier}} ser\xE0 eliminat d'aquest compte.",
        messageLine2: "Ja no podr\xE0s utilitzar aquest compte connectat i qualsevol funcionalitat dependent deixar\xE0 de funcionar.",
        successMessage: "{{connectedAccount}} ha estat eliminat del teu compte.",
        title: "Elimina compte connectat"
      },
      socialButtonsBlockButton: "{{provider|titleize}}",
      successMessage: "El prove\xEFdor s'ha afegit al teu compte",
      title: "Afegeix compte connectat"
    },
    deletePage: {
      actionDescription: 'Escriu "Elimina compte" a continuaci\xF3 per continuar.',
      confirm: "Elimina compte",
      messageLine1: "Est\xE0s segur que vols eliminar el teu compte?",
      messageLine2: "Aquesta acci\xF3 \xE9s permanent i irreversible.",
      title: "Elimina compte"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "S'enviar\xE0 un correu electr\xF2nic que cont\xE9 un codi de verificaci\xF3 a aquesta adre\xE7a de correu electr\xF2nic.",
        formSubtitle: "Introdueix el codi de verificaci\xF3 enviat a {{identifier}}",
        formTitle: "Codi de verificaci\xF3",
        resendButton: "No has rebut el codi? Reenvia",
        successMessage: "El correu electr\xF2nic {{identifier}} s'ha afegit al teu compte."
      },
      emailLink: {
        formHint: "S'enviar\xE0 un correu electr\xF2nic que cont\xE9 un enlla\xE7 de verificaci\xF3 a aquesta adre\xE7a de correu electr\xF2nic.",
        formSubtitle: "Fes clic a l'enlla\xE7 de verificaci\xF3 al correu enviat a {{identifier}}",
        formTitle: "Enlla\xE7 de verificaci\xF3",
        resendButton: "No has rebut l'enlla\xE7? Reenvia",
        successMessage: "El correu electr\xF2nic {{identifier}} s'ha afegit al teu compte."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} ser\xE0 eliminat d'aquest compte.",
        messageLine2: "Ja no podr\xE0s iniciar sessi\xF3 utilitzant aquesta adre\xE7a de correu electr\xF2nic.",
        successMessage: "{{emailAddress}} ha estat eliminat del teu compte.",
        title: "Elimina adre\xE7a de correu electr\xF2nic"
      },
      title: "Afegeix adre\xE7a de correu electr\xF2nic",
      verifyTitle: "Verifica adre\xE7a de correu electr\xF2nic"
    },
    formButtonPrimary__add: "Afegeix",
    formButtonPrimary__continue: "Continua",
    formButtonPrimary__finish: "Finalitza",
    formButtonPrimary__remove: "Elimina",
    formButtonPrimary__save: "Guarda",
    formButtonReset: "Cancel\xB7la",
    mfaPage: {
      formHint: "Selecciona un m\xE8tode per afegir.",
      title: "Afegeix verificaci\xF3 en dos passos"
    },
    mfaPhoneCodePage: {
      backButton: "Utilitza el n\xFAmero existent",
      primaryButton__addPhoneNumber: "Afegeix n\xFAmero de tel\xE8fon",
      removeResource: {
        messageLine1: "{{ identifier }} ja no rebr\xE0 codis de verificaci\xF3 quan inici\xEF sessi\xF3.",
        messageLine2: "El teu compte podria no ser tan segur.Est\xE0s segur que vols continuar ? ",
        successMessage: "La verificaci\xF3 en dos passos per SMS ha estat eliminada per { { mfaPhoneCode } } ",
        title: "Elimina la verificaci\xF3 en dos passos"
      },
      subtitle__availablePhoneNumbers: "Selecciona un n\xFAmero de tel\xE8fon existent per registrar - lo per la verificaci\xF3 en dos passos per SMS o afegeix - ne un de nou.",
      subtitle__unavailablePhoneNumbers: "No hi ha n\xFAmeros de tel\xE8fon disponibles per registrar - los per la verificaci\xF3 en dos passos per SMS, si us plau, afegeix - ne un de nou.",
      successMessage1: "Quan inici\xEFs sessi\xF3, necessitar\xE0s introduir un codi de verificaci\xF3 enviat a aquest n\xFAmero de tel\xE8fon com un pas addicional.",
      successMessage2: "Guarda aquests codis de seguretat i emmagatzema'ls en un lloc segur. Si perds l'acc\xE9s al teu dispositiu d'autenticaci\xF3, pots utilitzar els codis de seguretat per iniciar sessi\xF3.",
      successTitle: "La verificaci\xF3 per codi SMS activada",
      title: "Afegeix verificaci\xF3 per codi SMS"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Escaneja el codi QR en lloc",
        buttonUnableToScan__nonPrimary: "No pots escanejar el codi QR? ",
        infoText__ableToScan: "Configura un nou m\xE8tode d'inici de sessi\xF3 a la teva aplicaci\xF3 d'autenticador i escaneja el seg\xFCent codi QR per vincular - lo al teu compte.",
        infoText__unableToScan: "Configura un nou m\xE8tode d'inici de sessi\xF3 al teu autenticador i introdueix la clau proporcionada a continuaci\xF3.",
        inputLabel__unableToScan1: "Assegura't que els contrasenyes basades en temps o d'\xFAs \xFAnic estan habilitats, despr\xE9s finalitza la vinculaci\xF3 del teu compte.",
        inputLabel__unableToScan2: "Alternativament, si el teu autenticador suporta URI de TOTP, tamb\xE9 pots copiar l'URI completa."
      },
      removeResource: {
        messageLine1: "Els codis de verificaci\xF3 d'aquest autenticador ja no seran necessaris quan inici\xEFs sessi\xF3.",
        messageLine2: "El teu compte podria no ser tan segur.Est\xE0s segur que vols continuar? ",
        successMessage: "La verificaci\xF3 en dos passos mitjan\xE7ant l'aplicaci\xF3 d'autenticaci\xF3 ha estat eliminada.",
        title: "Elimina la verificaci\xF3 en dos passos"
      },
      successMessage: "La verificaci\xF3 en dos passos ara est\xE0 habilitada.Quan inici\xEFs sessi\xF3, necessitar\xE0s introduir un codi de verificaci\xF3 d'aquest autenticador com un pas addicional.",
      title: "Afegeix aplicaci\xF3 d'autenticaci\xF3",
      verifySubtitle: "Introdueix el codi de verificaci\xF3 generat pel teu autenticador",
      verifyTitle: "Codi de verificaci\xF3"
    },
    mobileButton__menu: "Men\xFA",
    navbar: {
      account: "Perfil",
      apiKeys: void 0,
      billing: void 0,
      description: "Gestiona la informaci\xF3 del teu compte.",
      security: "Seguretat",
      title: "Compte"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: void 0,
        title: void 0
      },
      subtitle__rename: void 0,
      title__rename: void 0
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Es recomana tancar sessi\xF3 en tots els altres dispositius que hagin utilitzat la teva contrasenya antiga.",
      readonly: "Actualment no pots editar la teva contrasenya perqu\xE8 pots iniciar sessi\xF3 nom\xE9s a trav\xE9s de la connexi\xF3 d'empresa.",
      successMessage__set: "La teva contrasenya ha estat establerta.",
      successMessage__signOutOfOtherSessions: "S'ha tancat sessi\xF3 en tots els altres dispositius.",
      successMessage__update: "La teva contrasenya ha estat actualitzada.",
      title__set: "Estableix contrasenya",
      title__update: "Actualitza contrasenya"
    },
    phoneNumberPage: {
      infoText: "S'enviar\xE0 un missatge de text que cont\xE9 un codi de verificaci\xF3 a aquest n\xFAmero de tel\xE8fon. Poden aplicar-se tarifes de missatges i dades.",
      removeResource: {
        messageLine1: "{ { identifier } } ser\xE0 eliminat d'aquest compte.",
        messageLine2: "Ja no podr\xE0s iniciar sessi\xF3 utilitzant aquest n\xFAmero de tel\xE8fon.",
        successMessage: "{ { phoneNumber } } ha estat eliminat del teu compte.",
        title: "Elimina n\xFAmero de tel\xE8fon"
      },
      successMessage: "{ { identifier } } s'ha afegit al teu compte.",
      title: "Afegeix n\xFAmero de tel\xE8fon",
      verifySubtitle: "Introdueix el codi de verificaci\xF3 enviat a { { identifier } } ",
      verifyTitle: "Verifica n\xFAmero de tel\xE8fon"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "Mida recomanada 1:1, fins a 10MB.",
      imageFormDestructiveActionSubtitle: "Elimina",
      imageFormSubtitle: "Puja",
      imageFormTitle: "Imatge de perfil",
      readonly: "La informaci\xF3 del teu perfil ha estat proporcionada per la connexi\xF3 d'empresa i no pot ser editada.",
      successMessage: "El teu perfil ha estat actualitzat.",
      title: "Actualitza perfil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Tanca sessi\xF3 del dispositiu",
        title: "Dispositius actius"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Torna-ho a intentar",
        actionLabel__reauthorize: "Autoritza ara",
        destructiveActionTitle: "Elimina",
        primaryButton: "Connecta compte",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "Els \xE0mbits requerits han estat actualitzats, i podr\xE0s estar experimentant funcionalitat limitada. Si us plau, reautoritza aquesta aplicaci\xF3 per evitar qualsevol problema",
        title: "Comptes connectats"
      },
      dangerSection: {
        deleteAccountButton: "Elimina compte",
        title: "Elimina compte"
      },
      emailAddressesSection: {
        destructiveAction: "Elimina correu electr\xF2nic",
        detailsAction__nonPrimary: "Estableix com a principal",
        detailsAction__primary: "Completa la verificaci\xF3",
        detailsAction__unverified: "Verifica",
        primaryButton: "Afegeix adre\xE7a de correu electr\xF2nic",
        title: "Adreces de correu electr\xF2nic"
      },
      enterpriseAccountsSection: {
        title: "Comptes d'empresa"
      },
      headerTitle__account: "Detalls del perfil",
      headerTitle__security: "Seguretat",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Regenera",
          headerTitle: "Codis de seguretat",
          subtitle__regenerate: "Obt\xE9n un nou conjunt de codis de seguretat segurs. Els codis de seguretat anteriors seran eliminats i no podran ser utilitzats.",
          title__regenerate: "Regenera codis de seguretat"
        },
        phoneCode: {
          actionLabel__setDefault: "Estableix com a predeterminat",
          destructiveActionLabel: "Elimina"
        },
        primaryButton: "Afegeix verificaci\xF3 en dos passos",
        title: "Verificaci\xF3 en dos passos",
        totp: {
          destructiveActionTitle: "Elimina",
          headerTitle: "Aplicaci\xF3 d'autenticaci\xF3"
        }
      },
      passkeysSection: {
        menuAction__destructive: void 0,
        menuAction__rename: void 0,
        primaryButton: void 0,
        title: void 0
      },
      passwordSection: {
        primaryButton__setPassword: "Estableix contrasenya",
        primaryButton__updatePassword: "Actualitza contrasenya",
        title: "Contrasenya"
      },
      phoneNumbersSection: {
        destructiveAction: "Elimina n\xFAmero de tel\xE8fon",
        detailsAction__nonPrimary: "Estableix com a principal",
        detailsAction__primary: "Completa la verificaci\xF3",
        detailsAction__unverified: "Verifica n\xFAmero de tel\xE8fon",
        primaryButton: "Afegeix n\xFAmero de tel\xE8fon",
        title: "N\xFAmeros de tel\xE8fon"
      },
      profileSection: {
        primaryButton: "Actualitza perfil",
        title: "Perfil"
      },
      usernameSection: {
        primaryButton__setUsername: "Estableix nom d'usuari",
        primaryButton__updateUsername: "Actualitza nom d'usuari",
        title: "Nom d'usuari"
      },
      web3WalletsSection: {
        destructiveAction: "Elimina cartera",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Carteres Web3",
        title: "Carteres Web3"
      }
    },
    usernamePage: {
      successMessage: "El teu nom d'usuari ha estat actualitzat.",
      title__set: "Estableix nom d'usuari",
      title__update: "Actualitza nom d'usuari"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} ser\xE0 eliminat d'aquest compte.",
        messageLine2: "Ja no podr\xE0s iniciar sessi\xF3 utilitzant aquesta cartera Web3.",
        successMessage: "{{web3Wallet}} ha estat eliminada del teu compte.",
        title: "Elimina cartera Web3"
      },
      subtitle__availableWallets: "Selecciona una cartera Web3 per connectar al teu compte.",
      subtitle__unavailableWallets: "No hi ha carteres Web3 disponibles.",
      successMessage: "La cartera ha estat afegida al teu compte.",
      title: "Afegeix cartera Web3",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  caES
};
//# sourceMappingURL=ca-ES.mjs.map